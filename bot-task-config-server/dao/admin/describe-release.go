// bot-task-config-server
//
// @(#)describe-release.go  Wednesday, January 31, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package admin

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	adminPb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// DescribeLatestReleaseStatus 获取机器人最新发布状态
// ReleaseBizId: 当前发布的任务
// Status: 状态 1:待发布 2:发布中 3:发布成功 4:发布失败 5:审核中6:审核成功 7:审核失败 8:发布成功回调处理中 9:发布暂停
func DescribeLatestReleaseStatus(ctx context.Context, botBizId, corpID uint64) (uint64, uint32, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	req := adminPb.DescribeLatestReleaseStatusReq{
		BotBizId: botBizId,
		CorpId:   corpID,
	}
	if len(codec.Message(ctx).ServerMetaData()["request_id"]) == 0 {
		util.WithRequestID(ctx, sid)
	}
	resp, err := proxy.GetAdminAPIProxy().DescribeLatestReleaseStatus(ctx, &req)
	log.Infof("R|Admin|DescribeLatestReleaseStatus|%s|%v|%v|%+v|ERR:%v|%s", sid, botBizId, corpID, resp, err,
		time.Since(t0))
	return resp.GetReleaseBizId(), resp.GetStatus(), err
}
