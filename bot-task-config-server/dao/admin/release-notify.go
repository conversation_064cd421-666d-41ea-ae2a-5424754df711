// bot-task-config-server
//
// @(#)release-notify.go  Thursday, December 28, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package admin

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	adminPb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ReleaseNotify 通知发布状态
func ReleaseNotify(ctx context.Context, botBizId, taskID uint64, success bool, message string) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	req := adminPb.ReleaseNotifyReq{
		// RobotId: 此字段不要传(ronenli)
		VersionId:      taskID,
		IsSuccess:      success,
		Message:        message,
		Transparent:    "",
		CallbackSource: 1,
		RobotBizId:     botBizId,
	}
	if len(codec.Message(ctx).ServerMetaData()["request_id"]) == 0 {
		util.WithRequestID(ctx, sid)
	}
	_, err := proxy.GetAdminAPIProxy().ReleaseNotify(ctx, &req)
	log.Infof("R|Admin|ReleaseNotify|%s|%v|%v|%v|%v|ERR:%v|%s", sid, botBizId, taskID, success, message, err,
		time.Since(t0))
	return err
}
