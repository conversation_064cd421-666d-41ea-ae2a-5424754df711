// bot-task-config-server
//
// @(#)account.go  星期二, 三月 12, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package cloud

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/cloud"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	jsoniter "github.com/json-iterator/go"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

// DescribeNickname 获取当前用户昵称
func DescribeNickname(ctx context.Context, uin, subAccountUin string) (*cloud.NicknameInfo, error) {
	cfg := config.GetMainConfig().CloudAPIs
	credential := common.NewCredential(cfg.SecretID, cfg.SecretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = cfg.AccountHost
	cpf.HttpProfile.ReqMethod = "POST"
	//创建common client
	client := common.NewCommonClient(credential, cfg.Region, cpf)

	// 创建common request，依次传入产品名、产品版本、接口名称
	request := tchttp.NewCommonRequest("account", cfg.Version, "DescribeNickname")

	// 自定义请求参数:
	body := map[string]any{}

	// set custom headers
	request.SetHeader(map[string]string{
		"X-TC-TraceId":    util.RequestID(ctx),
		"X-Auth-OwnerUin": uin,
		"X-Auth-SubUin":   subAccountUin,
	})

	// 设置action所需的请求数据
	err := request.SetActionParameters(body)
	if err != nil {
		log.ErrorContextf(ctx, "DescribeNickname request:%+v err:%+v", request, err)
		return nil, err
	}

	//创建common response
	response := tchttp.NewCommonResponse()

	//发送请求
	err = client.Send(request, response)
	if err != nil {
		log.ErrorContextf(ctx, "DescribeNickname request:%+v err:%+v", request, err)
		return nil, err
	}
	log.DebugContextf(ctx, "DescribeNickname request:%+v rsp:%s", request, string(response.GetBody()))

	rsp := cloud.DescribeNicknameRsp{}
	if err = jsoniter.Unmarshal(response.GetBody(), &rsp); err != nil {
		log.ErrorContextf(ctx, "DescribeNickname response:%+v err:%+v", response, err)
		return nil, err
	}
	return &rsp.Response, nil
}
