// bot-task-config-server
//
// @(#)common.go  星期四, 四月 11, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package dao

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// HasSaveCorpusVectorGroupToRedis 创建后保存 Redis 缓存
func HasSaveCorpusVectorGroupToRedis(ctx context.Context, key string) (bool, error) {
	redisClient := database.GetRedis()
	sid := util.RequestID(ctx)
	_, err := redisClient.Get(ctx, key).Result()
	log.InfoContextf(ctx, "sid:%s|HasSaveCorpusVectorGroupToRedis|key:%+v|err:%+v", sid, key, err)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, redis.Nil) {
		return false, nil
	}
	log.InfoContextf(ctx, "hasSaveCorpusVectorGroup fail, "+
		"redis.Get fail, key: %s, err: %v", key, err)
	return false, err
}

// GetExampleListByBotAndIntentId 获取任务流程下的示例问法
func GetExampleListByBotAndIntentId(ctx context.Context, keyword, botBizId,
	intentId string) (*[]entity.IntentCorpus, int64, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var examples *[]entity.IntentCorpus
	var total int64

	result := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_intent_id=?", botBizId, intentId).
		Order("f_update_time DESC")

	if len(keyword) > 0 {
		// 避免 Like '%_%' 筛选全部示例问法
		result = result.Where("f_corpus Like ? escape '*'", "%*"+keyword+"%")
	}
	result = result.Scan(&examples).Count(&total)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, 0, nil
	}
	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleListByBotAndIntentId:err:%+v", sid, result.Error)
		return nil, 0, result.Error
	}
	return examples, total, nil
}
