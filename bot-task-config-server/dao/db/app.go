// bot-task-config-server
//
// @(#)app.go  星期三, 二月 28, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	llm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// CodeInfo 分享码结构体信息
type CodeInfo struct {
	BotBizId   string `json:"bot_biz_id"`
	Code       string `json:"code"`
	Expired    uint32 `json:"expired"`
	CreateUser string `json:"create_user"`
}

// GetAppChatInputNum ...
// 这里接口不能立马下线，需要评估用户调用
func GetAppChatInputNum(ctx context.Context, mn string) (int32, error) {
	var limitNum int32
	modelLen := "4k"
	limitNum = 2500

	modelLimits := config.GetMainConfig().LlmInputNum

	if util.CorpID(ctx) > 0 {
		resp, err := rpc.GetModelInfo(ctx, util.CorpID(ctx), mn)
		if err != nil {
			log.WarnContextf(ctx, "GetAppChatInputNum"+
				" GetModelInfo|CorpId:%s|ModelName:%s|error:%+v|resp|%v",
				util.CorpID(ctx), mn, err, resp)
			return limitNum, err
		}
		// 如果是自定义模型，返回默认的 limitNum
		if resp != nil && resp.GetIsCustomModel() {
			return limitNum, nil
		}
		if resp != nil && len(resp.GetModelLength()) > 0 {
			modelLen = resp.GetModelLength()
		}
	} else {
		mliReq := &llm.GetModelRequest{
			RequestId:   util.RequestID(ctx),
			ModelName:   mn,
			RequestType: llm.RequestType_ONLINE,
		}
		resp, err := proxy.GetLLMProxy().GetModelInfo(ctx, mliReq)
		if err != nil {
			log.WarnContextf(ctx, "GetAppChatInputNum"+
				" GetLLMProxy().GetModelInfo|CorpId:%s|ModelName:%s|error:%+v|resp|%v",
				util.CorpID(ctx), mn, err, resp)
			return limitNum, err
		}
		if resp != nil && len(resp.GetModelLength()) > 0 {
			modelLen = resp.GetModelLength()
		}
	}

	ml := strings.ToUpper(modelLen)
	for i := range modelLimits {
		if ml == strings.ToUpper(modelLimits[i].Length) {
			limitNum = modelLimits[i].Limit
			break
		}
	}
	return limitNum, nil
}

// CheckShareCodeInDB 校验分享码
func CheckShareCodeInDB(ctx context.Context, code string) (bool, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var total int64
	err := db.Table(entity.AppShared{}.TableName()).
		Where("f_shared_code=? AND f_is_deleted=?", code, entity.AppSharedUnDeleted).
		Where("f_expire_time > ?", time.Now()).Count(&total).Error
	if err != nil {
		return false, err
	}
	if total > 0 {
		return true, nil
	}
	return false, nil
}

// GetRobotIdByShareCodeFromDB 获取分享码
func GetRobotIdByShareCodeFromDB(ctx context.Context, shareCode string) (string, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var shareApp *entity.AppShared
	sDb := db.Table(entity.AppShared{}.TableName()).
		Where("f_shared_code=? AND f_is_deleted=?", shareCode, entity.AppSharedUnDeleted).
		Where("f_expire_time > ?", time.Now())
	err := sDb.First(&shareApp).Error
	if err == gorm.ErrRecordNotFound {
		return "", nil
	}
	if err != nil {
		return "", err
	}
	return shareApp.AppID, nil
}

// GetShareCodeInDB 从DB获取分享码
func GetShareCodeInDB(ctx context.Context, botBizId string, expired uint32) (codeInfo CodeInfo, err error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var shareApp *entity.AppShared
	sDb := db.Table(entity.AppShared{}.TableName()).
		Where("f_app_id=? AND f_is_deleted=?", botBizId, entity.AppSharedUnDeleted).
		Order("f_create_time DESC")
	// 有过期
	if expired > 0 {
		sDb = sDb.Where("f_expire_time > ?", time.Now())
	}
	err = sDb.First(&shareApp).Error
	if err == gorm.ErrRecordNotFound {
		return codeInfo, nil
	}
	if err != nil {
		return codeInfo, err
	}
	codeInfo.Code = shareApp.SharedCode
	codeInfo.CreateUser = shareApp.CreateUser
	codeInfo.BotBizId = shareApp.AppID
	//subTimeStamp := shareApp.ExpiredTime.Sub(time.Now()).Seconds()
	subTimeStamp := time.Until(shareApp.ExpiredTime)
	if subTimeStamp > 0 {
		codeInfo.Expired = uint32(subTimeStamp)
	} else {
		codeInfo.Expired = 0
	}
	return codeInfo, nil
}

// SaveAppShareCodeToDB 保存分享码到BD
func SaveAppShareCodeToDB(ctx context.Context, botBizId string, code string,
	expiredTime time.Time) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	sid := util.RequestID(ctx)
	// 保存分享码记录
	appShare := &entity.AppShared{
		AppID:       botBizId,
		SharedCode:  code,
		CreateUser:  subUin,
		UpdateUser:  subUin,
		ExpiredTime: expiredTime,
	}
	if err := db.Table(appShare.TableName()).Create(appShare).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|appShare:%s|SaveAppShareCodeToDB,appShare err:%s",
			sid, appShare, err)
		return err
	}
	return nil
}

// DeleteAppShareCodeFromDB 从DB删除分享码
func DeleteAppShareCodeFromDB(ctx context.Context, botBizId string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	if err := db.Table(entity.AppShared{}.TableName()).Where(&entity.AppShared{
		AppID:     botBizId,
		IsDeleted: entity.AppSharedUnDeleted,
	}).Updates(map[string]interface{}{
		"f_is_deleted":  gorm.Expr("f_id"),
		"f_update_user": subUin,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "DeleteAppShareCodeFromDB failed delete,"+
			" botBizId:%s,err:%s", botBizId, err.Error())
		return err
	}
	return nil
}

// GetAppShareCodeFromRedis 从Redis获取分享码
func GetAppShareCodeFromRedis(ctx context.Context, key string) (codeInfo CodeInfo, err error) {
	redisClient := database.GetRedis()
	codeInfoValue, err := redisClient.Get(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return codeInfo, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetRedisAppShareCode fail, "+
			"redis.Get fail, key: %s, err: %v", key, err)
		return codeInfo, err
	}
	if err = json.Unmarshal([]byte(codeInfoValue), &codeInfo); err != nil {
		log.ErrorContextf(ctx, "SaveShareInfoToRD fail, Unmarshal codeInfo fail,"+
			" CodeInfo: %+v,err:%s", codeInfo, err.Error())
		return codeInfo, err
	}

	return codeInfo, nil
}

// SaveShareInfoToRD 设置分享码到缓存
func SaveShareInfoToRD(ctx context.Context, key string, codeInfo CodeInfo, expiredUint uint32) error {
	redisClient := database.GetRedis()
	value, err := json.Marshal(codeInfo)
	expired := time.Duration(expiredUint) * time.Second
	if err != nil {
		log.ErrorContextf(ctx, "SaveShareInfoToRD fail, Marshal codeInfo fail,"+
			" CodeInfo: %+v,err:%s", codeInfo, err.Error())
		return err
	}
	if err := redisClient.Set(ctx, key, value, expired).Err(); err != nil {
		log.ErrorContextf(ctx, "SetRedisAppShareCode "+
			"redis.Set fail, key: %s, codeInfo: %+v|err:%s", key, codeInfo, err.Error())
		return err
	}
	return nil
}

// DeleteAppShareCodeFromRedis 从Redis删除code
func DeleteAppShareCodeFromRedis(ctx context.Context, key string) error {
	redisClient := database.GetRedis()
	if _, err := redisClient.Del(ctx, key).Result(); err != nil {
		log.ErrorContextf(ctx, "DeleteAppShareCodeFromRedis, fail:%s", err.Error())
		return err
	}
	return nil
}
