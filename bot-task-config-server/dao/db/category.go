package db

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// GetCategoryByCateId 获取分类
func GetCategoryByCateId(ctx context.Context, cateId, botBizId string) (*category.Category, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	cate := category.Category{}
	result := db.Table(cate.TableName()).
		Where(category.TCategoryColumns.IsDeleted+" = 0").
		Where(category.TCategoryColumns.RobotID+" = ?", botBizId).
		Where(category.TCategoryColumns.CategoryID+"=?", cateId).
		Find(&cate)
	if result.Error != nil {
		log.Errorf("GetCategoryByCateId:%s|%v", sid, result.Error)
		return nil, result.Error
	}
	if result.RowsAffected == 0 {
		return nil, nil
	}
	return &cate, nil
}

// CreateCategory 创建Category
func CreateCategory(ctx context.Context, cate *category.Category) (*category.Category, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)

	cSql := `INSERT INTO t_category (f_category_id, 
                        f_category_name,f_robot_id, f_parent_id,f_feature,f_order_number, f_uin,f_sub_uin) 
		SELECT ?,?,?, ?, ?,?,?,?  FROM dual WHERE NOT EXISTS ( SELECT 1 FROM  t_category WHERE
      	f_robot_id = ? AND f_parent_id = ? AND f_category_name =? AND f_is_deleted=0)`
	args := []any{cate.CategoryID, cate.CategoryName, cate.RobotID, cate.ParentID, cate.Feature,
		cate.OrderNum, cate.Uin, cate.SubUin, cate.RobotID, cate.ParentID, cate.CategoryName}
	err := db.Exec(cSql, args...).Error
	if err != nil {
		log.Errorf("CreateCategory %s|%v|", sid, err.Error())
		return &category.Category{}, err
	}
	return cate, err
}

// UpdateCategory 更新Category
func UpdateCategory(ctx context.Context, cateId, name, robotId string) (err error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	result := db.Table(category.Category{}.TableName()).
		Where(category.TCategoryColumns.CategoryID+" =?", cateId).
		Where(category.TCategoryColumns.RobotID+"=?", robotId).
		Update(category.TCategoryColumns.CategoryName, name)
	if result.Error != nil {
		log.Errorf("UpdateCategory: %s|%v", sid, result.Error.Error())
		return result.Error
	}
	if result.RowsAffected <= 0 {
		log.Errorf("UpdateCategory failed, CategoryID %s|%s not found", sid, cateId)
		return fmt.Errorf("UpdateCategory failed, ID %s not found", cateId)
	}
	return nil
}

// DeleteCategory 删除Category
func DeleteCategory(ctx context.Context, cateIDs []string,
	unCategorizedCateID uint64, robotId string) (err error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	if err := db.Transaction(func(tx *gorm.DB) error {
		args := make([]any, 0, len(cateIDs)+1)
		args = append(args, category.CateIsDeleted)
		for _, id := range cateIDs {
			args = append(args, id)
		}

		// 先获取cates
		err := tx.Table(category.Category{}.TableName()).
			Where(category.TCategoryColumns.CategoryID+" IN (?)", cateIDs).
			Where(category.TCategoryColumns.RobotID+"=?", robotId).
			Updates(map[string]interface{}{"f_is_deleted": gorm.Expr("f_id")}).Error
		if err != nil {
			log.Errorf("DeleteCategory err:%s|%v", sid, err)
			return err
		}

		tableName := entity.TaskFlow{}.TableName()
		if err := tx.Table(tableName).Where(entity.TTaskFlowColumns.CategoryID+" IN (?)", cateIDs).
			Updates(map[string]interface{}{
				entity.TTaskFlowColumns.CategoryID: unCategorizedCateID}).Error; err != nil {
			log.Errorf("DeleteCategory,%s|%s|:%v", sid, tableName, err)
			return err
		}
		return nil
	}); err != nil {
		log.Errorf("删除分类失败 err:%s|%+v", sid, err)
		return err
	}
	return nil
}

// QueryUnCateUnderRobot 查询机器人下是否有未分类，没有创建
func QueryUnCateUnderRobot(ctx context.Context, robotId string) (bool, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	cate := category.Category{}
	result := db.Table(cate.TableName()).Where(
		"f_is_deleted=0 AND f_robot_id=? AND f_parent_id=? AND f_category_name=?",
		robotId, category.ParentAllID, category.UncategorizedCateName).Find(&cate)
	log.Infof("QueryUnCateUnderRobot:sid:%s|%v", sid, result.RowsAffected)
	if result.Error != nil {
		log.Errorf("QueryUnCateUnderRobot:%s|%v", sid, result.Error)
		return true, result.Error
	}
	if result.RowsAffected == 0 {
		return false, nil
	}
	return true, nil
}

// GetCategoryStat 分类统计
func GetCategoryStat(ctx context.Context, robotID, feature string) ([]*category.CateStat, error) {
	var stat []*category.CateStat
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)

	// 先获取 robotID 下的 所有 f_intent_ids, 然后再找 CategoryID
	var intentIDs []string
	var robotIntents []entity.RobotIntent
	err := db.Table(entity.RobotIntent{}.TableName()).Select(
		"f_intent_id").Where("f_is_deleted=? AND f_robot_id=?",
		0, robotID).Find(&robotIntents).Error
	if err != nil {
		log.Errorf("GetCategoryStat:%s|%v", sid, err.Error())
		return nil, err
	}
	for _, intent := range robotIntents {
		intentIDs = append(intentIDs, intent.IntentID)
	}

	err = db.Table(entity.TaskFlow{}.TableName()).Select("f_category_id, count(*) as total").
		Where("f_is_deleted = ? AND+ f_intent_id IN (?)", entity.TaskFlowUnDeleted, intentIDs).
		Group(category.TCategoryColumns.CategoryID).Find(&stat).Error
	if err != nil {
		log.Errorf("GetCategoryStat:%s|%v", sid, err.Error())
		return nil, err
	}
	return stat, nil
}

// GetCategorys 获取机器人分类信息
func GetCategorys(ctx context.Context, robotID string) ([]*category.Category, error) {
	var categorys []*category.Category
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	err := db.Table(category.Category{}.TableName()).
		Where(category.TCategoryColumns.IsDeleted+" = 0").
		Where(category.TCategoryColumns.RobotID+" = ?", robotID).
		Order(category.TCategoryColumns.OrderNum + " DESC").
		Find(&categorys).Error
	if err != nil {
		log.Errorf("GetCategorys:%s|%v", sid, err)
		return nil, err
	}
	return categorys, nil
}

// GetCategoryMap 获取分类map信息
func GetCategoryMap(ctx context.Context, robotID string) (map[string]*category.Category, error) {
	categorys, err := GetCategorys(ctx, robotID)
	sid := util.RequestID(ctx)
	if err != nil {
		log.Errorf("GetCategoryMap:%s|%v", sid, err)
		return nil, err
	}
	mapCategory := make(map[string]*category.Category)
	for _, v := range categorys {
		mapCategory[v.CategoryID] = v
	}
	return mapCategory, nil
}

// CreateCategoriesByTx 事物批量创建分类信息
func CreateCategoriesByTx(ctx context.Context, tx *gorm.DB, categories []*category.Category) error {
	if len(categories) == 0 {
		return nil
	}
	err := tx.Table(category.Category{}.TableName()).Create(&categories).Error
	if err != nil {
		log.ErrorContextf(ctx, "CreateCategoriesByTx err:%+v", err)
		return err
	}
	return nil
}
