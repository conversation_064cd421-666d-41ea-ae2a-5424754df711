package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

const (
	tableCorpus = "t_corpus"
)

// GetCorpusByIntentIDs 通过意图ID获取意图信息
func GetCorpusByIntentIDs(ctx context.Context, intentIDs []string) (map[string][]*entity.Corpus, error) {
	mapIntentCorpus := make(map[string][]*entity.Corpus)
	if len(intentIDs) == 0 {
		return mapIntentCorpus, nil
	}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		var txErr error
		mapIntentCorpus, txErr = GetCorpusByIntentIDsWithTx(ctx, tx, intentIDs)
		return txErr
	})
	if err != nil {
		return nil, err
	}
	return mapIntentCorpus, nil
}

// GetCorpusByIntentIDsWithTx 通过意图ID获取意图信息 -- 事物
func GetCorpusByIntentIDsWithTx(ctx context.Context, tx *gorm.DB, intentIDs []string) (
	map[string][]*entity.Corpus, error) {
	var corpus []*entity.Corpus
	sid := util.RequestID(ctx)
	mapIntentCorpus := make(map[string][]*entity.Corpus)
	if len(intentIDs) == 0 {
		return mapIntentCorpus, nil
	}
	err := tx.Table(tableCorpus).
		Where("f_is_deleted = 0").
		Where("f_intent_id IN ?", intentIDs).
		Find(&corpus).Error
	if err != nil {
		log.Errorf("getCorpusByIntentIDs failed,intentIDs:%s|%+v|err:%+v", sid, intentIDs, err)
		return nil, err
	}
	for _, v := range corpus {
		mapIntentCorpus[v.IntentID] = append(mapIntentCorpus[v.IntentID], v)
	}
	return mapIntentCorpus, nil
}
