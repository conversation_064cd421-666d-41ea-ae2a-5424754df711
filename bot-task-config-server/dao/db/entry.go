// bot-task-config-server
//
// @(#)entry.go  星期四, 二月 29, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package db

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

var EntityEntryKey = "TaskConfig:ENV:%s:RobotID:%s:EntityEntries"

const (
	SandboxEnv = "Sandbox"
	ProductEnv = "Product"
)

const (
	queryEntryListBySlotIDList = "select f_entry_id from t_entry where f_is_deleted = 0 "
)

// GetEntityEntriesKey 获取词条redis的key
func GetEntityEntriesKey(ctx context.Context, envName, robotID string) string {
	return fmt.Sprintf(EntityEntryKey, envName, robotID)
}

// RedisEntryInfo 词条redis的value结构
type RedisEntryInfo struct {
	Value     string   `json:"Value"`
	AliasName []string `json:"AliasName"`
}

//func GetRedisEntryInfos(ctx context.Context, existEntries []*entity.Entry, curEntryInfo entity.Entry,
//action string) (string, error) {
//	entryInfoMap := make(map[string]RedisEntryInfo, 0)
//
//	for _, entry := range existEntries {
//		if entry.EntryID == curEntryInfo.EntryID && action == entity.ActionUpdate {
//			aliasArray := []string{}
//			_ = json.Unmarshal([]byte(curEntryInfo.EntryAlias), &aliasArray)
//			redisEntryInfo := RedisEntryInfo{
//				Value:     curEntryInfo.EntryValue,
//				AliasName: aliasArray,
//			}
//			entryInfoMap[entry.EntryID] = redisEntryInfo
//		} else if entry.EntryID == curEntryInfo.EntryID && action == entity.ActionDelete {
//			continue
//		}
//
//	}
//	redisEntryVal, err := json.Marshal(entryInfoMap)
//	if err != nil {
//		log.ErrorContextf(ctx, "GetFinalEntryInfos json.Marshal Failed,data:%+v,err:%+v", entryInfoMap, err)
//		return "", err
//	}
//	return string(redisEntryVal), nil
//}

// GetNoDeletedEntryWithEntityID 查询指定实体下未删除的词条
func GetNoDeletedEntryWithEntityID(ctx context.Context, entityID string) ([]entity.Entry, error) {
	sid := util.RequestID(ctx)

	var entryInfo []entity.Entry
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Model(&entity.Entry{}).
		Where("f_entity_id = ?  and f_is_deleted = 0", entityID).
		Find(&entryInfo).Error
	if err != nil {
		log.Errorf("GetEntryByDeletedStatus Failed! sid:%s,err:%v", sid, err)
		return entryInfo, err
	}
	return entryInfo, nil
}

// TXUpsertDeleteEntry 更新删除词条处理，redis同步
func TXUpsertDeleteEntry(ctx context.Context, robotID, slotID, entityID string, insertEntries, updateEntries,
	deletedEntries []entity.Entry, flowInfos []TaskFlowInfo) (err error) {
	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM()).Debug()
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()
	action := entity.ActionUpdate
	// 根据entryAction 来创建、修改、删除t_entry表
	err = txUpsertDeleteEntryTable(ctx, tx, insertEntries, updateEntries, deletedEntries, flowInfos)
	if err != nil {
		return err
	}
	// 词条部分同步到redis
	err = entrySetRedis(ctx, robotID, tx, entityID)
	if err != nil {
		return err
	}
	// 根据entryAction 来创建、修改、删除 词条向量表
	err = TxUpsertDeletedEntryVectorGroupTable(ctx, robotID, insertEntries, updateEntries, deletedEntries)
	if err != nil {
		return err
	}
	// 更新t_slot表
	err = tx.Model(&entity.Slot{}).
		Where("f_slot_id = ? and f_is_deleted = 0", slotID).
		Updates(map[string]interface{}{
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         action,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry UpdateSlot Failed err:%v", err)
		return err
	}
	// 更新t_entity表
	err = tx.Model(&entity.Entity{}).
		Where("f_entity_id =? and f_is_deleted = 0", entityID).
		Updates(map[string]interface{}{
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         action,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry UpdateEntity Failed err:%v", err)
		return err
	}
	// 更新t_slot_entity表
	err = tx.Model(&entity.SlotEntity{}).
		Where("f_slot_id = ? and f_entity_id =? and f_is_deleted = 0", slotID, entityID).
		Updates(map[string]interface{}{
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         action,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry UpdateSlotEntity Failed err:%v", err)
		return err
	}
	// 更新t_intent_slot表
	err = txUpdateIntentSlotBySlotID(ctx, tx, action, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry txUpdateIntentSlotBySlotID Failed err:%v", err)
		return err
	}
	// 更新t_task_flow表
	err = txUpdateTaskFlowByFlowInfo(ctx, tx, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry txUpdateTaskFlowByFlowInfo Failed err:%v", err)
		return err
	}
	return nil
}

// entrySetRedis 词条同步到redis
func entrySetRedis(ctx context.Context, robotID string, tx *gorm.DB, entityID string) error {
	redisEntryInfo, err := GetRedisEntryInfo(ctx, tx, robotID, entityID)
	if err != nil {
		return err
	}
	key := GetEntityEntriesKey(ctx, SandboxEnv, robotID)
	err = database.GetRedis().HSet(ctx, key, entityID, redisEntryInfo).Err()
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry SyncToRedis Failed!,key:%s,filedKey:%s,val:%s,err:%+v",
			key, entityID, redisEntryInfo, err)
		return err
	}
	log.InfoContextf(ctx, "Sync2Redis success, key:%s,val:%s", key, redisEntryInfo)
	return nil
}

// txUpsertDeleteEntryTable 增删改词条db
func txUpsertDeleteEntryTable(ctx context.Context, tx *gorm.DB, insertEntries, updateEntries,
	deleteEntries []entity.Entry, flowInfos []TaskFlowInfo) (err error) {
	// 新建词条时,插入t_entry表
	if len(insertEntries) > 0 {
		err = tx.Model(&entity.Entry{}).CreateInBatches(&insertEntries, config.GetMainConfig().TaskFlow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "TXUpsertDeleteEntry CreateEntry Failed err:%v", err)
			return err
		}
	}
	// 删除词条时，更新t_entry表
	if len(deleteEntries) > 0 {
		// 删除词条的时候需要判断词条是否被引用, 删除只在删除词条处使用，则可以再删除处判断是否引用
		entityID := deleteEntries[0].EntityID
		var entryIDs []string
		for _, entry := range deleteEntries {
			entryIDs = append(entryIDs, entry.EntryID)
		}
		err = tx.Model(&entity.Entry{}).
			Where("f_entry_id in ? and f_entity_id = ? ", entryIDs, entityID).
			Updates(map[string]interface{}{
				"f_release_status": entity.ReleaseStatusUnPublished,
				"f_action":         entity.ActionDelete,
				"f_is_deleted":     entity.DeleteByEntry,
			}).Error
		if err != nil {
			log.ErrorContextf(ctx, "DeleteEntry Failed err:%v", err)
			return err
		}
		if err = tx.Model(&entity.IntentEntry{}).
			Where("f_entry_id IN (?) and f_is_deleted = 0 ", entryIDs).
			Updates(map[string]interface{}{
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
				"f_update_time":    time.Now(),
				"f_is_deleted":     entity.DeleteByEntry,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "txUpsertDeleteEntryTable Failed!|entryIds:%+v|%s", entryIDs, err)
			return err
		}

	}

	// 更新词条时，更新t_entry表
	if len(updateEntries) > 0 {
		updateEntriesId := make([]string, 0)
		for _, entry := range updateEntries {
			//action := CalcActionByFlowReleaseStatus(flowInfos)
			updateEntriesId = append(updateEntriesId, entry.EntryID)
			err = tx.Model(&entity.Entry{}).
				Where("f_entry_id = ? and f_entity_id = ? ", entry.EntryID, entry.EntityID).
				Updates(map[string]interface{}{
					"f_entry_value":    entry.EntryValue,
					"f_entry_alias":    entry.EntryAlias,
					"f_release_status": entry.ReleaseStatus,
					"f_action":         entity.ActionUpdate,
				}).Error
			log.InfoContextf(ctx, "updateEntry action:%s,data:%+v", entity.ActionUpdate, entry)
			if err != nil {
				log.ErrorContextf(ctx, "UpdateEntry Failed err:%v", err)
				return err
			}
		}
		// 更新IntentEntry绑定关系
		if err = txUpdateIntentEntryByEntryIds(ctx, tx, entity.ActionUpdate, updateEntriesId); err != nil {
			log.ErrorContextf(ctx, "txUpdateIntentEntryByEntryIds Failed err:%+v", err)
			return err
		}
	}
	return nil
}

// GetRedisEntryInfo 根据指实体条ID获取下面所有词条，转化成redis的value值
func GetRedisEntryInfo(ctx context.Context, tx *gorm.DB, robotID, entityID string) (redisEntryInfo string, err error) {

	// 1. 找到entryInfo
	var entryInfo []*entity.Entry
	err = tx.Model(&entity.Entry{}).
		Where("f_entity_id = ? and f_is_deleted = 0", entityID).Find(&entryInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "getRedisEntryInfo Failed! err:%+v", err)
		return "", err
	}

	// 2. 构建redisEntryInfo信息
	entryInfoMap := make(map[string]RedisEntryInfo, 0)
	for _, entry := range entryInfo {
		aliasArray := []string{}
		_ = json.Unmarshal([]byte(entry.EntryAlias), &aliasArray)
		redisEntryInfo := RedisEntryInfo{
			Value:     entry.EntryValue,
			AliasName: aliasArray,
		}
		entryInfoMap[entry.EntryID] = redisEntryInfo

	}
	redisEntryVal, err := json.Marshal(entryInfoMap)
	if err != nil {
		log.ErrorContextf(ctx, "GetFinalEntryInfos json.Marshal Failed,data:%+v,err:%+v", entryInfoMap, err)
		return "", err
	}
	return string(redisEntryVal), nil
}

// TxUpsertDeletedEntryVectorGroupTable 增删改词条向量
func TxUpsertDeletedEntryVectorGroupTable(ctx context.Context, robotID string, insertEntries, updateEntries,
	deleteEntries []entity.Entry) (err error) {
	sid := util.RequestID(ctx)

	vectorDB := vdao.NewDao()
	sandboxGroupID, _, err := vectorDB.GetEntryVectorGroupID(ctx, robotID)
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry.upsertVectorEntry getGroupID Failed! err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "EntryVectorGroupID, groupID:%s,insertEntries:%+v,updateEntries:%+v,deleteEntries:%+v",
		sandboxGroupID, insertEntries, updateEntries, deleteEntries)
	// 新建和更新词条时,upsert检索词条向量组
	// 更新和插入方法相同,向量库暂时不支持批量操作
	insertEntries = append(insertEntries, updateEntries...)
	for _, v := range insertEntries {
		err = vectorDB.UpsertEntryVector(ctx, robotID, sandboxGroupID, v.EntryID, v.EntryValue, v.EntityID)
		if err != nil {
			log.ErrorContextf(ctx, "TXUpsertDeleteEntry.upsertVectorEntry Failed! sid:%s,err:%+v", sid, err)
			return err
		}
	}

	// 删除词条时，delete检索词条向量组
	if len(deleteEntries) > 0 {
		entryIds := make([]string, 0)
		for _, v := range deleteEntries {
			entryIds = append(entryIds, v.EntryID)
		}
		err = vectorDB.DeleteEntryVector(ctx, robotID, sandboxGroupID, entryIds)
		if err != nil {
			log.ErrorContextf(ctx, "TXUpsertDeleteEntry.deletedEntryVector Failed! sid:%s,err:%+v", sid, err)
			return err
		}
	}

	return nil

}

// GetEntryList 模糊查询实体下所有词条
func GetEntryList(ctx context.Context, entityID, keyword string) ([]*entity.Entry, error) {
	sid := util.RequestID(ctx)

	var entryInfo []*entity.Entry
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Model(&entity.Entry{}).
		Where("f_entity_id = ? and f_is_deleted = 0", entityID)

	if len(keyword) > 0 {
		likeKeyword := "%" + keyword + "%"
		db = db.Where("f_entry_value LIKE ? or f_entry_alias LIKE ? ", likeKeyword, likeKeyword)
	}
	err := db.Order("f_id DESC").Find(&entryInfo).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warnf("GetEntryList RecordNotFound,sid:%s,entityID:%s", sid, entityID)
			return nil, nil
		}
		log.ErrorContextf(ctx, "sid:%s|GetEntryList|err:%+v", sid, err)
		return nil, err
	}
	return entryInfo, nil
}

// CreateEntryImportTask 创建词条导入任务流程
func CreateEntryImportTask(ctx context.Context, corpID, staffID uint64, importID, robotID, fileName string, task *entity.TaskFlowImport) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 导入任务
		if err := tx.Table(tableTaskFlowImport).Create(task).Error; err != nil {
			log.Errorf("CreateEntryImportTask createTask Failed! err:%v", err)
			return err
		}

		// 任务调度参数
		taskParams := entity.TaskFlowImportParentParams{
			RequestID: util.RequestID(ctx),
			CorpID:    corpID,
			StaffID:   staffID,
			RobotID:   robotID,
			ImportID:  importID,
			FileName:  fileName,
		}
		// 创建导入词条任务调度
		if err := scheduler.NewImportEntryTask(ctx, robotID, taskParams); err != nil {
			return err
		}

		// 创建导入任务通知
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId:     uint64(encode.StringToInt64(robotID)),
			PageId:       entity.NoticeWorkflowPageID,
			Type:         entity.NoticeTypeTaskFlowImport,
			Level:        entity.LevelInfo,
			RelateId:     uint64(encode.StringToInt64(importID)),
			Content:      fmt.Sprintf(entity.TaskFlowImportNoticeContentIng, fileName),
			IsGlobal:     false,
			IsAllowClose: false,
			CorpId:       corpID,
			StaffId:      staffID,
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建词条任务流程导入import失败 err:%+v", err)
		return err
	}
	return nil
}

// RobotEntityEntryInfo 词条信息
type RobotEntityEntryInfo struct {
	RobotID    string `json:"f_robot_id"   gorm:"column:f_robot_id"`
	EntityID   string `json:"f_entity_id"  gorm:"column:f_entity_id"`
	EntryID    string `json:"f_entry_id"   gorm:"column:f_entry_id"`
	EntryValue string `json:"f_entry_value" gorm:"column:f_entry_value"`
	EntryAlias string `json:"f_entry_alias" gorm:"column:f_entry_alias"`
}

// GetSandboxOrProductEntityEntriesInfo 根据应用ID及环境类型，获取沙箱或正式环境的词条信息
func GetSandboxOrProductEntityEntriesInfo(ctx context.Context, robotId string, envType string) (
	[]*RobotEntityEntryInfo, error) {
	var db *gorm.DB
	if envType == SandboxEnv {
		db = database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	} else {
		db = database.GetLLMRobotTaskProdGORM().WithContext(ctx).Debug()
	}
	db = db.Table("t_entity").
		Joins("inner join t_entry on t_entity.f_entity_id = t_entry.f_entity_id").
		Where("t_entity.f_is_deleted = 0 and t_entry.f_is_deleted = 0").
		Select("t_entity.f_robot_id as f_robot_id,t_entity.f_entity_id as f_entity_id," +
			"t_entry.f_entry_id as f_entry_id,t_entry.f_entry_value as f_entry_value," +
			"t_entry.f_entry_alias as f_entry_alias")

	if len(robotId) > 0 {
		db = db.Where("t_entity.f_robot_id = ?", robotId)
	}

	var results []*RobotEntityEntryInfo
	err := db.Find(&results).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetEntityEntriesInfo SandboxOrProduct failed! err:%+v", err)
		return nil, err
	}
	for _, v := range results {
		log.InfoContextf(ctx, "GetEntityEntriesInfo, SandboxOrProduct results:%+v", v)
	}

	return results, nil
}

// BatchGetEntryListBySlotIDList 通过SlotID集合查询词条
func BatchGetEntryListBySlotIDList(ctx context.Context, slotIDList []string) ([]*entity.Entry, error) {
	entryInfoList := make([]*entity.Entry, 0)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	querySql := queryEntryListBySlotIDList
	//slotIDList = append(slotIDList, "568f5b7c-feaf-4777-a005-e0b3f5d72646")
	querySql += ` and f_entity_id in (select f_entity_id from t_slot_entity where f_is_deleted = 0 
		and f_slot_id IN (?` + strings.Repeat(",?", len(slotIDList)-1) + "))"
	paramValues := make([]interface{}, 0)
	for _, slotID := range slotIDList {
		paramValues = append(paramValues, slotID)
	}
	err := db.Raw(querySql, paramValues...).Scan(&entryInfoList).Error
	if err != nil {
		log.Errorf("BatchGetEntryListBySlotIDList query sql:%s|err:%v", querySql, err)
		return nil, err
	}
	return entryInfoList, nil
}

// GetSlotEntriesBySlotId 通过 slotIds获取 SlotEntryMap
func GetSlotEntriesBySlotId(ctx context.Context, slotIds []string) (map[string][]*entity.SlotEntry, error) {
	var querySlotEntrie string
	slotEntryList := make([]*entity.SlotEntry, 0)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	if len(slotIds) > 0 {
		querySlotEntrie = `SELECT t.f_slot_id, r.f_entity_id,r.f_entry_id,r.f_entry_value,
    r.f_entry_alias FROM t_slot_entity AS t   INNER JOIN  t_entry AS r ON t.f_entity_id=r.f_entity_id 
     WHERE t.f_is_deleted=0 AND  t.f_slot_id IN (?` + strings.Repeat(",?", len(slotIds)-1) + ")"
	} else {
		querySlotEntrie = `SELECT t.f_slot_id, r.f_entity_id,r.f_entry_id,r.f_entry_value,
    r.f_entry_alias FROM t_slot_entity AS t   INNER JOIN  t_entry AS r ON t.f_entity_id=r.f_entity_id 
     WHERE t.f_is_deleted=0 `
	}

	paramValues := make([]interface{}, 0)
	for _, slotID := range slotIds {
		paramValues = append(paramValues, slotID)
	}
	err := db.Raw(querySlotEntrie, paramValues...).Scan(&slotEntryList).Error
	if err != nil {
		log.Errorf("GetSlotEntriesBySlotId|querySql:%s|paramValues:%+v|err:%v", querySlotEntrie, paramValues, err)
		return nil, err
	}

	slotEntriesMap := make(map[string][]*entity.SlotEntry)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetSlotEntriesBySlotId|err:%+v", util.RequestID(ctx), err)
		return nil, err
	}
	for _, v := range slotEntryList {
		slotEntriesMap[v.SlotID] = append(slotEntriesMap[v.SlotID], v)
	}
	return slotEntriesMap, nil
}

// GetEntriesMapByAppIdAndIds 获取应用下的Entry
func GetEntriesMapByAppIdAndIds(ctx context.Context, appId string, entriesId []string) (map[string]*entity.Entry, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	entryInfoList := make([]*entity.Entry, 0)
	var entityIds []string
	if err := db.Table(entity.Entity{}.TableName()).Select("f_entity_id").Where("f_is_deleted = 0  "+
		"and f_robot_id=?", appId).Scan(&entityIds).Error; err != nil {
		return nil, err
	}

	if err := db.Table(entity.Entry{}.TableName()).Where("f_is_deleted = 0 "+
		"and f_entry_id in ? and f_entity_id in ?", entriesId, entityIds).
		Scan(&entryInfoList).Error; err != nil {
		return nil, err
	}
	var entriesMap = make(map[string]*entity.Entry, 0)
	for _, v := range entryInfoList {
		entriesMap[v.EntryID] = v
	}
	return entriesMap, nil
}
