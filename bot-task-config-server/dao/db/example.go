// bot-task-config-server
//
// @(#)example.go  星期一, 四月 08, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	tdb "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func updateTaskFlowStatus(ctx context.Context, tx *gorm.DB, intentId, robotId string) error {
	taskflow := entity.TaskFlow{}
	sid := util.RequestID(ctx)
	if err := tx.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id=?", intentId).
		Find(&taskflow).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|updateTaskFlowAndSaveCorpusToVector|get taskflow error:%+v", sid, err)
		return err
	}
	// 通过任务流程的状态，判断action
	action := getActionByFlowReleaseStatus(&taskflow)
	finalFlowTaskStatus := getTaskFlowStatus(taskflow.ReleaseStatus, taskflow.FlowState)

	// 更新任务流程状态
	if err := tx.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id=?", intentId).
		Find(&taskflow).Updates(map[string]interface{}{
		"f_action":         action,
		"f_release_status": entity.ReleaseStatusUnPublished,
		"f_flow_state":     finalFlowTaskStatus,
		"f_update_time":    time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|updateTaskFlowAndSaveCorpusToVector|get taskflow error:%+v", sid, err)
		return err
	}
	return nil
}

// updateTaskFlowAndSaveCorpusToVector
func updateTaskFlowAndSaveCorpusToVector(ctx context.Context, tx *gorm.DB, intentId, robotId string) error {
	taskflow := entity.TaskFlow{}
	sid := util.RequestID(ctx)
	if err := tx.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id=?", intentId).
		Find(&taskflow).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|updateTaskFlowAndSaveCorpusToVector|get taskflow error:%+v", sid, err)
		return err
	}
	// 通过任务流程的状态，判断action
	action := getActionByFlowReleaseStatus(&taskflow)
	finalFlowTaskStatus := getTaskFlowStatus(taskflow.ReleaseStatus, taskflow.FlowState)

	// 更新任务流程状态
	if err := tx.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id=?", intentId).
		Find(&taskflow).Updates(map[string]interface{}{
		"f_action":         action,
		"f_release_status": entity.ReleaseStatusUnPublished,
		"f_flow_state":     finalFlowTaskStatus,
		"f_update_time":    time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|updateTaskFlowAndSaveCorpusToVector|get taskflow error:%+v", sid, err)
		return err
	}

	// 将语料及下面示例问法进行向量化保存，草稿态任务流程下的示例问法不保存向量库
	if taskflow.FlowState != entity.FlowStateDraft {
		if err := SaveIntentCorpusToVector(ctx, tx, robotId, intentId); err != nil {
			log.ErrorContextf(ctx, "sid:%s|updateTaskFlowAndSaveCorpusToVector"+
				"|SaveIntentCorpusToVector|err:%s|%v", sid, err)
			return err
		}
	}

	return nil
}

// recoverRelatedIntentExamples 恢复历史画布中的示例问法
func recoverRelatedIntentExamples(ctx context.Context, tx *gorm.DB,
	robotId, intentId, version string) error {
	var historyIntentExamples []entity.IntentCorpusPublishHistory
	var exitIntentExs []entity.IntentCorpus
	intentExamples := make([]entity.IntentCorpus, 0)
	sid := util.RequestID(ctx)

	// 将现有的标记删除
	if err := tx.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_intent_id=?", robotId, intentId).
		Find(&exitIntentExs).
		Updates(map[string]interface{}{
			"f_is_deleted": 1, "f_action": entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		return err
	}

	// 获取历史示例问法Id
	if err := tx.Table(entity.IntentCorpusPublishHistory{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id=? AND f_version=?"+
			" AND f_save_type=?", intentId, version, entity.EnumSaveType[entity.SavePublished]).
		Scan(&historyIntentExamples).Error; err != nil {
		return err
	}

	var robotIntentExample []entity.IntentCorpus
	// 查询 IntentCorpus 表中示例问法
	if err := tx.Table(entity.IntentCorpus{}.TableName()).
		Where("f_robot_id=? AND f_is_deleted=0", robotId).
		Find(&robotIntentExample).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|recoverRelatedIntentExamples"+
			"|duplicateIntentExample error:%+v", sid, err)
		return err
	}

	exitRobotExamples := make([]string, 0)
	for _, k := range robotIntentExample {
		exitRobotExamples = append(exitRobotExamples, k.Corpus)
	}

	// 现有历史示例问法中有应用机器人下重名的，将历史问法略过
	for _, v := range historyIntentExamples {
		if !util.StrInArray(v.Corpus, exitRobotExamples) {
			intentExamples = append(intentExamples, entity.IntentCorpus{
				IntentID: v.IntentID, CorpusID: v.CorpusID,
				Corpus: v.Corpus, Uin: v.Uin, RobotId: robotId, SubUin: v.SubUin,
				ReleaseStatus: entity.ReleaseStatusUnPublished, Action: entity.ActionUpdate,
			})
		}
	}
	log.InfoContextf(ctx, "sid:%s|recoverRelatedIntentExamples|intentExampleIds:%+v", sid, intentExamples)

	if len(intentExamples) > 0 {
		if err := tx.WithContext(ctx).Table(entity.IntentCorpus{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_corpus_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_id", "f_corpus_id", "f_intent_id", "f_corpus",
					"f_robot_id", "f_uin", "f_sub_uin", "f_release_status", "f_is_deleted", "f_action",
				}),
			}).Create(&intentExamples).Error; err != nil {
			log.ErrorContextf(ctx, "sid:%s|recoverRelatedIntentExamples err:%v", sid, err)
			return err
		}
	}

	if len(intentExamples) > 0 || len(exitIntentExs) > 0 {
		if err := updateTaskFlowAndSaveCorpusToVector(ctx, tx, intentId, robotId); err != nil {
			log.ErrorContextf(ctx, "sid:%s|recoverRelatedIntentExamples err:%v", sid, err)
			return err
		}
	}
	return nil
}

// createImportIntentCorpusExamples 导入创建示例问法
func createImportIntentCorpusExamples(ctx context.Context, tx *gorm.DB,
	robotId string, examples []*entity.IntentCorpus) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|createImportIntentCorpusExamples|robotID:%s|len(examples):%d",
		sid, robotId, len(examples))
	if len(examples) > 0 {
		err := tx.Model(&entity.IntentCorpus{}).Create(examples).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|createImportIntentCorpusExamples,CreateIntentCorpus Failed,err:%v", sid, err)
			return err
		}
	}
	return nil
}

// GetExampleHistroyListByIntentId 获取历史任务流程下的示例问法
func GetExampleHistroyListByIntentId(ctx context.Context, keyword, botBizId,
	intentId, bizVersion, saveType string) (*[]entity.IntentCorpus, int64, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var examples *[]entity.IntentCorpus
	var total int64
	var err error

	result := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_corpus_id IN (SELECT f_corpus_id FROM t_intent_corpus_publish_history WHERE"+
			" f_is_deleted=0 AND f_intent_id=? "+
			"AND f_save_type=? AND f_version=?)", intentId, entity.EnumSaveType[saveType], bizVersion).
		Order("f_update_time DESC").Scan(&examples).Count(&total)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) || len(*examples) == 0 {
		// 如果草稿态下没有，则去主表查
		examples, total, err = tdb.GetExampleListByBotAndIntentId(ctx, keyword, botBizId, intentId)
		if err != nil {
			return nil, 0, err
		}
	}
	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleListByBotAndIntentId:err:%+v", sid, result.Error)
		return nil, 0, result.Error
	}

	return examples, total, nil
}

// GetImportIntentExampleInfos 通过示例问法 获取导入的全部示例用法信息
func GetImportIntentExampleInfos(ctx context.Context, robotID string,
	examples []string) (map[string]*entity.IntentCorpus, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|GetImportIntentExampleInfos|robotID:%s|len(examples):%d",
		sid, robotID, len(examples))
	if len(robotID) == 0 || len(examples) == 0 {
		return nil, nil
	}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var intentExamples []*entity.IntentCorpus
	err := db.Table(entity.IntentCorpus{}.TableName()).Where("f_is_deleted=0 AND f_robot_id=?", robotID).
		Where("f_corpus IN ?", examples).
		Find(&intentExamples).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetImportIntentExampleInfos db.Find Failed,err:%v", sid, err)
		return nil, err
	}
	exampleCorpusMap := make(map[string]*entity.IntentCorpus)
	for _, v := range intentExamples {
		exampleCorpusMap[v.Corpus] = v
	}

	return exampleCorpusMap, nil
}

// GetExportIntentExampleInfos 通过intentIDs获取导出时全部示例问法
func GetExportIntentExampleInfos(ctx context.Context, robotID string, intentIDs []string) (
	map[string][]*entity.IntentCorpus, error) {
	mapIntentCorpus := make(map[string][]*entity.IntentCorpus)
	if len(intentIDs) == 0 {
		return mapIntentCorpus, nil
	}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		var txErr error
		mapIntentCorpus, txErr = GetIntentCorpusByIntentIDsWithTx(ctx, tx, robotID, intentIDs)
		return txErr
	})
	if err != nil {
		return nil, err
	}
	return mapIntentCorpus, nil
}

// GetIntentCorpusByIntentIDsWithTx 通过意图IDs获取意图示例问法
func GetIntentCorpusByIntentIDsWithTx(ctx context.Context, tx *gorm.DB, robotId string, intentIDs []string) (
	map[string][]*entity.IntentCorpus, error) {
	var intentCorpus []*entity.IntentCorpus
	sid := util.RequestID(ctx)
	mapIntentCorpus := make(map[string][]*entity.IntentCorpus)
	if len(intentIDs) == 0 {
		return mapIntentCorpus, nil
	}
	err := tx.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_robot_id=? AND f_intent_id IN ?", robotId, intentIDs).
		Find(&intentCorpus).Error
	if err != nil {
		log.Errorf("GetIntentCorpusByIntentIDsWithTx failed,intentIDs:%s|%+v|err:%+v", sid, intentIDs, err)
		return nil, err
	}
	for _, v := range intentCorpus {
		mapIntentCorpus[v.IntentID] = append(mapIntentCorpus[v.IntentID], v)
	}
	return mapIntentCorpus, nil
}

// GetExampleForRobotIntentCount 获取机器人意图下面示例问法的总数
func GetExampleForRobotIntentCount(ctx context.Context, robotId, intentId string) (int64, error) {
	var count int64
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_intent_id=?", robotId, intentId).
		Count(&count).Error; err != nil &&
		!errors.Is(err, gorm.ErrRecordNotFound) {
		log.ErrorContextf(ctx, "sid:%s|GetExampleForRobotIntent|robotId:%s|intentId:%s",
			util.RequestID(ctx), robotId, intentId)
		return 0, err
	}
	return count, nil
}

// GetExampleByNameCorpusId 通过Id获取机器人下面的示例问法
func GetExampleByNameCorpusId(ctx context.Context, robotId, corpusId string) (*entity.IntentCorpus, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var example *entity.IntentCorpus

	result := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_corpus_id=?", robotId, corpusId).Find(&example)

	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleByNameInRobot|robotId:%s|corpus:%s",
			util.RequestID(ctx), robotId, corpusId)
		return nil, result.Error
	}

	if result.RowsAffected == 0 {
		return nil, nil
	}
	return example, nil
}

// GetExampleByNameInRobot 通过名称获取机器人下面的示例问法
func GetExampleByNameInRobot(ctx context.Context, robotId, corpus string) ([]*entity.IntentCorpus, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var example []*entity.IntentCorpus

	// 注意example中英文符号区别，肉眼比较难看出来
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_corpus=?",
			robotId, strings.TrimSpace(corpus)).Find(&example).Error; err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetExampleByNameInRobot|robotId:%s|example:%+v", util.RequestID(ctx),
		robotId, example)

	return example, nil
}

// CreateExampleIntentCorpus 创建示例问法语料
func CreateExampleIntentCorpus(ctx context.Context, intentCorpus *entity.IntentCorpus) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(entity.IntentCorpus{}.TableName()).Create(intentCorpus).Error; err != nil {
			return err
		}

		err := updateTaskFlowAndSaveCorpusToVector(ctx, tx, intentCorpus.IntentID, intentCorpus.RobotId)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

// UpdateExampleIntentCorpus 更新示例问法
func UpdateExampleIntentCorpus(ctx context.Context, botBizId, exampleId, exam string) error {
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	intentCorpus := entity.IntentCorpus{}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		rTx := tx.Table(entity.IntentCorpus{}.TableName()).
			Where("f_is_deleted=0 AND f_robot_id=? AND f_corpus_id=?", botBizId, exampleId).Find(&intentCorpus)
		if rTx.RowsAffected == 0 {
			return errors.ErrCorpusNotFound
		}
		// 通过IntentId 获取任务流程
		intentId := intentCorpus.IntentID

		if err := tx.Table(entity.IntentCorpus{}.TableName()).
			Where("f_is_deleted=0 AND f_robot_id=? AND f_corpus_id=?", botBizId, exampleId).
			Updates(map[string]interface{}{
				"f_corpus":         exam,
				"f_sub_uin":        subUin,
				"f_action":         entity.ActionUpdate,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			return err
		}

		err := updateTaskFlowAndSaveCorpusToVector(ctx, tx, intentId, botBizId)
		if err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}
	return nil
}

// DeleteExampleIntentCorpus 删除示例问法
func DeleteExampleIntentCorpus(ctx context.Context, botBizId, exampleId string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	sid := util.RequestID(ctx)
	vdb := vdao.NewDao()

	if err := db.Transaction(func(tx *gorm.DB) error {
		intentExample := entity.IntentCorpus{}

		if err := tx.Table(entity.IntentCorpus{}.TableName()).
			Where("f_is_deleted=0 AND f_robot_id=? AND f_corpus_id=?", botBizId, exampleId).
			Find(&intentExample).
			Updates(map[string]interface{}{
				"f_is_deleted":     gorm.Expr("f_id"),
				"f_sub_uin":        subUin,
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "sid:%s|UpdateExampleIntentCorpus fail, err:%+v", sid, err)
			return err
		}

		if err := vdb.DeleteIntentExampleVector(ctx, botBizId, []string{intentExample.CorpusID}); err != nil {
			log.ErrorContextf(ctx, "DeleteIntentExampleVector fail err:%s", sid)
			return err
		}

		err := updateTaskFlowStatus(ctx, tx, intentExample.IntentID, botBizId)
		if err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}

	return nil
}

// GetExampleListByBotAndIntentIdByTx 获取任务流程下的示例问法
func GetExampleListByBotAndIntentIdByTx(ctx context.Context, tx *gorm.DB, botBizId,
	intentId string) (*[]entity.IntentCorpus, int64, error) {
	sid := util.RequestID(ctx)
	var examples *[]entity.IntentCorpus
	var total int64

	result := tx.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_intent_id=?", botBizId, intentId).
		Order("f_update_time DESC").Scan(&examples).Count(&total)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, 0, nil
	}
	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleListByBotAndIntentId:err:%+v", sid, result.Error)
		return nil, 0, result.Error
	}
	return examples, total, nil
}

// GetIntentExampleByRobotId 通过robotID获取应用下的示例问法
func GetIntentExampleByRobotId(ctx context.Context, botBizId string) (*[]entity.IntentCorpus, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	var examples *[]entity.IntentCorpus
	var total int64

	result := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=?", botBizId).
		Order("f_update_time DESC").Scan(&examples).Count(&total)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleListByBotAndIntentId:err:%+v", sid, result.Error)
		return nil, result.Error
	}
	return examples, nil
}

// getActionByFlowReleaseStatus 通过 TaskFlow 状态获取 action状态
func getActionByFlowReleaseStatus(flowInfo *entity.TaskFlow) string {
	if flowInfo.FlowState == entity.FlowStatePublishChange ||
		((flowInfo.FlowState == entity.FlowStateEnable || flowInfo.FlowState == entity.FlowStateChangeUnPublish) &&
			flowInfo.ReleaseStatus == entity.ReleaseStatusPublished) {
		return entity.ActionUpdate
	} else {
		return entity.ActionInsert
	}
}

// GetIntentIdsByDeleteUnPublishedExample 获取删除已发布/发布已修改，示例问法的IntentId
func GetIntentIdsByDeleteUnPublishedExample(ctx context.Context) ([]string, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	// 先获取所有异常的IntentId
	var intentIds []string
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Select("f_intent_id").Where("f_is_deleted!=0 AND f_release_status='UNPUBLISHED'").
		Scan(&intentIds).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetIntentIdsByDeleteExample|err:%+v", sid, err)
		return intentIds, err
	}
	var publishIntentIds []string
	// 再通过意图状态判断，过滤 获取已发布意图
	if err := db.Table(entity.TaskFlow{}.TableName()).
		Select("f_intent_id").
		Where("f_release_status='PUBLISHED' AND f_intent_id IN ?", intentIds).
		Scan(&publishIntentIds).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetIntentIdsByDeleteExample|err:%+v", sid, err)
		return publishIntentIds, err
	}
	return publishIntentIds, nil
}

// GetDeleteExamples 获取删除的示例问法
func GetDeleteExamples(ctx context.Context) ([]entity.IntentCorpus, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	// 先获取所有异常的IntentId
	var examples []entity.IntentCorpus
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted!=0").
		Scan(&examples).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetDeleteExamples|err:%+v", sid, err)
		return examples, err
	}
	return examples, nil
}

// GetDeleteExamplesByRobotId 获取机器人下删除的示例问法
func GetDeleteExamplesByRobotId(ctx context.Context, robotIds []string) ([]entity.IntentCorpus, error) {
	// 先获取所有异常的IntentId
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var examples []entity.IntentCorpus
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted!=0 AND f_robot_id IN ?", robotIds).
		Scan(&examples).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetDeleteExamples|err:%+v", sid, err)
		return examples, err
	}
	return examples, nil
}

// GetRobotExampleMap 获取机器人及关联的示例问法Map
func GetRobotExampleMap(ctx context.Context, intentIds []string) (
	map[string][]*entity.IntentCorpus, []string, error) {
	intentExamples := make([]*entity.IntentCorpus, 0)
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	rExaMap := make(map[string][]*entity.IntentCorpus, 0)
	robotArr := make([]string, 0)
	mm := make(map[string]struct{}, 0)

	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_release_status != ?", entity.ReleaseStatusPublished).
		Where("f_intent_id IN ?", intentIds).
		Find(&intentExamples).Error; err != nil {
		log.Errorf("GetRobotExampleMap,intentIDs:%s|%+v|err:%+v", sid, intentIds, err)
		return rExaMap, robotArr, err
	}
	for _, v := range intentExamples {
		rExaMap[v.RobotId] = append(rExaMap[v.RobotId], v)
	}
	for _, v := range intentExamples {
		if _, ok := mm[v.RobotId]; !ok {
			robotArr = append(robotArr, v.RobotId)
			mm[v.RobotId] = struct{}{}
		}
	}
	return rExaMap, robotArr, nil
}
