// bot-task-config-server
//
// @(#)feedback.go  星期三, 十月 09, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	utError "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// AddWorkflowFeedback  添加工作流测试反馈
func AddWorkflowFeedback(ctx context.Context, fb *entity.WorkflowFeedbackRecord,
	reasons []*entity.WorkflowFeedbackReason, appID uint64) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 1. 创建反馈原因记录
		if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).Create(reasons).Error; err != nil {
			return err
		}
		// 2. 创建反馈记录
		if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).Create(fb).Error; err != nil {
			return err
		}

		// 获取工作流信息
		flowName := ""
		workflow, err := GetWorkflowDetail(ctx, fb.WorkflowID, fb.RobotId)
		if err != nil {
			return utError.ErrWorkflowNotFound
		}
		if workflow != nil {
			flowName = workflow.WorkflowName
		}
		// 3. 发送反馈消息信息
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId: appID,
			PageId:   entity.NoticeZeroPageID,
			Type:     entity.OpTypeViewDetail,
			Level:    entity.LevelSuccess,
			RelateId: uint64(encode.StringToInt64(fb.BizId)),
			Content: fmt.Sprintf(entity.AddWorkFeedbackSuccessNoticeContent,
				atMost(flowName), fb.BizId),
			Subject:      "反馈信息",
			IsGlobal:     true,
			IsAllowClose: true,
			StaffId:      util.StaffID(ctx),
			CorpId:       util.CorpID(ctx),
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}
	return nil
}

func atMost(s string) string {
	if utf8.RuneCountInString(s) > entity.MaxShowQuestionLength {
		return string([]rune(s)[:entity.MaxShowQuestionLength]) + "..."
	}
	return s
}

// ListWorkflowFeedback 获取工作流反馈列表
func ListWorkflowFeedback(ctx context.Context,
	req *KEP_WF.ListFlowFeedbackReq) (*[]entity.WorkflowFeedbackRecord, int64, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	pageSize := uint32(15)
	pageNum := uint32(1)
	appBizId := req.GetAppBizId()
	sid := util.RequestID(ctx)
	query := strings.TrimSpace(req.GetQuery())
	reasons := req.GetReasons()
	status := req.GetStatus()
	corpId := util.CorpID(ctx)

	var total int64
	var recordList *[]entity.WorkflowFeedbackRecord

	listQuery := db.Table(entity.WorkflowFeedbackRecord{}.TableName()).
		Where("f_is_deleted=0").Order("f_update_time DESC")

	if corpId > 0 {
		listQuery = listQuery.Where("f_corp_id=?", corpId)
	}

	if len(appBizId) > 0 {
		listQuery = listQuery.Where("f_robot_id=?", appBizId)
	}

	if len(query) > 0 {
		q := "%" + query + "%"
		listQuery = listQuery.Where("f_question LIKE ? OR f_answer LIKE ? "+
			"OR f_desc LIKE ?", q, q, q)
	}
	if len(status) > 0 {
		listQuery = listQuery.Where("f_status IN ?", status)
	}
	if len(reasons) > 0 {
		feedbackIds, err := getFeedbackIdsByReasons(ctx, appBizId, reasons)
		if err != nil {
			log.ErrorContextf(ctx, "ListWorkflowFeedback,sid:%s|err:%+v", sid, err)
			return recordList, 0, err
		}
		listQuery = listQuery.Where("f_biz_id IN ?", feedbackIds)
	}

	if req.GetPageSize() > 0 {
		pageSize = req.GetPageSize()
	}
	if req.GetPage() > 0 {
		pageNum = req.GetPage()
	}
	offset := (pageNum - 1) * pageSize

	if err := listQuery.Count(&total).Offset(int(offset)).Limit(int(pageSize)).
		Scan(&recordList).Error; err != nil {
		log.ErrorContextf(ctx, "ListWorkflowFeedback,sid:%s|err:%+v", sid, err)
		return recordList, 0, err
	}
	return recordList, total, nil

}

// GetWorkflowFeedBackRecordByBizId 获取反馈记录
func GetWorkflowFeedBackRecordByBizId(ctx context.Context,
	feedbackBizId string) (*entity.WorkflowFeedbackRecord, error) {
	feedRecord := &entity.WorkflowFeedbackRecord{}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.WorkflowFeedbackRecord{}.TableName()).
		Where("f_is_deleted=0 AND f_biz_id=?", feedbackBizId).Take(feedRecord).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return feedRecord, err
	}

	return feedRecord, nil
}

// getFeedbackIdsByReasons 通过反馈Id获取反馈原因
func getFeedbackIdsByReasons(ctx context.Context, appId string, reasons []string) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var feedbackIds []string
	res := db.Table(entity.WorkflowFeedbackReason{}.TableName()).Select("f_feedback_id").
		Where("f_is_deleted=0")

	if len(appId) > 0 {
		res = res.Where("f_robot_id=?", appId)
	}

	if len(reasons) > 0 {
		// 判断一下Reasons 是否包含其他原因
		res = res.Where("f_reason IN ?", reasons)
	}
	if err := res.Scan(&feedbackIds).Error; err != nil {
		return nil, err
	}
	return feedbackIds, nil
}

// GetFeedbackReasonByBizId ...
func GetFeedbackReasonByBizId(ctx context.Context,
	feedbackIds []string) (map[string][]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	feedReasons := make([]*entity.FeedReasons, 0)

	if err := db.Table(entity.WorkflowFeedbackReason{}.TableName()).Select("f_reason, f_feedback_id").
		Where("f_is_deleted=0 AND f_feedback_id IN ?", feedbackIds).
		Scan(&feedReasons).Error; err != nil {
		return nil, err
	}
	var feedReasonMap = make(map[string][]string, 0)
	if len(feedReasons) > 0 {
		for _, v := range feedReasons {
			feedReasonMap[v.FeedbackId] = append(feedReasonMap[v.FeedbackId], v.Reason)
		}
	}
	return feedReasonMap, nil
}

// DeleteWorkflowFeedback 删除工作流反馈
func DeleteWorkflowFeedback(ctx context.Context, feedbackBizIds []string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	if err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).
			Where("f_is_deleted=0 AND f_biz_id IN ?", feedbackBizIds).
			Updates(map[string]interface{}{
				"f_is_deleted": 1,
				"f_sub_uin":    subUin,
			}).Error; err != nil {
			return err
		}

		// 删除原因
		if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).
			Where("f_is_deleted=0 AND f_feedback_id IN ?", feedbackBizIds).
			Updates(map[string]interface{}{
				"f_is_deleted": 1,
				"f_sub_uin":    subUin,
			}).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowFeedback fail, err:%+v", err)
		return err
	}
	return nil
}

// DeleteWorkflowFeedbackByFlowIdTx 删除工作流对应的反馈信息
func DeleteWorkflowFeedbackByFlowIdTx(ctx context.Context, tx *gorm.DB, flowIds []string, appBizId string) error {
	var records []*entity.WorkflowFeedbackRecord
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	// 删除记录并获取recordBizIds
	if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).
		Where("f_is_deleted=0 AND f_workflow_id IN ?", flowIds).Find(&records).
		Updates(map[string]interface{}{
			"f_is_deleted": 1,
			"f_sub_uin":    subUin,
		}).Error; err != nil {
		return err
	}

	recordBizIds := make([]string, 0)
	for _, v := range records {
		recordBizIds = append(recordBizIds, v.BizId)
	}

	// 删除原因
	if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).
		Where("f_is_deleted=0 AND f_feedback_id IN ?", recordBizIds).
		Updates(map[string]interface{}{
			"f_is_deleted": 1,
			"f_sub_uin":    subUin,
		}).Error; err != nil {
		return err
	}

	// 删除对应的评测对话反馈记录
	if err := rpc.DeleteFeedbackByFlowIds(ctx, flowIds, cast.ToUint64(appBizId)); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow|DeleteFeedbackByFlowIds｜%v", err)
		return err
	}
	return nil
}

// UpdateWorkflowFeedback 更新工作流反馈
func UpdateWorkflowFeedback(ctx context.Context,
	param *entity.WorkflowFeedbackRecord, reasons []*entity.WorkflowFeedbackReason) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 先删除原因
		if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).
			Where("f_is_deleted=0 AND f_feedback_id=?", param.BizId).Updates(map[string]interface{}{
			"f_is_deleted": 1,
			"f_sub_uin":    subUin,
		}).Error; err != nil {
			return err
		}
		if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).
			Where("f_is_deleted=0 AND f_biz_id=?", param.BizId).
			Updates(map[string]interface{}{
				"f_answer":      param.Answer,
				"f_question":    param.Question,
				"f_node_id":     param.NodeId,
				"f_workflow_id": param.WorkflowID,
				"f_session_id":  param.SessionId,
				"f_record_id":   param.RecordId,
				"f_sub_uin":     subUin,
				"f_desc":        param.Desc,
			}).Error; err != nil {
			return err
		}

		// 新建原因
		if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).Create(reasons).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowFeedback fail, err:%+v", err)
		return err
	}
	return nil
}

// UpdateWorkflowFeedbackStatus 更新工作流反馈状态（内部接口）
func UpdateWorkflowFeedbackStatus(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackStatusReq) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	status := req.GetStatus()
	rejectReason := req.GetRejectReason()
	res := req.GetOptimizedResult()
	bizId := req.GetFeedbackBizId()
	if err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).Where("f_is_deleted=0 AND f_biz_id=?", bizId).
			Updates(map[string]interface{}{
				"f_status":           status,
				"f_reject_reason":    rejectReason,
				"f_andon_ticket":     req.GetAndonTicket(),
				"f_andon_url":        req.GetAndonUrl(),
				"f_optimized_result": res,
				"f_uin":              uin,
				"f_sub_uin":          subUin,
			}).Error; err != nil {
			return err
		}

		if status == entity.FeedbackStatusFinished || status == entity.FeedbackStatusRejected {
			fb, err := GetWorkflowFeedBackRecordByBizId(ctx, bizId)
			if err != nil {
				log.ErrorContextf(ctx, "获取 feedback 失败 err:%+v", err)
				return err
			}

			notice := fmt.Sprintf(entity.FinishedFeedbackSuccessNoticeContent, fb.BizId, atMost(fb.Question))
			lv := entity.LevelSuccess
			if status == entity.FeedbackStatusRejected {
				notice = fmt.Sprintf(entity.FinishedFeedbackRejectNoticeContent, atMost(fb.Question), fb.BizId)
				lv = entity.LevelWarning
			}
			createNoticeReq := &pb.CreateNoticeReq{
				BotBizId: cast.ToUint64(fb.RobotId),
				PageId:   entity.NoticeZeroPageID,
				Type:     entity.OpTypeViewDetail,
				Level:    lv, RelateId: fb.ID, Content: notice,
				Subject:  "反馈信息",
				IsGlobal: true, IsAllowClose: true,
				StaffId: fb.StaffId, CorpId: fb.CorpId,
			}
			if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowFeedbackStatus fail, err:%+v", err)
		return err
	}
	return nil
}

// UpdateWorkflowFeedbackTapd 更新tapd
func UpdateWorkflowFeedbackTapd(ctx context.Context, fBizId, tapd string) error {
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Table(entity.WorkflowFeedbackRecord{}.TableName()).Where("f_is_deleted=0 AND f_biz_id=?", fBizId).
		Updates(map[string]interface{}{
			"f_tapd":    tapd,
			"f_sub_uin": subUin,
		}).Error; err != nil {
		return err
	}
	return nil
}
