package db

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

const (
	tableIntent = "t_intent"
)

// GetIntentByIntentIDs 通过意图ID获取意图信息
func GetIntentByIntentIDs(ctx context.Context, intentIDs []string) (map[string]*entity.Intent, error) {
	var intents []*entity.Intent
	mapIntent := make(map[string]*entity.Intent)
	if len(intentIDs) == 0 {
		return mapIntent, nil
	}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	err := db.Table(tableIntent).
		Where("f_is_deleted = 0").
		Where("f_intent_id IN ?", intentIDs).
		Find(&intents).Error
	if err != nil {
		log.Errorf("GetIntentByIntentIDs failed,intentIDs:%s|%+v,err:%+v", sid, intentIDs, err)
		return nil, err
	}
	for _, v := range intents {
		mapIntent[v.IntentID] = v
	}
	return mapIntent, nil
}

//
//// txUpdateIntentByFlowInfo 通过任务流信息更新t_intent表
//func txUpdateIntentByFlowInfo(ctx context.Context, tx *gorm.DB, action string, flowInfos []TaskFlowInfo) error {
//	sid := util.RequestID(ctx)
//
//	for _, taskFlowInfo := range flowInfos {
//		err := tx.Model(&entity.Intent{}).
//			Where("f_intent_id = ? and f_is_deleted = 0 ", taskFlowInfo.TaskIntentID).
//			Updates(map[string]interface{}{
//				"f_action":         action,
//				"f_release_status": entity.ReleaseStatusUnPublished,
//				"f_update_time":    time.Now(),
//			}).Error
//		if err != nil {
//			log.Errorf("txUpdateIntentByFlowInfo Failed! |%s|%s", sid, err)
//			return err
//		}
//	}
//	return nil
//}
//
//// txUpdateIntentFlowByFlowInfo 通过任务流信息更新t_intent表
//func txUpdateIntentFlowByFlowInfo(ctx context.Context, tx *gorm.DB, action string, flowInfos []TaskFlowInfo) error {
//	sid := util.RequestID(ctx)
//
//	for _, taskFlowInfo := range flowInfos {
//		err := tx.Model(&entity.IntentFlow{}).
//			Where("f_flow_id = ? and f_intent_id = ? and f_is_deleted = 0 ", taskFlowInfo.TaskFlowID,
//				taskFlowInfo.TaskIntentID).
//			Updates(map[string]interface{}{
//				"f_action":         action,
//				"f_release_status": entity.ReleaseStatusUnPublished,
//				"f_update_time":    time.Now(),
//			}).Error
//		if err != nil {
//			log.Errorf("txUpdateIntentFlowByFlowInfo Failed! |%s|%s", sid, err)
//			return err
//		}
//	}
//	return nil
//}
