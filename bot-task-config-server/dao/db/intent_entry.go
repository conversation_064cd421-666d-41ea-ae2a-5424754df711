package db

import (
	"context"
	"errors"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
	"time"
)

// GetTaskFlowsByEntryId 通过词条ID获取画布
func GetTaskFlowsByEntryId(ctx context.Context, appId, entryId string) ([]entity.TaskFlow, error) {
	sid := util.RequestID(ctx)
	var tfInfos []entity.TaskFlow
	var entityInfos []entity.Entity
	var entrys []entity.Entry
	entityIds := make([]string, 0)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	// 判断应用下是否有该词条
	if err := db.Table(entity.Entry{}.TableName()).Where("f_entry_id=? AND f_is_deleted=0", entryId).
		Find(&entrys).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|getTaskFlowsByEntryId|get Entry|err:%+v", sid, err)
		return nil, err
	}
	for _, item := range entrys {
		entityIds = append(entityIds, item.EntityID)
	}
	if err := db.Table(entity.Entity{}.TableName()).Where("f_robot_id=? AND f_entity_id IN ?",
		appId, entityIds).Find(&entityInfos).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|getTaskFlowsByEntryId|getEntity|err:%+v", sid, err)
		return nil, err
	}
	if len(entityInfos) == 0 {
		return nil, fmt.Errorf("应用%s下不存在词条Id为:%s的词条", appId, entryId)
	}

	if err := db.Table(entity.TaskFlow{}.TableName()).Where("f_intent_id IN (SELECT f_intent_id FROM"+
		" t_intent_entry WHERE f_is_deleted=0 AND f_entry_id=?)", entryId).Find(&tfInfos).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|getTaskFlowsByEntryId|err:%+v", sid, err)
		return nil, err
	}
	return tfInfos, nil
}

// updateIntentEntryAssociation 以intent的维度，更新与entry的关联关系
func updateIntentEntryAssociation(ctx context.Context, tx *gorm.DB, intentID string, entryIDs []string) error {
	var err error
	log.InfoContextf(ctx, "updateIntentEntryAssociation|intentID:%s|entryIDs:%+v",
		intentID, entryIDs)

	// 1. 标记删除最新的tree中没用到的EntryID的关联关系
	if err = tx.Table(entity.IntentEntry{}.TableName()).
		Where("f_intent_id = ? ", intentID).Where("f_entry_id NOT IN (?)", entryIDs).
		Where("f_is_deleted = 0").Updates(map[string]interface{}{
		"f_is_deleted": 1, "f_action": entity.ActionDelete,
		"f_release_status": entity.ReleaseStatusUnPublished,
		"f_update_time":    time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "updateIntentEntryAssociation|1-Updates|err:%+v", err)
		return err
	}

	// 2. 查找现在库里有哪些
	var intentEntrys []entity.IntentEntry
	if err = tx.Table(entity.IntentEntry{}.TableName()).
		Where("f_intent_id = ? ", intentID).
		Where("f_entry_id IN (?)", entryIDs).Where("f_is_deleted = 0").
		Find(&intentEntrys).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.ErrorContextf(ctx, "updateIntentEntryAssociation|2-Find|err:%+v", err)
		return err
	}

	log.InfoContextf(ctx, "updateIntentEntryAssociation|intentEntrys:%+v", intentEntrys)
	existingEntryIDs := make(map[string]bool)
	for _, entry := range intentEntrys {
		existingEntryIDs[entry.EntryID] = true
	}

	needUpdateEntrys := make([]string, 0)
	newIntentEntrys := make([]entity.IntentEntry, 0)
	// 3. 库里有的更新，库里没有的insert
	for _, entryId := range entryIDs {
		log.InfoContextf(ctx, "updateIntentEntryAssociation|slotID:%s", entryId)
		if existingEntryIDs[entryId] {
			needUpdateEntrys = append(needUpdateEntrys, entryId)
		} else {
			// 如果找不到记录，则创建新的记录
			newIntentEntrys = append(newIntentEntrys, entity.IntentEntry{
				IntentID: intentID, EntryID: entryId,
				ReleaseStatus: entity.ReleaseStatusUnPublished,
				IsDeleted:     0, Action: entity.ActionInsert,
				CreateTime: time.Now(), UpdateTime: time.Now(),
			})
		}
	}

	log.InfoContextf(ctx, "updateIntentEntryAssociation|needUpdateEntrys:%+v", needUpdateEntrys)
	if len(needUpdateEntrys) > 0 {
		// 如果找到了记录，则更新
		if err = tx.Table(entity.IntentEntry{}.TableName()).
			Where("f_intent_id = ? ", intentID).Where("f_entry_id IN ?", needUpdateEntrys).
			Where("f_is_deleted = 0").Updates(map[string]interface{}{
			"f_action":         entity.ActionUpdate,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error; err != nil {
			log.ErrorContextf(ctx, "3-updates|needUpdate:%+v|err:%+v", needUpdateEntrys, err)
			return err
		}
	}

	log.InfoContextf(ctx, "updateIntentEntryAssociation|newIntentEntrys:%s", newIntentEntrys)
	if len(newIntentEntrys) > 0 {
		if err = tx.Table(entity.IntentEntry{}.TableName()).Create(&newIntentEntrys).Error; err != nil {
			log.ErrorContextf(ctx, "3-insert|newIntentEntrys:%+v|err:%+v", newIntentEntrys, err)
			return err
		}
	}
	return nil
}

// deleteIntentEntry 以intent的维度，更新与Entry的关联关系
func deleteIntentEntry(ctx context.Context, tx *gorm.DB, intentIDs []string) error {
	sid := util.RequestID(ctx)
	err := tx.Table(entity.IntentEntry{}.TableName()).
		Where("f_intent_id IN (?)", intentIDs).
		Where("f_is_deleted = 0").
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|deleteIntentEntry|Updates|err:%+v", sid, err)
		return err
	}
	return nil
}

// txUpdateIntentEntryByEntryIds 通过任务流信息更新t_intent_entry表
func txUpdateIntentEntryByEntryIds(ctx context.Context, tx *gorm.DB, action string, entryIds []string) error {
	sid := util.RequestID(ctx)

	err := tx.Model(&entity.IntentEntry{}).
		Where("f_entry_id IN (?) and f_is_deleted = 0 ", entryIds).
		Updates(map[string]interface{}{
			"f_action":         action,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.Errorf("sid:%s|txUpdateIntentEntryBySlotID Failed!|entryIds:%+v|%s", sid, entryIds, err)
		return err
	}
	return nil
}
