package db

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"gorm.io/gorm"
)

// - 产品述求：草稿态也保存slot和taskFlow的关联关系；
// 影响：
//  1. 导出草稿态的对话树时，是否会带上关联的slot；
//  2. 历史版本恢复和拷贝对话树，是否会带草稿态的对话树关联的slot， 是会带上
//  3. 导入时，是否会把草稿态中关联的slot导入进去
//  4. 删除slot时，被草稿态的对话树引用的是否能被删除；
//
// 如果 taskFlow.Action 不存在，则从原任务流程状态获取
// updateIntentSlotAssociation 以intent的维度，更新与slot的关联关系
func updateIntentSlotAssociation(ctx context.Context, tx *gorm.DB,
	intentID string, slotIDs []string) error {
	log.InfoContextf(ctx, "updateIntentSlotAssociation|intentID:%s|slotIDs:%s", intentID, slotIDs)
	// 1. 标记删除最新的tree中没用到的SlotID的关联关系
	err := deleteIntentSlotNotInSlot(ctx, tx, intentID, slotIDs)
	if err != nil {
		return err
	}
	// 2. 查找现在库里有哪些
	var intentSlots []entity.IntentSlot
	err = tx.Table(entity.IntentSlot{}.TableName()).
		Where("f_intent_id = ? ", intentID).
		Where("f_slot_id IN (?)", slotIDs).
		Where("f_is_deleted = 0").
		Find(&intentSlots).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.ErrorContextf(ctx, "updateIntentSlotAssociation|2-Find|err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "updateIntentSlotAssociation|intentSlots:%+v", intentSlots)
	existingSlotIDs := make(map[string]bool)
	for _, slot := range intentSlots {
		existingSlotIDs[slot.SlotID] = true
	}
	needUpdateSlots := make([]string, 0)
	newIntentSlots := make([]entity.IntentSlot, 0)
	// 3. 库里有的更新，库里没有的insert
	for _, slotID := range slotIDs {
		log.InfoContextf(ctx, "updateIntentSlotAssociation|slotID:%s", slotID)
		if existingSlotIDs[slotID] {
			needUpdateSlots = append(needUpdateSlots, slotID)
		} else {
			// 如果找不到记录，则创建新的记录
			newIntentSlots = append(newIntentSlots, entity.IntentSlot{
				IntentID:      intentID,
				SlotID:        slotID,
				ReleaseStatus: entity.ReleaseStatusUnPublished,
				IsDeleted:     0,
				Action:        entity.ActionInsert,
				CreateTime:    time.Now(),
				UpdateTime:    time.Now(),
			})
		}
	}
	log.InfoContextf(ctx, "updateIntentSlotAssociation|needUpdateSlots:%s", needUpdateSlots)
	err = updateIntentSlotInSlot(ctx, tx, needUpdateSlots, intentID)
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, "updateIntentSlotAssociation|newIntentSlots:%s", newIntentSlots)
	if len(newIntentSlots) > 0 {
		err = tx.Table(entity.IntentSlot{}.TableName()).Create(&newIntentSlots).Error
		if err != nil {
			log.ErrorContextf(ctx, "updateIntentSlotAssociation|3-insert|newIntentSlots:%+v|err:%+v",
				newIntentSlots, err)
			return err
		}
	}
	return nil
}

// deleteIntentSlotNotInSlot 以intent的维度，更新t_intent_slot中不在指定slots的操作状态
func deleteIntentSlotNotInSlot(ctx context.Context, tx *gorm.DB, intentID string, slotIDs []string) error {
	err := tx.Table(entity.IntentSlot{}.TableName()).
		Where("f_intent_id = ? ", intentID).
		Where("f_slot_id NOT IN (?)", slotIDs).
		Where("f_is_deleted = 0").
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "updateIntentSlotAssociation|1-Updates|err:%+v", err)
		return err
	}
	return nil
}

// updateIntentSlotInSlot 以intent的维度，更新t_intent_slot中指定slots的操作状态
func updateIntentSlotInSlot(ctx context.Context, tx *gorm.DB, needUpdateSlots []string, intentID string) error {
	if len(needUpdateSlots) > 0 {
		// 如果找到了记录，则更新
		err := tx.Table(entity.IntentSlot{}.TableName()).
			Where("f_intent_id = ? ", intentID).
			Where("f_slot_id IN ?", needUpdateSlots).
			Where("f_is_deleted = 0").
			Updates(map[string]interface{}{
				"f_action":         entity.ActionUpdate,
				"f_release_status": entity.ReleaseStatusUnPublished,
				"f_update_time":    time.Now(),
			}).Error
		if err != nil {
			log.ErrorContextf(ctx, "updateIntentSlotAssociation|3-updates|needUpdateSlots:%+v|err:%+v",
				needUpdateSlots, err)
			return err
		}
	}
	return nil
}

// deleteIntentSlot 以intent的维度，更新与slot的关联关系
//
// 现状：需要依赖 t_task_flow 这张表在这种case时候的逻辑；
//  1. 新建 tree，并引用一个slotID
//  2. t_intent_slot 表里维护了 新tree 和 slotID 的关联关系。 这个时候 action 是 insert
//  3. 删除 tree， 要在 t_intent_slot 里删除关联关系， 那这个时候的 action 应该是 insert 还是 delete => 答： delete
func deleteIntentSlot(ctx context.Context, tx *gorm.DB, intentIDs []string) error {
	err := tx.Table(entity.IntentSlot{}.TableName()).
		Where("f_intent_id IN (?)", intentIDs).
		Where("f_is_deleted = 0").
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "deleteIntentSlot|Updates|err:%+v", err)
		return err
	}
	return nil
}

//func getIntentWithSlotID(ctx context.Context, tx *gorm.DB, slotID string) ([]string, error) {
//	var intentSlots []entity.IntentSlot
//	err := tx.Table(entity.IntentSlot{}.TableName()).
//		Where("f_slot_id = ? ", slotID).
//		Where("f_is_deleted = 0").
//		Find(&intentSlots).Error
//	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
//		log.ErrorContextf(ctx, "getIntentWithSlotID|2-Find|err:%+v", err)
//		return []string{}, err
//	}
//	ids := make([]string, 0, len(intentSlots))
//	for _, intentSlot := range intentSlots {
//		ids = append(ids, intentSlot.IntentID)
//	}
//	return ids, nil
//}

// txUpdateIntentSlotBySlotID 通过任务流信息更新t_intent_slot表
func txUpdateIntentSlotBySlotID(ctx context.Context, tx *gorm.DB, action, slotID string) error {
	sid := util.RequestID(ctx)

	err := tx.Model(&entity.IntentSlot{}).
		Where("f_slot_id = ? and f_is_deleted = 0 ", slotID).
		Updates(map[string]interface{}{
			"f_action":         action,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.Errorf("txUpdateIntentSlotBySlotID Failed! |%s|%s", sid, err)
		return err
	}
	return nil
}
