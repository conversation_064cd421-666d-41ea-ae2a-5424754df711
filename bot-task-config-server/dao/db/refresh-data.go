// bot-task-config-server
//
// @(#)refresh-data.go  星期四, 八月 01, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"errors"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"time"
)

// --------------------- 示例用法刷数据 ---------------

// CorpusInfo 意图Corpus
type CorpusInfo struct {
	IntentID string `json:"f_intent_id"    gorm:"column:f_intent_id"`
	Content  string `json:"f_content"      gorm:"column:f_content"`
	CorpusID string `json:"f_corpus_id"    gorm:"column:f_corpus_id"`
}

// IntentCorpusDetail ...
type IntentCorpusDetail struct {
	RobotId        string                 `json:"robot_id"`         // 应用机器人Id
	IntentCorpusID string                 `json:"intent_corpus_id"` // 意图语料ID
	IntentID       string                 `json:"intent_id"`
	Content        string                 `json:"content"`
	Examples       *[]entity.IntentCorpus `json:"examples"`
}

// GetRobotIdListFromCorpus 机器人列表及意图IDs
func GetRobotIdListFromCorpus(ctx context.Context, tx *gorm.DB) ([]string, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var appIds, intentIds []string
	if err := tx.Model(&entity.IntentCorpus{}).
		Where("f_is_deleted = 0").
		Select("DISTINCT f_robot_id").
		Find(&appIds).Error; err != nil {
		return appIds, err
	}
	if err := db.Model(&entity.IntentCorpus{}).
		Where("f_is_deleted = 0").
		Select("DISTINCT f_intent_id").
		Find(&intentIds).Error; err != nil {
		return appIds, err
	}
	return appIds, nil
}

// GetIntentIdsInIntentCorpus 通过应用ID获取 意图IDs
func GetIntentIdsInIntentCorpus(ctx context.Context, appId string, tx *gorm.DB) ([]string, error) {
	var intentIDs []string
	if err := tx.Model(&entity.IntentCorpus{}).
		Where("f_is_deleted = 0 AND f_robot_id=?", appId).
		Select("DISTINCT f_intent_id").
		Find(&intentIDs).Error; err != nil {
		return intentIDs, err
	}
	return intentIDs, nil
}

// GetIntentCorpusDetails 获取每个意图下的示例问法
func GetIntentCorpusDetails(ctx context.Context, appId, intentId string,
	tx *gorm.DB) (*IntentCorpusDetail, error) {
	var intentCorpus *[]entity.IntentCorpus
	// 示例问法Id
	corpusDetail := &IntentCorpusDetail{}

	var corpusInfo *CorpusInfo
	if err := tx.Table(entity.Corpus{}.TableName()).
		Where("f_intent_id = ?", intentId).
		Select("f_corpus_id, f_intent_id, f_content").
		Scan(&corpusInfo).Error; err != nil {
		return nil, err
	}

	err := tx.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_robot_id=? AND f_intent_id=?", appId, intentId).
		Find(&intentCorpus).Error
	if err != nil {
		return nil, err
	}

	log.InfoContextf(ctx, "corpusInfo:%+v|intentCorpus:%+v", corpusInfo, intentCorpus)
	if corpusInfo != nil {
		corpusDetail.RobotId = appId
		corpusDetail.IntentID = corpusInfo.IntentID
		corpusDetail.IntentCorpusID = corpusInfo.CorpusID
		corpusDetail.Content = corpusInfo.Content
		corpusDetail.Examples = intentCorpus
		return corpusDetail, nil
	}

	return corpusDetail, nil
}

// ------------------- 保存 redis操作 --------------

// SaveRefreshInfosToRD 保存信息到redis
func SaveRefreshInfosToRD(ctx context.Context, key string, value string,
	r redis.UniversalClient, expired time.Duration) error {
	if err := r.Set(ctx, key, value, expired).Err(); err != nil {
		return err
	}
	return nil
}

// GetRefreshInfosInRD ...
func GetRefreshInfosInRD(ctx context.Context, key string, r redis.UniversalClient) (string, error) {
	value, err := r.Get(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return "", nil
	}
	if err != nil {
		return "", err
	}
	return value, nil
}

// DelRefreshInfosInRD redis 删除
func DelRefreshInfosInRD(ctx context.Context, key string, r redis.UniversalClient) error {
	if _, err := r.Del(ctx, key).Result(); err != nil {
		return err
	}
	return nil
}
