package db

import (
	"context"
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
)

// AssembleDmUpsertTaskFlowData 组装任务流更新数据给dm
func AssembleDmUpsertTaskFlowData(ctx context.Context, robotID string, dmTaskFlow *KEP.TaskFlow,
	intentID, version, intentDesc string) (*KEP_DM.UpsertRobotIntentToSandboxReply, error) {
	log.InfoContextf(ctx, "AssembleDmUpsertTaskFlowData req robotID:|%s|taskFlow:%v|"+
		"intentID:%s|version:%s|IntentDesc:%s",
		robotID, json0.Marshal2StringNoErr(dmTaskFlow), intentID, version, intentDesc)
	dmTaskFlowRequest := new(KEP_DM.UpsertRobotIntentToSandboxRequest)
	dmTaskFlowRequest.RobotID = robotID
	dmTaskFlowRequest.IntentName = dmTaskFlow.TaskFlowName
	dmTaskFlowRequest.IntentID = intentID
	dmTaskFlowRequest.IntentDesc = intentDesc
	dmTaskFlowRequest.TaskFlowVersion = version
	dmTaskFlowRequest.TaskFlow = dmTaskFlow
	log.InfoContextf(ctx, "AssembleDmUpsertTaskFlowData dmTaskFlowRequest|%v",
		json0.Marshal2StringNoErr(dmTaskFlowRequest))
	dmUpsertTaskFlowResp, err := rpc.UpdateTaskFlowToSandbox(ctx, dmTaskFlowRequest)
	if err != nil {
		log.ErrorContextf(ctx, "AssembleDmUpsertTaskFlowData dmTaskFlowRequest failed error|%v", err)
		return dmUpsertTaskFlowResp, err
	}
	return dmUpsertTaskFlowResp, nil
}

// AssembleDmDeleteTaskFlowData 组装任务流删除数据给dm
func AssembleDmDeleteTaskFlowData(ctx context.Context, robotID string, taskFlowIDs []string) (
	*KEP_DM.DeleteRobotIntentsInSandboxReply, error) {
	log.InfoContextf(ctx, "AssembleDmDeleteTaskFlowData req robotID:|%s|taskFlowIDs:%v", robotID, taskFlowIDs)
	dmDeleteTaskFlowRequest := new(KEP_DM.DeleteRobotIntentsInSandboxRequest)
	dmDeleteTaskFlowResp := new(KEP_DM.DeleteRobotIntentsInSandboxReply)
	dmDeleteTaskFlowRequest.RobotID = robotID
	taskFlowMap, err := GetTaskFlowByFlowIDs(ctx, robotID, taskFlowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowByFlowIDs|%v", err)
		return dmDeleteTaskFlowResp, err
	}
	intentIDs := make([]string, 0)
	if len(taskFlowMap) <= 0 {
		return dmDeleteTaskFlowResp, errors.New("task flow intent id is empty")
	}
	for _, taskFlow := range taskFlowMap {
		intentIDs = append(intentIDs, taskFlow.IntentID)
	}
	dmDeleteTaskFlowRequest.IntentIDs = intentIDs
	log.InfoContextf(ctx, "AssembleDmDeleteTaskFlowData DeleteRequest|%v", dmDeleteTaskFlowRequest)
	dmDeleteTaskFlowResp, err = rpc.DeleteTaskFlowToSandbox(ctx, dmDeleteTaskFlowRequest)
	if err != nil {
		return dmDeleteTaskFlowResp, err
	}
	return dmDeleteTaskFlowResp, nil
}

// AssembleDmReleaseIntentRobot 组装任务流发布正式数据给dm
func AssembleDmReleaseIntentRobot(ctx context.Context, robotID string, upsertTaskFlowIDs []string,
	deleteTaskFlowIDs []string) (
	*KEP_DM.ReleaseIntentRobotReply, error) {
	log.InfoContextf(ctx, "AssembleDmReleaseIntentRobot req robotID:|%s|upsertTaskFlowIDs:%v|"+
		"deleteTaskFlowIDs:%v", robotID, upsertTaskFlowIDs, deleteTaskFlowIDs)
	dmReleaseIntentRobotRequest := new(KEP_DM.ReleaseIntentRobotRequest)
	dmReleaseIntentRobotReply := new(KEP_DM.ReleaseIntentRobotReply)
	dmReleaseIntentRobotRequest.RobotID = robotID
	upsertTaskFlowMap, err := GetTaskFlowByFlowIDs(ctx, robotID, upsertTaskFlowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowByFlowIDs upsertTaskFlowMap|%v", err)
		return dmReleaseIntentRobotReply, err
	}
	upsertIntentIDs := make([]string, 0)
	for _, taskFlow := range upsertTaskFlowMap {
		upsertIntentIDs = append(upsertIntentIDs, taskFlow.IntentID)
	}
	dmReleaseIntentRobotRequest.UpsertIntentIDs = upsertIntentIDs
	deleteTaskFlowMap, err := GetTaskFlowByFlowIDs(ctx, robotID, deleteTaskFlowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowByFlowIDs deleteTaskFlowMap|%v", err)
		return dmReleaseIntentRobotReply, err
	}
	deleteIntentIDs := make([]string, 0)
	for _, taskFlow := range deleteTaskFlowMap {
		deleteIntentIDs = append(deleteIntentIDs, taskFlow.IntentID)
	}
	dmReleaseIntentRobotRequest.DeleteIntentIDs = deleteIntentIDs
	log.InfoContextf(ctx, "AssembleDmReleaseIntentRobot dmReleaseIntentRobotRequest|%v",
		dmReleaseIntentRobotRequest)
	dmReleaseIntentRobotReply, err = rpc.ReleaseIntentRobot(ctx, dmReleaseIntentRobotRequest)
	if err != nil {
		return dmReleaseIntentRobotReply, err
	}
	return dmReleaseIntentRobotReply, nil
}
