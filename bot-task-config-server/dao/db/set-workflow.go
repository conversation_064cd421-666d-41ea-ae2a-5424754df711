package db

import (
	"context"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// deleteWorkflowToDM 组装工作流删除数据给dm
func deleteWorkflowToDM(ctx context.Context, appBizId string, workflowIDs []string) (
	*KEP_WF_DM.DeleteWorkflowsInSandboxReply, error) {
	log.InfoContextf(ctx, "deleteWorkflowToDM req appBizId:|%s|workflowIDs:%v", appBizId, workflowIDs)
	request := new(KEP_WF_DM.DeleteWorkflowsInSandboxRequest)
	request.AppID = appBizId
	request.WorkflowIDs = workflowIDs
	log.InfoContextf(ctx, "deleteWorkflowToDM request|%v", request)
	dmDeleteWorkflowResp, err := rpc.DeleteWorkflowToSandbox(ctx, request)
	if err != nil {
		return dmDeleteWorkflowResp, err
	}
	return dmDeleteWorkflowResp, nil
}

// sendWorkflowToDM  组装工作流更新数据给dm
func sendWorkflowToDM(ctx context.Context, appBizId string, workflow *KEP_WF.Workflow) (
	*KEP_WF_DM.UpsertWorkflowToSandboxReply, error) {
	log.InfoContextf(ctx, "sendWorkflowToDM|appBizId:%s|workflow:%v", appBizId, workflow)
	request := new(KEP_WF_DM.UpsertWorkflowToSandboxRequest)
	request.AppID = appBizId
	request.Workflow = workflow
	log.InfoContextf(ctx, "sendWorkflowToDM request|%v", request)
	dmDeleteWorkflowResp, err := rpc.UpsertWorkflowToSandbox(ctx, request)
	if err != nil {
		return dmDeleteWorkflowResp, err
	}
	return dmDeleteWorkflowResp, nil
}
