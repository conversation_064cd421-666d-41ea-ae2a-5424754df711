// Package db 槽位相关DB操作
// @Author: halelv
// @Date: 2024/2/28 16:03
package db

import (
	"context"
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

// GetExportSlotInfos 获取导出时全部槽位实体信息
func GetExportSlotInfos(ctx context.Context, robotID string, intentIDs []string) (
	map[string][]*entity.SlotMigrationInfo, error) {
	log.InfoContextf(ctx, "GetExportSlotInfos, robotID:%s, len(intentIDs):%d", robotID, len(intentIDs))

	// 查询槽位
	slotIntentInfo, err := getSlotWithIntentIDs(ctx, robotID, intentIDs)
	if err != nil {
		return nil, err
	}

	slotIDs := make([]string, 0)
	intentIDSlotMap := make(map[string][]*entity.SlotIntentInfo)
	for _, slotIntent := range slotIntentInfo {
		slotIDs = append(slotIDs, slotIntent.SlotID)
		intentIDSlotMap[slotIntent.IntentID] = append(intentIDSlotMap[slotIntent.IntentID], slotIntent)
	}

	// 查询实体｜词条
	slotIDEntityMigrationMap, err := getEntityMigrationWithSlotIDs(ctx, robotID, slotIDs)
	if err != nil {
		return nil, err
	}

	// 组装转换导入/导出所需的槽位实体信息
	intentSlotMigrationInfosMap := make(map[string][]*entity.SlotMigrationInfo)
	for _, intentID := range intentIDs {
		slotInfos, ok := intentIDSlotMap[intentID]
		if !ok || len(slotInfos) == 0 {
			continue
		}

		slotMigrationInfos := make([]*entity.SlotMigrationInfo, 0)
		for _, slotInfo := range slotInfos {
			entityMigrationInfos := slotIDEntityMigrationMap[slotInfo.SlotID]
			slotMigrationInfo, err := slotInfo.ConvertToSlotMigrationInfo(entityMigrationInfos)
			if err != nil {
				log.ErrorContextf(ctx, "GetExportSlotInfos slotInfo.ConvertToSlotMigrationInfo Failed, "+
					"err:%v", err)
				return nil, err
			}

			slotMigrationInfos = append(slotMigrationInfos, slotMigrationInfo)
		}

		intentSlotMigrationInfosMap[intentID] = slotMigrationInfos
	}

	log.InfoContextf(ctx, "GetExportSlotInfos, len(intentSlotMigrationInfosMap):%d",
		len(intentSlotMigrationInfosMap))
	return intentSlotMigrationInfosMap, nil
}

// GetImportSlotInfos 获取导入时存在的槽位实体信息
func GetImportSlotInfos(ctx context.Context, robotID string, slotNames []string) (
	map[string]*entity.SlotMigrationInfo, error) {
	log.InfoContextf(ctx, "GetImportSlotInfos, robotID:%s, len(slotNames):%s", robotID, len(slotNames))

	// 查询槽位
	slotIntentInfo, err := getSlotWithNames(ctx, robotID, slotNames)
	if err != nil {
		return nil, err
	}

	slotIDs := make([]string, 0)
	nameSlotMap := make(map[string]*entity.SlotIntentInfo)
	for _, slotIntent := range slotIntentInfo {
		slotIDs = append(slotIDs, slotIntent.SlotID)
		nameSlotMap[slotIntent.SlotName] = slotIntent
	}

	// 查询实体｜词条
	slotIDEntityMigrationMap, err := getEntityMigrationWithSlotIDs(ctx, robotID, slotIDs)
	if err != nil {
		return nil, err
	}

	// 组装转换导入/导出所需的槽位实体信息
	nameSlotMigrationInfoMap := make(map[string]*entity.SlotMigrationInfo)
	for _, slotName := range slotNames {
		slotInfo, ok := nameSlotMap[slotName]
		if !ok || slotInfo == nil {
			continue
		}

		entityMigrationInfos := slotIDEntityMigrationMap[slotInfo.SlotID]
		slotMigrationInfo, err := slotInfo.ConvertToSlotMigrationInfo(entityMigrationInfos)
		if err != nil {
			log.ErrorContextf(ctx, "GetImportSlotInfos slotInfo.ConvertToSlotMigrationInfo Failed, "+
				"err:%v", err)
			return nil, err
		}

		nameSlotMigrationInfoMap[slotName] = slotMigrationInfo
	}

	log.InfoContextf(ctx, "GetImportSlotInfos, len(nameSlotMigrationInfoMap):%d", len(nameSlotMigrationInfoMap))
	return nameSlotMigrationInfoMap, nil
}

// createImportSlotInfos 创建导入时需要的槽位实体信息
func createImportSlotInfos(ctx context.Context, tx *gorm.DB, robotID, uin, subUin string,
	slotInfos []*entity.SlotMigrationInfo) error {
	log.InfoContextf(ctx, "createImportSlotInfos, robotID:%s, uin:%s, subUin:%s, len(slotInfos):%d",
		robotID, uin, subUin, len(slotInfos))

	// 获取需要新建全部槽位｜实体｜词条｜槽位-实体关联
	slots := make([]entity.Slot, 0)
	entities := make([]entity.Entity, 0)
	entries := make([]entity.Entry, 0)
	slotEntities := make([]entity.SlotEntity, 0)

	for _, slotInfo := range slotInfos {
		slotDB, entityDBs, entryDBs, slotEntityDBs := slotInfo.ConvertToDBCreateInfo(robotID, uin, subUin)

		slots = append(slots, slotDB)
		entities = append(entities, entityDBs...)
		entries = append(entries, entryDBs...)
		slotEntities = append(slotEntities, slotEntityDBs...)
	}

	// DB事物创建全部槽位｜实体｜词条｜槽位-实体关联
	err := txBatchCreateSlotEntityTable(ctx, tx, robotID, slots, entities, entries, slotEntities)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "createImportSlotInfos|success created|len(slots):%d|len(entities):%d|"+
		"len(entries):%d|len(slotEntities):%d", len(slots), len(entities), len(entries), len(slotEntities))
	return nil
}

// getEntityMigrationWithSlotIDs 用槽位ID查询槽位关联的实体词条信息
func getEntityMigrationWithSlotIDs(ctx context.Context, robotID string, slotIDs []string) (
	map[string][]entity.EntityMigrationInfo, error) {
	// 查询实体
	slotEntityInfo, err := getEntityWithSlotIDs(ctx, robotID, slotIDs)
	if err != nil {
		return nil, err
	}

	entityIDs := make([]string, 0)
	slotIDEntityMap := make(map[string][]*entity.SlotEntityInfo)
	for _, slotEntity := range slotEntityInfo {
		// 非系统实体才需要查询词条
		if slotEntity.LevelType != entity.SlotLevelSYS {
			entityIDs = append(entityIDs, slotEntity.EntityID)
		}
		slotIDEntityMap[slotEntity.SlotID] = append(slotIDEntityMap[slotEntity.SlotID], slotEntity)
	}

	// 查询词条
	entries, err := getEntryWithEntityIDs(ctx, entityIDs)
	if err != nil {
		return nil, err
	}

	entityIDEntryMap := make(map[string][]*entity.Entry)
	for _, entry := range entries {
		entityIDEntryMap[entry.EntityID] = append(entityIDEntryMap[entry.EntityID], entry)
	}

	// 组装实体词条信息
	slotIDEntityMigrationMap := make(map[string][]entity.EntityMigrationInfo)
	for _, slotID := range slotIDs {
		entityInfos, ok := slotIDEntityMap[slotID]
		if !ok || len(entityInfos) == 0 {
			// 槽位关联的实体不存在
			log.ErrorContextf(ctx, "getEntityMigrationWithSlotIDs ilegal slot, slotID:%s has no entity", slotID)
			continue
		}

		entityMigrationInfos := make([]entity.EntityMigrationInfo, 0)
		for _, entityInfo := range entityInfos {
			entryMigrationInfos := make([]entity.EntryMigrationInfo, 0)
			entryInfos, ok := entityIDEntryMap[entityInfo.EntityID]
			if ok {
				for _, entryInfo := range entryInfos {
					entryMigrationInfos = append(entryMigrationInfos, entryInfo.ConvertToEntryMigrationInfo())
				}
			}

			entityMigrationInfo, err := entityInfo.ConvertToEntityMigrationInfo(entryMigrationInfos)
			if err != nil {
				log.ErrorContextf(ctx, "getEntityMigrationWithSlotIDs entityInfo.ConvertToEntityMigrationInfo "+
					"Failed, err:%v", err)
				return nil, err
			}

			entityMigrationInfos = append(entityMigrationInfos, entityMigrationInfo)
		}

		slotIDEntityMigrationMap[slotID] = entityMigrationInfos
	}
	return slotIDEntityMigrationMap, nil
}

// getSlotWithIntentIDs 用意图ID查询意图关联的槽位信息
func getSlotWithIntentIDs(ctx context.Context, robotID string, intentIDs []string) ([]*entity.SlotIntentInfo, error) {
	log.InfoContextf(ctx, "getSlotWithIntentIDs, robotID:%s, len(intentIDs):%d", robotID, len(intentIDs))
	if len(robotID) == 0 || len(intentIDs) == 0 {
		return nil, nil
	}

	var slotEntityInfo []*entity.SlotIntentInfo
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Slot{}.TableName()).
		Select("t_intent_slot.f_intent_id, "+
			"t_slot.f_slot_id, t_slot.f_slot_name, t_slot.f_slot_desc, t_slot.f_slot_examples").
		Joins("INNER JOIN t_intent_slot on t_slot.f_slot_id = t_intent_slot.f_slot_id").
		Where("t_slot.f_is_deleted = 0").
		Where("t_slot.f_robot_id = ?", robotID).
		Where("t_intent_slot.f_is_deleted = 0").
		Where("t_intent_slot.f_intent_id IN ?", intentIDs).
		Find(&slotEntityInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "getSlotWithIntentIDs db.Find Failed, err:%v", err)
		return nil, err
	}
	return slotEntityInfo, nil
}

// getSlotWithNames 用槽位名称查询所有存在的槽位信息
func getSlotWithNames(ctx context.Context, robotID string, slotNames []string) ([]*entity.SlotIntentInfo, error) {
	log.InfoContextf(ctx, "getSlotWithNames, robotID:%s, len(slotNames):%d", robotID, len(slotNames))
	if len(robotID) == 0 || len(slotNames) == 0 {
		return nil, nil
	}

	var slotEntityInfo []*entity.SlotIntentInfo
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Slot{}.TableName()).
		Select("f_slot_id, f_slot_name, f_slot_desc, f_slot_examples").
		Where("f_is_deleted = 0").
		Where("f_robot_id = ?", robotID).
		Where("f_slot_name IN ?", slotNames).
		Find(&slotEntityInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "getSlotWithNames db.Find Failed, err:%v", err)
		return nil, err
	}
	return slotEntityInfo, nil
}

// getEntityWithSlotIDs 用槽位ID查询槽位关联的实体信息
func getEntityWithSlotIDs(ctx context.Context, robotID string, slotIDs []string) ([]*entity.SlotEntityInfo, error) {
	log.InfoContextf(ctx, "getEntityWithSlotIDs, robotID:%s, len(slotIDs):%d", robotID, len(slotIDs))
	if len(robotID) == 0 || len(slotIDs) == 0 {
		return nil, nil
	}

	var slotEntityInfo []*entity.SlotEntityInfo
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Entity{}.TableName()).
		Select("t_slot_entity.f_slot_id, "+
			"t_entity.f_entity_id, t_entity.f_entity_name, t_entity.f_entity_desc, "+
			"t_entity.f_entity_examples, t_entity.f_level_type").
		Joins("INNER JOIN t_slot_entity on t_entity.f_entity_id = t_slot_entity.f_entity_id").
		Where("t_entity.f_is_deleted = 0").
		Where("t_entity.f_robot_id = ?", robotID).
		Where("t_slot_entity.f_is_deleted = 0").
		Where("t_slot_entity.f_slot_id IN ?", slotIDs).
		Find(&slotEntityInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "getEntityWithSlotIDs db.Find, err:%v", err)
		return nil, err
	}
	return slotEntityInfo, nil
}

// getEntryWithEntityIDs 用实体ID查询实体关联的词条信息
func getEntryWithEntityIDs(ctx context.Context, entityIDs []string) ([]*entity.Entry, error) {
	log.InfoContextf(ctx, "getEntryWithEntityIDs, len(entityIDs):%d", len(entityIDs))
	if len(entityIDs) == 0 {
		return nil, nil
	}

	var entries []*entity.Entry
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Entry{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_entity_id IN ?", entityIDs).
		Find(&entries).Error
	if err != nil {
		log.ErrorContextf(ctx, "getEntryWithEntityIDs db.Find Failed, err:%v", err)
		return nil, err
	}
	return entries, nil
}

// GetSlotByName 通过机器人ID，slotName查看是否有槽位信息
func GetSlotByName(ctx context.Context, robotID string, slotName string) ([]*entity.Slot, error) {
	sid := util.RequestID(ctx)

	var slotInfo []*entity.Slot
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Slot{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_slot_name = ?", robotID, slotName).Find(&slotInfo).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return slotInfo, nil
		}
		log.Errorf("GetSlotByName Failed! |%s|%v", sid, err)
		return nil, err
	}
	return slotInfo, nil
}

// TXCreateSlotEntityTable 以事务的形式创建实体表和槽位表以及实体槽位关联表
func TXCreateSlotEntityTable(ctx context.Context, slotInfo entity.Slot, entityInfo entity.Entity,
	slotEntityInfo entity.SlotEntity) (err error) {
	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	err = txBatchCreateSlotEntityTable(ctx, tx, slotInfo.RobotID, []entity.Slot{slotInfo}, []entity.Entity{entityInfo},
		[]entity.Entry{}, []entity.SlotEntity{slotEntityInfo})
	return err
}

// txBatchCreateSlotEntityTable 以事务的形式批量创建实体表｜槽位表｜词条表以及实体槽位关联表
func txBatchCreateSlotEntityTable(ctx context.Context, tx *gorm.DB, robotID string, slotInfos []entity.Slot,
	entityInfos []entity.Entity, entryInfos []entity.Entry, slotEntityInfos []entity.SlotEntity) (err error) {
	log.InfoContextf(ctx, "txBatchCreateSlotEntityTable, robotID:%s, len(slotInfos):%d, len(entityInfos):%d, "+
		"len(entryInfos):%d, len(slotEntityInfos):%d", robotID, len(slotInfos), len(entityInfos),
		len(entryInfos), len(slotEntityInfos))
	// DB插入
	if len(slotInfos) > 0 {
		err = tx.Model(&entity.Slot{}).CreateInBatches(slotInfos, config.GetMainConfig().TaskFlow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable CreateSlot Failed, err:%v", err)
			return err
		}
	}
	entityIDMap := make(map[string]int, 0)
	if len(entityInfos) > 0 {
		err = tx.Model(&entity.Entity{}).CreateInBatches(entityInfos, config.GetMainConfig().TaskFlow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable CreateEntity Failed, err:%v", err)
			return err
		}
		for _, entity := range entityInfos {
			entityIDMap[entity.EntityID] = 1
		}
	}
	// 创建词槽的时候创建词条的向量库，并通知DM
	vectorDB := vdao.NewDao()
	sandboxGroupID, _, err := vectorDB.GetEntryVectorGroupID(ctx, robotID)
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryVectorGroupID Failed! err:%+v", err)
		return err
	}
	if len(entryInfos) > 0 {
		err = tx.Model(&entity.Entry{}).CreateInBatches(entryInfos, config.GetMainConfig().TaskFlow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable CreateEntry Failed, err:%v", err)
			return err
		}
		// 根据entryAction 来创建、修改、删除 词条向量表
		err = TxUpsertDeletedEntryVectorGroupTable(ctx, robotID, entryInfos, nil, nil)
		if err != nil {
			return err
		}
	}
	// 词条部分同步到redis
	err = entryListSetRedis(ctx, tx, robotID, entityIDMap)
	if err != nil {
		return err
	}
	if len(slotEntityInfos) > 0 {
		err = tx.Model(&entity.SlotEntity{}).
			CreateInBatches(slotEntityInfos, config.GetMainConfig().TaskFlow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable CreateSlotEntity Failed, err:%v", err)
			return err
		}
	}
	log.InfoContextf(ctx, "EntryVectorGroupID groupID:%s", sandboxGroupID)
	// 通知DM
	err = UpsertSlotsDM(ctx, slotEntityInfos, slotInfos, robotID)
	if err != nil {
		return err
	}
	return nil
}

// entryListSetRedis 词条批量同步到redis
func entryListSetRedis(ctx context.Context, tx *gorm.DB, robotID string, entityIDMap map[string]int) error {
	log.InfoContextf(ctx, "Sync2Redis,entityIDMap:%+v", entityIDMap)
	var redisVal []interface{}
	for entityID := range entityIDMap {
		redisEntryInfo, err := GetRedisEntryInfo(ctx, tx, robotID, entityID)
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable,GetRedisEntryInfo Failed!err:%+v", err)
			return err
		}
		redisVal = append(redisVal, entityID, redisEntryInfo)
	}
	log.InfoContextf(ctx, "Sync2Redis,redisVal:%+v", redisVal)
	if len(redisVal) > 0 {
		key := GetEntityEntriesKey(ctx, SandboxEnv, robotID)
		err := database.GetRedis().HMSet(ctx, key, redisVal).Err()
		if err != nil {
			log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable,SyncToRedis Failed!,key:%s,val:%s,err:%+v",
				key, redisVal, err)
			return err
		}
		log.InfoContextf(ctx, "txBatchCreateSlotEntityTable,Sync2Redis succ, key:%s,val:%s", key, redisVal)
	}
	return nil
}

// UpsertSlotsDM 构建UpsertSlotsToSandbox请求参数通知DM
func UpsertSlotsDM(ctx context.Context, slotEntityInfos []entity.SlotEntity, slotInfos []entity.Slot,
	robotID string) error {
	slotEntityIDsMap := make(map[string][]string)
	for _, slotEntity := range slotEntityInfos {
		slotEntityIDsMap[slotEntity.SlotID] = append(slotEntityIDsMap[slotEntity.SlotID], slotEntity.EntityID)
	}

	slots := make([]*KEP_DM.SlotMain, 0)
	for _, slotInfo := range slotInfos {
		// 槽位示例
		var slotExamples []string
		err := jsoniter.Unmarshal([]byte(slotInfo.Examples), &slotExamples)
		if err != nil {
			log.ErrorContextf(ctx, "Unmarshal Failed! data:%+v,err:%v", slotInfo.Examples, err)
			return err
		}
		slots = append(slots, &KEP_DM.SlotMain{
			SlotID:      slotInfo.SlotID,
			SlotName:    slotInfo.SlotName,
			Description: slotInfo.SlotDesc,
			Examples:    slotExamples,
			EntityIDs:   slotEntityIDsMap[slotInfo.SlotID],
		})
	}

	req := &KEP_DM.UpsertSlotsToSandboxRequest{
		RobotID: robotID,
		Slots:   slots,
	}
	_, err := rpc.UpsertSlotsToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable UpsertSlotsToSandbox Failed, err:%v", err)
		return err
	}
	return nil
}

// GetSlotEntityRelationBySlotID 通过slotID找到对应的entity信息
func GetSlotEntityRelationBySlotID(ctx context.Context, slotID string) (entity.SlotEntity, error) {
	sid := util.RequestID(ctx)

	var slotEntityInfo entity.SlotEntity
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Model(&entity.SlotEntity{}).
		Where("f_is_deleted = 0 and f_slot_id = ? ", slotID).First(&slotEntityInfo).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return slotEntityInfo, nil
		}

		log.Errorf("GetSlotEntityRelationBySlotID Failed! |%s|%+v", sid, err)
		return slotEntityInfo, err
	}
	return slotEntityInfo, nil
}

// BatchGetSlotEntityBySlotIds ...
func BatchGetSlotEntityBySlotIds(ctx context.Context, slotIDs []string) ([]*entity.SlotEntity, error) {
	sid := util.RequestID(ctx)

	var slotEntityInfos []*entity.SlotEntity
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(entity.SlotEntity{}.TableName()).
		Where("f_is_deleted = 0 and f_slot_id in ? ", slotIDs).Find(&slotEntityInfos).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return slotEntityInfos, nil
		}
		log.Errorf("BatchGetSlotEntityBySlotIds Failed! |%s|%v", sid, err)
		return nil, err
	}
	return slotEntityInfos, nil
}

// TXUpdateSlotEntityInfo ...
func TXUpdateSlotEntityInfo(ctx context.Context, robotID, slotID, entityID, slotName, slotDesc, exampleStr string,
	slotExamples []string, flowInfos []TaskFlowInfo) (err error) {
	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()
	action := entity.ActionUpdate
	log.InfoContextf(ctx, "TXUpdateSlotEntityInfo action:%s,flowInfos:%+v", action, flowInfos)
	err = tx.Model(&entity.Slot{}).
		Where("f_robot_id = ? and f_slot_id = ?", robotID, slotID).
		Updates(map[string]interface{}{
			"f_slot_name":      slotName,
			"f_slot_desc":      slotDesc,
			"f_slot_examples":  exampleStr,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         action,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpdateSlotEntityTable.UpdateSlot Failed! err|%+v", err)
		return err
	}
	err = tx.Model(&entity.Entity{}).
		Where("f_robot_id = ? and f_entity_id = ?", robotID, entityID).
		Updates(map[string]interface{}{
			"f_entity_name":     slotName,
			"f_entity_desc":     slotDesc,
			"f_entity_examples": exampleStr,
			"f_release_status":  entity.ReleaseStatusUnPublished,
			"f_action":          action,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpdateSlotEntityTable.UpdateEntity Failed! err|%+v", err)
		return err
	}
	err = tx.Model(&entity.SlotEntity{}).
		Where("f_slot_id = ? and f_entity_id = ?", slotID, entityID).
		Updates(map[string]interface{}{
			"f_action":         action,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpdateSlotEntityTable.UpdateSlotEntity Failed! err|%+v", err)
		return err
	}
	// 更新t_intent_slot表
	err = txUpdateIntentSlotBySlotID(ctx, tx, action, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry txUpdateIntentSlotBySlotID Failed! err|%+v", err)
		return err
	}
	// 更新t_task_flow表
	err = txUpdateTaskFlowByFlowInfo(ctx, tx, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "TXUpsertDeleteEntry txUpdateTaskFlowByFlowInfo Failed! err|%+v", err)
		return err
	}
	// 通知dm
	dmUpsertSlotInfos := &KEP_DM.UpsertSlotsToSandboxRequest{
		RobotID: robotID,
		Slots: []*KEP_DM.SlotMain{{
			SlotID:      slotID,
			SlotName:    slotName,
			Description: slotDesc,
			Examples:    slotExamples,
			EntityIDs:   []string{entityID},
		}},
	}
	_, err = rpc.UpsertSlotsToSandbox(ctx, dmUpsertSlotInfos)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlot.UpsertSlotsToSandbox Failed! err|%+v", err)
		return err
	}
	return
}

// CalcActionByFlowReleaseStatus ...
func CalcActionByFlowReleaseStatus(flowInfos []TaskFlowInfo) string {
	for _, flowInfo := range flowInfos {
		if flowInfo.TaskFlowStatus == entity.FlowStatePublishChange ||
			((flowInfo.TaskFlowStatus == entity.FlowStateEnable ||
				flowInfo.TaskFlowStatus == entity.FlowStateChangeUnPublish) &&
				flowInfo.TaskReleaseStatus == entity.ReleaseStatusPublished) {
			return entity.ActionUpdate
		}
	}
	return entity.ActionInsert
}

// GetSlotFromSlotTable ...
func GetSlotFromSlotTable(ctx context.Context, robotID, entityScope, keyword string, slotIds []string,
	reqOffset, reqLimit int, filterNoEntry bool) (int64, []entity.Slot, error) {
	sid := util.RequestID(ctx)
	var err error

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Model(&entity.Slot{}).
		Joins("inner join t_slot_entity on t_slot_entity.f_slot_id = t_slot.f_slot_id").
		Joins("inner join t_entity on t_entity.f_entity_id = t_slot_entity.f_entity_id").
		Where("t_entity.f_level_type = ? and t_entity.f_is_deleted = 0", entityScope)
	if len(robotID) > 0 {
		db = db.Where("t_slot.f_robot_id = ?", robotID)
	}

	// 过滤掉没有的词条的实体
	if filterNoEntry {
		db = db.Joins("inner join t_entry on t_entity.f_entity_id=t_entry.f_entity_id").Distinct()
	}
	if len(slotIds) > 0 {
		db = db.Where("t_slot.f_slot_id  in ?", slotIds)
	}

	// total不受到下面条件的约束
	var total int64
	db = db.Count(&total)
	if len(keyword) > 0 {
		db = db.Where("t_slot.f_slot_name LIKE ?", "%"+keyword+"%")
	}

	var slotInfos []entity.Slot
	// slotIds不为空的时候忽略page,offset
	if len(slotIds) > 0 {
		err = db.Order("f_id ASC").Find(&slotInfos).Error
	} else {
		offset, limit := getDefaultPOffsetLimit(reqOffset, reqLimit)
		err = db.Offset(offset).Limit(limit).Order("f_id ASC").Find(&slotInfos).Error
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotFromSlotTable Failed! sid:%s,err:%s", sid, err.Error())
		return 0, slotInfos, err
	}

	log.Infof("sid:%s,total:%d", sid, total)
	return total, slotInfos, nil

}

func getDefaultPOffsetLimit(offset, limit int) (int, int) {
	if limit == 0 {
		limit = 15
	}
	return offset, limit
}

// TaskFlowInfo ...
type TaskFlowInfo struct {
	TaskFlowID        string `json:"task_flow_id"        gorm:"column:task_flow_id"`
	TaskFlowName      string `json:"task_flow_name"      gorm:"column:task_flow_name"`
	TaskIntentID      string `json:"task_intent_id"      gorm:"column:task_intent_id"`
	TaskFlowStatus    string `json:"task_flow_status"    gorm:"column:task_flow_status"`
	TaskReleaseStatus string `json:"task_release_status" gorm:"column:task_release_status"`
	TaskFlowDeleted   int    `json:"task_flow_deleted" gorm:"column:task_flow_deleted"`
	IntentSlotDeleted int    `json:"intent_slot_deleted" gorm:"column:intent_slot_deleted"`
}

// GetTaskFlowInfoBySlotID 获取slotID关联的任务流程信息
func GetTaskFlowInfoBySlotID(ctx context.Context, robotID, slotID string) ([]TaskFlowInfo, error) {
	sid := util.RequestID(ctx)

	var taskFlowInfo []TaskFlowInfo
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	err := db.Table("t_intent_slot").
		Select("taskFlow.f_flow_id as task_flow_id, "+
			"taskFlow.f_intent_name as task_flow_name, "+
			"taskFlow.f_intent_id as task_intent_id, "+
			"taskFlow.f_flow_state as task_flow_status, "+
			"taskFlow.f_release_status as task_release_status, "+
			"taskFlow.f_is_deleted as task_flow_deleted,"+
			"t_intent_slot.f_is_deleted as intent_slot_deleted").
		Joins("inner join t_task_flow as taskFlow on t_intent_slot.f_intent_id = taskFlow.f_intent_id").
		Joins("inner join t_slot as slot on slot.f_slot_id = t_intent_slot.f_slot_id").
		Where("slot.f_robot_id = ? and slot.f_slot_id = ?", robotID, slotID).
		Where("slot.f_is_deleted = 0 ").
		Find(&taskFlowInfo).Error

	if err != nil {
		log.Errorf("GetTaskFlowInfoBySlotID Failed! sid:%s,err:%v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetTaskFlowInfoBySlotID, sid:%s,taskFlowInfo:%+v", sid, taskFlowInfo)
	return taskFlowInfo, nil
}

// TXDeleteSlotEntityInfo 获取slotID关联的任务流程信息
func TXDeleteSlotEntityInfo(ctx context.Context, robotID, slotID, entityID string) (err error) {
	sid := util.RequestID(ctx)

	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	err = deleteSlotAndEntity(ctx, robotID, slotID, entityID, tx)
	if err != nil {
		return err
	}

	// 删除词条的同时也需要删除词条的向量库
	entryInfos, err := GetNoDeletedEntryWithEntityID(ctx, entityID)
	if err != nil {
		log.Error("GetEntryByDeletedStatus Failed! sid:%s,err:%v", sid, err)
		return err
	}
	err = TxUpsertDeletedEntryVectorGroupTable(ctx, robotID, nil, nil, entryInfos)
	if err != nil {
		log.Error("TxUpsertDeletedEntryVectorGroupTable Failed! sid:%s,err:%v", sid, err)
		return err
	}

	// 删除槽位的时候需要把同步到redis里面的词条信息清空
	key := GetEntityEntriesKey(ctx, SandboxEnv, robotID)
	err = database.GetRedis().HDel(ctx, key, entityID).Err()
	if err != nil {
		log.ErrorContextf(ctx, "TXDeleteSlotEntityInfo HDel Failed!,key:%s,filedKey:%s ,err:%+v", key, entityID, err)
		return err
	}
	log.InfoContextf(ctx, "TXDeleteSlotEntityInfo HDel Entity succ, key:%s,val:%s", key, entityID)

	// 通知dm
	dmDelSlotInfos := &KEP_DM.DeleteSlotsInSandboxRequest{
		RobotID: robotID,
		SlotIDs: []string{slotID},
	}
	_, err = rpc.DeleteSlotsToSandbox(ctx, dmDelSlotInfos)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteSlot.TXDeleteSlotEntityInfo Failed! |%s|%s", sid, err.Error())
		return err
	}

	return nil
}

// deleteSlotAndEntity 删除槽位，实体，词条相关
func deleteSlotAndEntity(ctx context.Context, robotID string, slotID string, entityID string, tx *gorm.DB) (err error) {
	err = tx.Model(&entity.Slot{}).
		Where("f_robot_id = ? and f_slot_id = ?", robotID, slotID).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXDeleteSlotEntityInfo.DelSlot Failed! err|%+v", err)
		return err
	}

	err = tx.Model(&entity.Entity{}).
		Where("f_robot_id = ? and f_entity_id = ?", robotID, entityID).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpdateSlotEntityTable.DelEntity Failed! err|%+v", err)
		return err
	}

	err = tx.Model(&entity.SlotEntity{}).
		Where("f_slot_id = ? and f_entity_id = ?", slotID, entityID).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TXUpdateSlotEntityTable.DelSlotEntity Failed! err|%+v", err)
		return err
	}

	err = tx.Model(&entity.Entry{}).
		Where("f_entity_id = ? and f_is_deleted = 0", entityID).
		Updates(map[string]interface{}{
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         entity.ActionDelete,
			"f_is_deleted":     entity.DeleteBySlot,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "DeleteEntry Failed! err:%v", err)
		return err
	}
	return nil
}

// GetSlotsWithIDs 用SlotID查找
func GetSlotsWithIDs(ctx context.Context, robotID string, slotIDs []string) ([]*entity.Slot, error) {
	tx := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Slot{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_slot_id IN ?", slotIDs).
		// 在对话树中使用的可能是该机器人下的或者是系统级的
		Where("f_robot_id = ?", robotID)

	var slotInfo []*entity.Slot
	err := tx.Find(&slotInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotsWithIDs|robotID:%s|slotIDs:%d|err:%+v", robotID, slotIDs, err)
		return slotInfo, err
	}
	return slotInfo, nil

}

func getTaskFlowStatus(releaseState, flowState string) (flowState1 string) {
	if flowState == entity.FlowStateDraft {
		return entity.FlowStateDraft
	}

	if flowState == entity.FlowStateEnable && releaseState != entity.ReleaseStatusPublished {
		return entity.FlowStateChangeUnPublish // 已修改待发布
	}
	if releaseState == entity.ReleaseStatusPublished {
		return entity.FlowStatePublishChange // 已发布仍有修改
	}
	return flowState
}
