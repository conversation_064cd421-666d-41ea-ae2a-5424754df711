package db

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	utilsErrors "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idget"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/go-comm/set"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

const (
	tableTaskFlow       = "t_task_flow"
	tableRobotIntent    = "t_robot_intent"
	tableTaskFlowImport = "t_task_flow_import"
)

// GetPublishTaskFlowDetail 获取发布历史详情
func GetPublishTaskFlowDetail(ctx context.Context, botBizId, flowId, flowType,
	version, saveType string) (*entity.TaskFlowPublishHistory, error) {
	sid := util.RequestID(ctx)
	var tfph entity.TaskFlowPublishHistory
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	log.InfoContextf(ctx, "sid:%s|GetPublishTaskFlowDetail:flowId%s|flowType:%s",
		sid, flowId, flowType)
	// 草稿
	if saveType == entity.SaveDraft {
		taskflow, err := GetTaskFlowDetail(ctx, flowId, botBizId)
		version := config.GetMainConfig().VerifyTaskFlow.Version + "-" +
			fmt.Sprintf("%d", taskflow.ProtoVersion) + "-" +
			strconv.Itoa(int(taskflow.UpdateTime.Unix()))
		draft := entity.TaskFlowToHistoryFlowTask(taskflow, version,
			entity.EnumSaveType[entity.SaveDraft])
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|GetPublishTaskFlowDetail,GetTaskFlowDetail,err:%s",
				sid, err.Error())
			return nil, err
		}
		return draft, nil
	}

	err := db.Model(&entity.TaskFlowPublishHistory{}).Select("f_flow_id,f_intent_id,"+
		"f_flow_json, f_version, f_proto_version, f_save_type,f_uin,f_sub_uin,"+
		"f_publish_time, f_create_time, f_update_time").
		Where("f_flow_id=? AND f_is_deleted=? AND  f_version=?",
			flowId, entity.TaskFlowUnDeleted, version).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)",
			botBizId).First(&tfph).Error

	if err == gorm.ErrRecordNotFound {
		log.DebugContextf(ctx, "sid:%s|GetPublishTaskFlowDetail, err:ErrRecordNotFound", sid)
		return nil, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetPublishTaskFlowDetail, err:%s", sid, err.Error())
		return nil, err
	}
	return &tfph, nil
}

// GetPublishTaskFlowList 获取发布历史列表
func GetPublishTaskFlowList(ctx context.Context, flowId,
	botBizId string) (*[]entity.TaskFlowPublishHistory, int64, error) {
	sid := util.RequestID(ctx)
	var total int64
	var tfphs []entity.TaskFlowPublishHistory
	var taskflow *entity.TaskFlow
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	taskflow, err := GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetPublishTaskFlowList,GetTaskFlowDetail,err:%s",
			sid, err.Error())
		return nil, 0, err
	}

	err = db.Model(&entity.TaskFlowPublishHistory{}).Select("f_flow_id,"+
		"f_flow_json, f_version, f_proto_version, f_save_type,f_uin,f_sub_uin,"+
		"f_publish_time, f_create_time, f_update_time").
		Where("f_flow_id=? AND f_is_deleted=? AND f_save_type=1",
			flowId, entity.TaskFlowUnDeleted).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)",
			botBizId).Order("f_save_type ASC, f_publish_time DESC").
		Limit(config.GetMainConfig().TaskFlow.HistoryLen - 1).Count(&total).Scan(&tfphs).Error

	if err == gorm.ErrRecordNotFound {
		return nil, 0, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetPublishTaskFlowList,err:%s", sid, err.Error())
		return nil, 0, err
	}

	if len(taskflow.DialogJsonDraft) > 0 {
		version := config.GetMainConfig().VerifyTaskFlow.Version + "-" +
			fmt.Sprintf("%d", taskflow.ProtoVersion) + "-" +
			strconv.Itoa(int(taskflow.UpdateTime.Unix()))
		draft := entity.TaskFlowToHistoryFlowTask(taskflow, version,
			entity.EnumSaveType[entity.SaveDraft])
		tfphs = append(tfphs[:0], append([]entity.TaskFlowPublishHistory{*draft}, tfphs[0:]...)...)
		log.DebugContextf(ctx, "sid:%s|GetPublishTaskFlowList, draft:%+v", sid, draft)
		return &tfphs, total + 1, nil
	}

	return &tfphs, total, nil
}

// ListTaskFlow 任务流程列表（含筛选）
func ListTaskFlow(ctx context.Context,
	params *entity.ListTaskFLowParams) ([]*entity.TaskFlow, int64, error) {
	var total int64
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var taskFlows []*entity.TaskFlow
	var releaseStatus []string
	sid := util.RequestID(ctx)

	if params.FlowType == "" {
		params.FlowType = entity.FlowTypeICS // 目前默认客服
	}
	if params.OrderBy == "" {
		params.OrderBy = "DESC" // 默认降序
	}

	var result = db.Model(&entity.TaskFlow{}).Select(
		"f_flow_id, f_intent_name, f_intent_desc, f_intent_id,f_flow_state,f_flow_type,f_version,"+
			"f_category_id,f_release_status,  f_create_time, f_update_time").
		Where("f_is_deleted=? AND f_flow_type=?", entity.TaskFlowUnDeleted, params.FlowType).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)",
			params.BotBizId).Order("f_update_time " + params.OrderBy).Order("f_id DESC")

	if params.Query != "" {
		result = result.Where("f_intent_name Like ? escape '*'", "%*"+params.Query+"%")
	}

	if len(params.CateIds) > 0 {
		result = result.Where("f_category_id IN ?", params.CateIds)
	}

	if len(params.ReleaseStatus) > 0 {
		for _, val := range params.ReleaseStatus {
			// 已发布
			if val == entity.FrontStatusPublished {
				releaseStatus = append(releaseStatus, entity.ReleaseStatusPublished)
			}
			// 发布中
			if val == entity.FrontStatusPublishing {
				releaseStatus = append(releaseStatus, entity.ReleaseStatusPublishing)
			}
			// 发布失败
			if val == entity.FrontStatusPublishedFail {
				releaseStatus = append(releaseStatus, entity.ReleaseStatusFail)
			}
		}
	}

	if len(releaseStatus) > 0 {
		result = result.Where("f_release_status IN ?", releaseStatus)
		// 草稿
		if entity.IsExitFrontStatusDraft(params.ReleaseStatus) {
			result = result.Or("(f_is_deleted=0 AND f_flow_state=? AND f_intent_id IN (SELECT f_intent_id "+
				"FROM t_robot_intent WHERE f_robot_id = ?))", entity.FlowStateDraft, params.BotBizId)
			if params.Query != "" {
				result = result.Where("f_intent_name Like ? escape '*'", "%*"+params.Query+"%")
			}
		}
		// 待发布
		if entity.IsExitFrontStatusUnPublished(params.ReleaseStatus) {
			result = result.Or("(f_is_deleted=0 AND f_release_status=? AND f_flow_state IN ? AND "+
				"f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?))", entity.ReleaseStatusUnPublished,
				[]string{entity.FlowStateEnable, entity.FlowStateChangeUnPublish}, params.BotBizId)
			if params.Query != "" {
				result = result.Where("f_intent_name Like ? escape '*'", "%*"+params.Query+"%")
			}
		}
		// 待更新发布
		if entity.IsExitFrontStatusChangeUnPublished(params.ReleaseStatus) {
			result = result.Or("(f_is_deleted=0 AND f_release_status=? AND f_flow_state IN ? AND"+
				" f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?))", entity.ReleaseStatusUnPublished,
				[]string{entity.FlowStatePublishChange}, params.BotBizId)
			if params.Query != "" {
				result = result.Where("f_intent_name Like ? escape '*'", "%*"+params.Query+"%")
			}
		}
	} else {
		// 草稿
		var flowState []string
		if entity.IsExitFrontStatusDraft(params.ReleaseStatus) {
			releaseStatus = append(releaseStatus, entity.ReleaseStatusUnPublished)
			flowState = append(flowState, entity.FlowStateDraft)
		}
		// 待发布
		if entity.IsExitFrontStatusUnPublished(params.ReleaseStatus) {
			releaseStatus = append(releaseStatus, entity.ReleaseStatusUnPublished)
			flowState = append(flowState, entity.FlowStateEnable, entity.FlowStateChangeUnPublish)
		}
		// 待更新发布
		if entity.IsExitFrontStatusChangeUnPublished(params.ReleaseStatus) {
			releaseStatus = append(releaseStatus, entity.ReleaseStatusUnPublished)
			flowState = append(flowState, entity.FlowStatePublishChange)
		}
		if len(releaseStatus) > 0 {
			result = result.Where("f_release_status IN ?",
				set.RemoveDuplicatesAndEmpty(releaseStatus))
		}
		if len(flowState) > 0 {
			result = result.Where("f_flow_state IN ?",
				set.RemoveDuplicatesAndEmpty(flowState))
		}

	}

	result = result.Count(&total)

	pageSize := uint32(15)
	page := uint32(1)
	if params.PageSize > 0 {
		pageSize = params.PageSize
	}
	if params.Page > 0 {
		page = params.Page
	}
	offset := (page - 1) * pageSize

	log.InfoContextf(ctx, "ListTaskFlow, params:%s|%+v", sid, params)
	result = result.Offset(int(offset)).
		Limit(int(pageSize)).Scan(&taskFlows)
	if result.Error != nil {
		log.ErrorContextf(ctx, "ListTaskFlow, err:%s|%s", sid, result.Error.Error())
		return nil, 0, result.Error
	}

	return taskFlows, total, nil
}

// GroupTaskFlow 分组移动任务流到不同分类
func GroupTaskFlow(ctx context.Context, cateId string, flowIds []string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Table(entity.TaskFlow{}.TableName()).Where(&entity.TaskFlow{
		IsDeleted: entity.TaskFlowUnDeleted,
	}).Where(entity.TTaskFlowColumns.FlowID+" IN (?)", flowIds).Updates(
		entity.TaskFlow{CategoryID: cateId}).Error; err != nil {
		log.ErrorContextf(ctx, "GroupTaskFlow, error:%s", err.Error())
		return err
	}
	return nil
}

// GetTaskFLowDetails 批量获取TaskFLow详情
func GetTaskFLowDetails(ctx context.Context, flowIds []string,
	botBizId string) (map[string]*entity.TaskFlow, error) {
	if len(flowIds) == 0 {
		return nil, nil
	}
	list := make([]*entity.TaskFlow, 0)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=?", entity.TaskFlowUnDeleted).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)", botBizId).
		Where("f_flow_id IN (?)", flowIds).Find(&list).Error
	if err != nil {
		log.ErrorContextf(ctx, "批量获取TaskFLow详情：%s", err.Error())
		return nil, err
	}
	tfs := make(map[string]*entity.TaskFlow, 0)
	for _, item := range list {
		tfs[item.FlowID] = item
	}
	return tfs, nil
}

// GetTaskFLowDetailsByName 通过名称查询任务流程名是否存在
func GetTaskFLowDetailsByName(ctx context.Context, flowId, botBizId, taskFlowName string) (*entity.TaskFlow, error) {
	var taskFLow *entity.TaskFlow

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	join := fmt.Sprintf("INNER JOIN %s ON %s.f_intent_id = %s.f_intent_id", tableRobotIntent, tableTaskFlow,
		tableRobotIntent)
	result := db.Table(tableTaskFlow).
		Joins(join).
		Where(fmt.Sprintf("%s.f_robot_id = ?", tableRobotIntent), strings.TrimSpace(botBizId)).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableTaskFlow)).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableRobotIntent)).
		Where(fmt.Sprintf("%s.f_flow_id != ?", tableTaskFlow), strings.TrimSpace(flowId)).
		Where(fmt.Sprintf("%s.f_intent_name = ?", tableTaskFlow), strings.TrimSpace(taskFlowName)).
		Find(&taskFLow)

	if result.Error != nil {
		return nil, result.Error
	}

	if result.RowsAffected == 0 {
		return nil, nil
	}

	return taskFLow, nil
}

// GetTaskFlowDetail 获取任务流程详情
func GetTaskFlowDetail(ctx context.Context, flowId,
	botBizId string) (*entity.TaskFlow, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var total int64
	taskFlow := entity.TaskFlow{}
	if err := db.Table(taskFlow.TableName()).
		Where(entity.TTaskFlowColumns.FlowID+"=? AND "+
			entity.TTaskFlowColumns.IsDeleted+"=?",
			flowId, entity.TaskFlowUnDeleted).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)", botBizId).
		Count(&total).Find(&taskFlow).Error; err != nil {
		return nil, err
	}
	if total == 0 {
		log.WarnContextf(ctx, "GetTaskFlowDetail:botId:%s|:fid:%s,not found",
			botBizId, flowId)
		return nil, utilsErrors.ErrTaskFlowNotFound
	}
	return &taskFlow, nil
}

// GetDHTFNewNameJSON 获取最新的任务流名称，替换json里面任务流程名称
func GetDHTFNewNameJSON(ctx context.Context, flowId string, botBizId, flowType,
	version, tfName, saveType string) (*entity.TaskFlowPublishHistory, error) {

	tfph, err := GetPublishTaskFlowDetail(ctx, botBizId, flowId, entity.FlowTypeICS, version, saveType)
	if err != nil {
		return nil, err
	}
	if tfph == nil {
		return nil, errors.New("get history publish taskflow failed")
	}
	hJson, err := protoutil.JsonToTaskFlow(tfph.FlowJson)
	if err != nil {
		return nil, err
	}
	hJson.TaskFlowName = tfName
	newJson, err := protoutil.TaskFlowToJson(hJson)
	if err != nil {
		return nil, err
	}
	tfph.FlowJson = newJson
	return tfph, nil
}

// TxGetTaskFlowDetailCopyWithVersionRecover 获取指定版本的任务流程详情
func TxGetTaskFlowDetailCopyWithVersionRecover(ctx context.Context, tx *gorm.DB, botBizId, flowId,
	version string) (*entity.TaskFlow, string, error) {
	var msg string
	// 版本号为空，取最新版本
	originalTaskFlow, err := GetTaskFlowDetail(ctx, flowId, botBizId)
	if len(version) == 0 {
		return originalTaskFlow, "", err
	}
	// 版本号非空，取指定版本
	taskFlow := &entity.TaskFlow{}
	// 获取指定版本的任务流(更新历史版本Json中的IntentName)
	tfph, err := GetDHTFNewNameJSON(ctx, flowId, botBizId, entity.FlowTypeICS,
		version, originalTaskFlow.IntentName, entity.SavePublished)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetTaskFlowDetailWithVersion, flowId:%s, version:%s, err:%s",
			util.RequestID(ctx), flowId, version, err.Error())
		return &entity.TaskFlow{}, "", err
	}

	tfph, msg, _, _, err = recoverTaskFlowRelatedData(ctx, tx, botBizId, tfph)

	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetTaskFlowDetailWithVersion, flowId:%s, version:%s, err:%+v",
			util.RequestID(ctx), flowId, version, err)
		return &entity.TaskFlow{}, msg, err
	}

	taskFlow.DialogJsonDraft = tfph.FlowJson
	taskFlow.Uin = tfph.Uin
	taskFlow.SubUin = tfph.SubUin
	taskFlow.FlowID = originalTaskFlow.FlowID
	taskFlow.ProtoVersion = originalTaskFlow.ProtoVersion
	taskFlow.IntentName = originalTaskFlow.IntentName
	taskFlow.IntentDesc = originalTaskFlow.IntentDesc
	taskFlow.CategoryID = originalTaskFlow.CategoryID
	taskFlow.UpdateTime = originalTaskFlow.UpdateTime

	return taskFlow, msg, nil
}

// DeleteTaskFlow 逻辑删除TaskFlow
func DeleteTaskFlow(ctx context.Context,
	robotId string, flowIds []string) error {
	var corpusIDs []string
	var intentIDs []string
	var enableIntentIds []string
	var enableCorpusIDs []string
	var enableFlowsId []string
	var enableIntentExampleIds []string

	var taskFlows []entity.TaskFlow
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	vdb := vdao.NewDao()
	sid := util.RequestID(ctx)

	if err := db.Transaction(func(tx *gorm.DB) error {
		if len(flowIds) <= 0 {
			log.ErrorContextf(ctx, "DeleteTaskFlow,GetTaskFLowDetails err:%s|flowIds:%s", sid, flowIds)
			return utilsErrors.ErrParams
		}

		//1. 通过flowIds 在 TaskFlow 找到 intentids 并标记删除
		if err := tx.Table(entity.TaskFlow{}.TableName()).
			Where("f_flow_id IN (?)", flowIds).
			Where("f_release_status != ?", entity.ReleaseStatusPublishing).
			Find(&taskFlows).Updates(map[string]interface{}{
			"f_is_deleted":     gorm.Expr("f_id"),
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow failed delete:%+v,err:%+v", flowIds, err)
			return err
		}
		for _, flow := range taskFlows {
			intentIDs = append(intentIDs, flow.IntentID)
		}

		// 1-2 删除历史记录
		if err := tx.Table(entity.TaskFlowHistory{}.TableName()).
			Where("f_flow_id IN (?)", flowIds).
			Updates(map[string]interface{}{"f_is_deleted": gorm.Expr("f_id")}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow TaskFlowHistory failed delete:%+v,err:%+v", flowIds, err)
			return err
		}

		// 1-3 删除历史发布版本
		if err := tx.Table(entity.TaskFlowPublishHistory{}.TableName()).
			Where("f_flow_id IN (?)", flowIds).
			Updates(map[string]interface{}{"f_is_deleted": gorm.Expr("f_id")}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow TaskFlowPublishHistory failed delete:%+v,err:%+v", flowIds, err)
			return err
		}

		// 找出校验过的任务流
		for _, flow := range taskFlows {
			if flow.DialogJsonEnable != "" {
				enableIntentIds = append(enableIntentIds, flow.IntentID)
				enableFlowsId = append(enableFlowsId, flow.FlowID)
			}
		}

		log.InfoContextf(ctx, "DeleteTaskFlow,robotId:%s|%s, intentIDs:%+v", sid, robotId, intentIDs)

		// 2. 删除Intent与Flow绑定关系
		if err := tx.Table(entity.IntentFlow{}.TableName()).
			Where("f_flow_id IN (?) AND f_intent_id IN (?)", flowIds, intentIDs).
			Updates(map[string]interface{}{
				"f_is_deleted":     gorm.Expr("f_id"),
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow failed delete IntentFlow,"+
				"intentIDs:%+v,flowIds:%+v,err:%+v", intentIDs, flowIds, err)
			return err
		}
		// 3. 在表 t_intent 删除
		if err := tx.Table(entity.Intent{}.TableName()).Where("f_intent_id IN (?)",
			intentIDs).Updates(map[string]interface{}{
			"f_is_deleted":     gorm.Expr("f_id"),
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow, delete Intent fail err:%s｜%v", sid, err)
			return err
		}
		// 3-1. 在表 t_intent_slot 删除
		if err := deleteIntentSlot(ctx, tx, intentIDs); err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow|deleteIntentSlot|sid:%s｜%v", sid, err)
			return err
		}
		// 3-2. 在表 t_intent_entry 删除
		if err := deleteIntentEntry(ctx, tx, intentIDs); err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow|deleteIntentSlot|sid:%s｜%v", sid, err)
			return err
		}
		// 3-3 在表 t_intent_var 删除
		if err := deleteIntentVarParams(ctx, tx, intentIDs); err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow|deleteIntentVarParams|sid:%s｜%v", sid, err)
			return err
		}

		// 3-4 删除对应的评测对话反馈记录
		if err := rpc.DeleteFeedbackByFlowIds(ctx, flowIds, cast.ToUint64(robotId)); err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow|DeleteFeedbackByFlowIds|sid:%s｜%v", sid, err)
			return err
		}

		// 4. 删除语料库 t_corpus
		var corpuss []entity.Corpus
		if err := tx.Table(entity.Corpus{}.TableName()).Where("f_intent_id IN (?)",
			intentIDs).Find(&corpuss).Updates(map[string]interface{}{
			"f_is_deleted":     gorm.Expr("f_id"),
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow,"+
				" delete Corpus err:%v, intentIDs:%+v", err, intentIDs)
			return err
		}
		for _, corpus := range corpuss {
			corpusIDs = append(corpusIDs, corpus.CorpusID)
		}

		// 找出保存了向量库的预料
		var enableCorpusList []entity.Corpus
		if err := tx.Table(entity.Corpus{}.TableName()).Where("f_intent_id IN (?)",
			enableIntentIds).Find(&enableCorpusList).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow,"+
				" delete Corpus err:%v, enableIntentIds:%+v", err, enableIntentIds)
			return err
		}
		for _, corpus := range enableCorpusList {
			enableCorpusIDs = append(enableCorpusIDs, corpus.CorpusID)
		}

		// 5. 删除与机器人绑定 t_robot_intent
		if err := tx.Table(entity.RobotIntent{}.TableName()).
			Where("f_robot_id = ? AND f_intent_id IN (?)", robotId, intentIDs).
			Updates(map[string]interface{}{
				"f_is_deleted":     gorm.Expr("f_id"),
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow,delete RobotIntent err:%v,robotId:%+v, intentIDs:%v",
				err, robotId, intentIDs)
			return err
		}

		// 6. 删除示例问法
		var intentExamples []entity.IntentCorpus
		if err := tx.Table(entity.IntentCorpus{}.TableName()).
			Where("f_robot_id = ? AND f_intent_id IN (?)", robotId, intentIDs).
			Find(&intentExamples).Updates(map[string]interface{}{
			"f_is_deleted":     gorm.Expr("f_id"),
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteTaskFlow,delete IntentCorpus err:%v,robotId:%+v, intentIDs:%v",
				err, robotId, intentIDs)
			return err
		}
		for _, example := range intentExamples {
			enableIntentExampleIds = append(enableIntentExampleIds, example.CorpusID)
		}
		// 删除历史的示例问法
		if err := tx.Table(entity.IntentCorpusPublishHistory{}.TableName()).
			Where("f_intent_id IN ?", intentIDs).
			Find(&intentExamples).Updates(map[string]interface{}{
			"f_is_deleted": gorm.Expr("f_id"),
		}).Error; err != nil {
			log.ErrorContextf(ctx, "sid:%s|DeleteTaskFlow,delete IntentCorpusPublishHistory|err:%v,"+
				"robotId:%+v, intentIDs:%v", err, robotId, intentIDs)
			return err
		}
		// =================== 向量操作 ===========================
		// 删除示例问法
		if len(enableIntentExampleIds) > 0 {
			log.InfoContextf(ctx, "DeleteTaskFlow,DeleteIntentExampleVector,"+
				"robotId:%s|%s, enableIntentExampleIds:%+v", sid, robotId, enableIntentExampleIds)
			if err := vdb.DeleteIntentExampleVector(ctx, robotId, enableIntentExampleIds); err != nil {
				log.ErrorContextf(ctx, "DeleteTaskFlow|DeleteIntentExampleVector fail err:%s", sid)
				return err
			}
		}

		// 6. 删除向量语料数据
		if len(enableCorpusIDs) > 0 {
			log.InfoContextf(ctx, "DeleteTaskFlow,DeleteCorpusVector,"+
				"robotId:%s|%s, corpusIDs:%+v", sid, robotId, corpusIDs)
			if err := vdb.DeleteCorpusVector(ctx, robotId, enableCorpusIDs); err != nil {
				log.ErrorContextf(ctx, "DeleteTaskFlow, DeleteCorpusVector fail err:%s", sid)
				return err
			}
		}

		if len(enableFlowsId) > 0 {
			// 7. 组装任务流删除数据给dm
			log.InfoContextf(ctx, "DeleteTaskFlow,AssembleDmDeleteTaskFlowData,"+
				"robotId:%s|%s, flowIds:%+v", sid, robotId, enableFlowsId)
			_, dmErr := AssembleDmDeleteTaskFlowData(ctx, robotId, enableFlowsId)
			if dmErr != nil {
				log.ErrorContextf(ctx, "DeleteTaskFlow,AssembleDmDeleteTaskFlowData: %s|%v", sid, dmErr)
				return dmErr
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "DeleteTaskFlow fail, err:%+v", err)
		return err
	}
	return nil
}

// SaveTaskFlow 更新任务流
func SaveTaskFlow(ctx context.Context, params entity.ModifyTaskFlowParams) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		return editTaskFlowByTx(ctx, tx, params)
	}); err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow fail, err:%+v", err)
		return err
	}
	return nil
}

// editTaskFlowByTx 事物更新任务流
func editTaskFlowByTx(ctx context.Context, tx *gorm.DB, params entity.ModifyTaskFlowParams) error {
	var err error
	// 构造新的任务流程
	newTaskFlow := getTaskFlow(params)
	// 1. 通过任务流ID获取 意图ID
	oldTaskFlow := entity.TaskFlow{}
	oldTaskFlowResult := tx.Table(entity.TaskFlow{}.TableName()).Where(entity.TTaskFlowColumns.FlowID+"=?",
		params.FlowID).Find(&oldTaskFlow)
	if oldTaskFlowResult.Error != nil {
		log.ErrorContextf(ctx, "getOldTaskFlow err %v", oldTaskFlowResult.Error)
		return oldTaskFlowResult.Error
	}
	oldFlowJson := oldTaskFlow.DialogJsonEnable
	newFlowJson := newTaskFlow.DialogJsonDraft
	newTaskFlow.IntentID = oldTaskFlow.IntentID
	isJsonEqual, _ := util.CompareJSONEqual(ctx, oldFlowJson, newFlowJson)
	// 保存至测试环境时更新任务流程状态动作相关及获取历史任务流程；历史任务流程只有一个
	if params.IsEnable == uint32(KEP.SaveTaskFlowReq_ENABLE) && !isJsonEqual {
		log.InfoContextf(ctx, "editTaskFlowByTx-oldTaskFlow:%+v", oldTaskFlow)
		if err, newTaskFlow = taskFlowStatusAndAction(oldTaskFlow, newTaskFlow); err != nil {
			return err
		}
		if err = saveHistoryTaskFlow(ctx, tx, params, newTaskFlow); err != nil {
			return err
		}
	}
	log.DebugContextf(ctx, "Updates newTaskFlow:%+v", newTaskFlow)
	// 1. 更新任务流程
	if err := oldTaskFlowResult.Updates(newTaskFlow).Error; err != nil {
		log.ErrorContextf(ctx, "Updates newTaskFlow err:%v", err)
		return err
	}
	// 2. 通过RobotId 和 IntentId 查绑定关系，防止其它机器人的修改
	if err = updateRobotIntent(ctx, tx, params.RobotId, newTaskFlow); err != nil {
		return err
	}
	// 3. 更新意图
	if err = updateIntent(ctx, tx, newTaskFlow); err != nil {
		return err
	}
	// 4. 更新语料库
	if err = updateIntentCorpus(ctx, tx, newTaskFlow); err != nil {
		return err
	}
	// 5. 更新意图和slot的关联关系
	if err := updateIntentSlotAssociation(ctx, tx, oldTaskFlow.IntentID, params.SlotIDs); err != nil {
		log.ErrorContextf(ctx, "editTaskFlowByTx|updateIntentSlotAssociation|err:%+v", err)
		return err
	}
	// 6.更新意图和entry(词条)的关联关系
	if err := updateIntentEntryAssociation(ctx, tx, oldTaskFlow.IntentID, params.EntryIDs); err != nil {
		log.ErrorContextf(ctx, "editTaskFlowByTx|updateIntentEntryAssociation|err:%+v", err)
		return err
	}
	// 6.更新意图和VarParams(自定义变量)的关联关系
	if err := updateIntentVarParamsAssociation(ctx, tx, oldTaskFlow.IntentID, params.VarIDs); err != nil {
		log.ErrorContextf(ctx, "editTaskFlowByTx|updateIntentEntryAssociation|err:%+v", err)
		return err
	}
	if params.IsEnable == uint32(KEP.SaveTaskFlowReq_ENABLE) && !isJsonEqual {
		if err := SaveIntentCorpusToVector(ctx, tx, params.RobotId, oldTaskFlow.IntentID); err != nil {
			log.ErrorContextf(ctx, "SaveTaskFlow|SaveIntentCorpusToVector|err:%v", err)
			return err
		}
		tree, err := protoutil.JsonToTaskFlow(newTaskFlow.DialogJsonEnable)
		if err != nil {
			log.ErrorContextf(ctx, "SaveTaskFlow, UnmarshalStr taskFlow.DialogJsonEnable,err:%v", err)
			return utilsErrors.ErrTaskFlowUIJsonParams
		}
		//通知DM： AssembleDmUpsertTaskFlowData 组装任务流更新数据给dm,对接人 mike
		log.InfoContextf(ctx, "SaveTaskFlow,db.SaveTaskFlow AssembleDmUpsertTaskFlowData:%s|%+v|%s|%s",
			params.RobotId, tree, oldTaskFlow.IntentID, oldTaskFlow.Version)
		_, dmErr := AssembleDmUpsertTaskFlowData(ctx, params.RobotId, tree, oldTaskFlow.IntentID,
			oldTaskFlow.Version, newTaskFlow.IntentDesc)
		if dmErr != nil {
			log.ErrorContextf(ctx, "SaveTaskFlow, AssembleDmUpsertTaskFlowData err:%v", dmErr)
			return dmErr
		}
	}
	return nil
}

// taskFlowStatusAndAction 保存至测试环境；根据任务流程历史状态变更最新状态和动作
func taskFlowStatusAndAction(oldTaskFlow entity.TaskFlow, newTaskFlow entity.TaskFlow) (error, entity.TaskFlow) {
	oldReleaseState := oldTaskFlow.ReleaseStatus // 历史任务流程发布状态
	oldFlowState := oldTaskFlow.FlowState        // 历史任务流程画布状态
	// 发布状态中不允许修改
	if oldReleaseState == entity.ReleaseStatusPublishing {
		return utilsErrors.ErrTaskFlowCantNotPublish, newTaskFlow
	}
	// 草稿状态➡️启用状态，启用状态态只会出现一次；第一次保存至测试环境的时候
	if oldFlowState == entity.FlowStateDraft {
		newTaskFlow.FlowState = entity.FlowStateEnable
	}
	// 启用状态且没有发布：已修改待发布
	if oldFlowState == entity.FlowStateEnable && oldReleaseState != entity.ReleaseStatusPublished {
		newTaskFlow.FlowState = entity.FlowStateChangeUnPublish // 已修改待发布
	}
	// 已发布：已发布仍有修改
	if oldReleaseState == entity.ReleaseStatusPublished {
		newTaskFlow.FlowState = entity.FlowStatePublishChange // 已发布仍有修改
	}
	// 第一次发布成功 FlowStateEnable||FlowStateChangeUnPublish， ReleaseStatusPublished
	if oldFlowState == entity.FlowStatePublishChange ||
		((oldFlowState == entity.FlowStateEnable || oldFlowState == entity.FlowStateChangeUnPublish) &&
			oldReleaseState == entity.ReleaseStatusPublished) {
		// 发布过的任务流再编辑 action是 Update
		newTaskFlow.Action = entity.ActionUpdate
	}
	// 保存至测试环境，发布状态releaseStatus改成:未发布
	newTaskFlow.ReleaseStatus = entity.ReleaseStatusUnPublished
	newTaskFlow.DialogJsonEnable = newTaskFlow.DialogJsonDraft
	return nil, newTaskFlow
}

// saveHistoryTaskFlow 保存任务流程历史任务
func saveHistoryTaskFlow(ctx context.Context, tx *gorm.DB, params entity.ModifyTaskFlowParams,
	newTaskFlow entity.TaskFlow) error {
	historyTaskFlow := &entity.TaskFlowHistory{}
	result := tx.Table(historyTaskFlow.TableName()).Where("f_flow_id=?",
		params.FlowID).Order("f_id DESC").First(&historyTaskFlow)
	if result.Error != nil && result.RowsAffected > 0 {
		log.ErrorContextf(ctx, "saveHistoryTaskFlow get err:%v", result.Error)
		return result.Error
	}
	if result.RowsAffected <= 0 {
		//保存历史记录
		if err := tx.Table(historyTaskFlow.TableName()).Create(&entity.TaskFlowHistory{
			FlowID:       params.FlowID,
			FlowJson:     newTaskFlow.DialogJsonEnable,
			Version:      config.GetMainConfig().VerifyTaskFlow.Version,
			Uin:          params.Uin,
			SubUin:       params.SubUin,
			ProtoVersion: int32(KEP.TaskFlowProtoVersion_V2_4), // 保存历史记录时，保存FlowJson的协议版本号
		}).Error; err != nil {
			log.ErrorContextf(ctx, "saveHistoryTaskFlow Create err: %v", err)
			return err
		}
	} else {
		// 更新历史记录
		version := config.GetMainConfig().VerifyTaskFlow.Version +
			"-" + strconv.Itoa(int(time.Now().Unix()))
		result := tx.Table(historyTaskFlow.TableName()).
			Where("f_flow_id=?", params.FlowID).
			Updates(&entity.TaskFlowHistory{
				FlowJson:     params.DialogJsonEnable,
				Version:      version,
				ProtoVersion: historyTaskFlow.ProtoVersion, // 保存历史记录时，保存FlowJson的协议版本号
			})

		if result.Error != nil {
			log.ErrorContextf(ctx, "saveHistoryTaskFlow update err: %v", result.Error)
			return result.Error
		}
	}
	return nil
}

// getTaskFlow 构造任务流程基本修改参数
func getTaskFlow(params entity.ModifyTaskFlowParams) entity.TaskFlow {
	taskFlow := entity.TaskFlow{}
	taskFlow.IntentName = params.IntentName
	taskFlow.IntentDesc = params.IntentDesc
	taskFlow.Uin = params.Uin
	taskFlow.SubUin = params.SubUin
	taskFlow.CategoryID = params.CategoryID
	taskFlow.DialogJsonDraft = params.DialogJsonDraft
	return taskFlow
}

// SaveIntentCorpusToVector 将意图语料保存向量
func SaveIntentCorpusToVector(ctx context.Context, tx *gorm.DB, robotID, intentID string) error {
	sid := util.RequestID(ctx)
	// 通过意图ID获取意图信息
	intentMap, err := GetCorpusByIntentIDsWithTx(ctx, tx, []string{intentID})
	log.InfoContextf(ctx, "SaveTaskFlow GetCorpusByIntentIDsWithTx:%s|%+v", sid, intentMap)
	if err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow GetCorpusByIntentIDsWithTx:%s|err:%+v", sid, err)
		return err
	}
	cors, ok := intentMap[intentID]
	if !ok || len(cors) == 0 {
		log.WarnContextf(ctx, "SaveTaskFlow GetCorpusByIntentIDsWithTx:%s|len(cor):%d", sid, len(cors))
		return utilsErrors.ErrCorpusNotFound
	}
	// 更新语料到向量库
	log.InfoContextf(ctx, "SaveTaskFlow,db.SaveTaskFlow"+
		" saveCorpusToVector:%s|%s|%s|%+v", sid, robotID, intentID, cors[0])
	if err := saveCorpusToVector(ctx, tx, robotID, intentID, cors[0]); err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow, saveCorpusToVector Corpus err:%s|%v", sid, err)
		return err
	}
	return nil
}

// SaveCorpusToVector 保存语料到向量库
func saveCorpusToVector(ctx context.Context, tx *gorm.DB, robotID, intentID string, corpus *entity.Corpus) error {
	if corpus == nil {
		return nil
	}

	sid := util.RequestID(ctx)
	vdb := vdao.NewDao()
	//redisClient := database.GetRedis()

	sandboxGroupID, _, err := vdb.GetIntentVectorGroupId(ctx, tx, robotID)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|saveCorpusToVector|GetIntentVectorGroupId,fail,"+
			"robotID:%s|err:%s|%v", sid, robotID, err)
		return err
	}

	// 创建向量Group并缓存（查缓存如果存在不创建，不存在创建）
	//rkey := fmt.Sprintf("%s-%s-%s",
	//	config.GetMainConfig().VectorGroup.Biz, vdao.SandboxGroupInfix, robotID)
	//sandboxGroupID, _ := vdao.GetVectorGroupID(robotID)
	//ok, err := HasSaveCorpusVectorGroupToRedis(ctx, rkey)
	//log.InfoContextf(ctx, "SaveTaskFlow,db.SaveTaskFlow  saveCorpusToVector:%s|%s|%s|%+v",
	//	sid, rkey, sandboxGroupID, ok)
	//if err != nil {
	//	log.ErrorContextf(ctx, "saveCorpusToVector hasSaveCorpusVectorGroup fail, "+
	//		"redis.Get fail, key: %s, err: %s|%v", rkey, sid, err)
	//	return err
	//}
	//if !ok {
	//	// 没有向量Group，创建
	//sandboxGroupID, _, err = vdb.CreateBotCorpusGroup(ctx, robotID)
	//	if err != nil {
	//		log.ErrorContextf(ctx, "saveCorpusToVector CreateBotCorpusGroup, fail,"+
	//			" robotID: %s, err: %s|%v", robotID, sid, err)
	//		return err
	//	}
	//	// TODO... 缓存不过期。后续可以优化设置一个比较长的缓存期或者存mysql
	//	if err = redisClient.Set(ctx, rkey, 1, 0).Err(); err != nil {
	//		log.ErrorContextf(ctx, "saveCorpusToVector CreateBotCorpusGroup, "+
	//			"redis.Set fail, key: %s, err: %s|%v", rkey, sid, err)
	//		return err
	//	}
	//}

	// 获取示例用法
	intentExamples, _, err := GetExampleListByBotAndIntentIdByTx(ctx, tx, robotID, intentID)
	if err != nil {
		log.ErrorContextf(ctx, "saveCorpusToVector|GetExampleListByBotAndIntentIdByTx: %s|%v", sid, err)
		return err
	}
	// 保存到向量库
	if err = vdb.SaveCorpusVector(ctx, robotID, corpus.CorpusID, intentID, corpus.Content,
		sandboxGroupID, intentExamples); err != nil {
		log.ErrorContextf(ctx, "saveCorpusToVector SaveVector: %s|%v", sid, err)
		return err
	}

	return nil
}

// CreateTaskFlow 创建任务流 及 绑定关系
func CreateTaskFlow(ctx context.Context, params entity.CreateTaskFlowParams) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		return TxCreateTaskFlow(ctx, tx, params)
	}); err != nil {
		log.ErrorContextf(ctx, "CreateTaskFlow fail, err:%+v", err)
		return err
	}
	return nil
}

// TxCreateTaskFlow 创建任务流 及 绑定关系
func TxCreateTaskFlow(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) error {
	// 任务流数量限制
	var total int64
	err := tx.Model(&entity.TaskFlow{}).Select("f_flow_id").
		Where("f_is_deleted=? AND f_flow_type=?", entity.TaskFlowUnDeleted, params.FlowType).
		Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id=?)", params.RobotId).
		Count(&total).Error
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, "TxCreateTaskFlow currentTotal:%d, maxTotal:%d",
		total, config.GetMainConfig().VerifyTaskFlow.TaskFlowLimit)
	if total > int64(config.GetMainConfig().VerifyTaskFlow.TaskFlowLimit) {
		return utilsErrors.ErrTaskFlowLimit
	}
	return createTaskFlow(ctx, tx, params, "", nil)
}

// createTaskFlow
func createTaskFlow(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams, operatorType string,
	slotParams []*entity.SlotMigrationInfo) error {
	// 1. 新建意图
	err := saveIntent(ctx, tx, params)
	if err != nil {
		return err
	}
	// 2. 新建对话流程
	taskFlow, err := saveTaskFlow(ctx, tx, params)
	if err != nil {
		return err
	}
	// 3. 新建意图语料
	err = saveIntentCorpus(ctx, tx, params)
	if err != nil {
		return err
	}

	// 4. 绑定机器人和意图（任务流程）
	err = saveRobotIntent(ctx, tx, params)
	if err != nil {
		return err
	}
	// 5. 绑定意图 和 任务流程
	err = saveIntentTaskFlow(ctx, tx, params)
	if err != nil {
		return err
	}

	// 6. 创建/导入 任务流程时，获取向量GroupId，没有就创建
	if _, _, err = vdao.NewDao().GetIntentVectorGroupId(ctx, tx, params.RobotId); err != nil {
		log.WarnContextf(ctx, "createTaskFlow,Create GetIntentVectorGroupId err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "createTaskFlow success")
	// TODO mike 下面的理论上，创建任务流程的时候不需要执行；但导入和复制时候需要走下面的逻辑
	// 意图和槽位的绑定关系: 新建的时候不需要，统一在 SaveTaskFlow 处理
	// 上述废弃：2.2.1迭代新增：草稿状态的任务流也需要绑定意图槽位关系
	tree, err := protoutil.JsonToTaskFlowForPreCheck(taskFlow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,JsonToTaskFlowForPreCheck err:%v", err)
		return err
	}

	// 导入，复制时关联实体，自定义变量，词条，分支条件实体全选词条
	slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList := idget.GetAllRelatedFromTree(ctx, tree)
	if operatorType == entity.OperateTypeImport && slotParams != nil {
		entryIDList = GetRelatedEntryFromSlotParams(ctx, slotEntryAllIDList, entryIDList, slotParams)
	} else {
		entryIDList = GetRelatedEntryFromTreeSlot(ctx, slotEntryAllIDList, entryIDList)
	}
	if len(slotIDList) > 0 {
		log.InfoContextf(ctx, "createTaskFlow|slotIDList:+%v", slotIDList)
		if err = updateIntentSlotAssociation(ctx, tx, taskFlow.IntentID, slotIDList); err != nil {
			log.ErrorContextf(ctx, "createTaskFlow,updateIntentSlotAssociation err:%v", err)
			return err
		}
	}
	if len(entryIDList) > 0 {
		log.InfoContextf(ctx, "createTaskFlow|updateIntentEntryAssociation|entryIDList:+%v", entryIDList)
		if err = updateIntentEntryAssociation(ctx, tx, taskFlow.IntentID, entryIDList); err != nil {
			log.ErrorContextf(ctx, "createTaskFlow,updateIntentEntryAssociation err:%v", err)
			return err
		}
	}
	if len(varParamsIDList) > 0 {
		log.InfoContextf(ctx, "createTaskFlow|varParamsIDList:+%v", varParamsIDList)
		err = updateIntentVarParamsAssociation(ctx, tx, taskFlow.IntentID, varParamsIDList)
		if err != nil {
			log.ErrorContextf(ctx, "createTaskFlow,updateIntentVarParamsAssociation err:%v", err)
			return err
		}
	}
	return nil
}

// GetRelatedEntryFromTreeSlot 从任务流程的条件实体中获取全选词条
func GetRelatedEntryFromTreeSlot(ctx context.Context, slotEntryAllIDList []string, entryIDList []string) []string {
	// 处理词条,根据收集的条件中值为选择词条和引用实体，且为全部类型的实体集合查询词条集合
	log.InfoContextf(ctx, "GetRelatedEntryFromTreeSlot|entryIDList1 %+v", entryIDList)
	log.InfoContextf(ctx, "GetRelatedEntryFromTreeSlot|slotEntryAllIDList %+v", slotEntryAllIDList)
	if len(slotEntryAllIDList) > 0 {
		entryInfos, err := BatchGetEntryListBySlotIDList(ctx, slotEntryAllIDList)
		log.InfoContextf(ctx, "GetRelatedEntryFromTreeSlot|entryInfos %+v", entryInfos)
		if err != nil {
			log.ErrorContextf(ctx, "GetRelatedEntryFromTreeSlot BatchGetEntryListBySlotIDList err:%+v", err)
			return entryIDList
		}
		if len(entryInfos) > 0 {
			for _, entry := range entryInfos {
				entryIDList = append(entryIDList, entry.EntryID)
			}
		}
		// 去重复
		log.InfoContextf(ctx, "GetRelatedEntryFromTreeSlot|entryIDList2 %+v", entryIDList)
		entryIDList = set.RemoveDuplicatesAndEmpty(entryIDList)
	}
	log.InfoContextf(ctx, "GetRelatedEntryFromTreeSlot|entryIDList len:%d", len(entryIDList))
	return entryIDList
}

// GetRelatedEntryFromSlotParams 导入时从导入数据中获取词槽对应的全部词条
func GetRelatedEntryFromSlotParams(ctx context.Context, slotEntryAllIDList []string, entryIDList []string,
	slotParams []*entity.SlotMigrationInfo) []string {
	log.InfoContextf(ctx, "GetRelatedEntryFromSlotParams|slotEntryAllIDList %+v", slotEntryAllIDList)
	log.InfoContextf(ctx, "GetRelatedEntryFromSlotParams|slotParams %+v", slotParams)
	log.InfoContextf(ctx, "GetRelatedEntryFromSlotParams|entryIDList %+v", entryIDList)
	// 处理词条,根据收集的条件中值为选择词条和引用实体，且为全部类型的实体集合查询词条集合
	for _, slotID := range slotEntryAllIDList {
		for _, slot := range slotParams {
			if slotID == slot.SlotID {
				for _, entity := range slot.EntityInfo {
					for _, entry := range entity.Entries {
						entryIDList = append(entryIDList, entry.EntryID)
					}
				}
			}
		}
	}
	entryIDList = set.RemoveDuplicatesAndEmpty(entryIDList)
	log.InfoContextf(ctx, "GetRelatedEntryFromSlotParams|entryIDList len:%+v", entryIDList)
	return entryIDList
}

// saveIntentTaskFlow 创建意图与任务流程关联关系
func saveIntentTaskFlow(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) error {
	intentFlow := &entity.IntentFlow{
		FlowID:        params.FlowID,
		IntentID:      params.IntentID,
		ReleaseStatus: params.ReleaseStatus,
		Action:        params.Action,
	}
	if err := tx.Table(intentFlow.TableName()).Create(intentFlow).Error; err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,Create IntentFlow err:%v", err)
		return err
	}
	return nil
}

// saveRobotIntent 创建机器人与意图关联关系
func saveRobotIntent(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) error {
	robotIntent := &entity.RobotIntent{
		RobotID:       params.RobotId,
		IntentID:      params.IntentID,
		ReleaseStatus: params.ReleaseStatus,
		Action:        params.Action,
	}
	if err := tx.Table(robotIntent.TableName()).Create(robotIntent).Error; err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,Create RobotIntent err:%v", err)
		return err
	}
	return nil
}

// updateRobotIntent 更新机器人与意图关联关系
func updateRobotIntent(ctx context.Context, tx *gorm.DB, robotId string, newTaskFlow entity.TaskFlow) error {
	robotIntent := entity.RobotIntent{}
	result := tx.Table(robotIntent.TableName()).Where("f_robot_id=? AND f_intent_id=? ",
		robotId, newTaskFlow.IntentID).Updates(&entity.RobotIntent{
		ReleaseStatus: newTaskFlow.ReleaseStatus,
		Action:        newTaskFlow.Action,
	}).Find(&robotIntent)

	log.InfoContextf(ctx, "updateRobotIntent robotIntent:%+v", robotIntent)
	if result.RowsAffected <= 0 {
		log.ErrorContextf(ctx, "updateRobotIntent err: %s", utilsErrors.ErrIntentBoundRobot)
		return utilsErrors.ErrIntentBoundRobot
	}
	return nil
}

// saveIntentCorpus 创建意图语料，新建任务流程的时候创建一次
func saveIntentCorpus(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) error {
	corpus := &entity.Corpus{
		CorpusID:      params.CorpusID,
		Content:       params.IntentName, // 预料内容和意图名称及任务流程名称相同(已经确认)
		IntentID:      params.IntentID,
		Uin:           params.Uin,
		SubUin:        params.SubUin,
		ReleaseStatus: params.ReleaseStatus,
		Action:        params.Action,
	}
	if err := tx.Table(corpus.TableName()).Create(corpus).Error; err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,Create Corpus err:%v", err)
		return err
	}
	return nil
}

// updateIntentCorpus 更新意图语料
func updateIntentCorpus(ctx context.Context, tx *gorm.DB, newTaskFlow entity.TaskFlow) error {
	corpus := &entity.Corpus{
		Content:       newTaskFlow.IntentName,
		ReleaseStatus: newTaskFlow.ReleaseStatus,
		Action:        newTaskFlow.Action,
	}
	if err := tx.Table(corpus.TableName()).Where("f_intent_id=? ",
		newTaskFlow.IntentID).Updates(corpus).Error; err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow, Update Corpus err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "updateIntentCorpus success")
	return nil
}

// saveTaskFlow 创建任务流程
func saveTaskFlow(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) (*entity.TaskFlow, error) {
	taskFlow := &entity.TaskFlow{
		FlowID:          params.FlowID,
		IntentName:      params.IntentName,
		IntentDesc:      params.IntentDesc,
		IntentID:        params.IntentID,
		FlowState:       params.FlowState,
		FlowType:        params.FlowType,
		Version:         params.Version,
		CategoryID:      params.CategoryID,
		DialogJsonDraft: params.DialogJsonDraft,
		Uin:             params.Uin,
		SubUin:          params.SubUin,
		ReleaseStatus:   params.ReleaseStatus,
		Action:          params.Action,
		ProtoVersion:    int32(KEP.TaskFlowProtoVersion_V2_4),
	}
	if err := tx.Table(taskFlow.TableName()).Create(taskFlow).Error; err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,Create TaskFlow err:%v", err)
		return nil, err
	}
	return taskFlow, nil
}

// saveIntent 创建意图
func saveIntent(ctx context.Context, tx *gorm.DB, params entity.CreateTaskFlowParams) error {
	intent := &entity.Intent{
		IntentID:      params.IntentID,
		IntentName:    params.IntentName,
		IntentDesc:    params.IntentDesc,
		IntentType:    params.IntentType,
		Source:        params.IntentSource,
		Uin:           params.Uin,
		SubUin:        params.SubUin,
		ReleaseStatus: params.ReleaseStatus,
		Action:        params.Action,
	}
	if err := tx.Table(intent.TableName()).Create(intent).Error; err != nil {
		log.ErrorContextf(ctx, "createTaskFlow,CreateIntent err:%v", err)
		return err
	}
	return nil
}

// updateIntent 更新意图
func updateIntent(ctx context.Context, tx *gorm.DB, newTaskFlow entity.TaskFlow) error {
	intent := &entity.Intent{
		IntentName:    newTaskFlow.IntentName,
		Action:        newTaskFlow.Action,
		ReleaseStatus: newTaskFlow.ReleaseStatus,
		IntentDesc:    newTaskFlow.IntentDesc,
	}
	if err := tx.Table(intent.TableName()).Where("f_intent_id=? ",
		newTaskFlow.IntentID).Updates(intent).Error; err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow, Update Intent err:%v", err)
		return err
	}
	return nil
}

// GetTaskFlowByFlowIDs 通过任务流程ID获取任务流程信息
func GetTaskFlowByFlowIDs(ctx context.Context, robotID string, flowIDs []string) (map[string]*entity.TaskFlow,
	error) {
	mapTaskFlow := make(map[string]*entity.TaskFlow)
	if len(flowIDs) == 0 {
		return mapTaskFlow, nil
	}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		var txErr error
		mapTaskFlow, txErr = GetTaskFlowByFlowIDsWithTx(ctx, tx, robotID, flowIDs)
		return txErr
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowByFlowIDs failed,robotID:%s,flowIDs:%+v,err:%+v", robotID,
			flowIDs, err)
		return nil, err
	}
	return mapTaskFlow, nil
}

// GetTaskFlowByFlowIDsWithTx 通过任务流程ID获取任务流程信息 -- 事物
func GetTaskFlowByFlowIDsWithTx(ctx context.Context, tx *gorm.DB, robotID string, flowIDs []string) (
	map[string]*entity.TaskFlow, error) {
	var taskFlows []*entity.TaskFlow
	mapTaskFlow := make(map[string]*entity.TaskFlow)
	if len(flowIDs) == 0 {
		return mapTaskFlow, nil
	}
	join := fmt.Sprintf("INNER JOIN %s ON %s.f_intent_id = %s.f_intent_id", tableRobotIntent, tableTaskFlow,
		tableRobotIntent)
	err := tx.Table(tableTaskFlow).
		Joins(join).
		Where(fmt.Sprintf("%s.f_robot_id = ?", tableRobotIntent), robotID).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableTaskFlow)).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableRobotIntent)).
		Where(fmt.Sprintf("%s.f_flow_id IN ?", tableTaskFlow), flowIDs).
		Find(&taskFlows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowByFlowIDsWithTx failed,robotID:%s,flowIDs:%+v,err:%+v", robotID,
			flowIDs, err)
		return nil, err
	}
	for _, v := range taskFlows {
		mapTaskFlow[v.FlowID] = v
	}
	return mapTaskFlow, nil
}

// GetTaskFlowsByRobotID 通过机器人ID获取流程信息
func GetTaskFlowsByRobotID(ctx context.Context, robotID string) ([]*entity.TaskFlow, error) {
	var taskFlows []*entity.TaskFlow
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	join := fmt.Sprintf("INNER JOIN %s ON %s.f_intent_id = %s.f_intent_id", tableRobotIntent, tableTaskFlow,
		tableRobotIntent)
	err := db.Table(tableTaskFlow).
		Joins(join).
		Where(fmt.Sprintf("%s.f_robot_id = ?", tableRobotIntent), robotID).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableTaskFlow)).
		Where(fmt.Sprintf("%s.f_is_deleted = 0", tableRobotIntent)).
		Find(&taskFlows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowsByRobotID failed,robotID:%s,err:%+v", robotID, err)
		return nil, err
	}
	return taskFlows, nil
}

// CreateExportTaskFlow 创建导出任务流
func CreateExportTaskFlow(ctx context.Context, corpID, staffID uint64, robotID string, flowIDs []string) error {
	// 添加任务到任务队列
	params := entity.TaskFlowExportParams{
		RequestID: util.RequestID(ctx),
		CorpID:    corpID,
		StaffID:   staffID,
		RobotID:   robotID,
		ExportID:  uint64(idgenerator.NewInt64ID()),
		FlowIDs:   flowIDs,
	}
	if err := scheduler.NewExportTaskFlowTask(ctx, robotID, params); err != nil {
		return err
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(robotID)),
		PageId:       entity.NoticeTaskFlowPageID,
		Type:         entity.NoticeTypeTaskFlowExport,
		Level:        entity.LevelInfo,
		RelateId:     params.ExportID,
		Content:      entity.TaskFlowExportNoticeContentIng,
		IsGlobal:     false,
		IsAllowClose: false,
		CorpId:       corpID,
		StaffId:      staffID,
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// InnerCreateExportTaskFlow 创建内部平台导出任务流
func InnerCreateExportTaskFlow(ctx context.Context, corpID, staffID, exportId uint64, robotID,
	platform string, flowIDs []string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	// 添加任务到任务队列
	params := entity.TaskFlowExportParams{
		RequestID: util.RequestID(ctx),
		CorpID:    corpID,
		StaffID:   staffID,
		RobotID:   robotID,
		ExportID:  exportId,
		FlowIDs:   flowIDs,
		Platform:  platform,
	}
	if err := scheduler.NewExportTaskFlowTask(ctx, robotID, params); err != nil {
		return err
	}
	// 创建导出记录
	exportParam := &entity.ExportFile{
		ExportID: exportId,
		AppID:    robotID,
		Params:   json0.Marshal2StringNoErr(params),
		Status:   entity.ExportTaskStatusStart,
		Type:     entity.ExportTaskTypeFlow,
		Platform: platform,
	}
	if err := db.Table(entity.ExportFile{}.TableName()).Create(exportParam).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|ExportFile|Create:%+v|err:%+v", sid, exportParam, err)
		return err
	}

	return nil
}

// CreateTaskFlowImport 创建导入任务流程
func CreateTaskFlowImport(ctx context.Context, corpID, staffID uint64, robotID, fileName string,
	parentTask *entity.TaskFlowImport, tasks []*entity.TaskFlowImport) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(tableTaskFlowImport).Create(parentTask).Error; err != nil {
			return err
		}
		for _, v := range tasks {
			if err := tx.Table(tableTaskFlowImport).Create(v).Error; err != nil {
				return err
			}
		}
		taskParams := entity.TaskFlowImportParentParams{
			RequestID: util.RequestID(ctx),
			CorpID:    corpID,
			StaffID:   staffID,
			RobotID:   robotID,
			ImportID:  parentTask.ImportID,
			FileName:  fileName,
		}
		// 创建父任务调度
		if err := scheduler.NewImportTaskFlowParentTask(ctx, robotID, taskParams); err != nil {
			return err
		}
		// 创建导入任务通知
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId:     uint64(encode.StringToInt64(robotID)),
			PageId:       entity.NoticeTaskFlowPageID,
			Type:         entity.NoticeTypeTaskFlowImport,
			Level:        entity.LevelInfo,
			RelateId:     uint64(encode.StringToInt64(parentTask.ImportID)),
			Content:      fmt.Sprintf(entity.TaskFlowImportNoticeContentIng, fileName),
			IsGlobal:     false,
			IsAllowClose: false,
			CorpId:       corpID,
			StaffId:      staffID,
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建任务流程导入import失败 err:%+v", err)
		return err
	}
	return nil
}

// CreateTaskFlowImportSub 创建导入任务流程子任务调度
func CreateTaskFlowImportSub(ctx context.Context, corpID, staffID uint64, robotID, fileName, parentImportID,
	importID string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		taskFlowImport := &entity.TaskFlowImport{
			Status: entity.FlowImportStatusProcessing,
		}
		err := tx.Table(tableTaskFlowImport).
			Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
			Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.Status), entity.FlowImportStatusWait).
			Updates(taskFlowImport).Error
		if err != nil {
			log.ErrorContextf(ctx, "CreateTaskFlowImportSub update import status failed,importID:%s,err:%+v",
				importID, err)
			return err
		}
		params := entity.TaskFlowImportParams{
			RequestID:      util.RequestID(ctx),
			CorpID:         corpID,
			StaffID:        staffID,
			RobotID:        robotID,
			ParentImportID: parentImportID,
			ImportID:       importID,
			FileName:       fileName,
		}
		if err := scheduler.NewImportTaskFlowTask(ctx, robotID, params); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建任务流程导入import子任务失败 err:%+v", err)
		return err
	}
	return nil
}

// GetTaskFlowImportByID 通过导入ID获取任务导入信息
func GetTaskFlowImportByID(ctx context.Context, importID string) (*entity.TaskFlowImport, error) {
	taskFlowImport := &entity.TaskFlowImport{}
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Find(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowImportByID failed,importID:%s,err:%+v", importID, err)
		return nil, err
	}
	return taskFlowImport, nil
}

// GetTaskFlowImportByParentID 通过父导入ID获取子导入任务信息
func GetTaskFlowImportByParentID(ctx context.Context, parentImportID string) ([]*entity.TaskFlowImport, error) {
	var taskFlowImports []*entity.TaskFlowImport
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ParentImportID), parentImportID).
		Find(&taskFlowImports).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowImportByParentID failed,parentImportID:%s,err:%+v",
			parentImportID, err)
		return nil, err
	}
	return taskFlowImports, nil
}

// UpdateTaskFlowImportStatus 更新任务流程导入状态
func UpdateTaskFlowImportStatus(ctx context.Context, importID string, oldStatus, status uint32) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	taskFlowImport := &entity.TaskFlowImport{
		Status: status,
	}
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.Status), oldStatus).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateTaskFlowImportStatus failed,importID:%s,oldStatus:%d,status:%d,err:%+v",
			importID, oldStatus, status, err)
		return err
	}
	return nil
}

// updateTaskFlowImportFinalParams 更新任务流程最终参数
func updateTaskFlowImportFinalParams(ctx context.Context, tx *gorm.DB, importID string,
	finalParams string) error {
	taskFlowImport := &entity.TaskFlowImport{
		FinalParams: finalParams,
	}
	err := tx.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "updateTaskFlowImportFinalParams failed,importID:%s,finalParams:%s,err:%+v",
			importID, finalParams, err)
		return err
	}
	return nil
}

// GetTaskFlowAvailableImports ...
//
//	@Description: 获取正在生效的调度任务(待处理｜处理中的任务)
//	@param ctx
//	@param updateBeforeTime        更新时间点
//	@return []*entity.TaskFlowImport
//	@return error
func GetTaskFlowAvailableImports(ctx context.Context, updateBeforeTime time.Time) ([]*entity.TaskFlowImport, error) {
	// 任务流导入任务会写 t_task_flow_import 表
	// 当前生效的任务包括：待处理｜处理中的任务

	log.InfoContextf(ctx, "GetTaskFlowAvailableImports, updateBeforeTime:%s", updateBeforeTime.String())
	tasks := make([]*entity.TaskFlowImport, 0)
	if updateBeforeTime.IsZero() {
		return tasks, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? and %s < ?",
		entity.TTaskFlowImportColumns.Status, entity.TTaskFlowImportColumns.UpdateTime)
	err := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.TaskFlowImport{}.TableName()).
		Where(query, []int{entity.FlowImportStatusWait, entity.FlowImportStatusProcessing}, updateBeforeTime).
		Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowAvailableImports db.Find err;%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetTaskFlowAvailableImports success, len(tasks):%d", len(tasks))
	return tasks, nil
}

// ImportTaskFlow 导入任务流程
func ImportTaskFlow(ctx context.Context, importParamData *entity.TaskFlowImportParamsData,
	varParams []*entity.VarParams, slotParams []*entity.SlotMigrationInfo, intentCorpusParams []*entity.IntentCorpus,
	createParams *entity.CreateTaskFlowParams, robotID, importID string) error {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 创建所需的变量
		if err := createImportVarParams(ctx, tx, robotID, varParams); err != nil {
			log.ErrorContextf(ctx, "sid:%s|createImportVarParams|err:%+v", sid, err)
			return err
		}

		// 创建所需的示例问法
		if err := createImportIntentCorpusExamples(ctx, tx, robotID, intentCorpusParams); err != nil {
			return err
		}
		// 创建所需槽位
		if err := createImportSlotInfos(ctx, tx, robotID, importParamData.Uin, importParamData.SubUin,
			slotParams); err != nil {
			return err
		}
		// 创建任务流程
		if err := createTaskFlow(ctx, tx, *createParams, entity.OperateTypeImport, slotParams); err != nil {
			return err
		}
		// 更新导入后的数据
		finalParams, _ := jsoniter.MarshalToString(importParamData)
		if err := updateTaskFlowImportFinalParams(ctx, tx, importID, finalParams); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "CreateTaskFlow fail, err:%+v", err)
		return err
	}
	return nil
}

// UpdateEntryImportFinalParams 更新导入词条流程最终参数
func UpdateEntryImportFinalParams(ctx context.Context, importID string,
	finalParams string) error {
	sid := util.RequestID(ctx)

	taskFlowImport := &entity.TaskFlowImport{
		FinalParams: finalParams,
	}

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntryImportFinalParams failed,sid:%s,importID:%s,finalParams:%s,err:%+v",
			sid, importID, finalParams, err)
		return err
	}
	return nil
}

// UpdateWFEntryImportFinalParams 工作流-更新导入词条流程最终参数
func UpdateWFEntryImportFinalParams(ctx context.Context, importID string,
	finalParams string) error {

	taskFlowImport := &entity.TaskFlowImport{
		FinalParams: finalParams,
	}

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWFEntryImportFinalParams Failed!,importID:%s,finalParams:%s,err:%+v",
			importID, finalParams, err)
		return err
	}
	return nil
}

// ImportTaskFlowCategory 导入任务流程分类
func ImportTaskFlowCategory(ctx context.Context, categories []*category.Category, importID,
	finalParams string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 批量创建分类
		if err := CreateCategoriesByTx(ctx, tx, categories); err != nil {
			return err
		}
		// 更新导入后的数据
		if err := updateTaskFlowImportFinalParams(ctx, tx, importID, finalParams); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "ImportTaskFlowCategory fail, err:%+v", err)
		return err
	}
	return nil
}

// UpdateTaskFlowImportMessage 更新任务流程错误信息
func UpdateTaskFlowImportMessage(ctx context.Context, importID, message string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	taskFlowImport := &entity.TaskFlowImport{
		Message: message,
	}
	err := db.Table(tableTaskFlowImport).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateTaskFlowImportMessage failed,importID:%s,message:%s,err:%+v",
			importID, message, err)
		return err
	}
	return nil
}

// TxUpdateTaskFlowCopyCount 更新任务流复制次数
func TxUpdateTaskFlowCopyCount(ctx context.Context, tx *gorm.DB, taskFlow *entity.TaskFlow, copyCount int) error {
	err := tx.Table(tableTaskFlow).
		Where(fmt.Sprintf("%s = ?", entity.TTaskFlowColumns.FlowID), taskFlow.FlowID).
		Updates(map[string]interface{}{
			entity.TTaskFlowColumns.CopyCount: copyCount,
			// 更新时间不变，保持原来的时间
			entity.TTaskFlowColumns.UpdateTime: taskFlow.UpdateTime,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "TxUpdateTaskFlowCopyCount Failed, err:%v", err)
		return err
	}
	return nil
}

// txUpdateTaskFlowByFlowInfo 通过任务流信息更新t_task_flow表
func txUpdateTaskFlowByFlowInfo(ctx context.Context, tx *gorm.DB, flowInfos []TaskFlowInfo) error {
	sid := util.RequestID(ctx)
	for _, taskFlowInfo := range flowInfos {
		if taskFlowInfo.TaskFlowDeleted != 0 {
			continue
		}
		action := CalcActionByFlowReleaseStatus([]TaskFlowInfo{taskFlowInfo})
		finalFlowTaskStatus := getTaskFlowStatus(taskFlowInfo.TaskReleaseStatus, taskFlowInfo.TaskFlowStatus)
		err := tx.Model(&entity.TaskFlow{}).
			Where("f_flow_id = ?", taskFlowInfo.TaskFlowID).
			Updates(map[string]interface{}{
				"f_action":         action,
				"f_release_status": entity.ReleaseStatusUnPublished,
				"f_flow_state":     finalFlowTaskStatus,
				"f_update_time":    time.Now(),
			}).Error
		if err != nil {
			log.Errorf("TXUpdateTaskFlowByFlowInfo Failed! |%s|%s", sid, err)
			return err
		}
	}
	return nil
}
