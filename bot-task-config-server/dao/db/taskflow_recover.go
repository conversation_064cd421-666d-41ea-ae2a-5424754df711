// bot-task-config-server
//
// @(#)taskflow_recover.go  星期四, 三月 14, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idget"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

// RecoverRelatedData 恢复历史状态时，恢复槽位、词条、变量等数据
func RecoverRelatedData(ctx context.Context, robotId string, nowTaskFlowAction string,
	tfph *entity.TaskFlowPublishHistory) (*entity.TaskFlowPublishHistory, string, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	_, subUin := util.GetUinAndSubAccountUin(ctx)
	var slotIDs, varParamIDs []string
	var msg string

	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		tfph, msg, slotIDs, varParamIDs, err = recoverTaskFlowRelatedData(ctx, tx, robotId, tfph)
		if err != nil {
			log.ErrorContextf(ctx, "RecoverRelatedData｜recoverTaskFlowRelatedData err:%s|%v", sid, err)
			return err
		}
		if len(slotIDs) > 0 {
			// 恢复历史绑定槽位
			if err := updateIntentSlotAssociation(ctx, tx, tfph.IntentID, slotIDs); err != nil {
				log.ErrorContextf(ctx, "RecoverRelatedData|"+
					"updateIntentSlotAssociation|%s|err:%+v", sid, err)
				return err
			}
		}

		if len(varParamIDs) > 0 {
			// 恢复 变量的绑定关系
			if err = updateIntentVarParamsAssociation(ctx, tx, tfph.IntentID, varParamIDs); err != nil {
				log.ErrorContextf(ctx, "sid:%s|RecoverRelatedData,updateIntentVarParamsAssociation err:%v", sid, err)
				return err
			}
		}

		// 恢复示例问法绑定关系
		if err := recoverRelatedIntentExamples(ctx, tx, robotId, tfph.IntentID, tfph.Version); err != nil {
			log.ErrorContextf(ctx, "RecoverRelatedData|"+
				"updateIntentSlotAssociation|%s|err:%+v", sid, err)
			return err
		}

		// 更新草稿
		if err = tx.Table(entity.TaskFlow{}.TableName()).
			Where("f_flow_id=? AND f_is_deleted=?", tfph.FlowID, entity.TaskFlowUnDeleted).
			Where("f_intent_id IN (SELECT f_intent_id FROM t_robot_intent WHERE f_robot_id = ?)", robotId).
			Updates(map[string]interface{}{
				"f_dialog_json_draft": tfph.FlowJson, "f_sub_uin": subUin,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "RecoverRelatedData｜update taskflow err:%s|%v", sid, err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "sid:%s|RecoverHistoryTaskFlow,"+
			"recoverTaskFlowRelatedData,err:%s", sid, err.Error())
		return tfph, "", err
	}
	return tfph, msg, nil
}

// recoverTaskFlowRelatedData 恢复历史状态时，恢复槽位、词条、 变量
func recoverTaskFlowRelatedData(ctx context.Context, tx *gorm.DB, robotId string,
	tfph *entity.TaskFlowPublishHistory) (*entity.TaskFlowPublishHistory, string, []string, []string, error) {
	var duplicateSlots *[]entity.Slot
	var duplicateEntities *[]entity.Entity
	var duplicateVars *[]entity.VarParams
	var msg string
	sid := util.RequestID(ctx)

	// 画布JSON正确性初步校验
	//log.InfoContextf(ctx, "sid:%s|RecoverHistoryTaskFlow|JsonToTaskFlow:%s", sid, tfph.FlowJson)
	tree, err := protoutil.JsonToTaskFlowForPreCheck(tfph.FlowJson)
	if err != nil {
		log.ErrorContextf(ctx, "RecoverHistoryTaskFlow,JsonToTaskFlow err:%s|%s", sid, err.Error())
		return tfph, "", nil, nil, err
	}

	// 获取关联的槽位,变量
	slotIDList, varParamsIDList, _, _ := idget.GetAllRelatedFromTree(ctx, tree)
	log.InfoContextf(ctx, "RecoverHistoryTaskFlow|slotIDList,len:%d|varParamsIDList,len:%d",
		len(slotIDList), len(varParamsIDList))
	// 务流JSON，槽位信息/词条等是否可用（用于恢复历史版本）,slot有效性在后面校验 checkSlot中进行
	duplicateSlots, duplicateEntities, duplicateVars, err = recoverTaskFlowRelatedDataTx(ctx, tx,
		robotId, slotIDList, varParamsIDList)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|RecoverHistoryTaskFlow,"+
			"recoverTaskFlowRelatedData,err:%s", sid, err.Error())
		return tfph, "", nil, nil, err
	}

	if duplicateSlots != nil && len(*duplicateSlots) > 0 {
		msg = "重复的Slot:"
		for i, item := range *duplicateSlots {
			if i == 0 {
				msg += item.SlotName + ":" + item.SlotID
			}
			msg += "," + item.SlotID
		}
	}
	if duplicateEntities != nil && len(*duplicateEntities) > 0 {
		msg += "\n;重复的Entities:"
		for i, item := range *duplicateEntities {
			if i == 0 {
				msg += item.EntityName + ":" + item.EntityID
			}
			msg += "," + item.EntityID
		}
	}
	if duplicateVars != nil && len(*duplicateVars) > 0 {
		msg += "\n;重复的变量Vars:"
		for i, item := range *duplicateVars {
			if i == 0 {
				msg += item.VarName + ":" + item.VarID
			}
			msg += "," + item.VarID
		}
	}

	return tfph, msg, slotIDList, varParamsIDList, nil
}

// recoverRelatedVarParamsTx 恢复关联的变量
func recoverRelatedVarParamsTx(ctx context.Context,
	tx *gorm.DB, appId string, varParamsIDs []string) (*[]entity.VarParams, error) {
	sid := util.RequestID(ctx)
	var duplicateVars []entity.VarParams // 重复的变量

	// 1. 恢复变量
	// 查询 t_var 表中名称重复的变量
	if err := tx.Table(entity.VarParams{}.TableName()).
		Select("f_var_name, COUNT(*)").Where("f_app_id=?", appId).
		Group("f_var_name").Having("COUNT(*) > 1").
		Find(&duplicateVars).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover Var|recoverRelatedVarParamsTx error:%+v", sid, err)
		return &duplicateVars, err
	}

	// 构造名称重复的变量名列表
	var duplicateVarNames []string
	if len(duplicateVars) > 0 {
		for _, v := range duplicateVars {
			duplicateVarNames = append(duplicateVarNames, v.VarName)
		}
	} else {
		duplicateVarNames = append(duplicateVarNames, "")
	}

	var upInsertVars []entity.VarParams
	var updateVars []entity.VarParams
	// 查询不重复的变量，并更新 Action (历史发布的画布的变量都发布过，因此统一用update)
	if err := tx.Table(entity.VarParams{}.TableName()).
		Where("f_app_id=? AND f_var_id IN ? AND  f_var_name NOT IN ?",
			appId, varParamsIDs, duplicateVarNames).Find(&upInsertVars).
		Where("f_is_deleted != 0 AND f_action IN ?",
			[]string{entity.ActionInsert, entity.ActionUpdate, entity.ActionDelete}).
		Updates(map[string]interface{}{
			"f_action":         entity.ActionUpdate,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_is_deleted":     0}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover VarParams error", sid, err.Error())
		return &duplicateVars, err
	}
	updateVars = append(updateVars, upInsertVars...)

	variables := make([]*KEP_WF_DM.Var, 0)
	for _, v := range updateVars {
		variables = append(variables, &KEP_WF_DM.Var{VarID: v.VarID, VarName: v.VarName,
			ValueType: KEP_WF.TypeEnum(KEP_WF.TypeEnum_value[v.VarType]), VarDefaultValue: v.VarDefaultValue})
	}
	// 恢复变量，通知DM
	err := UpsertVarParamsToDM(ctx, appId, variables)
	if err != nil {
		log.ErrorContextf(ctx, "recoverRelatedVarParamsTx.UpsertVariablesToSandbox Failed! "+
			"|sid:%s|err:%+v", sid, err)
		return &duplicateVars, err
	}
	return &duplicateVars, nil

}

// recoverTaskFlowRelatedDataTx 恢复词槽、实体、变量
func recoverTaskFlowRelatedDataTx(ctx context.Context,
	tx *gorm.DB, robotId string, slotIDs, varParamsIDs []string) (*[]entity.Slot,
	*[]entity.Entity, *[]entity.VarParams, error) {
	sid := util.RequestID(ctx)
	var duplicateSlots *[]entity.Slot
	var duplicateEntities *[]entity.Entity
	var duplicateVars *[]entity.VarParams
	// 恢复词槽、实体
	duplicateSlots, duplicateEntities, err := recoverRelatedSlotsAndEntitiesTx(ctx, tx, robotId, slotIDs)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|recoverRelatedSlotsAndEntitiesTx|err:%+v", sid, err)
		return duplicateSlots, duplicateEntities, duplicateVars, err
	}

	// 恢复 变量
	duplicateVars, err = recoverRelatedVarParamsTx(ctx, tx, robotId, varParamsIDs)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|recoverRelatedVarParamsTx|err:%+v", sid, err)
		return duplicateSlots, duplicateEntities, duplicateVars, err
	}

	return duplicateSlots, duplicateEntities, duplicateVars, nil
}

// recoverRelatedSlotsAndEntitiesTx 恢复词槽、实体
func recoverRelatedSlotsAndEntitiesTx(ctx context.Context, tx *gorm.DB,
	robotId string, slotIDs []string) (*[]entity.Slot, *[]entity.Entity, error) {
	sid := util.RequestID(ctx)
	var duplicateSlots []entity.Slot
	var duplicateEntities []entity.Entity

	// 恢复槽位：查询 Slot 表中名称重复的槽位
	if err := tx.Table(entity.Slot{}.TableName()).Select("f_slot_name, COUNT(*)").
		Where("f_robot_id=?", robotId).Group("f_slot_name").
		Having("COUNT(*) > 1").Find(&duplicateSlots).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover Slot error:%v", sid, err)
		return &duplicateSlots, &duplicateEntities, err
	}
	var duplicateSlotNames []string
	if len(duplicateSlots) > 0 {
		for _, slot := range duplicateSlots {
			duplicateSlotNames = append(duplicateSlotNames, slot.SlotName)
		}
	} else {
		duplicateSlotNames = append(duplicateSlotNames, "")
	}

	var upInsertSlots, updateSlots []entity.Slot
	// 查询不重复的槽位，并更新 Action (历史发布的画布的词槽都发布过，因此统一用update)
	if err := tx.Table(entity.Slot{}.TableName()).
		Where("f_robot_id=? AND f_slot_id IN ? AND  f_slot_name NOT IN ?",
			robotId, slotIDs, duplicateSlotNames).Find(&upInsertSlots).
		Where("f_is_deleted != 0").Updates(map[string]interface{}{
		"f_action": entity.ActionUpdate, "f_release_status": entity.ReleaseStatusUnPublished,
		"f_is_deleted": 0}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover Slot error", sid, err.Error())
		return &duplicateSlots, &duplicateEntities, err
	}
	updateSlots = append(updateSlots, upInsertSlots...)

	// 2. 恢复槽位关联的实体表t_slot_entity, 并获取实体
	var slotEntities []entity.SlotEntity
	if err := tx.Table(entity.SlotEntity{}.TableName()).Where("f_slot_id IN ?", slotIDs).
		Find(&slotEntities).Where("f_is_deleted != 0").Updates(map[string]interface{}{
		"f_action": entity.ActionUpdate, "f_release_status": entity.ReleaseStatusUnPublished,
		"f_is_deleted": 0}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover SlotEntity error", sid, err.Error())
		return &duplicateSlots, &duplicateEntities, err
	}
	// 恢复槽位，通知DM
	if err := upsertSlotsToSandbox(ctx, robotId, updateSlots, slotEntities); err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover upsertSlotsToSandbox|err:%+v", sid, err)
		return &duplicateSlots, &duplicateEntities, err
	}
	// 2. 恢复实体,获取实体IDs
	var entitiesIds []string
	for _, item := range slotEntities {
		entitiesIds = append(entitiesIds, item.EntityID)
	}
	// 查询 Entity 表中名称重复的实体
	if err := tx.Table(entity.Entity{}.TableName()).Select("f_entity_name, COUNT(*)").
		Where("f_robot_id=?", robotId).Group("f_entity_name").
		Having("COUNT(*) > 1").Find(&duplicateEntities).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|Recover Entity|err:%+v", sid, err)
		return &duplicateSlots, &duplicateEntities, err
	}
	// 构造名称重复的槽位名称列表
	var duplicateEntitiesNames []string
	if len(duplicateEntities) > 0 {
		for _, e := range duplicateEntities {
			duplicateEntitiesNames = append(duplicateEntitiesNames, e.EntityName)
		}
	} else {
		duplicateEntitiesNames = append(duplicateEntitiesNames, "")
	}

	// 5. 查询并恢复实体
	if err := tx.Table(entity.Entity{}.TableName()).
		Where("f_level_type=? AND f_robot_id=? AND f_is_deleted !=0 AND f_entity_id IN ? "+
			"AND f_entity_name NOT IN ? ", entity.SlotLevelBOT, robotId, entitiesIds,
			duplicateEntitiesNames).Updates(map[string]interface{}{"f_action": entity.ActionUpdate,
		"f_release_status": entity.ReleaseStatusUnPublished, "f_is_deleted": 0}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|recover Entity:err:%+v", sid, err)
		return &duplicateSlots, &duplicateEntities, err
	}
	// 6. 查询t_entry 并恢复实体下的词条，词条不恢复（产品确定：每个版本也可能会词条有所增减，不做恢复）
	return &duplicateSlots, &duplicateEntities, nil
}

// upsertSlotsToSandbox 更新通知DM
func upsertSlotsToSandbox(ctx context.Context, robotId string,
	updateSlots []entity.Slot, slotEntities []entity.SlotEntity) error {
	slots := make([]*KEP_DM.SlotMain, 0)

	slotEntityIDsMap := make(map[string][]string)
	for _, slotEntity := range slotEntities {
		slotEntityIDsMap[slotEntity.SlotID] = append(slotEntityIDsMap[slotEntity.SlotID], slotEntity.EntityID)
	}

	// 恢复槽位，需要通知dm(如果保存测试环境的时候通知了，这里应该不需要通知)
	for _, slotInfo := range updateSlots {
		// 槽位示例
		var slotExamples []string
		err := jsoniter.Unmarshal([]byte(slotInfo.Examples), &slotExamples)
		if err != nil {
			log.ErrorContextf(ctx, "Unmarshal Failed! data:%+v,err:%v", slotInfo.Examples, err)
			return err
		}
		slots = append(slots, &KEP_DM.SlotMain{
			SlotID:      slotInfo.SlotID,
			SlotName:    slotInfo.SlotName,
			Description: slotInfo.SlotDesc,
			Examples:    slotExamples,
			EntityIDs:   slotEntityIDsMap[slotInfo.SlotID],
		})
	}

	req := &KEP_DM.UpsertSlotsToSandboxRequest{
		RobotID: robotId,
		Slots:   slots,
	}
	log.InfoContextf(ctx, "sid:%s|upsertSlotsToSandbox,req:%+v", util.RequestID(ctx), req)
	_, err := rpc.UpsertSlotsToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "txBatchCreateSlotEntityTable UpsertSlotsToSandbox Failed, err:%v", err)
		return err
	}
	return nil
}
