package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"gorm.io/gorm"
)

// SaveUpgradePublishedTaskFlowV24 保存更新发布的任务流
func SaveUpgradePublishedTaskFlowV24(ctx context.Context, id uint64, tfpjsonStr string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	if err := db.Table(entity.TaskFlowPublishHistory{}.TableName()).Where("f_id=?", id).Updates(map[string]interface{}{
		"f_proto_version": KEP.TaskFlowProtoVersion_V2_4,
		"f_flow_json":     tfpjsonStr,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveUpgradePublishedTaskFlowV24, err:%s|%s", sid, err)
		return err
	}
	return nil
}

// SaveUpgradeProdTaskFlowV24 ...
func SaveUpgradeProdTaskFlowV24(ctx context.Context, id uint64, tf entity.ModifyTaskFlowParams) error {
	db := database.GetLLMRobotTaskProdGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	if err := db.Table(entity.TaskFlow{}.TableName()).Where("f_id=?", id).Updates(map[string]interface{}{
		"f_proto_version":      KEP.TaskFlowProtoVersion_V2_4,
		"f_dialog_json_draft":  tf.DialogJsonDraft,
		"f_dialog_json_enable": tf.DialogJsonEnable,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveUpgradeProdTaskFlowV24, err:%s|%s", sid, err)
		return err
	}
	return nil
}

// FetchProdTaskFlowV24 ...
func FetchProdTaskFlowV24(ctx context.Context, flowId string) ([]*entity.TaskFlow, error) {
	db := database.GetLLMRobotTaskProdGORM().WithContext(ctx).Debug()
	var tfprod []*entity.TaskFlow
	sid := util.RequestID(ctx)
	if err := db.Table(entity.TaskFlow{}.TableName()).
		Where("f_flow_id=? AND f_is_deleted=0 AND f_proto_version=?",
			flowId, KEP.TaskFlowProtoVersion_V2_1).Find(&tfprod).Error; err != nil {
		log.ErrorContextf(ctx, "FetchProdTaskFlowV24, err:%s|%s", sid, err)
		return nil, err
	}
	return tfprod, nil
}

// FetchPublishedTaskFlowV24 ...
func FetchPublishedTaskFlowV24(ctx context.Context, flowId string) ([]*entity.TaskFlowPublishHistory, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var tfp []*entity.TaskFlowPublishHistory
	sid := util.RequestID(ctx)
	if err := db.Table(entity.TaskFlowPublishHistory{}.TableName()).
		Where("f_flow_id=? AND f_is_deleted=0", flowId).Find(&tfp).Error; err != nil {
		log.ErrorContextf(ctx, "FetchPublishedTaskFlowV24, err:%s|%s", sid, err)
		return nil, err
	}
	return tfp, nil
}

// SaveUpgrade_V24 ...
func SaveUpgrade_V24(ctx context.Context, tf entity.ModifyTaskFlowParams) error {
	sid := util.RequestID(ctx)
	tx := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	err := tx.Model(&entity.TaskFlow{}).
		Where("f_flow_id = ?", tf.FlowID).
		Updates(map[string]interface{}{
			"f_proto_version":      KEP.TaskFlowProtoVersion_V2_4,
			"f_dialog_json_draft":  tf.DialogJsonDraft,
			"f_dialog_json_enable": tf.DialogJsonEnable,
			"f_update_time":        tf.UpdateTime,
		}).Error
	if err != nil {
		log.Errorf("txUpdateTaskFlowProtoVersion Failed! |%s|%s", sid, err)
		return err
	}
	return nil
}

// CreateCustomVarUserID ...
func CreateCustomVarUserID(ctx context.Context, appId, varName, uin,
	subUin string, isListCreate bool) (*entity.VarParams, error) {
	sid := util.RequestID(ctx)
	var varParams []*entity.VarParams
	var err error
	// 创建前查询是否存在，如果存在则返回，不创建
	varParams, err = GetVarsByNameOrAppId(ctx, appId, varName, "", isListCreate)
	if err != nil {
		log.ErrorContextf(ctx, "Upgrade_V24|sid:%s|CreateCustomVarUserID|GetVarsByNameOrAppId|err:%+v", sid, err)
		return nil, err
	}
	if len(varParams) > 0 {
		return varParams[0], nil
	}

	// 没有，创建UserID变量
	varParam := &entity.VarParams{
		VarName:       varName,
		AppID:         appId,
		VarID:         idgenerator.NewUUID(),
		UIN:           uin,
		SubUIN:        subUin,
		IsDeleted:     0,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		Action:        entity.ActionInsert,
	}
	if err = CreateVar(ctx, varParam); err != nil {
		log.ErrorContextf(ctx, "Upgrade_V24|sid:%s|CreateCustomVarUserID|CreateVar|err:%+v", sid, err)
		return nil, err
	}
	return varParam, nil
}

// FetchTaskFlowV24 获取 任务流程
func FetchTaskFlowV24(ctx context.Context, botBizId, flowId string) ([]*entity.TaskFlow, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var taskFlows []*entity.TaskFlow
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "FetchTaskFlowV24|botBizId=%s", botBizId)
	tx := db.Table("t_task_flow AS tf").
		Select("tf.*").
		Joins("INNER JOIN t_robot_intent AS ri ON tf.f_intent_id = ri.f_intent_id").
		Where("ri.f_is_deleted = 0")

	if len(flowId) > 0 {
		tx = tx.Where("tf.f_flow_id = ?", flowId)
	}

	tx = tx.Where("ri.f_robot_id = ?", botBizId).
		Where("tf.f_is_deleted = 0").
		Where("tf.f_proto_version = ?", KEP.TaskFlowProtoVersion_V2_1).
		Where("tf.f_flow_type = ? ", entity.FlowTypeICS).
		//Where("tf.f_dialog_json_enable <> ''").
		Order("tf.f_update_time ASC").
		Find(&taskFlows)
	if tx.Error != nil {
		log.ErrorContextf(ctx, "FetchTaskFlow, err:%s|%s", sid, tx.Error.Error())
		return nil, tx.Error
	}

	return taskFlows, nil
}

// [升级v1.7对话树的协议到 v2.1]

// FetchTaskFlow 捞一批数据，任务流程
// SELECT
// tf.*
// FROM
// t_task_flow AS tf
// INNER JOIN t_robot_intent AS ri ON tf.f_intent_id = ri.f_intent_id
// WHERE
// ri.f_is_deleted = 0
// AND ri.f_robot_id ="1737375624057454592"
// AND tf.f_is_deleted = 0
// AND tf.f_proto_version = 0
// AND tf.f_flow_type = "ICS"
// AND tf.f_dialog_json_enable != ""
// ORDER by  tf.f_update_time ASC
func FetchTaskFlow(ctx context.Context, botBizId, flowId string) ([]*entity.TaskFlow, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var taskFlows []*entity.TaskFlow
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "FetchTaskFlow|botBizId=%s", botBizId)
	tx := db.Table("t_task_flow AS tf").
		Select("tf.*").
		Joins("INNER JOIN t_robot_intent AS ri ON tf.f_intent_id = ri.f_intent_id").
		Where("ri.f_is_deleted = 0")

	if len(flowId) > 0 {
		tx = tx.Where("tf.f_flow_id = ?", flowId)
	}

	tx = tx.Where("ri.f_robot_id = ?", botBizId).
		Where("tf.f_is_deleted = 0").
		Where("tf.f_proto_version = 0 OR tf.f_proto_version = ?", KEP.TaskFlowProtoVersion_V1_7).
		Where("tf.f_flow_type = ? ", entity.FlowTypeICS).
		Where("tf.f_dialog_json_enable <> ''").
		Order("tf.f_update_time ASC").
		Find(&taskFlows)
	if tx.Error != nil {
		log.ErrorContextf(ctx, "FetchTaskFlow, err:%s|%s", sid, tx.Error.Error())
		return nil, tx.Error
	}

	return taskFlows, nil
}

// GetSlotListWithNames 取槽位
func GetSlotListWithNames(ctx context.Context, robotID string, slotNames []string) ([]*entity.Slot, error) {
	log.InfoContextf(ctx, "GetSlotListWithNames, robotID:%s, len(slotNames):%d", robotID, len(slotNames))
	if len(robotID) == 0 || len(slotNames) == 0 {
		return nil, nil
	}

	var slots []*entity.Slot
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Table(entity.Slot{}.TableName()).
		Select("f_slot_id, f_slot_name").
		Where("f_is_deleted = 0").
		Where("f_robot_id = ?", robotID).
		Where("f_slot_name IN ?", slotNames).
		Find(&slots).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotListWithNames db.Find Failed, err:%v", err)
		return nil, err
	}
	return slots, nil
}

// SaveUpgrade 升级
func SaveUpgrade(ctx context.Context, uin, subUin string, robotID string,
	needCreateSlotNameList map[string]string, tf entity.ModifyTaskFlowParams) error {
	tx := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := tx.Transaction(func(tx *gorm.DB) error {
		log.InfoContextf(ctx, "SaveUpgrade|start...")

		for slotName, slotID := range needCreateSlotNameList {
			entityID := idgenerator.NewUUID()
			slotInfo, entityInfo, slotEntityInfo := buildSlotEntityInfo(uin, subUin, slotID, entityID,
				slotName, "", "[]", robotID)
			err := txBatchCreateSlotEntityTable(ctx, tx, robotID, []entity.Slot{slotInfo}, []entity.Entity{entityInfo},
				[]entity.Entry{}, []entity.SlotEntity{slotEntityInfo})
			if err != nil {
				log.ErrorContextf(ctx, "SaveUpgrade|txBatchCreateSlotEntityTable|slotID:%s|flowId:%s|err:%+v",
					slotID, tf.FlowID, err)
				return err
			}
		}

		log.InfoContextf(ctx, "SaveUpgrade|SaveUpgradeTaskFlow|tf:%+v", tf)
		// 保存任务流程
		if err := SaveUpgradeTaskFlow(ctx, tx, tf); err != nil {
			log.ErrorContextf(ctx, "SaveUpgrade|SaveUpgradeTaskFlow|flowId:%s|err:%+v", tf.FlowID, err)
			return err
		}

		// 单独的"更改协议标识"
		if err := txUpdateTaskFlowProtoVersion(ctx, tx, tf.FlowID, KEP.TaskFlowProtoVersion_V2_1); err != nil {
			log.ErrorContextf(ctx, "SaveUpgrade|txUpdateTaskFlowProtoVersion|flowId:%s|err:%+v", tf.FlowID, err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "SaveUpgrade fail, err:%+v", err)
		return err
	}
	return nil
}

// SaveUpgradeTaskFlow  更新任务流
func SaveUpgradeTaskFlow(ctx context.Context, tx *gorm.DB, params entity.ModifyTaskFlowParams) error {
	return editTaskFlowByTx(ctx, tx, params)

}

// txUpdateTaskFlowProtoVersion 通过任务流信息更新t_task_flow表
func txUpdateTaskFlowProtoVersion(ctx context.Context, tx *gorm.DB, taskFlowId string,
	version KEP.TaskFlowProtoVersion) error {
	sid := util.RequestID(ctx)
	err := tx.Model(&entity.TaskFlow{}).
		Where("f_flow_id = ?", taskFlowId).
		Updates(map[string]interface{}{
			entity.TTaskFlowColumns.ProtoVersion: version,
		}).Error
	if err != nil {
		log.Errorf("txUpdateTaskFlowProtoVersion Failed! |%s|%s", sid, err)
		return err
	}
	return nil
}

// GetDirtyTaskFlow  去要删的内容
func GetDirtyTaskFlow(ctx context.Context, botBizId string) (map[string]*entity.TaskFlow, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var taskFlows []*entity.TaskFlow
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetDirtyTaskFlow|botBizId=%s", botBizId)
	tx := db.Table("t_task_flow AS tf").
		Select("tf.*").
		Joins("INNER JOIN t_robot_intent AS ri ON tf.f_intent_id = ri.f_intent_id").
		Where("ri.f_is_deleted = 0").
		Where("ri.f_robot_id = ?", botBizId).
		Where("tf.f_is_deleted = 0").
		Where("tf.f_proto_version = 0 OR tf.f_proto_version = ?", KEP.TaskFlowProtoVersion_V1_7).
		Where("tf.f_flow_type = ? ", entity.FlowTypeICS).
		Where("tf.f_dialog_json_enable = ''").
		Find(&taskFlows)
	if tx.Error != nil {
		log.ErrorContextf(ctx, "GetDirtyTaskFlow, err:%s|%s", sid, tx.Error.Error())
		return nil, tx.Error
	}

	tfs := make(map[string]*entity.TaskFlow, 0)
	for _, item := range taskFlows {
		tfs[item.FlowID] = item
	}

	return tfs, nil
}

// GetRobotIDList 机器人列表
func GetRobotIDList(ctx context.Context) ([]string, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var robotIDs []string
	err := db.
		Model(&entity.RobotIntent{}).
		Where("f_is_deleted = 0").
		Select("DISTINCT f_robot_id").
		Find(&robotIDs).Error
	if err != nil {
		return robotIDs, err
	}
	return robotIDs, nil
}

// DeleteTaskFlowWithIgnoreDMErr 删除，通知dm 失败也忽略
//func DeleteTaskFlowWithIgnoreDMErr(ctx context.Context,
//	robotId string, flowIds []string) error {
//	if len(flowIds) == 0 {
//		return nil
//	}
//	var corpusIDs []string
//	var intentIDs []string
//	var enableIntentIds []string
//	var enableCorpusIDs []string
//	var enableFlowsId []string
//
//	var taskFlows []entity.TaskFlow
//	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
//	vdb := vdao.NewDao()
//	sid := util.RequestID(ctx)
//
//	if err := db.Transaction(func(tx *gorm.DB) error {
//		if len(flowIds) <= 0 {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,GetTaskFLowDetails err:%s|flowIds:%s", sid, flowIds)
//			return utilsErrors.ErrParams
//		}
//
//		//1. 通过flowIds 在 TaskFlow 找到 intentids 并标记删除
//		if err := tx.Table(entity.TaskFlow{}.TableName()).
//			Where("f_flow_id IN (?)", flowIds).
//			Where("f_release_status != ?", entity.ReleaseStatusPublishing).
//			Find(&taskFlows).Updates(map[string]interface{}{
//			"f_is_deleted":     gorm.Expr("f_id"),
//			"f_action":         entity.ActionDelete,
//			"f_release_status": entity.ReleaseStatusUnPublished,
//		}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr failed delete:%+v,err:%+v", flowIds, err)
//			return err
//		}
//		for _, flow := range taskFlows {
//			intentIDs = append(intentIDs, flow.IntentID)
//		}
//
//		// 1-2 删除历史记录
//		if err := tx.Table(entity.TaskFlowHistory{}.TableName()).
//			Where("f_flow_id IN (?)", flowIds).
//			Updates(map[string]interface{}{"f_is_deleted": gorm.Expr("f_id")}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr TaskFlowHistory failed delete:%+v,err:%+v",
//				flowIds, err)
//			return err
//		}
//
//		// 1-3 删除历史发布版本
//		if err := tx.Table(entity.TaskFlowPublishHistory{}.TableName()).
//			Where("f_flow_id IN (?)", flowIds).
//			Updates(map[string]interface{}{"f_is_deleted": gorm.Expr("f_id")}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr TaskFlowPublishHistory failed delete:%+v,err:%+v",
//				flowIds, err)
//			return err
//		}
//
//		// 找出校验过的任务流
//		for _, flow := range taskFlows {
//			if flow.DialogJsonEnable != "" {
//				enableIntentIds = append(enableIntentIds, flow.IntentID)
//				enableFlowsId = append(enableFlowsId, flow.FlowID)
//			}
//		}
//
//		log.InfoContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,robotId:%s|%s, intentIDs:%+v", sid, robotId, intentIDs)
//
//		// 2. 删除Intent与Flow绑定关系
//		if err := tx.Table(entity.IntentFlow{}.TableName()).
//			Where("f_flow_id IN (?) AND f_intent_id IN (?)", flowIds, intentIDs).
//			Updates(map[string]interface{}{
//				"f_is_deleted":     gorm.Expr("f_id"),
//				"f_action":         entity.ActionDelete,
//				"f_release_status": entity.ReleaseStatusUnPublished,
//			}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr failed delete IntentFlow,"+
//				"intentIDs:%+v,flowIds:%+v,err:%+v", intentIDs, flowIds, err)
//			return err
//		}
//		// 3. 在表 t_intent 删除
//		if err := tx.Table(entity.Intent{}.TableName()).Where("f_intent_id IN (?)",
//			intentIDs).Updates(map[string]interface{}{
//			"f_is_deleted":     gorm.Expr("f_id"),
//			"f_action":         entity.ActionDelete,
//			"f_release_status": entity.ReleaseStatusUnPublished,
//		}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr, delete Intent fail err:%s｜%v", sid, err)
//			return err
//		}
//		// 3-1. 在表 t_intent_slot 删除
//		if err := deleteIntentSlot(ctx, tx, intentIDs); err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr|deleteIntentSlot|sid:%s｜%v", sid, err)
//			return err
//		}
//		// 4. 删除语料库 t_corpus
//		var corpuss []entity.Corpus
//		if err := tx.Table(entity.Corpus{}.TableName()).Where("f_intent_id IN (?)",
//			intentIDs).Find(&corpuss).Updates(map[string]interface{}{
//			"f_is_deleted":     gorm.Expr("f_id"),
//			"f_action":         entity.ActionDelete,
//			"f_release_status": entity.ReleaseStatusUnPublished,
//		}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,"+
//				" delete Corpus err:%v, intentIDs:%+v", err, intentIDs)
//			return err
//		}
//		for _, corpus := range corpuss {
//			corpusIDs = append(corpusIDs, corpus.CorpusID)
//		}
//
//		// 找出保存了向量库的预料
//		var enalbeCorpuss []entity.Corpus
//		if err := tx.Table(entity.Corpus{}.TableName()).Where("f_intent_id IN (?)",
//			enableIntentIds).Find(&enalbeCorpuss).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,"+
//				" delete Corpus err:%v, enableIntentIds:%+v", err, enableIntentIds)
//			return err
//		}
//		for _, corpus := range enalbeCorpuss {
//			enableCorpusIDs = append(enableCorpusIDs, corpus.CorpusID)
//		}
//
//		// 5. 删除与机器人绑定 t_robot_intent
//		if err := tx.Table(entity.RobotIntent{}.TableName()).
//			Where("f_robot_id = ? AND f_intent_id IN (?)", robotId, intentIDs).
//			Updates(map[string]interface{}{
//				"f_is_deleted":     gorm.Expr("f_id"),
//				"f_action":         entity.ActionDelete,
//				"f_release_status": entity.ReleaseStatusUnPublished,
//			}).Error; err != nil {
//			log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,delete RobotIntent err:%v,robotId:%+v, intentIDs:%v",
//				err, robotId, intentIDs)
//			return err
//		}
//
//		// 6. 删除向量语料数据
//		if len(enableCorpusIDs) > 0 {
//			log.InfoContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,DeleteCorpusVector,"+
//				"robotId:%s|%s, corpusIDs:%+v", sid, robotId, corpusIDs)
//			if err := vdb.DeleteCorpusVector(ctx, robotId, enableCorpusIDs); err != nil {
//				log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr, DeleteCorpusVector fail err:%s", sid)
//				return err
//			}
//		}
//
//		if len(enableFlowsId) > 0 {
//			// 7. 组装任务流删除数据给dm
//			log.InfoContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr,AssembleDmDeleteTaskFlowData,"+
//				"robotId:%s|%s, flowIds:%+v", sid, robotId, enableFlowsId)
//			_, _ = AssembleDmDeleteTaskFlowData(ctx, robotId, enableFlowsId)
//		}
//
//		return nil
//	}); err != nil {
//		log.ErrorContextf(ctx, "DeleteTaskFlowWithIgnoreDMErr fail, err:%+v", err)
//		return err
//	}
//	return nil
//}

// buildSlotEntityInfo ...
func buildSlotEntityInfo(uin, subAccountUin, slotID, entityID, slotName, slotDesc, examples, robotId string) (
	entity.Slot, entity.Entity, entity.SlotEntity) {

	entityInfo := entity.Entity{
		EntityID:      entityID,
		EntityName:    slotName,
		EntityDesc:    slotDesc,
		Examples:      examples,
		LevelType:     entity.SlotLevelBOT,
		RobotID:       robotId,
		UIN:           uin,
		SubUIN:        subAccountUin,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}

	slotInfo := entity.Slot{
		SlotID:        slotID,
		SlotName:      slotName,
		SlotDesc:      slotDesc,
		Examples:      examples,
		RobotID:       robotId,
		UIN:           uin,
		SubUIN:        subAccountUin,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}

	slotEntityInfo := entity.SlotEntity{
		SlotID:        slotID,
		EntityID:      entityID,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}

	return slotInfo, entityInfo, slotEntityInfo
}
