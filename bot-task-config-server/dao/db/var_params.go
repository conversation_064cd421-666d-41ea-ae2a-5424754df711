// bot-task-config-server
//
// @(#)var_params.go  星期三, 六月 26, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"gorm.io/gorm"
)

var (
	// GetVarsByNameOrAppIdFunc for 单测
	GetVarsByNameOrAppIdFunc = GetVarsByNameOrAppId
)

// UpsertVarParamsToDM 参数变更通知DM
func UpsertVarParamsToDM(ctx context.Context, appId string, varParams []*KEP_WF_DM.Var) error {
	sid := util.RequestID(ctx)
	if len(varParams) == 0 {
		return nil
	}
	// 老的dm调用
	//upsertVarReq := &KEP_DM.UpsertVariablesToSandboxRequest{
	//	RobotID:   appId,
	//	Variables: varParams,
	//}
	//_, err := rpc.UpsertVariablesToSandbox(ctx, upsertVarReq)
	//if err != nil {
	//	log.ErrorContextf(ctx, "UpdateVar.UpsertVariablesToSandbox Failed! "+
	//		"|sid:%s|err:%s", sid, err.Error())
	//	return err
	//}
	// 调用新的dm
	varWorkflowReqs := make([]*KEP_WF_DM.Var, 0)
	for _, item := range varParams {
		varWorkflowReqs = append(varWorkflowReqs, &KEP_WF_DM.Var{
			VarID:           item.VarID,
			VarName:         item.VarName,
			VarDesc:         item.VarDesc,
			ValueType:       item.ValueType,
			VarDefaultValue: item.VarDefaultValue,
		})
	}
	upsertVarWorkflowReq := &KEP_WF_DM.UpsertVariablesToSandboxRequest{
		AppID:     appId,
		Variables: varWorkflowReqs,
	}
	_, err := rpc.UpsertVariablesToWorkflowSandbox(ctx, upsertVarWorkflowReq)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateVar.UpsertVariablesToWorkflowSandbox Failed! "+
			"|sid:%s|err:%s", sid, err.Error())
		return err
	}
	return nil
}

// createImportVarParams 导入创建变量
func createImportVarParams(ctx context.Context, tx *gorm.DB,
	robotId string, varParams []*entity.VarParams) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|createImportVarParams|robotID:%s|len(varParams):%d",
		sid, robotId, len(varParams))
	if len(varParams) > 0 {
		dbVarParams, _ := GetVarsByNameOrAppId(ctx, robotId, "", "", false)
		// 导入前，对变量名称去重处理
		// 首先 找到 已经存在的变量名称
		dbVarParamNames := make([]string, 0)
		createDBParamVars := make([]entity.VarParams, 0)
		for _, v := range dbVarParams {
			dbVarParamNames = append(dbVarParamNames, v.VarName)
		}
		// 对变量名称去重
		for _, v := range varParams {
			if !util.StrInArray(v.VarName, dbVarParamNames) {
				createDBParamVars = append(createDBParamVars, *v)
			}
		}
		if len(createDBParamVars) > 0 {
			// 导入 剩下的部分
			maxImportMax := config.GetMainConfig().VerifyTaskFlow.VarParamsCountMax
			maxCreateCnt := maxImportMax - len(dbVarParams) // 最多能导入的变量
			// 导入超限的处理
			if maxCreateCnt < len(createDBParamVars) {
				createDBParamVars = createDBParamVars[:maxCreateCnt]
			}
			err := tx.Model(&entity.VarParams{}).Create(createDBParamVars).Error
			if err != nil {
				log.ErrorContextf(ctx, "sid:%s|createImportIntentCorpusExamples,CreateIntentCorpus Failed,err:%v", sid, err)
				return err
			}
			// 导入成功，更新DM
			varReqs := make([]*KEP_WF_DM.Var, 0)
			for _, item := range createDBParamVars {
				varReqs = append(varReqs, &KEP_WF_DM.Var{
					VarName:         item.VarName,
					VarID:           item.VarID,
					VarDesc:         item.VarDesc,
					ValueType:       KEP_WF.TypeEnum(KEP_WF.TypeEnum_value[item.VarType]),
					VarDefaultValue: item.VarDefaultValue,
				})
			}
			err = UpsertVarParamsToDM(ctx, robotId, varReqs)
			if err != nil {
				log.ErrorContextf(ctx, "sid:%s|createImportIntentCorpusExamples,"+
					"UpsertVarParamsToDM Failed,err:%v", sid, err)
				return err
			}
		}

	}
	return nil
}

// GetImportVarParamsInfos 通过变量名 获取导入的全部变量名信息
func GetImportVarParamsInfos(ctx context.Context, robotID string,
	varParamsName []string) (map[string]*entity.VarParams, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	log.InfoContextf(ctx, "sid:%s|GetImportVarParamsInfos|robotID:%s|len(varParamsName):%d",
		sid, robotID, len(varParamsName))
	if len(robotID) == 0 || len(varParamsName) == 0 {
		return nil, nil
	}
	var varParams []*entity.VarParams
	db = db.Table(entity.VarParams{}.TableName()).Where("f_is_deleted=0 AND f_app_id=?", robotID)
	if len(varParamsName) > 0 {
		db = db.Where("f_var_name IN ?", varParamsName)
	}

	if err := db.Find(&varParams).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetImportVarParamsInfos db.Find Failed,err:%v", sid, err)
		return nil, err
	}
	varParamsMap := make(map[string]*entity.VarParams)
	for _, v := range varParams {
		varParamsMap[v.VarName] = v
	}

	return varParamsMap, nil
}

// GetExportIntentVarParams 获取导出 意图 关联的 自定义变量
func GetExportIntentVarParams(ctx context.Context, robotID string,
	intentIDs []string) (map[string][]*entity.ExportIntentVar, error) {
	sid := util.RequestID(ctx)
	mm := make(map[string]struct{}, 0)

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var exportIntentVar []*entity.ExportIntentVar
	mapExportIntentVar := make(map[string][]*entity.ExportIntentVar)

	if err := db.Table("t_var as v").
		Select("v.f_var_id, v.f_var_name, v.f_var_desc,v.f_var_type,v.f_var_default_value,"+
			"f_var_default_file_name,iv.f_intent_id").
		Joins("RIGHT JOIN t_intent_var as iv ON v.f_var_id=iv.f_var_id").
		Where("v.f_is_deleted=0 AND iv.f_is_deleted=0 AND v.f_app_id=? AND iv.f_intent_id IN ?",
			robotID, intentIDs).Find(&exportIntentVar).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExportIntentVarParams|err:%+v", sid, err)
		return nil, err
	}
	for _, v := range exportIntentVar {
		if _, ok := mm[v.VarID]; !ok {
			mapExportIntentVar[v.IntentID] = append(mapExportIntentVar[v.IntentID], v)
			mm[v.VarID] = struct{}{}
		}
	}
	return mapExportIntentVar, nil
}

// GetTaskFlowInfoByVarParamIds 通过参数ID，找到引用的画布
func GetTaskFlowInfoByVarParamIds(ctx context.Context, robotID, varId string) ([]*entity.TaskFlow, error) {
	sid := util.RequestID(ctx)
	//	通过关联表 t_intent_var 查找到对应的f_intent_id
	var intentVarParams []*entity.IntentVarParams
	var taskFlowInfo []*entity.TaskFlow
	var intentIDs []string

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	if err := db.Table(entity.IntentVarParams{}.TableName()).
		Where("f_is_deleted=0 AND f_var_id = ?", varId).
		Scan(&intentVarParams).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetTaskFlowInfoByVarParamIds:err:%v", sid, err)
		return nil, err
	}
	for _, intentVar := range intentVarParams {
		intentIDs = append(intentIDs, intentVar.IntentID)
	}
	log.InfoContextf(ctx, "sid:%s|GetTaskFlowInfoByVarParamIds|len:%d|intentIDs:%+v",
		sid, len(intentIDs), intentIDs)
	// 通过intentId 查询 taskflow
	if err := db.Table(entity.TaskFlow{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id IN (SELECT f_intent_id FROM "+
			"t_robot_intent WHERE f_is_deleted=0 AND f_intent_id IN ? AND f_robot_id = ?)", intentIDs, robotID).
		Scan(&taskFlowInfo).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetTaskFlowInfoByVarParamIds:err:%v", sid, err)
		return nil, err
	}
	return taskFlowInfo, nil
}

// GetWorkflowInfoByVarParamIds 获取自定义参数关联工作流
func GetWorkflowInfoByVarParamIds(ctx context.Context, appBizId, varId string) ([]entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowInfo []entity.Workflow
	if err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted = 0 AND f_workflow_id IN (SELECT f_workflow_id FROM "+
			"t_workflow_var WHERE f_is_deleted = 0 AND f_var_id = ?) AND f_robot_id = ?", varId, appBizId).
		Scan(&workFlowInfo).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowInfoByVarParamIds:err:%v", err)
		return nil, err
	}
	return workFlowInfo, nil
}

// deleteIntentVarParams 删除自定义参数关联
func deleteIntentVarParams(ctx context.Context, tx *gorm.DB, intentIDs []string) error {
	sid := util.RequestID(ctx)
	err := tx.Table(entity.IntentVarParams{}.TableName()).
		Where("f_intent_id IN (?)", intentIDs).
		Where("f_is_deleted = 0").
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|deleteIntentVarParams|Updates|err:%+v", sid, err)
		return err
	}
	return nil
}

// updateIntentVarParamsAssociation 以intent维度，更新自定义变量的关联关系
func updateIntentVarParamsAssociation(ctx context.Context, tx *gorm.DB,
	intentID string, varParamsIDs []string) error {
	log.InfoContextf(ctx, "updateIntentVarParamsAssociation|intentID:%s|varParamsIDs:%+v",
		intentID, varParamsIDs)
	if len(varParamsIDs) == 0 {
		return nil
	}

	// 1. 标记删除最新的tree中没有用到的VarParamsIDs的关联关系
	if err := tx.Table(entity.IntentVarParams{}.TableName()).
		Where("f_intent_id = ? ", intentID).Where("f_var_id NOT IN (?)", varParamsIDs).
		Where("f_is_deleted = 0").Updates(map[string]interface{}{
		"f_is_deleted": 1, "f_action": entity.ActionDelete,
		"f_release_status": entity.ReleaseStatusUnPublished,
		"f_update_time":    time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "updateIntentVarParamsAssociation|1-Updates|err:%+v", err)
		return err
	}

	// 2. 查找现在库里有哪些
	var intentVars []entity.IntentVarParams
	if err := tx.Table(entity.IntentVarParams{}.TableName()).
		Where("f_intent_id = ? ", intentID).Where("f_var_id IN (?)", varParamsIDs).
		Where("f_is_deleted = 0").
		Find(&intentVars).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.ErrorContextf(ctx, "updateIntentVarParamsAssociation|2-Find|err:%+v", err)
		return err
	}

	existingVarParamsIDs := make(map[string]bool)
	for _, varParam := range intentVars {
		existingVarParamsIDs[varParam.VarID] = true
	}

	needUpdateVarParamsId := make([]string, 0)
	newIntentVars := make([]entity.IntentVarParams, 0)
	// 3. 库里有的更新，库里没有的insert
	log.InfoContextf(ctx, "updateIntentVarParamsAssociation|varParamsIDs:%s", varParamsIDs)
	for _, varId := range varParamsIDs {
		if existingVarParamsIDs[varId] {
			needUpdateVarParamsId = append(needUpdateVarParamsId, varId)
		} else {
			// 如果找不到记录，则创建新的记录
			newIntentVars = append(newIntentVars, entity.IntentVarParams{
				IntentID: intentID, VarID: varId, ReleaseStatus: entity.ReleaseStatusUnPublished,
				IsDeleted: 0, Action: entity.ActionInsert,
				CreateTime: time.Now(), UpdateTime: time.Now(),
			})
		}
	}

	log.InfoContextf(ctx, "updateIntentVarParamsAssociation|needUpdateVarParamsId:%+v", needUpdateVarParamsId)
	if len(needUpdateVarParamsId) > 0 {
		// 如果找到了记录，则更新
		if err := tx.Table(entity.IntentVarParams{}.TableName()).
			Where("f_intent_id = ? ", intentID).Where("f_var_id IN (?)", needUpdateVarParamsId).
			Where("f_is_deleted = 0").Updates(map[string]interface{}{
			"f_action":         entity.ActionUpdate,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error; err != nil {
			log.ErrorContextf(ctx, "updateIntentVarParamsAssociation|3-updates|needUpdateVarParamsId:%+v|err:%+v",
				needUpdateVarParamsId, err)
			return err
		}
	}

	log.InfoContextf(ctx, "updateIntentVarParamsAssociation|newIntentVars:%+v", newIntentVars)
	if len(newIntentVars) > 0 {
		if err := tx.Table(entity.IntentVarParams{}.TableName()).Create(&newIntentVars).Error; err != nil {
			log.ErrorContextf(ctx, "updateIntentVarParamsAssociation|4-insert|newIntentVars:%+v|err:%+v",
				newIntentVars, err)
			return err
		}
	}

	return nil
}

// DeleteVar 删除变量
func DeleteVar(ctx context.Context, appId, varId string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)

	if err := db.Transaction(func(tx *gorm.DB) error {
		// 删除变量前判断变量是否存在
		varParam, err := GetVarByVarId(ctx, appId, varId)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteVar GetVarByVarId error:%+v", err)
			return err
		}
		if varParam == nil {
			return errs.Newf(errors.ErrVarNotFoundInApp, "变量在该应用下不存在")
		}

		// 逻辑删除变量
		if err = tx.Table(entity.VarParams{}.TableName()).
			Where("f_is_deleted=0 AND f_app_id=? AND f_var_id=?", appId, varId).
			Updates(map[string]interface{}{
				"f_is_deleted":     1,
				"f_uin":            uin,
				"f_sub_uin":        subUin,
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "DeleteVar update fail err:%+v", err)
			return err
		}
		// 需要解除任务流程的引用关系才能删除，这里不用操作关联表

		// 调用新的dm，去掉老的
		delVarWorkflowReq := &KEP_WF_DM.DeleteVariablesInSandboxRequest{
			AppID:  appId,
			VarIDs: []string{varId},
		}
		_, err = rpc.DeleteVariablesToWorkflowSandbox(ctx, delVarWorkflowReq)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteVar DeleteVariablesToWorkflowSandbox Failed!:%s", err.Error())
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "DeleteVar fail, err:%+v", err)
		return err
	}
	return nil
}

// UpdateVar 更新变量
func UpdateVar(ctx context.Context, appId, varId, varName, varDesc, varType, varDefaultValue,
	varDefaultFileName string) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 看是否存在要更新的变量
		varParam, err := GetVarByVarId(ctx, appId, varId)
		if err != nil {
			log.ErrorContextf(ctx, "UpdateVar GetVarByVarId error:%+v", err)
			return err
		}
		if varParam == nil {
			return errs.Newf(errors.ErrVarNotFoundInApp, "变量在该应用下不存在")
		}
		// 查询自定义变量是否被老版本任务流程引用，检查引用的任务流程的发布状态，是否处于发布状态中，发布状态不允许编辑
		err = checkTaskFlowByVar(ctx, tx, varId)
		if err != nil {
			return err
		}
		// 查询自定义变量是否被新版本工作流引用，检查引用的工作流的发布状态，是否处于发布状态中，发布状态不允许编辑
		err = checkWorkflowByVar(ctx, appId, varId)
		if err != nil {
			return err
		}
		// 根据自身情况，修改状态
		action := entity.ActionUpdate
		if (varParam.ReleaseStatus == entity.WorkflowReleaseStatusUnPublished ||
			varParam.ReleaseStatus == entity.WorkflowReleaseStatusFail) && varParam.Action == entity.ActionInsert {
			action = entity.ActionInsert
		}
		// 更新变量
		if err = tx.Table(entity.VarParams{}.TableName()).
			Where("f_is_deleted=0 AND f_app_id=? AND f_var_id=?", appId, varId).
			Updates(map[string]interface{}{
				"f_var_name":              varName,
				"f_var_desc":              varDesc,
				"f_var_type":              varType,
				"f_var_default_value":     varDefaultValue,
				"f_var_default_file_name": varDefaultFileName,
				"f_uin":                   uin,
				"f_sub_uin":               subUin,
				"f_action":                action,
				"f_release_status":        entity.ReleaseStatusUnPublished,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "UpdateVar update fail, err:%+v", err)
			return err
		}
		// 知识型引用同步修改
		modifyAppVarReq := &admin.ModifyAppVarReq{
			AppBizId: uint64(encode.StringToInt64(appId)),
			VarInfo: &admin.ModifyAppVarReqVarInfo{
				VarId:   varId,
				VarName: varName,
			},
		}
		_, err = rpc.ModifyAppVar(ctx, modifyAppVarReq)
		if err != nil {
			log.ErrorContextf(ctx, "UpdateVar ModifyAppVar|err:%+v", err)
			return err
		}
		// 同步dm
		varParams := []*KEP_WF_DM.Var{{
			VarID:           varId,
			VarName:         varName,
			VarDesc:         varDesc,
			ValueType:       KEP_WF.TypeEnum(KEP_WF.TypeEnum_value[varType]),
			VarDefaultValue: varDefaultValue,
		}}
		err = UpsertVarParamsToDM(ctx, appId, varParams)
		if err != nil {
			log.ErrorContextf(ctx, "UpdateVar UpsertVarParamsToDM Failed err:%v", err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "UpdateVar fail, err:%+v", err)
		return err
	}
	return nil
}

// checkTaskFlowByVar 检查变量被任务流程引用情况
func checkTaskFlowByVar(ctx context.Context, tx *gorm.DB, varId string) error {
	// 获取关联的intentIds
	var intentIds []string
	if err := tx.Table(entity.IntentVarParams{}.TableName()).
		Select("f_intent_id").Where("f_is_deleted=0 AND f_var_id= ? ", varId).
		Scan(&intentIds).Error; err != nil {
		log.ErrorContextf(ctx, "checkTaskFlowByVar get intentVar fail, err:%+v", err)
		return err
	}
	// 更新中间关系表状态
	if err := tx.Table(entity.IntentVarParams{}.TableName()).
		Where("f_is_deleted = 0 and f_var_id = ?", varId).
		Updates(map[string]interface{}{
			"f_action":         entity.ActionUpdate,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "checkTaskFlowByVar update intentVar err:%+v", err)
		return err
	}
	// 查询任务流程
	var flowInfos []TaskFlowInfo
	if err := tx.Table(entity.TaskFlow{}.TableName()).
		Select("f_flow_id as task_flow_id,"+
			" f_intent_name as task_flow_name, f_intent_id as task_intent_id,"+
			"f_flow_state as task_flow_status,f_release_status as task_release_status,"+
			"f_is_deleted as task_flow_deleted").Where("f_is_deleted=0 AND f_intent_id IN ? ", intentIds).
		Scan(&flowInfos).Error; err != nil {
		log.ErrorContextf(ctx, "checkTaskFlowByVar update taskFlow fail err:%+v", err)
		return err
	}
	// 检查任务流程的状态，是否处于发布状态中，发布状态不允许编辑
	if len(flowInfos) > 0 {
		for _, flowInfo := range flowInfos {
			if flowInfo.TaskReleaseStatus == entity.ReleaseStatusPublishing {
				return errors.ErrTaskFlowCantNotPublish
			}
		}
		// 更新t_task_flow表状态
		err := txUpdateTaskFlowByFlowInfo(ctx, tx, flowInfos)
		if err != nil {
			log.ErrorContextf(ctx, "checkTaskFlowByVar txUpdateTaskFlowByFlowInfo Failed err:%v", err)
			return err
		}
	}
	return nil
}

// checkWorkflowByVar 检查变量被工作流程引用情况
func checkWorkflowByVar(ctx context.Context, appId string, varId string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workFlowInfoList, err := GetWorkflowInfoByVarParamIds(ctx, appId, varId)
	if err != nil {
		log.ErrorContextf(ctx, "checkWorkflowByVar GetWorkflowInfoByVarParamIds|err:%+v", err)
		return err
	}
	// 更新中间关系表状态
	if err := db.Table(entity.WorkflowVar{}.TableName()).
		Where("f_is_deleted = 0 and f_var_id = ?", varId).
		Updates(map[string]interface{}{
			"f_action":         entity.ActionUpdate,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "checkWorkflowByVar update workflowVar err:%+v", err)
		return err
	}
	// 检查工作流的状态，是否处于发布状态中，发布状态不允许编辑
	if len(workFlowInfoList) > 0 {
		for _, workflowInfo := range workFlowInfoList {
			if workflowInfo.ReleaseStatus == entity.ReleaseStatusPublishing {
				return errors.ErrWorkflowPublishing
			}
		}
		// 更新t_workflow表状态 及 工作流向量启用状态
		err = txUpdateWorkflowByFlowInfo(ctx, db, workFlowInfoList, appId)
		if err != nil {
			log.ErrorContextf(ctx, "checkWorkflowByVar txUpdateWorkflowByFlowInfo Failed err:%v", err)
			return err
		}
	}
	return nil
}

// CreateVar 新建变量
func CreateVar(ctx context.Context, varParam *entity.VarParams) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)

	if err := db.Transaction(func(tx *gorm.DB) error {

		if err := tx.Table(entity.VarParams{}.TableName()).Create(varParam).Error; err != nil {
			log.ErrorContextf(ctx, "sid:%s|CreateVar, err:%v", sid, err)
			return err
		}
		varParams := []*KEP_WF_DM.Var{{
			VarID:           varParam.VarID,
			VarName:         varParam.VarName,
			VarDesc:         varParam.VarDesc,
			ValueType:       KEP_WF.TypeEnum(KEP_WF.TypeEnum_value[varParam.VarType]),
			VarDefaultValue: varParam.VarDefaultValue,
		}}
		err := UpsertVarParamsToDM(ctx, varParam.AppID, varParams)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|CreateVar,"+
				"UpsertVarParamsToDM Failed,err:%v", sid, err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "CreateVar fail, err:%+v", err)
		return err
	}

	return nil
}

// GetVarByVarId 通过appId 及 varId获取变量
func GetVarByVarId(ctx context.Context, appId, varId string) (*entity.VarParams, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	var varParam entity.VarParams

	res := db.Table(entity.VarParams{}.TableName()).
		Where("f_is_deleted=0 AND f_app_id=? AND f_var_id=?", appId, varId).
		Find(&varParam)

	if res.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetVarByVarId|err:%+v", sid, res.Error)
		return nil, res.Error
	}

	if res.RowsAffected == 0 {
		return nil, nil
	}

	return &varParam, nil
}

// GetVarsByAppIdAndVarIdsOrQuery 通过appId、keyWords 或 varIds 查询变量
func GetVarsByAppIdAndVarIdsOrQuery(ctx context.Context, appId, keyWords, varType string,
	offset, limit uint32, varIds []string) ([]*entity.VarParams, int64, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	query := strings.TrimSpace(keyWords)

	var total int64
	var varParams []*entity.VarParams
	varQuery := db.Table(entity.VarParams{}.TableName()).
		Where("f_is_deleted=0 AND f_app_id=?", appId)

	if len(query) > 0 {
		varQuery = varQuery.Where("f_var_name LIKE ?", "%"+query+"%")
	}

	if len(varType) > 0 {
		varQuery = varQuery.Where("f_var_type = ?", varType)
	}

	if len(varIds) > 0 {
		varQuery = varQuery.Where("f_var_id IN (?)", varIds)
	}
	varQuery = varQuery.Count(&total)

	varQuery = varQuery.Offset(int(offset)).Limit(int(limit))

	if err := varQuery.Order("f_update_time DESC").Scan(&varParams).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetVarsByAppIdAndVarIdsOrQuery|appId:%s|varIds:%s|keyWords:%s|err:%v",
			sid, appId, varIds, query, err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "sid:%s|GetVarsByAppIdAndVarIdsOrQuery|appId:%s|varParams:%+v", sid, appId, varParams)

	return varParams, total, nil
}

// GetVarsByNameOrAppId 通过应用Id及变量名称查找变量实例
func GetVarsByNameOrAppId(ctx context.Context, appId, varName, varId string, isListCreate bool) ([]*entity.VarParams, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	name := strings.TrimSpace(varName)
	var varParams []*entity.VarParams
	varQuery := db.Table(entity.VarParams{}.TableName()).
		Where("f_app_id=?", appId)
	if !isListCreate {
		varQuery = varQuery.Where("f_is_deleted=0")
	}
	if name != "" {
		varQuery = varQuery.Where("f_var_name=?", name)
	}
	if varId != "" {
		varQuery = varQuery.Where("f_var_id != ?", varId)
	}

	if err := varQuery.Scan(&varParams).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetVarByNameInApp|appId:%s|varName:%s|err:%v",
			sid, appId, name, err)
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetVarByNameInApp|appId:%s|varParams:%+v", sid, appId, varParams)

	return varParams, nil
}

// GetVarsMapByAppIdAndIds 通过应用Id，获取应用下的变量
func GetVarsMapByAppIdAndIds(ctx context.Context, appId string, varIds []string) (map[string]*entity.VarParams, error) {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var varParams []*entity.VarParams
	if err := db.Table(entity.VarParams{}.TableName()).
		Where("f_var_id IN ? AND  f_app_id=?", varIds, appId).
		Scan(&varParams).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetVarsMapByAppId|appId:%s|err:%v", sid, appId, err)
		return nil, err
	}
	var varsMap = make(map[string]*entity.VarParams, 0)
	for _, v := range varParams {
		varsMap[v.VarID] = v
	}
	return varsMap, nil
}
