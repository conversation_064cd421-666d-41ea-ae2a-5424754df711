// Package db 参数相关DB操作
// @Author: reinhold
// @Date: 2024/2/28 16:03
package db

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"gorm.io/gorm"
)

const (
	DeleteByEntry    = 1 // 手动删除
	DeleteByAutoSave = 2 // 自动保存是的删除
)

// GetEntryByParamID 根据参数id查找词条信息
func GetEntryByParamID(ctx context.Context, appBizID, paramID string) (
	entryInfos []*entity.WorkflowEntry, err error) {
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).
		Where(" f_robot_id = ? and f_parameter_id = ? and f_is_deleted= 0", appBizID, paramID).
		Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryByParamID db.Find Failed, err:%v", err)
		return
	}
	return
}

// GetEntryByParamIDs 批量根据参数id查找词条信息
func GetEntryByParamIDs(ctx context.Context, appBizID string, paramIDs []string) (
	entryInfos []*entity.WorkflowEntry, err error) {
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).
		Where(" f_robot_id = ? and f_parameter_id in ? and f_is_deleted= 0", appBizID, paramIDs).
		Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryByParamIDs db.Find Failed, err:%v", err)
		return
	}
	return
}

// CreateEntry 词条【正确示例】写入数据库
func CreateEntry(ctx context.Context, entryInfo *entity.WorkflowEntry, workflowInfo *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1. 词条写入db
	err = tx.WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).Create(&entryInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntry Failed! data:%+v,err:%+v", entryInfo, err)
		return err
	}

	//// 2. 更新映射关系
	//uin, subUin := util.GetUinAndSubAccountUin(ctx)
	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin, entity.ActionUpdate,
	//	entryInfo.ParameterID, workflowInfo)
	//if err != nil {
	//	return err
	//}
	return nil
}

// UpdateEntry 更新词条【正确示例】
func UpdateEntry(ctx context.Context, appBizID, paramID, entryID, entryName, entryAlias string,
	workflow *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// t_entry表
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = tx.WithContext(ctx).Debug().
		Exec("UPDATE t_entry "+
			"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
			"f_entry_value = ?,"+
			"f_entry_alias = ?,"+
			"f_uin = ?,"+
			"f_sub_uin = ?,"+
			"f_release_status = ? "+
			"WHERE f_entry_id = ? and f_robot_id = ? and f_is_deleted= 0",
			entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
			entity.ActionUpdate, entryName, entryAlias, uin, subUin,
			entity.ReleaseStatusUnPublished, entryID, appBizID).
		Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntry Failed!, err:%+v", err)
		return err
	}

	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflow)
	//if err != nil {
	//	return err
	//}
	return nil
}

// DeleteEntry 删除词条【正确示例】
func DeleteEntry(ctx context.Context, appBizID, paramID, entryID string, workflow *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).
		Where("f_entry_id = ? and f_robot_id = ? and f_is_deleted= 0", entryID, appBizID).
		Updates(map[string]interface{}{
			"f_uin":            uin,
			"f_sub_uin":        subUin,
			"f_is_deleted":     1,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         entity.ActionDelete,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "DeleteEntry Failed! err:%+v", err)
		return err
	}

	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflow)
	//if err != nil {
	//	return err
	//}
	return nil
}

// GetEntryInfosByParamID 词条【正确示例】 列表
func GetEntryInfosByParamID(ctx context.Context, paramID, keyword string, pageOffset, pageLimit int) (int64,
	[]*entity.WorkflowEntry, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).
		Where("f_parameter_id = ? and f_is_deleted= 0", paramID)

	if len(keyword) > 0 {
		likeKeyword := "%" + keyword + "%"
		db = db.Where("f_entry_value LIKE ? or f_entry_alias LIKE ? ", likeKeyword, likeKeyword)
	}

	// total
	var total int64
	db = db.Count(&total)

	// 根据偏移量查询
	var entryInfos []*entity.WorkflowEntry
	err := db.Offset(pageOffset).Limit(pageLimit).Order("f_id DESC").Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryInfosByParamID Failed! err:%+v", err)
		return 0, entryInfos, err
	}
	return total, entryInfos, nil
}

// GetAllEntryByParamID 获取某个paramID下所有的正确示例信息
func GetAllEntryByParamID(ctx context.Context, appBizID, paramID string) ([]*entity.WorkflowEntry, error) {
	var entryInfos []*entity.WorkflowEntry
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowEntry{}.TableName()).
		Where("f_robot_id = ? and f_parameter_id = ? and f_is_deleted= 0", appBizID, paramID).Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetAllEntryByParamID Failed! err:%+v", err)
		return nil, err
	}
	return entryInfos, nil

}

// GetAllInvalidEntryByParamID 获取某个paramID下所有的错误示例信息
func GetAllInvalidEntryByParamID(ctx context.Context, appBizID, paramID string) ([]*entity.WorkflowInvalidEntry, error) {
	var entryInfos []*entity.WorkflowInvalidEntry
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where("f_robot_id = ? and f_parameter_id = ? and f_is_deleted= 0", appBizID, paramID).Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetAllInvalidEntryByParamID Failed! err:%+v", err)
		return nil, err
	}
	return entryInfos, nil
}

//// txUpdateWorkflowEntryRelationState 从正确示例，错误示例往上修改一系列表的状态
//func txUpdateWorkflowEntryRelationState(ctx context.Context, tx *gorm.DB, uin, subUin, middleTableAction string, paramID string,
//	workflowInfo *entity.Workflow) (err error) {
//
//	// 更新parameter表
//	err = tx.WithContext(ctx).Debug().
//		Exec("UPDATE t_parameter "+
//			"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
//			"f_uin = ?,"+
//			"f_sub_uin = ?,"+
//			"f_release_status = ? "+
//			"WHERE f_parameter_id = ?  and f_is_deleted= 0",
//			entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
//			entity.ActionUpdate, uin, subUin,
//			entity.FrontStatusUnPublished, paramID).
//		Error
//	if err != nil {
//		log.ErrorContextf(ctx, "UpdateParam Failed!, err:%+v", err)
//		return err
//	}
//
//	return txUpdateWorkflowParamRelationState(ctx, tx, uin, subUin, middleTableAction, paramID, workflowInfo)
//
//}

// CreateWFEntryImportTask 创建词条导入任务流程
func CreateWFEntryImportTask(ctx context.Context, corpID, staffID uint64, importID, robotID, fileName string,
	task *entity.TaskFlowImport) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 导入任务
		if err := tx.Model(&entity.TaskFlowImport{}).Create(task).Error; err != nil {
			log.Errorf("CreateWFEntryImportTask createTask Failed! err:%v", err)
			return err
		}

		// 任务调度参数
		taskParams := entity.TaskFlowImportParentParams{
			RequestID: util.RequestID(ctx),
			CorpID:    corpID,
			StaffID:   staffID,
			RobotID:   robotID,
			ImportID:  importID,
			FileName:  fileName,
		}
		// 创建导入词条任务调度
		if err := scheduler.NewWFImportEntryTask(ctx, robotID, taskParams); err != nil {
			return err
		}

		// 创建导入任务通知
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId:     uint64(encode.StringToInt64(robotID)),
			PageId:       entity.NoticeWorkflowPageID,
			Type:         entity.NoticeTypeTaskFlowImport,
			Level:        entity.LevelInfo,
			RelateId:     uint64(encode.StringToInt64(importID)),
			Content:      fmt.Sprintf(entity.TaskFlowImportNoticeContentIng, fileName),
			IsGlobal:     false,
			IsAllowClose: false,
			CorpId:       corpID,
			StaffId:      staffID,
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "CreateWFEntryImportTask err:%+v", err)
		return err
	}
	return nil
}

// TXUpdateEntryAndRelationFromImportEntry 以事务的方式更新导入词条的相关数据及关联表关系
func TXUpdateEntryAndRelationFromImportEntry(ctx context.Context, uin, subUin, paramID string,
	insertEntry, updateEntry []entity.WorkflowEntry, workflowInfo *entity.Workflow) (err error) {
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1. 新增词条信息
	if len(insertEntry) > 0 {
		err = tx.Model(&entity.WorkflowEntry{}).
			CreateInBatches(&insertEntry, config.GetMainConfig().TaskFlow.MaxRow).
			Error
		if err != nil {
			log.ErrorContextf(ctx, "ImportWFEntry  CreateEntry Failed err:%v", err)
			return err
		}
	}

	// 2. 更新词条信息
	if len(updateEntry) > 0 {
		for _, entry := range updateEntry {
			err = tx.Model(&entity.WorkflowEntry{}).
				Where("f_entry_id = ? and f_parameter_id = ?", entry.EntryID, entry.ParameterID).
				Updates(map[string]interface{}{
					"f_entry_value":    entry.EntryValue,
					"f_entry_alias":    entry.EntryAlias,
					"f_release_status": entry.ReleaseStatus,
					"f_action":         entity.ActionUpdate,
				}).Error
			if err != nil {
				log.ErrorContextf(ctx, "ImportWFEntry UpdateEntry Failed err:%v", err)
				return err
			}
		}
	}

	////3. 更新参数词条关联表， 工作流状态表
	//err = txUpdateWorkflowParamRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflowInfo)
	//if err != nil {
	//	return err
	//}
	return nil
}

// GetWFEntryInvalidEntryRedisData 组装正确示例，错误示例需要写入redis的数据
func GetWFEntryInvalidEntryRedisData(ctx context.Context, entryInfos []*entity.WorkflowEntry,
	invalidEntries []*entity.WorkflowInvalidEntry) (entryRedisInfo []string,
	invalidEntryRedisInfo []string, err error) {
	log.InfoContextf(ctx, "GetWFEntryInvalidEntryRedisData len(entries):%+v,len(invalidEntries):%+v", len(entryInfos), len(invalidEntries))
	// 正确示例
	paramIdEntryMap := make(map[string]map[string]entity.WFParamEntryInfo)
	for _, v := range entryInfos {
		aliasArray := make([]string, 0)
		_ = json.Unmarshal([]byte(v.EntryAlias), &aliasArray)
		tmpEntryValue := entity.WFParamEntryInfo{Value: v.EntryValue, AliasName: aliasArray}

		if entryMap, ok := paramIdEntryMap[v.ParameterID]; ok {
			entryMap[v.EntryID] = tmpEntryValue
		} else {
			paramIdEntryMap[v.ParameterID] = map[string]entity.WFParamEntryInfo{v.EntryID: tmpEntryValue}
		}
	}

	for paramID, entryValMap := range paramIdEntryMap {
		redisEntryVal, err := json.Marshal(entryValMap)
		if err != nil {
			log.ErrorContextf(ctx, "GetFinalEntryInfos json.Marshal Failed,data:%+v,err:%+v", entryValMap, err)
			return nil, nil, err
		}
		entryRedisInfo = append(entryRedisInfo, paramID, string(redisEntryVal))
	}

	// 错误示例
	invalidParamIdEntryMap := make(map[string]map[string]entity.WFParamInvalidEntryInfo)
	for _, v := range invalidEntries {
		tmpInvalidEntryValue := entity.WFParamInvalidEntryInfo{Value: v.EntryValue}
		if entryMap, ok := invalidParamIdEntryMap[v.ParameterID]; ok {
			entryMap[v.EntryID] = tmpInvalidEntryValue
		} else {
			invalidParamIdEntryMap[v.ParameterID] = map[string]entity.WFParamInvalidEntryInfo{v.EntryID: tmpInvalidEntryValue}
		}
	}

	for paramID, entryValMap := range invalidParamIdEntryMap {
		redisEntryVal, err := json.Marshal(entryValMap)
		if err != nil {
			log.ErrorContextf(ctx, "GetFinalEntryInfos json.Marshal Failed,data:%+v,err:%+v", entryValMap, err)
			return nil, nil, err
		}
		invalidEntryRedisInfo = append(invalidEntryRedisInfo, paramID, string(redisEntryVal))
	}
	return entryRedisInfo, invalidEntryRedisInfo, nil
}

// RefreshWFEntryInvalidEntryRedisData 刷新正确示例，错误示例redis数据
func RefreshWFEntryInvalidEntryRedisData(ctx context.Context, env, appBizID string,
	entryRedis, invalidEntryRedis, deleteParamIds []string) error {
	log.InfoContextf(ctx, "RefreshWFEntryInvalidEntryRedisData,env:%s,deleteParamIds:%+v, "+
		"entries:%+v,invalidEntries:%+v", env, deleteParamIds, entryRedis, invalidEntryRedis)

	var err error

	// 正确示例redis清除
	entryKey := entity.GetWFParameterEntryKey(ctx, env, appBizID)
	if len(entryRedis) > 0 {
		err = database.GetRedis().HMSet(ctx, entryKey, entryRedis).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFEntry2Redis HMSET Failed! key:%s,val:%+v,err:%+v",
				entryKey, entryRedis, err)
			return err
		}
		log.InfoContextf(ctx, "SyncWFEntry2Redis HMSET Success! key:%s,val:%+v", entryKey, entryRedis)
	}

	// 错误示例redis数据清除
	invalidEntryKey := entity.GetWFParameterInvalidEntryKey(ctx, env, appBizID)
	if len(invalidEntryRedis) > 0 {
		err = database.GetRedis().HMSet(ctx, invalidEntryKey, invalidEntryRedis).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFInvalidEntry2Redis HMSET Failed! key:%s,val:%+v,err:%+v",
				invalidEntryKey, invalidEntryRedis, err)
			return err
		}
		log.InfoContextf(ctx, "SyncWFInvalidEntry2Redis HMSET Success! key:%s,val:%+v",
			invalidEntryKey, invalidEntryRedis)
	}

	if len(deleteParamIds) > 0 {
		err = database.GetRedis().HDel(ctx, entryKey, deleteParamIds...).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFEntry2Redis  HDEL Failed! key:%s,val:%+v,err:%+v",
				entryKey, entryRedis, err)
			return err
		}
		log.InfoContextf(ctx, "SyncWFEntry2Redis  HDEL Success! key:%s,val:%+v", entryKey, deleteParamIds)

		err = database.GetRedis().HDel(ctx, invalidEntryKey, deleteParamIds...).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFInvalidEntry2Redis HDEL Failed! key:%s,val:%+v,err:%+v",
				invalidEntryKey, invalidEntryRedis, err)
			return err
		}
		log.InfoContextf(ctx, "SyncWFInvalidEntry2Redis HDEL Success! key:%s,val:%+v",
			invalidEntryKey, invalidEntryRedis)
	}

	log.InfoContextf(ctx, "RefreshWFEntryInvalidEntryRedisData Success! env:%+v", env)

	return nil
}
