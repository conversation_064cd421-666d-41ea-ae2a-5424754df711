package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm/clause"
)

// GetWorkflowGuideViewedList 获取用户是否需要展示新手引导
func GetWorkflowGuideViewedList(ctx context.Context, uin, subUin string) ([]string, error) {
	log.InfoContextf(ctx, "GetWorkflowGuideViewedList|uin:%s|subUin:%s", uin, subUin)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var keys []string
	err := db.Model(&entity.WorkflowGuide{}).
		Where("f_uin = ? AND f_sub_uin = ? AND f_viewed = 1 AND f_is_deleted = 0", uin, subUin).
		Pluck("f_key", &keys).Error
	if err != nil {
		log.WarnContextf(ctx, "GetWorkflowGuideViewedList|uin:%s|subUin:%s|err:%+v", uin, subUin, err)
		return nil, err
	}
	return keys, nil
}

// MarkWorkflowGuideViewed 标记用户已查看新手引导
func MarkWorkflowGuideViewed(ctx context.Context, uin, subUin, key string) error {
	log.InfoContextf(ctx, "MarkWorkflowGuideViewed|uin:%s|subUin:%s", uin, subUin)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	guide := entity.WorkflowGuide{
		Uin:    uin,
		SubUin: subUin,
		Key:    key,
		Viewed: 1,
	}
	return db.Omit("f_create_time", "f_update_time").Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "f_uin"},
			{Name: "f_sub_uin"},
			{Name: "f_key"},
		},
		DoUpdates: clause.AssignmentColumns([]string{"f_viewed"}),
	}).Create(&guide).Error
}
