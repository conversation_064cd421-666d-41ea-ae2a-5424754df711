// Package db 错误示例[词条]相关DB操作
// @Author: reinhold
// @Date: 2024/2/28 16:03
package db

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
)

// GetInvalidEntryByParamID 根据参数id查找错误示例【词条】信息
func GetInvalidEntryByParamID(ctx context.Context, appBizID, paramID string) (
	entryInfos []*entity.WorkflowInvalidEntry, err error) {
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where(" f_robot_id = ? and f_parameter_id = ? and f_is_deleted= 0", appBizID, paramID).
		Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetInvalidEntryByParamID db.Find Failed, err:%v", err)
		return
	}
	return
}

// GetInvalidEntryByParamIDs 根据参数id批量查找错误示例【词条】信息
func GetInvalidEntryByParamIDs(ctx context.Context, appBizID string, paramIDs []string) (
	entryInfos []*entity.WorkflowInvalidEntry, err error) {
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where(" f_robot_id = ? and f_parameter_id in ? and f_is_deleted= 0", appBizID, paramIDs).
		Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetInvalidEntryByParamIDs db.Find Failed, err:%v", err)
		return
	}
	return
}

// CreateInvalidEntry 词条【错误示例】写入数据库
func CreateInvalidEntry(ctx context.Context, entryInfo *entity.WorkflowInvalidEntry, workflowInfo *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1. 词条写入db
	err = tx.WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).Create(&entryInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "CreateInvalidEntry Failed! data:%+v,err:%+v", entryInfo, err)
		return err
	}

	//// 2. 更新映射关系
	//uin, subUin := util.GetUinAndSubAccountUin(ctx)
	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin,
	//	entity.ActionUpdate, entryInfo.ParameterID, workflowInfo)
	//if err != nil {
	//	return err
	//}
	return nil
}

// UpdateInvalidEntry 更新词条【错误示例】
func UpdateInvalidEntry(ctx context.Context, appBizID, paramID, entryID, entryName string, workflow *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 更新t_invalid_entry
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = tx.WithContext(ctx).Debug().
		Exec("UPDATE t_invalid_entry "+
			"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
			"f_entry_value = ?,"+
			"f_uin = ?,"+
			"f_sub_uin = ?,"+
			"f_release_status = ? "+
			"WHERE f_entry_id = ? and f_robot_id = ? and f_is_deleted= 0",
			entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
			entity.ActionUpdate, entryName, uin, subUin,
			entity.ReleaseStatusUnPublished, entryID, appBizID).
		Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateInvalidEntry Failed!, err:%+v", err)
		return err
	}

	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflow)
	//if err != nil {
	//	return err
	//}
	return nil
}

// DeleteInvalidEntry 删除词条【错误示例】
func DeleteInvalidEntry(ctx context.Context, appBizID, paramID, entryID string, workflow *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where("f_entry_id = ? and f_robot_id = ? and f_is_deleted= 0", entryID, appBizID).
		Updates(map[string]interface{}{
			"f_uin":            uin,
			"f_sub_uin":        subUin,
			"f_is_deleted":     1,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         entity.ActionDelete,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "DeleteInvalidEntry Failed! err:%+v", err)
		return err
	}

	//err = txUpdateWorkflowEntryRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflow)
	//if err != nil {
	//	return err
	//}
	return nil
}

// GetInvalidEntryInfosByParamID 词条【错误示例】 列表
func GetInvalidEntryInfosByParamID(ctx context.Context, paramID, keyword string, pageOffset, pageLimit int) (int64, []*entity.WorkflowInvalidEntry, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where("f_parameter_id = ? and f_is_deleted= 0", paramID)

	if len(keyword) > 0 {
		likeKeyword := "%" + keyword + "%"
		db = db.Where("f_entry_value LIKE ? ", likeKeyword)
	}

	// total
	var total int64
	db = db.Count(&total)

	// 根据偏移量查询
	var entryInfos []*entity.WorkflowInvalidEntry
	err := db.Offset(pageOffset).Limit(pageLimit).Order("f_id DESC").Find(&entryInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowInvalidEntry Failed! err:%+v", err)
		return 0, entryInfos, err
	}
	return total, entryInfos, nil
}
