// bot-task-config-server
//
// @(#)workflow-model.go  星期四, 二月 27, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// GetWorkflowByModelName ...
func GetWorkflowByModelName(ctx context.Context, cropId uint64, modelName,
	env string) ([]*KEP_WF.ListWorkflowInfoByModelNameRsp_WorkflowInfo, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var customModels []entity.WorkflowCustomModel
	if err := db.Table(entity.WorkflowCustomModel{}.TableName()).
		Where("f_model_name=? AND f_corp_id=? AND f_is_deleted=0",
			modelName, cropId).Scan(&customModels).Error; err != nil {
		return nil, err
	}
	mm := make(map[string]struct{}, len(customModels))
	wfIds := make([]string, 0, len(customModels))
	for _, v := range customModels {
		if _, ok := mm[v.WorkflowID]; !ok {
			wfIds = append(wfIds, v.WorkflowID)
			mm[v.WorkflowID] = struct{}{}
		}
	}
	var workflowInfo = make([]*KEP_WF.ListWorkflowInfoByModelNameRsp_WorkflowInfo, 0)
	if len(wfIds) == 0 {
		return workflowInfo, nil
	}
	// 通过工作流Ids获取工作流信息
	var workflows []entity.Workflow
	if err := db.Table(entity.Workflow{}.TableName()).Where("f_is_deleted=0 AND "+
		"f_workflow_id IN (?)", wfIds).Scan(&workflows).Error; err != nil {
		return nil, err
	}
	for _, v := range workflows {
		workflowInfo = append(workflowInfo, &KEP_WF.ListWorkflowInfoByModelNameRsp_WorkflowInfo{
			WorkflowName: v.WorkflowName,
			WorkflowId:   v.WorkflowID,
			AppBizId:     v.RobotId,
		})
	}
	return workflowInfo, nil
}

// GetCustomModelByWfId ...
func GetCustomModelByWfId(ctx context.Context, WorkflowId string) ([]*entity.WorkflowCustomModel, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var customModels []*entity.WorkflowCustomModel
	if err := db.Table(entity.WorkflowCustomModel{}.TableName()).
		Where("f_workflow_id=? AND f_is_deleted=0",
			WorkflowId).Scan(&customModels).Error; err != nil {
		return nil, err
	}
	return customModels, nil
}
