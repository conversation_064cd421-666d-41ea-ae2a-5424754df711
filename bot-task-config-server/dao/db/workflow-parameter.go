// Package db 参数相关DB操作
// @Author: reinhold
// @Date: 2024/2/28 16:03
package db

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"gorm.io/gorm"
)

// GetWorkflowStatus 获取工作流的状态
func GetWorkflowStatus(releaseState, flowState string) string {
	if flowState == entity.FlowStateDraft {
		return entity.FlowStateDraft
	}

	if flowState == entity.FlowStateEnable && releaseState != entity.ReleaseStatusPublished {
		return entity.FlowStateChangeUnPublish // 已修改待发布
	}
	if releaseState == entity.ReleaseStatusPublished {
		return entity.FlowStatePublishChange // 已发布仍有修改
	}
	return flowState
}

// CheckParamNameExist 检查参数名是否存在
func CheckParamNameExist(ctx context.Context, paramIDs []string, unExpectParamID, paramName string) (
	paramInfo []entity.Parameter, err error) {

	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_parameter_name = ? and f_parameter_id in ?  "+
			"and f_parameter_id != ? and  f_is_deleted= 0", paramName, paramIDs, unExpectParamID).
		Find(&paramInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "CheckParamNameExist db.Find Failed, err:%v", err)
		return
	}
	return

}

// GetWorkflowParamInfoByAppParamID 根据参数ID查找参数信息
func GetWorkflowParamInfoByAppParamID(ctx context.Context, robotID string, paramID string) (
	workflowParam entity.WorkflowParameter, err error) {

	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).
		Where("f_robot_id = ?  and f_parameter_id = ? and f_is_deleted= 0", robotID, paramID).
		Find(&workflowParam).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowParamInfoByAppParamID db.Find Failed, err:%v", err)
		return
	}
	return
}

// GetParamByAppParamID 根据参数ID查找参数信息
func GetParamByAppParamID(ctx context.Context, robotID string, paramID string) (
	parameterInfo entity.Parameter, err error) {

	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_robot_id = ?  and f_parameter_id = ? and f_is_deleted= 0", robotID, paramID).
		Find(&parameterInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetParamByAppParamID db.Find Failed, err:%v", err)
		return
	}
	return
}

// CreateParam 参数写入数据库
func CreateParam(ctx context.Context, nodeID, nodeName string, paramInfo *entity.Parameter, workflowInfo *entity.Workflow) error {
	var err error
	var newWorkflow *entity.Workflow
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1. 写paramInfo到DB
	err = tx.WithContext(ctx).Table(entity.Parameter{}.TableName()).Create(&paramInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "CreateParam Failed! data:%+v,err:%+v", paramInfo, err)
		return err
	}

	// 2.写映射
	workflowParam := &entity.WorkflowParameter{
		AppBizID:      workflowInfo.RobotId,
		WorkFlowID:    workflowInfo.WorkflowID,
		NodeID:        nodeID,
		NodeName:      nodeName,
		ParameterID:   paramInfo.ParameterID,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}
	err = tx.WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).Create(&workflowParam).Error
	if err != nil {
		log.ErrorContextf(ctx, "create workflowParameter Failed! err:%+v", err)
		return err
	}
	// 3. 通知dm，2.7.5 单点调试需求
	if err = upsertParameterToDM(ctx, []*entity.Parameter{paramInfo}, workflowInfo.RobotId); err != nil {
		return err
	}
	// 4. 修改画布状态
	err, newWorkflow = UpdateWorkflowStatusFromSubAtom(ctx, tx, workflowInfo.WorkflowID)
	if err != nil {
		return err
	}

	// 5. 更新开关向量
	if err := SaveWorkflowVectorAndRedis(ctx, tx, newWorkflow, workflowInfo.RobotId,
		entity.VectorWorkflowEnable); err != nil {
		log.ErrorContextf(ctx, "CreateParam|SaveWorkflowCorpusToVector|err:%v", err)
		return err
	}

	return nil
}

// UpdateParam 更新参数信息
func UpdateParam(ctx context.Context, appBizID, paramID, paramName, paramDesc, paramType,
	correctExamples, incorrectExamples, customAsk string, customAskEnable bool, workflowInfo *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1. 更新db
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = tx.WithContext(ctx).Debug().
		Exec("UPDATE t_parameter "+
			"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
			"f_parameter_name = ?,"+
			"f_parameter_desc = ?,"+
			"f_parameter_type = ?,"+
			"f_correct_examples = ?,"+
			"f_incorrect_examples = ?,"+
			"f_uin = ?,"+
			"f_sub_uin = ?,"+
			"f_release_status = ?, f_custom_ask=?, f_custom_ask_enable=? "+
			"WHERE f_parameter_id = ? and f_robot_id = ? and f_is_deleted= 0",
			entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
			entity.ActionUpdate, paramName, paramDesc, paramType, correctExamples, incorrectExamples, uin, subUin,
			entity.ReleaseStatusUnPublished, customAsk, customAskEnable, paramID, appBizID).
		Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateParam Failed!, err:%+v", err)
		return err
	}
	paramInfo := &entity.Parameter{
		ParameterID:       paramID,
		ParameterName:     paramName,
		ParameterDesc:     paramDesc,
		ParameterType:     paramType,
		CorrectExamples:   correctExamples,
		IncorrectExamples: incorrectExamples,
		CustomAsk:         customAsk,
		CustomAskEnable:   customAskEnable,
		AppID:             appBizID,
	}

	// 3. 通知dm，2.7.5 单点调试需求
	if err = upsertParameterToDM(ctx, []*entity.Parameter{paramInfo}, workflowInfo.RobotId); err != nil {
		return err
	}

	err = txUpdateWorkflowParamRelationState(ctx, tx, uin, subUin, entity.ActionUpdate, paramID, workflowInfo)
	if err != nil {
		return err
	}
	return nil
}

// DeleteParam 删除参数信息
func DeleteParam(ctx context.Context, appBizID, paramID string, workflowInfo *entity.Workflow) error {
	var err error
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 1.更新db
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err = database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_parameter_id = ? and f_robot_id = ? and f_is_deleted= 0", paramID, appBizID).
		Updates(map[string]interface{}{
			"f_uin":            uin,
			"f_sub_uin":        subUin,
			"f_is_deleted":     1,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         entity.ActionDelete,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "DeleteParam Failed! err:%+v", err)
		return err
	}
	if len(paramID) > 0 {
		deleteParametersReq := &KEP_WF_DM.DeleteParametersInSandboxRequest{
			AppID:        appBizID,
			ParameterIDs: []string{paramID},
		}
		_, err = rpc.DeleteParametersInSandbox(ctx, deleteParametersReq)
		if err != nil {
			return err
		}
	}
	err = txUpdateWorkflowParamRelationState(ctx, tx, uin, subUin, entity.ActionDelete, paramID, workflowInfo)
	if err != nil {
		return err
	}
	return nil
}

// GetParamIDByNodeID 根据节点ID获取参数ID
func GetParamIDByNodeID(ctx context.Context, workflowID, nodeID string) ([]string, error) {
	var paramIDs []string
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).
		Where("f_workflow_id = ? and f_node_id = ? and f_is_deleted= 0", workflowID, nodeID).
		Pluck("f_parameter_id", &paramIDs).Order("f_id desc").Error
	if err != nil {
		log.ErrorContextf(ctx, "GetParamIDByNodeID Failed! err:%+v", err)
		return paramIDs, err
	}
	return paramIDs, nil
}

// GetParamNameByParamIds 根据节点ID获取当前节点下所有的名字
func GetParamNameByParamIds(ctx context.Context, paramIds []string) ([]string, error) {
	var paramNames []string
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_parameter_id in  ?  and f_is_deleted= 0", paramIds).
		Pluck("f_parameter_name", &paramNames).Order("f_id desc").Error
	if err != nil {
		log.ErrorContextf(ctx, "GetParamIDByNodeID Failed! err:%+v", err)
		return paramNames, err
	}
	return paramNames, nil
}

// GetParamInfosByParamIDs 根据参数ID获取参数信息
func GetParamInfosByParamIDs(ctx context.Context, paramIDs []string, pageSize, pageNum int) (int64, []*entity.Parameter, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_parameter_id in ? and f_is_deleted= 0", paramIDs)

	// total
	var total int64
	db = db.Count(&total)

	// 根据偏移量查询
	var paramInfos []*entity.Parameter
	offset := (pageNum - 1) * pageSize
	err := db.Offset(offset).Limit(pageSize).Order("f_id ASC").Find(&paramInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetParamInfosByParamIDs Failed! err:%+v", err)
		return 0, paramInfos, err
	}
	return total, paramInfos, nil
}

// GetWorkflowIDByParamID 根据paramID获workflowParam信息
func GetWorkflowIDByParamID(ctx context.Context, paramID string) (entity.WorkflowParameter, error) {
	var workflowParam entity.WorkflowParameter
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).
		Where("f_parameter_id = ? and f_is_deleted = 0", paramID).Find(&workflowParam).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowIDByParamID Failed! err:%+v", err)
		return workflowParam, err
	}
	return workflowParam, nil
}

// CheckWorkflowStatusByParamID 检查工作流的状态，是否处于发布状态中，发布状态不允许编辑
func CheckWorkflowStatusByParamID(ctx context.Context, appBizID, paramID string) (workflowInfo *entity.Workflow, err error) {

	workflowParam, err := GetWorkflowIDByParamID(ctx, paramID)
	if err != nil {
		return workflowInfo, err
	}
	workflowInfo, err = GetWorkflowDetail(ctx, workflowParam.WorkFlowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return workflowInfo, errors.ErrWorkflowNotFound
		}
		return workflowInfo, err
	}

	if workflowInfo.ReleaseStatus == entity.ReleaseStatusPublishing {
		return workflowInfo, errors.ErrWorkflowPublishing
	}

	return workflowInfo, nil
}

// NodeParamInfos 节点参数信息
type NodeParamInfos struct {
	WorkFlowID        string `gorm:"column:f_workflow_id"`        // 工作流ID
	NodeName          string `gorm:"column:f_node_name"`          // 工作流中的Node名称
	ParameterID       string `gorm:"column:f_parameter_id"`       // 参数ID
	ParameterName     string `gorm:"column:f_parameter_name"`     // 参数Name
	ParameterDesc     string `gorm:"column:f_parameter_desc"`     // 参数Name
	ParameterType     string `gorm:"column:f_parameter_type"`     // 参数Name
	CorrectExamples   string `gorm:"column:f_correct_examples"`   // 正确示例
	IncorrectExamples string `gorm:"column:f_incorrect_examples"` // 错误示例
	CustomAsk         string `gorm:"column:f_custom_ask"`         //自定义回复话术
	CustomAskEnable   bool   `gorm:"column:f_custom_ask_enable"`  // 开关
}

// GetNodeParamInfoByAppID 通过应用id查询工作流节点参数信息
func GetNodeParamInfoByAppID(ctx context.Context, appBizID, flowID, keyword string, pageOffset, pageLimit int) (int64, []*NodeParamInfos, error) {

	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	orderStr := fmt.Sprintf("CASE f_workflow_id WHEN '%s' THEN 0 ELSE 1 END", flowID)
	db = db.Table(entity.WorkflowParameter{}.TableName()).
		Joins("inner join t_parameter on t_parameter.f_parameter_id =  t_workflow_parameter.f_parameter_id").
		Where("t_workflow_parameter.f_is_deleted = 0 and t_workflow_parameter.f_robot_id = ?", appBizID).
		Select(
			"t_workflow_parameter.f_workflow_id as f_workflow_id, " +
				"t_workflow_parameter.f_node_name as f_node_name, " +
				"t_workflow_parameter.f_parameter_id as f_parameter_id," +
				"t_parameter.f_parameter_name as f_parameter_name," +
				"t_parameter.f_parameter_desc as f_parameter_desc ," +
				"t_parameter.f_parameter_type as f_parameter_type ," +
				"t_parameter.f_correct_examples as f_correct_examples ," +
				"t_parameter.f_incorrect_examples as f_incorrect_examples, " +
				"t_parameter.f_custom_ask as f_custom_ask, " +
				"t_parameter.f_custom_ask_enable as f_custom_ask_enable ").
		Order(orderStr).
		Order("t_workflow_parameter.f_workflow_id  DESC").
		Order("t_workflow_parameter.f_node_id  DESC").
		Order("t_parameter.f_id ASC")

	if len(keyword) > 0 {
		likeKeyword := "%" + keyword + "%"
		db = db.Where("f_parameter_name LIKE ?  ", likeKeyword)
	}

	// total
	var total int64
	db = db.Count(&total)

	var results []*NodeParamInfos
	err := db.Limit(pageLimit).Offset(pageOffset).Find(&results).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetNodeParamInfoByAppID Failed! err:%+v", err)
		return 0, nil, err
	}

	return total, results, nil
}

// TxHandleCopyParam 事务的形式拷贝参数
func TxHandleCopyParam(ctx context.Context, curParamID string, paramInfo entity.Parameter,
	workflowParam entity.WorkflowParameter, workflowInfo *entity.Workflow) error {
	var err error
	var newWorkflow *entity.Workflow
	tx := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	appBizID := paramInfo.AppID

	if len(curParamID) == 0 {
		// 完全新增的时候的处理逻辑
		// 写param
		err = tx.Model(&entity.Parameter{}).Create(&paramInfo).Error
		if err != nil {
			log.ErrorContextf(ctx, "TxHandleCopyParam Create Parameter Failed! err:%+v", err)
			return err
		}

		// 建立关联关系
		err = tx.Model(&entity.WorkflowParameter{}).Create(&workflowParam).Error
		if err != nil {
			log.ErrorContextf(ctx, "TxHandleCopyParam Create WorkflowParameter Failed! err:%+v", err)
			return err
		}

	} else {
		// 覆盖原来参数的处理逻辑
		// 1. 更新 t_parameter 状态
		err = tx.WithContext(ctx).Debug().
			Exec("UPDATE t_parameter "+
				"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
				"f_parameter_name = ?,"+
				"f_parameter_desc = ?,"+
				"f_parameter_type = ?,"+
				"f_correct_examples = ?,"+
				"f_incorrect_examples = ?,"+
				"f_uin = ?,"+
				"f_sub_uin = ?,"+
				"f_release_status = ? "+
				"WHERE f_parameter_id = ? and f_robot_id = ? and f_is_deleted= 0",
				entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
				entity.ActionUpdate, paramInfo.ParameterName, paramInfo.ParameterDesc, paramInfo.ParameterType,
				paramInfo.CorrectExamples, paramInfo.IncorrectExamples,
				paramInfo.UIN, paramInfo.SubUIN,
				entity.ReleaseStatusUnPublished, curParamID, appBizID).
			Error
		if err != nil {
			log.ErrorContextf(ctx, "CopyParam|UpdateParam Failed!, err:%+v", err)
			return err
		}

		// 2. 更新 t_workflow_parameter参数状态
		err = tx.Model(&entity.WorkflowParameter{}).
			Where("f_robot_id = ? and f_parameter_id = ? and f_is_deleted = 0", appBizID, curParamID).
			Updates(map[string]interface{}{
				"f_action":         entity.ActionUpdate,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error
	}

	// 3. 修改画布状态
	err, newWorkflow = UpdateWorkflowStatusFromSubAtom(ctx, tx, workflowInfo.WorkflowID)
	if err != nil {
		return err
	}

	// 4. 更新向量
	if err := SaveWorkflowVectorAndRedis(ctx, tx, newWorkflow, workflowInfo.RobotId,
		entity.VectorWorkflowEnable); err != nil {
		log.ErrorContextf(ctx, "TxHandleCopyParam|SaveWorkflowCorpusToVector|err:%v", err)
		return err
	}

	return nil
}

// DeleteParameterDataByDeleteApp 删除应用的时候删除参数相关数据
func DeleteParameterDataByDeleteApp(ctx context.Context, appBizID uint64) error {
	var batchSize int64 = 1000

	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	// 删除t_workflow_parameter表
	deleteWorkflowParameter := "DELETE FROM t_workflow_parameter WHERE f_robot_id = ? LIMIT ?"
	var rowsAffect = batchSize
	for rowsAffect >= batchSize {
		result := db.Exec(deleteWorkflowParameter, appBizID, batchSize)
		if result.Error != nil {
			log.ErrorContextf(ctx, "Delete t_workflow_parameter Failed! err:%+v", result.Error)
			return result.Error
		}
		rowsAffect = result.RowsAffected
		// sleep一下， 避免锁死数据库
		time.Sleep(60 * time.Millisecond)
	}
	log.InfoContextf(ctx, "DeleteParameterDataByDeleteApp|deltet t_workflow_parameter success!")

	// 删除parameter表
	deleteParameter := "DELETE FROM t_parameter WHERE f_robot_id = ? LIMIT ?"
	var rowsAffect1 = batchSize
	for rowsAffect1 >= batchSize {
		result := db.Exec(deleteParameter, appBizID, batchSize)
		if result.Error != nil {
			log.ErrorContextf(ctx, "Delete t__parameter Failed! err:%+v", result.Error)
			return result.Error
		}
		rowsAffect1 = result.RowsAffected
		// sleep一下， 避免锁死数据库
		time.Sleep(60 * time.Millisecond)
	}

	log.InfoContextf(ctx, "DeleteParameterDataByDeleteApp|deltet t_parameter success!")
	return nil
}

// txUpdateWorkflowParamRelationState 从参数往上修改一些列相关的表的状态
func txUpdateWorkflowParamRelationState(ctx context.Context, tx *gorm.DB, uin, subUin, middleTableAction,
	paramID string, workflowInfo *entity.Workflow) (err error) {
	var newWorkflow *entity.Workflow
	isDelete := 0
	if middleTableAction == entity.ActionDelete {
		isDelete = 1
	}

	// 更新映射关系
	err = tx.WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).
		Where("f_parameter_id = ? and f_is_deleted= 0", paramID).
		Updates(map[string]interface{}{
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_action":         middleTableAction,
			"f_is_deleted":     isDelete,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "update workflowParameter Failed! err:%+v", err)
		return err
	}

	// 修改画布状态
	err, newWorkflow = UpdateWorkflowStatusFromSubAtom(ctx, tx, workflowInfo.WorkflowID)
	if err != nil {
		return err
	}

	// 更新向量
	if err := SaveWorkflowVectorAndRedis(ctx, tx, newWorkflow, workflowInfo.RobotId,
		entity.VectorWorkflowEnable); err != nil {
		log.ErrorContextf(ctx, "txUpdateWorkflowParamRelationState|SaveWorkflowCorpusToVector|err:%v", err)
		return err
	}

	return nil
}
