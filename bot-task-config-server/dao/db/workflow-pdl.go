/*
 * 2024-12-22
 * Copyright (c) 2024. le<PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package db

import (
	"context"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gorm.io/gorm"
)

const (
	convertingState         wfTransformState = 8  // 转换中
	convertedFailedState    wfTransformState = 9  // 转换失败
	convertedState          wfTransformState = 10 // 已转换
	convertedPublishedState wfTransformState = 11 // 已发布-已转换
)

// GetWorkflowParamInfos 从工作流获取参数
func GetWorkflowParamInfos(ctx context.Context, workflowID, robotID string) (*KEP_WF.ParameterInfoList, error) {
	var paramInfos []*entity.Parameter
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	err := db.Table("t_parameter as p").
		Joins("inner join t_workflow_parameter as wp on p.f_parameter_id = wp.f_parameter_id").
		Where("wp.f_workflow_id = ?", workflowID).
		Where("wp.f_robot_id = ?", robotID).
		Where("wp.f_is_deleted = 0").
		Find(&paramInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowParamInfos|err:%+v", err)
		return nil, err
	}

	parameterInfoList := &KEP_WF.ParameterInfoList{}
	for _, v := range paramInfos {
		paramInfo := &KEP_WF.ParameterInfo{
			ParameterId:       v.ParameterID,
			Name:              v.ParameterName,
			Desc:              v.ParameterDesc,
			Type:              v.ParameterType,
			CorrectExamples:   util.GetParameterExamplesArr(ctx, v.CorrectExamples),
			IncorrectExamples: util.GetParameterExamplesArr(ctx, v.IncorrectExamples),
		}
		parameterInfoList.List = append(parameterInfoList.List, paramInfo)
	}

	return parameterInfoList, nil
}

// ConvertWorkflowToPDL 转换任务
func ConvertWorkflowToPDL(ctx context.Context, corpID uint64, workflow *entity.Workflow) (*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowPDL *entity.WorkflowPDL

	// 获取工作流参数
	parameterInfoList, err := GetWorkflowParamInfos(ctx, workflow.WorkflowID, workflow.RobotId)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPDL|GetWorkflowParamInfos|err:%+v", err)
		return nil, err
	}

	// pbToJson
	parameterJson, err := protoutil.ParameterInfoListToJson(parameterInfoList)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPDL|Parameter toJson|err:%+v", err)
		return nil, err
	}

	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		workflowPDL, err = tryCreateConvertingWorkflowPDLByTx(ctx, tx, workflow, parameterJson)
		if err != nil {
			return err
		}

		err = createConvertWorkflowToPDLTask(ctx, corpID, workflowPDL)
		return err
	}); err != nil {
		log.WarnContextf(ctx, "ConvertWorkflowToPDL|fail, err:%+v", err)
		return nil, err
	}

	return workflowPDL, nil
}

// tryCreateConvertingWorkflowPDLByTx
func tryCreateConvertingWorkflowPDLByTx(ctx context.Context, tx *gorm.DB, workflow *entity.Workflow, parameter string) (*entity.WorkflowPDL, error) {
	var workflowPDL entity.WorkflowPDL
	var err error
	botBizId := workflow.RobotId
	workflowID := workflow.WorkflowID
	// 查询是否存在PDL
	err = tx.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_robot_id = ? AND f_workflow_id = ? AND f_is_deleted = 0", botBizId, workflowID).
		Take(&workflowPDL).Error // 使用 Take 只获取一个记录
	if err == nil {
		log.InfoContextf(ctx, "tryCreateConvertingWorkflowPDLByTx|existedPDL:%+v", workflowPDL)

		// 如果存在PDL，判断是否可以转换
		err = handleConvertingWorkflowPDL(ctx, &workflowPDL)
		if err != nil {
			return nil, err
		}

		// 更新PDL状态为转换中，更新画布内容
		workflowPDL.DialogJsonEnable = workflow.DialogJsonEnable
		workflowPDL.Parameter = parameter
		workflowPDL.PdlCreateTime = workflow.UpdateTime
		workflowPDL.StaffID = workflow.StaffID
		workflowPDL.WorkflowState = getWorkflowPDLConvertingState(&workflowPDL)
		// 更新PDL（WF，参数，状态，操作人）
		updateMap := map[string]interface{}{
			"f_dialog_json_enable": workflowPDL.DialogJsonEnable,
			"f_parameter":          workflowPDL.Parameter,
			"f_pdl_time":           workflowPDL.PdlCreateTime,
			"f_flow_state":         workflowPDL.WorkflowState,
			"f_staff_id":           workflowPDL.StaffID,
		}

		err := tx.Table(workflowPDL.TableName()).
			Where("f_workflow_id=? AND f_robot_id=?", workflowPDL.WorkflowID, workflowPDL.RobotId).
			Updates(updateMap).
			Error
		if err != nil {
			log.ErrorContextf(ctx, "tryCreateConvertingWorkflowPDLByTx|UpdatePDL|err:%v", err)
			return nil, err
		}
	} else if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "tryCreateConvertingWorkflowPDLByTx|PDL not exist, prepare to create, workflowID:%+v", workflow.WorkflowID)
		workflowPDL = createConvertingWorkflowPDL(workflow, parameter)
		err = tx.Table(workflowPDL.TableName()).
			Create(&workflowPDL).
			Error
		if err != nil {
			log.ErrorContextf(ctx, "tryCreateConvertingWorkflowPDLByTx|create PDL err:%v", err)
			return nil, err
		}
	} else {
		log.ErrorContextf(ctx, "tryCreateConvertingWorkflowPDLByTx|GetWorkflowPDLDetail|err:%+v", err)
		return nil, errors.WorkflowUnableConvertError("数据库异常")
	}

	return &workflowPDL, nil
}

// createConvertingWorkflowPDL
func createConvertingWorkflowPDL(workflow *entity.Workflow, parameter string) entity.WorkflowPDL {
	// 创建初始化的PDL，第一次创建状态为"转换中"
	return entity.WorkflowPDL{
		PdlID:            idgenerator.NewUUID(),
		WorkflowID:       workflow.WorkflowID,
		WorkflowName:     workflow.WorkflowName,
		WorkflowState:    entity.WorkflowPdlStateConverting,
		Version:          0,
		RobotId:          workflow.RobotId,
		DialogJsonEnable: workflow.DialogJsonEnable,
		Parameter:        parameter,
		PdlContent:       "",
		ToolsInfo:        "",
		UserConstraints:  "",
		StaffID:          workflow.StaffID,
		ReleaseStatus:    entity.WorkflowPdlReleaseStatusUnPublished,
		IsDeleted:        0,
		IsEnable:         false,
		Action:           entity.ActionInsert,
		PdlCreateTime:    workflow.UpdateTime,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}
}

// UpdateWorkflowPDLConvertState 更新PDL状态
func UpdateWorkflowPDLConvertState(ctx context.Context, params entity.UpdateWorkflowPDLConvertStateParams) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		workflowPDL := entity.WorkflowPDL{}
		err = tx.Table(entity.WorkflowPDL{}.TableName()).
			Where("f_robot_id = ? AND f_workflow_id = ? AND f_is_deleted = 0", params.AppBizID, params.WorkflowID).
			Take(&workflowPDL).Error // 使用 Take 只获取一个记录
		if err != nil {
			return err
		}

		// 更新转换状态
		state := ""
		if params.IsConverted {
			state = getWorkflowPDLConvertedState(&workflowPDL)
		} else if params.IsConverting {
			state = getWorkflowPDLConvertingState(&workflowPDL)
		} else {
			state = getWorkflowPDLConvertedFailedState(&workflowPDL)
		}

		return updateWorkflowPDLStateByTx(ctx, tx, workflowPDL.WorkflowID,
			workflowPDL.RobotId, workflowPDL.WorkflowState, state, params.FailedReason)
	}); err != nil {
		log.WarnContextf(ctx, "ConvertWorkflowToPDL|fail, err:%+v", err)
		return err
	}
	return nil
}

// updateWorkflowPDLStateByTx 更新PDL状态
func updateWorkflowPDLStateByTx(ctx context.Context, tx *gorm.DB, workflowID, robotID string, oldStatus, status, failed string) error {
	workflowPDL := &entity.WorkflowPDL{
		WorkflowState: status,
		FailReason:    failed,
	}

	if err := tx.Table(entity.WorkflowPDL{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.WorkflowPdlColumns.WorkflowID), workflowID).
		Where(fmt.Sprintf("%s = ?", entity.WorkflowPdlColumns.RobotID), robotID).
		Where(fmt.Sprintf("%s = ?", entity.WorkflowPdlColumns.WorkflowState), oldStatus).
		Updates(workflowPDL).Error; err != nil {
		log.ErrorContextf(ctx, "updateWorkflowPDLStateByTx|err:%v", err)
		return err
	}
	return nil
}

// handleConvertingWorkflowPDL 检查转换中的状态
func handleConvertingWorkflowPDL(ctx context.Context, workflowPDL *entity.WorkflowPDL) error {
	var errConvert error
	if workflowPDL == nil {
		return errors.ErrWorkflowNotFound
	}
	if workflowPDL.IsWorkflowPDLConverting() {
		log.ErrorContextf(ctx, "handleConvertingWorkflowPDL|State Status:%s", workflowPDL.WorkflowState)
		// 如果长时间没有变化的处理（超过1小时），直接置换成失败并返回错误
		convertingFromLastUpdate := time.Since(workflowPDL.UpdateTime)
		isConvertingTooLong := convertingFromLastUpdate.Seconds() > 3600
		if isConvertingTooLong {
			log.ErrorContextf(ctx, "handleConvertingWorkflowPDL|convertingFromLastUpdate:%+v", convertingFromLastUpdate)
			errConvert = errors.ErrWorkflowConvertingTooLong
		} else {
			errConvert = errors.ErrWorkflowConverting
		}

		updateStateParams := entity.UpdateWorkflowPDLConvertStateParams{
			WorkflowID:        workflowPDL.WorkflowID,
			AppBizID:          workflowPDL.RobotId,
			IsConvertedFailed: true,
			FailedReason:      errConvert.Error(),
		}

		if err := UpdateWorkflowPDLConvertState(ctx, updateStateParams); err != nil {
			log.ErrorContextf(ctx, "handleConvertingWorkflowPDL, UpdateWorkflowPDLState|err:%+v", err)
			return err
		}

		return errConvert
	} else if workflowPDL.ReleaseStatus == entity.WorkflowPdlReleaseStatusPublishing {
		log.ErrorContextf(ctx, "handleConvertingWorkflowPDL|Release Status:%s", workflowPDL.ReleaseStatus)
		return errors.ErrWorkflowPublishingNotAllowConvert
	}

	return errConvert
}

// ListWorkflowPDL 获取PDL工作流列表
func ListWorkflowPDL(ctx context.Context, workflowIds []string) ([]*entity.WorkflowPDL, error) {
	workflows := make([]*entity.WorkflowPDL, 0)

	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)

	res := db.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_workflow_id IN ? AND f_is_deleted = 0", workflowIds)

	res = res.Scan(&workflows)
	if res.Error != nil {
		log.ErrorContextf(ctx, "ListWorkflowPDL|%s|err:%+v", sid, res.Error)
		return nil, res.Error
	}
	return workflows, nil
}

// GetWorkflowPDLDetail 获取PDL工作流详情
func GetWorkflowPDLDetail(ctx context.Context, workflowID, botBizId string) (*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowPDL entity.WorkflowPDL
	// 查询符合条件的记录
	err := db.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_robot_id = ? AND f_workflow_id = ? AND f_is_deleted = 0", botBizId, workflowID).
		Take(&workflowPDL).Error // 使用 Take 只获取一个记录
	if err != nil {
		log.WarnContextf(ctx, "GetWorkflowPDLDetail|botId:%s|:workflowID:%s|err:%+v",
			botBizId, workflowID, err)
		return nil, err
	}
	return &workflowPDL, nil
}

// GetProdPDLDetail 发布库中PDL的具体信息
func GetProdPDLDetail(ctx context.Context, workflowID, botBizId string) (*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	var workflowPDL entity.WorkflowPDL
	// 查询符合条件的记录
	err := db.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_robot_id = ? AND f_workflow_id = ? AND f_is_deleted = 0", botBizId, workflowID).
		Take(&workflowPDL).Error // 使用 Take 只获取一个记录

	if err != nil {
		log.ErrorContextf(ctx, "GetProdPDLDetail|botId:%s|:workflowID:%s|err:%+v", botBizId, workflowID, err)
		return nil, err
	}
	return &workflowPDL, nil
}

// SaveWorkflowPDL 更新工作流
func SaveWorkflowPDL(ctx context.Context, params entity.ModifyWorkflowPDLParams) (*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var newWorkflow *entity.WorkflowPDL
	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		// 更新pdl版本表
		err = updatePDLVersion(ctx, tx, params)
		if err != nil {
			return err
		}
		// 更新pdl表
		newWorkflow, err = editWorkflowPDLByTx(ctx, tx, params)
		return err
	}); err != nil {
		log.WarnContextf(ctx, "SaveWorkflowPDL|err:%+v", err)
		return nil, err
	}
	return newWorkflow, nil
}

// 更新PDL时同步更新t_pdl_version表的内容
func updatePDLVersion(ctx context.Context, tx *gorm.DB, params entity.ModifyWorkflowPDLParams) error {
	updateMap := map[string]interface{}{
		entity.PDLVersionColumns.PdlContentModified:      params.PdlContent,
		entity.PDLVersionColumns.ToolsInfoModified:       params.ApiInfo,
		entity.PDLVersionColumns.UserConstraintsModified: params.Constraints,
	}
	err := tx.Model(&entity.PDLVersion{}).
		Where(fmt.Sprintf("%s = ? AND %s = ?", entity.PDLVersionColumns.PdlID, entity.PDLVersionColumns.PdlSnapshotVersion),
			params.PdlID, params.PdlSnapshotVersion).Updates(updateMap).Error
	if err != nil {
		log.ErrorContextf(ctx, "updatePDLVersion|err:%+v", err)
		return err
	}
	return nil
}

// SaveConvertPDL 保存转换后的的PDL，仅在画布转换PDL时调用，会创建新的版本
func SaveConvertPDL(ctx context.Context, pdl *entity.WorkflowPDL) (*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var newWorkflow *entity.WorkflowPDL
	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		// 版本号+1
		pdl.PdlSnapshotVersion++
		// 1. pdl版本表创建新的记录
		pdlVersion := entity.PDLVersion{
			PdlID:                   pdl.PdlID,
			PdlSnapshotVersion:      pdl.PdlSnapshotVersion,
			WorkflowID:              pdl.WorkflowID,
			RobotID:                 pdl.RobotId,
			WorkflowName:            pdl.WorkflowName,
			DialogJsonEnable:        pdl.DialogJsonEnable,
			Parameter:               pdl.Parameter,
			PdlContent:              pdl.PdlContent,
			ToolsInfo:               pdl.ToolsInfo,
			UserConstraints:         pdl.UserConstraints,
			PdlContentModified:      pdl.PdlContent,
			ToolsInfoModified:       pdl.ToolsInfo,
			UserConstraintsModified: pdl.UserConstraints,
		}
		if err = tx.Create(&pdlVersion).Error; err != nil {
			log.ErrorContextf(ctx, "SaveConvertPDL create pdl version error|err:%+v", err)
			return err
		}
		// 2. 更新pdl表
		modifyPDL := entity.ModifyWorkflowPDLParams{
			PdlSnapshotVersion: pdl.PdlSnapshotVersion,
			WorkflowID:         pdl.WorkflowID,
			WorkflowName:       pdl.WorkflowName,
			Version:            pdl.Version,
			PdlContent:         pdl.PdlContent,      // 覆盖更新
			ApiInfo:            pdl.ToolsInfo,       // 覆盖更新
			Constraints:        pdl.UserConstraints, // 保留
			StaffID:            pdl.StaffID,
			UpdateTime:         time.Now(),
			AppBizID:           pdl.RobotId,
			IsDebug:            uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			IsConverted:        true,
		}
		newWorkflow, err = editWorkflowPDLByTx(ctx, tx, modifyPDL)
		return err
	}); err != nil {
		log.WarnContextf(ctx, "SaveWorkflowPDL|err:%+v", err)
		return nil, err
	}
	return newWorkflow, nil
}

// editWorkflowPDLByTx 事物更新工作流
func editWorkflowPDLByTx(ctx context.Context, tx *gorm.DB, params entity.ModifyWorkflowPDLParams) (*entity.WorkflowPDL, error) {
	// 构造新的工作流
	newWorkflow := entity.WorkflowPDL{
		PdlSnapshotVersion: params.PdlSnapshotVersion,
		WorkflowID:         params.WorkflowID,
		WorkflowName:       params.WorkflowName,
		PdlContent:         params.PdlContent,
		ToolsInfo:          params.ApiInfo,
		UserConstraints:    params.Constraints,
		StaffID:            params.StaffID,
		Parameter:          params.Parameter,
		FailReason:         "",
	}

	// 1. 取db里的工作流
	oldWorkflow := entity.WorkflowPDL{}
	oldWorkflowResult := tx.
		Table(entity.WorkflowPDL{}.TableName()).
		Where("f_workflow_id = ? AND f_robot_id = ? AND f_is_deleted=0", params.WorkflowID, params.AppBizID).
		Find(&oldWorkflow)
	if oldWorkflowResult.Error != nil {
		log.WarnContextf(ctx, "editWorkflowPDLByTx|err:%v", oldWorkflowResult.Error)
		return nil, oldWorkflowResult.Error
	}

	log.InfoContextf(ctx, "editWorkflowPDLByTx|Req.version:%d|db.version:%d", params.Version, oldWorkflow.Version)
	if params.Version != oldWorkflow.Version {
		return nil, errors.ErrWorkflowWrongVersion
	}
	newWorkflow.PdlID = oldWorkflow.PdlID
	newWorkflow.Version = oldWorkflow.Version + 1
	newWorkflow.DialogJsonEnable = oldWorkflow.DialogJsonEnable
	newWorkflow.Parameter = oldWorkflow.Parameter // 只读
	newWorkflow.WorkflowState = oldWorkflow.WorkflowState
	newWorkflow.ReleaseStatus = oldWorkflow.ReleaseStatus
	newWorkflow.Action = oldWorkflow.Action
	newWorkflow.IsEnable = oldWorkflow.IsEnable
	// 如果是调试保存，默认启用
	if params.IsDebug == uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE) {
		newWorkflow.IsEnable = true
	}

	equal := oldWorkflow.PdlContent == newWorkflow.PdlContent &&
		oldWorkflow.ToolsInfo == newWorkflow.ToolsInfo &&
		oldWorkflow.UserConstraints == newWorkflow.UserConstraints
	log.InfoContextf(ctx, "editWorkflowPDLByTx|equal:%v", equal)

	// 转换后的保存
	if params.IsConverted && oldWorkflow.IsWorkflowPDLConverting() {
		oldWorkflow.WorkflowState = getWorkflowPDLConvertedState(&oldWorkflow)
	}

	// 如果调试保存，触发了保存 || 转换
	if params.IsConverted || params.IsDebug == uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE) || !equal {
		flowState, action, err := TransformWorkflowPDLState(ctx, oldWorkflow, params.IsDebug)
		if err != nil {
			log.ErrorContextf(ctx, "editWorkflowPDLByTx|TransformWorkflowState|err:%v", err)
			return nil, err
		}
		newWorkflow.WorkflowState = flowState
		newWorkflow.Action = action
		newWorkflow.ReleaseStatus = entity.WorkflowPdlReleaseStatusUnPublished
	}

	// 2. 更新工作流，不直接用newWorkflow，解决零值被忽略问题
	if err := oldWorkflowResult.Updates(map[string]interface{}{
		"f_pdl_snapshot_version": newWorkflow.PdlSnapshotVersion,
		"f_flow_state":           newWorkflow.WorkflowState,
		"f_version":              newWorkflow.Version,
		"f_pdl_content":          newWorkflow.PdlContent,
		"f_tools_info":           newWorkflow.ToolsInfo,
		"f_user_constraints":     newWorkflow.UserConstraints,
		"f_staff_id":             newWorkflow.StaffID,
		"f_release_status":       newWorkflow.ReleaseStatus,
		"f_action":               newWorkflow.Action,
		"f_is_enable":            newWorkflow.IsEnable,
		"f_fail_reason":          newWorkflow.FailReason,
	}).Error; err != nil {
		log.WarnContextf(ctx, "editWorkflowPDLByTx|Updates|newWorkflow err:%v", err)
		return nil, err
	}

	log.InfoContextf(ctx, "editWorkflowPDLByTx|success!")

	return &newWorkflow, nil
}

// GetWorkflowPDLsByFlowIds ...
func GetWorkflowPDLsByFlowIds(ctx context.Context, flowIds []string, robotId string) ([]*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.WorkflowPDL, 0)

	if err := db.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id IN ?", robotId, flowIds).
		Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLsByFlowIds|err:%+v", err)
		return workflows, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// GetEnabledWorkflowPDLs ...
func GetEnabledWorkflowPDLs(ctx context.Context, robotId string) ([]*entity.WorkflowPDL, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.WorkflowPDL, 0)

	if err := db.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_is_enable=?", robotId, true).
		Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetEnabledWorkflowPDLs|err:%+v", err)
		return workflows, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// GetWorkflowPDLInfo 获取pdl工作流
func GetWorkflowPDLInfo(ctx context.Context, workflowID string, robotId string, envType uint32) ([]*entity.WorkflowPDL, error) {
	if len(robotId) == 0 {
		return nil, nil
	}
	workflows := make([]*entity.WorkflowPDL, 0)
	var db *gorm.DB
	var query string
	var args []interface{}
	switch KEP_WF.GetAgentWorkflowInfoReq_EnvType(int32(envType)) {
	case KEP_WF.GetAgentWorkflowInfoReq_TEST: // 测试环境
		db = database.GetLLMRobotWorkflowGORM().Debug()
		if len(workflowID) > 0 {
			query = fmt.Sprintf("%s = ? AND %s = ? AND %s = ? AND f_is_deleted=0",
				entity.WorkflowPdlColumns.WorkflowID, entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.IsEnable)
			args = append(args, workflowID)
		} else {
			query = fmt.Sprintf("%s = ? AND %s = ? AND f_is_deleted=0",
				entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.IsEnable)
		}
		args = append(args, robotId)
		args = append(args, true)

	case KEP_WF.GetAgentWorkflowInfoReq_PROD: // 线上环境
		db = database.GetLLMRobotWorkflowProdGORM().Debug()
		if len(workflowID) > 0 {
			query = fmt.Sprintf("%s = ? AND  %s = ? AND %s = ? AND f_is_deleted=0",
				entity.WorkflowPdlColumns.WorkflowID, entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.ReleaseStatus)
			args = append(args, workflowID)
		} else {
			query = fmt.Sprintf("%s = ? AND %s = ? AND f_is_deleted=0",
				entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.ReleaseStatus)
		}
		args = append(args, robotId)
		args = append(args, entity.WorkflowPdlReleaseStatusPublished)
	default:
		log.ErrorContextf(ctx, "GetWorkflowPDLInfo|envType: %d illegal", envType)
		return nil, fmt.Errorf("envType: %d illegal", envType)
	}

	err := db.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(query, args...).Scan(&workflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLInfo|db.Count err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetWorkflowPDLInfo success")

	return workflows, nil
}

// SwitchWorkflowPDLState 切换工作流状态
func SwitchWorkflowPDLState(ctx context.Context, robotID, workflowId string, isEnable bool) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var enable uint64
	if isEnable { // 启用
		enable = 1
	} else { // false： 禁用
		enable = 0
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		err := SwitchWorkflowPDLStateTx(ctx, tx, robotID, workflowId, enable)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowPDLState|err:%+v", err)
		return err
	}

	return nil
}

// SwitchWorkflowPDLStateTx 切换工作流状态
func SwitchWorkflowPDLStateTx(ctx context.Context, tx *gorm.DB, robotID, workflowId string, enable uint64) error {
	sid := util.RequestID(ctx)
	//uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffId := util.StaffID(ctx)
	workflowPDL := entity.WorkflowPDL{}

	// 更新工作流列表, 启用和禁用 与发布状态无关
	flowDb := tx.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?", robotID, workflowId).
		Find(&workflowPDL)

	if err := flowDb.Error; err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowPDLStateTx|sid:%s|err:%+v", sid, err)
		return err
	}
	// 转换失败，不允许更改状态
	if workflowPDL.IsWorkflowPDLConvertedFailed() {
		return errors.ErrWorkflowConvertStatus
	}
	// 转换中，不允许更改状态
	if workflowPDL.IsWorkflowPDLConverting() {
		return errors.ErrWorkflowConverting
	}
	// 发布状态中的工作流，不允许更改状态
	if workflowPDL.ReleaseStatus == entity.WorkflowPdlReleaseStatusPublishing {
		return errors.ErrWorkflowPublishing
	}
	isDebug := uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE)
	// 草稿态的停启用相当于自动保存，其他状态相当于调试
	// 见状态流转 https://iwiki.woa.com/p/4012998976
	if workflowPDL.WorkflowState == entity.WorkflowPdlStateDraft || workflowPDL.WorkflowState == entity.WorkflowPdlStatePublishedDraft {
		isDebug = uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT)
	}

	flowState, action, err := TransformWorkflowPDLState(ctx, workflowPDL, isDebug)
	if err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowPDLStateTx, TransformWorkflowPDLState|err:%v", err)
		return err
	}

	// 更新工作流的状态
	if err := flowDb.Updates(map[string]interface{}{
		"f_is_enable":      enable,
		"f_action":         action,
		"f_flow_state":     flowState,
		"f_release_status": entity.ReleaseStatusUnPublished,
		"f_update_time":    time.Now(),
		"f_staff_id":       staffId,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowPDLStateTx|sid:%s|err:%+v", sid, err)
		return err
	}

	return nil
}

// transformPDLRules 状态流转规则
var transformPDLRules = map[wfTransformState]map[KEP_WF.SaveAgentWorkflowReq_SaveTypeEnum]transformRule{
	draftState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStateEnable, nextAction: entity.ActionInsert},
	},
	enableState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStateEnable, nextAction: entity.ActionInsert},
	},
	publishedState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStatePublishedChange, nextAction: entity.ActionUpdate},
	},
	publishFailedState: { // 默认为从来没有发布成功时的流转 如果已经发布成功过，需要特殊判断和流转
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStateEnable, nextAction: entity.ActionInsert},
	},
	publishedDraftState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStatePublishedChange, nextAction: entity.ActionUpdate},
	},
	publishedChangeState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStatePublishedChange, nextAction: entity.ActionUpdate},
	},
	convertedState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStateEnable, nextAction: entity.ActionInsert},
	},
	convertedPublishedState: {
		KEP_WF.SaveAgentWorkflowReq_DRAFT:  {nextState: entity.WorkflowPdlStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveAgentWorkflowReq_ENABLE: {nextState: entity.WorkflowPdlStatePublishedChange, nextAction: entity.ActionUpdate},
	},
}

// getWorkflowPDLTransformState 获取工作流状态
func getWorkflowPDLTransformState(wf entity.WorkflowPDL) (wfTransformState, error) {
	// 发布状态
	switch wf.ReleaseStatus {
	case entity.WorkflowPdlReleaseStatusUnPublished:
		// 发布状态待发布
		// 根据WorkflowState确定状态
		switch wf.WorkflowState {
		case entity.WorkflowPdlStateDraft:
			return draftState, nil
		case entity.WorkflowPdlStateEnable:
			return enableState, nil
		case entity.WorkflowPdlStatePublishedDraft:
			return publishedDraftState, nil
		case entity.WorkflowPdlStatePublishedChange:
			return publishedChangeState, nil
		case entity.WorkflowPdlStateConverted:
			return convertedState, nil
		case entity.WorkflowPdlStatePublishedConverted:
			return convertedPublishedState, nil
		case entity.WorkflowPdlStateConverting:
			return convertingState, nil
		case entity.WorkflowPdlStateConvertedFail:
			return convertedFailedState, nil
		case entity.WorkflowPdlStatePublishedConverting:
			return convertingState, nil
		case entity.WorkflowPdlStatePublishedConvertedFail:
			return convertedFailedState, nil
		default:
			return 0, fmt.Errorf("WorkflowPDLState:%s illegal", wf.WorkflowState)
		}
	case entity.WorkflowPdlReleaseStatusPublishing:
		// 发布状态发布中
		return publishingState, nil
	case entity.WorkflowPdlReleaseStatusPublished:
		// 发布状态已发布
		// 根据WorkflowState确定状态
		switch wf.WorkflowState {
		case entity.WorkflowPdlStateConverting:
			return convertingState, nil
		case entity.WorkflowPdlStateConvertedFail:
			return convertedFailedState, nil
		case entity.WorkflowPdlStatePublishedConverting:
			return convertingState, nil
		case entity.WorkflowPdlStatePublishedConvertedFail:
			return convertedFailedState, nil
		default:
			return publishedState, nil
		}
	case entity.WorkflowPdlReleaseStatusPublishedFail:
		// 发布状态发布失败
		// 根据WorkflowState确定状态
		switch wf.WorkflowState {
		case entity.WorkflowPdlStateConverting:
			return convertingState, nil
		case entity.WorkflowPdlStateConvertedFail:
			return convertedFailedState, nil
		case entity.WorkflowPdlStatePublishedConverting:
			return convertingState, nil
		case entity.WorkflowPdlStatePublishedConvertedFail:
			return convertedFailedState, nil
		default:
			return publishFailedState, nil
		}
	default:
		return 0, fmt.Errorf("WorkflowPDLReleaseStatus:%s illegal", wf.ReleaseStatus)
	}
}

// TransformWorkflowPDLState PDL工作流任务流转，参考工作流TransformWorkflowState
func TransformWorkflowPDLState(ctx context.Context, wf entity.WorkflowPDL, isDebug uint32) (nextState, nextAction string, err error) {
	log.InfoContextf(ctx, "TransformWorkflowPDLState|WF:%+v, isDebug:%d", wf, isDebug)
	if isDebug != uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT) && isDebug != uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE) {
		err = fmt.Errorf("workflowPDL isDebug:%d illegal", isDebug)
		log.ErrorContextf(ctx, "TransformWorkflowPDLState|err:%+v", err)
		return "", "", err
	}
	state, err := getWorkflowPDLTransformState(wf)
	if err != nil {
		log.ErrorContextf(ctx, "TransformWorkflowPDLState, getWorkflowPDLTransformState|err:%+v", err)
		return "", "", err
	}
	log.InfoContextf(ctx, "TransformWorkflowPDLState|state:%+v", state)

	rules, ok := transformPDLRules[state]
	if !ok {
		err = fmt.Errorf("workflowPDL transformPDLRules:%d not allow trans", state)
		log.ErrorContextf(ctx, "TransformWorkflowPDLState|err:%+v", err)
		return "", "", err
	}

	rule, ok := rules[KEP_WF.SaveAgentWorkflowReq_SaveTypeEnum(isDebug)]
	if !ok {
		err = fmt.Errorf("workflowPDL isDebug:%d illegal", isDebug)
		log.ErrorContextf(ctx, "TransformWorkflowPDLState|err:%+v", err)
		return "", "", err
	}
	log.InfoContextf(ctx, "TransformWorkflowPDLState|rule:%+v", rule)

	// 针对发布失败的特殊处理
	if state == publishFailedState && wf.IsWorkflowPDLPublished() {
		switch KEP_WF.SaveAgentWorkflowReq_SaveTypeEnum(isDebug) {
		case KEP_WF.SaveAgentWorkflowReq_DRAFT:
			rule.nextState = entity.WorkflowPdlStatePublishedDraft
		case KEP_WF.SaveAgentWorkflowReq_ENABLE:
			rule.nextState = entity.WorkflowPdlStatePublishedChange
		}

		rule.nextAction = entity.ActionUpdate
		log.InfoContextf(ctx, "TransformWorkflowPDLState|rule:%+v", rule)
	}
	return rule.nextState, rule.nextAction, nil
}

// createConvertWorkflowToPDLTask 创建导入任务流程
func createConvertWorkflowToPDLTask(ctx context.Context, corpID uint64, workflowPDL *entity.WorkflowPDL) error {
	sid := util.RequestID(ctx)
	// 创建任务参数
	taskParams := entity.TaskConvertWorkflowToPDLParams{
		RequestID:  sid,
		Name:       workflowPDL.WorkflowName,
		CorpID:     corpID,
		StaffID:    workflowPDL.StaffID,
		RobotID:    workflowPDL.RobotId,
		TaskID:     uint64(idgenerator.NewInt64ID()),
		WorkflowID: workflowPDL.WorkflowID,
	}
	// 创建任务调度
	if err := scheduler.NewConvertWorkflowToPDLTask(ctx, workflowPDL.RobotId, taskParams); err != nil {
		return err
	}
	// NOTE:创建任务通知（交互中未提及）
	//createNoticeReq := &pb.CreateNoticeReq{
	//	BotBizId:     uint64(encode.StringToInt64(workflowPDL.RobotId)),
	//	PageId:       entity.NoticeWorkflowPageID,
	//	Type:         entity.NoticeTypeWorkFlowConvert,
	//	Level:        entity.LevelInfo,
	//	RelateId:     taskParams.TaskID,
	//	Content:      fmt.Sprintf(entity.WorkflowConvertPDLNoticeContentIng, taskParams.Name),
	//	IsGlobal:     false,
	//	IsAllowClose: true,
	//	CorpId:       corpID,
	//	StaffId:      workflowPDL.StaffID,
	//}
	//if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
	//	return err
	//}
	return nil

}

// getWorkflowPDLConvertingState 获取工作流PDL转换中状态
func getWorkflowPDLConvertingState(w *entity.WorkflowPDL) string {
	if w.IsWorkflowPDLPublished() {
		return entity.WorkflowPdlStatePublishedConverting
	} else {
		return entity.WorkflowPdlStateConverting
	}
}

// getWorkflowPDLConvertedFailedState 获取工作流PDL转换失败状态
func getWorkflowPDLConvertedFailedState(w *entity.WorkflowPDL) string {
	if w.IsWorkflowPDLPublished() {
		return entity.WorkflowPdlStatePublishedConvertedFail
	} else {
		return entity.WorkflowPdlStateConvertedFail
	}
}

// getWorkflowPDLConvertedState 获取工作流PDL转换成功状态
func getWorkflowPDLConvertedState(w *entity.WorkflowPDL) string {
	if w.IsWorkflowPDLPublished() {
		return entity.WorkflowPdlStatePublishedConverted
	} else {
		return entity.WorkflowPdlStateConverted
	}
}

// deleteWorkflowPDLByFlowIds 根据工作流ID，批量删除工作流
func deleteWorkflowPDLByFlowIds(ctx context.Context, tx *gorm.DB, appBizId string, flowIds []string) ([]entity.WorkflowPDL, error) {
	var workFlowPDLs []entity.WorkflowPDL
	err := tx.Table(entity.WorkflowPDL{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Where("f_release_status != ?", entity.WorkflowPdlReleaseStatusPublishing).
		Find(&workFlowPDLs).Updates(map[string]interface{}{
		"f_is_deleted":     1,
		"f_action":         entity.ActionDelete,
		"f_release_status": entity.WorkflowPdlReleaseStatusUnPublished,
	}).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.InfoContextf(ctx, "deleteWorkflowPDLByFlowIds|delete nothing, err:%+v", err)
			return nil, nil
		}
		log.ErrorContextf(ctx, "deleteWorkflowPDLByFlowIds|failed:%+v,err:%+v", flowIds, err)
		return workFlowPDLs, err
	}
	deleteFlowIds := make([]string, 0)
	for _, pdl := range workFlowPDLs {
		deleteFlowIds = append(deleteFlowIds, pdl.WorkflowID)
	}
	log.InfoContextf(ctx, "deleteWorkflowPDLByFlowIds|delete workflowPdlIds:%+v", deleteFlowIds)

	// 修复已发布的PDL，如果删除了，需要发布删除，要不存在问题
	if err := updateDeleteWorkflowPDLStatePublished(ctx, tx, appBizId, workFlowPDLs); err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowPDLByFlowIds|failed:%+v,err:%+v", flowIds, err)
		return workFlowPDLs, err
	}
	return workFlowPDLs, nil
}

// updateDeleteWorkflowPDLStatePublished 将已发布的PDL 更新f_flow_state 为 WorkflowStatePublishedChange
func updateDeleteWorkflowPDLStatePublished(ctx context.Context, tx *gorm.DB, appBizId string,
	workflowPDLs []entity.WorkflowPDL) error {
	// 获取flowSate
	pDraftIds := make([]string, 0)
	for _, v := range workflowPDLs {
		// 已发布草稿态需要变为 PUBLISHED_CHANGE
		if v.IsWorkflowPDLPublished() {
			pDraftIds = append(pDraftIds, v.WorkflowID)
		}
	}
	if len(pDraftIds) > 0 {
		if err := tx.Table(entity.WorkflowPDL{}.TableName()).
			Where("f_robot_id = ? and f_workflow_id IN (?)", appBizId, pDraftIds).
			Updates(map[string]interface{}{
				"f_flow_state": entity.WorkflowPdlStatePublishedChange,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "updateDeleteWorkflowPDLStatePublished|failed|err:%+v", err)
			return err
		}
	}

	return nil
}

func ListPDLVersion(ctx context.Context, botBizID, pdlID string) ([]*entity.PDLVersion, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	limit := config.GetMainConfig().PDLConfig.VersionNumLimit
	if limit == 0 {
		limit = 20
	}
	var pdlVersions []*entity.PDLVersion
	err := db.Model(&entity.PDLVersion{}).
		Where(fmt.Sprintf("%s = ? AND %s = ?", entity.PDLVersionColumns.RobotID, entity.PDLVersionColumns.PdlID),
			botBizID, pdlID).
		Order(fmt.Sprintf("%s DESC", entity.PDLVersionColumns.PdlSnapshotVersion)).
		Limit(limit).
		Scan(&pdlVersions).Error
	if err != nil {
		log.ErrorContextf(ctx, "ListPDLVersion|failed|err:%+v|botBizID:%s|pdlID:%s", err, botBizID, pdlID)
		return nil, err
	}
	return pdlVersions, nil
}

func GetPDLVersionDetail(ctx context.Context, pdlID string, version uint32) (*entity.PDLVersion, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var pdlVersion entity.PDLVersion
	err := db.Model(&entity.PDLVersion{}).
		Where(fmt.Sprintf("%s = ? AND %s = ?", entity.PDLVersionColumns.PdlID, entity.PDLVersionColumns.PdlSnapshotVersion),
			pdlID, version).
		First(&pdlVersion).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetPDLVersionDetail|failed|err:%+v|pdlID:%s|version:%d", err, pdlID, version)
		return nil, err
	}
	log.DebugContextf(ctx, "GetPDLVersionDetail|success|pdlVersion:%+v", pdlVersion)
	return &pdlVersion, nil
}
