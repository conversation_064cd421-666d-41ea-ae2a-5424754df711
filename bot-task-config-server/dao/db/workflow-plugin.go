package db

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// GetWorkflowRefPluginConditions 生成查询条件
func GetWorkflowRefPluginConditions(tx *gorm.DB, pluginId string, toolId string, workflowId string) *gorm.DB {
	tx = tx.Where("f_is_deleted = ?", 0)
	if pluginId != "" {
		tx = tx.Where("f_plugin_id = ?", pluginId)
	}
	if toolId != "" {
		tx = tx.Where("f_tool_id = ?", toolId)
	}
	if workflowId != "" {
		tx = tx.Where("f_workflow_id = ?", workflowId)
	}
	return tx
}

// GetWorkflowRefPlugin 获取引用了指定插件的工作流信息
func GetWorkflowRefPlugin(ctx context.Context, pluginId string, toolId string, workflowId string) (
	[]*entity.WorkflowRefPlugin, error) {
	workflowRefPlugins := make([]*entity.WorkflowRefPlugin, 0)
	if pluginId == "" && toolId == "" && workflowId == "" {
		return workflowRefPlugins, nil
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		var txErr error
		workflowRefPlugins, txErr = GetWorkflowRefPluginWithTx(ctx, tx, pluginId, toolId, workflowId)
		return txErr
	})
	if err != nil {
		return nil, err
	}
	return workflowRefPlugins, nil
}

// GetWorkflowRefPluginWithTx 获取引用了指定插件的工作流信息，事务处理
func GetWorkflowRefPluginWithTx(ctx context.Context, tx *gorm.DB, pluginId string, toolId string, workflowId string) (
	[]*entity.WorkflowRefPlugin, error) {
	workflowRefPlugins := make([]*entity.WorkflowRefPlugin, 0)
	if pluginId == "" && toolId == "" && workflowId == "" {
		return workflowRefPlugins, nil
	}
	tx = tx.Table(entity.WorkflowRefPlugin{}.TableName())
	tx = GetWorkflowRefPluginConditions(tx, pluginId, toolId, workflowId)
	if err := tx.Scan(&workflowRefPlugins).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRefPluginWithTx err:%v", err)
		return workflowRefPlugins, err
	}
	return workflowRefPlugins, nil
}
