package db

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/json0"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

// GetWorkflowCountByFlowIds 获取Workflow的个数
func GetWorkflowCountByFlowIds(ctx context.Context, appBizId string, flowIDs []string) (int, error) {
	var total int64
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted = ?", 0).
		Where("f_robot_id = ?", appBizId).
		Where("f_workflow_id IN (?)", flowIDs).
		Count(&total).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowCountByFlowIds err：%v", err)
		return 0, err
	}
	return int(total), nil
}

// GetWorkflowRefMapByFlowIds 获取导出工作流关联的工作流
func GetWorkflowRefMapByFlowIds(ctx context.Context, flowIDs []string, workflowMap map[string]*entity.Workflow) (
	map[string][]*entity.ExportWorkflowRef, error) {
	mapWorkflowRef := make(map[string][]*entity.ExportWorkflowRef)
	if len(flowIDs) == 0 {
		return mapWorkflowRef, nil
	}
	var exportWorkflowRef []*entity.ExportWorkflowRef
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.WorkflowReference{}.TableName()).Select("f_workflow_id,f_node_id,f_workflow_ref_id").
		Where("f_is_deleted = 0").
		Where("f_workflow_id IN (?)", flowIDs).Find(&exportWorkflowRef).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRefMapByFlowIds err:%v", err)
		return nil, err
	}
	if len(exportWorkflowRef) == 0 {
		return mapWorkflowRef, nil
	}
	for _, v := range exportWorkflowRef {
		if _, ok := workflowMap[v.WorkflowRefID]; ok {
			v.WorkflowRefName = workflowMap[v.WorkflowRefID].WorkflowName
			mapWorkflowRef[v.WorkflowID] = append(mapWorkflowRef[v.WorkflowID], v)
		}
	}
	return mapWorkflowRef, nil
}

// GetUnPublishWorkflowExample 通过应用ID及flowIds获取示未发布的示例问法
func GetUnPublishWorkflowExample(ctx context.Context, flowIds []string,
	appBizId string) ([]*entity.WorkflowExample, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var examples []*entity.WorkflowExample
	sid := util.RequestID(ctx)
	if len(flowIds) == 0 {
		return examples, nil
	}
	err := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_release_status !=? AND f_robot_id = ? AND f_workflow_id IN (?)",
			entity.WorkflowReleaseStatusPublished, appBizId, flowIds).
		Find(&examples).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExampleByFlowIds failed,appBizId:%s|%s|err:%+v", sid, appBizId, err)
		return nil, err
	}
	return examples, nil
}

// GetAllExample 通过应用ID获取示例问法
func GetAllExample(ctx context.Context, appBizId string) (map[string]struct{}, error) {
	exampleMap := make(map[string]struct{}, 0)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var examples []string
	err := db.Table(entity.WorkflowExample{}.TableName()).
		Select("f_example").
		Where("f_is_deleted = 0").
		Where("f_robot_id = ?", appBizId).
		Find(&examples).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetAllExample failed,appBizId:%+v|err:%+v", appBizId, err)
		return nil, err
	}
	for _, v := range examples {
		exampleMap[v] = struct{}{}
	}
	return exampleMap, nil
}

// GetWorkflowVarParams 获取导出工作流关联的自定义变量
//
// mapVar：工作流ID-关联变量集合
// exportVar：工作流与变量的关联表关系数据
// mapWorkflowVar：变量ID-变量详情
func GetWorkflowVarParams(ctx context.Context, robotID string,
	flowIDs []string) (map[string][]*entity.ExportWorkflowVar, error) {
	mapVar := make(map[string][]*entity.ExportWorkflowVar)
	sid := util.RequestID(ctx)
	mapWorkflowVar := make(map[string]*entity.ExportWorkflowVar)
	if len(flowIDs) == 0 {
		return mapVar, nil
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var exportVar []*entity.ExportWorkflowVar
	var varIDs []string
	if err := db.Table("t_workflow_var").
		Select("f_var_id,f_workflow_id").
		Where("f_is_deleted=0 and f_robot_id=? AND f_workflow_id IN ?",
			robotID, flowIDs).Find(&exportVar).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetWorkflowVarParams|err:%+v", sid, err)
		return nil, err
	}
	if len(exportVar) == 0 {
		return mapVar, nil
	}
	for _, v := range exportVar {
		varIDs = append(varIDs, v.VarID)
	}
	var varInfo []*entity.ExportWorkflowVar
	db = database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Table("t_var").
		Select("f_var_id, f_var_name,f_var_desc,f_var_type, f_var_default_value, f_var_default_file_name").
		Where("f_is_deleted=0 and f_app_id=? and f_var_id IN ?",
			robotID, varIDs).Find(&varInfo).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetWorkflowVarParams|err:%+v", sid, err)
		return nil, err
	}
	for _, v := range varInfo {
		mapWorkflowVar[v.VarID] = v
	}
	for _, v := range exportVar {
		if _, ok := mapWorkflowVar[v.VarID]; ok {
			v.VarName = mapWorkflowVar[v.VarID].VarName
			v.VarDesc = mapWorkflowVar[v.VarID].VarDesc
			v.VarType = mapWorkflowVar[v.VarID].VarType
			v.VarDefaultValue = mapWorkflowVar[v.VarID].VarDefaultValue
			v.VarDefaultFileName = mapWorkflowVar[v.VarID].VarDefaultFileName
			mapVar[v.FlowID] = append(mapVar[v.FlowID], v)
		}
	}
	return mapVar, nil
}

// GetExportParams 获取导出时全部参数信息
func GetExportParams(ctx context.Context, robotID string, flowIDs []string) (
	map[string][]*entity.ParamMigrationInfo, error) {
	log.InfoContextf(ctx, "GetExportParams, robotID:%s, len(flowIDs):%d", robotID, len(flowIDs))
	paramMap := make(map[string][]*entity.WorkflowParameter)
	// 查询参数
	params, err := getParamWithFlowIds(ctx, robotID, flowIDs)
	if err != nil {
		return nil, err
	}
	for _, v := range params {
		paramMap[v.WorkFlowID] = append(paramMap[v.WorkFlowID], v)
	}
	paramIDs := make([]string, 0)
	for _, param := range params {
		paramIDs = append(paramIDs, param.ParameterID)
	}
	paramInfoMap, err := getParamInfoWithParamIds(ctx, paramIDs)
	if err != nil {
		return nil, err
	}
	paramMigrationInfosMap := make(map[string][]*entity.ParamMigrationInfo)
	// 组装转换导入/导出所需的槽位实体信息
	for _, flowId := range flowIDs {
		paramList, ok := paramMap[flowId]
		if !ok || len(paramList) == 0 {
			continue
		}
		for _, param := range paramList {
			paramInfo, ok := paramInfoMap[param.ParameterID]
			if !ok {
				continue
			}
			info := &entity.ParamMigrationInfo{
				NodeID:           param.NodeID,
				NodeName:         param.NodeName,
				ParamID:          paramInfo.ParameterID,
				ParamName:        paramInfo.ParameterName,
				ParamDesc:        paramInfo.ParameterDesc,
				ParamType:        paramInfo.ParameterType,
				CorrectExample:   paramInfo.CorrectExamples,
				IncorrectExample: paramInfo.IncorrectExamples,
			}
			paramMigrationInfosMap[flowId] = append(paramMigrationInfosMap[flowId], info)
		}
	}

	log.InfoContextf(ctx, "GetExportParams, len(paramMigrationInfosMap):%d",
		len(paramMigrationInfosMap))
	return paramMigrationInfosMap, nil
}

// getParamWithFlowIds 用工作流ID查询关联的参数信息
func getParamWithFlowIds(ctx context.Context, robotID string, flowIDs []string) ([]*entity.WorkflowParameter, error) {
	log.InfoContextf(ctx, "getParamWithFlowIds, robotID:%s, len(flowIDs):%d", robotID, len(flowIDs))
	if len(flowIDs) == 0 {
		return nil, nil
	}

	var params []*entity.WorkflowParameter
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.WorkflowParameter{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_workflow_id IN ?", flowIDs).
		Find(&params).Error
	if err != nil {
		log.ErrorContextf(ctx, "getParamWithFlowIds db.Find Failed, err:%v", err)
		return nil, err
	}
	return params, nil
}

// getParamInfoWithParamIds 用工作流ID查询关联的参数信息
func getParamInfoWithParamIds(ctx context.Context, paramIds []string) (map[string]*entity.Parameter, error) {
	log.InfoContextf(ctx, "getParamWithFlowIds,  len(flowIDs):%d", len(paramIds))
	if len(paramIds) == 0 {
		return nil, nil
	}

	var params []*entity.Parameter
	err := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table(entity.Parameter{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_parameter_id IN ?", paramIds).
		Find(&params).Error
	if err != nil {
		log.ErrorContextf(ctx, "getEntryWithParamIds db.Find Failed, err:%v", err)
		return nil, err
	}
	paramInfoMap := make(map[string]*entity.Parameter)
	for _, v := range params {
		paramInfoMap[v.ParameterID] = v
	}
	return paramInfoMap, nil
}

// SetExportTaskFileFinishStatus 设置导出任务状态
func SetExportTaskFileFinishStatus(ctx context.Context, exportID, fileSize uint64, cosPath string, status int) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Table(entity.ExportWorkflowFile{}.TableName()).
		Where("f_export_id=?", exportID).Updates(map[string]interface{}{
		"f_cos_url":     cosPath,
		"f_file_size":   fileSize,
		"f_status":      status,
		"f_update_time": time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|ExportWorkflowFile|Update|err:%+v", util.RequestID(ctx), err)
		return err
	}
	return nil
}

// GetWorkflowNames 查询所有工作流的名字
func GetWorkflowNames(ctx context.Context, robotId string) ([]entity.Workflow, error) {
	var list []entity.Workflow
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id, f_workflow_name").
		Where("f_is_deleted=?", 0).
		Where("f_robot_id=?", robotId).Find(&list).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowNames err:%v", err)
		return nil, err
	}
	return list, nil
}

// CreateWorkflowImport 创建导入任务流程
func CreateWorkflowImport(ctx context.Context, corpID, staffID uint64, robotID, fileName string,
	parentTask *entity.WorkflowImport, tasks []*entity.WorkflowImport) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(entity.WorkflowImport{}.TableName()).Create(parentTask).Error; err != nil {
			return err
		}
		for _, v := range tasks {
			if err := tx.Table(entity.WorkflowImport{}.TableName()).Create(v).Error; err != nil {
				return err
			}
		}
		taskParams := entity.WorkflowImportParentParams{
			RequestID: util.RequestID(ctx),
			CorpID:    corpID,
			StaffID:   staffID,
			RobotID:   robotID,
			ImportID:  parentTask.ImportID,
			FileName:  fileName,
		}
		// 创建父任务调度
		if err := scheduler.NewImportWorkflowParentTask(ctx, robotID, taskParams); err != nil {
			return err
		}
		// 创建导入任务通知
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId:     uint64(encode.StringToInt64(robotID)),
			PageId:       entity.NoticeWorkflowPageID,
			Type:         entity.NoticeTypeWorkflowImport,
			Level:        entity.LevelInfo,
			RelateId:     uint64(encode.StringToInt64(parentTask.ImportID)),
			Content:      fmt.Sprintf(entity.WorkflowImportNoticeContentIng, fileName),
			IsGlobal:     false,
			IsAllowClose: false,
			CorpId:       corpID,
			StaffId:      staffID,
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建任务流程导入import失败 err:%+v", err)
		return err
	}
	return nil
}

// CreateWorkflowImportSub 创建导入任务流程子任务调度
func CreateWorkflowImportSub(ctx context.Context, corpID, staffID uint64, robotID, fileName, parentImportID,
	importID string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		taskFlowImport := &entity.WorkflowImport{
			Status: entity.ImportWorkflowStatusProcessing,
		}
		err := tx.Table(entity.WorkflowImport{}.TableName()).
			Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ImportID), importID).
			Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.Status), entity.FlowImportStatusWait).
			Updates(taskFlowImport).Error
		if err != nil {
			log.ErrorContextf(ctx, "CreateWorkflowImportSub update import status failed,importID:%s,err:%+v",
				importID, err)
			return err
		}
		params := entity.WorkflowImportParams{
			RequestID:      util.RequestID(ctx),
			CorpID:         corpID,
			StaffID:        staffID,
			RobotID:        robotID,
			ParentImportID: parentImportID,
			ImportID:       importID,
			FileName:       fileName,
		}
		if err := scheduler.NewImportWorkflowTask(ctx, robotID, params); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建任务流程导入import子任务失败 err:%+v", err)
		return err
	}
	return nil
}

// GetWorkflowImportByID 通过导入ID获取任务导入信息
func GetWorkflowImportByID(ctx context.Context, importID string) (*entity.WorkflowImport, error) {
	taskFlowImport := &entity.WorkflowImport{}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.WorkflowImport{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ImportID), importID).
		Find(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowImportByID failed,importID:%s,err:%+v", importID, err)
		return nil, err
	}
	return taskFlowImport, nil
}

// GetWorkflowImportByParentID 通过父导入ID获取子导入任务信息
func GetWorkflowImportByParentID(ctx context.Context, parentImportID string) ([]*entity.WorkflowImport, error) {
	var taskFlowImports []*entity.WorkflowImport
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.WorkflowImport{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ParentImportID), parentImportID).
		Find(&taskFlowImports).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowImportByParentID failed,parentImportID:%s,err:%+v",
			parentImportID, err)
		return nil, err
	}
	return taskFlowImports, nil
}

// UpdateWorkflowImportStatus 更新任务流程导入状态
func UpdateWorkflowImportStatus(ctx context.Context, importID string, oldStatus, status uint32) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	taskFlowImport := &entity.WorkflowImport{
		Status: status,
	}
	err := db.Table(entity.WorkflowImport{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ImportID), importID).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.Status), oldStatus).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowImportStatus failed,importID:%s,oldStatus:%d,status:%d,err:%+v",
			importID, oldStatus, status, err)
		return err
	}
	return nil
}

// updateWorkflowImportFinalParams 更新任务流程最终参数
func updateWorkflowImportFinalParams(ctx context.Context, tx *gorm.DB, importID string,
	finalParams string) error {
	taskFlowImport := &entity.WorkflowImport{
		FinalParams: finalParams,
	}
	err := tx.Table(entity.WorkflowImport{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "updateWorkflowImportFinalParams failed,importID:%s,finalParams:%s,err:%+v",
			importID, finalParams, err)
		return err
	}
	return nil
}

// UpdateWorkflowImportMessage 更新任务流程错误信息
func UpdateWorkflowImportMessage(ctx context.Context, importID, message string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	taskFlowImport := &entity.WorkflowImport{
		Message: message,
	}
	err := db.Table(entity.WorkflowImport{}.TableName()).
		Where(fmt.Sprintf("%s = ?", entity.TWorkflowImportColumns.ImportID), importID).
		Updates(taskFlowImport).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowImportMessage failed,importID:%s,message:%s,err:%+v",
			importID, message, err)
		return err
	}
	return nil
}

// ImportVarForWorkflow 跨库了，要单独插入
func ImportVarForWorkflow(ctx context.Context, varParams []*entity.VarParams, robotID string) error {
	if len(varParams) == 0 {
		return nil
	}
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 创建所需的变量
		if err := createImportVarParams(ctx, tx, robotID, varParams); err != nil {
			log.ErrorContextf(ctx, "sid:%s|createImportVarParams|err:%+v", sid, err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "importVarForWorkflow fail, err:%+v", err)
		return err
	}
	return nil

}

// ImportWorkflow 导入工作流程
func ImportWorkflow(ctx context.Context, importParamData *entity.WorkflowImportData,
	varParams []*entity.VarParams, varRefParams []*entity.WorkflowVar, params []*entity.ParamMigrationInfo,
	examples []*entity.WorkflowExample, createParams *entity.CreateWorkflowParams,
	workflowRefParams []*entity.WorkflowReference, workflowRefPlugins []*entity.WorkflowRefPlugin,
	workflowPDLs []*entity.WorkflowPDL, robotID, importID string) error {
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// 创建所需的变量
	if err := ImportVarForWorkflow(ctx, varParams, robotID); err != nil {
		log.ErrorContextf(ctx, "sid:%s|importVarForWorkflow|err:%+v", sid, err)
		return err
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		err := ExecImportWorkflow(tx, ctx, robotID, examples, importParamData, params, createParams,
			varRefParams, workflowRefParams, workflowRefPlugins, workflowPDLs)
		if err != nil {
			return err
		}
		// 更新导入后的数据
		finalParams, _ := jsoniter.MarshalToString(importParamData)
		if err := updateWorkflowImportFinalParams(ctx, tx, importID, finalParams); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "CreateWorkflow fail, err:%+v", err)
		return err
	}
	return nil
}

func ExecImportWorkflow(tx *gorm.DB, ctx context.Context, robotID string, examples []*entity.WorkflowExample,
	importParamData *entity.WorkflowImportData, params []*entity.ParamMigrationInfo,
	createParams *entity.CreateWorkflowParams, varRefParams []*entity.WorkflowVar,
	workflowRefParams []*entity.WorkflowReference, workflowRefPlugins []*entity.WorkflowRefPlugin,
	workflowPDLs []*entity.WorkflowPDL) error {
	// 创建所需的示例问法
	if err := createImportWorkflowExamples(ctx, tx, robotID, examples); err != nil {
		return err
	}
	// 创建所需槽位
	if err := createImportParams(ctx, tx, robotID, importParamData.Uin, importParamData.SubUin,
		params); err != nil {
		return err
	}
	// 创建工作流程
	if err := TxCreateWorkflow(ctx, tx, *createParams); err != nil {
		return err
	}
	// 创建关联关系
	if err := TxInsertWorkflowVar(ctx, tx, varRefParams); err != nil {
		return err
	}
	// 创建工作流引用关系
	if err := TxInsertWorkflowRef(ctx, tx, workflowRefParams); err != nil {
		return err
	}
	// 创建工作流引用插件关系，注意导入导出只保留官方插件
	if err := TxInsertWorkflowRefPlugin(ctx, tx, workflowRefPlugins); err != nil {
		return err
	}
	// 创建所需的pdl
	if err := CreateImportWorkflowPDLs(ctx, tx, robotID, workflowPDLs); err != nil {
		return err
	}
	return nil
}

// createImportWorkflowExamples 导入创建示例问法
func createImportWorkflowExamples(ctx context.Context, tx *gorm.DB,
	robotId string, examples []*entity.WorkflowExample) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|createImportWorkflowExamples|robotID:%s|len(examples):%d",
		sid, robotId, len(examples))
	if len(examples) > 0 {
		err := tx.Model(&entity.WorkflowExample{}).Create(examples).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|createImportWorkflowExamples Failed,err:%v", sid, err)
			return err
		}
	}
	return nil
}

// createImportParams 创建导入时需要的参数信息
func createImportParams(ctx context.Context, tx *gorm.DB, robotID, uin, subUin string,
	params []*entity.ParamMigrationInfo) error {
	log.InfoContextf(ctx, "createImportParams, robotID:%s, uin:%s, subUin:%s, len(slotInfos):%d",
		robotID, uin, subUin, len(params))

	// 获取需要新建全部槽位｜实体｜词条｜槽位-实体关联
	dbParams := make([]*entity.Parameter, 0)
	dbWfParams := make([]*entity.WorkflowParameter, 0)

	for _, param := range params {
		dbParam, dbWfParam := param.ConvertToDBCreateInfo(robotID, uin, subUin)
		dbParams = append(dbParams, dbParam)
		dbWfParams = append(dbWfParams, dbWfParam)
	}

	// DB事务创建全部参数｜示例｜词条｜参数-工作流关联
	err := TxBatchCreateParams(ctx, tx, robotID, dbParams, dbWfParams)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "createImportParams|success created|len(params):%d", len(dbWfParams))
	return nil
}

// TxBatchCreateParams 以事务的形式批量创建实体表｜槽位表｜词条表以及实体槽位关联表
func TxBatchCreateParams(ctx context.Context, tx *gorm.DB, robotID string, params []*entity.Parameter,
	wfParams []*entity.WorkflowParameter) (err error) {
	log.InfoContextf(ctx, "TxBatchCreateParams, robotID:%s, len(params):%d, len(wfParams):%d, ",
		robotID, len(params), len(wfParams))
	if len(params) > 0 {
		err = tx.Model(&entity.Parameter{}).CreateInBatches(params,
			config.GetMainConfig().Workflow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "TxBatchCreateParams CreateParameter Failed, err:%v", err)
			return err
		}
	}
	if len(wfParams) > 0 {
		err = tx.Model(&entity.WorkflowParameter{}).CreateInBatches(wfParams,
			config.GetMainConfig().Workflow.MaxRow).Error
		if err != nil {
			log.ErrorContextf(ctx, "TxBatchCreateParams CreateWorkflowParameter Failed, err:%v", err)
			return err
		}
	}
	return nil
}

// InnerCreateExportWorkflow 创建内部平台导出任务流
func InnerCreateExportWorkflow(ctx context.Context, corpID, staffID, exportId uint64, robotID,
	platform string, flowIDs []string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	// 添加任务到任务队列
	params := entity.WorkflowExportParams{
		RequestID: util.RequestID(ctx),
		CorpID:    corpID,
		StaffID:   staffID,
		RobotID:   robotID,
		ExportID:  exportId,
		FlowIDs:   flowIDs,
		Platform:  platform,
	}
	if err := scheduler.NewExportWorkflowTask(ctx, robotID, params); err != nil {
		return err
	}
	// 创建导出记录
	exportParam := &entity.ExportWorkflowFile{
		ExportID: exportId,
		RobotID:  robotID,
		Params:   json0.Marshal2StringNoErr(params),
		Status:   entity.ExportWorkflowStatusStart,
		Type:     entity.ExportWorkflowTypeFlow,
		Platform: platform,
	}
	if err := db.Table(entity.ExportWorkflowFile{}.TableName()).Create(exportParam).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|ExportFile|Create:%+v|err:%+v", sid, exportParam, err)
		return err
	}

	return nil
}

// CreateImportWorkflowPDLs 导入创建pdl
func CreateImportWorkflowPDLs(ctx context.Context, tx *gorm.DB,
	robotId string, workflowPDLs []*entity.WorkflowPDL) error {
	log.InfoContextf(ctx, "CreateImportWorkflowPDLs|robotID:%s|len(workflowPDLs):%d",
		robotId, len(workflowPDLs))
	if len(workflowPDLs) > 0 {
		err := tx.Model(&entity.WorkflowPDL{}).Create(workflowPDLs).Error
		if err != nil {
			log.ErrorContextf(ctx, "CreateImportWorkflowPDLs Failed,err:%v", err)
			return err
		}
		PDLVersionList := make([]*entity.PDLVersion, 0)
		for _, pdl := range workflowPDLs {
			pdlVersion := &entity.PDLVersion{
				PdlID:                   pdl.PdlID,
				PdlSnapshotVersion:      1,
				WorkflowID:              pdl.WorkflowID,
				RobotID:                 pdl.RobotId,
				WorkflowName:            pdl.WorkflowName,
				DialogJsonEnable:        pdl.DialogJsonEnable,
				Parameter:               pdl.Parameter,
				PdlContent:              pdl.PdlContent,
				ToolsInfo:               pdl.ToolsInfo,
				UserConstraints:         pdl.UserConstraints,
				PdlContentModified:      pdl.PdlContent,
				ToolsInfoModified:       pdl.ToolsInfo,
				UserConstraintsModified: pdl.UserConstraints,
			}
			PDLVersionList = append(PDLVersionList, pdlVersion)
		}
		if len(PDLVersionList) > 0 {
			err := tx.Model(&entity.PDLVersion{}).Create(PDLVersionList).Error
			if err != nil {
				log.ErrorContextf(ctx, "CreateImportWorkflowPDLs version Failed,err:%v", err)
				return err
			}
		}
	}
	return nil
}

// TxCreateImportExams 添加导入的工作流示例并更新工作流的状态
func TxCreateImportExams(ctx context.Context, importID, wfID, appID string, insertExams []*entity.WorkflowExample) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	if len(insertExams) == 0 {
		return nil
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		// 创建示例问法
		if err := tx.Table(entity.WorkflowExample{}.TableName()).Create(insertExams).Error; err != nil {
			log.WarnContextf(ctx, "TxCreateImportExams｜err:%+v", err)
			return err
		}
		// 更新工作流状态
		// 调试的时候 更新redis及vector，这里不需要
		if err, _ := UpdateWorkflowStatusFromSubAtom(ctx, tx, wfID); err != nil {
			log.WarnContextf(ctx, "TxCreateImportExams｜UpdateWorkflowStatusFromSubAtom｜err:%+v", err)
			return err
		}
		// 更新导入后的数据
		finalParams, err := jsoniter.MarshalToString(insertExams)
		if err != nil {
			log.WarnContextf(ctx, "TxCreateImportExams｜MarshalToString｜err:%+v", err)
			return err
		}
		if err := updateWorkflowImportFinalParams(ctx, tx, importID, finalParams); err != nil {
			log.WarnContextf(ctx, "TxCreateImportExams｜updateWorkflowImportFinalParams｜err:%+v", err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "CreateWorkflow fail, err:%+v", err)
		return err
	}
	return nil
}
