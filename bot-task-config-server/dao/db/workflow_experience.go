// bot-task-config-server
//
// @(#)workflow_experience.go  星期二, 二月 18, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package db

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
)

// GetExistWorkflowByRobotID 获取应用下所有的工作流ID和名称
func GetExistWorkflowByRobotID(ctx context.Context, robotID string) (map[string]entity.Workflow, error) {
	workflows, err := GetWorkflowNames(ctx, robotID)
	if err != nil {
		return nil, err
	}
	mapExistName := make(map[string]entity.Workflow, 0)
	for _, v := range workflows {
		mapExistName[v.WorkflowName] = v
	}
	return mapExistName, nil
}

// GetExperienceWorkflowIds 获取体验中心存在的已发布的工作流
func GetExperienceWorkflowIds(ctx context.Context, robotIds, workflowIds []string) (
	[]*entity.ExportWorkflow, error) {
	if len(workflowIds) == 0 && len(robotIds) == 0 {
		log.WarnContextf(ctx, "GetExperienceWorkflowIds workflowIds and robotIds is empty "+
			"workflowIds:%+v, robotIds:%+v", workflowIds, robotIds)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowList []*entity.ExportWorkflow
	// 查询符合条件的记录
	db = db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id,f_workflow_name,f_desc").
		Where("f_is_deleted = 0").
		Where("f_release_status = ?", entity.ReleaseStatusPublished)
	if len(robotIds) > 0 {
		db = db.Where("f_robot_id IN (?)", robotIds)
	}
	if len(workflowIds) > 0 {
		db = db.Where("f_workflow_id IN (?)", workflowIds)
	}
	err := db.Find(&workflowList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExperienceWorkflowIds failed workflowIDs:%+v, robotId:%+v, err:%+v",
			workflowIds, robotIds, err)
		return nil, err
	}
	return workflowList, nil
}

// IDsWorkflowAndRef 获取体验中心工作流和引用的工作流ID
func IDsWorkflowAndRef(refWorkflowIdMap map[string]struct{}, workflowIDs []string) (map[string]struct{}, []string) {
	idMap := make(map[string]struct{})
	for workflowRefID := range refWorkflowIdMap {
		idMap[workflowRefID] = struct{}{}
	}
	for _, workflowID := range workflowIDs {
		idMap[workflowID] = struct{}{}
	}
	ids := make([]string, 0, len(idMap))
	for k := range idMap {
		ids = append(ids, k)
	}
	return idMap, ids
}

// GetExportWorkflowDetail 获取导出工作流详情
func GetExportWorkflowDetail(ctx context.Context, robotId string, workflowIds []string) (
	[]*entity.ExportWorkflow, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetExportWorkflowDetail workflowIds and robotId is empty "+
			"workflowIds:%+v, robotId:%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowList []*entity.ExportWorkflow
	// 查询符合条件的记录
	db = db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id,f_workflow_name,f_desc," +
		"f_dialog_json_draft").
		Where("f_is_deleted = 0")
	if len(robotId) > 0 {
		db = db.Where("f_robot_id = ?", robotId)
	}
	if len(workflowIds) > 0 {
		db = db.Where("f_workflow_id IN (?)", workflowIds)
	}
	err := db.Find(&workflowList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExportWorkflowDetail failed workflowIDs:%+v, robotId:%s, err:%+v",
			workflowIds, robotId, err)
		return nil, err
	}
	return workflowList, nil
}

// GetExportExampleDetail 获取导出工作流示例问法详情
func GetExportExampleDetail(ctx context.Context, robotId string, workflowIds []string) (
	[]*entity.ExportWorkflowExample, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetExportExampleDetail workflowIds and robotId is empty "+
			"workflowIds:|%+v,robotId:|%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowExampleList []*entity.ExportWorkflowExample
	// 查询符合条件的记录
	db = db.Table(entity.WorkflowExample{}.TableName()).Select("f_workflow_id,f_example_id,f_example").
		Where("f_is_deleted = 0")
	if len(robotId) > 0 {
		db = db.Where("f_robot_id = ?", robotId)
	}
	if len(workflowIds) > 0 {
		db = db.Where("f_workflow_id IN (?)", workflowIds)
	}
	err := db.Find(&workflowExampleList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExportExampleDetail failed workflowIDs:%+v, robotId:%s, err:%+v",
			workflowIds, robotId, err)
		return nil, err
	}
	return workflowExampleList, nil
}

//// GetAllExample 通过应用ID获取示例问法
//func GetAllExample(ctx context.Context, appBizId string) (map[string]struct{}, error) {
//	exampleMap := make(map[string]struct{}, 0)
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	var examples []string
//	err := db.Table(entity.WorkflowExample{}.TableName()).
//		Select("f_example").
//		Where("f_is_deleted = 0").
//		Where("f_robot_id = ?", appBizId).
//		Find(&examples).Error
//	if err != nil {
//		log.ErrorContextf(ctx, "GetAllExample failed,appBizId:%+v|err:%+v", appBizId, err)
//		return nil, err
//	}
//	for _, v := range examples {
//		exampleMap[v] = struct{}{}
//	}
//	return exampleMap, nil
//}

// GetExportVarDetail 获取导出变量详情
func GetExportVarDetail(ctx context.Context, robotId string, workflowIds []string) (
	[]*entity.ExportWorkflowVar, []*entity.ExportWorkflowVar, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetExportVarDetail workflowIds and robotId is empty "+
			"workflowIds:|%+v,robotId:|%s", workflowIds, robotId)
		return nil, nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var exportWorkflowVar []*entity.ExportWorkflowVar
	var exportVar []*entity.ExportWorkflowVar
	var varIDs []string
	db = db.Table("t_workflow_var").Select("f_var_id,f_workflow_id").
		Where("f_is_deleted = 0")
	if len(robotId) > 0 {
		db = db.Where("f_robot_id = ?", robotId)
	}
	if len(workflowIds) > 0 {
		db = db.Where("f_workflow_id IN (?)", workflowIds)
	}
	err := db.Find(&exportWorkflowVar).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExportVarDetail failed workflowIDs:%+v, robotId:%s, err:%+v",
			workflowIds, robotId, err)
		return nil, nil, err
	}
	if len(exportWorkflowVar) == 0 {
		return nil, nil, nil
	}
	for _, v := range exportWorkflowVar {
		varIDs = append(varIDs, v.VarID)
	}
	db = database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if err := db.Table("t_var").Select("f_var_id, f_var_name,f_var_desc,f_var_type,f_var_default_value,"+
		"f_var_default_file_name").
		Where("f_is_deleted=0 and f_var_id IN ?", varIDs).
		Find(&exportVar).Error; err != nil {
		log.ErrorContextf(ctx, "GetExportVarDetail var failed workflowIDs:%+v, robotId:%s, err:%+v",
			workflowIds, robotId, err)
		return nil, nil, err
	}
	return exportWorkflowVar, exportVar, nil
}

// GetExportParamsDetail 获取导出工作流参数详情
func GetExportParamsDetail(ctx context.Context, robotId string, workflowIds []string) (
	[]*entity.ParamMigrationInfo, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetExportParamsDetail workflowIds and robotId is empty "+
			"workflowIds:|%+v,robotId:|%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowParam []*entity.ParamMigrationInfo
	paramValues := make([]interface{}, 0)
	querySql := "select a.f_workflow_id, a.f_node_id, a.f_node_name,a.f_parameter_id,b.f_parameter_name," +
		"b.f_parameter_desc,b.f_parameter_type,b.f_correct_examples,b.f_incorrect_examples " +
		"from t_workflow_parameter a INNER join t_parameter b " +
		"on a.f_parameter_id = b.f_parameter_id  where a.f_is_deleted = 0 and b.f_is_deleted = 0 "
	if len(robotId) > 0 {
		querySql += " and a.f_robot_id = ? and b.f_robot_id = ? "
		paramValues = append(paramValues, robotId, robotId)
	}
	if len(workflowIds) > 0 {
		querySql += ` and a.f_workflow_id IN (?` + strings.Repeat(",?", len(workflowIds)-1) + ")"
		for _, flowId := range workflowIds {
			paramValues = append(paramValues, flowId)
		}
	}
	err := db.Raw(querySql, paramValues...).Scan(&workflowParam).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetExportParamsDetail query sql:%s|err:%v", querySql, err)
		return nil, err
	}
	return workflowParam, nil
}

// GetPDLByFlowIds 通过工作流ID获取pdl
func GetPDLByFlowIds(ctx context.Context, robotId string,
	workflowIds []string) ([]*entity.ExportWorkflowPDL, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetPDLByFlowIds workflowIds and robotId is empty "+
			"workflowIds:|%+v,robotId:|%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	var PDLList []*entity.ExportWorkflowPDL
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	db = db.Table(entity.WorkflowPDL{}.TableName()).Select("f_workflow_id,f_user_constraints," +
		"f_parameter,f_pdl_content,f_tools_info").
		Where("f_fail_reason = '' AND f_is_deleted = 0")
	if len(robotId) > 0 {
		db = db.Where("f_robot_id = ?", robotId)
	}
	if len(workflowIds) > 0 {
		db = db.Where("f_workflow_id IN (?)", workflowIds)
	}
	err := db.Find(&PDLList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetPDLByFlowIds failed workflowIDs:%+v, robotId:%s, err:%+v",
			workflowIds, robotId, err)
		return nil, err
	}
	return PDLList, nil
}
