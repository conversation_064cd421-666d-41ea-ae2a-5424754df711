package db

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTransformWorkflowPDLState(t *testing.T) {

	tests := []struct {
		name           string
		workflow       entity.WorkflowPDL
		isDebug        uint32
		expectedState  string
		expectedAction string
		expectedErr    string
	}{
		//	================================= 参数非法 ===============================
		// 测试案例1: 非法的isDebug值,报错
		{"测试案例1: 非法的isDebug值",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateDraft, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			3, "", "", "workflowPDL isDebug:3 illegal"},

		//	=================================  草稿 DRAFT ===============================
		{"测试案例2: 草稿Draft,未发布UnPublished,自动保存0 ==> Draft,insert",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateDraft, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStateDraft, entity.ActionInsert, ""},
		{"测试案例3: 草稿Draft,未发布UnPublished,调试1 ==> Enable,insert",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateDraft, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStateEnable, entity.ActionInsert, ""},

		//	=================================  启用（待发布） ENABLE ===============================
		{"测试案例4: 启用Enable,未发布UnPublished,自动保存0 ==> Draft,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), entity.WorkflowPdlStateDraft, entity.ActionInsert, ""},
		{"测试案例5: 启用Enable,未发布UnPublished,调试1 ==> Enable,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), entity.WorkflowPdlStateEnable, entity.ActionInsert, ""},
		{"测试案例6: 启用Enable,已发布Published,自动保存0  ==> PublishedDraft,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例7: 启用Enable,已发布Published,调试1 ==> PublishedChange,Update",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},
		// Publishing（发布中）的状态,不允许编辑调试,不管flowState和isDebug为任何值报错,
		// workflowPDL transformPDLRules:3 not allow trans
		// 实际业务中应该不会出现这种情况
		{"测试案例8: 启用Enable,发布中Publishing,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例9: 启用Enable,发布中Publishing,调试1 ==> '','',报错",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:3 not allow trans"},

		{"测试案例10: 启用Enable,发布失败PublishedFail,自动保存0 ==> 草稿,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), entity.WorkflowPdlStateDraft, entity.ActionInsert, ""},
		{"测试案例11: 启用Enable,发布失败PublishedFail,调试 1==> enable,insert",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStateEnable, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), entity.WorkflowPdlStateEnable, entity.ActionInsert, ""},

		//	=================================  已发布-草稿态 PUBLISHED_DRAFT ===============================
		{"测试案例12: 已发布-草稿态 PUBLISHED_DRAFT,未发布UnPublished,自动保存0 ==> PUBLISHED_DRAFT,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedDraft, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例13: 已发布-草稿态 PUBLISHED_DRAFT,未发布UnPublished,调试 1 ==> PUBLISHED_CHANGE,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedDraft, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},

		//	=================================  已发布-有修改 PUBLISHED_CHANGE ===============================
		{"测试案例14: 已发布-有修改 PUBLISHED_CHANGE,未发布UnPublished,自动保存0 ==> PUBLISHED_DRAFT,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例15: 已发布-有修改 PUBLISHED_CHANGE,未发布UnPublished,调试 1 ==> PUBLISHED_CHANGE,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},
		{"测试案例16: 已发布-有修改 PUBLISHED_CHANGE,已发布 Published,自动保存0 ==> PUBLISHED_DRAFT,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例17: 已发布-有修改 PUBLISHED_CHANGE,已发布 Published,调试1 ==> PUBLISHED_CHANGE,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},
		// 实际业务中 发布中 是不可以进行编辑调试相关的
		{"测试案例18: 已发布-有修改 PUBLISHED_CHANGE,发布中Publishing,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例19: 已发布-有修改 PUBLISHED_CHANGE,发布中Publishing,调试1 ==> '','',报错",
			entity.WorkflowPDL{
				WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例20: 已发布-有修改 PUBLISHED_CHANGE,发布失败 fail,自动保存0 ==> PUBLISHED_DRAFT,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例21: 已发布-有修改 PUBLISHED_CHANGE,发布失败 fail,调试1 ==> PUBLISHED_CHANGE,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStatePublishedChange, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},
		//	=================================  已转换 CONVERTED ===============================
		{"测试案例22: 已转换 CONVERTED,待发布 UNPUBLISHED,自动保存0 ==> 待调试 DRAFT,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStateDraft, entity.ActionInsert, ""},
		{"测试案例23: 已转换 CONVERTED,待发布 UNPUBLISHED,调试1 ==> 待调试 ENABLE,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStateEnable, entity.ActionInsert, ""},
		{"测试案例24: 已转换 CONVERTED,发布中 PUBLISHING,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例25: 已转换 CONVERTED,发布中 PUBLISHING,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例26: 已转换 CONVERTED,已发布 PUBLISHED,自动保存0 => 待调试 PUBLISHED_DRAFT,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例27: 已转换 CONVERTED,已发布 PUBLISHED,调试1 => 待调试 PUBLISHED_CHANGE,update",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStatePublishedChange, entity.ActionUpdate, ""},
		{"测试案例28: 已转换 CONVERTED,发布失败 PUBLISHED_FAIL,自动保存0 => 待调试 DRAFT,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT),
			entity.WorkflowPdlStateDraft, entity.ActionInsert, ""},
		{"测试案例29: 已转换 CONVERTED,发布失败 PUBLISHED_FAIL,调试1 => 待调试 ENABLE,insert",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverted, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE),
			entity.WorkflowPdlStateEnable, entity.ActionInsert, ""},
		//	=================================  转换失败 CONVERTED_FAIL ===============================
		{"测试案例30: 转换失败 CONVERTED_FAIL,待发布 UNPUBLISHED,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		{"测试案例31: 转换失败 CONVERTED_FAIL,发布中 PUBLISHING,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例32: 转换失败 CONVERTED_FAIL,已发布 PUBLISHED,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		{"测试案例33: 转换失败 CONVERTED_FAIL,发布失败 PUBLISHED_FAIL,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		{"测试案例34: 转换失败 CONVERTED_FAIL,待发布 UNPUBLISHED,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		{"测试案例35: 转换失败 CONVERTED_FAIL,发布中 PUBLISHING,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例36: 转换失败 CONVERTED_FAIL,已发布 PUBLISHED,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		{"测试案例37: 转换失败 CONVERTED_FAIL,发布失败 PUBLISHED_FAIL,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConvertedFail, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:9 not allow trans"},
		//	=================================  转换中 CONVERTING ===============================
		{"测试案例38: 转换中 CONVERTING,待发布 UNPUBLISHED,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
		{"测试案例39: 转换中 CONVERTING,待发布 UNPUBLISHED,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusUnPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
		{"测试案例40: 转换中 CONVERTING,发布中 PUBLISHING,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例41: 转换中 CONVERTING,发布中 PUBLISHING,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishing},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:3 not allow trans"},
		{"测试案例42: 转换中 CONVERTING,已发布 PUBLISHED,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
		{"测试案例43: 转换中 CONVERTING,已发布 PUBLISHED,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublished},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
		{"测试案例44: 转换中 CONVERTING,发布失败 PUBLISHED_FAIL,自动保存0 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_DRAFT), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
		{"测试案例45: 转换中 CONVERTING,发布失败 PUBLISHED_FAIL,调试1 ==> '','',报错",
			entity.WorkflowPDL{WorkflowState: entity.WorkflowPdlStateConverting, ReleaseStatus: entity.WorkflowPdlReleaseStatusPublishedFail},
			uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE), "", "", "workflowPDL transformPDLRules:8 not allow trans"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assertions := assert.New(t)

			nextState, nextAction, err := TransformWorkflowPDLState(context.Background(), tt.workflow, tt.isDebug)
			assertions.Equal(tt.expectedState, nextState, "State did not match")
			assertions.Equal(tt.expectedAction, nextAction, "Action did not match")
			if tt.expectedErr == "" {
				assertions.NoError(err, "Expected no error,but got %v", err)
			} else {
				assertions.EqualError(err, tt.expectedErr, "Error did not match")
			}

		})
	}
}
