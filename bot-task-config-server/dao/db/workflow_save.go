/*
 * 2024-10-11
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gorm.io/gorm"
)

// SaveWorkflow  更新工作流
func SaveWorkflow(ctx context.Context, params entity.ModifyWorkflowParams) (*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var newWorkflow *entity.Workflow
	if err := db.Transaction(func(tx *gorm.DB) error {
		var err error
		newWorkflow, err = editWorkflowByTx(ctx, tx, params)
		return err
	}); err != nil {
		log.WarnContextf(ctx, "SaveWorkflow fail｜flowId:%s｜err:%+v", params.WorkflowID, err)
		return nil, err
	}
	return newWorkflow, nil
}

// editWorkflowByTx 事物更新工作流
func editWorkflowByTx(ctx context.Context, tx *gorm.DB, params entity.ModifyWorkflowParams) (*entity.Workflow, error) {
	// 构造新的工作流
	newWorkflow := buildWorkflow(params)
	// 1. 取db里的工作流
	oldWorkflow := entity.Workflow{}
	oldWorkflowResult := tx.
		Table(entity.Workflow{}.TableName()).
		Where(entity.WorkflowColumns.WorkflowID+"=? AND "+entity.WorkflowColumns.RobotID+"=? ",
			params.WorkflowID, params.AppBizID).
		Find(&oldWorkflow)
	if oldWorkflowResult.Error != nil {
		log.ErrorContextf(ctx, "editWorkflowByTx|editWorkflowByTx|err:%v", oldWorkflowResult.Error)
		return nil, oldWorkflowResult.Error
	}

	log.InfoContextf(ctx, "editWorkflowByTx|Req.version:%d|db.version:%d", params.Version, oldWorkflow.Version)
	if params.Version != oldWorkflow.Version {
		return nil, errors.ErrWorkflowWrongVersion
	}
	newWorkflow.WorkflowID = oldWorkflow.WorkflowID
	newWorkflow.Version = oldWorkflow.Version + 1
	newWorkflow.DialogJsonEnable = oldWorkflow.DialogJsonEnable
	newWorkflow.WorkflowState = oldWorkflow.WorkflowState
	newWorkflow.ReleaseStatus = oldWorkflow.ReleaseStatus
	newWorkflow.Action = oldWorkflow.Action
	newWorkflow.IsEnable = oldWorkflow.IsEnable
	newWorkflow.RobotId = oldWorkflow.RobotId
	var needNotifyDM bool
	var needSyncVector bool

	equal, _ := protoutil.CompareWorkflowEqual(ctx, oldWorkflow.DialogJsonEnable, newWorkflow.DialogJsonDraft)
	// 调试状态下，如果画布状态为未发布或者画布数据发生变化时，是需要变更DialogJsonEnable字段并通知dm的
	if params.IsDebug == uint32(KEP_WF.SaveWorkflowReq_ENABLE) && (params.HasUnPublishData || !equal) {
		newWorkflow.IsEnable = true
		newWorkflow.DialogJsonEnable = newWorkflow.DialogJsonDraft
		needNotifyDM = true
	}

	// 其他子项（变量、参数、示例问法）改变会将工作流变成已发布-草稿态，点击调试需要流转工作流为待发布，但此时画布json内容没变，此时要满足（调试）校验改变状态
	// 如果自动保存（没有调试-保存测试环境），画布改变了，也需要改变状态
	log.DebugContextf(ctx, "editWorkflowByTx|equal:%+v|HasUnPublishData:%+v", equal, params.HasUnPublishData)
	if (params.IsDebug == uint32(KEP_WF.SaveWorkflowReq_ENABLE) && params.HasUnPublishData) || !equal {
		flowState, action, err := TransformWorkflowState(ctx, oldWorkflow, params.IsDebug)
		if err != nil {
			log.ErrorContextf(ctx, "editWorkflowByTx|TransformWorkflowState Failed err:%v", err)
			return nil, err
		}

		newWorkflow.WorkflowState = flowState
		newWorkflow.Action = action
		newWorkflow.ReleaseStatus = entity.ReleaseStatusUnPublished
		needSyncVector = true
	}

	//log.DebugContextf(ctx, "editWorkflowByTx|newWorkflow:%+v", newWorkflow)
	// 1. 更新工作流,不直接用newWorkflow，解决零值被忽略问题
	if err := oldWorkflowResult.Updates(map[string]interface{}{
		"f_workflow_name":      newWorkflow.WorkflowName,
		"f_desc":               newWorkflow.WorkflowDesc,
		"f_flow_state":         newWorkflow.WorkflowState,
		"f_version":            newWorkflow.Version,
		"f_dialog_json_draft":  newWorkflow.DialogJsonDraft,
		"f_dialog_json_enable": newWorkflow.DialogJsonEnable,
		"f_uin":                newWorkflow.Uin,
		"f_sub_uin":            newWorkflow.SubUin,
		"f_staff_id":           newWorkflow.StaffID,
		"f_release_status":     newWorkflow.ReleaseStatus,
		"f_action":             newWorkflow.Action,
		"f_is_enable":          newWorkflow.IsEnable,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "editWorkflowByTx|Updates|newWorkflow err:%v", err)
		return nil, err
	}
	// 2. 机器人ID和工作流ID的关联关系更新
	if err := updateRobotWorkflow(ctx, tx, params.AppBizID, newWorkflow); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|updateRobotWorkflow|err:%v", err)
		return nil, err
	}
	// 3. 更新工作流与工作流的关系
	if err := UpdateWorkflowRef(ctx, tx, params.AppBizID, params.WorkflowID, params.RefWorkflows); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowRef|err:%v", err)
		return nil, err
	}

	// 4. 知识问答节点 或 知识检索节点中使用的知识类的引用关系
	if err := UpdateWorkflowRefKnowledge(ctx, tx, params.AppBizID, params.WorkflowID, params.KnowledgeRef); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowRefKnowledge|err:%v", err)
		return nil, err
	}

	// 5. 参数提取节点引用的"参数"
	log.InfoContextf(ctx, "editWorkflowByTx| saveType:%+v,needNotifyDM :%+v", params.IsDebug, needNotifyDM)
	if err := UpdateWorkflowParameterRef(ctx, tx, params.AppBizID, params.WorkflowID,
		params.Parameters, needNotifyDM); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowParameterRef|err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "editWorkflowByTx UpdateWorkflowParameterRef success!")

	// 6. 当前工作流引用的自定义变量（API参数）的引用关系维护
	if err := UpdateWorkflowVarParamsRef(ctx, tx, params.AppBizID, oldWorkflow.WorkflowID,
		params.CustomVarIDs); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowVarParamsRef|err:%+v", err)
		return nil, err
	}

	// 7. 更新工作流引用插件的信息
	if err := UpdateWorkflowRefPlugin(ctx, tx, params.AppBizID, params.WorkflowID, params.RefPlugins); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowRefPlugin|err:%v", err)
		return nil, err
	}

	// 8. 更新模型引用信息
	log.InfoContextf(ctx, "editWorkflowByTx|params.CustomModels:%+v", params.CustomModels)
	if err := UpdateWorkflowCustomModels(ctx, tx, params.AppBizID, oldWorkflow.WorkflowID,
		params.CustomModels); err != nil {
		log.WarnContextf(ctx, "editWorkflowByTx|UpdateWorkflowVarParamsRef|err:%+v", err)
		return nil, err
	}
	if needNotifyDM {
		workflow, err := protoutil.JsonToWorkflow(newWorkflow.DialogJsonEnable)
		if err != nil {
			log.WarnContextf(ctx, "editWorkflowByTx|JsonToWorkflow|err:%v", err)
			return nil, errors.ErrWorkflowUIJsonParams
		}
		_, dmErr := sendWorkflowToDM(ctx, params.AppBizID, workflow)
		if dmErr != nil {
			log.ErrorContextf(ctx, "editWorkflowByTx|sendWorkflowToDM|err:%v", dmErr)
			return nil, dmErr
		}
	}
	if needSyncVector {
		// 将向量变更放到最后，保证前面无错再同步
		// 当画布及关联数据有改变，变更画布状态需要同步变更向量(待调试-不可用)
		if err := SaveWorkflowVectorAndRedis(ctx, tx, &newWorkflow, params.AppBizID,
			entity.VectorWorkflow); err != nil {
			log.ErrorContextf(ctx, "editWorkflowByTx|SaveWorkflowCorpusToVector|err:%v", err)
			return nil, err
		}
		// 需要更新示例问法到redis（修复导入的工作流示例问法不同步）
		if err := ExampleSetRedis(ctx, tx, params.AppBizID, params.WorkflowID); err != nil {
			log.WarnContextf(ctx, "editWorkflowByTx|ExampleSetRedis|err:%v", err)
			return nil, err
		}
	}

	return &newWorkflow, nil
}

// buildWorkflow 构造工作流基本修改参数
func buildWorkflow(params entity.ModifyWorkflowParams) entity.Workflow {
	workflow := entity.Workflow{}
	workflow.WorkflowName = params.WorkflowName
	workflow.WorkflowDesc = params.WorkflowDesc
	workflow.Uin = params.Uin
	workflow.SubUin = params.SubUin
	workflow.DialogJsonDraft = params.DialogJsonDraft
	workflow.StaffID = params.StaffID
	return workflow
}

// updateRobotWorkflow 更新机器人与工作流关联关系
func updateRobotWorkflow(ctx context.Context, tx *gorm.DB, robotId string, newWorkflow entity.Workflow) error {
	robotWorkflow := entity.RobotWorkflow{}
	result := tx.Table(robotWorkflow.TableName()).Where("f_robot_id=? AND f_workflow_id=? ",
		robotId, newWorkflow.WorkflowID).Updates(&entity.RobotWorkflow{
		ReleaseStatus: newWorkflow.ReleaseStatus,
		Action:        newWorkflow.Action,
	}).Find(&robotWorkflow)

	log.InfoContextf(ctx, "updateRobotWorkflow robotWorkflow:%+v", robotWorkflow)
	if result.RowsAffected <= 0 {
		log.WarnContextf(ctx, "updateRobotWorkflow err: %s", errors.ErrWorkflowAndAppRef)
		return errors.ErrWorkflowAndAppRef
	}
	return nil
}

// updateWorkflow 更新工作流
//func updateWorkflow(ctx context.Context, tx *gorm.DB, newWorkflow entity.Workflow) error {
//	workflow := &entity.Workflow{
//		WorkflowName:  newWorkflow.WorkflowName,
//		Action:        newWorkflow.Action,
//		ReleaseStatus: newWorkflow.ReleaseStatus,
//		WorkflowDesc:  newWorkflow.WorkflowDesc,
//	}
//	if err := tx.Table(workflow.TableName()).Where("f_workflow_id=? ",
//		newWorkflow.WorkflowID).Updates(workflow).Error; err != nil {
//		log.ErrorContextf(ctx, "updateWorkflow|Update err:%v", err)
//		return err
//	}
//	return nil
//}
