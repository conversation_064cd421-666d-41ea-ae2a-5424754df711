// bot-task-config-server
//
// @(#)workflow_test.go  星期四, 十一月 28, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTransformWorkflowState(t *testing.T) {
	assert := assert.New(t)

	tests := []struct {
		name           string
		workflow       entity.Workflow
		isDebug        uint32
		expectedState  string
		expectedAction string
		expectedErr    string
	}{
		//	================================= 参数非法 ===============================
		// 测试案例1: 非法的isDebug值, 报错
		{"测试案例1: 非法的isDebug值",
			entity.Workflow{WorkflowState: entity.WorkflowStateDraft, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			3, "", "", "workflow isDebug:3 illegal"},

		//	=================================  草稿 DRAFT ===============================
		{"测试案例2: 草稿Draft，未发布UnPublished, 自动保存0 ==> Draft，insert",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateDraft, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStateDraft, entity.ActionInsert, ""},
		{"测试案例3: 草稿Draft，未发布UnPublished, 调试1 ==> Enable，insert",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateDraft, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE),
			entity.WorkflowStateEnable, entity.ActionInsert, ""},

		//	=================================  启用（待发布） ENABLE ===============================
		{"测试案例4: 启用Enable，未发布UnPublished, 自动保存0 ==> Draft，insert",
			entity.Workflow{WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT), entity.WorkflowStateDraft, entity.ActionInsert, ""},
		{"测试案例5: 启用Enable，未发布UnPublished, 调试1 ==> Enable，insert",
			entity.Workflow{WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE), entity.WorkflowStateEnable, entity.ActionInsert, ""},
		{"测试案例6: 启用Enable，已发布Published,自动保存0  ==> PublishedDraft，update",
			entity.Workflow{WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例7: 启用Enable，已发布Published, 调试1 ==> PublishedChange，Update",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE), entity.WorkflowStatePublishedChange, entity.ActionUpdate, ""},
		// Publishing（发布中）的状态，不允许编辑调试，不管flowState和isDebug为任何值报错,
		// workflow transformState:3 not allow trans
		// 实际业务中应该不会出现这种情况
		{"测试案例8: 启用Enable，发布中Publishing，自动保存0 ==> ''，'',报错",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusPublishing},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT), "", "", "workflow transformState:3 not allow trans"},
		{"测试案例9: 启用Enable，发布中Publishing，调试1 ==> ''，'',报错",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusPublishing},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE), "", "", "workflow transformState:3 not allow trans"},

		{"测试案例10: 启用Enable，发布失败PublishedFail，自动保存0 ==> 草稿，insert",
			entity.Workflow{WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusFail},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT), entity.WorkflowStateDraft, entity.ActionInsert, ""},
		{"测试案例11: 启用Enable，发布失败PublishedFail，调试 1==> enable，insert",
			entity.Workflow{
				WorkflowState: entity.WorkflowStateEnable, ReleaseStatus: entity.WorkflowReleaseStatusFail},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE), entity.WorkflowStateEnable, entity.ActionInsert, ""},

		//	=================================  已发布-草稿态 PUBLISHED_DRAFT ===============================
		{"测试案例12: 已发布-草稿态 PUBLISHED_DRAFT，未发布UnPublished，自动保存0 ==> PUBLISHED_DRAFT，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedDraft, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例13: 已发布-草稿态 PUBLISHED_DRAFT，未发布UnPublished，调试 1 ==> PUBLISHED_CHANGE，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedDraft, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE),
			entity.WorkflowStatePublishedChange, entity.ActionUpdate, ""},

		//	=================================  已发布-有修改 PUBLISHED_CHANGE ===============================
		{"测试案例14: 已发布-有修改 PUBLISHED_CHANGE，未发布UnPublished，自动保存0 ==> PUBLISHED_DRAFT，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例15: 已发布-有修改 PUBLISHED_CHANGE，未发布UnPublished，调试 1 ==> PUBLISHED_CHANGE，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusUnPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE),
			entity.WorkflowStatePublishedChange, entity.ActionUpdate, ""},
		{"测试案例16: 已发布-有修改 PUBLISHED_CHANGE，已发布 Published，自动保存0 ==> PUBLISHED_DRAFT，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusPublished},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例17: 已发布-有修改 PUBLISHED_CHANGE，已发布 Published，调试1 ==> PUBLISHED_CHANGE，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusPublished},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE),
			entity.WorkflowStatePublishedChange, entity.ActionUpdate, ""},
		// 实际业务中 发布中 是不可以进行编辑调试相关的
		{"测试案例18: 已发布-有修改 PUBLISHED_CHANGE，发布中Publishing，自动保存0 ==> ''，'',报错",
			entity.Workflow{
				WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusPublishing},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT), "", "", "workflow transformState:3 not allow trans"},
		{"测试案例19: 已发布-有修改 PUBLISHED_CHANGE，发布中Publishing，调试1 ==> ''，'',报错",
			entity.Workflow{
				WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusPublishing},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE), "", "", "workflow transformState:3 not allow trans"},
		{"测试案例20: 已发布-有修改 PUBLISHED_CHANGE，，发布失败 fail，自动保存0 ==> PUBLISHED_DRAFT，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusFail},
			uint32(KEP_WF.SaveWorkflowReq_DRAFT),
			entity.WorkflowStatePublishedDraft, entity.ActionUpdate, ""},
		{"测试案例21: 已发布-有修改 PUBLISHED_CHANGE，发布失败 fail，调试1 ==> PUBLISHED_CHANGE，update",
			entity.Workflow{WorkflowState: entity.WorkflowStatePublishedChange, ReleaseStatus: entity.WorkflowReleaseStatusFail},
			uint32(KEP_WF.SaveWorkflowReq_ENABLE),
			entity.WorkflowStatePublishedChange, entity.ActionUpdate, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nextState, nextAction, err := TransformWorkflowState(context.Background(), tt.workflow, tt.isDebug)
			assert.Equal(tt.expectedState, nextState, "State did not match")
			assert.Equal(tt.expectedAction, nextAction, "Action did not match")
			if tt.expectedErr == "" {
				assert.NoError(err, "Expected no error, but got %v", err)
			} else {
				assert.EqualError(err, tt.expectedErr, "Error did not match")
			}
		})
	}
}
