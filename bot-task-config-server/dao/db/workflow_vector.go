// bot-task-config-server
//
// @(#)workflow_vector.go  星期三, 十月 16, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gorm.io/gorm"
)

// SwitchWorkflowState 切换工作流状态
func SwitchWorkflowState(ctx context.Context, robotID string, workflow *entity.Workflow) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		err := SwitchWorkflowStateTx(ctx, tx, robotID, workflow)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState fail, err:%+v", err)
		return err
	}

	return nil
}

// SwitchWorkflowStateTx 切换工作流状态
func SwitchWorkflowStateTx(ctx context.Context, tx *gorm.DB, robotID string, workflow *entity.Workflow) error {
	sid := util.RequestID(ctx)
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffId := util.StaffID(ctx)

	// 发布状态中的工作流，不允许更改状态
	if workflow.ReleaseStatus == entity.ReleaseStatusPublishing {
		return errors.ErrWorkflowPublishing
	}
	isDebug := uint32(KEP_WF.SaveWorkflowReq_ENABLE)
	// 草稿态的停启用相当于自动保存，其他状态相当于调试
	// 见状态流转 https://iwiki.woa.com/p/**********
	if workflow.WorkflowState == entity.WorkflowStateDraft ||
		workflow.WorkflowState == entity.WorkflowStatePublishedDraft {
		isDebug = uint32(KEP_WF.SaveWorkflowReq_DRAFT)
	}
	flowState, action, err := TransformWorkflowState(ctx, *workflow, isDebug)
	if err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowStateTx|TransformWorkflowState Failed err:%v", err)
		return err
	}

	// 更新工作流的状态
	if err := tx.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?", robotID, workflow.WorkflowID).
		Updates(map[string]interface{}{
			"f_is_enable":      workflow.IsEnable,
			"f_action":         action,
			"f_flow_state":     flowState,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_update_time":    time.Now(),
			"f_staff_id":       staffId,
			"f_uin":            uin,
			"f_sub_uin":        subUin,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|SwitchWorkflowStateTx|err:%+v", sid, err)
		return err
	}

	// 更新向量
	if err := SaveWorkflowVectorAndRedis(ctx, tx, workflow, robotID, entity.VectorWorkflowEnable); err != nil {
		log.ErrorContextf(ctx, "editWorkflowByTx|SaveWorkflowCorpusToVector|err:%v", err)
		return err
	}
	return nil
}

// getVectorWorkflowEnable 获取工作流向量开关状态1：可用；0：不可用
func getVectorWorkflowEnable(ctx context.Context, robotId, flowId string) (uint64, error) {
	vdb := vdao.NewDao()
	wfVector, err := vdb.GetWorkflowCorpusVector(ctx, robotId, flowId)
	if err != nil {
		return 0, err
	}
	if len(wfVector) == 0 || len(wfVector[0].GetAttributeFields().GetFields()) == 0 {
		return 0, nil
	}
	for j := range wfVector[0].GetAttributeFields().GetFields() {
		if wfVector[0].GetAttributeFields().Fields[j].FieldName == vdao.FieldWorkflowEnable {
			isEnable := wfVector[0].GetAttributeFields().Fields[j].FieldValueUint64
			if isEnable > 0 {
				return 1, nil
			} else {
				return 0, nil
			}
		}
	}
	return 0, nil
}

// SaveWorkflowVectorAndRedis 工作流相关信息保存向量（工作流信息，工作流停启用开关，工作流示例问法）
func SaveWorkflowVectorAndRedis(ctx context.Context, tx *gorm.DB, newWorkflow *entity.Workflow,
	robotID string, vectorType int) error {
	newVectorEnable := vdao.GetWfVectorEnableByFlowState(newWorkflow.WorkflowState, newWorkflow.IsEnable)
	// 只涉及开关向量操作时，判断最新需要保存的数据和历史向量开关数据是否一致；一致则不做任何处理
	if vectorType == entity.VectorWorkflowEnable {
		oldVectorEnable, err := getVectorWorkflowEnable(ctx, newWorkflow.RobotId, newWorkflow.WorkflowID)
		if err != nil {
			log.ErrorContextf(ctx, "SaveWorkflowVectorAndRedis getVectorWorkflowEnable"+
				"workflowID:%s|err:%+v", newWorkflow.WorkflowID, err)
			return err
		}
		log.InfoContextf(ctx, "SaveWorkflowVectorAndRedis workflowID:%s|newWorkflowState:%s|"+
			"newWorkflowIsEnable:%+v|newVectorEnable:%+v|oldVectorEnable:%+v",
			newWorkflow.WorkflowID, newWorkflow.WorkflowState, newWorkflow.IsEnable, newVectorEnable, oldVectorEnable)
		if oldVectorEnable == newVectorEnable {
			log.InfoContextf(ctx, "SaveWorkflowVectorAndRedis VectorEnable no change")
			return nil
		}
	}

	var examples []*entity.WorkflowExample
	vdb := vdao.NewDao()

	// 获取工作流下面示例问法的信息
	examples, err := ListFlowExamByBootIdTx(ctx, tx, newWorkflow.WorkflowID, robotID)
	if err != nil {
		return err
	}

	err = vdb.SaveWorkflowToVector(ctx, tx, robotID, examples, newWorkflow)
	if err != nil {
		log.ErrorContextf(ctx, "%s|SaveWorkflowVectorAndRedis,err:%s", err)
		return err
	}
	if err := vdao.WorkflowEnableSetRedis(ctx, robotID, newWorkflow.WorkflowID, entity.SandboxEnv,
		newVectorEnable); err != nil {
		return err
	}
	return nil
}

// DelVectorStore 删除VectorStore
func DelVectorStore(ctx context.Context, tx *gorm.DB, robotId string, bizId []string) error {
	if err := tx.Table(entity.VectorStore{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_biz_id IN ?", robotId, bizId).
		Updates(map[string]interface{}{
			"f_is_deleted": 1,
		}).Error; err != nil {
		return err
	}
	return nil
}
