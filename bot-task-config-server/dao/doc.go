// KEP.bot-task-config-server
//
// @(#)doc.go  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

/*
Package dao is DAO layer top-level directory.

The Data Access Object (DAO) pattern is a structural pattern that allows us to isolate the application/business
layer from the persistence layer (usually a relational database, but it could be any other persistence mechanism)
using an abstract API.

The functionality of this API is to hide from the application all the complexities involved in performing CRUD
operations in the underlying storage mechanism. This permits both layers to evolve separately without knowing
anything about each other.

Usually, the DAO package is responsible for two concepts. Encapsulating the details of the persistence layer and
providing a CRUD interface for a single entity.
*/
package dao
