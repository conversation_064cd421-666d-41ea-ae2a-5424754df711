package publish

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// Dao 发布相关 Dao
type Dao interface {
	// 查询未发布数据
	queryUnpublish

	// 查询已发布数据
	queryPublished

	// 更新状态
	update

	// 执行发布
	execute
}

// queryUnpublish 查询未发布数据
type queryUnpublish interface {
	// GetUnPublishPDL 查询 t_workflow_pdl 待发布的数据
	GetUnPublishPDL(ctx context.Context, robotID string, pdlIDs []string) ([]*entity.WorkflowPDL, error)
}

// queryPublish 查询已发布数据
type queryPublished interface {
	// CountPublishedPDLs 统计应用已发布PDL数量
	CountPublishedPDLs(ctx context.Context, robotID string, envType uint32) (int64, error)
	// IsPDLPublishedById 通过ID判断PDL是否发布
	IsPDLPublishedById(ctx context.Context, robotID, workflowPDLID string, envType uint32) (bool, error)
	// GetPublishedPDLs 查询 t_workflow_pdl 发布后的数据
	GetPublishedPDLs(ctx context.Context, robotID string, workflowPDLIDs []string) ([]*entity.WorkflowPDL, error)
}

// update 更新状态
type update interface {
	// UpdatePDLStaffID 更新 t_workflow_pdl 用户ID
	UpdatePDLStaffID(ctx context.Context, db *gorm.DB,
		workflowPDLs []*entity.WorkflowPDL, staffID uint64) error

	// UpdatePDLReleaseStatus 更新 t_workflow_pdl 发布状态
	UpdatePDLReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowPDLs []*entity.WorkflowPDL, releaseStatus string) error
}

// execute 执行发布
type execute interface {
	// ---- DB数据 ---

	// PublishPDL 发布 t_workflow_pdl 数据
	PublishPDL(ctx context.Context, tx *gorm.DB,
		workflowPDLs []*entity.WorkflowPDL) error

	// ---- DB数据 ---
}

// dao ...
type dao struct {
	workflowDB     *gorm.DB
	workflowProdDB *gorm.DB
}

// NewDao new dao
func NewDao() Dao {
	return &dao{
		workflowDB:     database.GetLLMRobotWorkflowGORM().Debug(),
		workflowProdDB: database.GetLLMRobotWorkflowProdGORM().Debug(),
	}
}
