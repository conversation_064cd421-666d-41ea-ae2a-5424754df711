package publish

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

// PublishPDL 发布 t_workflow_pdl 数据
func (d dao) PublishPDL(ctx context.Context, tx *gorm.DB, workflowPDLs []*entity.WorkflowPDL) error {
	log.InfoContextf(ctx, "PublishPDL len(workflowPDLs):%d", len(workflowPDLs))
	if len(workflowPDLs) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowPDL, 0)
	updates := make([]*entity.WorkflowPDL, 0)
	deletes := make([]*entity.WorkflowPDL, 0)

	for _, pdl := range workflowPDLs {
		// 发布状态更新为已发布
		pdl.ReleaseStatus = entity.ReleaseStatusPublished

		switch pdl.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			pdl.CreateTime = time.Time{}
			pdl.UpdateTime = time.Time{}
			inserts = append(inserts, pdl)
		case entity.ActionUpdate:
			updates = append(updates, pdl)
		case entity.ActionDelete:
			deletes = append(deletes, pdl)
		default:
			log.WarnContextf(ctx, "PublishPDL pdl:%+v, Action:%s illegal",
				pdl, pdl.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishPDL tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowPdlColumns.PdlID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowPdlColumns.PdlSnapshotVersion,
					entity.WorkflowPdlColumns.WorkflowID,
					entity.WorkflowPdlColumns.WorkflowName,
					entity.WorkflowPdlColumns.WorkflowState,
					entity.WorkflowPdlColumns.Version,
					entity.WorkflowPdlColumns.RobotID,
					entity.WorkflowPdlColumns.DialogJsonEnable,
					entity.WorkflowPdlColumns.Parameter,
					entity.WorkflowPdlColumns.PdlContent,
					entity.WorkflowPdlColumns.ToolsInfo,
					entity.WorkflowPdlColumns.UserConstraints,
					entity.WorkflowPdlColumns.StaffID,
					entity.WorkflowPdlColumns.ReleaseStatus,
					entity.WorkflowPdlColumns.IsDeleted,
					entity.WorkflowPdlColumns.IsEnable,
					entity.WorkflowPdlColumns.Action,
					entity.WorkflowPdlColumns.PdlCreateTime,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishPDL tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishPDL len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}
