package publish

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"gorm.io/gorm"
)

// GetUnPublishPDL 查询 t_workflow_pdl 待发布的数据
func (d dao) GetUnPublishPDL(ctx context.Context, robotID string, pdlIDs []string) (
	[]*entity.WorkflowPDL, error) {
	log.InfoContextf(ctx, "GetUnPublishPDL, robotID: %s, pdlIDs: %+v", robotID, pdlIDs)
	workflowPDLs := make([]*entity.WorkflowPDL, 0)
	if len(pdlIDs) == 0 {
		return workflowPDLs, nil
	}
	query := fmt.Sprintf("%s = ? AND %s IN ? AND %s != ?",
		entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.PdlID, entity.WorkflowPdlColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(query, robotID, pdlIDs, entity.WorkflowPdlReleaseStatusPublished).
		Find(&workflowPDLs).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishPDL workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishPDL success, len(workflowPDLs): %d", len(workflowPDLs))
	return workflowPDLs, nil
}

// CountPublishedPDLs 统计应用已发布PDL数量
func (d dao) CountPublishedPDLs(ctx context.Context, robotID string, envType uint32) (int64, error) {
	log.InfoContextf(ctx, "CountPublishedPDLs, robotID: %s, envType: %d", robotID, envType)
	if len(robotID) == 0 {
		return 0, nil
	}
	var db *gorm.DB
	var query string
	var args []interface{}
	switch KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType)) {
	case KEP.GetTaskFlowReleaseStatusReq_TEST: // 测试环境
		db = d.workflowDB
		query = fmt.Sprintf("%s = ? AND %s IN ?",
			entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.WorkflowState)
		args = append(args, robotID)
		args = append(args, []string{
			entity.WorkflowPdlStateEnable, entity.WorkflowPdlStatePublishedChange})
	case KEP.GetTaskFlowReleaseStatusReq_PROD: // 线上环境
		db = d.workflowProdDB
		query = fmt.Sprintf("%s = ? AND %s = ?",
			entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.ReleaseStatus)
		args = append(args, robotID)
		args = append(args, entity.WorkflowPdlReleaseStatusPublished)
	default:
		log.ErrorContextf(ctx, "CountPublishedPDLs, envType: %d illegal", envType)
		return 0, fmt.Errorf("envType: %d illegal", envType)
	}

	var count int64
	err := db.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(query, args...).Count(&count).Error
	if err != nil {
		log.ErrorContextf(ctx, "CountPublishedPDLs db.Count err: %+v", err)
		return 0, err
	}
	log.InfoContextf(ctx, "CountPublishedPDLs success, count: %d", count)
	return count, nil
}

// GetPublishedPDLs 查询 t_workflow_pdl 发布后的数据
func (d dao) GetPublishedPDLs(ctx context.Context, robotID string, workflowPDLIDs []string) (
	[]*entity.WorkflowPDL, error) {
	log.InfoContextf(ctx, "GetPublishedPDLs, robotID: %s, workflowPDLIDs: %+v", robotID, workflowPDLIDs)
	workflowPDLs := make([]*entity.WorkflowPDL, 0)
	if len(workflowPDLIDs) == 0 {
		return workflowPDLs, nil
	}
	query := fmt.Sprintf("%s = ? AND %s IN ? AND %s = ?",
		entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.PdlID, entity.WorkflowPdlColumns.ReleaseStatus)
	err := d.workflowProdDB.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(query, robotID, workflowPDLIDs, entity.WorkflowPdlReleaseStatusPublished).
		Find(&workflowPDLs).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetPublishedPDLs workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetPublishedPDLs success, len(workflowPDLs): %d", len(workflowPDLs))
	return workflowPDLs, nil
}

func (d dao) IsPDLPublishedById(ctx context.Context, robotID, workflowPDLID string, envType uint32) (bool, error) {
	log.InfoContextf(ctx, "IsPDLPublishedById|robotID:%s|workflowId:%s|envType: %d",
		robotID, workflowPDLID, envType)
	if len(robotID) == 0 || len(workflowPDLID) == 0 {
		return false, nil
	}
	var db *gorm.DB
	var query string
	var args []interface{}
	switch KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType)) {
	case KEP.GetTaskFlowReleaseStatusReq_TEST: // 测试环境
		db = d.workflowDB
		query = fmt.Sprintf("%s = ? AND %s = ? AND %s IN ?",
			entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.WorkflowID, entity.WorkflowPdlColumns.WorkflowState)
		args = append(args, robotID, workflowPDLID)
		args = append(args, []string{
			entity.WorkflowPdlStateEnable, entity.WorkflowPdlStatePublishedChange})
	case KEP.GetTaskFlowReleaseStatusReq_PROD: // 线上环境
		db = d.workflowProdDB
		query = fmt.Sprintf("%s = ? AND %s = ? AND %s = ?",
			entity.WorkflowPdlColumns.RobotID, entity.WorkflowPdlColumns.WorkflowID, entity.WorkflowPdlColumns.ReleaseStatus)
		args = append(args, robotID, workflowPDLID)
		args = append(args, entity.WorkflowPdlReleaseStatusPublished)
	default:
		log.ErrorContextf(ctx, "IsPDLPublishedById|envType: %d illegal", envType)
		return false, fmt.Errorf("envType: %d illegal", envType)
	}

	var count int64
	err := db.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(query, args...).Count(&count).Error
	if err != nil {
		log.ErrorContextf(ctx, "IsPDLPublishedById db.Count err: %+v", err)
		return false, err
	}
	log.InfoContextf(ctx, "IsPDLPublishedById success, count: %d", count)
	if count > 0 {
		return true, nil
	}

	return false, nil
}
