package publish

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"gorm.io/gorm"
)

// UpdatePDLStaffID 更新 t_workflow_pdl 用户ID
func (d dao) UpdatePDLStaffID(ctx context.Context, db *gorm.DB,
	workflowPDLs []*entity.WorkflowPDL, staffID uint64) error {
	log.InfoContextf(ctx, "UpdatePDLStaffID, len(workflowPDLs):%d, staffID:%v",
		len(workflowPDLs), staffID)
	if len(workflowPDLs) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowPDLs))
	for _, pdl := range workflowPDLs {
		ids = append(ids, pdl.ID)
	}
	err := db.Debug().WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowPdlColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowPdlColumns.StaffID: staffID,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdatePDLStaffID db.Updates err:%+v", err)
		return err
	}
	// 这里需要重置工作流StaffID
	for _, pdl := range workflowPDLs {
		pdl.StaffID = staffID
	}
	log.InfoContextf(ctx, "UpdatePDLStaffID success")
	return nil
}

// UpdatePDLReleaseStatus 更新 t_workflow_pdl 发布状态
func (d dao) UpdatePDLReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowPDLs []*entity.WorkflowPDL, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdatePDLReleaseStatus, len(workflowPDLs):%d, releaseStatus:%s",
		len(workflowPDLs), releaseStatus)
	if len(workflowPDLs) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowPDLs))
	for _, pdl := range workflowPDLs {
		ids = append(ids, pdl.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowPDL{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowPdlColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowPdlColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdatePDLReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdatePDLReleaseStatus success")
	return nil
}
