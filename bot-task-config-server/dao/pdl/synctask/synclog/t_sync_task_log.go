// bot-task-config-server
//
// @(#)t_sync_task_log.go  Friday, December 22, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synclog

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// RecordSyncTaskLog ...
func RecordSyncTaskLog(ctx context.Context, stl entity.SyncTaskLog) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CreateSyncTaskLog|%s|%s|%+v", sid, stl.RobotID, stl)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g = g.Table(entity.SyncTaskLog{}.PDLTableName()).Create(stl)
	log.Infof("O|CreateSyncTaskLog|%s|%v|ERR:%v|%s", sid, stl.RobotID, g.Error, time.Since(t0))
	return g.Error
}

// ListSyncTaskLogByTask ...
func ListSyncTaskLogByTask(ctx context.Context, robotID string, taskID uint64) ([]entity.SyncTaskLog, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListSyncTaskLogByTask|%s|%s|%v", sid, robotID, taskID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var data []entity.SyncTaskLog
	g = g.Table(entity.SyncTaskLog{}.PDLTableName()).
		Where("robot_id = ?", robotID).
		Where("sync_task_id = ?", taskID).
		Find(&data)
	log.Infof("O|ListSyncTaskLogByTask|%s|%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// ListSyncTaskLogByEvent 最后收到的同步任务事件
func ListSyncTaskLogByEvent(ctx context.Context, robotID string, taskID uint64) ([]entity.SyncTaskLog, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListSyncTaskLogByEvent|%s|%s|%v", sid, robotID, taskID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var data []entity.SyncTaskLog
	g = g.Table(entity.SyncTaskLog{}.PDLTableName()).
		Where("robot_id = ?", robotID).
		Where("sync_task_id = ?", taskID).
		Where("type = ?", string(entity.LogTypeEvent)).
		Order("create_time ASC").
		Find(&data)
	log.Infof("O|ListSyncTaskLogByEvent|%s|%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}
