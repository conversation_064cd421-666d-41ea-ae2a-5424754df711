// bot-task-config-server
//
// @(#)t_sync_task.go  Friday, December 22, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// CreateSyncTask ...
func CreateSyncTask(ctx context.Context, st entity.SyncTask) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CreateSyncTask|%s|%+v", sid, st)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g = g.Table(entity.SyncTask{}.PDLTableName()).Create(st)
	log.Infof("O|CreateSyncTask|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}

// UpdateSyncTaskStatus ...
func UpdateSyncTaskStatus(ctx context.Context, robotID string, taskID uint64, status entity.TaskStatus) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|UpdateSyncTaskStatus|%s|%s|%d|%d", sid, robotID, taskID, status)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g.Table(entity.SyncTask{}.PDLTableName()).
		Where("robot_id = ?", robotID).
		Where("task_id = ?", taskID).
		Update("status", uint(status))
	log.Infof("O|UpdateSyncTaskStatus|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}

// UpdateSyncTaskStatusAndCount ...
func UpdateSyncTaskStatusAndCount(ctx context.Context, robotID string, taskID uint64,
	status entity.TaskStatus, successCount, failedCount int) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|UpdateSyncTaskStatusAndCount|%s|%s|%d|%d|%d|%d", sid, robotID, taskID,
		status, successCount, failedCount)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g.Table(entity.SyncTask{}.PDLTableName()).
		Where("robot_id = ?", robotID).
		Where("task_id = ?", taskID).
		Updates(map[string]any{
			"status":        uint(status),
			"success_count": successCount,
			"failed_count":  failedCount,
			"done_time":     time.Now(),
		})
	log.Infof("O|UpdateSyncTaskStatusAndCount|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}

// GetSyncTask 按任务 id 查询任务
func GetSyncTask(ctx context.Context, robotID string, taskID uint64) (entity.SyncTask, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|GetSyncTask|%s|%s|%d", sid, robotID, taskID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var st entity.SyncTask
	g.Table(entity.SyncTask{}.PDLTableName()).
		Where("robot_id = ?", robotID).
		Where("task_id = ?", taskID).
		First(&st)
	log.Infof("O|GetSyncTask|%s|%+v|ERR:%v|%s", sid, st, g.Error, time.Since(t0))
	return st, g.Error
}

// CountSyncTaskByStatus 统计整个表的任务状态, 不分机器人
func CountSyncTaskByStatus(ctx context.Context, status entity.TaskStatus) uint32 {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CountSyncTaskByStatus|%s", sid)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var c int64
	g.Table(entity.SyncTask{}.PDLTableName()).Where("status", uint(status)).Count(&c)
	log.Infof("O|CountSyncTaskByStatus|%s|%d|ERR:%v|%s", sid, c, g.Error, time.Since(t0))
	return uint32(c)
}

// ListSyncTasksByStatus 统计整个表的任务状态, 不分机器人
func ListSyncTasksByStatus(ctx context.Context, status entity.TaskStatus, limit int) ([]entity.SyncTask, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListSyncTasksByStatus|%s", sid)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var data []entity.SyncTask
	g.Table(entity.SyncTask{}.PDLTableName()).
		Where("status", uint(status)).
		Limit(limit).
		Find(&data)
	log.Infof("O|ListSyncTasksByStatus|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}
