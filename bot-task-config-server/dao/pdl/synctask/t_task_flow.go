// bot-task-config-server
//
// @(#)t_task_flow.go  Monday, December 25, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// CountUnpublishedPDL 统计未发布的PDL数量
func CountUnpublishedPDL(ctx context.Context, robotID string) int64 {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CountUnpublishedPDL|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var c int64
	g.Table("t_workflow_pdl").
		Where("f_robot_id = ?", robotID).
		Where("f_release_status IN ?", []string{entity.WorkflowPdlReleaseStatusUnPublished,
			entity.WorkflowPdlReleaseStatusPublishedFail}).
		Where("f_flow_state IN ?", []string{entity.WorkflowPdlStateEnable,
			entity.WorkflowPdlStatePublishedChange}).
		Count(&c)
	log.Infof("O|CountUnpublishedPDL|%s|%d|ERR:%v|%s", sid, c, g.Error, time.Since(t0))
	return c
}

// ListUnpublishedPDL 查询未发布的PDL
func ListUnpublishedPDL(ctx context.Context, robotID string, status []string) ([]entity.WorkflowPDL, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListUnpublishedPDL|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var data []entity.WorkflowPDL
	g.Table("t_workflow_pdl").
		Select("f_id", "f_pdl_id", "f_workflow_id", "f_workflow_name", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_enable", "f_parameter", "f_pdl_content", "f_tools_info", "f_user_constraints",
			"f_staff_id", "f_release_status", "f_is_deleted", "f_is_enable", "f_action", "f_pdl_time",
			"f_create_time", "f_update_time").
		Where("f_robot_id = ?", robotID).
		Where("f_release_status IN ?", status).
		Where("f_flow_state IN ?", []string{entity.WorkflowPdlStateEnable,
			entity.WorkflowPdlStatePublishedChange}).
		Find(&data)
	log.Infof("O|ListUnpublishedPDL|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// UpdatePDLReleaseStatus ...
func UpdatePDLReleaseStatus(ctx context.Context, robotID string, pdlIDs []string, releaseStatus string) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|UpdatePDLReleaseStatus|%s|%v|%v|%v", sid, robotID, pdlIDs, releaseStatus)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g.Table("t_workflow_pdl").
		Where("f_robot_id = ?", robotID).
		Where("f_pdl_id IN ?", pdlIDs).
		Update("f_release_status", releaseStatus)
	log.Infof("O|UpdatePDLReleaseStatus|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}

// CountUnpublishedPDLWithQuery 查询未发布的任务数量
func CountUnpublishedPDLWithQuery(ctx context.Context, robotID string, params entity.ListPDLParams) int64 {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CountUnpublishedPDLWithQuery|%s|%v", sid, robotID)

	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table("t_workflow_pdl").Where("f_robot_id = ?", robotID)

	if len(params.Query) > 0 {
		g = g.Where("f_workflow_name LIKE ?", "%"+types.ConvertSqlFuzzySearch(params.Query)+"%")
	}

	if len(params.FlowState) > 0 {
		g = g.Where("f_flow_state IN ?", params.FlowState)
	}
	if len(params.ReleaseStatus) > 0 {
		g = g.Where("f_release_status IN ?", params.ReleaseStatus)
	}
	if len(params.Actions) > 0 {
		g = g.Where("f_action IN ?", params.Actions)
	}
	if !params.StartTime.IsZero() {
		g = g.Where("f_update_time >= ?", params.StartTime)
	}
	if !params.EndTime.IsZero() {
		g = g.Where("f_update_time <= ?", params.EndTime)
	}
	var c int64
	g.Count(&c)
	log.Infof("O|CountUnpublishedPDLWithQuery|%s|%d|ERR:%v|%s", sid, c, g.Error, time.Since(t0))
	return c
}

// ListUnpublishedPDLWithQuery 查询未发布的任务
func ListUnpublishedPDLWithQuery(ctx context.Context, robotID string, params entity.ListPDLParams) (
	[]entity.WorkflowPDL, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListUnpublishedPDLWithQuery|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var data []entity.WorkflowPDL
	g = g.Table("t_workflow_pdl").
		Select("f_id", "f_pdl_id", "f_workflow_id", "f_workflow_name", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_enable", "f_parameter", "f_pdl_content", "f_tools_info", "f_user_constraints",
			"f_staff_id", "f_release_status", "f_is_deleted", "f_is_enable", "f_action", "f_pdl_time",
			"f_create_time", "f_update_time").
		Where("f_robot_id = ?", robotID)

	if len(params.Query) > 0 {
		g = g.Where("f_workflow_name LIKE ?", "%"+types.ConvertSqlFuzzySearch(params.Query)+"%")
	}

	if len(params.FlowState) > 0 {
		g = g.Where("f_flow_state IN ?", params.FlowState)
	}
	if len(params.ReleaseStatus) > 0 {
		g = g.Where("f_release_status IN ?", params.ReleaseStatus)
	}
	if len(params.Actions) > 0 {
		g = g.Where("f_action IN ?", params.Actions)
	}
	if !params.StartTime.IsZero() {
		g = g.Where("f_update_time >= ?", params.StartTime)
	}
	if !params.EndTime.IsZero() {
		g = g.Where("f_update_time <= ?", params.EndTime)
	}
	g = g.Order("f_update_time DESC")
	pageSize := uint32(15)
	page := uint32(1)
	if params.PageSize > 0 {
		pageSize = params.PageSize
	}
	if params.Page > 0 {
		page = params.Page
	}
	offset := (page - 1) * pageSize
	g.Offset(int(offset)).Limit(int(pageSize)).Find(&data)
	log.Infof("O|ListUnpublishedPDLWithQuery|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// ListPDLByIDs 按 id 查对应的任务流程
func ListPDLByIDs(ctx context.Context, db *gorm.DB, robotID string, ids []string) ([]entity.WorkflowPDL, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListPDLByIDs|%s|%v|%v|%v|db:%p", sid, robotID, len(ids), ids, db)
	if len(ids) == 0 {
		return nil, nil
	}
	g := db.WithContext(ctx).Debug()

	var data []entity.WorkflowPDL
	g.Table("t_workflow_pdl").
		Select("f_id", "f_pdl_id", "f_workflow_id", "f_workflow_name", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_enable", "f_parameter", "f_pdl_content", "f_tools_info", "f_user_constraints",
			"f_staff_id", "f_release_status", "f_is_deleted", "f_is_enable", "f_action", "f_pdl_time",
			"f_create_time", "f_update_time").
		Where("f_robot_id = ?", robotID).
		Where("f_pdl_id IN ?", ids).
		Find(&data)
	log.Infof("O|ListPDLByIDs|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// CreatePDLReleaseHistory 创建PDL发布版本
func CreatePDLReleaseHistory(ctx context.Context, historyPDLs []*entity.WorkflowPDLPublishHistory) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CreatePDLReleaseHistory|%s|%+v", sid, historyPDLs)
	if len(historyPDLs) == 0 {
		return nil
	}
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g = g.Table(entity.WorkflowPDLPublishHistory{}.TableName()).Create(historyPDLs)
	log.Infof("O|CreatePDLReleaseHistory|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}
