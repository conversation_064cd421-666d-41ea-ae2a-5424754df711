// Package permission 权限校验
package permission

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/PB_SmartService"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// VerifyPermission 去底座的权限底座验证权限
func VerifyPermission(ctx context.Context, uin, subAccountUin string, action string) (bool, error) {
	if len(uin) == 0 || len(subAccountUin) == 0 {
		log.WarnContext(ctx, "VerifyPermission|uin&subAccountUin is empty")
		return true, nil
	}
	req := &PB_SmartService.VerifyPermissionsReq{
		RequestId:     util.RequestID(ctx), // trace.SpanContextFromContext(ctx).TraceID().String(),
		Uin:           uin,                 // "************" xinghui 的开发环境账号
		SubAccountUin: subAccountUin,
		ProductType:   entity.ProductTypeLKE,
		Conditions: []*PB_SmartService.VerifyPermissionCond{
			{
				Action:    action,
				Resources: nil,
				// 现在没有机器人级别的权限
				//Resources: []*PB_SmartService.PermissionResource{
				//	{
				//		ResourceType: entity.ResourceTypeAppKey,
				//		ResourceIds:  []string{"*"},
				//	},
				//},
			},
		},
	}
	resp, err := proxy.GetAccessManagerProxy().VerifyPermission(ctx, req)
	log.InfoContextf(ctx, "VerifyPermission|req: %+v|resp: %+v", req, resp)
	if err != nil {
		log.ErrorContextf(ctx, "VerifyPermission|ERROR|err:%s", err.Error())
		return false, err
	}

	if resp.Code != 0 {
		log.ErrorContextf(ctx, "VerifyPermission|code:%d|msg:%s", resp.Code, resp.Message)
		return false, fmt.Errorf("%d %s", resp.Code, resp.Message)
	}

	log.InfoContextf(ctx, "VerifyPermission|States:%+v", resp.States)

	for _, state := range resp.States {
		log.InfoContextf(ctx, "VerifyPermission|action:%s|IsAllowed:%d", state.Action, state.IsAllowed)
		if state.Action == action {
			return state.IsAllowed == entity.PermissionAllow, nil
		}
	}

	return false, nil
}

// VerifyResource 验证资源
func VerifyResource(ctx context.Context, uin, subAccountUin string, botBizID uint64) (bool, error) {
	if uin == "" && subAccountUin == "" {
		log.WarnContext(ctx, "VerifyResource|uin&subAccountUin is empty")
		return true, nil
	}

	resourceIDs, err := GetUserResource(ctx, uin, subAccountUin)
	if err != nil {
		log.InfoContextf(ctx, "VerifyPermission|GetUserResource|err:%+v", err)
		return false, err
	}
	for _, id := range resourceIDs {
		if id == entity.AllResourcePermissionID {
			log.InfoContextf(ctx, "uin:%s subAccountUin:%s has all resource permission", uin, subAccountUin)
			return true, nil
		}
		if id == fmt.Sprintf("%d", botBizID) {
			log.InfoContextf(ctx, "uin:%s subAccountUin:%s has %d resource permission", uin,
				subAccountUin, botBizID)
			return true, nil
		}
	}
	return false, nil
}

// GetUserResource 获取用户资源
func GetUserResource(ctx context.Context, uin, subAccountUin string) ([]string, error) {
	if uin == "" && subAccountUin == "" {
		log.WarnContext(ctx, "GetUserResource|uin&subAccountUin is empty")
		return []string{entity.AllResourcePermissionID}, nil
	}

	req := &PB_SmartService.ListPermissionsReq{
		RequestId:     util.RequestID(ctx),
		Uin:           uin,
		SubAccountUin: subAccountUin,
		ProductType:   entity.ProductTypeLKE,
		Conditions: []*PB_SmartService.ListPermissionCond{
			{PermissionId: entity.ListRobotPermissionID},
		},
	}
	rsp, err := proxy.GetAccessManagerProxy().ListPermissions(ctx, req)
	log.InfoContextf(ctx, "GetUserResource|req:%+v rsp:%+v", req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserResource|req:%+v err:%+v", req, err)
		return nil, err
	}
	if rsp.GetCode() != 0 {
		log.ErrorContextf(ctx, "GetUserResource|req:%+v rsp:%+v", req, rsp)
		return nil, fmt.Errorf(rsp.GetMessage())
	}
	resourceIDs := make([]string, 0)
	for _, perm := range rsp.GetPermissions() {
		for _, i := range perm.GetResources() {
			resourceIDs = append(resourceIDs, i.GetResourceIds()...)
		}
	}
	log.DebugContextf(ctx, "GetUserResource|resourceIDs:%+v", resourceIDs)
	return resourceIDs, nil
}

// GetSystemIntegratorByID 通过ID获取集成商信息
func GetSystemIntegratorByID(ctx context.Context, uin, subAccountUin string,
	id int) (*pb.DescribeIntegratorRsp, error) {
	log.DebugContext(ctx, "getSystemIntegratorByID|id:%d", id)
	if len(uin) == 0 || len(subAccountUin) == 0 {
		log.WarnContext(ctx, "getSystemIntegratorByID|uin&subAccountUin is empty")
		return &pb.DescribeIntegratorRsp{}, nil
	}
	req := &pb.DescribeIntegratorReq{
		Uin:           uin,
		SubAccountUin: subAccountUin,
	}
	resp, err := proxy.GetAdminAPIProxy().DescribeIntegrator(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "getSystemIntegratorByID|req:%+v,err:%+v", req, err)
		return nil, err
	}
	log.InfoContextf(ctx, "getSystemIntegratorByID|req:%+v|resp:%+v", req, resp)
	return resp, nil
}
