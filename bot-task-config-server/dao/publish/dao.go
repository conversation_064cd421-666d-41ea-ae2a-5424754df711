// Package publish ...
// @Author: halelv
// @Date: 2023/12/21 17:12
package publish

import (
	"context"

	vectorDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"gorm.io/gorm"
)

// Dao 发布相关 Dao
type Dao interface {
	// GetUnPublishTaskFlow 查询 t_task_flow 待发布的数据
	GetUnPublishTaskFlow(ctx context.Context, robotID string, taskFlowIDs []string, taskFlowType string) (
		[]*entity.TaskFlow, error)

	// GetUnPublishIntent 查询 t_intent 待发布的数据
	GetUnPublishIntent(ctx context.Context, intentIDs []string) ([]*entity.Intent, error)

	// GetUnPublishIntentFlow 查询 t_intent_flow 待发布的数据
	GetUnPublishIntentFlow(ctx context.Context, taskFlowIDs, intentIDs []string) ([]*entity.IntentFlow, error)

	// GetUnPublishRobotIntent 查询 t_robot_intent 待发布数据
	GetUnPublishRobotIntent(ctx context.Context, robotID string, intentIDs []string) ([]*entity.RobotIntent, error)

	// GetUnPublishIntentExamples 查询 t_intent_corpus 待发布的意图示例问法
	GetUnPublishIntentExamples(ctx context.Context, robotID string, intentIDs []string) ([]*entity.IntentCorpus, error)

	// GetUnPublishCorpus 查询 t_corpus 待发布的数据
	GetUnPublishCorpus(ctx context.Context, intentIDs []string) ([]*entity.Corpus, error)

	// GetUnPublishIntentEntries 查询 t_intent_entry 待发布数据
	GetUnPublishIntentEntries(ctx context.Context, intentIDs []string) ([]*entity.IntentEntry, error)

	// GetUnPublishVarParams 查询 t_var 待发布数据
	GetUnPublishVarParams(ctx context.Context, varParamIDs []string) ([]*entity.VarParams, error)

	// GetUnPublishIntentVarParams 查询 t_intent_var 待发布数据
	GetUnPublishIntentVarParams(ctx context.Context, intentIDs []string) ([]*entity.IntentVarParams, error)

	// GetUnPublishIntentSlot 查询 t_intent_slot 待发布数据
	GetUnPublishIntentSlot(ctx context.Context, intentIDs []string) ([]*entity.IntentSlot, error)

	// GetUnPublishSlot 查询 t_slot 待发布数据
	GetUnPublishSlot(ctx context.Context, slotIDs []string) ([]*entity.Slot, error)

	// GetUnPublishSlotEntity 查询 t_slot_entity 待发布数据
	GetUnPublishSlotEntity(ctx context.Context, slotIDs []string) ([]*entity.SlotEntity, error)

	// GetUnPublishEntity 查询 t_entity 待发布数据
	GetUnPublishEntity(ctx context.Context, entityIDs []string) ([]*entity.Entity, error)

	// GetUnPublishEntry 查询 t_entry 待发布数据
	GetUnPublishEntry(ctx context.Context, entityIDs []string) ([]*entity.Entry, error)

	// GetEntryInfosByEntityIDs 获取某个实体下的所以词条信息
	GetEntryInfosByEntityIDs(ctx context.Context, entityIDs []string) ([]*entity.Entry, error)

	// UpdateTaskFlowReleaseStatus 更新 t_task_flow 发布状态
	UpdateTaskFlowReleaseStatus(ctx context.Context, taskFlows []*entity.TaskFlow, releaseStatus string) error

	// UpdateIntentReleaseStatus 更新 t_intent 发布状态
	UpdateIntentReleaseStatus(ctx context.Context, intents []*entity.Intent, releaseStatus string) error

	// UpdateIntentFlowReleaseStatus 更新 t_intent_flow 发布状态
	UpdateIntentFlowReleaseStatus(ctx context.Context, intentFlows []*entity.IntentFlow, releaseStatus string) error

	// UpdateRobotIntentReleaseStatus 更新 t_robot_intent 发布状态
	UpdateRobotIntentReleaseStatus(ctx context.Context, robotIntents []*entity.RobotIntent, releaseStatus string) error

	// UpdateCorpusReleaseStatus 更新 t_corpus 发布状态
	UpdateCorpusReleaseStatus(ctx context.Context, corpora []*entity.Corpus, releaseStatus string) error

	// UpdateIntentVarReleaseStatus 更新 t_intent_var 的发布状态
	UpdateIntentVarReleaseStatus(ctx context.Context, intentVarParams []*entity.IntentVarParams,
		releaseStatus string) error

	// UpdateVarReleaseStatus 更新 t_var 的发布状态
	UpdateVarReleaseStatus(ctx context.Context, varParams []*entity.VarParams, releaseStatus string) error

	// UpdateIntentEntriesReleaseStatus 更新 t_intent_entries 发布状态
	UpdateIntentEntriesReleaseStatus(ctx context.Context, intentEntries []*entity.IntentEntry, releaseStatus string) error

	// UpdateIntentSlotReleaseStatus 更新 t_intent_slot 发布状态
	UpdateIntentSlotReleaseStatus(ctx context.Context, intentSlots []*entity.IntentSlot, releaseStatus string) error

	// UpdateSlotReleaseStatus 更新 t_slot 发布状态
	UpdateSlotReleaseStatus(ctx context.Context, slots []*entity.Slot, releaseStatus string) error

	// UpdateSlotEntityReleaseStatus 更新 t_slot_entity 发布状态
	UpdateSlotEntityReleaseStatus(ctx context.Context, slotEntities []*entity.SlotEntity, releaseStatus string) error

	// UpdateEntityReleaseStatus 更新 t_entity 发布状态
	UpdateEntityReleaseStatus(ctx context.Context, entities []*entity.Entity, releaseStatus string) error

	// UpdateEntryReleaseStatus 更新 t_entry 发布状态
	UpdateEntryReleaseStatus(ctx context.Context, entries []*entity.Entry, releaseStatus string) error
	// UpdateIntentExamplesReleaseStatus t_intent_corpus 表的发布状态
	UpdateIntentExamplesReleaseStatus(ctx context.Context, intentExamples []*entity.IntentCorpus,
		releaseStatus string) error

	// PublishTaskFlow 事物发布 t_task_flow 数据
	PublishTaskFlow(ctx context.Context, tx *gorm.DB, taskFlows []*entity.TaskFlow) error

	// PublishIntent 事物发布 t_intent 数据
	PublishIntent(ctx context.Context, tx *gorm.DB, intents []*entity.Intent) error

	// PublishIntentFlow 事物发布 t_intent_flow 数据
	PublishIntentFlow(ctx context.Context, tx *gorm.DB, intentFlows []*entity.IntentFlow) error

	// PublishRobotIntent 事物发布 t_robot_intent 数据
	PublishRobotIntent(ctx context.Context, tx *gorm.DB, robotIntents []*entity.RobotIntent) error

	// PublishCorpus 事物发布 t_corpus 数据
	PublishCorpus(ctx context.Context, tx *gorm.DB, robotID string, corpora []*entity.Corpus) error

	// PublishIntentEntry 事物发布 t_intent_entry 数据
	PublishIntentEntry(ctx context.Context, tx *gorm.DB, intentEntries []*entity.IntentEntry) error

	// PublishVar 事务发布 t_var
	PublishVar(ctx context.Context, tx *gorm.DB, varParams []*entity.VarParams) error

	// PublishIntentVar 事务发布 t_intent_var
	PublishIntentVar(ctx context.Context, tx *gorm.DB, intentVars []*entity.IntentVarParams) error

	// PublishIntentSlot 事物发布 t_intent_slot 数据
	PublishIntentSlot(ctx context.Context, tx *gorm.DB, intentSlots []*entity.IntentSlot) error

	// PublishSlot 事物发布 t_slot 数据
	PublishSlot(ctx context.Context, tx *gorm.DB, slots []*entity.Slot) error

	// PublishSlotEntity 事物发布 t_slot_entity 数据
	PublishSlotEntity(ctx context.Context, tx *gorm.DB, slotEntities []*entity.SlotEntity) error

	// PublishEntity 事物发布 t_entity 数据
	PublishEntity(ctx context.Context, tx *gorm.DB, entities []*entity.Entity) error

	// PublishEntry 事物发布 t_entry 数据
	PublishEntry(ctx context.Context, tx *gorm.DB, entries []*entity.Entry) error

	// PublishEntityEntryToRedis 同步实体词条信息到redis
	PublishEntityEntryToRedis(ctx context.Context, robotID string, entityEntryMap map[string]string) error

	PublishIntentExample(ctx context.Context, tx *gorm.DB, examples []*entity.IntentCorpus) error

	// PublishNoticeDM 发布通知DM
	PublishNoticeDM(ctx context.Context, robotID string, slots []*entity.Slot, intents []*entity.Intent,
		corpusProdGroupID, entryProdGroupID string) error

	GetTaskFlowReleaseStatus(ctx context.Context, robotID string, taskFlowType string,
		envType KEP.GetTaskFlowReleaseStatusReq_EnvType) (isPublish bool, err error)
}

// dao ...
type dao struct {
	db        *gorm.DB
	prodDb    *gorm.DB
	vectorDao vectorDao.Dao
}

// NewDao new dao
func NewDao() Dao {
	return &dao{
		db:        database.GetLLMRobotTaskGORM().Debug(),
		prodDb:    database.GetLLMRobotTaskProdGORM().Debug(),
		vectorDao: vectorDao.NewDao(),
	}
}
