// Package publish ...
// @Author: halelv
// @Date: 2023/12/21 17:31
package publish

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// GetUnPublishTaskFlow ...
//
//	@Description: 查询 t_task_flow 待发布的数据
//	@receiver d
//	@param ctx
//	@param robotID      任务流关联的机器人ID
//	@param taskFlowIDs  任务流ID列表
//	@param taskFlowType 任务流类型：客服 or 助手
//	@return []*entity.TaskFlow
//	@return error
func (d dao) GetUnPublishTaskFlow(ctx context.Context, robotID string, taskFlowIDs []string, taskFlowType string) (
	[]*entity.TaskFlow, error) {
	log.InfoContextf(ctx, "GetUnPublishTaskFlow, robotID:%s, taskFlowIDs:%v", robotID, taskFlowIDs)
	taskFlows := make([]*entity.TaskFlow, 0)
	if len(taskFlowIDs) == 0 {
		return taskFlows, nil
	}
	// 联表查询
	join := fmt.Sprintf("INNER JOIN %s ON %s.%s = %s.%s",
		entity.RobotIntent{}.TableName(),
		entity.RobotIntent{}.TableName(), entity.TRobotIntentColumns.IntentID,
		entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.IntentID)
	query := fmt.Sprintf("%s.%s = ? AND %s.%s IN ? AND %s.%s = ? AND %s.%s != ?",
		entity.RobotIntent{}.TableName(), entity.TRobotIntentColumns.RobotID,
		entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.FlowID,
		entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.FlowType,
		entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).
		Joins(join).
		Where(query, robotID, taskFlowIDs, taskFlowType, entity.ReleaseStatusPublished).
		Find(&taskFlows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishTaskFlow db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishTaskFlow success, len(taskFlows):%d", len(taskFlows))
	return taskFlows, nil
}

// GetUnPublishIntent ...
//
//	@Description: 查询 t_intent 待发布的数据
//	@receiver d
//	@param ctx
//	@param intentIDs 意图ID列表
//	@return []*entity.Intent
//	@return error
func (d dao) GetUnPublishIntent(ctx context.Context, intentIDs []string) ([]*entity.Intent, error) {
	log.InfoContextf(ctx, "GetUnPublishIntent, intentIDs:%v", intentIDs)
	intents := make([]*entity.Intent, 0)
	if len(intentIDs) == 0 {
		return intents, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TIntentColumns.IntentID, entity.TIntentColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.Intent{}.TableName()).
		Where(query, intentIDs, entity.ReleaseStatusPublished).
		Find(&intents).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishIntent db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishIntent success, len(intents):%d", len(intents))
	return intents, nil
}

// GetUnPublishIntentFlow ...
//
//	@Description: 查询 t_intent_flow 待发布的数据
//	@receiver d
//	@param ctx
//	@param taskFlowIDs 任务流ID列表
//	@param intentIDs   意图ID列表
//	@return []*entity.IntentFlow
//	@return error
func (d dao) GetUnPublishIntentFlow(ctx context.Context, taskFlowIDs, intentIDs []string) (
	[]*entity.IntentFlow, error) {
	log.InfoContextf(ctx, "GetUnPublishIntentFlow, taskFlowIDs:%v, intentIDs:%v", taskFlowIDs, intentIDs)
	intentFlows := make([]*entity.IntentFlow, 0)
	if len(taskFlowIDs) == 0 || len(intentIDs) == 0 {
		return intentFlows, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s IN ? AND %s != ?",
		entity.TIntentFlowColumns.FlowID, entity.TIntentFlowColumns.IntentID, entity.TIntentFlowColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.IntentFlow{}.TableName()).
		Where(query, taskFlowIDs, intentIDs, entity.ReleaseStatusPublished).
		Find(&intentFlows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishIntentFlow db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishIntentFlow success, len(intentFlows):%d", len(intentFlows))
	return intentFlows, nil
}

// GetUnPublishRobotIntent ...
//
//	@Description: 查询 t_robot_intent 待发布数据
//	@receiver d
//	@param ctx
//	@param robotID   机器人ID
//	@param intentIDs 意图ID列表
//	@return []*entity.RobotIntent
//	@return error
func (d dao) GetUnPublishRobotIntent(ctx context.Context, robotID string, intentIDs []string) (
	[]*entity.RobotIntent, error) {
	log.InfoContextf(ctx, "GetUnPublishRobotIntent, robotID:%v, intentIDs:%v", robotID, intentIDs)
	robotIntents := make([]*entity.RobotIntent, 0)
	if len(robotID) == 0 || len(intentIDs) == 0 {
		return robotIntents, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s = ? AND %s IN ? AND %s != ?", entity.TRobotIntentColumns.RobotID,
		entity.TRobotIntentColumns.IntentID, entity.TRobotIntentColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.RobotIntent{}.TableName()).
		Where(query, robotID, intentIDs, entity.ReleaseStatusPublished).
		Find(&robotIntents).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishRobotIntent db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishRobotIntent success, len(robotIntents):%d", len(robotIntents))
	return robotIntents, nil
}

// GetUnPublishCorpus ...
//
//	@Description: 查询 t_corpus 待发布的数据
//	@receiver d
//	@param ctx
//	@param intentIDs 意图ID列表，语料一定是挂在意图下的
//	@return []*entity.Corpus
//	@return error
func (d dao) GetUnPublishCorpus(ctx context.Context, intentIDs []string) ([]*entity.Corpus, error) {
	log.InfoContextf(ctx, "GetUnPublishCorpus, intentIDs:%v", intentIDs)
	corpora := make([]*entity.Corpus, 0)
	if len(intentIDs) == 0 {
		return corpora, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TCorpusColumns.IntentID, entity.TCorpusColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.Corpus{}.TableName()).
		Where(query, intentIDs, entity.ReleaseStatusPublished).
		Find(&corpora).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishCorpus db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishCorpus success, len(corpora):%d", len(corpora))
	return corpora, nil
}

// GetUnPublishIntentEntries 查询 t_intent_entry 待发布数据
func (d dao) GetUnPublishIntentEntries(ctx context.Context, intentIDs []string) ([]*entity.IntentEntry, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|GetUnPublishIntentSlot|intentIDs:%v", sid, intentIDs)
	intentEntries := make([]*entity.IntentEntry, 0)
	if len(intentIDs) == 0 {
		return intentEntries, nil
	}
	err := d.db.WithContext(ctx).Table(entity.IntentEntry{}.TableName()).
		Where("f_intent_id IN ? AND f_release_status != ?", intentIDs, entity.ReleaseStatusPublished).
		Find(&intentEntries).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetUnPublishIntentEntries db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetUnPublishIntentEntries success, len(intentEntries):%d", sid, len(intentEntries))
	return intentEntries, nil
}

// GetUnPublishVarParams 查询 t_var 待发布数据
func (d dao) GetUnPublishVarParams(ctx context.Context, varParamIDs []string) ([]*entity.VarParams, error) {
	sid := util.RequestID(ctx)
	varParams := make([]*entity.VarParams, 0)
	if len(varParamIDs) == 0 {
		return varParams, nil
	}
	// 查询DB
	err := d.db.WithContext(ctx).Table(entity.VarParams{}.TableName()).
		Where("f_var_id IN ? AND f_release_status != ?", varParamIDs, entity.ReleaseStatusPublished).
		Find(&varParams).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetUnPublishVarParams db.Find err:%v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetUnPublishVarParams success,varParamIDs:%+v| len(varParams):%d",
		sid, varParamIDs, len(varParams))
	return varParams, nil
}

// GetUnPublishIntentVarParams 查询 t_intent_var 待发布数据
func (d dao) GetUnPublishIntentVarParams(ctx context.Context, intentIDs []string) ([]*entity.IntentVarParams, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|GetUnPublishIntentVarParams|intentIDs:%v", sid, intentIDs)
	intentVarParams := make([]*entity.IntentVarParams, 0)
	if len(intentIDs) == 0 {
		return intentVarParams, nil
	}
	// 查询DB
	err := d.db.WithContext(ctx).Table(entity.IntentVarParams{}.TableName()).
		Where("f_intent_id IN ? AND f_release_status != ?", intentIDs, entity.ReleaseStatusPublished).
		Find(&intentVarParams).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetUnPublishIntentVarParams db.Find err:%v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetUnPublishIntentVarParams success|len(intentVarParams):%d",
		sid, len(intentVarParams))
	return intentVarParams, nil
}

// GetUnPublishIntentSlot ...
//
//	@Description: 查询 t_intent_slot 待发布数据
//	@receiver d
//	@param ctx
//	@param intentIDs 意图ID列表，槽位一定是挂在意图下的
//	@return []*entity.IntentSlot
//	@return error
func (d dao) GetUnPublishIntentSlot(ctx context.Context, intentIDs []string) ([]*entity.IntentSlot, error) {
	log.InfoContextf(ctx, "GetUnPublishIntentSlot, intentIDs:%v", intentIDs)
	intentSlots := make([]*entity.IntentSlot, 0)
	if len(intentIDs) == 0 {
		return intentSlots, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TIntentSlotColumns.IntentID, entity.TIntentSlotColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.IntentSlot{}.TableName()).
		Where(query, intentIDs, entity.ReleaseStatusPublished).
		Find(&intentSlots).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishIntentSlot db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishIntentSlot success, len(intentSlots):%d", len(intentSlots))
	return intentSlots, nil
}

// GetUnPublishSlot ...
//
//	@Description: 查询 t_slot 待发布数据
//	@receiver d
//	@param ctx
//	@param slotIDs 槽位ID列表
//	@return []*entity.Slot
//	@return error
func (d dao) GetUnPublishSlot(ctx context.Context, slotIDs []string) ([]*entity.Slot, error) {
	log.InfoContextf(ctx, "GetUnPublishSlot, slotIDs:%v", slotIDs)
	slots := make([]*entity.Slot, 0)
	if len(slotIDs) == 0 {
		return slots, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?", entity.TSlotColumns.SlotID, entity.TSlotColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.Slot{}.TableName()).
		Where(query, slotIDs, entity.ReleaseStatusPublished).
		Find(&slots).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishSlot db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishSlot success, len(slots):%d", len(slots))
	return slots, nil
}

// GetUnPublishSlotEntity ...
//
//	@Description: 查询 t_slot_entity 待发布数据
//	@receiver d
//	@param ctx
//	@param slotIDs 槽位ID列表
//	@return []*entity.SlotEntity
//	@return error
func (d dao) GetUnPublishSlotEntity(ctx context.Context, slotIDs []string) ([]*entity.SlotEntity, error) {
	log.InfoContextf(ctx, "GetUnPublishSlotEntity, slotIDs:%v", slotIDs)
	slotEntities := make([]*entity.SlotEntity, 0)
	if len(slotIDs) == 0 {
		return slotEntities, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TSlotEntityColumns.SlotID, entity.TSlotEntityColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.SlotEntity{}.TableName()).
		Where(query, slotIDs, entity.ReleaseStatusPublished).
		Find(&slotEntities).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishSlotEntity db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishSlotEntity success, len(slotEntities):%d", len(slotEntities))
	return slotEntities, nil
}

// GetUnPublishEntity ...
//
//	@Description: 查询 t_entity 待发布数据
//	@receiver d
//	@param ctx
//	@param entityIDs 实体ID列表
//	@return []*entity.Entity
//	@return error
func (d dao) GetUnPublishEntity(ctx context.Context, entityIDs []string) ([]*entity.Entity, error) {
	log.InfoContextf(ctx, "GetUnPublishEntity, entityIDs:%v", entityIDs)
	entities := make([]*entity.Entity, 0)
	if len(entityIDs) == 0 {
		return entities, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TEntityColumns.EntityID, entity.TEntityColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.Entity{}.TableName()).
		Where(query, entityIDs, entity.ReleaseStatusPublished).
		Find(&entities).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishEntity db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishEntity success, len(entities):%d", len(entities))
	return entities, nil
}

// GetUnPublishEntry ...
//
//	@Description: 查询 t_entry 待发布数据
//	@receiver d
//	@param ctx
//	@param entityIDs 实体ID列表
//	@return []*entity.Entry
//	@return error
func (d dao) GetUnPublishEntry(ctx context.Context, entityIDs []string) ([]*entity.Entry, error) {
	log.InfoContextf(ctx, "GetUnPublishEntry, entityIDs:%v", entityIDs)
	entries := make([]*entity.Entry, 0)
	if len(entityIDs) == 0 {
		return entries, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.TEntryColumns.EntityID, entity.TEntryColumns.ReleaseStatus)
	err := d.db.WithContext(ctx).Table(entity.Entry{}.TableName()).
		Where(query, entityIDs, entity.ReleaseStatusPublished).
		Find(&entries).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishEntry db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishEntry success, le(entries):%d", len(entries))
	return entries, nil
}

// GetEntryInfosByEntityIDs 获取某个实体下的所以词条信息
func (d dao) GetEntryInfosByEntityIDs(ctx context.Context, entityIDs []string) ([]*entity.Entry, error) {
	log.InfoContextf(ctx, "GetEntryInfosByEntityIDs, entityIDs:%v", entityIDs)
	entries := make([]*entity.Entry, 0)

	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Model(&entity.Entry{}).
		Where("f_is_deleted = 0 AND f_entity_id IN ?").Find(&entries).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryInfosByEntityIDs db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetEntryInfosByEntityIDs success, le(entries):%d", len(entries))
	return entries, nil
}

// GetUnPublishIntentExamples 查询 t_intent_corpus 待发布的意图示例问法
func (d dao) GetUnPublishIntentExamples(ctx context.Context, robotId string,
	intentIDs []string) ([]*entity.IntentCorpus, error) {
	sid := util.RequestID(ctx)
	intentExamples := make([]*entity.IntentCorpus, 0)
	if len(intentIDs) == 0 {
		return intentExamples, nil
	}
	// 查询未发布的意图示例问法
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_release_status != ?", entity.ReleaseStatusPublished).
		Where("f_robot_id=? AND f_intent_id IN ?", robotId, intentIDs).
		Find(&intentExamples).Error
	log.InfoContextf(ctx, "sid:%s|GetUnPublishIntentExamples|robotId:%s|intentIDs:%+v|len(intentExamples):%d|err:%+v",
		sid, robotId, intentIDs, len(intentExamples), err)
	if err != nil {
		log.Errorf("GetIntentCorpusByIntentIDsWithTx failed,intentIDs:%s|%+v|err:%+v", sid, intentIDs, err)
		return nil, err
	}

	return intentExamples, nil
}

// UpdateTaskFlowReleaseStatus ...
//
//	@Description:  更新 t_task_flow 发布状态
//	@receiver d
//	@param ctx
//	@param taskFlows     待更新任务流列表
//	@param releaseStatus 待更新任务流发布状态
//	@return error
func (d dao) UpdateTaskFlowReleaseStatus(ctx context.Context, taskFlows []*entity.TaskFlow,
	releaseStatus string) error {
	sid := util.RequestID(ctx)
	if len(taskFlows) == 0 {
		return nil
	}
	taskFlowIDs := make([]string, 0)
	for _, taskFlow := range taskFlows {
		taskFlowIDs = append(taskFlowIDs, taskFlow.FlowID)
	}
	log.InfoContextf(ctx, "sid:%s|flowIds:%+v|UpdateTaskFlowReleaseStatus, len(taskFlows):%d, releaseStatus:%s",
		sid, taskFlowIDs, len(taskFlows), releaseStatus)
	err := d.db.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TTaskFlowColumns.FlowID), taskFlowIDs).
		Updates(map[string]interface{}{
			entity.TTaskFlowColumns.ReleaseStatus: releaseStatus,
			// entity.TTaskFlowColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateTaskFlowReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateTaskFlowReleaseStatus success")
	return nil
}

// UpdateIntentReleaseStatus ...
//
//	@Description: 更新 t_intent 发布状态
//	@receiver d
//	@param ctx
//	@param intents       待更新意图列表
//	@param releaseStatus 待更新意图发布状态
//	@return error
func (d dao) UpdateIntentReleaseStatus(ctx context.Context, intents []*entity.Intent, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateIntentReleaseStatus, len(intents):%d, releaseStatus:%s",
		len(intents), releaseStatus)
	if len(intents) == 0 {
		return nil
	}
	intentIDs := make([]string, 0)
	for _, intent := range intents {
		intentIDs = append(intentIDs, intent.IntentID)
	}
	err := d.db.WithContext(ctx).Table(entity.Intent{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TIntentColumns.IntentID), intentIDs).
		Updates(map[string]interface{}{
			entity.TIntentColumns.ReleaseStatus: releaseStatus,
			// entity.TIntentColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateIntentReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateIntentReleaseStatus success")
	return nil
}

// UpdateIntentFlowReleaseStatus ...
//
//	@Description:  更新 t_intent_flow 发布状态
//	@receiver d
//	@param ctx
//	@param intentFlows   待更新意图-任务列表
//	@param releaseStatus 待更新意图-任务发布状态
//	@return error
func (d dao) UpdateIntentFlowReleaseStatus(ctx context.Context, intentFlows []*entity.IntentFlow,
	releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateIntentFlowReleaseStatus, len(intentFlows):%d, releaseStatus:%s",
		len(intentFlows), releaseStatus)
	if len(intentFlows) == 0 {
		return nil
	}
	intentFlowIDs := make([]uint64, 0)
	for _, intentFlow := range intentFlows {
		intentFlowIDs = append(intentFlowIDs, intentFlow.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.IntentFlow{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TIntentFlowColumns.ID), intentFlowIDs).
		Updates(map[string]interface{}{
			entity.TIntentFlowColumns.ReleaseStatus: releaseStatus,
			// entity.TIntentFlowColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateIntentFlowReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateIntentFlowReleaseStatus success")
	return nil
}

// UpdateRobotIntentReleaseStatus ...
//
//	@Description: 更新 t_robot_intent 发布状态
//	@receiver d
//	@param ctx
//	@param robotIntents  待更新意图-机器人列表
//	@param releaseStatus 待更新意图-机器人发布状态
//	@return error
func (d dao) UpdateRobotIntentReleaseStatus(ctx context.Context, robotIntents []*entity.RobotIntent,
	releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateRobotIntentReleaseStatus, len(robotIntents):%d, releaseStatus:%s",
		len(robotIntents), releaseStatus)
	if len(robotIntents) == 0 {
		return nil
	}
	robotIntentIDs := make([]uint64, 0)
	for _, robotIntent := range robotIntents {
		robotIntentIDs = append(robotIntentIDs, robotIntent.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.RobotIntent{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TRobotIntentColumns.ID), robotIntentIDs).
		Updates(map[string]interface{}{
			entity.TRobotIntentColumns.ReleaseStatus: releaseStatus,
			// entity.TRobotIntentColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateRobotIntentReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateRobotIntentReleaseStatus success")
	return nil
}

// UpdateCorpusReleaseStatus ...
//
//	@Description: 更新 t_corpus 发布状态
//	@receiver d
//	@param ctx
//	@param corpora       待更新语料列表
//	@param releaseStatus 待更新语料发布状态
//	@return error
func (d dao) UpdateCorpusReleaseStatus(ctx context.Context, corpora []*entity.Corpus, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateCorpusReleaseStatus len(corpora):%d, releaseStatus:%s",
		len(corpora), releaseStatus)
	if len(corpora) == 0 {
		return nil
	}
	corpusIDs := make([]string, 0)
	for _, corpus := range corpora {
		corpusIDs = append(corpusIDs, corpus.CorpusID)
	}
	err := d.db.WithContext(ctx).Table(entity.Corpus{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TCorpusColumns.CorpusID), corpusIDs).
		Updates(map[string]interface{}{
			entity.TCorpusColumns.ReleaseStatus: releaseStatus,
			// entity.TCorpusColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateCorpusReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateCorpusReleaseStatus success")
	return nil
}

// UpdateIntentEntriesReleaseStatus 更新 t_intent_entries 发布状态
func (d dao) UpdateIntentEntriesReleaseStatus(ctx context.Context, intentEntries []*entity.IntentEntry, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateIntentEntriesReleaseStatus, len(intentEntries):%d, releaseStatus:%s",
		len(intentEntries), releaseStatus)
	if len(intentEntries) == 0 {
		return nil
	}
	intentEntryIDs := make([]uint64, 0)
	for _, intentEntry := range intentEntries {
		intentEntryIDs = append(intentEntryIDs, intentEntry.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.IntentEntry{}.TableName()).
		Where("f_id IN ?", intentEntryIDs).
		Updates(map[string]interface{}{
			entity.TIntentSlotColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateIntentEntriesReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateIntentEntriesReleaseStatus success")
	return nil
}

// UpdateIntentVarReleaseStatus ...
//
//	@Description: 更新 t_intent_var 发布状态
//	@receiver d
//	@param ctx
//	@param intentVars   待更新意图-变量列表
//	@param releaseStatus 待更新意图-变量发布状态
//	@return error
func (d dao) UpdateIntentVarReleaseStatus(ctx context.Context, intentVarParams []*entity.IntentVarParams,
	releaseStatus string) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|UpdateIntentVarReleaseStatus|len(intentVarParams):%d|releaseStatus:%s",
		sid, len(intentVarParams), releaseStatus)
	if len(intentVarParams) == 0 {
		return nil
	}
	intentVarIDs := make([]uint64, 0)
	for _, intentVar := range intentVarParams {
		intentVarIDs = append(intentVarIDs, intentVar.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.IntentVarParams{}.TableName()).
		Where("f_id IN ?", intentVarIDs).
		Updates(map[string]interface{}{"f_release_status": releaseStatus}).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateIntentVarReleaseStatus|db.Updates err:%v", sid, err)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|UpdateIntentVarReleaseStatus success", sid)
	return nil
}

// UpdateVarReleaseStatus ...
//
//	@Description: 更新 t_var 发布状态
//	@receiver d
//	@param ctx
//	@param varParams         待更自定义变量列表
//	@param releaseStatus 待更新槽位发布状态
//	@return error
func (d dao) UpdateVarReleaseStatus(ctx context.Context, varParams []*entity.VarParams, releaseStatus string) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|UpdateVarReleaseStatus|len(varParams):%d|releaseStatus:%s",
		sid, len(varParams), releaseStatus)
	if len(varParams) == 0 {
		return nil
	}
	varIds := make([]uint64, 0)
	for _, varParam := range varParams {
		varIds = append(varIds, varParam.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.VarParams{}.TableName()).
		Where("f_id IN ?", varIds).
		Updates(map[string]interface{}{"f_release_status": releaseStatus}).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateVarReleaseStatus|db.Updates err:%v", sid, err)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|UpdateVarReleaseStatus success", sid)
	return nil
}

// UpdateIntentSlotReleaseStatus ...
//
//	@Description: 更新 t_intent_slot 发布状态
//	@receiver d
//	@param ctx
//	@param intentSlots   待更新意图-槽位列表
//	@param releaseStatus 待更新意图-槽位发布状态
//	@return error
func (d dao) UpdateIntentSlotReleaseStatus(ctx context.Context, intentSlots []*entity.IntentSlot,
	releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateIntentSlotReleaseStatus, len(intentSlots):%d, releaseStatus:%s",
		len(intentSlots), releaseStatus)
	if len(intentSlots) == 0 {
		return nil
	}
	intentSlotIDs := make([]uint64, 0)
	for _, intentSlot := range intentSlots {
		intentSlotIDs = append(intentSlotIDs, intentSlot.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.IntentSlot{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TIntentSlotColumns.ID), intentSlotIDs).
		Updates(map[string]interface{}{
			entity.TIntentSlotColumns.ReleaseStatus: releaseStatus,
			// entity.TIntentSlotColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateIntentSlotReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateIntentSlotReleaseStatus success")
	return nil
}

// UpdateSlotReleaseStatus ...
//
//	@Description: 更新 t_slot 发布状态
//	@receiver d
//	@param ctx
//	@param slots         待更新槽位列表
//	@param releaseStatus 待更新槽位发布状态
//	@return error
func (d dao) UpdateSlotReleaseStatus(ctx context.Context, slots []*entity.Slot, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateSlotReleaseStatus, len(slots):%d, releaseStatus:%s", len(slots), releaseStatus)
	if len(slots) == 0 {
		return nil
	}
	slotIDs := make([]uint64, 0)
	for _, slot := range slots {
		slotIDs = append(slotIDs, slot.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.Slot{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TSlotColumns.ID), slotIDs).
		Updates(map[string]interface{}{
			entity.TSlotColumns.ReleaseStatus: releaseStatus,
			// entity.TSlotColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlotReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateSlotReleaseStatus success")
	return nil
}

// UpdateSlotEntityReleaseStatus ...
//
//	@Description: 更新 t_slot_entity 发布状态
//	@receiver d
//	@param ctx
//	@param slotEntities  待更新槽位-实体列表
//	@param releaseStatus 待更新槽位-实体发布状态
//	@return error
func (d dao) UpdateSlotEntityReleaseStatus(ctx context.Context, slotEntities []*entity.SlotEntity, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateSlotEntityReleaseStatus, len(slotEntities):%d, releaseStatus:%s",
		len(slotEntities), releaseStatus)
	if len(slotEntities) == 0 {
		return nil
	}
	slotEntityIDs := make([]uint64, 0)
	for _, slotEntity := range slotEntities {
		slotEntityIDs = append(slotEntityIDs, slotEntity.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.SlotEntity{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TSlotEntityColumns.ID), slotEntityIDs).
		Updates(map[string]interface{}{
			entity.TSlotEntityColumns.ReleaseStatus: releaseStatus,
			// entity.TSlotEntityColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlotEntityReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateSlotEntityReleaseStatus success")
	return nil
}

// UpdateEntityReleaseStatus ...
//
//	@Description: 更新 t_entity 发布状态
//	@receiver d
//	@param ctx
//	@param entities      待更新实体列表
//	@param releaseStatus 待更新实体发布状态
//	@return error
func (d dao) UpdateEntityReleaseStatus(ctx context.Context, entities []*entity.Entity, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateEntityReleaseStatus, len(entities):%d, releaseStatus:%s",
		len(entities), releaseStatus)
	if len(entities) == 0 {
		return nil
	}
	entityIDs := make([]uint64, 0)
	for _, et := range entities {
		entityIDs = append(entityIDs, et.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.Entity{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TEntityColumns.ID), entityIDs).
		Updates(map[string]interface{}{
			entity.TEntityColumns.ReleaseStatus: releaseStatus,
			// entity.TEntityColumns.UpdateTime:     time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntityReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateEntityReleaseStatus success")
	return nil
}

// UpdateEntryReleaseStatus ...
//
//	@Description: 更新 t_entry 发布状态
//	@receiver d
//	@param ctx
//	@param entries       待更新词条列表
//	@param releaseStatus 待更新词条发布状态
//	@return error
func (d dao) UpdateEntryReleaseStatus(ctx context.Context, entries []*entity.Entry, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateEntryReleaseStatus, len(entries):%d, releaseStatus:%s",
		len(entries), releaseStatus)
	if len(entries) == 0 {
		return nil
	}
	entryIDs := make([]uint64, 0)
	for _, entry := range entries {
		entryIDs = append(entryIDs, entry.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.Entry{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.TEntryColumns.ID), entryIDs).
		Updates(map[string]interface{}{
			entity.TEntryColumns.ReleaseStatus: releaseStatus,
			// entity.TEntryColumns.UpdateTime:    time.Now(),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntryReleaseStatus db.Updates err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateEntryReleaseStatus success")
	return nil
}

// UpdateIntentExamplesReleaseStatus ...
//
//	@Description: 更新 t_intent_corpus 发布状态
//	@receiver d
//	@param ctx
//	@param intentExamples       待更示例问法列表
//	@param releaseStatus 待更新示例问法发布状态
//	@return error
func (d dao) UpdateIntentExamplesReleaseStatus(ctx context.Context, intentExamples []*entity.IntentCorpus, releaseStatus string) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|UpdateIntentExamplesReleaseStatus|len(intentExamples):%d|releaseStatus:%s",
		sid, len(intentExamples), releaseStatus)
	if len(intentExamples) == 0 {
		return nil
	}
	intentExampleId := make([]uint64, 0)
	for _, example := range intentExamples {
		intentExampleId = append(intentExampleId, example.ID)
	}
	err := d.db.WithContext(ctx).Table(entity.IntentCorpus{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", "f_id"), intentExampleId).
		Updates(map[string]interface{}{
			entity.TEntryColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateIntentExamplesReleaseStatus db.Updates err:%v", sid, err)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|UpdateIntentExamplesReleaseStatus success", sid)
	return nil
}

// PublishTaskFlow ...
//
//	@Description: 事物发布 t_task_flow 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param taskFlows 待发布任务流
//	@return error
func (d dao) PublishTaskFlow(ctx context.Context, tx *gorm.DB, taskFlows []*entity.TaskFlow) error {
	log.InfoContextf(ctx, "PublishTaskFlow, len(taskFlows):%d", len(taskFlows))
	if len(taskFlows) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.TaskFlow, 0)
	updates := make([]*entity.TaskFlow, 0)
	deletes := make([]*entity.TaskFlow, 0)

	for _, taskFlow := range taskFlows {
		// 发布状态更新为已发布
		taskFlow.ReleaseStatus = entity.ReleaseStatusPublished
		// taskFlow.UpdateTime = time.Now()

		switch taskFlow.Action {
		case entity.ActionInsert:
			// taskFlow.CreateTime = taskFlow.UpdateTime
			// 使用数据库时间
			taskFlow.CreateTime = time.Time{}
			taskFlow.UpdateTime = time.Time{}
			inserts = append(inserts, taskFlow)
		case entity.ActionUpdate:
			updates = append(updates, taskFlow)
		case entity.ActionDelete:
			deletes = append(deletes, taskFlow)
		default:
			log.WarnContextf(ctx, "PublishTaskFlow TaskFlow:%+v, Action:%s illegal", *taskFlow, taskFlow.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishTaskFlow tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.TaskFlow, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TTaskFlowColumns.FlowID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TTaskFlowColumns.IntentName,
					entity.TTaskFlowColumns.IntentDesc,
					entity.TTaskFlowColumns.IntentID,
					entity.TTaskFlowColumns.FlowState,
					entity.TTaskFlowColumns.FlowType,
					entity.TTaskFlowColumns.Version,
					entity.TTaskFlowColumns.CategoryID,
					entity.TTaskFlowColumns.DialogJsonDraft,
					entity.TTaskFlowColumns.DialogJsonEnable,
					entity.TTaskFlowColumns.Uin,
					entity.TTaskFlowColumns.SubUin,
					entity.TTaskFlowColumns.ReleaseStatus,
					entity.TTaskFlowColumns.IsDeleted,
					entity.TTaskFlowColumns.Action,
					// entity.TTaskFlowColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishTaskFlow tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishTaskFlow success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishIntent ...
//
//	@Description: 事物发布 t_intent 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param intents 待发布意图
//	@return error
func (d dao) PublishIntent(ctx context.Context, tx *gorm.DB, intents []*entity.Intent) error {
	log.InfoContextf(ctx, "PublishIntent, len(intents):%d", len(intents))
	if len(intents) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Intent, 0)
	updates := make([]*entity.Intent, 0)
	deletes := make([]*entity.Intent, 0)

	for _, intent := range intents {
		// 发布状态更新为已发布
		intent.ReleaseStatus = entity.ReleaseStatusPublished
		// intent.UpdateTime = time.Now()

		switch intent.Action {
		case entity.ActionInsert:
			// intent.CreateTime = intent.UpdateTime
			// 使用数据库时间
			intent.CreateTime = time.Time{}
			intent.UpdateTime = time.Time{}
			inserts = append(inserts, intent)
		case entity.ActionUpdate:
			updates = append(updates, intent)
		case entity.ActionDelete:
			deletes = append(deletes, intent)
		default:
			log.WarnContextf(ctx, "PublishIntent Intent:%+v, Action:%s illegal", *intent, intent.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.Intent{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntent tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.Intent, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.Intent{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TIntentColumns.IntentID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TIntentColumns.IntentName,
					entity.TIntentColumns.IntentDesc,
					entity.TIntentColumns.IntentType,
					entity.TIntentColumns.Source,
					entity.TIntentColumns.Uin,
					entity.TIntentColumns.SubUin,
					entity.TIntentColumns.IsDeleted,
					entity.TIntentColumns.ReleaseStatus,
					entity.TIntentColumns.Action,
					// entity.TIntentColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntent tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishIntent success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishIntentFlow ...
//
//	@Description: 事物发布 t_intent_flow 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param intentFlows 待发布意图-任务
//	@return error
func (d dao) PublishIntentFlow(ctx context.Context, tx *gorm.DB, intentFlows []*entity.IntentFlow) error {
	log.InfoContextf(ctx, "PublishIntentFlow, len(intentFlows):%d", len(intentFlows))
	if len(intentFlows) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.IntentFlow, 0)
	updates := make([]*entity.IntentFlow, 0)
	deletes := make([]*entity.IntentFlow, 0)

	for _, intentFlow := range intentFlows {
		// 发布状态更新为已发布
		intentFlow.ReleaseStatus = entity.ReleaseStatusPublished
		// intentFlow.UpdateTime = time.Now()

		switch intentFlow.Action {
		case entity.ActionInsert:
			// intentFlow.CreateTime = intentFlow.UpdateTime
			// 使用数据库时间
			intentFlow.CreateTime = time.Time{}
			intentFlow.UpdateTime = time.Time{}
			inserts = append(inserts, intentFlow)
		case entity.ActionUpdate:
			updates = append(updates, intentFlow)
		case entity.ActionDelete:
			deletes = append(deletes, intentFlow)
		default:
			log.WarnContextf(ctx, "PublishIntentFlow IntentFlow:%+v, Action:%s illegal",
				*intentFlow, intentFlow.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentFlow{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntentFlow tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.IntentFlow, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentFlow{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TIntentFlowColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TIntentFlowColumns.FlowID,
					entity.TIntentFlowColumns.IntentID,
					entity.TIntentFlowColumns.ReleaseStatus,
					entity.TIntentFlowColumns.IsDeleted,
					entity.TIntentFlowColumns.Action,
					// entity.TIntentFlowColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntentFlow tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishIntentFlow success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishRobotIntent ...
//
//	@Description: 事物发布 t_robot_intent 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param robotIntents 待发布意图-机器人
//	@return error
func (d dao) PublishRobotIntent(ctx context.Context, tx *gorm.DB, robotIntents []*entity.RobotIntent) error {
	log.InfoContextf(ctx, "PublishRobotIntent, len(robotIntents):%d", len(robotIntents))
	if len(robotIntents) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.RobotIntent, 0)
	updates := make([]*entity.RobotIntent, 0)
	deletes := make([]*entity.RobotIntent, 0)

	for _, robotIntent := range robotIntents {
		// 发布状态更新为已发布
		robotIntent.ReleaseStatus = entity.ReleaseStatusPublished
		// robotIntent.UpdateTime = time.Now()

		switch robotIntent.Action {
		case entity.ActionInsert:
			// robotIntent.CreateTime = robotIntent.UpdateTime
			// 使用数据库时间
			robotIntent.CreateTime = time.Time{}
			robotIntent.UpdateTime = time.Time{}
			inserts = append(inserts, robotIntent)
		case entity.ActionUpdate:
			updates = append(updates, robotIntent)
		case entity.ActionDelete:
			deletes = append(deletes, robotIntent)
		default:
			log.WarnContextf(ctx, "PublishRobotIntent RobotIntent:%+v, Action:%s illegal",
				*robotIntent, robotIntent.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.RobotIntent{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishRobotIntent tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.RobotIntent, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.RobotIntent{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TRobotIntentColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TRobotIntentColumns.RobotID,
					entity.TRobotIntentColumns.IntentID,
					entity.TRobotIntentColumns.ReleaseStatus,
					entity.TRobotIntentColumns.IsDeleted,
					entity.TRobotIntentColumns.Action,
					// entity.TRobotIntentColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishRobotIntent tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishRobotIntent success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishCorpus ...
//
//	@Description: 事物发布 t_corpus 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param robotID 待发布机器人ID
//	@param corpora 待发布语料
//	@return error
func (d dao) PublishCorpus(ctx context.Context, tx *gorm.DB, robotID string, corpora []*entity.Corpus) error {
	log.InfoContextf(ctx, "PublishCorpus, robotID:%s, len(corpora):%d", robotID, len(corpora))
	if len(robotID) == 0 || len(corpora) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Corpus, 0)
	updates := make([]*entity.Corpus, 0)
	deletes := make([]*entity.Corpus, 0)

	for _, corpus := range corpora {
		// 发布状态更新为已发布
		corpus.ReleaseStatus = entity.ReleaseStatusPublished
		// corpus.UpdateTime = time.Now()

		switch corpus.Action {
		case entity.ActionInsert:
			// corpus.CreateTime = corpus.UpdateTime
			// 使用数据库时间
			corpus.CreateTime = time.Time{}
			corpus.UpdateTime = time.Time{}
			inserts = append(inserts, corpus)
		case entity.ActionUpdate:
			updates = append(updates, corpus)
		case entity.ActionDelete:
			deletes = append(deletes, corpus)
		default:
			log.WarnContextf(ctx, "PublishCorpus Corpus:%+v, Action:%s illegal", *corpus, corpus.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.Corpus{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishCorpus tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.Corpus, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.Corpus{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TCorpusColumns.CorpusID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TCorpusColumns.Content,
					entity.TCorpusColumns.IntentID,
					entity.TCorpusColumns.Uin,
					entity.TCorpusColumns.SubUin,
					entity.TCorpusColumns.IsDeleted,
					entity.TCorpusColumns.ReleaseStatus,
					entity.TCorpusColumns.Action,
					// entity.TCorpusColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishCorpus tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishCorpus success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishIntentEntry 发布 IntentEntry
func (d dao) PublishIntentEntry(ctx context.Context, tx *gorm.DB, intentEntries []*entity.IntentEntry) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|PublishIntentEntry|len(intentSlots):%d", sid, len(intentEntries))
	if len(intentEntries) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.IntentEntry, 0)
	updates := make([]*entity.IntentEntry, 0)
	deletes := make([]*entity.IntentEntry, 0)

	for _, intentEntry := range intentEntries {
		// 发布状态更新为已发布
		intentEntry.ReleaseStatus = entity.ReleaseStatusPublished

		switch intentEntry.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			intentEntry.CreateTime = time.Time{}
			intentEntry.UpdateTime = time.Time{}
			inserts = append(inserts, intentEntry)
		case entity.ActionUpdate:
			updates = append(updates, intentEntry)
		case entity.ActionDelete:
			deletes = append(deletes, intentEntry)
		default:
			log.WarnContextf(ctx, "sid:%s|PublishIntentEntry|intentEntry:%+v, Action:%s illegal",
				sid, *intentEntry, intentEntry.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentEntry{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishIntentEntry tx.Create err:%v", sid, err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.IntentEntry, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentEntry{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_intent_id", "f_entry_id", "f_release_status",
					"f_is_deleted", "f_action"}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishIntentEntry tx.Updates err:%v", sid, err)
			return err
		}
	}

	log.InfoContextf(ctx, "sid:%s|PublishIntentEntry success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		sid, len(inserts), len(updates), len(deletes))
	return nil
}

// PublishIntentVar ...
//
//	@Description: 事务发布 t_intent_var 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param intentVars 待发布意图-自定义变量
//	@return error
func (d dao) PublishIntentVar(ctx context.Context, tx *gorm.DB, intentVars []*entity.IntentVarParams) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|PublishIntentSlot, len(intentVars):%d", sid, len(intentVars))
	if len(intentVars) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.IntentVarParams, 0)
	updates := make([]*entity.IntentVarParams, 0)
	deletes := make([]*entity.IntentVarParams, 0)

	for _, intentVar := range intentVars {
		// 发布状态更新为已发布
		intentVar.ReleaseStatus = entity.ReleaseStatusPublished

		switch intentVar.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			intentVar.CreateTime = time.Time{}
			intentVar.UpdateTime = time.Time{}
			inserts = append(inserts, intentVar)
		case entity.ActionUpdate:
			updates = append(updates, intentVar)
		case entity.ActionDelete:
			deletes = append(deletes, intentVar)
		default:
			log.WarnContextf(ctx, "sid:%s|PublishIntentVar intentVars:%+v, Action:%s illegal",
				sid, *intentVar, intentVar.Action)
		}
	}

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentVarParams{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishIntentVar tx.Create err:%v", sid, err)
			return err
		}
	}
	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类
	// 变更，
	changes := make([]*entity.IntentVarParams, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentVarParams{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_intent_id", "f_var_id",
					"f_release_status", "f_is_deleted", "f_action",
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishIntentVar tx.Updates err:%v", sid, err)
			return err
		}
	}

	log.InfoContextf(ctx, "sid:%s|PublishIntentVar success|len(inserts):%d, len(updates):%d, len(deletes):%d",
		sid, len(inserts), len(updates), len(deletes))
	return nil
}

// PublishVar ...
//
//	@Description: 事务发布 t_var 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param varParams 待发布自定义变量
//	@return error
func (d dao) PublishVar(ctx context.Context, tx *gorm.DB, varParams []*entity.VarParams) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|PublishVar|len(varParams):%d", sid, len(varParams))
	if len(varParams) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.VarParams, 0)
	updates := make([]*entity.VarParams, 0)
	deletes := make([]*entity.VarParams, 0)

	for _, varParam := range varParams {
		// 发布状态更新为已发布
		varParam.ReleaseStatus = entity.ReleaseStatusPublished

		switch varParam.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			varParam.CreateTime = time.Time{}
			varParam.UpdateTime = time.Time{}
			inserts = append(inserts, varParam)
		case entity.ActionUpdate:
			updates = append(updates, varParam)
		case entity.ActionDelete:
			deletes = append(deletes, varParam)
		default:
			log.WarnContextf(ctx, "sid:%s|PublishVar varParam:%+v, Action:%s illegal",
				sid, *varParam, varParam.Action)
		}
	}

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.VarParams{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishVar tx.Create err:%v", sid, err)
			return err
		}
	}
	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类
	// 变更
	changes := make([]*entity.VarParams, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.VarParams{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_var_id", "f_var_name", "f_var_desc", "f_var_type", "f_var_default_value",
					"f_var_default_file_name", "f_app_id", "f_uin", "f_sub_uin",
					"f_release_status", "f_is_deleted", "f_action",
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PublishVar tx.Updates err:%v", sid, err)
			return err
		}
	}

	log.InfoContextf(ctx, "sid:%s|PublishVar success|len(inserts):%d, len(updates):%d, len(deletes):%d",
		sid, len(inserts), len(updates), len(deletes))
	return nil
}

// PublishIntentSlot ...
//
//	@Description: 事物发布 t_intent_slot 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param intentSlots 待发布意图-槽位
//	@return error
func (d dao) PublishIntentSlot(ctx context.Context, tx *gorm.DB, intentSlots []*entity.IntentSlot) error {
	log.InfoContextf(ctx, "PublishIntentSlot, len(intentSlots):%d", len(intentSlots))
	if len(intentSlots) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.IntentSlot, 0)
	updates := make([]*entity.IntentSlot, 0)
	deletes := make([]*entity.IntentSlot, 0)

	for _, intentSlot := range intentSlots {
		// 发布状态更新为已发布
		intentSlot.ReleaseStatus = entity.ReleaseStatusPublished
		// intentSlot.UpdateTime = time.Now()

		switch intentSlot.Action {
		case entity.ActionInsert:
			// intentSlot.CreateTime = intentSlot.UpdateTime
			// 使用数据库时间
			intentSlot.CreateTime = time.Time{}
			intentSlot.UpdateTime = time.Time{}
			inserts = append(inserts, intentSlot)
		case entity.ActionUpdate:
			updates = append(updates, intentSlot)
		case entity.ActionDelete:
			deletes = append(deletes, intentSlot)
		default:
			log.WarnContextf(ctx, "PublishIntentSlot IntentSlot:%+v, Action:%s illegal",
				*intentSlot, intentSlot.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentSlot{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntentSlot tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.IntentSlot, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentSlot{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TIntentSlotColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TIntentSlotColumns.IntentID,
					entity.TIntentSlotColumns.SlotID,
					entity.TIntentSlotColumns.ReleaseStatus,
					entity.TIntentSlotColumns.IsDeleted,
					entity.TIntentSlotColumns.Action,
					// entity.TIntentSlotColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntentSlot tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishIntentSlot success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishSlot ...
//
//	@Description: 事物发布 t_slot 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param slots 待发布槽位
//	@return error
func (d dao) PublishSlot(ctx context.Context, tx *gorm.DB, slots []*entity.Slot) error {
	log.InfoContextf(ctx, "PublishSlot, len(slots):%d", len(slots))
	if len(slots) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Slot, 0)
	updates := make([]*entity.Slot, 0)
	deletes := make([]*entity.Slot, 0)

	for _, slot := range slots {
		// 发布状态更新为已发布
		slot.ReleaseStatus = entity.ReleaseStatusPublished
		// slot.UpdateTime = time.Now()

		switch slot.Action {
		case entity.ActionInsert:
			// slot.CreateTime = slot.UpdateTime
			// 使用数据库时间
			slot.CreateTime = time.Time{}
			slot.UpdateTime = time.Time{}
			inserts = append(inserts, slot)
		case entity.ActionUpdate:
			updates = append(updates, slot)
		case entity.ActionDelete:
			deletes = append(deletes, slot)
		default:
			log.WarnContextf(ctx, "PublishSlot Slot:%+v, Action:%s illegal", *slot, slot.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.Slot{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishSlot tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.Slot, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.Slot{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TSlotColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TSlotColumns.SlotID,
					entity.TSlotColumns.SlotName,
					entity.TSlotColumns.SlotDesc,
					entity.TSlotColumns.Examples,
					entity.TSlotColumns.RobotID,
					entity.TSlotColumns.UIN,
					entity.TSlotColumns.SubUIN,
					entity.TSlotColumns.ReleaseStatus,
					entity.TSlotColumns.IsDeleted,
					entity.TSlotColumns.Action,
					// entity.TSlotColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishSlot tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishSlot success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishSlotEntity ...
//
//	@Description: 事物发布 t_slot_entity 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param slotEntities 待发布槽位-实体
//	@return error
func (d dao) PublishSlotEntity(ctx context.Context, tx *gorm.DB, slotEntities []*entity.SlotEntity) error {
	log.InfoContextf(ctx, "PublishSlotEntity, len(slotEntities):%d", len(slotEntities))
	if len(slotEntities) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.SlotEntity, 0)
	updates := make([]*entity.SlotEntity, 0)
	deletes := make([]*entity.SlotEntity, 0)

	for _, slotEntity := range slotEntities {
		// 发布状态更新为已发布
		slotEntity.ReleaseStatus = entity.ReleaseStatusPublished
		// slotEntity.UpdateTime = time.Now()

		switch slotEntity.Action {
		case entity.ActionInsert:
			// slotEntity.CreateTime = slotEntity.UpdateTime
			// 使用数据库时间
			slotEntity.CreateTime = time.Time{}
			slotEntity.UpdateTime = time.Time{}
			inserts = append(inserts, slotEntity)
		case entity.ActionUpdate:
			updates = append(updates, slotEntity)
		case entity.ActionDelete:
			deletes = append(deletes, slotEntity)
		default:
			log.WarnContextf(ctx, "PublishSlotEntity SlotEntity:%+v, Action:%s illegal",
				*slotEntity, slotEntity.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.SlotEntity{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishSlotEntity tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.SlotEntity, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.SlotEntity{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TSlotEntityColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TSlotEntityColumns.SlotID,
					entity.TSlotEntityColumns.EntityID,
					entity.TSlotEntityColumns.ReleaseStatus,
					entity.TSlotEntityColumns.IsDeleted,
					entity.TSlotEntityColumns.Action,
					// entity.TSlotEntityColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishSlotEntity tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishSlotEntity success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishEntity ...
//
//	@Description: 事物发布 t_entity 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param entities 待发布实体
//	@return error
func (d dao) PublishEntity(ctx context.Context, tx *gorm.DB, entities []*entity.Entity) error {
	log.InfoContextf(ctx, "PublishEntity, len(entities):%d", len(entities))
	if len(entities) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Entity, 0)
	updates := make([]*entity.Entity, 0)
	deletes := make([]*entity.Entity, 0)

	for _, et := range entities {
		// 发布状态更新为已发布
		et.ReleaseStatus = entity.ReleaseStatusPublished
		// et.UpdateTime = time.Now()

		switch et.Action {
		case entity.ActionInsert:
			// et.CreateTime = et.UpdateTime
			// 使用数据库时间
			et.CreateTime = time.Time{}
			et.UpdateTime = time.Time{}
			inserts = append(inserts, et)
		case entity.ActionUpdate:
			updates = append(updates, et)
		case entity.ActionDelete:
			deletes = append(deletes, et)
		default:
			log.WarnContextf(ctx, "PublishEntity Entity:%+v, Action:%s illegal", *et, et.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.Entity{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishEntity tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.Entity, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.Entity{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TEntityColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TEntityColumns.EntityID,
					entity.TEntityColumns.EntityName,
					entity.TEntityColumns.EntityDesc,
					entity.TEntityColumns.Examples,
					entity.TEntityColumns.LevelType,
					entity.TEntityColumns.RobotID,
					entity.TEntityColumns.UIN,
					entity.TEntityColumns.SubUIN,
					entity.TEntityColumns.ReleaseStatus,
					entity.TEntityColumns.IsDeleted,
					entity.TEntityColumns.Action,
					// entity.TEntityColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishEntity tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishEntity success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishEntry ...
//
//	@Description: 事物发布 t_entry 数据
//	@receiver d
//	@param ctx
//	@param tx
//	@param entries 待发布词条
//	@return error
func (d dao) PublishEntry(ctx context.Context, tx *gorm.DB, entries []*entity.Entry) error {
	log.InfoContextf(ctx, "PublishEntry, len(entries):%d", len(entries))
	if len(entries) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Entry, 0)
	updates := make([]*entity.Entry, 0)
	deletes := make([]*entity.Entry, 0)

	for _, entry := range entries {
		// 发布状态更新为已发布
		entry.ReleaseStatus = entity.ReleaseStatusPublished
		// entry.UpdateTime = time.Now()

		switch entry.Action {
		case entity.ActionInsert:
			// entry.CreateTime = entry.UpdateTime
			// 使用数据库时间
			entry.CreateTime = time.Time{}
			entry.UpdateTime = time.Time{}
			inserts = append(inserts, entry)
		case entity.ActionUpdate:
			updates = append(updates, entry)
		case entity.ActionDelete:
			deletes = append(deletes, entry)
		default:
			log.WarnContextf(ctx, "PublishEntry Entry:%+v, Action:%s illegal", *entry, entry.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.Entry{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishEntry tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.Entry, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.Entry{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.TEntryColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.TEntryColumns.EntryID,
					entity.TEntryColumns.EntityID,
					entity.TEntryColumns.EntryValue,
					entity.TEntryColumns.EntryAlias,
					entity.TEntryColumns.UIN,
					entity.TEntryColumns.SubUIN,
					entity.TEntryColumns.ReleaseStatus,
					entity.TEntryColumns.IsDeleted,
					entity.TEntryColumns.Action,
					// entity.TEntryColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishEntry tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishEntry success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishEntityEntryToRedis 同步实体词条信息到redis
func (d dao) PublishEntityEntryToRedis(ctx context.Context, robotID string, entityEntryMap map[string]string) error {

	// 词条同步到redis
	key := db.GetEntityEntriesKey(ctx, db.ProductEnv, robotID)
	values := make([]interface{}, 0)

	for entityID, entryInfo := range entityEntryMap {
		values = append(values, entityID, entryInfo)

	}
	if len(values) == 0 {
		return nil
	}

	err := database.GetRedis().HMSet(ctx, key, values...).Err()
	if err != nil {
		log.ErrorContextf(ctx, "PublishEntityEntryToRedis SyncToRedis Failed!,key:%s,vals:%+v,err:%+v", key, values, err)
		return err
	}
	return nil
}

// PublishIntentExample 发布示例问法
func (d dao) PublishIntentExample(ctx context.Context, tx *gorm.DB, examples []*entity.IntentCorpus) error {
	//	 示例问法发布同步
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|PublishIntentExample|len(examples):%d", sid, len(examples))
	if len(examples) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.IntentCorpus, 0)
	updates := make([]*entity.IntentCorpus, 0)
	deletes := make([]*entity.IntentCorpus, 0)

	for _, example := range examples {
		// 发布状态更新为已发布
		example.ReleaseStatus = entity.ReleaseStatusPublished
		// entry.UpdateTime = time.Now()

		switch example.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			example.CreateTime = time.Time{}
			example.UpdateTime = time.Time{}
			inserts = append(inserts, example)
		case entity.ActionUpdate:
			updates = append(updates, example)
		case entity.ActionDelete:
			deletes = append(deletes, example)
		default:
			log.WarnContextf(ctx, "PublishIntentExample example:%+v, Action:%s illegal", *example, example.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentCorpus{}.TableName()).Create(&inserts).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishEntry tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := make([]*entity.IntentCorpus, 0)
	changes = append(changes, updates...)
	changes = append(changes, deletes...)
	if len(changes) > 0 {
		err := tx.WithContext(ctx).Table(entity.IntentCorpus{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_id",
					"f_corpus_id",
					"f_intent_id",
					"f_corpus",
					"f_uin",
					"f_sub_uin",
					"f_release_status",
					"f_is_deleted",
					"f_action",
					// entity.TEntryColumns.UpdateTime,
				}),
			}).Create(&changes).Error
		if err != nil {
			log.ErrorContextf(ctx, "PublishIntentExample tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishIntentExample success, len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishNoticeDM ...
//
//	@Description: 通知DM任务流发布
//	@receiver d
//	@param ctx
//	@param robotID           发布的机器人ID
//	@param slots             发布的槽位
//	@param intents           发布的意图
//	@param corpusProdGroupID 语料Prod向量库GroupID
//	@param entryProdGroupID  词条Prod向量库GroupID
//	@return error
func (d dao) PublishNoticeDM(ctx context.Context, robotID string,
	slots []*entity.Slot, intents []*entity.Intent, corpusProdGroupID, entryProdGroupID string) error {
	log.InfoContextf(ctx, "PublishNoticeDM, robotID:%s, len(slots):%d, len(intents):%d, corpusProdGroupID:%s, "+
		"entryProdGroupID:%s", robotID, len(slots), len(intents), corpusProdGroupID, entryProdGroupID)
	if len(robotID) == 0 {
		return nil
	}

	var upsertSlotIDs, deleteSlotIDs []string
	for _, slot := range slots {
		switch slot.Action {
		case entity.ActionInsert, entity.ActionUpdate:
			upsertSlotIDs = append(upsertSlotIDs, slot.SlotID)
		case entity.ActionDelete:
			deleteSlotIDs = append(deleteSlotIDs, slot.SlotID)
		}
	}

	var upsertIntentIDs, deleteIntentIDs []string
	for _, intent := range intents {
		switch intent.Action {
		case entity.ActionInsert, entity.ActionUpdate:
			upsertIntentIDs = append(upsertIntentIDs, intent.IntentID)
		case entity.ActionDelete:
			deleteIntentIDs = append(deleteIntentIDs, intent.IntentID)
		}
	}

	log.InfoContextf(ctx, "PublishNoticeDM len(upsertIntentIDs):%d, len(deleteIntentIDs):%d, "+
		"len(upsertSlotIDs):%d, len(deleteSlotIDs):%d", len(upsertIntentIDs), len(deleteIntentIDs),
		len(upsertSlotIDs), len(deleteSlotIDs))

	// 通知DM
	req := &KEP_DM.ReleaseIntentRobotRequest{
		RobotID:                robotID,
		UpsertIntentIDs:        upsertIntentIDs,
		DeleteIntentIDs:        deleteIntentIDs,
		UpsertSlotIDs:          upsertSlotIDs,
		DeleteSlotIDs:          deleteSlotIDs,
		RetrievalIntentGroupID: corpusProdGroupID,
		RetrievalEntryGroupID:  entryProdGroupID,
	}
	log.InfoContextf(ctx, "PublishNoticeDM ReleaseIntentRobot req:%+v", req)
	rsp, err := rpc.ReleaseIntentRobot(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "PublishNoticeDM ReleaseIntentRobot Failed, err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "PublishNoticeDM ReleaseIntentRobot rsp:%+v", rsp)
	return nil
}

func (d dao) GetTaskFlowReleaseStatus(ctx context.Context, robotID string, taskFlowType string,
	envType KEP.GetTaskFlowReleaseStatusReq_EnvType) (isPublish bool, err error) {
	var total int64
	// 联表查询
	join := fmt.Sprintf("INNER JOIN %s ON %s.%s = %s.%s",
		entity.RobotIntent{}.TableName(),
		entity.RobotIntent{}.TableName(), entity.TRobotIntentColumns.IntentID,
		entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.IntentID)

	// 测试环境
	if envType == KEP.GetTaskFlowReleaseStatusReq_TEST {
		// 发布测试环境，即保存到测试环境
		query := fmt.Sprintf("%s.%s = ? AND %s.%s = ? AND %s.%s IN ?",
			entity.RobotIntent{}.TableName(), entity.TRobotIntentColumns.RobotID,
			entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.FlowType,
			entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.FlowState)
		err = d.db.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).
			Joins(join).
			Where(query, robotID, taskFlowType, []string{entity.FlowStateEnable,
				entity.FlowStateChangeUnPublish, entity.FlowStatePublishChange}).
			Count(&total).Error
	}

	// 线上环境
	if envType == KEP.GetTaskFlowReleaseStatusReq_PROD {
		query := fmt.Sprintf("%s.%s = ? AND %s.%s = ? AND %s.%s = ?",
			entity.RobotIntent{}.TableName(), entity.TRobotIntentColumns.RobotID,
			entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.FlowType,
			entity.TaskFlow{}.TableName(), entity.TTaskFlowColumns.ReleaseStatus)
		err = d.prodDb.WithContext(ctx).Table(entity.TaskFlow{}.TableName()).
			Joins(join).
			Where(query, robotID, taskFlowType, entity.ReleaseStatusPublished).
			Count(&total).Error
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowReleaseStatus db.Find err:%v", err)
		return false, err
	}
	if total > 0 {
		return true, nil
	}
	log.InfoContextf(ctx, "robotId:%s|total:%d, GetTaskFlowReleaseStatus success", robotID, total)
	return false, nil
}
