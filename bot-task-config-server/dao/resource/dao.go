// bot-task-config-server
//
// @(#)dao.go  星期三, 十一月 06, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package resource

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"gorm.io/gorm"
)

// DeleteHandler 删除应用知识
type DeleteHandler interface {
	// CountNeedDeletedData 统计表需要删除数据的数量
	CountNeedDeletedData(ctx context.Context, corpID, robotID uint64, tableName string) (int64, error)
	// DeleteNeedDeletedData 删除表需要删除的数据
	DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64, tableName string, totalCount int64) error
}

// Dao 清理数据相关 Dao
type Dao interface {
	GetAppByID(ctx context.Context, robotID uint64) (*pb.GetAppListRsp_AppInfo, error)

	// CreateFlowDeleteTask 创建知识删除任务
	CreateFlowDeleteTask(ctx context.Context, params entity.FlowDeleteParams) error

	// CountTableNeedDeletedData 统计表需要删除数据的数量
	CountTableNeedDeletedData(ctx context.Context, gdb *gorm.DB, robotID interface{}, tableName string,
		customField, customFieldValue string) (int64, error)

	// DeleteTableNeedDeletedData 删除表需要删除的数据
	DeleteTableNeedDeletedData(ctx context.Context, gdb *gorm.DB, robotID interface{},
		tableName string, totalCount int64, customField, customFieldValue string) error

	// GetCustomFieldIDList 获取指定表自定义主键ID列表
	GetCustomFieldIDList(ctx context.Context, gdb *gorm.DB, robotID interface{},
		tableName, customSelectField string, customField string, customFieldValue []string) ([]string, error)
	// DeleteByCustomFieldID 删除指定表自定义字段列表
	DeleteByCustomFieldID(ctx context.Context, gdb *gorm.DB, tableName string, limit int64,
		customFields []string, customConditions []string, customFieldValues []interface{}) (int64, error)

	// FlowDeleteResultCallback 知识删除任务结果回调
	FlowDeleteResultCallback(ctx context.Context, taskID uint64, isSuccess bool, message string) error

	// DeleteWorkflowResource 删除应用下的工作流反馈
	DeleteWorkflowResource(ctx context.Context, robotID string) error
}

type dao struct{}

// NewDao new dao
func NewDao() Dao {
	return &dao{}
}
