// bot-task-config-server
//
// @(#)dao_impl.go  星期三, 十一月 06, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package resource

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"gorm.io/gorm"
)

const (
	dbFieldCorpID  = "corp_id"    // 企业ID
	dbFieldRobotID = "f_robot_id" // 应用ID
)

func (d *dao) GetAppByID(ctx context.Context, robotID uint64) (*admin.GetAppListRsp_AppInfo, error) {
	rsp, err := rpc.GetAppInfosByAppIds(ctx, 1, []uint64{robotID}, rpc.DeleteFlagNoFilter)
	if err != nil {
		log.ErrorContextf(ctx, "GetAppByID|err:%+v", err)
		return nil, err
	}
	return rsp.GetList()[0], nil
}

// CreateFlowDeleteTask 创建知识删除任务
func (d *dao) CreateFlowDeleteTask(ctx context.Context, params entity.FlowDeleteParams) error {
	log.InfoContextf(ctx, "CreateKnowledgeDeleteTask params:%+v", params)
	return scheduler.NewFlowDeleteTask(ctx, params)
}

// CountTableNeedDeletedData 统计表需要删除数据的数量, customField 自定义字段
func (d *dao) CountTableNeedDeletedData(ctx context.Context, gdb *gorm.DB, robotID interface{}, tableName string, customField,
	customFieldValue string) (int64, error) {
	log.InfoContextf(ctx, "CountTableNeedDeletedData robotID:%d, tableName:%s, customField:%s, "+
		"customFieldValue:%s", robotID, tableName, customField, customFieldValue)
	var count int64
	db := gdb.WithContext(ctx).Table(tableName)
	if len(customField) > 0 {
		query := fmt.Sprintf("%s = ?", customField)
		db = db.Where(query, customFieldValue)
	} else {
		query := fmt.Sprintf("%s = ?", dbFieldRobotID)
		db = db.Where(query, robotID)
	}
	err := db.Count(&count).Error
	if err != nil {
		log.ErrorContextf(ctx, "CountTableNeedDeletedData gormDB.Count Failed, err:%+v", err)
		return 0, err
	}
	log.InfoContextf(ctx, "CountTableNeedDeletedData robotID:%d, tableName:%s, customField:%s,"+
		" customFieldValue:%s, count:%d", robotID, tableName, customField, customFieldValue, count)
	return count, nil
}

// DeleteTableNeedDeletedData 删除表需要删除的数据
func (d *dao) DeleteTableNeedDeletedData(ctx context.Context, gdb *gorm.DB, robotID interface{},
	tableName string, totalCount int64, customField, customFieldValue string) error {
	log.InfoContextf(ctx, "DeleteTableNeedDeletedData robotID:%d, tableName:%s, customField:%s, "+
		"customFieldValue:%s, totalCount:%d", robotID, tableName, customField, customFieldValue, totalCount)
	count, err := d.CountTableNeedDeletedData(ctx, gdb, robotID, tableName, customField, customFieldValue)
	if err != nil {
		return err
	}
	if count != totalCount {
		err = fmt.Errorf("count not equal totalCount:%d != %d", count, totalCount)
		log.ErrorContextf(ctx, "DeleteTableNeedDeletedData Failed, err:%+v", err)
		return err
	}
	deletedCount := int64(0)
	for count > 0 {
		db := gdb.WithContext(ctx)
		if len(customField) > 0 {
			deleteSql := fmt.Sprintf("DELETE from %s WHERE %s = ?  LIMIT %d", tableName,
				customField, config.GetMainConfig().FlowDeleteConfig.DeleteBatchSize)
			db = db.Exec(deleteSql, customFieldValue)
		} else {
			deleteSql := fmt.Sprintf("DELETE from %s WHERE %s = ? LIMIT %d", tableName,
				dbFieldRobotID, config.GetMainConfig().FlowDeleteConfig.DeleteBatchSize)
			db = db.Exec(deleteSql, robotID)
		}
		if db.Error != nil {
			log.ErrorContextf(ctx, "DeleteTableNeedDeletedData gormDB.Deleted Failed, err:%+v", db.Error)
			return db.Error
		}
		deletedCount += db.RowsAffected
		log.InfoContextf(ctx, "DeleteTableNeedDeletedData deletedCount:%d", deletedCount)
		count, err = d.CountTableNeedDeletedData(ctx, gdb, robotID, tableName, customField, customFieldValue)
		if err != nil {
			return err
		}
	}
	if deletedCount != totalCount {
		err = fmt.Errorf("deletedCount not equal totalCount:%d != %d", deletedCount, totalCount)
		log.ErrorContextf(ctx, "DeleteTableNeedDeletedData Failed, err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "DeleteTableNeedDeletedData, robotID:%d, tableName:%s, customField:%s, "+
		"customFieldValue:%s success", robotID, tableName, customField, customFieldValue)
	return nil
}

// GetCustomFieldIDList 获取指定表自定义主键ID列表
func (d *dao) GetCustomFieldIDList(ctx context.Context, gdb *gorm.DB, robotID interface{},
	tableName, customSelectField string, customField string, customFieldValue []string) ([]string, error) {
	log.InfoContextf(ctx, "GetCustomFieldIDList robotID:%d, tableName:%s, customField:%s",
		robotID, tableName, customField)
	ids := make([]string, 0)
	db := gdb.WithContext(ctx).Table(tableName).Select(customSelectField)
	if len(customField) > 0 {
		query := fmt.Sprintf("%s IN ? ", customField)
		db = db.Where(query, customFieldValue)
	} else {
		query := fmt.Sprintf("%s = ?", dbFieldRobotID)
		db = db.Where(query, robotID)
	}
	err := db.Limit(config.GetMainConfig().FlowDeleteConfig.QueryBatchSize).
		Find(&ids).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetCustomFieldIDList gormDB.Find Failed, err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetCustomFieldIDList robotID:%d, tableName:%s, customSelectField:%s, customField:%s，"+
		"customFieldValue:%s, len(ids):%d", robotID, tableName, customSelectField, customField, customFieldValue, len(ids))
	return ids, nil
}

// DeleteByCustomFieldID 删除指定表自定义字段列表
func (d *dao) DeleteByCustomFieldID(ctx context.Context, gdb *gorm.DB, tableName string, limit int64,
	customFields []string, customConditions []string, customFieldValues []interface{}) (int64, error) {
	log.InfoContextf(ctx, "DeleteByCustomFieldID tableName:%s, limit:%d, "+
		"customFields:%+v, customConditions:%+v, len(customFieldValues):%d", tableName, limit,
		customFields, customConditions, len(customFieldValues))
	deletedCount := int64(0)
	if len(customFields) > 0 && len(customFields) == len(customConditions) &&
		len(customFields) == len(customFieldValues) {
		conditions := make([]string, 0)
		for i, filed := range customFields {
			conditions = append(conditions, fmt.Sprintf("%s %s ?", filed, customConditions[i]))
		}
		deleteCon := strings.Join(conditions, " AND ")
		deleteSql := fmt.Sprintf("DELETE from %s WHERE %s LIMIT %d", tableName, deleteCon, limit)
		if limit <= 0 { // 不做限制
			deleteSql = fmt.Sprintf("DELETE from %s WHERE %s", tableName, deleteCon)
		}
		db := gdb.WithContext(ctx).Exec(deleteSql, customFieldValues...)
		if db.Error != nil {
			log.ErrorContextf(ctx, "DeleteByCustomFieldID gormDB.Deleted Failed, err:%+v", db.Error)
			return 0, db.Error
		}
		deletedCount = db.RowsAffected
	}
	log.InfoContextf(ctx, "DeleteByCustomFieldID tableName:%s, deletedCount:%d", tableName, deletedCount)
	return deletedCount, nil
}

// FlowDeleteResultCallback 知识删除任务结果回调
func (d *dao) FlowDeleteResultCallback(ctx context.Context, taskID uint64, isSuccess bool, message string) error {
	log.InfoContextf(ctx, "FlowDeleteResultCallback taskID:%d, isSuccess:%v, message:%s",
		taskID, isSuccess, message)
	req := &admin.ClearAppFlowCallbackReq{
		TaskId:    taskID,
		IsSuccess: isSuccess,
		Message:   message,
	}
	log.DebugContextf(ctx, "FlowDeleteResultCallback req:%+v", req)
	rsp, err := rpc.ClearAppFlowCallback(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FlowDeleteResultCallback Failed, err:%+v", err)
		return err
	}
	log.DebugContextf(ctx, "FlowDeleteResultCallback rsp:%+v", rsp)
	return nil
}
