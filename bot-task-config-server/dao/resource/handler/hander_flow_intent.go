// bot-task-config-server
//
// @(#)handler_flow_entity.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// FlowIntentHandler 工作流实体相关删除
type FlowIntentHandler struct {
	dao resource.Dao
	tdb *gorm.DB
}

// NewFlowIntentHandler 初始化不满意回复删除处理
func NewFlowIntentHandler() *FlowIntentHandler {
	return &FlowIntentHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotTaskDelGORM(),
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (l *FlowIntentHandler) CountNeedDeletedData(ctx context.Context, corpsId, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, " FlowIntentHandler CountNeedDeletedData,  robotID:%d, tableName:%s",
		robotID, tableName)
	if tableName != "t_robot_intent" {
		return 0, fmt.Errorf("tableName must be `t_robot_intent`")
	}
	return l.dao.CountTableNeedDeletedData(ctx, l.tdb, robotID, "t_robot_intent", "", "")
}

// deleteFlowIdsData 删除跟 flowId关联的表记录
func (l *FlowIntentHandler) deleteFlowIdsData(ctx context.Context, robotID uint64, flowIds, intentIds []string, totalCount int64) error {
	if len(flowIds) > 0 {
		// t_task_flow_history
		_, err := l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_task_flow_history", 0,
			[]string{"f_flow_id"}, []string{"IN"}, []interface{}{flowIds})
		if err != nil {
			return err
		}
		// t_task_flow_publish_history
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_task_flow_publish_history", 0,
			[]string{"f_flow_id"}, []string{"IN"}, []interface{}{flowIds})
		if err != nil {
			return err
		}

		// t_task_flow
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_task_flow", int64(len(flowIds)),
			[]string{"f_flow_id"}, []string{"IN"}, []interface{}{flowIds})
		if err != nil {
			return err
		}
	}
	return nil
}

// deleteIntentIdsData 删除跟 intent 关联的表记录
func (l *FlowIntentHandler) deleteIntentIdsData(ctx context.Context, robotID uint64, intentIds []string, totalCount int64) error {
	if len(intentIds) > 0 {
		// t_intent_corpus_publish_history
		_, err := l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent_corpus_publish_history", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}
		// t_intent_entry
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent_entry", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}

		// t_intent_flow
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent_flow", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}
		// t_intent_slot
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent_slot", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}

		// t_intent_var
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent_var", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}

		// t_intent
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_intent", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}

		// t_corpus
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_corpus", int64(len(intentIds)*10),
			[]string{"f_intent_id"}, []string{"IN"}, []interface{}{intentIds})
		if err != nil {
			return err
		}

	}

	return nil
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (l *FlowIntentHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "FlowIntentHandler DeleteNeedDeletedData, robotID:%d, tableName:%s, "+
		"totalCount:%d", robotID, tableName, totalCount)
	if tableName != "t_robot_intent" {
		return fmt.Errorf("tableName must be `t_robot_intent`")
	}
	deletedCount := int64(0)
	intentIds, err := l.dao.GetCustomFieldIDList(ctx, l.tdb, robotID, "t_robot_intent", "f_intent_id", "", []string{""})
	if err != nil {
		return err
	}
	flowIds, err := l.dao.GetCustomFieldIDList(ctx, l.tdb, robotID, "t_task_flow", "f_flow_id", "f_intent_id", intentIds)
	if err != nil {
		return err
	}

	for len(intentIds) > 0 {

		err = l.deleteFlowIdsData(ctx, robotID, flowIds, intentIds, totalCount)
		if err != nil {
			return err
		}

		err = l.deleteIntentIdsData(ctx, robotID, intentIds, totalCount)
		if err != nil {
			return err
		}

		// t_robot_intent
		count, err := l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_robot_intent", int64(len(intentIds)),
			[]string{"f_intent_id", "f_robot_id"}, []string{"IN", "="}, []interface{}{intentIds, robotID})
		if err != nil {
			return err
		}
		deletedCount += count

		intentIds, err = l.dao.GetCustomFieldIDList(ctx, l.tdb, robotID, "t_robot_intent", "f_intent_id", "", []string{""})
		if err != nil {
			return err
		}
		flowIds, err = l.dao.GetCustomFieldIDList(ctx, l.tdb, robotID, "t_task_flow", "f_flow_id", "f_intent_id", intentIds)
		if err != nil {
			return err
		}

	}
	if deletedCount != totalCount {
		err := fmt.Errorf("deletedCount not equal totalCount:%d != %d", deletedCount, totalCount)
		log.ErrorContextf(ctx, "FlowEntityHandler DeleteNeedDeletedData Failed, err:%+v", err)
		return err
	}

	return nil
}
