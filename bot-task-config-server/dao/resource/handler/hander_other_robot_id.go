// bot-task-config-server
//
// @(#)hander_other_robot_id.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

// bot-task-config-server
//
// @(#)handler_flow_entity.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// OtherRobotIdHandler 工作流实体相关删除
type OtherRobotIdHandler struct {
	dao resource.Dao
	tdb *gorm.DB
	ts  []string
}

// NewOtherRobotIdHandler 初始化不满意回复删除处理
func NewOtherRobotIdHandler() *OtherRobotIdHandler {
	return &OtherRobotIdHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotTaskDelGORM(),
		ts:  []string{"t_export_file", "t_app_shared", "t_bot_task", "t_bot_task_history", "t_sync_task", "t_sync_task_log"},
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (l *OtherRobotIdHandler) CountNeedDeletedData(ctx context.Context, corpsId, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "OtherRobotIdHandler CountNeedDeletedData,  robotID:%d, tableName:%s",
		robotID, tableName)
	if tableName == "t_export_file" || tableName == "t_app_shared" || tableName == "t_var" {
		return l.dao.CountTableNeedDeletedData(ctx, l.tdb, robotID, tableName, "f_app_id", fmt.Sprintf("%d", robotID))
	}
	if tableName == "t_bot_task" || tableName == "t_bot_task_history" {
		return l.dao.CountTableNeedDeletedData(ctx, l.tdb, robotID, tableName, "user_id", fmt.Sprintf("%d", robotID))
	}
	if tableName == "t_sync_task" || tableName == "t_sync_task_log" {
		return l.dao.CountTableNeedDeletedData(ctx, l.tdb, robotID, tableName, "robot_id", fmt.Sprintf("%d", robotID))
	}
	return 0, fmt.Errorf("tableName must be %+v", l.ts)

}

// DeleteNeedDeletedData 删除表需要删除的数据
func (l *OtherRobotIdHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "FlowAppIdHandler DeleteNeedDeletedData, robotID:%d, tableName:%s, "+
		"totalCount:%d", robotID, tableName, totalCount)
	if util.ContainsList(l.ts, tableName) {
		// 防止类型隐式转换导致慢查询
		robotIdStr := fmt.Sprintf("%d", robotID)
		// t_export_file
		_, err := l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_export_file", 0,
			[]string{"f_app_id"}, []string{"="}, []interface{}{robotIdStr})
		if err != nil {
			return err
		}
		// t_app_shared
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_app_shared", 0,
			[]string{"f_app_id"}, []string{"="}, []interface{}{robotIdStr})
		if err != nil {
			return err
		}

		// t_var
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_var", 0,
			[]string{"f_app_id"}, []string{"="}, []interface{}{robotIdStr})
		if err != nil {
			return err
		}

		// t_bot_task
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_bot_task", 0,
			[]string{"user_id"}, []string{"="}, []interface{}{robotID})
		if err != nil {
			return err
		}
		// t_bot_task_history
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_bot_task_history", 0,
			[]string{"user_id"}, []string{"="}, []interface{}{robotID})
		if err != nil {
			return err
		}

		// t_sync_task
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_sync_task", 0,
			[]string{"robot_id"}, []string{"="}, []interface{}{robotIdStr})
		if err != nil {
			return err
		}
		// t_sync_task_log
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_sync_task_log", 0,
			[]string{"robot_id"}, []string{"="}, []interface{}{robotIdStr})
		if err != nil {
			return err
		}

	}

	return nil
}
