// bot-task-config-server
//
// @(#)hander_other_robot_id.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

// bot-task-config-server
//
// @(#)handler_flow_entity.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// OtherRobotIdProdHandler 工作流实体相关删除
type OtherRobotIdProdHandler struct {
	dao resource.Dao
	tdb *gorm.DB
	ts  []string
}

// NewOtherRobotIdProdHandler 初始化不满意回复删除处理
func NewOtherRobotIdProdHandler() *OtherRobotIdProdHandler {
	return &OtherRobotIdProdHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotTaskDelProdGORM(),
		ts:  []string{"t_var"},
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (l *OtherRobotIdProdHandler) CountNeedDeletedData(ctx context.Context, corpsId, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "OtherRobotIdHandler CountNeedDeletedData,  robotID:%d, tableName:%s",
		robotID, tableName)
	if util.ContainsList(l.ts, tableName) {
		return l.dao.CountTableNeedDeletedData(ctx, l.tdb, robotID, tableName, "f_app_id", fmt.Sprintf("%d", robotID))
	}
	return 0, fmt.Errorf("tableName must be %+v", l.ts)

}

// DeleteNeedDeletedData 删除表需要删除的数据
func (l *OtherRobotIdProdHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "FlowAppIdHandler DeleteNeedDeletedData, robotID:%d, tableName:%s, "+
		"totalCount:%d", robotID, tableName, totalCount)
	if util.ContainsList(l.ts, tableName) {
		// t_var
		_, err := l.dao.DeleteByCustomFieldID(ctx, l.tdb, "t_var", 0,
			[]string{"f_app_id"}, []string{"="}, []interface{}{fmt.Sprintf("%d", robotID)})
		if err != nil {
			return err
		}
	}

	return nil
}
