// bot-task-config-server
//
// @(#)handler.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

// Package handler ...
package handler

import (
	"fmt"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
)

const (
	OtherRobotIdDeleteHandler     = "OTHER_ROBOT_ID"      // 删除应用ID为f_app_id 的数据
	OtherRobotIdDeleteProdHandler = "OTHER_ROBOT_ID_PROD" // 删除应用ID为f_app_id 的数据

	RobotIDDeleteHandler      = "ROBOT_ID"       // 应用ID删除逻辑
	FlowIntentIDDeleteHandler = "FLOW_INTENT_ID" // 意图ID删除逻辑
	EntityIDDeleteHandler     = "ENTITY_ID"      // 实体ID删除逻辑

	RobotIDDeleteProdHandler      = "ROBOT_ID_PROD"       // 应用ID删除逻辑
	FlowIntentIDDeleteProdHandler = "FLOW_INTENT_ID_PROD" // 意图ID删除逻辑
	EntityIDDeleteProdHandler     = "ENTITY_ID_PROD"      // 实体ID删除逻辑

	WorkflowDeleteHandler          = "W_ROBOT_ID"
	WorkflowDeleteProdHandler      = "W_ROBOT_ID_PROD"
	WorkflowDeleteOtherHandler     = "W_ROBOT_OTHER_ID"
	WorkflowDeleteOtherProdHandler = "W_ROBOT_OTHER_ID_PROD"
)

// GetDeleteHandler 获取handler
func GetDeleteHandler(handlerType string) (resource.DeleteHandler, error) {
	switch handlerType {
	case FlowIntentIDDeleteHandler:
		return NewFlowIntentHandler(), nil
	case FlowIntentIDDeleteProdHandler:
		return NewFlowIntentProdHandler(), nil
	case EntityIDDeleteHandler:
		return NewFlowEntityHandler(), nil
	case EntityIDDeleteProdHandler:
		return NewFlowEntityHandlerProd(), nil
	case RobotIDDeleteHandler:
		return NewRobotIDHandler(), nil
	case RobotIDDeleteProdHandler:
		return NewRobotIDProdHandler(), nil
	case OtherRobotIdDeleteHandler:
		return NewOtherRobotIdHandler(), nil
	case OtherRobotIdDeleteProdHandler:
		return NewOtherRobotIdProdHandler(), nil
	case WorkflowDeleteHandler:
		return NewWorkflowRobotIDHandler(), nil
	case WorkflowDeleteProdHandler:
		return NewWorkflowRobotIDProdHandler(), nil
	case WorkflowDeleteOtherHandler:
		return NewWorkflowOtherRobotIDHandler(), nil
	case WorkflowDeleteOtherProdHandler:
		return NewWorkflowOtherRobotIDProdHandler(), nil
	default:
		return nil, fmt.Errorf("unknown handler type: %s", handlerType)
	}
}
