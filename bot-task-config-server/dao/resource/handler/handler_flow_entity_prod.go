// bot-task-config-server
//
// @(#)handler_flow_entity.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package handler

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// FlowEntityHandlerProd 工作流实体相关删除
type FlowEntityHandlerProd struct {
	dao     resource.Dao
	tdbProd *gorm.DB
}

// NewFlowEntityHandlerProd 初始化不满意回复删除处理
func NewFlowEntityHandlerProd() *FlowEntityHandlerProd {
	return &FlowEntityHandlerProd{
		dao:     resource.NewDao(),
		tdbProd: database.GetLLMRobotTaskDelProdGORM(),
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (l *FlowEntityHandlerProd) CountNeedDeletedData(ctx context.Context, corpsId, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "FlowEntityHandlerProd CountNeedDeletedData,  robotID:%d, tableName:%s",
		robotID, tableName)
	if tableName != "t_entity" {
		return 0, fmt.Errorf("tableName must be `t_entity`")
	}
	return l.dao.CountTableNeedDeletedData(ctx, l.tdbProd, robotID, "t_entity", "", "")
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (l *FlowEntityHandlerProd) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "FlowEntityHandlerProd DeleteNeedDeletedData, robotID:%d, tableName:%s, "+
		"totalCount:%d", robotID, tableName, totalCount)
	if tableName != "t_entity" {
		return fmt.Errorf("tableName must be `t_entity`")
	}
	deletedCount := int64(0)
	ids, err := l.dao.GetCustomFieldIDList(ctx, l.tdbProd, robotID, "t_entity", "f_entity_id", "", []string{""})
	if err != nil {
		return err
	}
	for len(ids) > 0 {
		// t_entry
		_, err = l.dao.DeleteByCustomFieldID(ctx, l.tdbProd, "t_entry", 0,
			[]string{"f_entity_id"}, []string{"IN"}, []interface{}{ids})
		if err != nil {
			return err
		}
		// t_slot_entity
		_, err := l.dao.DeleteByCustomFieldID(ctx, l.tdbProd, "t_slot_entity", 0,
			[]string{"f_entity_id"}, []string{"IN"}, []interface{}{ids})
		if err != nil {
			return err
		}

		// t_entity
		count, err := l.dao.DeleteByCustomFieldID(ctx, l.tdbProd, "t_entity", 0,
			[]string{"f_entity_id", "f_robot_id"}, []string{"IN", "="}, []interface{}{ids, robotID})
		if err != nil {
			return err
		}
		deletedCount += count

		ids, err = l.dao.GetCustomFieldIDList(ctx, l.tdbProd, robotID, "t_entity", "f_entity_id", "", []string{""})
		if err != nil {
			return err
		}
	}
	if deletedCount != totalCount {
		err = fmt.Errorf("deletedCount not equal totalCount:%d != %d", deletedCount, totalCount)
		log.ErrorContextf(ctx, "FlowEntityHandlerProd DeleteNeedDeletedData Failed, err:%+v", err)
		return err
	}
	return nil
}
