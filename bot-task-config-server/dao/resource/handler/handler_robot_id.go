package handler

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// RobotIDHandler 应用ID删除
type RobotIDHandler struct {
	dao resource.Dao
	tdb *gorm.DB
}

// NewRobotIDHandler 初始化应用ID删除处理
func NewRobotIDHandler() *RobotIDHandler {
	return &RobotIDHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotTaskDelGORM(),
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (r *RobotIDHandler) CountNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "RobotIDHandler CountNeedDeletedData corpID:%d, robotID:%d, tableName:%s",
		corpID, robotID, tableName)
	return r.dao.CountTableNeedDeletedData(ctx, r.tdb, robotID, tableName, "", "")
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (r *RobotIDHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "RobotIDHandler DeleteNeedDeletedData corpID:%d, robotID:%d, tableName:%s, totalCount:%d",
		corpID, robotID, tableName, totalCount)
	// corpID置为0
	return r.dao.DeleteTableNeedDeletedData(ctx, r.tdb, robotID, tableName, totalCount, "", "")
}
