package handler

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// RobotIDProdHandler 应用ID删除
type RobotIDProdHandler struct {
	dao     resource.Dao
	tdbProd *gorm.DB
}

// NewRobotIDProdHandler 初始化应用ID删除处理
func NewRobotIDProdHandler() *RobotIDProdHandler {
	return &RobotIDProdHandler{
		dao:     resource.NewDao(),
		tdbProd: database.GetLLMRobotTaskDelProdGORM(),
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (r *RobotIDProdHandler) CountNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "RobotIDHandler CountNeedDeletedData corpID:%d, robotID:%d, tableName:%s",
		corpID, robotID, tableName)
	return r.dao.CountTableNeedDeletedData(ctx, r.tdbProd, robotID, tableName, "", "")
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (r *RobotIDProdHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "RobotIDHandler DeleteNeedDeletedData corpID:%d, robotID:%d, tableName:%s, totalCount:%d",
		corpID, robotID, tableName, totalCount)
	// corpID置为0
	return r.dao.DeleteTableNeedDeletedData(ctx, r.tdbProd, robotID, tableName, totalCount, "", "")
}
