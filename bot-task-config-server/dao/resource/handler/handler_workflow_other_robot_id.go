package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// WorkflowOtherRobotIDHandler 应用ID删除
type WorkflowOtherRobotIDHandler struct {
	dao resource.Dao
	tdb *gorm.DB
	ts  []string
}

// NewWorkflowOtherRobotIDHandler 初始化应用ID删除处理
func NewWorkflowOtherRobotIDHandler() *WorkflowOtherRobotIDHandler {
	return &WorkflowOtherRobotIDHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotWorkflowDelGORM(),
		ts:  []string{"t_sync_task", "t_sync_task_log", "t_pdl_sync_task", "t_pdl_sync_task_log"},
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (r *WorkflowOtherRobotIDHandler) CountNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "WorkflowOtherRobotIDHandler CountNeedDeletedData corpID:%d, robotID:%d, tableName:%s",
		corpID, robotID, tableName)
	if util.ContainsList(r.ts, tableName) {
		return r.dao.CountTableNeedDeletedData(ctx, r.tdb, robotID, tableName, "robot_id", fmt.Sprintf("%d", robotID))
	}
	return 0, fmt.Errorf("tableName must be %+v", r.ts)
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (r *WorkflowOtherRobotIDHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "WorkflowOtherRobotIDHandler DeleteNeedDeletedData corpID:%d, robotID:%d,"+
		" tableName:%s, totalCount:%d", corpID, robotID, tableName, totalCount)
	if util.ContainsList(r.ts, tableName) {
		_, err := r.dao.DeleteByCustomFieldID(ctx, r.tdb, tableName, 0,
			[]string{"robot_id"}, []string{"="}, []interface{}{fmt.Sprintf("%d", robotID)})
		if err != nil {
			return err
		}
	}
	return nil
}
