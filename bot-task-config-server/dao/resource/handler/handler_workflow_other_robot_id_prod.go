package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// WorkflowOtherRobotIDProdHandler 应用ID删除
type WorkflowOtherRobotIDProdHandler struct {
	dao resource.Dao
	tdb *gorm.DB
	ts  []string
}

// NewWorkflowOtherRobotIDProdHandler 初始化应用ID删除处理
func NewWorkflowOtherRobotIDProdHandler() *WorkflowOtherRobotIDProdHandler {
	return &WorkflowOtherRobotIDProdHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotWorkflowDelProdGORM(),
		ts:  []string{"t_sync_task", "t_sync_task_log"},
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (r *WorkflowOtherRobotIDProdHandler) CountNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "WorkflowOtherRobotIDProdHandler CountNeedDeletedData corpID:%d, robotID:%d, tableName:%s",
		corpID, robotID, tableName)
	if util.ContainsList(r.ts, tableName) {
		return r.dao.CountTableNeedDeletedData(ctx, r.tdb, robotID, tableName, "robot_id", fmt.Sprintf("%d", robotID))
	}
	return 0, fmt.Errorf("tableName must be %+v", r.ts)
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (r *WorkflowOtherRobotIDProdHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "WorkflowOtherRobotIDProdHandler DeleteNeedDeletedData corpID:%d, robotID:%d,"+
		" tableName:%s, totalCount:%d", corpID, robotID, tableName, totalCount)
	if util.ContainsList(r.ts, tableName) {
		// t_sync_task
		_, err := r.dao.DeleteByCustomFieldID(ctx, r.tdb, "t_sync_task", 0,
			[]string{"robot_id"}, []string{"="}, []interface{}{fmt.Sprintf("%d", robotID)})
		if err != nil {
			return err
		}
		// t_sync_task_log
		_, err = r.dao.DeleteByCustomFieldID(ctx, r.tdb, "t_sync_task_log", 0,
			[]string{"robot_id"}, []string{"="}, []interface{}{fmt.Sprintf("%d", robotID)})
		if err != nil {
			return err
		}
	}
	return nil
}
