package handler

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// WorkflowRobotIDProdHandler 应用ID删除
type WorkflowRobotIDProdHandler struct {
	dao resource.Dao
	tdb *gorm.DB
}

// NewWorkflowRobotIDProdHandler 初始化应用ID删除处理
func NewWorkflowRobotIDProdHandler() *WorkflowRobotIDProdHandler {
	return &WorkflowRobotIDProdHandler{
		dao: resource.NewDao(),
		tdb: database.GetLLMRobotWorkflowDelProdGORM(),
	}
}

// CountNeedDeletedData 统计表需要删除数据的数量
func (r *WorkflowRobotIDProdHandler) CountNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string) (int64, error) {
	log.InfoContextf(ctx, "WorkflowRobotIDProdHandler CountNeedDeletedData corpID:%d, robotID:%d, tableName:%s",
		corpID, robotID, tableName)
	robotIdStr := fmt.Sprintf("%d", robotID)
	return r.dao.CountTableNeedDeletedData(ctx, r.tdb, robotIdStr, tableName, "", "")
}

// DeleteNeedDeletedData 删除表需要删除的数据
func (r *WorkflowRobotIDProdHandler) DeleteNeedDeletedData(ctx context.Context, corpID, robotID uint64,
	tableName string, totalCount int64) error {
	log.InfoContextf(ctx, "WorkflowRobotIDProdHandler DeleteNeedDeletedData corpID:%d, robotID:%d, "+
		"tableName:%s, totalCount:%d", corpID, robotID, tableName, totalCount)
	robotIdStr := fmt.Sprintf("%d", robotID)
	return r.dao.DeleteTableNeedDeletedData(ctx, r.tdb, robotIdStr, tableName, totalCount, "", "")
}
