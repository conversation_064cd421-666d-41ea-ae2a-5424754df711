// bot-task-config-server
//
// @(#)workflow_imp.go  星期三, 十一月 20, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package resource

import (
	"context"
	"time"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

func (d dao) DeleteWorkflowResource(ctx context.Context, robotID string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		err := d.deleteWorkflowFeedback(ctx, tx, robotID)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (d dao) deleteWorkflowFeedback(ctx context.Context, tx *gorm.DB, robotID string) error {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	if err := tx.Table(entity.WorkflowFeedbackRecord{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id = ?", robotID).
		Updates(map[string]interface{}{
			"f_is_deleted":  1,
			"f_uin":         uin,
			"f_sub_uin":     subUin,
			"f_update_time": time.Now(),
		}).Error; err != nil {
		return err
	}
	// 删除原因
	if err := tx.Table(entity.WorkflowFeedbackReason{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id = ?", robotID).
		Updates(map[string]interface{}{
			"f_is_deleted":  1,
			"f_sub_uin":     subUin,
			"f_update_time": time.Now(),
		}).Error; err != nil {
		return err
	}
	return nil
}
