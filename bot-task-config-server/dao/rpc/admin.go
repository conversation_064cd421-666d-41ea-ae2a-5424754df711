package rpc

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

const (
	// BizType 业务类型
	BizType uint32 = 1

	// CorpType 企业类型
	CorpType uint32 = 1

	DeleteFlagNoFilter = 0
	DeleteFlagUnDelete = 1
	DeleteFlagDeleted  = 2
)

// GetAppChatInputNum 获取输入限制
func GetAppChatInputNum(ctx context.Context, appBizID string, mn string) (int32, error) {
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		log.WarnContextf(ctx, "rpc.admin.GetAppChatInputNum|err%+v", err)
		return 0, err
	}
	if botId == 0 || len(mn) == 0 {
		return 0, errors.BadRequestError(fmt.Sprintf("请求参数错误：appBizID:%d|mn:%s", botId, mn))
	}
	req := &pb.GetAppChatInputNumReq{
		AppBizId:  botId,
		ModelName: mn,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetAppChatInputNum(ctx, req)
	if err != nil {
		log.WarnContextf(ctx, "rpc.admin.GetAppChatInputNum|err%+v", err)
		return 0, err
	}
	return rsp.InputLenLimit, nil
}

// GetModelList 获取模型列表
func GetModelList(ctx context.Context, corpId uint64) ([]*pb.ModelInfo, error) {
	if corpId == 0 {
		log.WarnContextf(ctx, "rpc.admin.GetModelList.req|corpId,err%+v", corpId)
		return nil, errors.BadRequestError(fmt.Sprintf("请求参数corpId：%d 错误，", corpId))
	}
	req := &pb.GetModelListReq{
		CorpId: corpId,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetModelList(ctx, req)
	if err != nil {
		log.WarnContextf(ctx, "rpc.admin.GetModelList|err%+v", err)
		return nil, err
	}
	return rsp.GetList(), nil
}

// GetModelInfo 获取模型信息
func GetModelInfo(ctx context.Context, corpId uint64, modelName string) (*pb.GetModelInfoRsp, error) {
	if corpId == 0 {
		log.WarnContextf(ctx, "rpc.admin.GetModelList.req|corpId,err%+v", corpId)
		return nil, errors.BadRequestError(fmt.Sprintf("请求参数corpId：%d 错误，", corpId))
	}
	req := &pb.GetModelInfoReq{
		CorpId:    corpId,
		ModelName: modelName,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetModelInfo(ctx, req)
	if err != nil {
		log.WarnContextf(ctx, "GetModelInfo|err:%+v", err)
		return nil, err
	}
	return rsp, nil
}

// DeleteFeedbackByFlowIds 通过flowId 删除对应的反馈
func DeleteFeedbackByFlowIds(ctx context.Context, flowIds []string, appid uint64) error {
	req := &pb.DeleteFeedbackByFlowIdsReq{
		FlowIds:  flowIds,
		AppBizId: appid,
	}
	_, err := proxy.GetAdminAPIProxy().DeleteFeedbackByFlowIds(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

// ListCorpStaffByIds 通过员工Id获取员工信息
func ListCorpStaffByIds(ctx context.Context, statffIds []uint64) (*pb.ListCorpStaffByIdsRsp, error) {
	pageSize := uint32(len(statffIds))
	if pageSize == 0 {
		pageSize = 1
	}
	req := &pb.ListCorpStaffByIdsReq{
		CorpBizId: 0,
		Page:      1,
		PageSize:  pageSize,
		StaffIds:  statffIds,
	}
	return proxy.GetAdminAPIProxy().ListCorpStaffByIds(ctx, req)
}

// GetRobotInfo 获取机器人详情信息
func GetRobotInfo(ctx context.Context, botBizID uint64) (*pb.RobotInfo, error) {
	req := &pb.GetRobotListReq{
		Page:       1,
		PageSize:   1,
		BotBizIds:  []uint64{botBizID},
		DeleteFlag: 1,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetRobotList(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "get robot list req:%+v,err:%+v", req, err)
		return nil, err
	}
	if len(rsp.GetList()) == 0 {
		return nil, errors.ErrRobotNotFound
	}
	return rsp.GetList()[0], nil
}

// GetAppInfo 获取应用详情信息, scenes:测试场景为1，正式场景为2
func GetAppInfo(ctx context.Context, scenes uint32, botBizID uint64) (*pb.GetAppInfoRsp, error) {
	req := &pb.GetAppInfoReq{
		AppBizId: botBizID,
		Scenes:   scenes,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetAppInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "get appInfo req:%+v,err:%+v", req, err)
		return nil, err
	}
	return rsp, nil
}

// GetAppInfosByAppIds 获取应用详情信息, scenes:测试场景为1，正式场景为2； DeleteFlag: 0不过滤 1未删除 2已删除
func GetAppInfosByAppIds(ctx context.Context, scenes uint32, botBizIDs []uint64,
	deleteFlag uint32) (*pb.GetAppListRsp, error) {
	size := uint32(len(botBizIDs))
	req := &pb.GetAppListReq{
		Page:       1,
		PageSize:   size,
		BotBizIds:  botBizIDs,
		Scenes:     scenes,
		DeleteFlag: deleteFlag,
	}
	rsp, err := proxy.GetAdminAPIProxy().GetAppList(ctx, req)
	log.InfoContextf(ctx, "sid:%s|GetAppInfosByAppIds|req:%+v", util.RequestID(ctx), req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "get app list req:%+v,err:%+v", req, err)
		return nil, err
	}
	if len(rsp.GetList()) == 0 {
		return nil, errors.ErrRobotNotFound
	}

	log.InfoContextf(ctx, "sid:%s|GetAppInfosByAppIds|rsp:%+v", util.RequestID(ctx), rsp)
	return rsp, nil
}

// CreateNotice 创建通知接口
func CreateNotice(ctx context.Context, req *pb.CreateNoticeReq) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CreateNotice sid:%s, req:%+v", sid, req)
	if _, err := proxy.GetAdminAPIProxy().CreateNotice(ctx, req); err != nil {
		log.ErrorContextf(ctx, "CreateNotice Failed, sid:%s,err:%+v", sid, err)
		return err
	}
	return nil
}

// CheckSession 检查session会话信息
func CheckSession(ctx context.Context, req *pb.CheckSessionReq) (*pb.CheckSessionRsp, error) {
	rsp, err := proxy.GetAdminLoginProxy().CheckSession(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CheckSession req:%+v,err:%+v", req, err)
		return nil, err
	}
	return rsp, nil
}

// GetAccountBalance 获取体验包余量
func GetAccountBalance(ctx context.Context, appInfo *pb.GetAppListRsp_AppInfo) (float64, error) {
	req := &pb.DescribeAccountBalanceReq{
		BizType:  BizType,
		CorpType: CorpType,
		CorpId:   strconv.FormatUint(appInfo.GetCorpId(), 10),
		AccountTypes: []*pb.AccountType{
			{
				Type: "KnowledgeQA",
				//ModelName: modelName,
			},
		},
	}
	log.InfoContextf(ctx, "GetAccountBalance req:%+v", req)
	rsp, err := proxy.GetAdminAPIProxy().DescribeAccountBalance(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetAccountBalance failed, err:%+v", err)
		return 0, err
	}
	log.InfoContextf(ctx, "GetAccountBalance rsp:%+v", rsp)
	if rsp != nil && len(rsp.AccountList) > 0 {
		return rsp.AccountList[0].Balance, nil
	}
	return 0, err
}

// GetDescribeLicense 私有化场景下需要校验License是否过期
func GetDescribeLicense(ctx context.Context, req *pb.GetDescribeLicenseReq) (*pb.GetDescribeLicenseRsp, error) {
	rsp, err := proxy.GetAdminAPIProxy().GetDescribeLicense(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetDescribeLicense req:%+v,err:%+v", req, err)
		return nil, err
	}
	return rsp, nil
}

// CheckVarIsUsed 检查自定义参数是否被使用
func CheckVarIsUsed(ctx context.Context, req *pb.CheckVarIsUsedReq) (
	*pb.CheckVarIsUsedRsp, error) {
	var err error
	log.InfoContextf(ctx, "CheckVarIsUsed req:%+v", req)
	resp, err := proxy.GetAdminAPIProxy().CheckVarIsUsed(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CheckVarIsUsed Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "CheckVarIsUsed Success resp:%+v", resp)
	return resp, nil
}

// ModifyAppVar 检查自定义参数是否被使用
func ModifyAppVar(ctx context.Context, req *pb.ModifyAppVarReq) (
	*pb.ModifyAppVarRsp, error) {
	var err error
	log.InfoContextf(ctx, "ModifyAppVar req:%+v", req)
	resp, err := proxy.GetAdminAPIProxy().ModifyAppVar(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ModifyAppVar Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "ModifyAppVar Success resp:%+v", resp)
	return resp, nil
}

// ClearAppFlowCallback 清理应用下数据回调
func ClearAppFlowCallback(ctx context.Context, req *pb.ClearAppFlowCallbackReq) (*pb.ClearAppFlowCallbackRsp, error) {
	var err error
	log.InfoContextf(ctx, "ClearAppFlowCallback req:%+v", req)
	resp, err := proxy.GetAdminAPIProxy().ClearAppFlowCallback(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ClearAppFlowCallback Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "ClearAppFlowCallback Success resp:%+v", resp)
	return resp, nil
}

// GetValidExperienceApps 获取有效的体验应用信息
func GetValidExperienceApps(ctx context.Context, req *pb.GetValidExperienceAppsReq) (
	*pb.GetValidExperienceAppsRsp, error) {
	var err error
	log.InfoContextf(ctx, "GetValidExperienceApps req:%+v", req)
	resp, err := proxy.GetAdminAPIProxy().GetValidExperienceApps(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetValidExperienceApps Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetValidExperienceApps Success resp:%+v", resp)
	return resp, nil
}

// IsCustomModel 是否是自定义模型
func IsCustomModel(ctx context.Context, mainModel string) bool {
	req := &pb.GetModelFinanceInfoReq{ModelNames: []string{mainModel}}
	log.InfoContextf(ctx, "IsCustomModel req:%+v", req)
	rsp, err := proxy.GetAdminAPIProxy().GetModelFinanceInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "IsCustomModel Get app by appKey error: %+v, req: %+v", err, req)
		return false
	}
	log.InfoContextf(ctx, "IsCustomModel Success resp:%+v", rsp)
	if info, ok := rsp.GetModelFinanceInfo()[mainModel]; ok {
		return info.IsCustomModel
	}
	return false
}
