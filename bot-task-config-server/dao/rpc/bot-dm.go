package rpc

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
)

// UpsertVariablesToSandbox 保存自定义变量到Sandbox
func UpsertVariablesToSandbox(ctx context.Context, req *KEP_DM.UpsertVariablesToSandboxRequest) (
	*KEP_DM.UpsertVariablesToSandboxReply, error) {
	sid := util.RequestID(ctx)
	var err error
	resp, err := proxy.GetBotDmProxy().UpsertVariablesToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertVariablesToSandbox Failed|sid:%s|err:%+v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertVariablesToSandbox Success|sid:%s|resp:%v", sid, resp)
	return resp, nil
}

// DeleteVariablesToSandbox 删除自定义变量到SandBox
func DeleteVariablesToSandbox(ctx context.Context,
	req *KEP_DM.DeleteVariablesInSandboxRequest) (*KEP_DM.DeleteVariablesInSandboxReply, error) {
	sid := util.RequestID(ctx)

	var err error
	resp, err := proxy.GetBotDmProxy().DeleteVariablesInSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteVariablesToSandbox Failed|sid:%s|err:%+v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteVariablesToSandbox Success sid:%s|resp:%+v", sid, resp)
	return resp, nil
}

// UpdateTaskFlowToSandbox 任务流程更新测试环境数据
func UpdateTaskFlowToSandbox(ctx context.Context, req *KEP_DM.UpsertRobotIntentToSandboxRequest) (
	*KEP_DM.UpsertRobotIntentToSandboxReply, error) {
	var err error
	resp, err := proxy.GetBotDmProxy().UpsertRobotIntentToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateTaskFlowToSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "UpdateTaskFlowToSandbox|RESP|%v", resp)
	return resp, nil
}

// DeleteTaskFlowToSandbox 任务流程删除到测试环境数据
func DeleteTaskFlowToSandbox(ctx context.Context, req *KEP_DM.DeleteRobotIntentsInSandboxRequest) (
	*KEP_DM.DeleteRobotIntentsInSandboxReply, error) {
	var err error
	resp, err := proxy.GetBotDmProxy().DeleteRobotIntentsInSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteTaskFlowToSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteTaskFlowToSandbox|RESP|%v", resp)
	return resp, nil
}

// ReleaseIntentRobot 任务流程发布到正式环境
func ReleaseIntentRobot(ctx context.Context, req *KEP_DM.ReleaseIntentRobotRequest) (
	*KEP_DM.ReleaseIntentRobotReply, error) {
	var err error
	resp, err := proxy.GetBotDmProxy().ReleaseIntentRobot(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ReleaseIntentRobot|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "ReleaseIntentRobot|RESP|%v", resp)
	return resp, nil
}

// UpsertSlotsToSandbox 保存槽位到Sandbox
func UpsertSlotsToSandbox(ctx context.Context, req *KEP_DM.UpsertSlotsToSandboxRequest) (
	*KEP_DM.UpsertSlotsToSandboxReply, error) {
	sid := util.RequestID(ctx)
	var err error
	resp, err := proxy.GetBotDmProxy().UpsertSlotsToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertSlotsToSandbox Failed! sid:%s,err:%+v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertSlotsToSandbox Success! sid:%s,resp:%v", sid, resp)
	return resp, nil
}

// DeleteSlotsToSandbox 删除槽位到SandBox
func DeleteSlotsToSandbox(ctx context.Context, req *KEP_DM.DeleteSlotsInSandboxRequest) (
	*KEP_DM.DeleteSlotsInSandboxReply, error) {
	sid := util.RequestID(ctx)

	var err error
	resp, err := proxy.GetBotDmProxy().DeleteSlotsInSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteSlotsToSandbox Failed! sid:%s,err:%+v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteSlotsToSandbox Success sid:%s,resp:%+v", sid, resp)
	return resp, nil
}

// UpsertRobotToSandbox 初始化向量库的时候调用，通知DM向量库的名字
func UpsertRobotToSandbox(ctx context.Context, req *KEP_DM.UpsertRobotToSandboxRequest) (
	*KEP_DM.UpsertRobotToSandboxReply, error) {
	sid := util.RequestID(ctx)

	var err error
	resp, err := proxy.GetBotDmProxy().UpsertRobotToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertRobotToSandbox Failed! sid:%s,err:%+v", sid, err)
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertRobotToSandbox Success! sid:%s,req:%+v,resp:%+v", sid, req, resp)
	return resp, nil
}
