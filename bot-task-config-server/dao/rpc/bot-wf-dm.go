package rpc

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// UpsertWorkflowToSandbox 发布工作流到Sandbox
func UpsertWorkflowToSandbox(ctx context.Context, req *KEP_WF_DM.UpsertWorkflowToSandboxRequest) (
	*KEP_WF_DM.UpsertWorkflowToSandboxReply, error) {
	var err error
	resp, err := proxy.GetWfDmProxy().UpsertWorkflowToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertWorkflowToSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertWorkflowToSandbox|RESP|%v", resp)
	return resp, nil
}

// DeleteWorkflowToSandbox 工作流程删除到测试环境数据
func DeleteWorkflowToSandbox(ctx context.Context, req *KEP_WF_DM.DeleteWorkflowsInSandboxRequest) (
	*KEP_WF_DM.DeleteWorkflowsInSandboxReply, error) {
	var err error
	log.InfoContextf(ctx, "DeleteWorkflowToSandbox req:%+v", req)
	resp, err := proxy.GetWfDmProxy().DeleteWorkflowsInSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowToSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteWorkflowToSandbox|RESP|%v", resp)
	return resp, nil
}

// UpsertVariablesToWorkflowSandbox 保存自定义变量到Sandbox
func UpsertVariablesToWorkflowSandbox(ctx context.Context, req *KEP_WF_DM.UpsertVariablesToSandboxRequest) (
	*KEP_WF_DM.UpsertVariablesToSandboxReply, error) {
	needDmNotify := config.GetMainConfig().WorkflowDmNotify
	resp := &KEP_WF_DM.UpsertVariablesToSandboxReply{}
	if needDmNotify {
		var err error
		log.InfoContextf(ctx, "UpsertVariablesToWorkflowSandbox req:%+v", req)
		resp, err = proxy.GetWfDmProxy().UpsertVariablesToSandbox(ctx, req)
		if err != nil {
			log.ErrorContextf(ctx, "UpsertVariablesToWorkflowSandbox Failed err:%+v", err)
			return nil, err
		}
		log.InfoContextf(ctx, "UpsertVariablesToWorkflowSandbox Success resp:%v", resp)
	}

	return resp, nil
}

// DeleteVariablesToWorkflowSandbox 删除自定义变量到SandBox
func DeleteVariablesToWorkflowSandbox(ctx context.Context,
	req *KEP_WF_DM.DeleteVariablesInSandboxRequest) (*KEP_WF_DM.DeleteVariablesInSandboxReply, error) {
	var err error
	needDmNotify := config.GetMainConfig().WorkflowDmNotify
	resp := &KEP_WF_DM.DeleteVariablesInSandboxReply{}

	if needDmNotify {
		log.InfoContextf(ctx, "DeleteVariablesToWorkflowSandbox req:%+v", req)
		resp, err = proxy.GetWfDmProxy().DeleteVariablesInSandbox(ctx, req)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteVariablesToWorkflowSandbox Failed err:%+v", err)
			return nil, err
		}
		log.InfoContextf(ctx, "DeleteVariablesToWorkflowSandbox Success resp:%+v", resp)
	}
	return resp, nil
}

// UpsertParametersToSandbox 参数提取节点-参数Upsert到测试环境
func UpsertParametersToSandbox(ctx context.Context, req *KEP_WF_DM.UpsertParametersToSandboxRequest) (*KEP_WF_DM.UpsertParametersToSandboxReply, error) {
	var err error
	log.InfoContextf(ctx, "UpsertParametersToSandbox Req:%+v", req)
	resp, err := proxy.GetWfDmProxy().UpsertParametersToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertParametersToSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertParametersToSandbox|RESP|%v", resp)
	return resp, nil
}

// DeleteParametersInSandbox 参数提取节点-参数删除到测试环境
func DeleteParametersInSandbox(ctx context.Context, req *KEP_WF_DM.DeleteParametersInSandboxRequest) (*KEP_WF_DM.DeleteParametersInSandboxReply, error) {
	var err error
	log.InfoContextf(ctx, "DeleteParametersInSandbox Req:%+v", req)
	resp, err := proxy.GetWfDmProxy().DeleteParametersInSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteParametersInSandbox|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteParametersInSandbox|RESP|%v", resp)
	return resp, nil
}

// UpsertAppToSandbox 初始化向量库的时候调用，通知DM向量库的名字
func UpsertAppToSandbox(ctx context.Context, req *KEP_WF_DM.UpsertAppToSandboxRequest) (
	*KEP_WF_DM.UpsertAppToSandboxReply, error) {
	var err error
	log.InfoContextf(ctx, "UpsertAppToSandbox req:%+v", req)
	resp, err := proxy.GetWfDmProxy().UpsertAppToSandbox(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UpsertAppToSandbox Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "UpsertAppToSandbox Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}

// StartWorkflowRun 启动工作流运行实例
func StartWorkflowRun(ctx context.Context, req *KEP_WF_DM.StartWorkflowRunRequest) (
	*KEP_WF_DM.StartWorkflowRunReply, error) {
	var err error
	log.InfoContextf(ctx, "StartWorkflowRun req:%+v", req)
	resp, err := proxy.GetWfDmProxy().StartWorkflowRun(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "StartWorkflowRun Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "StartWorkflowRun Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}

// StopWorkflowRun 停止工作流运行实例
func StopWorkflowRun(ctx context.Context, req *KEP_WF_DM.StopWorkflowRunRequest) (
	*KEP_WF_DM.StopWorkflowRunReply, error) {
	var err error
	log.InfoContextf(ctx, "StopWorkflowRun req:%+v", req)
	resp, err := proxy.GetWfDmProxy().StopWorkflowRun(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "StopWorkflowRun Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "StopWorkflowRun Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}
