package rpc

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_exec_pycode_server"
	interpreter "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
)

// const (
//	// DefaultEntryFuncName 默认代码入口
//	DefaultEntryFuncName = "main"
// )

const (
	// // UnSupportResultType 不支持类型
	// UnSupportResultType = "unsupport"
	// // FileResultType 文件类型
	// FileResultType = "file"
	// // ImageResultType 图片类型
	// ImageResultType = "image"

	// TextResultType 文本类型
	TextResultType = "text"

	// // WarnResultType 警告类型
	// WarnResultType = "warning"

	// ErrResultType 失败类型
	ErrResultType = "error"
)

var (
	// 定义正则表达式，匹配 {{name}} 格式的占位符
	placeHolderRegexp = regexp.MustCompile(`\{\{([^{}]*?)\}\}`)
)

// ReplacePlaceholders 替换文本中的 {{name}} 占位符为对应的值
func ReplacePlaceholders(text string, values map[string]any) (allNames []string, result string) {
	// 使用正则表达式查找并替换占位符
	result = placeHolderRegexp.ReplaceAllStringFunc(text, func(match string) string {
		// 提取占位符中的键名
		key := placeHolderRegexp.FindStringSubmatch(match)[1]
		key = strings.TrimSpace(key)
		// 查找对应的值，如果存在则替换，否则保留原占位符
		if val, ok := values[key]; ok {
			allNames = append(allNames, key)
			valueStr, ok := val.(string)
			if ok {
				return valueStr
			}
			return utils.ToJsonString(val)
		}
		return ""
	})
	return
}

// CheckCode 检查代码
func CheckCode(ctx context.Context, req *bot_exec_pycode_server.CheckCodeRequest) (
	*bot_exec_pycode_server.CheckCodeResponse, error) {
	var err error
	resp, err := proxy.GetCodeExecutor().Check(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CheckCode|%v", err.Error())
		return nil, err
	}
	log.InfoContextf(ctx, "CheckCode|RESP|%v", resp)
	return resp, nil
}

//// RunCode 执行代码
//func RunCode(ctx context.Context, req *bot_exec_pycode_server.RunCodeRequest) (
//	*bot_exec_pycode_server.RunCodeResponse, error) {
//	log.Infof("%s|run code:%s,param:%s", util.RequestID(ctx), req.FuncCode, req.FuncArgs)
//	var err error
//	resp, err := proxy.GetCodeExecutor().Run(ctx, req)
//	if err != nil {
//		log.ErrorContextf(ctx, "RunCode|%v", err.Error())
//		return nil, err
//	}
//	log.InfoContextf(ctx, "RunCode|RESP|%v", resp)
//	return resp, nil
//}

// RunCode 执行代码
func RunCode(ctx context.Context, appID, codeContent, arguments string) (string, error) {
	if arguments == "" {
		arguments = "{}"
	}
	_, finalCode := ReplacePlaceholders(config.GetMainConfig().Workflow.RunCode.CodeTemplate, map[string]any{
		"CodeContent":      codeContent,
		"InputParamObject": strings.ReplaceAll(arguments, "\\", "\\\\"),
		"ResultSeparator":  config.GetMainConfig().Workflow.RunCode.ResultSeparator,
	})

	req := &interpreter.ExecuteReq{
		SessionID:      appID,
		Code:           finalCode,
		Language:       "python",
		MaxErrorLen:    config.GetMainConfig().Workflow.RunCode.MaxResultLength,
		NoNeedContext:  true,
		NoNeedInitCode: true,
	}
	dispatchResponse, err := proxy.GetCodeExecProxy().Execute(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "invokeRunCode failed, err:  %v", err)
		return "", err
	}

	if !dispatchResponse.Success && len(dispatchResponse.Message) > 0 {
		log.ErrorContextf(ctx, "getCodeInterpreterRsp dispatchResponse:  %v", dispatchResponse)
		return "", fmt.Errorf("system error")
	}

	var execResult, execMsg string
	// 结果解析
	isFail := false
	for _, output := range dispatchResponse.Outputs {
		if isFail {
			continue
		}
		switch output.Type {
		case ErrResultType:
			isFail = true
			execMsg += output.Content
		case TextResultType:
			execResult += output.Content
		}
	}
	if isFail {
		log.WarnContextf(ctx, "dispatchResponse.Outputs has failed message:  %v", execMsg)
		return "", fmt.Errorf("fail message: %v", execMsg)
	}

	parts := strings.Split(execResult, config.GetMainConfig().Workflow.RunCode.ResultSeparator)
	if len(parts) != 2 {
		log.ErrorContextf(ctx, "invalid code result: %v", execResult)
		return "", fmt.Errorf("system error")
	}
	return parts[1], nil
}
