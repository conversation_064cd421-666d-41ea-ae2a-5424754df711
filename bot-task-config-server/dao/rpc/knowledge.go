// bot-task-config-server
//
// @(#)knowledge.go  星期二, 十月 15, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

package rpc

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
)

// BatchQueryDocuments 批量查询文档（单知识库）
func BatchQueryDocuments(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (
	*bot_knowledge_config_server.InnerDescribeDocsRsp, error) {
	var err error
	log.InfoContextf(ctx, "BatchQueryDocuments req:%+v", req)
	resp, err := proxy.GetKnowledgeProxy().InnerDescribeDocs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "BatchQueryDocuments Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "BatchQueryDocuments Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}
