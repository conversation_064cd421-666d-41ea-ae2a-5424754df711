package rpc

import (
	"context"
	"fmt"
	"io"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	llm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	sec "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	secpkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// 计费上报类型
const (
	// FinanceSubBizTypeTaskFlowPreview 任务流程话术预览
	FinanceSubBizTypeTaskFlowPreview = "TaskFlowPreview"
)

// TaskFlowNodeModePreview 大模型效果预览
func TaskFlowNodeModePreview(ctx context.Context, req *llm.Request) (
	*llm.Response, error) {
	var err error
	resp, err := proxy.GetLLMProxy().SimpleChat(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "LLMSimpleChat getLLMManagerObjClientProxy error|%v|resp|%v", err.Error(), resp)
		return nil, err
	}
	log.InfoContextf(ctx, "LLMSimpleChat getLLMManagerObjClientProxy success |RESP|%v", resp)
	return resp, nil
}

// DocumentPreview 引用知识文档效果预览
func DocumentPreview(ctx context.Context, req *chat.GetAnswerFromDocsRequest) (
	*chat.GetAnswerFromDocsReply, error) {
	log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview req|%v", req)
	var err error
	response := new(chat.GetAnswerFromDocsReply)
	cli, err := proxy.GetQBotStreamChatProxy().GetAnswerFromDocs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetChatStreamProxy DocumentPreview error|%v|resp|%v", err.Error(), cli)
		return nil, err
	}
	log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview success |RESP|%v", cli)

	defer panicprinter.PrintPanic()
	defer func() { _ = cli.CloseSend() }()
	duration := config.GetMainConfig().LLM.DocTimeout // 设置循环超时时间

	startTime := time.Now() // 记录循环开始时间
	log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview for startTime", time.Since(startTime))
	for {
		rsp, err := cli.Recv()
		log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview success |RESP|%v|err|%v", rsp, err)
		//if (err == nil || err == io.EOF) && (rsp != nil && rsp.Finished) && rsp.Code == 0 {
		//	response = rsp
		//	log.InfoContextf(ctx, "GetBotDmProxy DocumentPreview success |RESP|%v", cli)
		//	return response, err
		//}
		if err == nil && rsp != nil && rsp.Code == 0 {
			response = rsp
		}
		if (rsp != nil && rsp.Finished) || err == io.EOF {
			return response, err
		}
		if err != nil {
			log.ErrorContextf(ctx, "GetChatStreamProxy DocumentPreview Read model response error|%v",
				err.Error(), rsp)
			return response, err
		}

		// 检查是否超过15秒
		elapsedTime := time.Since(startTime)
		if elapsedTime >= duration {
			log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview timeout")
			break // 超过15秒，退出循环
		}
	}
	endTime := time.Now() // 记录循环结束时间
	log.InfoContextf(ctx, "GetChatStreamProxy DocumentPreview for endTime", time.Since(endTime))
	return response, nil
}

// GetDescribeAccountStatus 获取账户状态 账户状态 0可用, 1不可用
func GetDescribeAccountStatus(ctx context.Context, appInfo *pb.GetAppInfoRsp) uint32 {
	log.InfoContextf(ctx, "GetDescribeAccountStatus appInfo start%+v", appInfo)
	if config.GetMainConfig().Finance.Disabled {
		log.InfoContextf(ctx, "GetDescribeAccountStatus is disabled")
		return 0
	}
	uin := util.Uin(ctx)
	sid := util.SID(ctx)
	modelName := entity.GetAppModelName(appInfo)
	if IsCustomModel(ctx, modelName) {
		log.InfoContextf(ctx, "GetDescribeAccountStatus model: %s is custom model, status return 0", modelName)
		return 0
	}
	req := &finance.DescribeAccountStatusReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: FinanceSubBizTypeTaskFlowPreview},
		Account: &finance.Account{
			Sid: finance.SID(sid),
			Uin: uin,
		},
		ModelName: modelName,
	}
	log.InfoContextf(ctx, "GetDescribeAccountStatus req:%+v", req)
	rsp, err := proxy.GetQBotFinanceProxy().DescribeAccountStatus(ctx, req)
	log.InfoContextf(ctx, "GetDescribeAccountStatus rsp %v, ERR: %v", rsp, err)
	if err != nil {
		log.ErrorContextf(ctx, "GetDescribeAccountStatus error: %+v", err)
		return 0
	}
	return rsp.GetStatus()
}

//// GetModelToken 获取token余量
//func GetModelToken(ctx context.Context, modelName string) int {
//	uin := util.Uin(ctx)
//	req := &finance.DescribeAccountInfoReq{
//		Biz:       &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: "KnowledgeQA"},
//		Uin:       fmt.Sprintf("%d", uint64(encode.StringToInt64(uin))),
//		ModelName: []string{modelName},
//	}
//	log.InfoContextf(ctx, "GetModelToken req uin: %d|modelName:%s|req:%+v", uin, modelName, req)
//	rsp, err := proxy.GetQBotFinanceProxy().DescribeAccountInfo(ctx, req)
//	log.InfoContextf(ctx, "GetModelToken rsp %v, ERR: %v", rsp, err)
//	if err != nil {
//		log.ErrorContextf(ctx, "GetModelToken error: %+v", err)
//		return -1
//	}
//	if rsp != nil && len(rsp.GetList()) > 0 {
//		return int(rsp.List[0].GetBalance())
//	}
//	return 0
//}

// ReportTokenDosage 上报用量 reprot为1时才上报输入输出
func ReportTokenDosage(ctx context.Context, appInfo *pb.GetAppInfoRsp, startTime time.Time,
	endTime time.Time, inputDosage float64, outputDosage float64, reportType int) error {
	if config.GetMainConfig().Finance.Disabled {
		log.InfoContextf(ctx, "ReportTokenDosage is disabled")
		return nil
	}
	modelName := entity.GetAppModelName(appInfo)
	if IsCustomModel(ctx, modelName) {
		log.InfoContextf(ctx, "ReportTokenDosage model: %s is custom model, status return 0", modelName)
		return nil
	}
	uin := util.Uin(ctx)
	sid := util.SID(ctx)
	req := &finance.ReportDosageReq{
		Biz:       &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: FinanceSubBizTypeTaskFlowPreview},
		Account:   &finance.Account{Sid: finance.SID(sid), Uin: uin},
		ModelName: modelName,
		DosageId:  util.RequestID(ctx),
		Payload:   fmt.Sprintf(`{"AppID":"%v","AppType":"%v"}`, appInfo.GetAppBizId(), appInfo.GetAppType()),
	}
	if !startTime.IsZero() {
		req.StartTime = uint64(startTime.Unix())
	}
	if !endTime.IsZero() {
		req.EndTime = uint64(endTime.Unix())
	}
	if reportType == 1 {
		req.List = append(req.List, &finance.ReportDosageReq_Detail{
			Dosage:  outputDosage,
			Payload: `{"type":"output"}`,
		})
	}
	req.List = append(req.List, &finance.ReportDosageReq_Detail{
		Dosage:  inputDosage,
		Payload: `{"type":"input"}`,
	})
	log.InfoContextf(ctx, "ReportTokenDosage req:%+v", req)
	rsp, err := proxy.GetQBotFinanceProxy().ReportDosage(ctx, req)
	log.InfoContextf(ctx, "ReportTokenDosage rsp |%v, ERR: |%v", rsp, err)
	if err != nil {
		log.ErrorContextf(ctx, "ReportTokenDosage error: %+v", err)
		return err
	}
	return nil
}

// Check 审核
func Check(ctx context.Context, content string, bizType string) (bool, error) {
	req := &sec.CheckReq{
		User: &sec.CheckReq_User{
			Uin:         util.Uin(ctx),
			AccountType: uint32(secpkg.AccountTypeOther),
		},
		Id:       util.RequestID(ctx),
		Source:   "chat",
		PostTime: time.Now().Unix(),
		Type:     uint32(secpkg.CheckTypeText),
		Content:  content,
		BizType:  bizType,
	}
	rsp, err := sec.NewInfosecClientProxy().Check(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Check Info Content fail, req: %+v, err: %v", req, err)
		return true, err
	}
	if rsp.GetResultCode() == secpkg.ResultEvil {
		log.InfoContextf(ctx, "Check Info Content evil, req: %+v, rsp: %+v", req, rsp)
		return true, errors.ErrTaskFlowNodePreviewIsEvil
	}
	log.InfoContextf(ctx, "Check Info Content evil success req: %+v |rsp|%v", req, rsp)
	return false, nil
}
