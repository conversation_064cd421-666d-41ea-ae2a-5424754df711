package rpc

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// ListPlugins 获取插件详情
func ListPlugins(ctx context.Context, req *pb.ListPluginsReq) (*pb.ListPluginsRsp, error) {
	sid := util.RequestID(ctx)
	var err error
	resp, err := proxy.GetPluginConfigProxy().ListPlugins(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListPlugins Failed|sid:%s|req:%+v|err:%+v", sid, req, err)
		return nil, err
	}
	log.InfoContextf(ctx, "ListPlugins Success|sid:%s|resp:%v", sid, resp)
	return resp, nil
}

// ListTools 获取工具详情
func ListTools(ctx context.Context, req *pb.ListToolsReq) (*pb.ListToolsRsp, error) {
	sid := util.RequestID(ctx)
	var err error
	resp, err := proxy.GetPluginConfigProxy().ListTools(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListTools Failed|sid:%s|req:%+v|err:%+v", sid, req, err)
		return nil, err
	}
	log.InfoContextf(ctx, "ListTools Success|sid:%s|resp:%v", sid, resp)
	return resp, nil
}
