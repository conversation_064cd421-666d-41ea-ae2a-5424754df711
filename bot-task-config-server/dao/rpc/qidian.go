package rpc

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	robot "git.woa.com/ivy/protobuf/trpc-go/customer_service/robot/robot_config"
)

// GetQdAccountWorkflowLimit 获取企点账户的任务流数量限制
func GetQdAccountWorkflowLimit(ctx context.Context, uin, robotID string) (int64, error) {
	Kfuin, _ := strconv.ParseInt(uin, 10, 64)
	req := &robot.RobotListV2Req{
		Kfuin:  uint32(Kfuin),
		Filter: fmt.Sprintf("sku.frobotactiveflg eq 1 and frobotchatgpttype gt 0 and frobotcappkey eq %s", robotID),
	}
	log.InfoContextf(ctx, "GetQdAccountWorkflowLimit req:%v", req)
	rsp, err := proxy.GetQidianProxy().GetRobotListV2(ctx, req)
	if err != nil || len(rsp.GetRobotInfo()) == 0 {
		log.ErrorContextf(ctx, "GetQdAccountWorkflowLimit GetRobotListV2 req:%v, err:%v", req, err)
		return -1, err
	}
	log.InfoContextf(ctx, "GetQdAccountWorkflowLimit rsp:%v", rsp)
	robotInfo := rsp.GetRobotInfo()[0]
	limit, ok := robotInfo.GetMeta().GetGptComboInfo()["workflowLimit"]
	if !ok {
		log.WarnContextf(ctx, "GetWorkflowLimit is empty")
		return -1, nil
	}
	if limit == "" {
		return -1, nil
	}
	num, err := strconv.ParseInt(limit, 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "GetQdAccountWorkflowLimit ParseInt limit:%s, err:%v", limit, err)
		return -1, err
	}
	return num, nil
}
