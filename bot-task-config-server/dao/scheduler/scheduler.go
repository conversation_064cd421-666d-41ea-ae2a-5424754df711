// Package scheduler ...
package scheduler

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
)

var (
	// taskScheduler 调度器
	taskScheduler *task_scheduler.TaskScheduler
)

// RunTask 定时任务执行
func RunTask() error {
	runner, err := os.Hostname()
	if err != nil {
		return err
	}

	taskScheduler = task_scheduler.New(
		runner,
		task_scheduler.ScheduleConfig{
			FetchNum:     config.GetMainConfig().TaskFetchNum,
			FetchTimeout: config.GetMainConfig().TaskFetchTimeout,
			FetchPeriod:  config.GetMainConfig().TaskFetchPeriod,
		},
		task_scheduler.NewDataAccessor(
			database.GetRedis(), config.GetMainConfig().TaskPrefix,
			database.GetLLMRobotTaskDB(), "t_bot_task"),
		task_scheduler.WithTaskConfigFn(func(typ task_scheduler.TaskType) task_scheduler.TaskConfig {
			conf, ok := config.GetMainConfig().Tasks[entity.TaskTypeNameMap[typ]]
			if !ok {
				return task_scheduler.DefaultTaskConfig
			}
			return task_scheduler.TaskConfig{
				Runners:           conf.Runners,
				RetryWaitTime:     conf.RetryWaitTime,
				MaxRetry:          conf.MaxRetry,
				Timeout:           conf.Timeout,
				FailTimeout:       conf.FailTimeout,
				Delay:             conf.Delay,
				Batch:             conf.Batch,
				BatchSize:         conf.BatchSize,
				StoppedResumeTime: conf.StoppedResumeTime,
			}
		}),
	)
	log.Info("start task scheduler")
	return taskScheduler.Run()
}

// NewExportTaskFlowTask 创建导出任务流任务
func NewExportTaskFlowTask(ctx context.Context, robotID string, params entity.TaskFlowExportParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskExportTaskFlow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskExportTaskFlow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportTaskFlowParentTask 创建导入任务流程父任务
func NewImportTaskFlowParentTask(ctx context.Context, robotID string,
	params entity.TaskFlowImportParentParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskImportTaskFlowParent]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportTaskFlowParent,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportTaskFlowTask 创建导入任务流程子任务
func NewImportTaskFlowTask(ctx context.Context, robotID string, params entity.TaskFlowImportParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskImportTaskFlow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportTaskFlow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportEntryTask 创建导入词条任务
func NewImportEntryTask(ctx context.Context, robotID string, params entity.TaskFlowImportParentParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskImportEntryFlow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportEntryFlow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewWFImportEntryTask 工作流-创建导入词条任务
func NewWFImportEntryTask(ctx context.Context, robotID string, params entity.TaskFlowImportParentParams) error {
	params.Name = entity.TaskTypeNameMap[entity.WFTaskImportEntryFlow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.WFTaskImportEntryFlow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.NewWFImportEntryTask failed! ,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// GetBotAvailableTasks ...
//
//	@Description: 获取正在生效的调度任务
//	@param ctx
//	@param updateBeforeTime        更新时间点
//	@return []*entity.SchedulerTask
//	@return error
func GetBotAvailableTasks(ctx context.Context, updateBeforeTime time.Time) ([]*entity.SchedulerTask, error) {
	// task_scheduler 查询正在生效的任务，即查询t_bot_task表未达到最大重试次数的任务中的数据

	log.InfoContextf(ctx, "GetBotAvailableTasks, updateBeforeTime:%s", updateBeforeTime.String())
	tasks := make([]*entity.SchedulerTask, 0)
	if updateBeforeTime.IsZero() {
		return tasks, nil
	}
	// 查询DB
	query := fmt.Sprintf("%s != %s AND %s < ?",
		entity.TSchedulerTaskColumns.RetryTimes, entity.TSchedulerTaskColumns.MaxRetryTimes,
		entity.TSchedulerTaskColumns.UpdateTime)
	err := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName()).
		Where(query, updateBeforeTime).
		Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetBotAvailableTasks db.Find err:%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetBotAvailableTasks success, len(tasks):%d", len(tasks))
	return tasks, nil
}

// NewFlowDeleteTask 创建工作流删除任务
func NewFlowDeleteTask(ctx context.Context, params entity.FlowDeleteParams) error {
	params.Name = entity.TaskTypeNameMap[entity.FlowDeleteTask]
	// 后台任务, userID取corpID, 以便并行处理
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(params.RobotID), entity.FlowDeleteTask, entity.TaskMutexNone, params,
	)
	log.InfoContextf(ctx, "newFlowDeleteTask task: %+v", t)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "newFlowDeleteTask fail, task: %+v, err: %v", t, err)
		return err
	}
	return nil
}
