package scheduler

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
)

// NewExportWorkflowTask 创建导出工作流任务
func NewExportWorkflowTask(ctx context.Context, robotID string, params entity.WorkflowExportParams) error {
	params.Name = entity.WorkflowTaskTypeNameMap[entity.TaskExportWorkflow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskExportWorkflow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportWorkflowParentTask 创建导入工作流程父任务
func NewImportWorkflowParentTask(ctx context.Context, robotID string,
	params entity.WorkflowImportParentParams) error {
	params.Name = entity.WorkflowTaskTypeNameMap[entity.TaskImportWorkflowParent]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportWorkflowParent,
		entity.TaskMutexWorkflowNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportWorkflowTask 创建导入任务流程子任务
func NewImportWorkflowTask(ctx context.Context, robotID string, params entity.WorkflowImportParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskImportWorkflow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportWorkflow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewConvertWorkflowToPDLTask 创建转换PDL工作流任务
func NewConvertWorkflowToPDLTask(ctx context.Context, robotID string, params entity.TaskConvertWorkflowToPDLParams) error {
	//params.Name = entity.TaskTypeNameMap[entity.TaskConvertWorkflowToPDL]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskConvertWorkflowToPDL,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportExperienceWorkflowTask 创建导入体验中心工作流任务
func NewImportExperienceWorkflowTask(ctx context.Context, robotID string,
	params entity.TaskCopyExperienceWorkflowParams) error {
	params.Name = entity.WorkflowTaskTypeNameMap[entity.TaskImportExperienceWorkflow]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportExperienceWorkflow,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask NewImportExperienceWorkflowTask "+
			"failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}

// NewImportWfExampleTask 创建工作流导入任务
func NewImportWfExampleTask(ctx context.Context, robotID string, params entity.WorkflowImportParentParams) error {
	params.Name = entity.TaskTypeNameMap[entity.TaskImportWfExample]
	t, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(robotID))), entity.TaskImportWfExample,
		entity.TaskMutexNone, params,
	)
	if _, err := taskScheduler.CreateTask(ctx, t); err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask NewImportWfExampleTask failed,t:%+v,err:%+v", t, err)
		return err
	}
	return nil
}
