package storage

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"net/http"
	"strings"
	"time"

	sts "github.com/tencentyun/qcloud-cos-sts-sdk/go"

	"git.code.oa.com/trpc-go/trpc-database/cos"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type cosClient struct {
	cfg         config.Cos
	cosCli      cos.Client
	innerCosCli cos.Client
}

// newCOS creates cos instance
func newCOS() *cosClient {
	cfg := config.GetMainConfig().Storage.Cos
	return &cosClient{
		cfg: cfg,
		cosCli: cos.New("trpc.http.cos.Access", cos.Conf{
			Bucket:    strings.TrimRight(cfg.Bucket, "-"+cfg.AppID),
			AppID:     cfg.AppID,
			SecretID:  cfg.SecretID,
			SecretKey: cfg.<PERSON><PERSON>ey,
			Region:    cfg.Region,
			Domain:    cfg.Domain,
		}),
		innerCosCli: cos.New("trpc.http.cos.Access", cos.Conf{
			Bucket:    strings.TrimRight(cfg.Bucket, "-"+cfg.AppID),
			AppID:     cfg.AppID,
			SecretID:  cfg.SecretID,
			SecretKey: cfg.SecretKey,
			Region:    cfg.Region,
			Domain:    cfg.Domain,
			Prefix:    "cos-internal",
		}),
	}
}

// GetDomain 获取domain
func (c *cosClient) GetDomain(ctx context.Context) string {
	return c.cfg.Domain
}

// GetType 获取对象存储类型
func (c *cosClient) GetType(ctx context.Context) string {
	return StorageTypeCOS
}

// GetBucket 获取存储桶
func (c *cosClient) GetBucket(ctx context.Context) string {
	return c.cfg.Bucket
}

// GetRegion 获取存储桶地域
func (c *cosClient) GetRegion(ctx context.Context) string {
	return c.cfg.Region
}

// GetCredential 获取临时密钥
func (c *cosClient) GetCredential(ctx context.Context, pathList []string) (*CredentialResult, error) {
	resource := []string{
		"qcs::cos:" + c.cfg.Region + ":uid/" + c.cfg.AppID + ":" + c.cfg.Bucket + SharePath + "*",
		"qcs::cos:" + c.cfg.Region + ":uid/" + c.cfg.AppID + ":" + c.cfg.Bucket + PublicPath + "*",
	}
	for _, path := range pathList {
		// e.g. qcs::cos:{region}:uid/{appid}:{bucket}/{path}/*
		resource = append(resource, "qcs::cos:"+c.cfg.Region+":uid/"+c.cfg.AppID+":"+c.cfg.Bucket+path+"*")
	}
	opt := &sts.CredentialOptions{
		DurationSeconds: int64(time.Hour.Seconds()),
		Region:          c.cfg.Region,
		Policy: &sts.CredentialPolicy{Statement: []sts.CredentialPolicyStatement{{
			Action:   COSUpAndDownload,
			Effect:   "allow",
			Resource: resource,
		}}},
	}

	r, err := sts.NewClient(c.cfg.SecretID, c.cfg.SecretKey, nil).GetCredential(opt)
	if err != nil {
		log.ErrorContextf(ctx, "Get cos credential error: %+v, opt: %+v", err, opt)
		return nil, err
	}

	return &CredentialResult{
		Credentials: &Credentials{
			TmpSecretID:  r.Credentials.TmpSecretID,
			TmpSecretKey: r.Credentials.TmpSecretKey,
			SessionToken: r.Credentials.SessionToken,
		},
		ExpiredTime: int64(r.ExpiredTime),
		StartTime:   int64(r.StartTime),
	}, nil
}

// GetPreSignedURL 获取 COS 预签名 URL
func (c *cosClient) GetPreSignedURL(ctx context.Context, key string) (string, error) {
	url, err := c.cosCli.GetPreSignedURL(ctx, key, http.MethodGet, c.cfg.ExpireTime)
	if err != nil {
		log.ErrorContextf(ctx, "获取 COS 预签名 URL 失败 key: %s err: %+v", key, err)
		return "", err
	}
	return url, nil
}

// GetObject 获取 COS 文件
func (c *cosClient) GetObject(ctx context.Context, key string) ([]byte, error) {
	object, err := c.innerCosCli.GetObject(ctx, key)
	if err != nil {
		log.ErrorContextf(ctx, "获取 COS 文件失败 key: %s err: %+v", key, err)
		return nil, err
	}
	return object, nil
}

// PutObject 上传 COS 文件
func (c *cosClient) PutObject(ctx context.Context, bs []byte, key string) error {
	if _, err := c.innerCosCli.PutObject(ctx, bs, key); err != nil {
		log.ErrorContextf(ctx, "上传 COS 文件失败 key: %s, len: %d, err: %+v", key, len(bs), err)
		return err
	}
	return nil
}

// DelObject 删除 COS 文件
func (c *cosClient) DelObject(ctx context.Context, key string) error {
	if err := c.innerCosCli.DelObject(ctx, key); err != nil {
		log.ErrorContextf(ctx, "删除 COS 文件失败 key: %s, err: %+v", key, err)
		return err
	}
	return nil
}
