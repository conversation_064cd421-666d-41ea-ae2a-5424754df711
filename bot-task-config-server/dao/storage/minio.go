package storage

import (
	"bytes"
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"io"
	"net/url"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"

	"git.code.oa.com/trpc-go/trpc-go/log"
	//"git.woa.com/ivy/qbot/qbot/admin/internal/model"
)

type minioClient struct {
	cfg      config.MinIO
	minioCli *minio.Client
}

// newMinIO create minio instance
func newMinIO() *minioClient {
	cfg := config.GetMainConfig().Storage.MinIO
	cli, err := minio.New(cfg.EndPoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.SecretID, cfg.SecretKey, ""),
		Secure: cfg.UseHTTPS,
		Region: cfg.Region,
	})
	if err != nil {
		log.Fatalf("create minio client cfg:%+v err:%+v", cfg, err)
	}
	return &minioClient{
		cfg:      cfg,
		minioCli: cli,
	}
}

// GetDomain 获取domain
func (m *minioClient) GetDomain(ctx context.Context) string {
	return m.cfg.STSEndpoint
}

// GetType 获取对象存储类型
func (m *minioClient) GetType(ctx context.Context) string {
	return StorageTypeMinIO
}

// GetBucket 获取存储桶
func (m *minioClient) GetBucket(ctx context.Context) string {
	return m.cfg.Bucket
}

// GetRegion 获取存储桶地域
func (m *minioClient) GetRegion(ctx context.Context) string {
	return m.cfg.Region
}

// GetCredential 获取临时密钥
func (m *minioClient) GetCredential(ctx context.Context, pathList []string) (
	*CredentialResult, error) {
	duration := time.Hour
	expireTime := time.Now().Add(duration)
	resource := []string{
		"arn:aws:s3:::" + m.cfg.Bucket + SharePath + "*",
		"arn:aws:s3:::" + m.cfg.Bucket + PublicPath + "*",
	}
	for _, path := range pathList {
		resource = append(resource, "arn:aws:s3:::"+m.cfg.Bucket+path+"*")
	}
	credentialPolicy := CredentialPolicy{
		Version: MinIOVersionID,
		Statement: []CredentialPolicyStatement{
			{
				Action:   MinIOUpAndDownload,
				Effect:   "Allow",
				Resource: resource,
			},
		},
	}
	policy, err := jsoniter.MarshalToString(credentialPolicy)
	if err != nil {
		log.ErrorContextf(ctx, "marshal minio policy err:%+v", err)
		return nil, err
	}
	res, err := credentials.NewSTSAssumeRole(m.cfg.STSEndpoint, credentials.STSAssumeRoleOptions{
		AccessKey:       m.cfg.SecretID,
		SecretKey:       m.cfg.SecretKey,
		Policy:          policy,
		Location:        m.cfg.Region,
		DurationSeconds: int(duration.Seconds()),
	})
	if err != nil {
		log.ErrorContextf(ctx, "get minio credential err:%+v", err)
		return nil, err
	}
	cre, err := res.Get()
	if err != nil {
		log.ErrorContextf(ctx, "Error retrieving STS credentials err:%+v", err)
		return nil, err
	}
	return &CredentialResult{
		Credentials: &Credentials{
			TmpSecretID:  cre.AccessKeyID,
			TmpSecretKey: cre.SecretAccessKey,
			SessionToken: cre.SessionToken,
		},
		ExpiredTime: expireTime.Unix(),
		StartTime:   time.Now().Add(time.Second * 10).Unix(),
	}, nil
}

// GetPreSignedURL 获取 COS 预签名 URL
func (m *minioClient) GetPreSignedURL(ctx context.Context, key string) (string, error) {
	reqParams := make(url.Values)
	preSignedURL, err := m.minioCli.PresignedGetObject(ctx, m.cfg.Bucket, key, m.cfg.ExpireTime, reqParams)
	if err != nil {
		log.ErrorContextf(ctx, "获取 MinIO 预签名 URL 失败 key: %s err: %+v", key, err)
		return "", err
	}
	return preSignedURL.String(), nil
}

// GetObject 获取 COS 文件
func (m *minioClient) GetObject(ctx context.Context, key string) ([]byte, error) {
	object, err := m.minioCli.GetObject(ctx, m.cfg.Bucket, key, minio.GetObjectOptions{})
	if err != nil {
		log.ErrorContextf(ctx, "获取 MinIO 文件失败 key: %s err: %+v", key, err)
		return nil, err
	}
	defer func() { _ = object.Close() }()
	stat, err := object.Stat()
	if err != nil {
		log.ErrorContextf(ctx, "获取 MinIO 文件统计信息失败 key: %s err: %+v", key, err)
		return nil, err
	}
	content := make([]byte, stat.Size)
	if _, err = object.Read(content); err != io.EOF && err != nil {
		log.ErrorContextf(ctx, "读取 MinIO 文件内容失败 key: %s err: %+v", key, err)
		return nil, err
	}
	return content, nil
}

// PutObject 上传 COS 文件
func (m *minioClient) PutObject(ctx context.Context, bs []byte, key string) error {
	reader := bytes.NewReader(bs)
	putOptions := minio.PutObjectOptions{}
	if _, err := m.minioCli.PutObject(ctx, m.cfg.Bucket, key, reader, reader.Size(), putOptions); err != nil {
		log.ErrorContextf(ctx, "上传 MinIO 文件失败 key: %s, len: %d, err: %+v", key, len(bs), err)
		return err
	}
	return nil
}

// DelObject 删除 COS 文件
func (m *minioClient) DelObject(ctx context.Context, key string) error {
	removeOptions := minio.RemoveObjectOptions{}
	if err := m.minioCli.RemoveObject(ctx, m.cfg.Bucket, key, removeOptions); err != nil {
		log.ErrorContextf(ctx, "删除 MinIO 文件失败 key: %s, err: %+v", key, err)
		return err
	}
	return nil
}
