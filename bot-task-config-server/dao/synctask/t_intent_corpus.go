// bot-task-config-server
//
// @(#)t_intent_corpus.go  星期二, 四月 09, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package synctask

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"time"
)

// GetIntentCorpusByTaskflows 通过任务流程获取示例问法
func GetIntentCorpusByTaskflows(ctx context.Context, taskFlows *[]entity.TaskFlow,
	timeStamp string, publishTime time.Time) (*[]entity.IntentCorpusPublishHistory, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	var sid = util.RequestID(ctx)

	if *taskFlows == nil {
		return nil, nil
	}
	var intentIds []string
	var intentCorpus []entity.IntentCorpus
	var historyIntentCorpus []entity.IntentCorpusPublishHistory
	IntentProtoVersionMap := make(map[string]string)
	IntentFlowIdMap := make(map[string]string)

	log.InfoContextf(ctx, "sid:%s|GetIntentCorpusByTaskflows|taskFlows:%+v", sid, *taskFlows)
	for _, v := range *taskFlows {
		intentIds = append(intentIds, v.IntentID)
		IntentProtoVersionMap[v.IntentID] = fmt.Sprintf("%d", v.ProtoVersion)
		IntentFlowIdMap[v.IntentID] = v.FlowID
	}
	if err := db.Table(entity.IntentCorpus{}.TableName()).
		Where("f_is_deleted=0 AND f_intent_id IN ?",
			intentIds).Scan(&intentCorpus).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetIntentCorpusByTaskflows|"+
			"intentIds:%+v|err:%+v", util.RequestID(ctx), intentIds, err)
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|intentIds:%+v|IPVersionMap:%+v|iFlowIdMap:%+v|intentCorpus:%+v",
		sid, intentIds, IntentProtoVersionMap, IntentFlowIdMap, intentCorpus)
	for _, ic := range intentCorpus {
		version := config.GetMainConfig().VerifyTaskFlow.Version + "-" +
			IntentProtoVersionMap[ic.IntentID] + "-" + timeStamp
		historyIntentCorpus = append(historyIntentCorpus, entity.IntentCorpusPublishHistory{
			FlowID:      IntentFlowIdMap[ic.IntentID],
			IntentID:    ic.IntentID,
			CorpusID:    ic.CorpusID,
			Corpus:      ic.Corpus,
			Version:     version,
			SaveType:    entity.EnumSaveType[entity.SavePublished],
			Uin:         ic.Uin,
			SubUin:      ic.SubUin,
			PublishTime: publishTime,
		})
	}

	return &historyIntentCorpus, nil
}

// RecordSyncIntentCorpusRelease 保存历史画布关联的任务流程
func RecordSyncIntentCorpusRelease(ctx context.Context, ic *[]entity.IntentCorpusPublishHistory) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)

	log.Infof("I|RecordSyncIntentCorpusRelease|%s|%+v", sid, ic)
	g := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	g = g.Table(entity.IntentCorpusPublishHistory{}.TableName()).Create(ic)
	log.Infof("O|RecordSyncIntentCorpusRelease|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}
