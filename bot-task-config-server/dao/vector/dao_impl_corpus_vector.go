// Package vector 向量数据库相关
// @Author: halelv
// @Date: 2023/12/25 11:34
package vector

import (
	"context"
	"fmt"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	tdb "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
)

const (
	// SandboxGroupInfix 沙箱环境向量库中缀
	SandboxGroupInfix = "sandbox"
	// ProdGroupInfix 正式环境向量库中缀
	ProdGroupInfix = "prod"

	FieldIntentID    = "IntentID"
	FieldCorpusID    = "CorpusID"
	FieldCorpusValue = "CorpusValue" // Corpus 原文值
)

// GetVectorGroupID get vector group ID
func GetVectorGroupID(robotID string) (string, string) {
	sandboxGroupID := fmt.Sprintf("%s-%s-%s",
		config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID)
	prodGroupID := fmt.Sprintf("%s-%s-%s",
		config.GetMainConfig().VectorGroup.Biz, ProdGroupInfix, robotID)
	return sandboxGroupID, prodGroupID
}

// getVectorOperationMaxIDs get vector operation max id length
func getVectorOperationMaxIDs() int {
	return config.GetMainConfig().VectorGroup.OperationMaxIDs
}

// CreateBotCorpusGroup ...
//
//	@Description: 创建机器人语料向量库
//	@receiver d
//	@param ctx
//	@param robotID          机器人ID
//	@return sandboxGroupID
//	@return prodGroupID
//	@return err
func (d dao) CreateBotCorpusGroup(ctx context.Context, robotID string, vectorInfo []entity.VectorGroup) (
	sandboxGroupID string, prodGroupID string, err error) {
	log.InfoContextf(ctx, "CreateBotCorpusGroup, robotID:%s", robotID)
	appInfo := getVectorAppInfo(robotID)
	requestID := util.RequestID(ctx)
	//sandboxGroupID, prodGroupID = GetVectorGroupID(robotID)
	sandboxGroupID, prodGroupID = getSandboxProdGroupID(vectorInfo)

	defer func() {
		if err != nil {
			_ = d.DeleteBotCorpusGroup(ctx, robotID)
		}
	}()

	reqSandbox := &vector_db_manager.CreateGroupReq{
		RequestId: requestID,
		GroupId:   sandboxGroupID,
		AppInfo:   appInfo,
		UseVdb:    config.GetMainConfig().VectorGroup.TaskFlowUseVdb,
	}
	log.InfoContextf(ctx, "CreateBotCorpusGroup CreateGroup reqSandbox:%s", reqSandbox.String())
	rspSandbox, err := d.client.CreateGroup(ctx, reqSandbox)
	if err != nil || rspSandbox == nil || (rspSandbox.Code != 0 && rspSandbox.Code != 71014) {
		err = fmt.Errorf("rspSandbox:%+v, err:%v", rspSandbox, err)
		log.ErrorContextf(ctx, "CreateBotCorpusGroup CreateGroup, err:%v", err)
		return sandboxGroupID, prodGroupID, err
	}

	reqProd := &vector_db_manager.CreateGroupReq{
		RequestId: requestID,
		GroupId:   prodGroupID,
		AppInfo:   appInfo,
		UseVdb:    config.GetMainConfig().VectorGroup.TaskFlowUseVdb, //老任务流程暂时不变 ,为false， V2.6.0 会上线vbd
	}
	log.InfoContextf(ctx, "CreateBotCorpusGroup CreateGroup reqProd:%s", reqProd.String())
	rspProd, err := d.client.CreateGroup(ctx, reqProd)
	if err != nil || rspProd == nil || (rspProd.Code != 0 && rspProd.Code != 71014) {
		err = fmt.Errorf("rspProd:%+v, err:%v", rspProd, err)
		log.ErrorContextf(ctx, "CreateBotCorpusGroup CreateGroup, err:%v", err)
		return sandboxGroupID, prodGroupID, err
	}

	log.InfoContextf(ctx, "CreateBotCorpusGroup success, sandboxGroupID:%s, prodGroupID:%s",
		sandboxGroupID, prodGroupID)
	return sandboxGroupID, prodGroupID, nil
}

// GetIntentVectorGroupId 获取意图向量GroupId，没有就创建
func (d dao) GetIntentVectorGroupId(ctx context.Context, tx *gorm.DB, robotID string) (string, string, error) {
	var err error
	sid := util.RequestID(ctx)

	// 从数据库中查找
	intentCorpusVectorInfo, err := getVectorInfoByRobotIdAndType(ctx, tx, robotID, entity.SaveIntentType)
	if err != nil {
		log.ErrorContextf(ctx, "getVectorInfoByRobotIdAndType Failed! sid:%s,err:%v", sid, err)
		return "", "", err
	}

	if len(intentCorpusVectorInfo) == 0 {
		intentCorpusVectorInfo = buildVectorInfo(ctx, robotID, entity.SaveIntentType)

		// 说明没在表t_vector_group里面创建，则创建intentVectorGroup
		if err := tx.Model(&entity.VectorGroup{}).CreateInBatches(intentCorpusVectorInfo, 20).Error; err != nil {
			log.ErrorContextf(ctx, "GetEntryVectorGroupID.CreateFailed!  sid:%s, err:%+v", sid, err)
			return "", "", err
		}
		log.InfoContextf(ctx, "CreateVectorGroup Success|sid:%s|VectorInfo:%+v", sid, intentCorpusVectorInfo)

		// 兼容历史创建 intentVectorGroup保存到 redis的数据，刷新到mysql库
		rkey := fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID)
		ok, err := tdb.HasSaveCorpusVectorGroupToRedis(ctx, rkey)
		if err != nil {
			log.ErrorContextf(ctx, "getVectorInfoByRobotIdAndType Failed|sid|%s,err:%v", sid, err)
			return "", "", err
		}
		// redis没有，说明没创建 VectorGroup
		if !ok {
			_, _, err = d.CreateBotCorpusGroup(ctx, robotID, intentCorpusVectorInfo)
			if err != nil {
				log.ErrorContextf(ctx, "GetIntentVectorGroupId|CreateBotCorpusGroup Failed,err:%v", err)
				return "", "", err
			}

			// 通知DM,只需要sandbox的部分
			req := &KEP_DM.UpsertRobotToSandboxRequest{
				RobotID:                robotID,
				RetrievalIntentGroupID: intentCorpusVectorInfo[0].VectorGroupID,
			}
			if _, err := rpc.UpsertRobotToSandbox(ctx, req); err != nil {
				log.ErrorContextf(ctx, "UpsertSlotsToSandbox Failed, sid:%s, err:%v", sid, err)
				return "", "", err
			}
		}
	}

	sandboxGroupID, prodGroupID := getSandboxProdGroupID(intentCorpusVectorInfo)
	return sandboxGroupID, prodGroupID, nil
}

// DeleteBotCorpusGroup ...
//
//	@Description: 删除机器人语料向量库
//	@receiver d
//	@param ctx
//	@param robotID 机器人ID
//	@return error
func (d dao) DeleteBotCorpusGroup(ctx context.Context, robotID string) error {
	log.InfoContextf(ctx, "DeleteBotCorpusGroup, robotID:%s", robotID)
	appInfo := getVectorAppInfo(robotID)
	requestID := util.RequestID(ctx)
	sandboxGroupID, prodGroupID, err := getVectorGroupSandboxAndProdIdFromDB(ctx,
		robotID, entity.SaveIntentType)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|DeleteBotCorpusGroup|getVectorGroupSandboxAndProdIdFromDB,"+
			"|err:%v", requestID, err)
		return err
	}

	reqSandbox := &vector_db_manager.DeleteGroupReq{
		RequestId: requestID,
		GroupId:   sandboxGroupID,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "DeleteBotCorpusGroup DeleteGroup reqSandbox:%s", reqSandbox.String())
	rspSandbox, err := d.client.DeleteGroup(ctx, reqSandbox)
	if err != nil || rspSandbox == nil || (rspSandbox.Code != 0 && rspSandbox.Code != 71014) {
		err = fmt.Errorf("rspSandbox:%+v, err:%v", rspSandbox, err)
		log.ErrorContextf(ctx, "DeleteBotCorpusGroup DeleteGroup, err:%v", err)
		return err
	}

	reqProd := &vector_db_manager.DeleteGroupReq{
		RequestId: requestID,
		GroupId:   prodGroupID,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "DeleteBotCorpusGroup CreateGroup reqProd:%s", reqProd.String())
	rspProd, err := d.client.DeleteGroup(ctx, reqProd)
	if err != nil || rspProd == nil || (rspProd.Code != 0 && rspProd.Code != 71014) {
		err = fmt.Errorf("rspProd:%+v, err:%v", rspProd, err)
		log.ErrorContextf(ctx, "DeleteBotCorpusGroup CreateGroup, err:%v", err)
		return err
	}

	log.InfoContextf(ctx, "DeleteBotCorpusGroup success, sandboxGroupID:%s, prodGroupID:%s",
		sandboxGroupID, prodGroupID)
	return nil
}

// PublishCorpusVector ...
//
//	@Description: 任务流语料向量发布
//	@receiver d
//	@param ctx
//	@param robotID 待发布机器人ID
//	@param corpora 待发布语料
//	@return string 语料Prod向量库GroupID
//	@return error
func (d dao) PublishCorpusVector(ctx context.Context, robotID string, corpora []*entity.Corpus) (string, error) {
	log.InfoContextf(ctx, "PublishCorpusVector, robotID:%s, len(corpora):%d", robotID, len(corpora))
	if len(robotID) == 0 || len(corpora) == 0 {
		return "", nil
	}
	corpusIDs := make([]string, 0)
	corpusIDMap := make(map[string]*entity.Corpus)
	for _, corpus := range corpora {
		corpusIDs = append(corpusIDs, corpus.CorpusID)
		corpusIDMap[corpus.CorpusID] = corpus
	}

	// 基本信息
	appInfo := getVectorAppInfo(robotID)
	requestID := util.RequestID(ctx)
	sandboxGroupID, prodGroupID, err := getVectorGroupSandboxAndProdIdFromDB(ctx,
		robotID, entity.SaveIntentType)
	if err != nil {
		log.ErrorContextf(ctx, "PublishCorpusVector|getVectorGroupSandboxAndProdIdFromDB err:%v", err)
		return "", err
	}

	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(corpusIDs, getVectorOperationMaxIDs()) {
		log.InfoContextf(ctx, "PublishCorpusVector, corpusIDs:%v", ids)
		// 根据action分类
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		corpusVectors, err := d.GetVectors(ctx, requestID, sandboxGroupID, appInfo, ids)
		if err != nil {
			return "", err
		}
		if len(corpusVectors) > 0 {
			for _, vector := range corpusVectors {
				corpus, ok := corpusIDMap[vector.Id]
				if !ok {
					log.WarnContextf(ctx, "PublishCorpusVector vectorID:%s not exits", vector.Id)
					continue
				}
				switch corpus.Action {
				case entity.ActionInsert:
					inserts = append(inserts, vector)
				case entity.ActionUpdate:
					updates = append(updates, vector)
				case entity.ActionDelete:
					deletes = append(deletes, vector.Id)
				default:
					log.WarnContextf(ctx, "PublishCorpusVector Corpus:%+v, Action:%s illegal",
						*corpus, corpus.Action)
				}
			}
		}
		// sandbox中删除的向量
		for _, id := range ids {
			corpus, ok := corpusIDMap[id]
			if !ok {
				log.WarnContextf(ctx, "PublishCorpusVector CorpusID:%s not exits", id)
				continue
			}
			if corpus.Action == entity.ActionDelete {
				deletes = append(deletes, corpus.CorpusID)
			}
		}

		err = d.vectorCallHandling(ctx, requestID, prodGroupID, appInfo, inserts, updates, deletes)
		if err != nil {
			return "", err
		}
		log.InfoContextf(ctx, "PublishCorpusVector published, len(inserts):%d, len(updates):%d, len(deletes):%d",
			len(inserts), len(updates), len(deletes))
	}
	log.InfoContextf(ctx, "PublishCorpusVector success, prodGroupID:%s", prodGroupID)
	return prodGroupID, nil
}

// PublishIntentExampleVector ...
//
//	@Description: 任务流示例问法向量发布
//	@receiver d
//	@param ctx
//	@param robotID 待发布机器人ID
//	@param examples 待发布示例问法
//	@return string 语料Prod向量库GroupID
//	@return error
func (d dao) PublishIntentExampleVector(ctx context.Context, robotID string,
	examples []*entity.IntentCorpus) (string, error) {
	sid := util.RequestID(ctx)
	if len(robotID) == 0 || len(examples) == 0 {
		return "", nil
	}
	corpusIDs := make([]string, 0)
	corpusIDMap := make(map[string]*entity.IntentCorpus)
	for _, example := range examples {
		corpusIDs = append(corpusIDs, example.CorpusID)
		corpusIDMap[example.CorpusID] = example
	}

	appInfo := getVectorAppInfo(robotID)
	sandboxGroupID, prodGroupID, err := getVectorGroupSandboxAndProdIdFromDB(ctx, robotID, entity.SaveIntentType)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|PublishIntentExampleVector|GroupID|err:%v", sid, err)
		return "", err
	}

	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(corpusIDs, getVectorOperationMaxIDs()) {
		log.InfoContextf(ctx, "sid:%s|PublishIntentExampleVector|corpusIDs:%v", sid, ids)
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0) // 根据action分类
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		corpusVectors, err := d.GetVectors(ctx, sid, sandboxGroupID, appInfo, ids)
		if err != nil {
			return "", err
		}
		if len(corpusVectors) > 0 {
			for _, vector := range corpusVectors {
				corpus, ok := corpusIDMap[vector.Id]
				if !ok {
					log.WarnContextf(ctx, "PublishExampleVector|vId:%s not exits", vector.Id)
					continue
				}
				switch corpus.Action {
				case entity.ActionInsert:
					inserts = append(inserts, vector)
				case entity.ActionUpdate:
					updates = append(updates, vector)
				case entity.ActionDelete:
					deletes = append(deletes, vector.Id)
				default:
					log.WarnContextf(ctx, "PublishIntentExampleVector Corpus:%+v, Action:%s illegal",
						*corpus, corpus.Action)
				}
			}
		}
		for _, id := range ids { // sandbox中删除的向量
			corpus, ok := corpusIDMap[id]
			if !ok {
				log.WarnContextf(ctx, "PublishIntentExampleVector CorpusID:%s not exits", corpus.CorpusID)
				continue
			}
			if corpus.Action == entity.ActionDelete {
				deletes = append(deletes, corpus.CorpusID)
			}
		}

		err = d.vectorCallHandling(ctx, sid, prodGroupID, appInfo, inserts, updates, deletes)
		if err != nil {
			return "", err
		}
		log.InfoContextf(ctx, "PublishIntentExampleVector published, len(inserts):%d, "+
			"len(updates):%d, len(deletes):%d", len(inserts), len(updates), len(deletes))
	}
	log.InfoContextf(ctx, "sid:%s|PublishIntentExampleVector success, prodGroupID:%s", sid, prodGroupID)
	return prodGroupID, nil
}

// GetVectors ...
//
//	@Description: 查询向量
//	@receiver d
//	@param ctx
//	@param requestID 请求ID
//	@param groupID   向量库GroupID
//	@param appInfo   向量数据库操作AppInfo
//	@param ids       待查询的向量ID列表
//	@return []*vector_db_manager.GetVectorRsp_Index
//	@return error
func (d dao) GetVectors(ctx context.Context, requestID, groupID string, appInfo *vector_db_manager.AppInfo,
	ids []string) ([]*vector_db_manager.GetVectorRsp_Index, error) {
	if len(ids) == 0 {
		log.InfoContextf(ctx, "GetVectors ids is empty, no need get")
		return []*vector_db_manager.GetVectorRsp_Index{}, nil
	}

	// 去重
	ids = types.Unique(ids)

	req := &vector_db_manager.GetVectorReq{
		RequestId: requestID + entity.ActionGet,
		GroupId:   groupID,
		Ids:       ids,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "GetVectors GetVector req.RequestId:%s", req.RequestId)
	log.DebugContextf(ctx, "GetVectors GetVector req:%s", req.String())
	rsp, err := d.client.GetVector(ctx, req)
	if err != nil || rsp == nil || rsp.Code != 0 {
		err = fmt.Errorf("rsp:%+v, err:%v", rsp, err)
		log.ErrorContextf(ctx, "GetVectors GetVector, err:%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "GetVectors GetVector rsp:%s", rsp.String())
	log.InfoContextf(ctx, "GetVectors GetVector rsp.RequestId:%s", rsp.RequestId)
	return rsp.DataList, nil
}

// covertGetToAdd convert get vector index to add vector index
func covertGetToAdd(getDataList []*vector_db_manager.GetVectorRsp_Index) (
	addDataList []*vector_db_manager.AddVectorReq_Index) {
	addDataIDMap := make(map[string]struct{})
	for _, data := range getDataList {
		if _, ok := addDataIDMap[data.Id]; !ok {
			addDataIDMap[data.Id] = struct{}{}
			addDataList = append(addDataList, &vector_db_manager.AddVectorReq_Index{
				Id:              data.Id,
				Embedding:       data.Embedding,
				AttributeFields: data.AttributeFields,
				EntityId:        data.EntityId,
			})
		}
	}
	return addDataList
}

// InsertVectors ...
//
//	@Description: 新增向量
//	@receiver d
//	@param ctx
//	@param requestID     请求ID
//	@param prodGroupID   正式环境向量库GroupID
//	@param appInfo       向量数据库操作AppInfo
//	@param InsertVectors 待新增的向量列表
//	@return error
func (d dao) InsertVectors(ctx context.Context, requestID, prodGroupID string,
	appInfo *vector_db_manager.AppInfo, insertVectors []*vector_db_manager.GetVectorRsp_Index) error {
	if len(insertVectors) == 0 {
		log.InfoContextf(ctx, "InsertVectors InsertVectors is empty, no need insert")
		return nil
	}

	req := &vector_db_manager.AddVectorReq{
		RequestId: requestID + entity.ActionInsert,
		GroupId:   prodGroupID,
		DataList:  covertGetToAdd(insertVectors),
		AppInfo:   appInfo,
		Force:     true,
		// 设置Force=true，表示upsert
		// 适配场景：发布失败再次发布
	}
	log.InfoContextf(ctx, "InsertVectors AddVector req.RequestId:%s", req.RequestId)
	log.DebugContextf(ctx, "InsertVectors AddVector req:%s", req.String())
	rsp, err := d.client.AddVector(ctx, req)
	if err != nil || rsp == nil || rsp.Code != 0 {
		err = fmt.Errorf("rsp:%+v, err:%v", rsp, err)
		log.ErrorContextf(ctx, "InsertVectors AddVector, err:%v", err)
		return err
	}
	log.DebugContextf(ctx, "InsertVectors AddVector rsp:%s", rsp.String())
	log.InfoContextf(ctx, "InsertVectors AddVector rsp.RequestId:%s", rsp.RequestId)
	return nil
}

// UpdateVectors ...
//
//	@Description:
//	@receiver d
//	@param ctx
//	@param requestID     请求ID
//	@param prodGroupID   正式环境向量库GroupID
//	@param appInfo       向量数据库操作AppInfo
//	@param updateVectors 待更新的向量列表
//	@return error
func (d dao) UpdateVectors(ctx context.Context, requestID, prodGroupID string,
	appInfo *vector_db_manager.AppInfo, updateVectors []*vector_db_manager.GetVectorRsp_Index) error {
	if len(updateVectors) == 0 {
		log.InfoContextf(ctx, "updateVectors updateVectors is empty, no need update")
		return nil
	}

	req := &vector_db_manager.AddVectorReq{
		RequestId: requestID + entity.ActionUpdate,
		GroupId:   prodGroupID,
		DataList:  covertGetToAdd(updateVectors),
		AppInfo:   appInfo,
		Force:     true,
		// 更新向量实际调用新增向量接口，设置Force=true，表示upsert
		// 底座向量侧update接口暂不可用，目前没有维护，建议使用上述方式
	}
	log.InfoContextf(ctx, "updateVectors AddVector req.RequestId:%s", req.RequestId)
	log.DebugContextf(ctx, "updateVectors AddVector req:%s", req.String())
	rsp, err := d.client.AddVector(ctx, req)
	if err != nil || rsp == nil || rsp.Code != 0 {
		err = fmt.Errorf("rsp:%+v, err:%v", rsp, err)
		log.ErrorContextf(ctx, "updateVectors AddVector, err:%v", err)
		return err
	}
	log.DebugContextf(ctx, "updateVectors AddVector rsp:%s", rsp.String())
	log.InfoContextf(ctx, "updateVectors AddVector rsp.RequestId:%s", rsp.RequestId)
	return nil
}

// DeleteVectors ...
//
//	@Description: 删除语料的向量
//	@receiver d
//	@param ctx
//	@param requestID       请求ID
//	@param prodGroupID     正式环境向量库GroupID
//	@param appInfo         向量数据库操作AppInfo
//	@param deleteVectorIDs 待删除的向量ID列表
//	@return error
func (d dao) DeleteVectors(ctx context.Context, requestID, prodGroupID string,
	appInfo *vector_db_manager.AppInfo, deleteVectorIDs []string) error {
	if len(deleteVectorIDs) == 0 {
		log.InfoContextf(ctx, "DeleteVectors deleteVectorIDs is empty, no need delete")
		return nil
	}

	// 去重
	deleteVectorIDs = types.Unique(deleteVectorIDs)

	vectors, err := d.GetVectors(ctx, requestID+entity.ActionDelete, prodGroupID, appInfo, deleteVectorIDs)
	if err != nil {
		return err
	}

	if len(vectors) == 0 {
		log.InfoContextf(ctx, "DeleteVectors vectors is empty, no need delete")
		return nil
	}

	vectorIDs := make([]string, 0)
	for _, vector := range vectors {
		vectorIDs = append(vectorIDs, vector.Id)
	}

	req := &vector_db_manager.DeleteVectorReq{
		RequestId: requestID + entity.ActionDelete,
		GroupId:   prodGroupID,
		Ids:       vectorIDs,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "DeleteVectors DeleteVector req.RequestId:%s", req.RequestId)
	log.DebugContextf(ctx, "DeleteVectors DeleteVector req:%s", req.String())
	rsp, err := d.client.DeleteVector(ctx, req)
	if err != nil || rsp == nil || rsp.Code != 0 {
		err = fmt.Errorf("rsp:%+v, err:%v", rsp, err)
		log.ErrorContextf(ctx, "DeleteVectors DeleteVector, err:%v", err)
		return err
	}
	log.DebugContextf(ctx, "DeleteVectors DeleteVector rsp:%s", rsp.String())
	log.InfoContextf(ctx, "DeleteVectors DeleteVector rsp.RequestId:%s", rsp.RequestId)
	return nil
}

// singleExtractionSaveCorpusVector 单个存示例问法
func (d dao) singleExtractionSaveCorpusVector(ctx context.Context, requestId, intentID string, itemIe *entity.IntentCorpus,
	fieldValueIntent *vector_db_manager.FieldValue,
	appInfo *vector_db_manager.AppInfo) (*vector_db_manager.AddVectorReq_Index, error) {
	embeddingIntentExampleReq := &vector_db_manager.EmbeddingReq{
		RequestId: requestId,
		Prompts:   []string{itemIe.Corpus},
		ModelName: config.GetMainConfig().VectorGroup.EmbeddingModelName,
		AppInfo:   appInfo,
	}
	log.DebugContextf(ctx, "sid:%s|SaveCorpusVector|Embedding embeddingIntentReq:%s",
		requestId, embeddingIntentExampleReq.String())
	embeddingIntentExampleRst, err := d.client.Embedding(ctx, embeddingIntentExampleReq)
	if err != nil || embeddingIntentExampleRst == nil ||
		embeddingIntentExampleRst.Code != 0 || len(embeddingIntentExampleRst.Embeddings) < 1 {
		err = fmt.Errorf("embeddingRsp:%+v, err:%v", embeddingIntentExampleRst, err)
		log.ErrorContextf(ctx, "sid:%s|SaveCorpusVector Embedding, err:%v", requestId, err)
		return nil, err
	}

	fieldValueExample := &vector_db_manager.FieldValue{
		FieldName:        FieldCorpusID,
		FieldType:        vector_db_manager.FieldValue_STRING,
		FieldValueString: itemIe.CorpusID,
	}
	fieldValueExampleOrg := &vector_db_manager.FieldValue{
		FieldName:        FieldCorpusValue,
		FieldType:        vector_db_manager.FieldValue_STRING,
		FieldValueString: itemIe.Corpus, // 示例问法原文
	}

	singleData := &vector_db_manager.AddVectorReq_Index{
		Id:        itemIe.CorpusID,                                        // 示例问法ID
		Embedding: embeddingIntentExampleRst.Embeddings[0].GetEmbedding(), // 示例问法向量化数据
		AttributeFields: &vector_db_manager.AttributeFields{
			Fields: []*vector_db_manager.FieldValue{fieldValueIntent, fieldValueExample, fieldValueExampleOrg},
		},
		EntityId: intentID, // 私有化弃用
	}
	return singleData, nil
}

// processBatchSaveCorpusVector
func (d dao) processBatchSaveCorpusVector(ctx context.Context, requestId, intentID string,
	fieldValueIntent *vector_db_manager.FieldValue,
	appInfo *vector_db_manager.AppInfo, intentExamples *[]entity.IntentCorpus,
	results chan *vector_db_manager.AddVectorReq_Index, wg *sync.WaitGroup) {
	defer wg.Done()
	for _, intentExample := range *intentExamples {
		result, err := d.singleExtractionSaveCorpusVector(ctx, requestId, intentID, &intentExample, fieldValueIntent, appInfo)
		if err == nil {
			results <- result
		}
	}
}

// SaveCorpusVector 根据corpus语料内容，先生成Embedding，然后保存到向量库.
// 该方法仅针对Sandbox环境.
// 不支持批量操作
// todo by gauss：重要 增加监控告警
func (d dao) SaveCorpusVector(ctx context.Context, robotID, corpusID, intentID, corpus,
	sandboxGroupID string, intentExample *[]entity.IntentCorpus) error {
	appInfo := getVectorAppInfo(robotID)
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|SaveCorpusVector|corpusID:%s|corpus:%s", sid, corpusID, corpus)
	// 向量化
	embeddingReq := &vector_db_manager.EmbeddingReq{
		RequestId: sid, Prompts: []string{corpus}, AppInfo: appInfo,
		ModelName: config.GetMainConfig().VectorGroup.EmbeddingModelName,
	}
	log.DebugContextf(ctx, "sid:%s|SaveCorpusVector Embedding|req:%s", sid, embeddingReq.String())
	embeddingRsp, err := d.client.Embedding(ctx, embeddingReq)
	if err != nil || embeddingRsp == nil || embeddingRsp.Code != 0 || len(embeddingRsp.Embeddings) < 1 {
		err = fmt.Errorf("embeddingRsp:%+v, err:%v", embeddingRsp, err)
		log.ErrorContextf(ctx, "SaveCorpusVector Embedding, err:%v", err)
		return err
	}

	// 包含示例问法的向量化
	if intentExample != nil && len(*intentExample) > 0 {
		log.InfoContextf(ctx, "sid:%s|SaveCorpusVector|intentExample|len:%d", sid, len(*intentExample))
		for _, ies := range splitIntentCorpusSlice(*intentExample, getVectorOperationMaxIDs()-5) {
			fieldValueIntent := &vector_db_manager.FieldValue{
				FieldName: FieldIntentID, FieldValueString: intentID,
				FieldType: vector_db_manager.FieldValue_STRING,
			}
			dataList := []*vector_db_manager.AddVectorReq_Index{
				{
					Id: corpusID, EntityId: intentID, //私有化弃用
					Embedding: embeddingRsp.Embeddings[0].GetEmbedding(),
					AttributeFields: &vector_db_manager.AttributeFields{
						Fields: []*vector_db_manager.FieldValue{fieldValueIntent},
					},
				},
			}

			results := make(chan *vector_db_manager.AddVectorReq_Index, len(ies))
			var wg sync.WaitGroup
			embeddingBatch := config.GetMainConfig().ExampleCorpus.ExampleEmbeddingBatchSize
			for i := 0; i < len(ies); i += embeddingBatch {
				batch := ies[i:util.Min(i+embeddingBatch, len(ies))]
				wg.Add(1)
				go d.processBatchSaveCorpusVector(ctx, sid,
					intentID, fieldValueIntent, appInfo, &batch, results, &wg)
			}
			go func() {
				wg.Wait()
				close(results)
			}()

			for result := range results {
				dataList = append(dataList, result)
			}
			log.InfoContextf(ctx, "sid:%s|AddBatch|AddVector|len:%d|results:%+v", sid, len(dataList), results)
			if err = d.AddVectorByBatch(ctx, dataList, appInfo, sid, sandboxGroupID); err != nil {
				log.ErrorContextf(ctx, "sid:%s|SaveCorpusVector|AddVectorByBatch|err:%v", sid, err)
				return err
			}
		}
	} else {
		// 没有示例问法
		fieldValueIntent := &vector_db_manager.FieldValue{
			FieldName:        FieldIntentID,
			FieldType:        vector_db_manager.FieldValue_STRING,
			FieldValueString: intentID,
		}
		dataList := []*vector_db_manager.AddVectorReq_Index{
			{
				Id:        corpusID,
				Embedding: embeddingRsp.Embeddings[0].GetEmbedding(),
				AttributeFields: &vector_db_manager.AttributeFields{
					Fields: []*vector_db_manager.FieldValue{fieldValueIntent},
				},
				EntityId: intentID, // 私有化弃用
			},
		}
		if err = d.AddVectorByBatch(ctx, dataList, appInfo, sid, sandboxGroupID); err != nil {
			log.ErrorContextf(ctx, "sid:%sSaveCorpusVector AddVector, err:%v", sid, err)
			return err
		}
	}
	return nil
}

// DeleteCorpusVector 删除向量预料
// 该方法仅针对Sandbox环境.
// 持批量操作
// todo by gauss：重要 增加监控告警
func (d dao) DeleteCorpusVector(ctx context.Context, robotID string, corpusIDs []string) error {
	log.InfoContextf(ctx, "DeleteCorpusVector, robotID:%s, corpusIDs:%+v", robotID, corpusIDs)
	appInfo := getVectorAppInfo(robotID)
	requestId := util.RequestID(ctx)
	if len(corpusIDs) == 0 {
		return nil
	}
	delVectorReq := &vector_db_manager.DeleteVectorReq{
		RequestId: requestId,
		GroupId: fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID),
		Ids:     corpusIDs,
		AppInfo: appInfo,
	}
	log.DebugContextf(ctx, "DeleteCorpusVector DelVector req:%s", delVectorReq.String())
	delVectorRsp, err := d.client.DeleteVector(ctx, delVectorReq)
	if err != nil || delVectorRsp == nil || delVectorRsp.Code != 0 {
		err = fmt.Errorf("delVectorRsp:%+v, err:%v", delVectorRsp, err)
		log.ErrorContextf(ctx, "DeleteCorpusVector delVectorRsp, err:%v", err)
		return err
	}
	// 删除对应的group缓存
	// TODO：放在机器人的创建/删除(删除向量Group，及未分类)更合理
	redisClient := database.GetRedis()
	rkey := fmt.Sprintf("%s-%s-%s",
		config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID)
	log.DebugContextf(ctx, "DeleteCorpusVector DelVector rkey:%s", rkey)
	if _, err = redisClient.Del(ctx, rkey).Result(); err != nil {
		log.ErrorContextf(ctx, "DeleteCorpusVector,VectorGroup fail:%s|%s", err.Error())
		return err
	}

	log.InfoContextf(ctx, "DeleteCorpusVector delVectorRsp rsp:%s", delVectorRsp.String())

	return nil
}

// DeleteIntentExampleVector 删除向量中的示例问法
// 该方法仅针对Sandbox环境.
// 持批量操作
func (d dao) DeleteIntentExampleVector(ctx context.Context, robotID string, intentExampleIds []string) error {
	log.InfoContextf(ctx, "DeleteCorpusVector, robotID:%s, intentExampleIds:%+v", robotID, intentExampleIds)
	appInfo := getVectorAppInfo(robotID)
	requestId := util.RequestID(ctx)
	if len(intentExampleIds) == 0 {
		return nil
	}
	delVectorReq := &vector_db_manager.DeleteVectorReq{
		RequestId: requestId,
		GroupId: fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID),
		Ids:     intentExampleIds,
		AppInfo: appInfo,
	}
	log.DebugContextf(ctx, "DeleteIntentExampleVector DelVector req:%s", delVectorReq.String())
	delVectorRsp, err := d.client.DeleteVector(ctx, delVectorReq)
	if err != nil || delVectorRsp == nil || delVectorRsp.Code != 0 {
		err = fmt.Errorf("delVectorRsp:%+v, err:%v", delVectorRsp, err)
		log.ErrorContextf(ctx, "DeleteIntentExampleVector delVectorRsp, err:%v", err)
		return err
	}
	// 删除对应的group缓存
	// TODO：放在机器人的创建/删除(删除向量Group，及未分类)更合理
	redisClient := database.GetRedis()
	rkey := fmt.Sprintf("%s-%s-%s",
		config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID)
	log.DebugContextf(ctx, "DeleteIntentExampleVector DelVector rkey:%s", rkey)
	if _, err = redisClient.Del(ctx, rkey).Result(); err != nil {
		log.ErrorContextf(ctx, "DeleteIntentExampleVector,VectorGroup fail:%s|%s", err.Error())
		return err
	}

	log.InfoContextf(ctx, "DeleteIntentExampleVector delVectorRsp rsp:%s", delVectorRsp.String())

	return nil
}

// splitIntentCorpusSlice 切分 IntentCorpus
func splitIntentCorpusSlice(icSlice []entity.IntentCorpus, eachSize int) [][]entity.IntentCorpus {
	// 取模
	mod := len(icSlice) % eachSize
	// 取余
	k := len(icSlice) / eachSize

	// 总片数
	var end int
	if mod == 0 {
		end = k
	} else {
		end = k + 1
	}

	result := make([][]entity.IntentCorpus, 0)
	for i := 0; i < end; i++ {
		if i != k {
			result = append(result, icSlice[i*eachSize:(i+1)*eachSize])
		} else {
			result = append(result, icSlice[i*eachSize:])
		}
	}
	return result
}

// vectorCallHandling vector新增修改删除调用处理
func (d dao) vectorCallHandling(ctx context.Context, requestID string, prodGroupID string,
	appInfo *vector_db_manager.AppInfo, inserts []*vector_db_manager.GetVectorRsp_Index,
	updates []*vector_db_manager.GetVectorRsp_Index, deletes []string) error {
	// 新增
	if len(inserts) > 0 {
		if err := d.InsertVectors(ctx, requestID, prodGroupID, appInfo, inserts); err != nil {
			return err
		}
	}
	// 更新
	if len(updates) > 0 {
		if err := d.UpdateVectors(ctx, requestID, prodGroupID, appInfo, updates); err != nil {
			return err
		}
	}
	// 删除
	if len(deletes) > 0 {
		if err := d.DeleteVectors(ctx, requestID, prodGroupID, appInfo, deletes); err != nil {
			return err
		}
	}
	return nil
}
