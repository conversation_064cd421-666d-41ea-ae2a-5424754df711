// Package vector 向量数据库相关
// @(#)dao_impl_entry_vector.go  星期五, 三月 22, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.
package vector

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
)

// GetEntryVectorGroupID 获取检索词条的groupID
func (d dao) GetEntryVectorGroupID(ctx context.Context, robotID string) (string, string, error) {
	sid := util.RequestID(ctx)

	var err error
	// 采用事务的形式，将写db与通知dm放到一起
	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM()).Debug()
	defer func() {
		// 事务的提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// 从数据库中查找
	entryVectorInfo, err := getVectorInfoByRobotIdAndType(ctx, tx, robotID, entity.SaveEntryType)
	if err != nil {
		log.ErrorContextf(ctx, "getVectorInfoByRobotIdAndType Failed! sid:%s,err:%v", sid, err)
		return "", "", err
	}

	// 往数据库插入一条数据并通知DM
	if len(entryVectorInfo) == 0 {
		entryVectorInfo = buildVectorInfo(ctx, robotID, entity.SaveEntryType)
		if err := tx.Model(&entity.VectorGroup{}).CreateInBatches(entryVectorInfo, 20).Error; err != nil {
			log.ErrorContextf(ctx, "GetEntryVectorGroupID.CreateFailed!  sid:%s, err:%+v", sid, err)
			return "", "", err
		}
		log.InfoContextf(ctx, "CreateEntryVector Success,sid:%s,entryVectorInfo:%+v", sid, entryVectorInfo)

		err = d.CreateBotEntryVectorGroup(ctx, robotID, entryVectorInfo[0].VectorGroupID)
		if err != nil {
			log.ErrorContextf(ctx, "CreateBotEntryVectorGroup Failed! sid:%s,err:%+v", sid, err)
			return "", "", err
		}

		err = d.CreateBotEntryVectorGroup(ctx, robotID, entryVectorInfo[1].VectorGroupID)
		if err != nil {
			log.ErrorContextf(ctx, "CreateBotEntryVectorGroup Failed! sid:%s,err:%+v", sid, err)
			return "", "", err
		}

		// 通知DM,只需要sandbox的部分
		req := &KEP_DM.UpsertRobotToSandboxRequest{
			RobotID:               robotID,
			RetrievalEntryGroupID: entryVectorInfo[0].VectorGroupID,
		}
		if _, err := rpc.UpsertRobotToSandbox(ctx, req); err != nil {
			log.ErrorContextf(ctx, "UpsertSlotsToSandbox Failed, sid:%s, err:%v", sid, err)
			return "", "", err
		}
	}

	sandbox, prod := getSandboxProdGroupID(entryVectorInfo)
	return sandbox, prod, nil
}

// CreateBotEntryVectorGroup  创建词条向量组
func (d dao) CreateBotEntryVectorGroup(ctx context.Context, robotID, groupID string) error {
	sid := util.RequestID(ctx)
	requestID := util.RequestID(ctx)

	appInfo := getVectorAppInfo(robotID)

	createReq := &vector_db_manager.CreateGroupReq{
		RequestId: requestID,
		GroupId:   groupID,
		AppInfo:   appInfo,
		UseVdb:    config.GetMainConfig().VectorGroup.TaskFlowUseVdb, // 老任务流程暂时不切 UseVdb
	}

	createResp, err := d.client.CreateGroup(ctx, createReq)
	if err != nil || createResp == nil || (createResp.Code != 0 && createResp.Code != 71014) {
		err = fmt.Errorf("createResp:%+v,err:%+v", createResp, err)
		log.ErrorContextf(ctx, "CreateBotEntryVectorGroup CreateGroup Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	log.InfoContextf(ctx, "CreateBotEntryVectorGroup Success! req:%+v,resp:%+v", createReq, createResp)
	return nil

}

// DeleteEntryVectorGroup 删除检索词条向量库
func (d dao) DeleteEntryVectorGroup(ctx context.Context, robotID, groupID string) error {
	sid := util.RequestID(ctx)

	requestID := util.RequestID(ctx)
	// 删除数据库
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Model(&entity.VectorGroup{}).
		Where("f_vector_group_id = ? AND f_robot_id = ? AND f_save_type = ?", robotID, groupID, entity.SaveEntryType).
		Update("f_is_deleted", 1).
		Error
	if err != nil {
		log.ErrorContextf(ctx, "DeleteEntryVectorGroup.DeleteTable Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	//
	// 删除向量库
	appInfo := getVectorAppInfo(robotID)

	delGroupReq := &vector_db_manager.DeleteGroupReq{
		RequestId: requestID,
		GroupId:   groupID,
		AppInfo:   appInfo,
	}
	delGroupResp, err := d.client.DeleteGroup(ctx, delGroupReq)
	if err != nil || delGroupResp == nil || (delGroupResp.Code != 0 && delGroupResp.Code != 71014) {
		err = fmt.Errorf("delGroupResp:%+v, err:%v", delGroupResp, err)
		log.ErrorContextf(ctx, "DeleteEntryVectorGroup delGroupResp, sid:%s,err:%v", sid, err)
		return err
	}

	return nil

}

func (d dao) UpsertEntryVector(ctx context.Context, robotID, groupID, entryID, entryName, entityID string) error {
	sid := util.RequestID(ctx)

	appInfo := getVectorAppInfo(robotID)
	requestId := util.RequestID(ctx)

	// 1. 词条名向量化
	embeddingReq := &vector_db_manager.EmbeddingReq{
		RequestId: requestId,
		Prompts:   []string{entryName},
		ModelName: config.GetMainConfig().VectorGroup.EmbeddingModelName,
		AppInfo:   appInfo,
	}
	embeddingRsp, err := d.client.Embedding(ctx, embeddingReq)
	if err != nil || embeddingRsp == nil || embeddingRsp.Code != 0 || len(embeddingRsp.Embeddings) < 1 {
		err = fmt.Errorf("embeddingRsp:%+v, err:%v", embeddingRsp, err)
		log.ErrorContextf(ctx, "UpsertEntryVector Embedding,sid:%s, err:%v", sid, err)
		return err
	}
	log.DebugContextf(ctx, "UpsertEntryVector Embedding Result, sid:%s,rsp:%s", sid, embeddingRsp.String())

	// 2.保存到向量库
	fieldValues := []*vector_db_manager.FieldValue{
		{
			FieldName:        "entityID",
			FieldType:        vector_db_manager.FieldValue_STRING,
			FieldValueString: entityID,
		},
		{
			FieldName:        "entryName",
			FieldType:        vector_db_manager.FieldValue_STRING,
			FieldValueString: entryName,
		},
	}

	addVectorReq := &vector_db_manager.AddVectorReq{
		RequestId: requestId,
		GroupId:   groupID,
		DataList: []*vector_db_manager.AddVectorReq_Index{
			{
				Id:        entryID, // 词条ID
				Embedding: embeddingRsp.Embeddings[0].GetEmbedding(),
				AttributeFields: &vector_db_manager.AttributeFields{
					Fields: fieldValues,
				},
				EntityId: entityID,
			},
		},
		AppInfo: appInfo,
		Force:   true, // update不维护，update时使用Force=true强制添加
	}
	addVectorRsp, err := d.client.AddVector(ctx, addVectorReq)
	if err != nil || addVectorRsp == nil || addVectorRsp.Code != 0 {
		err = fmt.Errorf("addVectorRsp:%+v, err:%v", addVectorRsp, err)
		log.ErrorContextf(ctx, "UpsertEntryVector AddVector Failed! addVectorReq:%+v,err:%v", addVectorReq, err)
		return err
	}
	log.InfoContextf(ctx, "UpsertEntryVector AddVector Success! req:%+v,rsp:%s", addVectorReq,
		addVectorRsp.String())

	return nil
}

// DeleteEntryVector 支持批量删除
func (d dao) DeleteEntryVector(ctx context.Context, robotID, groupID string, entryID []string) error {
	sid := util.RequestID(ctx)

	appInfo := getVectorAppInfo(robotID)
	requestId := util.RequestID(ctx)
	if len(entryID) == 0 {
		return nil
	}
	delVectorReq := &vector_db_manager.DeleteVectorReq{
		RequestId: requestId,
		GroupId:   groupID,
		Ids:       entryID,
		AppInfo:   appInfo,
	}
	delVectorRsp, err := d.client.DeleteVector(ctx, delVectorReq)
	if err != nil || delVectorRsp == nil || delVectorRsp.Code != 0 {
		err = fmt.Errorf("delVectorRsp:%+v, err:%v", delVectorRsp, err)
		log.ErrorContextf(ctx, "DeleteEntryVector Failed! sid:%s,err:%v", sid, err)
		return err
	}

	log.InfoContextf(ctx, "DeleteEntryVector Success! sid:%s,req:%+v,resp:%s", sid, delVectorReq, delVectorRsp.String())
	return nil
}

// PublishEntryVector ...
//
//	@Description: 任务流词条向量发布
//	@receiver d
//	@param ctx
//	@param robotID 待发布机器人ID
//	@param entries 待发布词条
//	@return string 词条Prod向量库GroupID
//	@return error
func (d dao) PublishEntryVector(ctx context.Context, robotID string, entries []*entity.Entry) (string, error) {
	log.InfoContextf(ctx, "PublishEntryVector, robotID:%s, len(entries):%d", robotID, len(entries))
	if len(robotID) == 0 || len(entries) == 0 {
		return "", nil
	}
	entryIDs := make([]string, 0)
	entryIDMap := make(map[string]*entity.Entry)
	for _, entry := range entries {
		entryIDs = append(entryIDs, entry.EntryID)
		entryIDMap[entry.EntryID] = entry
	}

	// 基本信息
	appInfo := getVectorAppInfo(robotID)
	requestID := util.RequestID(ctx)
	sandboxGroupID, prodGroupID, err := d.getEntrySandboxAndProdVectorGroupID(ctx, robotID)
	if err != nil {
		return "", err
	}

	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(entryIDs, getVectorOperationMaxIDs()) {
		log.InfoContextf(ctx, "PublishEntryVector, entryIDs:%v", ids)
		// 根据action分类
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		entryVectors, err := d.GetVectors(ctx, requestID, sandboxGroupID, appInfo, ids)
		if err != nil {
			return "", err
		}
		if len(entryVectors) > 0 {
			for _, vector := range entryVectors {
				entry, ok := entryIDMap[vector.Id]
				if !ok {
					log.WarnContextf(ctx, "PublishEntryVector vectorID:%s not exits", vector.Id)
					continue
				}
				switch entry.Action {
				case entity.ActionInsert:
					inserts = append(inserts, vector)
				case entity.ActionUpdate:
					updates = append(updates, vector)
				case entity.ActionDelete:
					deletes = append(deletes, vector.Id)
				default:
					log.WarnContextf(ctx, "PublishEntryVector Entry:%+v, Action:%s illegal",
						*entry, entry.Action)
				}
			}
		}
		// sandbox中删除的向量
		for _, id := range ids {
			entry, ok := entryIDMap[id]
			if !ok {
				log.WarnContextf(ctx, "PublishEntryVector EntryID:%s not exits", id)
				continue
			}
			if entry.Action == entity.ActionDelete {
				deletes = append(deletes, entry.EntryID)
			}
		}

		err = d.vectorCallHandling(ctx, requestID, prodGroupID, appInfo, inserts, updates, deletes)
		if err != nil {
			return "", err
		}
		log.InfoContextf(ctx, "PublishEntryVector published, len(inserts):%d, len(updates):%d, len(deletes):%d",
			len(inserts), len(updates), len(deletes))
	}
	log.InfoContextf(ctx, "PublishEntryVector success, prodGroupID:%s", prodGroupID)
	return prodGroupID, nil
}

// getEntrySandboxAndProdVectorGroupID 获取词条Sandbox和Prod的向量库GroupID
func (d dao) getEntrySandboxAndProdVectorGroupID(ctx context.Context, robotID string) (string, string, error) {
	log.InfoContextf(ctx, "getEntrySandboxAndProdVectorGroupID, robotID:%s", robotID)
	entryVectorInfos := make([]entity.VectorGroup, 0)
	err := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug().
		Model(&entity.VectorGroup{}).
		Where("f_robot_id = ? and f_save_type = ? and f_vector_group_type IN ? and f_is_deleted = 0 ",
			robotID, entity.SaveEntryType, []string{SandboxGroupInfix, ProdGroupInfix}).
		Find(&entryVectorInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "getEntrySandboxAndProdVectorGroupID Failed, err:%v", err)
		return "", "", err
	}
	var sandboxGroupID, prodGroupID string
	for _, info := range entryVectorInfos {
		if info.VectorGroupType == SandboxGroupInfix {
			sandboxGroupID = info.VectorGroupID
		}
		if info.VectorGroupType == ProdGroupInfix {
			prodGroupID = info.VectorGroupID
		}
	}
	if len(entryVectorInfos) != 2 || len(sandboxGroupID) == 0 || len(prodGroupID) == 0 {
		err = fmt.Errorf("getEntrySandboxAndProdVectorGroupID robotID:%s, entryVectorInfos:%v illegal",
			robotID, entryVectorInfos)
		log.ErrorContextf(ctx, "sid:%s|getEntrySandboxAndProdVectorGroupID,err:%s", err)
		return "", "", err
	}
	log.InfoContextf(ctx, "getEntrySandboxAndProdVectorGroupID success, sandboxGroupID:%s, prodGroupID:%s",
		sandboxGroupID, prodGroupID)
	return sandboxGroupID, prodGroupID, nil
}
