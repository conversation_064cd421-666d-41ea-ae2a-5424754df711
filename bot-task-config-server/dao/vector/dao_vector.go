// bot-task-config-server
//
// @(#)dao_vector.go  星期三, 四月 10, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package vector

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
)

// AddVectorByBatch 批量保存数据
func (d dao) AddVectorByBatch(ctx context.Context, dataList []*vector_db_manager.AddVectorReq_Index,
	appInfo *vector_db_manager.AppInfo, requestId, groupId string) error {
	sid := util.RequestID(ctx)
	addVectorReq := &vector_db_manager.AddVectorReq{
		RequestId: requestId,
		GroupId:   groupId,
		DataList:  dataList,
		AppInfo:   appInfo,
		Force:     true, // update不维护，update时使用Force=true强制添加
	}

	// 保存到向量库
	// 公有云向量：Zeus，https://iwiki.woa.com/p/364103381
	// 私有化向量：Hades，https://iwiki.woa.com/p/4009480075
	// Hades 的entityId后面要废弃，如果 DataList的ID(featureID)和 EntityId 不一致，在私有化的时候
	// 向量检索的时候不会返回 intentID， 这里DM端(kinvo)兼容处理了，通过过滤fieldValue中的IntentID

	log.InfoContextf(ctx, "sid:%s|AddVectorByBatch|AddVector|dataList.len:%d|req:%s",
		sid, len(dataList), addVectorReq.String())
	addVectorRsp, err := d.client.AddVector(ctx, addVectorReq)
	if err != nil || addVectorRsp == nil || addVectorRsp.Code != 0 {
		err = fmt.Errorf("addVectorRsp:%+v, err:%v", addVectorRsp, err)
		log.ErrorContextf(ctx, "sid:%s|AddVector|err:%v", sid, err)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|AddVectorByBatch|AddVector|rsp:%s", sid, addVectorRsp.String())
	return nil
}

// getVectorInfoByRobotIdAndType 通过robotId和type到 VectorGroup 查询 向量组
func getVectorInfoByRobotIdAndType(ctx context.Context, tx *gorm.DB,
	robotId, saveType string) ([]entity.VectorGroup, error) {
	var vectorInfo []entity.VectorGroup
	sid := util.RequestID(ctx)
	err := tx.Model(&entity.VectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", robotId, saveType).
		Find(&vectorInfo).Error
	log.InfoContextf(ctx, "getVectorInfoByRobotIdAndType.First Result, sid:%s,result:%+v,err:%+v", sid,
		vectorInfo, err)
	if err != nil {
		return vectorInfo, err
	}
	return vectorInfo, nil
}

// getVectorGroupSandboxAndProdIdFromDB 从DB获取向量组的GroupID
func getVectorGroupSandboxAndProdIdFromDB(ctx context.Context,
	robotId, saveType string) (string, string, error) {
	var vectorInfo []entity.VectorGroup
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	err := db.Model(&entity.VectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", robotId, saveType).
		Find(&vectorInfo).Error
	if err != nil {
		return "", "", err
	}
	sandboxGroupId, prodGroupId := getSandboxProdGroupID(vectorInfo)

	if len(vectorInfo) == 0 {
		vdb := NewDao()
		vectorInfo = buildVectorInfo(ctx, robotId, saveType)
		sandboxGroupId, prodGroupId, err = vdb.CreateBotCorpusGroup(ctx, robotId, vectorInfo)
		if err != nil {
			return "", "", nil
		}
	}
	return sandboxGroupId, prodGroupId, nil
}

func buildVectorInfo(ctx context.Context, robotID, saveType string) []entity.VectorGroup {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	sandboxGroup, prodGroup := getVectorGroupIDStr(robotID, saveType)

	return []entity.VectorGroup{
		{
			VectorGroupID:     sandboxGroup,
			VectorGroupType:   SandboxGroupInfix,
			RobotID:           robotID,
			SaveType:          saveType,
			EmbeddingModeName: config.GetMainConfig().VectorGroup.EmbeddingModelName,
			UIN:               uin,
			SubUIN:            subUin,
		},
		{
			VectorGroupID:     prodGroup,
			VectorGroupType:   ProdGroupInfix,
			RobotID:           robotID,
			SaveType:          saveType,
			EmbeddingModeName: config.GetMainConfig().VectorGroup.EmbeddingModelName,
			UIN:               uin,
			SubUIN:            subUin,
		},
	}
}

// getVectorAppInfo get vector app info
func getVectorAppInfo(robotID string) *vector_db_manager.AppInfo {
	return &vector_db_manager.AppInfo{
		Biz:    config.GetMainConfig().VectorGroup.Biz,
		AppKey: robotID,
		Secret: config.GetMainConfig().VectorGroup.Secret,
	}
}

// getSandboxProdGroupID 通过 VectorGroup 获取 groupId
func getSandboxProdGroupID(vectorInfo []entity.VectorGroup) (sandboxGroupID, prodGroupID string) {
	for _, v := range vectorInfo {
		if v.VectorGroupType == SandboxGroupInfix {
			sandboxGroupID = v.VectorGroupID
		}
		if v.VectorGroupType == ProdGroupInfix {
			prodGroupID = v.VectorGroupID
		}
	}
	return sandboxGroupID, prodGroupID
}

// getVectorGroupIDStr get vector group ID
func getVectorGroupIDStr(robotID, saveType string) (sandboxGroupID, prodGroupID string) {
	// 这里需要适配一下历史的intent的group
	if saveType == entity.SaveIntentType {
		sandboxGroupID = fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, SandboxGroupInfix, robotID)
		prodGroupID = fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, ProdGroupInfix, robotID)
	} else {
		sandboxGroupID = fmt.Sprintf("%s-%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, saveType, SandboxGroupInfix, robotID)
		prodGroupID = fmt.Sprintf("%s-%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, saveType, ProdGroupInfix, robotID)
	}

	return sandboxGroupID, prodGroupID
}
