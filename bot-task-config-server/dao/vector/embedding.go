// bot-task-config-server
//
// @(#)embedding.go  星期四, 十月 17, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package vector

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"golang.org/x/sync/errgroup"
)

// Content embedding 内容
type Content struct {
	Text  string
	BizId string
}

// GetBatchEmbedding 批量查询
func (d *dao) GetBatchEmbedding(ctx context.Context, contents []*Content,
	appInfo *vector_db_manager.AppInfo, modelName string) (vectors map[string][]float32, err error) {
	if len(contents) == 0 {
		log.WarnContextf(ctx, "GetBatch contents=0")
		return vectors, nil
	}
	// 避免 contents 内容重复 导致重复查询
	mm := make(map[string]struct{}, 0)
	var uniqContents []*Content
	for _, v := range contents {
		if _, ok := mm[v.BizId]; !ok {
			mm[v.BizId] = struct{}{}
			uniqContents = append(uniqContents, v)
		}
	}
	uniqVectors := make(map[string][]float32, len(uniqContents))
	wg, gCtx := errgroup.WithContext(ctx)
	batch := config.GetMainConfig().ExampleCorpus.ExampleEmbeddingBatchSize
	wg.SetLimit(batch)
	for _, v := range uniqContents {
		content := v
		wg.Go(func() (err error) {
			vector, err := d.GetEmbedding(gCtx, content, appInfo, modelName)
			if err != nil {
				return err
			}
			uniqVectors[content.BizId] = vector[content.BizId]
			return nil
		})
	}
	if err = wg.Wait(); err != nil {
		return vectors, err
	}

	return uniqVectors, nil
}

// GetEmbedding 查询
func (d *dao) GetEmbedding(ctx context.Context, content *Content,
	appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error) {
	sid := util.RequestID(ctx)
	req := &vector_db_manager.EmbeddingReq{
		RequestId: sid,
		Prompts:   []string{content.Text},
		ModelName: modelName,
		AppInfo:   appInfo,
	}
	//d.SaveWorkflowCorpusVector()
	log.DebugContextf(ctx, "sid:%s|SaveCorpusVector Embedding|req:%s", sid, req.String())
	rsp, err := d.client.Embedding(ctx, req)
	if err != nil || rsp == nil || rsp.Code != 0 {
		err = fmt.Errorf("embeddingRsp:%+v, err:%v", rsp, err)
		log.ErrorContextf(ctx, "SaveCorpusVector Embedding, err:%v", err)
		return nil, err
	}

	err = fmt.Errorf("get embedding empty")
	if len(rsp.GetEmbeddings()) != 1 {
		log.ErrorContextf(ctx, "Embedding request fail, len(GetEmbeddings) != 1, req: %+v, err: %v", req, err)
		return nil, err
	}
	if len(rsp.GetEmbeddings()[0].GetEmbedding()) == 0 {
		log.ErrorContextf(ctx, "Embedding request fail, len(GetEmbedding) == 0, req: %+v, err: %v", req, err)
		return nil, err
	}
	embeddingRsp := make(map[string][]float32, 0)
	embeddingRsp[content.BizId] = rsp.GetEmbeddings()[0].GetEmbedding()
	return embeddingRsp, nil
}
