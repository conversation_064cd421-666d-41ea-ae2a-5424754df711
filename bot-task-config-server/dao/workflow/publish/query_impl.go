package publish

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"gorm.io/gorm"
)

// GetUnPublishWorkflow 查询 t_workflow 待发布的数据
func (d dao) GetUnPublishWorkflow(ctx context.Context, robotID string, workflowIDs []string) (
	[]*entity.Workflow, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflow, robotID: %s, workflowIDs: %+v", robotID, workflowIDs)
	workflows := make([]*entity.Workflow, 0)
	if len(workflowIDs) == 0 {
		return workflows, nil
	}
	query := fmt.Sprintf("%s = ? AND %s IN ? AND %s != ?",
		entity.WorkflowColumns.RobotID, entity.WorkflowColumns.WorkflowID, entity.WorkflowColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(query, robotID, workflowIDs, entity.ReleaseStatusPublished).
		Find(&workflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflow workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflow success, len(workflows): %d", len(workflows))
	return workflows, nil
}

// GetUnPublishRobotWorkflow 查询 t_robot_workflow 待发布的数据
func (d dao) GetUnPublishRobotWorkflow(ctx context.Context, workflowIDs []string) ([]*entity.RobotWorkflow, error) {
	log.InfoContextf(ctx, "GetUnPublishRobotWorkflow, workflowIDs: %+v", workflowIDs)
	robotWorkflows := make([]*entity.RobotWorkflow, 0)
	if len(workflowIDs) == 0 {
		return robotWorkflows, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.RobotWorkflowColumns.WorkflowID, entity.RobotWorkflowColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.RobotWorkflow{}.TableName()).
		Where(query, workflowIDs, entity.ReleaseStatusPublished).
		Find(&robotWorkflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishRobotWorkflow workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishRobotWorkflow success, len(robotWorkflows): %d", len(robotWorkflows))
	return robotWorkflows, nil
}

// GetUnPublishWorkflowExample 查询 t_workflow_example 待发布的数据
func (d dao) GetUnPublishWorkflowExample(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowExample, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowExample, workflowIDs: %+v", workflowIDs)
	workflowExamples := make([]*entity.WorkflowExample, 0)
	if len(workflowIDs) == 0 {
		return workflowExamples, nil
	}
	// 将工作流关联的示例问法至为未发布，修复示例问法prod环境向量enable没发布问题,
	// 由于停启用及点调试会改变工作流的可用状态，所以需要全量查exam
	// tapd: https://tapd.woa.com/tapd_fe/70080800/bug/detail/1070080800137858823
	query := fmt.Sprintf("%s IN ?", entity.WorkflowExampleColumns.FlowID)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowExample{}.TableName()).
		Where(query, workflowIDs).
		Find(&workflowExamples).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowExample workflowDB.Find err: %+v", err)
		return nil, err
	}

	// 解决同步数据到Prod可能的冲突：action 如果是insert，转为update
	for i, v := range workflowExamples {
		if v.Action == entity.ActionInsert {
			workflowExamples[i].Action = entity.ActionUpdate
		}
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowExample success, len(workflowExamples): %d",
		len(workflowExamples))
	return workflowExamples, nil
}

// GetUnPublishWorkflowVar 查询 t_workflow_var 待发布的数据
func (d dao) GetUnPublishWorkflowVar(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowVar, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowVar, workflowIDs: %+v", workflowIDs)
	workflowVars := make([]*entity.WorkflowVar, 0)
	if len(workflowIDs) == 0 {
		return workflowVars, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.WorkflowVarColumns.WorkflowID, entity.WorkflowVarColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowVar{}.TableName()).
		Where(query, workflowIDs, entity.ReleaseStatusPublished).
		Find(&workflowVars).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowVar workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowVar success, len(workflowVars): %d", len(workflowVars))
	return workflowVars, nil
}

// GetUnPublishVarParams 查询 t_var 待发布数据
// t_var 的DB在旧的任务流程的表
func (d dao) GetUnPublishVarParams(ctx context.Context, varParamIDs []string) ([]*entity.VarParams, error) {
	log.InfoContextf(ctx, "GetUnPublishVarParams, varParamIDs: %+v", varParamIDs)
	varParams := make([]*entity.VarParams, 0)
	if len(varParamIDs) == 0 {
		return varParams, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.VarParamsColumns.VarID, entity.VarParamsColumns.ReleaseStatus)
	err := d.taskFlowDB.WithContext(ctx).Table(entity.VarParams{}.TableName()).
		Where(query, varParamIDs, entity.ReleaseStatusPublished).
		Find(&varParams).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishVarParams taskFlowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishVarParams success, len(varParams): %d", len(varParams))
	return varParams, nil
}

// GetUnPublishWorkflowParameter 查询 t_workflow_parameter 待发布的数据
func (d dao) GetUnPublishWorkflowParameter(ctx context.Context, workflowIDs []string) (
	[]*entity.WorkflowParameter, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowParameter, workflowIDs: %+v", workflowIDs)
	workflowParameters := make([]*entity.WorkflowParameter, 0)
	if len(workflowIDs) == 0 {
		return workflowParameters, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.WorkflowParameterColumns.WorkFlowID, entity.WorkflowParameterColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowParameter{}.TableName()).
		Where(query, workflowIDs, entity.ReleaseStatusPublished).
		Find(&workflowParameters).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowParameter workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowParameter success, len(workflowParameters): %d",
		len(workflowParameters))
	return workflowParameters, nil
}

// GetUnPublishParameter 查询 t_parameter 待发布的数据
func (d dao) GetUnPublishParameter(ctx context.Context, parameterIDs []string) ([]*entity.Parameter, error) {
	log.InfoContextf(ctx, "GetUnPublishParameter, parameterIDs: %+v", parameterIDs)
	parameters := make([]*entity.Parameter, 0)
	if len(parameterIDs) == 0 {
		return parameters, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.ParameterColumns.ParameterID, entity.ParameterColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.Parameter{}.TableName()).
		Where(query, parameterIDs, entity.ReleaseStatusPublished).
		Find(&parameters).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishParameter workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishParameter success, len(parameters): %d", len(parameters))
	return parameters, nil
}

// GetUnPublishWorkflowCustomModel 查询 t_workflow_custom_model 待发布的数据
func (d dao) GetUnPublishWorkflowCustomModel(ctx context.Context, workflowIDs []string) (
	[]*entity.WorkflowCustomModel, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowCustomModel|workflowIDs: %+v", workflowIDs)
	wfCustomModels := make([]*entity.WorkflowCustomModel, 0)
	if len(workflowIDs) == 0 {
		return wfCustomModels, nil
	}
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowCustomModel{}.TableName()).
		Where("f_workflow_id IN ? AND f_release_status != ?",
			workflowIDs, entity.WorkflowReleaseStatusPublished).
		Find(&wfCustomModels).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowCustomModel workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowCustomModel success, len(wfCustomModels): %d",
		len(wfCustomModels))
	return wfCustomModels, nil
}

// GetUnPublishWorkflowEntry 查询 t_entry 待发布的数据
func (d dao) GetUnPublishWorkflowEntry(ctx context.Context, parameterIDs []string) ([]*entity.WorkflowEntry, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowEntry, parameterIDs: %+v", parameterIDs)
	workflowEntries := make([]*entity.WorkflowEntry, 0)
	if len(parameterIDs) == 0 {
		return workflowEntries, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.WorkflowEntryColumns.ParameterID, entity.WorkflowEntryColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowEntry{}.TableName()).
		Where(query, parameterIDs, entity.ReleaseStatusPublished).
		Find(&workflowEntries).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowEntry workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowEntry success, len(workflowEntries): %d", len(workflowEntries))
	return workflowEntries, nil
}

// GetUnPublishWorkflowInvalidEntry 查询 t_invalid_entry 待发布的数据
func (d dao) GetUnPublishWorkflowInvalidEntry(ctx context.Context, parameterIDs []string) (
	[]*entity.WorkflowInvalidEntry, error) {
	log.InfoContextf(ctx, "GetUnPublishWorkflowInvalidEntry, parameterIDs: %+v", parameterIDs)
	workflowInvalidEntries := make([]*entity.WorkflowInvalidEntry, 0)
	if len(parameterIDs) == 0 {
		return workflowInvalidEntries, nil
	}
	query := fmt.Sprintf("%s IN ? AND %s != ?",
		entity.WorkflowInvalidEntryColumns.ParameterID, entity.WorkflowInvalidEntryColumns.ReleaseStatus)
	err := d.workflowDB.WithContext(ctx).Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where(query, parameterIDs, entity.ReleaseStatusPublished).
		Find(&workflowInvalidEntries).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUnPublishWorkflowInvalidEntry workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUnPublishWorkflowInvalidEntry success, len(workflowInvalidEntries): %d",
		len(workflowInvalidEntries))
	return workflowInvalidEntries, nil
}

func (d dao) IsWorkflowPublishedById(ctx context.Context, robotID, workflowId string, envType uint32) (bool, error) {
	log.InfoContextf(ctx, "IsWorkflowPublishedById|robotID:%s|workflowId:%s|envType: %d",
		robotID, workflowId, envType)
	if len(robotID) == 0 || len(workflowId) == 0 {
		return false, nil
	}
	var db *gorm.DB
	var query string
	var args []interface{}
	switch KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType)) {
	case KEP.GetTaskFlowReleaseStatusReq_TEST: // 测试环境
		db = d.workflowDB
		query = fmt.Sprintf("%s = ? AND %s = ? AND %s IN ?",
			entity.WorkflowColumns.RobotID, entity.WorkflowColumns.WorkflowID, entity.WorkflowColumns.WorkflowState)
		args = append(args, robotID, workflowId)
		args = append(args, []string{
			entity.WorkflowStateEnable, entity.WorkflowStatePublishedChange})
	case KEP.GetTaskFlowReleaseStatusReq_PROD: // 线上环境
		db = d.workflowProdDB
		query = fmt.Sprintf("%s = ? AND %s = ? AND %s = ?",
			entity.WorkflowColumns.RobotID, entity.WorkflowColumns.WorkflowID, entity.WorkflowColumns.ReleaseStatus)
		args = append(args, robotID, workflowId)
		args = append(args, entity.ReleaseStatusPublished)
	default:
		log.ErrorContextf(ctx, "IsWorkflowPublishedById, envType: %d illegal", envType)
		return false, fmt.Errorf("envType: %d illegal", envType)
	}

	var count int64
	err := db.WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(query, args...).Count(&count).Error
	if err != nil {
		log.ErrorContextf(ctx, "CountPublishedWorkflows db.Count err: %+v", err)
		return false, err
	}
	log.InfoContextf(ctx, "CountPublishedWorkflows success, count: %d", count)
	if count > 0 {
		return true, nil
	}

	return false, nil
}

// CountPublishedWorkflows 统计应用已发布工作流数量
func (d dao) CountPublishedWorkflows(ctx context.Context, robotID string, envType uint32) (int64, error) {
	log.InfoContextf(ctx, "CountPublishedWorkflows, robotID: %s, envType: %d", robotID, envType)
	if len(robotID) == 0 {
		return 0, nil
	}
	var db *gorm.DB
	var query string
	var args []interface{}
	switch KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType)) {
	case KEP.GetTaskFlowReleaseStatusReq_TEST: // 测试环境
		db = d.workflowDB
		query = fmt.Sprintf("%s = ? AND %s IN ?",
			entity.WorkflowColumns.RobotID, entity.WorkflowColumns.WorkflowState)
		args = append(args, robotID)
		args = append(args, []string{
			entity.FlowStateEnable, entity.FlowStateChangeUnPublish, entity.FlowStatePublishChange})
	case KEP.GetTaskFlowReleaseStatusReq_PROD: // 线上环境
		db = d.workflowProdDB
		query = fmt.Sprintf("%s = ? AND %s = ?",
			entity.WorkflowColumns.RobotID, entity.WorkflowColumns.ReleaseStatus)
		args = append(args, robotID)
		args = append(args, entity.ReleaseStatusPublished)
	default:
		log.ErrorContextf(ctx, "CountPublishedWorkflows, envType: %d illegal", envType)
		return 0, fmt.Errorf("envType: %d illegal", envType)
	}

	var count int64
	err := db.WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(query, args...).Count(&count).Error
	if err != nil {
		log.ErrorContextf(ctx, "CountPublishedWorkflows db.Count err: %+v", err)
		return 0, err
	}
	log.InfoContextf(ctx, "CountPublishedWorkflows success, count: %d", count)
	return count, nil
}

// GetPublishedWorkflows 查询 t_workflow 发布后的数据
func (d dao) GetPublishedWorkflows(ctx context.Context, robotID string, workflowIDs []string) (
	[]*entity.Workflow, error) {
	log.InfoContextf(ctx, "GetPublishedWorkflows, robotID: %s, workflowIDs: %+v", robotID, workflowIDs)
	workflows := make([]*entity.Workflow, 0)
	if len(workflowIDs) == 0 {
		return workflows, nil
	}
	query := fmt.Sprintf("%s = ? AND %s IN ? AND %s = ?",
		entity.WorkflowColumns.RobotID, entity.WorkflowColumns.WorkflowID, entity.WorkflowColumns.ReleaseStatus)
	err := d.workflowProdDB.WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(query, robotID, workflowIDs, entity.ReleaseStatusPublished).
		Find(&workflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetPublishedWorkflows workflowDB.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetPublishedWorkflows success, len(workflows): %d", len(workflows))
	return workflows, nil
}
