package publish

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"gorm.io/gorm"
)

// UpdateWorkflowStaffID 更新 t_workflow 用户ID
func (d dao) UpdateWorkflowStaffID(ctx context.Context, db *gorm.DB,
	workflows []*entity.Workflow, staffID uint64) error {
	log.InfoContextf(ctx, "UpdateWorkflowStaffID, len(workflows):%d, staffID:%v",
		len(workflows), staffID)
	if len(workflows) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflows))
	for _, workflow := range workflows {
		ids = append(ids, workflow.ID)
	}
	err := db.Debug().WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowColumns.StaffID: staffID,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowStaffID db.Updates err:%+v", err)
		return err
	}
	// 这里需要重置工作流StaffID
	for _, workflow := range workflows {
		workflow.StaffID = staffID
	}
	log.InfoContextf(ctx, "UpdateWorkflowStaffID success")
	return nil
}

// UpdateWorkflowReleaseStatus 更新 t_workflow 发布状态
func (d dao) UpdateWorkflowReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflows []*entity.Workflow, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowReleaseStatus, len(workflows):%d, releaseStatus:%s",
		len(workflows), releaseStatus)
	if len(workflows) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflows))
	for _, workflow := range workflows {
		ids = append(ids, workflow.ID)
	}
	err := tx.WithContext(ctx).Table(entity.Workflow{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowReleaseStatus success")
	return nil
}

// UpdateRobotWorkflowReleaseStatus 更新 t_robot_workflow 发布状态
func (d dao) UpdateRobotWorkflowReleaseStatus(ctx context.Context, tx *gorm.DB,
	robotWorkflows []*entity.RobotWorkflow, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateRobotWorkflowReleaseStatus, len(robotWorkflows):%d, releaseStatus:%s",
		len(robotWorkflows), releaseStatus)
	if len(robotWorkflows) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(robotWorkflows))
	for _, robotWorkflow := range robotWorkflows {
		ids = append(ids, robotWorkflow.ID)
	}
	err := tx.WithContext(ctx).Table(entity.RobotWorkflow{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.RobotWorkflowColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.RobotWorkflowColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateRobotWorkflowReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateRobotWorkflowReleaseStatus success")
	return nil
}

// UpdateWorkflowExampleReleaseStatus 更新 t_workflow_example 发布状态
func (d dao) UpdateWorkflowExampleReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowExamples []*entity.WorkflowExample, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowExampleReleaseStatus, len(workflowExamples):%d, releaseStatus:%s",
		len(workflowExamples), releaseStatus)
	if len(workflowExamples) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowExamples))
	for _, workflowExample := range workflowExamples {
		ids = append(ids, workflowExample.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowExample{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowExampleColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowExampleColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowExampleReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowExampleReleaseStatus success")
	return nil
}

// UpdateWorkflowVarReleaseStatus 更新 t_workflow_var 发布状态
func (d dao) UpdateWorkflowVarReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowVars []*entity.WorkflowVar, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowVarReleaseStatus, len(workflowVars):%d, releaseStatus:%s",
		len(workflowVars), releaseStatus)
	if len(workflowVars) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowVars))
	for _, workflowVar := range workflowVars {
		ids = append(ids, workflowVar.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowVar{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowVarColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowVarColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowVarReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowVarReleaseStatus success")
	return nil
}

// UpdateVarParamsReleaseStatus 更新 t_var 发布状态
func (d dao) UpdateVarParamsReleaseStatus(ctx context.Context, tx *gorm.DB,
	varParams []*entity.VarParams, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateVarParamsReleaseStatus, len(varParams):%d, releaseStatus:%s",
		len(varParams), releaseStatus)
	if len(varParams) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(varParams))
	for _, varParam := range varParams {
		ids = append(ids, varParam.ID)
	}
	err := tx.WithContext(ctx).Table(entity.VarParams{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.VarParamsColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.VarParamsColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateVarParamsReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateVarParamsReleaseStatus success")
	return nil
}

// UpdateWorkflowParameterReleaseStatus 更新 t_workflow_parameter 发布状态
func (d dao) UpdateWorkflowParameterReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowParameters []*entity.WorkflowParameter, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowParameterReleaseStatus, len(workflowParameters):%d, releaseStatus:%s",
		len(workflowParameters), releaseStatus)
	if len(workflowParameters) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowParameters))
	for _, workflowParameter := range workflowParameters {
		ids = append(ids, workflowParameter.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowParameter{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowParameterColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowParameterColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowParameterReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowParameterReleaseStatus success")
	return nil
}

// UpdateParameterReleaseStatus 更新 t_parameter 发布状态
func (d dao) UpdateParameterReleaseStatus(ctx context.Context, tx *gorm.DB,
	parameters []*entity.Parameter, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateParameterReleaseStatus, len(parameters):%d, releaseStatus:%s",
		len(parameters), releaseStatus)
	if len(parameters) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(parameters))
	for _, parameter := range parameters {
		ids = append(ids, parameter.ID)
	}
	err := tx.WithContext(ctx).Table(entity.Parameter{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.ParameterColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.ParameterColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateParameterReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateParameterReleaseStatus success")
	return nil
}

// UpdateWorkflowEntryReleaseStatus 更新 t_entry 待发布的数据
func (d dao) UpdateWorkflowEntryReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowEntries []*entity.WorkflowEntry, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowEntryReleaseStatus, len(workflowEntries):%d, releaseStatus:%s",
		len(workflowEntries), releaseStatus)
	if len(workflowEntries) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowEntries))
	for _, workflowEntry := range workflowEntries {
		ids = append(ids, workflowEntry.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowEntry{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowEntryColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowEntryColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowEntryReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowEntryReleaseStatus success")
	return nil
}

// UpdateWorkflowInvalidEntryReleaseStatus 更新 t_invalid_entry 发布状态
func (d dao) UpdateWorkflowInvalidEntryReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowInvalidEntries []*entity.WorkflowInvalidEntry, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowInvalidEntryReleaseStatus, len(workflowInvalidEntries):%d, "+
		"releaseStatus:%s", len(workflowInvalidEntries), releaseStatus)
	if len(workflowInvalidEntries) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowInvalidEntries))
	for _, workflowInvalidEntry := range workflowInvalidEntries {
		ids = append(ids, workflowInvalidEntry.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowInvalidEntry{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowInvalidEntryColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowInvalidEntryColumns.ReleaseStatus: releaseStatus,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowInvalidEntryReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowInvalidEntryReleaseStatus success")
	return nil
}

// UpdateWorkflowCustomModelReleaseStatus 更新 t_workflow_custom_model 发布状态
func (d dao) UpdateWorkflowCustomModelReleaseStatus(ctx context.Context, tx *gorm.DB,
	workflowCustomModels []*entity.WorkflowCustomModel, releaseStatus string) error {
	log.InfoContextf(ctx, "UpdateWorkflowParameterReleaseStatus, len(workflowCustomModels):%d, releaseStatus:%s",
		len(workflowCustomModels), releaseStatus)
	if len(workflowCustomModels) == 0 {
		return nil
	}
	ids := make([]uint64, 0, len(workflowCustomModels))
	for _, v := range workflowCustomModels {
		ids = append(ids, v.ID)
	}
	err := tx.WithContext(ctx).Table(entity.WorkflowCustomModel{}.TableName()).
		Where(fmt.Sprintf("%s IN ?", entity.WorkflowCustomModelColumns.ID), ids).
		Updates(map[string]interface{}{
			entity.WorkflowCustomModelColumns.ReleaseStatus: releaseStatus,
			entity.WorkflowCustomModelColumns.StaffID:       util.StaffID(ctx),
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowCustomModelReleaseStatus tx.Updates err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "UpdateWorkflowCustomModelReleaseStatus success")
	return nil
}
