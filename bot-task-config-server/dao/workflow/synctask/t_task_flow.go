// bot-task-config-server
//
// @(#)t_task_flow.go  Monday, December 25, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// CountUnpublishedTaskFlow 统计未发布的任务流数量
func CountUnpublishedTaskFlow(ctx context.Context, robotID string) int64 {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CountUnpublishedTaskFlow|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var c int64
	g.Table("t_workflow").
		Where("f_robot_id = ?", robotID).
		Where("f_release_status IN ?", []string{entity.ReleaseStatusUnPublished, entity.ReleaseStatusFail}).
		Where("f_flow_state IN ?", []string{entity.FlowStateEnable,
			entity.FlowStateChangeUnPublish, entity.FlowStatePublishChange}).
		Count(&c)
	log.Infof("O|CountUnpublishedTaskFlow|%s|%d|ERR:%v|%s", sid, c, g.Error, time.Since(t0))
	return c
}

// ListUnpublishedTaskFlow 查询未发布的任务
func ListUnpublishedTaskFlow(ctx context.Context, robotID string, status []string) ([]entity.Workflow, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListUnpublishedTaskFlow|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var data []entity.Workflow
	g.Table("t_workflow").
		Select("f_id", "f_workflow_id", "f_workflow_name", "f_desc", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_draft", "f_dialog_json_enable", "f_uin", "f_sub_uin", "f_staff_id",
			"f_release_status", "f_is_deleted", "f_action", "f_is_enable", "f_create_time", "f_update_time",
			"f_proto_version").
		Where("f_robot_id = ?", robotID).
		Where("f_release_status IN ?", status).
		Where("f_flow_state IN ?", []string{entity.FlowStateEnable,
			entity.FlowStateChangeUnPublish, entity.FlowStatePublishChange}).
		Find(&data)
	log.Infof("O|ListUnpublishedTaskFlow|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// UpdateTaskFlowReleaseStatus ...
func UpdateTaskFlowReleaseStatus(ctx context.Context, robotID string, flowIDs []string, releaseStatus string) error {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|UpdateTaskFlowReleaseStatus|%s|%v|%v|%v", sid, robotID, flowIDs, releaseStatus)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	g.Table("t_workflow").
		Where("f_robot_id = ?", robotID).
		Where("f_workflow_id IN ?", flowIDs).
		Update("f_release_status", releaseStatus)
	log.Infof("O|UpdateTaskFlowReleaseStatus|%s|ERR:%v|%s", sid, g.Error, time.Since(t0))
	return g.Error
}

// CountUnpublishedTaskFlowWithQuery 查询未发布的任务数量
func CountUnpublishedTaskFlowWithQuery(ctx context.Context, robotID string, params entity.ListWorkflowParams) int64 {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|CountUnpublishedTaskFlowWithQuery|%s|%v", sid, robotID)

	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug().
		Table("t_workflow").Where("f_robot_id = ?", robotID)

	if len(params.Query) > 0 {
		g = g.Where("f_workflow_name LIKE ?", "%"+types.ConvertSqlFuzzySearch(params.Query)+"%")
	}

	if len(params.FlowState) > 0 {
		g = g.Where("f_flow_state IN ?", params.FlowState)
	}
	if len(params.ReleaseStatus) > 0 {
		g = g.Where("f_release_status IN ?", params.ReleaseStatus)
	}
	if len(params.Actions) > 0 {
		g = g.Where("f_action IN ?", params.Actions)
	}
	if !params.StartTime.IsZero() {
		g = g.Where("f_update_time >= ?", params.StartTime)
	}
	if !params.EndTime.IsZero() {
		g = g.Where("f_update_time <= ?", params.EndTime)
	}
	var c int64
	g.Count(&c)
	log.Infof("O|CountUnpublishedTaskFlowWithQuery|%s|%d|ERR:%v|%s", sid, c, g.Error, time.Since(t0))
	return c
}

// ListUnpublishedTaskFlowWithQuery 查询未发布的任务
func ListUnpublishedTaskFlowWithQuery(ctx context.Context, robotID string, params entity.ListWorkflowParams) (
	[]entity.Workflow, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListUnpublishedTaskFlow|%s|%v", sid, robotID)
	g := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	var data []entity.Workflow
	g = g.Table("t_workflow").
		Select("f_id", "f_workflow_id", "f_workflow_name", "f_desc", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_draft", "f_dialog_json_enable", "f_uin", "f_sub_uin", "f_staff_id",
			"f_release_status", "f_is_deleted", "f_action", "f_is_enable", "f_create_time", "f_update_time",
			"f_proto_version").
		Where("f_robot_id = ?", robotID)

	if len(params.Query) > 0 {
		g = g.Where("f_workflow_name LIKE ?", "%"+types.ConvertSqlFuzzySearch(params.Query)+"%")
	}

	if len(params.FlowState) > 0 {
		g = g.Where("f_flow_state IN ?", params.FlowState)
	}
	if len(params.ReleaseStatus) > 0 {
		g = g.Where("f_release_status IN ?", params.ReleaseStatus)
	}
	if len(params.Actions) > 0 {
		g = g.Where("f_action IN ?", params.Actions)
	}
	if !params.StartTime.IsZero() {
		g = g.Where("f_update_time >= ?", params.StartTime)
	}
	if !params.EndTime.IsZero() {
		g = g.Where("f_update_time <= ?", params.EndTime)
	}
	g = g.Order("f_update_time DESC")
	pageSize := uint32(15)
	page := uint32(1)
	if params.PageSize > 0 {
		pageSize = params.PageSize
	}
	if params.Page > 0 {
		page = params.Page
	}
	offset := (page - 1) * pageSize
	g.Offset(int(offset)).Limit(int(pageSize)).Find(&data)
	log.Infof("O|ListUnpublishedTaskFlow|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}

// ListTaskFlowByIDs 按 id 查对应的任务流程
func ListTaskFlowByIDs(ctx context.Context, db *gorm.DB, robotID string, ids []string) ([]entity.Workflow, error) {
	t0 := time.Now()
	sid := util.RequestID(ctx)
	log.Infof("I|ListTaskFlowByIDs|%s|%v|%v|%v|db:%p", sid, robotID, len(ids), ids, db)
	if len(ids) == 0 {
		return nil, nil
	}
	g := db.WithContext(ctx).Debug()

	var data []entity.Workflow
	g.Table("t_workflow").
		Select("f_id", "f_workflow_id", "f_workflow_name", "f_desc", "f_flow_state", "f_version",
			"f_robot_id", "f_dialog_json_draft", "f_dialog_json_enable", "f_uin", "f_sub_uin", "f_staff_id",
			"f_release_status", "f_is_deleted", "f_action", "f_is_enable", "f_create_time", "f_update_time",
			"f_proto_version").
		Where("f_robot_id = ?", robotID).
		Where("f_workflow_id IN ?", ids).
		Find(&data)
	log.Infof("O|ListTaskFlowByIDs|%s|len:%d|%v|ERR:%v|%s", sid, len(data), data, g.Error, time.Since(t0))
	return data, g.Error
}
