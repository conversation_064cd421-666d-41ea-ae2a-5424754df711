// bot-task-config-server
//
// @(#)app.go  星期二, 二月 27, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package entity

import (
	"time"

	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ModelType 模型类型
type ModelType string

const (
	AppSharedUnDeleted = 0

	// AppTypeKnowledgeQA 知识问答
	AppTypeKnowledgeQA string = "knowledge_qa"
	// AppTypeSummary 知识摘要
	AppTypeSummary string = "summary"
	// AppTypeClassify 知识标签提取
	AppTypeClassify string = "classify"

	ModelTypeMessage          ModelType = "normal_message"    // 知识问答
	ModelTypeSummaryRecognize ModelType = "summary_recognize" // 知识摘要
	ModelTypeClassifyExtract  ModelType = "classify_extract"  // 标签提取
)

const (
	AppPatternStandard       = "standard"        // 标准模式
	AppPatternAgent          = "agent"           // agent模式
	AppPatternSingleWorkflow = "single_workflow" // 单工作流模式
)

// GetAppModelName 获取应用模型名称
func GetAppModelName(app *pb.GetAppInfoRsp) string {
	var m *pb.AppModelInfo
	var ok bool

	switch app.GetAppType() {
	case AppTypeKnowledgeQA:
		m, ok = app.GetKnowledgeQa().GetModel()[string(ModelTypeMessage)]
	case AppTypeSummary:
		m, ok = app.GetSummary().GetModel()[string(ModelTypeSummaryRecognize)]
	case AppTypeClassify:
		m, ok = app.GetClassify().GetModel()[string(ModelTypeClassifyExtract)]
	}
	var mn string
	if ok {
		mn = m.GetModelName()
	}
	return mn

}

// AppShared 用户体验链接分享记录
type AppShared struct {
	ID          uint64    `gorm:"column:f_id"`                                          // 主键ID
	AppID       string    `gorm:"column:f_app_id"`                                      // 应用ID
	SharedCode  string    `gorm:"column:f_shared_code"`                                 // 分享码
	IsDeleted   uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateUser  string    `gorm:"column:f_create_user"`                                 // 创建用户
	UpdateUser  string    `gorm:"column:f_update_user"`                                 // 更新用户
	CreateTime  time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime  time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
	ExpiredTime time.Time `gorm:"column:f_expire_time;type:datetime"`                   // 过期时间
}

// TableName ...
func (t AppShared) TableName() string {
	return "t_app_shared"
}
