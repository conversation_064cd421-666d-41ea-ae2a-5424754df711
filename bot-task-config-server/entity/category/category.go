// bot-task-config-server
//
// @(#)category.go  星期四, 十二月 14, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package category

import (
	"time"
)

const (
	// AllCateID 全部分类业务ID
	AllCateID   = "0"
	ParentAllID = "0" // 全部的父分类
	// AllCateName 全部分类名称
	AllCateName = "全部分类"
	// UncategorizedCateName 未分类名称
	UncategorizedCateName = "未分类"

	// CateIsNotDeleted 未删除
	CateIsNotDeleted = 0
	// CateIsDeleted 已删除
	CateIsDeleted = 1
)

var TCategoryFeature = struct {
	Flow string
	Doc  string
}{
	Flow: "FLOW",
	Doc:  "DOC",
}

// TCategoryColumns get sql column name.获取数据库列名
var TCategoryColumns = struct {
	ID           string
	CategoryID   string
	CategoryName string
	RobotID      string
	Level        string
	ParentID     string
	Feature      string
	OrderNum     string
	Uin          string
	SubUin       string
	IsDeleted    string
	CreateTime   string
	UpdateTime   string
}{
	ID:           "f_id",
	CategoryID:   "f_category_id",
	CategoryName: "f_category_name",
	RobotID:      "f_robot_id",
	Level:        "f_level",
	ParentID:     "f_parent_id",
	Feature:      "f_feature",
	OrderNum:     "f_order_number",
	Uin:          "f_uin",
	SubUin:       "f_sub_uin",
	IsDeleted:    "f_is_deleted",
	CreateTime:   "f_create_time",
	UpdateTime:   "f_update_time",
}

// Category 分类
type Category struct {
	ID           uint64    `gorm:"column:f_id"`
	CategoryID   string    `gorm:"column:f_category_id"`   // 业务ID
	CategoryName string    `gorm:"column:f_category_name"` // 目录名称
	RobotID      string    `gorm:"column:f_robot_id"`
	Level        uint32    `gorm:"column:f_level"`
	ParentID     string    `gorm:"column:f_parent_id"` // 父级 ID
	Feature      string    `gorm:"column:f_feature"`
	OrderNum     uint32    `gorm:"column:f_order_number"`                                // 同层级title的排序编号
	Uin          string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin       string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	IsDeleted    uint32    `gorm:"column:f_is_deleted"`                                  // 0未删除 1已删除
	CreateTime   time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime   time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// CateStat 按分类统计
type CateStat struct {
	CategoryID string `gorm:"column:f_category_id"` // 分类ID
	Total      uint32 `gorm:"column:total"`
}

// HasDeleted 是否已删除
func (c Category) TableName() string {
	return "t_category"
}

// HasDeleted 是否已删除
func (c *Category) HasDeleted() bool {
	if c == nil {
		return false
	}
	return c.IsDeleted == CateIsDeleted
}

// IsUncategorized 是否为一级未分类
func (c *Category) IsUncategorized() bool {
	return c.CategoryName == UncategorizedCateName && c.ParentID == AllCateID
}
