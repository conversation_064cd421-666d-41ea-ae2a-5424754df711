// bot-task-config-server
//
// @(#)category_tree.go  星期四, 十二月 14, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package category

import (
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"sort"
	"strconv"
	"strings"

	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// CateNode 分类节点
type CateNode struct {
	*Category
	Depth    uint // 下钻
	Children []*CateNode
}

// ToPbCateTree 转为 PB 分类树
func (t *CateNode) ToPbCateTree(stat map[string]uint32) *pb.ListCategoryRsp_Cate {
	if t == nil {
		return nil
	}
	var children []*pb.ListCategoryRsp_Cate
	var canDelete bool

	for _, node := range t.Children {
		children = append(children, node.ToPbCateTree(stat))
	}
	cId := t.CategoryID
	total := stat[cId]
	for _, cid := range t.ChildrenIDs() {
		total += stat[cid]
	}

	if cId == AllCateID {
		return &pb.ListCategoryRsp_Cate{
			CateBizId: AllCateID,
			Name:      AllCateName,
			Total:     total,
			CanAdd:    true,
			Children:  children,
		}
	}
	if t.IsUncategorized() {
		canDelete = false
	} else {
		canDelete = true
	}

	return &pb.ListCategoryRsp_Cate{
		CateBizId: t.CategoryID,
		Name:      t.CategoryName,
		Total:     total,
		Children:  children,
		CanAdd:    !t.IsUncategorized() && t.Depth < uint(len(config.GetMainConfig().TaskFlow.CategoryHead)-1),
		CanEdit:   !t.IsUncategorized(),
		CanDelete: canDelete,
	}
}

// Create 根据路径给出需要创建的分类
func (t *CateNode) Create(path []string) []*Category {
	newCategorys := make([]*Category, 0)
	cur := t
next:
	for _, p := range path {
		for _, node := range cur.Children {
			if node.CategoryName == p {
				cur = node
				continue next
			}
		}
		node := &CateNode{&Category{CategoryName: p, ParentID: cur.CategoryID}, cur.Depth + 1, nil}
		newCategorys = append(newCategorys, node.Category)
		cur.Children = append(cur.Children, node)
		cur = node
	}
	return newCategorys
}

// NodeCount 计算节点总数
func (t *CateNode) NodeCount() int {
	var c int
	for _, child := range t.Children {
		c += child.NodeCount()
	}
	return c + 1
}

// Find 根据路径找节点
func (t *CateNode) Find(path []string) int64 {
	if t == nil {
		return -1
	}
	cId := StrToUint64(t.CategoryID)
	if len(path) == 0 {
		return int64(cId) //t.ID
	}
	for _, node := range t.Children {
		if node.CategoryName == path[0] {
			return node.Find(path[1:])
		}
	}
	return -1
}

// FindNode 根据 ID 获取它在分类树中的节点
func (t *CateNode) FindNode(cid uint64) *CateNode {
	if t == nil {
		return nil
	}
	//cateId, _ := strconv.ParseUint(t.CategoryID, 10, 64)
	cateId := StrToUint64(t.CategoryID)

	if cateId == cid { //|| t.ID == id
		return t
	}
	for _, node := range t.Children {
		if n := node.FindNode(cid); n != nil {
			return n
		}
	}
	return nil
}

// IsPathExist 判断路径是否存在
func (t *CateNode) IsPathExist(path []string) bool {
	return t.Find(path) != -1
}

// IsNameDuplicate 判断分类名称在子节点中是否重复
func (t *CateNode) IsNameDuplicate(name string) bool {
	if t == nil {
		return false
	}
	// 如果不可创建未分类，放开注释
	//if t.Depth == 0 && strings.TrimSpace(name) == UncategorizedCateName {
	//	return true
	//}
	for _, node := range t.Children {
		if strings.TrimSpace(node.CategoryName) == strings.TrimSpace(name) {
			return true
		}
	}
	return false
}

// ChildrenIDs 获取某分类下所有子分类的 ID
func (t *CateNode) ChildrenIDs() []string {
	if t == nil {
		return nil
	}
	var cids []string
	for _, node := range t.Children {
		ncId := node.CategoryID //StrToUint64(node.CategoryID)
		cids = append(cids, ncId)
		cids = append(cids, node.ChildrenIDs()...)
	}
	return cids
}

// Path 输出分类节点路径
func (t *CateNode) Path(cid uint64) []string {
	if t == nil {
		return nil
	}
	for _, node := range t.Children {
		ncId := StrToUint64(node.CategoryID)
		if ncId == cid { // node.ID
			return []string{node.CategoryName}
		}
		path := node.Path(cid)
		if len(path) > 0 {
			return append([]string{node.CategoryName}, path...)
		}
	}
	return nil
}

// GetUncategorizedID 获取未分类的分类 ID
func GetUncategorizedID(cates []*Category) string {
	for _, cate := range cates {
		if cate.IsUncategorized() {
			return cate.CategoryID
		}
	}
	// 兜底return0 理论上不会走到这里
	return "0"
}

// BuildCateTree 构造分类树
func BuildCateTree(cates []*Category) *CateNode {
	m := make(map[uint64][]*Category, len(cates))
	for _, cate := range cates {
		parentId := StrToUint64(cate.ParentID)
		m[parentId] = append(m[parentId], cate)
	}
	//TODO... 根分类的定义：
	return build(m, &Category{ID: 1, CategoryID: AllCateID, CategoryName: AllCateName, ParentID: ParentAllID}, 0)
}

func build(cates map[uint64][]*Category, parent *Category, depth uint) *CateNode {
	var nodes []*CateNode
	pcId := StrToUint64(parent.CategoryID)
	for _, cate := range cates[pcId] {
		nodes = append(nodes, build(cates, cate, depth+1))
	}
	sort.SliceStable(nodes, func(i, j int) bool {
		if nodes[i].OrderNum == nodes[j].OrderNum {
			icId := StrToUint64(nodes[i].CategoryID)
			jcId := StrToUint64(nodes[j].CategoryID)
			return icId > jcId
		}
		return nodes[i].OrderNum < nodes[j].OrderNum
	})
	return &CateNode{parent, depth, nodes}
}

// GetCatePath 提取有效分类路径
func GetCatePath(row []string) (bool, []string) {
	if len(row) < len(config.GetMainConfig().TaskFlow.CategoryHead)-1 {
		return false, nil
	}
	stop := -1
	for i := len(config.GetMainConfig().TaskFlow.CategoryHead) - 2; i >= 0; i-- {
		text := strings.TrimSpace(row[i])
		if text != "" {
			if stop == -1 {
				stop = i
			}
		} else if stop != -1 {
			return false, nil
		}
	}

	cates := row[0 : stop+1]
	for i := range cates {
		cates[i] = strings.TrimSpace(cates[i])
	}
	return true, cates
}

func StrToUint64(str string) uint64 {
	// 将字符串转换为 uint64
	uint64Value, err := strconv.ParseUint(str, 10, 64)
	//log.Infof("\nStrToUint64:==%s, %d\n", str, uint64Value)
	if err != nil {
		//log.Errorf("\nStrToUint64, error:%s\n", err.Error())
		return 0
	}
	return uint64Value
}
