package category

import (
	"fmt"
	"math/rand"
	"strconv"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
)

func When[V any](cond bool, yes, no V) V {
	if cond {
		return yes
	}
	return no
}
func Test_BuildCateTree(t *testing.T) {
	//t.Logf("%s", spew.Sdump(BuildCateTree(nil)))
	tree := BuildCateTree([]*Category{
		{ID: 1, CategoryID: "11", CategoryName: "1", OrderNum: 1, ParentID: "0"},
		{ID: 2, CategoryID: "22", CategoryName: "2", OrderNum: 2, ParentID: "0"},
		{ID: 3, CategoryID: "33", CategoryName: "3", OrderNum: 3, ParentID: "22"},
		{ID: 4, CategoryID: "44", CategoryName: "4", OrderNum: 4, ParentID: "33"},
		{ID: 5, CategoryID: "55", CategoryName: "5", OrderNum: 5, ParentID: "11"},
		{ID: 6, CategoryID: "66", CategoryName: "6", OrderNum: 6, ParentID: "11"},
		{ID: 7, CategoryID: "77", CategoryName: "7", OrderNum: 8, ParentID: "11"},
		{ID: 8, CategoryID: "88", CategoryName: "8", OrderNum: 7, ParentID: "11"},
		{ID: 9, CategoryID: "99", CategoryName: "9", OrderNum: 9, ParentID: "77"},
	})
	t.Logf("%s", spew.Sdump(tree))
	t.Logf("%+v", tree.Path(99))
	t.Logf("%+v", tree.IsPathExist([]string{"1", "7", "9"}))
	t.Logf("%+v", tree.FindNode(11).ChildrenIDs()) //55,66,77,88,99
	t.Logf("%+v", tree.IsPathExist([]string{"1", "10", "11"}))
	tree.Create([]string{"1", "10"})
	t.Logf("%+v", tree.IsPathExist([]string{"1", "10", "11"}))
	tree.Create([]string{"1", "10", "11"})
	t.Logf("%+v", tree.IsPathExist([]string{"1", "10", "11"}))
	tree.Create([]string{"1", "10", "12"})
	t.Logf("%+v", tree.IsPathExist([]string{"1", "10", "12"}))
	t.Logf("%s", spew.Sdump(tree.FindNode(99)))
}

func Test_buildLarge(t *testing.T) {
	size := 1000000
	cates := make([]*Category, 0, size)
	for i := 1; i <= size; i++ {
		cates = append(cates, &Category{
			//ID:       uint64(i),
			CategoryID:   fmt.Sprintf("%d", i),
			CategoryName: strconv.FormatInt(int64(i), 10),
			ParentID:     fmt.Sprintf("%d", When((i-1)%10 == 0, 0, uint64(i-1))),
		})
	}
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(cates), func(i, j int) { cates[i], cates[j] = cates[j], cates[i] })
	tree := BuildCateTree(cates)
	t.Logf("node count: %+v", tree.NodeCount())
}

func Test_getCatePath(t *testing.T) {
	row := []string{"0", "1", "2", "3", "4", "5", "6", "", "", "", ""}
	t.Log(GetCatePath(row))
}

func Test_ToPbCateTree(t *testing.T) {
	tree := BuildCateTree([]*Category{
		{ID: 218, CategoryID: "2218", CategoryName: "一级1", OrderNum: 0, ParentID: "1"},
		{ID: 219, CategoryID: "2219", CategoryName: "二级1", OrderNum: 0, ParentID: "2218"},
		{ID: 220, CategoryID: "2220", CategoryName: "三级1", OrderNum: 0, ParentID: "2219"},
		{ID: 221, CategoryID: "2221", CategoryName: "三级2", OrderNum: 0, ParentID: "2219"},
		{ID: 222, CategoryID: "2222", CategoryName: "二级2", OrderNum: 0, ParentID: "2218"},
		{ID: 223, CategoryID: "2223", CategoryName: "一级2", OrderNum: 0, ParentID: "1"},
		{ID: 224, CategoryID: "2224", CategoryName: "二级2", OrderNum: 0, ParentID: "2223"},
		{ID: 225, CategoryID: "2225", CategoryName: "三级1", OrderNum: 0, ParentID: "2224"},
		{ID: 226, CategoryID: "2226", CategoryName: "四级1", OrderNum: 0, ParentID: "2225"},
	})
	stat := map[string]uint32{
		"1":    3,
		"2220": 1,
		"2221": 1,
		"2222": 1,
		"2225": 1,
		"2226": 1,
	}
	t.Logf("\n%s\n", spew.Sdump(tree.ToPbCateTree(stat)))
	// {
	//    "id": 1,
	//    "name": "全部分类",
	//    "total": 8,
	//    "can_add": true,
	//    "children": {
	//        "id": 223,
	//        "name": "一级2",
	//        "total": 2,
	//        "can_add": true,
	//        "can_edit": true,
	//        "can_delete": true,
	//        "children": {
	//            "id": 224,
	//            "name": "二级2",
	//            "total": 2,
	//            "can_add": true,
	//            "can_edit": true,
	//            "can_delete": true,
	//            "children": {
	//                "id": 225,
	//                "name": "三级1",
	//                "total": 2,
	//                "can_add": true,
	//                "can_edit": true,
	//                "can_delete": true,
	//                "children": {
	//                    "id": 226,
	//                    "name": "四级1",
	//                    "total": 1,
	//                    "can_add": true,
	//                    "can_edit": true,
	//                    "can_delete": true
	//                }
	//            }
	//        }
	//    }
	//}
}
