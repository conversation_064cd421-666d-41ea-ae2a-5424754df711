package entity

import "time"

// Corpus 语料
type Corpus struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	CorpusID      string    `gorm:"column:f_corpus_id"`                                   // 语料ID
	Content       string    `gorm:"column:f_content"`                                     // 语料内容
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	Uin           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (c Corpus) TableName() string {
	return "t_corpus"
}

// TCorpusColumns 语料表列字段
var TCorpusColumns = struct {
	ID            string
	CorpusID      string
	Content       string
	IntentID      string
	Uin           string
	SubUin        string
	IsDeleted     string
	ReleaseStatus string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	CorpusID:      "f_corpus_id",
	Content:       "f_content",
	IntentID:      "f_intent_id",
	Uin:           "f_uin",
	SubUin:        "f_sub_uin",
	IsDeleted:     "f_is_deleted",
	ReleaseStatus: "f_release_status",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// IntentCorpus 意图关联语料(关联示例问法等)
type IntentCorpus struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	CorpusID      string    `gorm:"column:f_corpus_id"`                                   // 语料ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	Corpus        string    `gorm:"column:f_corpus"`                                      // 语料内容(如示例问法)
	RobotId       string    `gorm:"column:f_robot_id"`                                    // 应用机器人Id
	Uin           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (i IntentCorpus) TableName() string {
	return "t_intent_corpus"
}

// IntentCorpusPublishHistory 任务流程与语料问法关联历史发布记录
type IntentCorpusPublishHistory struct {
	ID          uint64    `gorm:"column:f_id"`                                           // 主键ID
	FlowID      string    `gorm:"column:f_flow_id"`                                      // 画布ID
	IntentID    string    `gorm:"column:f_intent_id"`                                    // 意图ID
	CorpusID    string    `gorm:"column:f_corpus_id"`                                    // 语料ID
	Corpus      string    `gorm:"column:f_corpus"`                                       // 语料内容(如示例问法)
	Version     string    `gorm:"column:f_version"`                                      // 任务流版本，  历史版本号 1.0.0-timestamp(时间戳)
	SaveType    int       `gorm:"column:f_save_type"`                                    // 保存的历史类型：1:发布版本、0:草稿版本
	Uin         string    `gorm:"column:f_uin"`                                          // 主用户ID
	SubUin      string    `gorm:"column:f_sub_uin"`                                      // 子用户ID
	IsDeleted   uint32    `gorm:"column:f_is_deleted"`                                   // 是否删除
	PublishTime time.Time `gorm:"column:f_publish_time;type:datetime;null;default:null"` // 发布时间
	CreateTime  time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"`  // 创建时间
	UpdateTime  time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"`  // 更新时间
}

// TableName ...
func (i IntentCorpusPublishHistory) TableName() string {
	return "t_intent_corpus_publish_history"
}
