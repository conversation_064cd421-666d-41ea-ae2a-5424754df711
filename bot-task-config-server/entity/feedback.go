// bot-task-config-server
//
// @(#)feedback.go  星期三, 八月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package entity

import "time"

const (
	// AddWorkFeedbackSuccessNoticeContent 反馈提交通知
	AddWorkFeedbackSuccessNoticeContent = `您对工作流"%s"的反馈已提交，反馈ID为%s`
	// FinishedFeedbackSuccessNoticeContent 反馈处理完成通知
	FinishedFeedbackSuccessNoticeContent = `反馈ID为%s的问题"%s"的反馈已被处理完成`
	// FinishedFeedbackRejectNoticeContent 反馈拒绝通知
	FinishedFeedbackRejectNoticeContent = `你对的问题"%s"的反馈(ID:%s)已被技术人员拒绝`
	// FeedbackStatusCreated 反馈状态-已提交
	FeedbackStatusCreated = 0
	// FeedbackStatusTroubleshooting 反馈状态-定位问题中
	FeedbackStatusTroubleshooting = 1
	// FeedbackStatusProcessing 反馈状态-处理中
	FeedbackStatusProcessing = 2
	// FeedbackStatusInOptimizationQueue 反馈状态-排期优化中
	FeedbackStatusInOptimizationQueue = 3
	// FeedbackStatusRejected 已拒绝
	FeedbackStatusRejected = 9
	// FeedbackStatusFinished 反馈状态-已处理完成 , 设置为10，方便后续插入状态
	FeedbackStatusFinished = 10

	// MaxShowQuestionLength 问题显示的最长字符符数
	MaxShowQuestionLength = 20

	FeedBackTypeFlow = "FLOW"

	FeedbackOtherReason = "其他原因"
)

// AppInfo 应用信息
type AppInfo struct {
	Name    string `json:"Name"`
	AppType string `json:"AppType"`
}

// WorkflowFeedbackReason 工作流反馈理由
type WorkflowFeedbackReason struct {
	ID         uint64    `gorm:"column:f_id"`                                          // 主键ID
	FeedbackId string    `gorm:"column:f_feedback_id"`                                 // 反馈Id
	Reason     string    `gorm:"column:f_reason"`                                      // 反馈理由
	RobotId    string    `gorm:"column:f_robot_id"`                                    // 应用Id
	IsDeleted  uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Uin        string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin     string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	CreateTime time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowFeedbackReason
func (wfb WorkflowFeedbackReason) TableName() string {
	return "t_feedback_reason"
}

// WorkflowFeedbackRecord 工作流反馈记录
type WorkflowFeedbackRecord struct {
	ID              uint64    `gorm:"column:f_id"`                                          // 主键ID
	WorkflowID      string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	BizId           string    `gorm:"column:f_biz_id"`                                      // 反馈业务ID
	RobotId         string    `gorm:"column:f_robot_id"`                                    // 应用ID
	CorpId          uint64    `gorm:"column:f_corp_id"`                                     // 企业ID
	StaffId         uint64    `gorm:"column:f_staff_id"`                                    // 员工ID
	SessionId       string    `gorm:"column:f_session_id"`                                  // 会话SessionId
	RecordId        string    `gorm:"column:f_record_id"`                                   // RecordId 记录Id
	Question        string    `gorm:"column:f_question"`                                    // Question 问题
	Answer          string    `gorm:"column:f_answer"`                                      //  Answer 答案
	Desc            string    `gorm:"column:f_desc"`                                        //  Desc 详细描述
	NodeId          string    `gorm:"column:f_node_id"`                                     //  NodeId 节点ID
	Tapd            string    `gorm:"column:f_tapd"`                                        // Tapd 单URl
	Status          uint      `gorm:"column:f_status"`                                      // 流转状态Status 0:已提交，1：定位问题中，2：处理中，3：排期优化中，9:拒绝，10-已处理完成
	RejectReason    string    `gorm:"column:f_reject_reason"`                               // 拒绝理由
	OptimizedResult string    `gorm:"column:f_optimized_result"`                            // 优化结果
	Uin             string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin          string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	IsDeleted       uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateTime      time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime      time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowFeedbackRecord
func (wfb WorkflowFeedbackRecord) TableName() string {
	return "t_feedback_record"
}

// StatusDesc WorkflowFeedbackRecord
func (wfb WorkflowFeedbackRecord) StatusDesc() string {
	switch wfb.Status {
	case FeedbackStatusCreated:
		return "已提交"
	case FeedbackStatusTroubleshooting:
		return "定位问题中"
	case FeedbackStatusProcessing:
		return "处理中"
	case FeedbackStatusInOptimizationQueue:
		return "排期优化中"
	case FeedbackStatusRejected:
		return "已拒绝"
	case FeedbackStatusFinished:
		return "已处理完成"
	default:
		return ""
	}
}

// FeedReasons ...
type FeedReasons struct {
	FeedbackId string `gorm:"column:f_feedback_id"` // 反馈Id
	Reason     string `gorm:"column:f_reason"`
}
