package entity

import "time"

const (
	// IntentTypeFlow 任务类型 FLOW对话树意图
	IntentTypeFlow = "FLOW"

	// IntentSourceSys 业务来源 系统全局-SYS
	IntentSourceSys = "SYS"
	// IntentSourceGlobal 业务来源 用户全局-GLOBAL
	IntentSourceGlobal = "GLOBAL"
	// IntentSourceIcs 业务来源 智能客服-ICS
	IntentSourceIcs = "ICS"
	// IntentSourceAssist 业务来源 助手-ASSIS
	IntentSourceAssist = "ASSIST"
)

// Intent 意图
type Intent struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	IntentName    string    `gorm:"column:f_intent_name"`                                 // 意图名称
	IntentDesc    string    `gorm:"column:f_intent_desc"`                                 // 意图描述
	IntentType    string    `gorm:"column:f_intent_type"`                                 // 任务类型：FLOW对话树意图 or 非对话树意图
	Source        string    `gorm:"column:f_source"`                                      // 业务来源：系统全局-SYS、用户全局-GLOBAL、智能客服-ICS、助手-ASSIS
	Uin           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (i Intent) TableName() string {
	return "t_intent"
}

// IsInvalidIntentType 是否有效意图类型
func IsInvalidIntentType(flowType string) bool {
	if len(flowType) == 0 || flowType == IntentTypeFlow {
		return true
	}
	return false
}

// IsInvalidIntentSource 是否有效的意图来源
func IsInvalidIntentSource(source string) bool {
	if source != IntentSourceSys && source != IntentSourceGlobal && source != IntentSourceIcs &&
		source != IntentSourceAssist {
		return false
	}
	return true
}

// TIntentColumns 意图表列字段
var TIntentColumns = struct {
	ID            string
	IntentID      string
	IntentName    string
	IntentDesc    string
	IntentType    string
	Source        string
	Uin           string
	SubUin        string
	IsDeleted     string
	ReleaseStatus string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	IntentID:      "f_intent_id",
	IntentName:    "f_intent_name",
	IntentDesc:    "f_intent_desc",
	IntentType:    "f_intent_type",
	Source:        "f_source",
	Uin:           "f_uin",
	SubUin:        "f_sub_uin",
	IsDeleted:     "f_is_deleted",
	ReleaseStatus: "f_release_status",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// IntentFlow 意图任务流程绑定
type IntentFlow struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	FlowID        string    `gorm:"column:f_flow_id"`                                     // 任务流程ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (i IntentFlow) TableName() string {
	return "t_intent_flow"
}

// TIntentFlowColumns 意图任务流程绑定列字段
var TIntentFlowColumns = struct {
	ID            string
	FlowID        string
	IntentID      string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	FlowID:        "f_flow_id",
	IntentID:      "f_intent_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// RobotIntent 机器人意图绑定
type RobotIntent struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (i RobotIntent) TableName() string {
	return "t_robot_intent"
}

// TRobotIntentColumns 机器人意图绑定列字段
var TRobotIntentColumns = struct {
	ID            string
	RobotID       string
	IntentID      string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	RobotID:       "f_robot_id",
	IntentID:      "f_intent_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}
