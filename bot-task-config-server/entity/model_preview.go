package entity

// RequestNodeReq 询问节点求信息
type RequestNodeReq struct {
	// 意图名称
	IntentName string `json:"intent_name"`
	// 槽位信息
	SlotInfo SlotInfo `json:"slot"`
}

// SlotInfo 槽位信息
type SlotInfo struct {
	// 槽位名称
	SlotNames []string `json:"slot_name"`
	// 实体集合
	EntityNames []string `json:"entitys"`
}

//// SpanInfo 富文本格式信息
//type SpanInfo struct {
//	// 意图名称
//	Name string `json:"name"`
//	// 槽位信息
//	Type string `json:"type"`
//}

//// AnswerNodeReq 答案节点请求信息
//type AnswerNodeReq struct {
//	// 意图名称
//	IntentName string `json:"intent_name"`
//	// 槽位名称集合
//	SlotNames []string `json:"slots"`
//	// 条件集合
//	Conditions []Condition `json:"conditions"`
//	// 条件逻辑名称
//	ConditionsLogic string `json:"type"`
//}
//
//// Condition 条件信息
//type Condition struct {
//	// 条件名称
//	ConditionName string `json:"name"`
//	// 条件操作符
//	Operator string `json:"operator"`
//	// 条件值
//	Value string `json:"value"`
//}
