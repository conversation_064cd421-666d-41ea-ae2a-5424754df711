package entity

const (
	// LevelSuccess 成功
	LevelSuccess = "success"
	// LevelWarning 警告
	LevelWarning = "warning"
	// LevelInfo 基础信息
	LevelInfo = "info"
	// LevelError 异常错误
	LevelError = "error"

	// OpTypeViewDetail 查看详情
	OpTypeViewDetail = uint32(1)
	// OpTypeExportDownload 导出下载
	OpTypeExportDownload = uint32(2)

	// NoticeTypeTaskFlowExport 任务流程导出
	NoticeTypeTaskFlowExport = uint32(12)
	// NoticeTypeTaskFlowImport 任务流程导入
	NoticeTypeTaskFlowImport = uint32(13)

	// NoticeTaskFlowPageID 任务流程页ID
	NoticeTaskFlowPageID = uint32(10)

	// NoticeWorkflowPageID 工作流程页ID
	NoticeWorkflowPageID = uint32(20)
	// NoticeTypeWorkflowExport 工作流程导出
	NoticeTypeWorkflowExport = uint32(22)
	// NoticeTypeWorkflowImport 工作流程导入
	NoticeTypeWorkflowImport = uint32(23)
	// NoticeTypeWorkFlowConvert 工作流流程转换
	NoticeTypeWorkFlowConvert = uint32(24)

	// ============ 页面设置 ===============

	// NoticeZeroPageID 无归属页面ID
	NoticeZeroPageID = uint32(0)
)

const (
	// TaskFlowExportNoticeContentIng 任务流程导出中通知内容
	TaskFlowExportNoticeContentIng = "任务流程导出中"
	// TaskFlowExportNoticeContent 任务流程导出通知内容
	TaskFlowExportNoticeContent = "任务流程导出%s"

	// TaskFlowImportNoticeContentIng 任务流程导入通知内容
	TaskFlowImportNoticeContentIng = "%s导入中"
	// TaskFlowImportSuccessNoticeContent 任务流程导入成功通知内容
	TaskFlowImportSuccessNoticeContent = "%s导入成功"
	// TaskFlowImportFailNoticeContent 任务流程导入失败通知内容
	TaskFlowImportFailNoticeContent = "%s导入失败，总共%d条，成功%d条，失败%d条"
	// TaskFlowParentImportFailNoticeContent 任务流程父导入失败通知内容
	TaskFlowParentImportFailNoticeContent = "%s导入失败"
	// EntryImportNoticeContent 词条通用文案
	EntryImportNoticeContent = "共%d条词汇，成功%d条，失败%d条"
)

const (
	// WorkflowExportNoticeContentIng 任务流程导出中通知内容
	WorkflowExportNoticeContentIng = "工作流程导出中"
	// WorkflowExportNoticeContent 任务流程导出通知内容
	WorkflowExportNoticeContent = "工作流程导出%s"

	// 	WorkflowImportNoticeContentIng 任务流程导入通知内容
	WorkflowImportNoticeContentIng = "%s导入中"
	// 	WorkflowImportSuccessNoticeContent 任务流程导入成功通知内容
	WorkflowImportSuccessNoticeContent = "%s导入成功"
	// 	WorkflowImportFailNoticeContent 任务流程导入失败通知内容
	WorkflowImportFailNoticeContent = "%s导入失败，总共%d条，成功%d条，失败%d条"
	// 	WorkflowParentImportFailNoticeContent 任务流程父导入失败通知内容
	WorkflowParentImportFailNoticeContent = "%s导入失败"
	// WfExamImportNoticeContent 导入工作流示例问法通用文案
	WfExamImportNoticeContent = "共%d条示例问法，成功%d条，失败%d条"

	// WorkflowConvertPDLNoticeContentIng 工作流程转换中通知内容
	WorkflowConvertPDLNoticeContentIng = "工作流%s转换中"
	// WorkflowConvertPDLNoticeContent 工作流程转换通知内容
	WorkflowConvertPDLNoticeContent = "工作流%s转换%s"
)
