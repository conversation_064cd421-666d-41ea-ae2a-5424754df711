// bot-task-config-server
//
// @(#)slot.go  星期三, 二月 28, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package entity

import (
	"time"

	jsoniter "github.com/json-iterator/go"
)

const (
	//SlotLevelSYS 实体[槽位] 系统级-SYS
	SlotLevelSYS = "SYS"
	// SlotLevelBOT 实体[槽位] 机器人级-BOT
	SlotLevelBOT = "BOT"
)

const (
	DeleteByEntry = 1 // 人工删除词条
	DeleteBySlot  = 2 // 删除槽位是删除词条
)

var SlotLevelMap = map[string]struct{}{
	SlotLevelSYS: {},
	SlotLevelBOT: {},
}

// FlowInfos 画布信息
type FlowInfos struct {
	FlowID   string `json:"flowId"    gorm:"column:flowId"`
	FlowName string `json:"flowName"  gorm:"column:flowName"`
}

// IntentEntry v2.3新增: 意图和词条的引用关系维护
type IntentEntry struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	EntryID       string    `gorm:"column:f_entry_id"`                                    // 词条ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName  意图槽位关系表
func (IntentEntry) TableName() string {
	return "t_intent_entry"
}

// IntentSlot v2.1新增: 意图和槽位的引用关系维护
type IntentSlot struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	SlotID        string    `gorm:"column:f_slot_id"`                                     // 槽位ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName  意图槽位关系表
func (IntentSlot) TableName() string {
	return "t_intent_slot"
}

// TIntentSlotColumns 意图-槽位表列字段
var TIntentSlotColumns = struct {
	ID            string
	IntentID      string
	SlotID        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	IntentID:      "f_intent_id",
	SlotID:        "f_slot_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// Slot 槽位表
type Slot struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	SlotID        string    `gorm:"column:f_slot_id"`                                     // 槽位ID
	SlotName      string    `gorm:"column:f_slot_name"`                                   // 槽位中文名称
	SlotDesc      string    `gorm:"column:f_slot_desc"`                                   // 槽位描述
	Examples      string    `gorm:"column:f_slot_examples"`                               // 词槽示例
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	UIN           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 槽位表
func (Slot) TableName() string {
	return "t_slot"
}

// TSlotColumns 槽位表列字段
var TSlotColumns = struct {
	ID            string
	SlotID        string
	SlotName      string
	SlotDesc      string
	Examples      string
	RobotID       string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	SlotID:        "f_slot_id",
	SlotName:      "f_slot_name",
	SlotDesc:      "f_slot_desc",
	Examples:      "f_slot_examples",
	RobotID:       "f_robot_id",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// SlotEntity 槽位关联的实体表
type SlotEntity struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	SlotID        string    `gorm:"column:f_slot_id"`                                     // 槽位ID
	EntityID      string    `gorm:"column:f_entity_id"`                                   // 实体ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 词槽实体关联表
func (SlotEntity) TableName() string {
	return "t_slot_entity"
}

// TSlotEntityColumns 槽位-实体关联表列字段
var TSlotEntityColumns = struct {
	ID            string
	SlotID        string
	EntityID      string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	SlotID:        "f_slot_id",
	EntityID:      "f_entity_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// Entity  实体表
type Entity struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	EntityID      string    `gorm:"column:f_entity_id"`                                   // 实体ID
	EntityName    string    `gorm:"column:f_entity_name"`                                 // 实体中文名称
	EntityDesc    string    `gorm:"column:f_entity_desc"`                                 // 实体描述
	Examples      string    `gorm:"column:f_entity_examples"`                             // 实体示例
	LevelType     string    `gorm:"column:f_level_type"`                                  // 数据级别 【系统级-SYS, 机器人级-BOT】
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 机器人ID 当level为SYS时为空，否则不为空
	UIN           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 实体表名
func (Entity) TableName() string {
	return "t_entity"
}

// TEntityColumns 实体表列字段
var TEntityColumns = struct {
	ID            string
	EntityID      string
	EntityName    string
	EntityDesc    string
	Examples      string
	LevelType     string
	RobotID       string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	EntityID:      "f_entity_id",
	EntityName:    "f_entity_name",
	EntityDesc:    "f_entity_desc",
	Examples:      "f_entity_examples",
	LevelType:     "f_level_type",
	RobotID:       "f_robot_id",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// SlotEntry Slot与Entry关联表
type SlotEntry struct {
	SlotID     string `gorm:"column:f_slot_id"`     // 槽位ID
	EntityID   string `gorm:"column:f_entity_id"`   // 实体ID
	EntryID    string `gorm:"column:f_entry_id"`    // 词条ID
	EntryValue string `gorm:"column:f_entry_value"` // 词条值
	EntryAlias string `gorm:"column:f_entry_alias"` // 词条别名(同义词)
}

// Entry 词条表
type Entry struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	EntryID       string    `gorm:"column:f_entry_id"`                                    // 词条ID
	EntityID      string    `gorm:"column:f_entity_id"`                                   // 实体ID
	EntryValue    string    `gorm:"column:f_entry_value"`                                 // 词条值
	EntryAlias    string    `gorm:"column:f_entry_alias"`                                 // 词条别名(同义词)
	UIN           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 词条表名
func (Entry) TableName() string {
	return "t_entry"
}

// TEntryColumns 词条表列字段
var TEntryColumns = struct {
	ID            string
	EntryID       string
	EntityID      string
	EntryValue    string
	EntryAlias    string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	EntryID:       "f_entry_id",
	EntityID:      "f_entity_id",
	EntryValue:    "f_entry_value",
	EntryAlias:    "f_entry_alias",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// ConvertToEntryMigrationInfo 转换导入/导出所需结构
func (e Entry) ConvertToEntryMigrationInfo() EntryMigrationInfo {
	entryAlias := make([]string, 0)
	_ = jsoniter.Unmarshal([]byte(e.EntryAlias), &entryAlias)
	return EntryMigrationInfo{
		EntryID:    e.EntryID,
		EntryValue: e.EntryValue,
		EntryAlias: entryAlias,
	}
}

// EntryIntentInfo 词条意图关系
type EntryIntentInfo struct {
	IntentID   string `gorm:"column:f_intent_id"`   // 意图ID
	EntryID    string `gorm:"column:f_entry_id"`    // 词条ID
	EntryValue string `gorm:"column:f_entry_value"` // 词条名称
	EntryAlias string `gorm:"column:f_entry_alias"` // 词条别名
}

// SlotIntentInfo 槽位意图关系
type SlotIntentInfo struct {
	IntentID     string `gorm:"column:f_intent_id"`     // 意图ID
	SlotID       string `gorm:"column:f_slot_id"`       // 槽位ID
	SlotName     string `gorm:"column:f_slot_name"`     // 槽位中文名称
	SlotDesc     string `gorm:"column:f_slot_desc"`     // 槽位描述
	SlotExamples string `gorm:"column:f_slot_examples"` // 槽位示例
}

// ConvertToSlotMigrationInfo 转换导入/导出所需结构
func (s SlotIntentInfo) ConvertToSlotMigrationInfo(entityMigrationInfos []EntityMigrationInfo) (
	*SlotMigrationInfo, error) {
	examples := make([]string, 0)
	err := jsoniter.Unmarshal([]byte(s.SlotExamples), &examples)
	if err != nil {
		return nil, err
	}

	return &SlotMigrationInfo{
		SlotID:       s.SlotID,
		SlotName:     s.SlotName,
		SlotDesc:     s.SlotDesc,
		SlotExamples: examples,
		EntityInfo:   entityMigrationInfos,
	}, nil
}

// SlotEntityInfo 槽位实体关系
type SlotEntityInfo struct {
	SlotID         string `gorm:"column:f_slot_id"`         // 槽位ID
	EntityID       string `gorm:"column:f_entity_id"`       // 实体ID
	EntityName     string `gorm:"column:f_entity_name"`     // 实体中文名称
	EntityDesc     string `gorm:"column:f_entity_desc"`     // 实体描述
	EntityExamples string `gorm:"column:f_entity_examples"` // 实体示例
	LevelType      string `gorm:"column:f_level_type"`      // 数据级别 【系统级-SYS, 用户级-BOT】
}

// ConvertToEntityMigrationInfo 转换导入/导出所需结构
func (s SlotEntityInfo) ConvertToEntityMigrationInfo(entryMigrationInfos []EntryMigrationInfo) (
	EntityMigrationInfo, error) {
	examples := make([]string, 0)
	err := jsoniter.Unmarshal([]byte(s.EntityExamples), &examples)
	if err != nil {
		return EntityMigrationInfo{}, err
	}

	return EntityMigrationInfo{
		EntityID:       s.EntityID,
		EntityName:     s.EntityName,
		EntityDesc:     s.EntityDesc,
		EntityExamples: examples,
		EntityType:     s.LevelType,
		Entries:        entryMigrationInfos,
	}, nil
}
