// bot-task-config-server
//
// @(#)sync-task.go  Friday, December 22, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package entity

import (
	"time"
)

const (
	TaskEventCollect string = "COLLECT" // 采集
	TaskEventRelease string = "RELEASE" // 发布
	TaskEventPause   string = "PAUSE"   // 暂停
	TaskEventRetry   string = "RETRY"   // 重试
)

type LogType string // 日志类型

const (
	LogTypeTask          LogType = "TASK"            // 任务
	LogTypeDiff          LogType = "DIFF"            // 对比
	LogTypeRelease       LogType = "RELEASE"         // 发布数据
	LogTypeNotify        LogType = "NOTIFY"          // 通知
	LogTypeResult        LogType = "RESULT"          // 结果
	LogTypeEvent         LogType = "EVENT"           // 事件记录
	LogTypeSaveRelease   LogType = "SAVE_RELEASE"    // 保存发布历史
	LogTypeReleaseAPIVar LogType = "RELEASE_API_VAR" // 发布API参数
)

type TaskStatus uint

const (
	TaskStatusCollect     TaskStatus = 1  // 待发布
	TaskStatusProcessing  TaskStatus = 2  // 发布中
	TaskStatusSyncSuccess TaskStatus = 3  // 发布成功
	TaskStatusSyncFailed  TaskStatus = 4  // 发布失败
	TaskStatusQueuing     TaskStatus = 55 // 发布队列等待中
)

// SyncTask 任务记录
type SyncTask struct {
	ID           int64     `gorm:"column:id"`                       // 主键 ID
	UserID       string    `gorm:"column:user_id"`                  // 用户ID, 登录用户唯一标识
	UserName     string    `gorm:"column:user_name"`                // 操作用户名称, 显示使用
	Scene        string    `gorm:"column:scene"`                    // 使用场景, 电话客服, 文本客服
	RobotID      string    `gorm:"column:robot_id"`                 // 机器人 id
	TaskID       uint64    `gorm:"column:task_id;primaryKey"`       // 任务 id
	CreateTime   time.Time `gorm:"column:create_time;default:null"` // 创建时间
	SuccessCount uint64    `gorm:"column:success_count"`            // 成功数量
	FailedCount  uint64    `gorm:"column:failed_count"`             // 失败数量
	// 状态
	//	1:待发布,
	//	2:发布中,
	//	3:发布成功,
	//	4:发布失败,
	//	5:审核中,
	//	6:审核成功,
	//	7:审核失败,
	//	8:发布成功回调处理中,
	//	9:发布暂停,
	//	55: 发布队列等待中
	Status    int       `gorm:"column:status"`
	DoneTime  time.Time `gorm:"column:done_time;default:null"` // 结束时间
	SessionID string    `gorm:"column:session_id"`             // 此次记录产生的 session id
	Server    string    `gorm:"column:server"`                 // 操作 server, 显示使用
	WorkIP    string    `gorm:"column:work_ip"`                // 操作 ip, 显示使用
	Note      string    `gorm:"column:note"`                   // 备注信息
}

func (m SyncTask) TableName() string {
	return "t_sync_task"
}

func (m SyncTask) PDLTableName() string {
	return "t_pdl_sync_task"
}

type SyncTaskLog struct {
	RobotID      string    `gorm:"column:robot_id"`                 // 机器人 ID
	SyncTaskID   uint64    `gorm:"column:sync_task_id"`             // 对应的任务记录 主键 ID
	SessionID    string    `gorm:"column:session_id"`               // 此次记录产生的 session id
	Type         string    `gorm:"column:type"`                     // 日志类型, 任务: TASK, 对比: DIFF
	LogContent   string    `gorm:"column:log_content"`              // 日志内容
	ErrorMessage string    `gorm:"column:error_message"`            // 错误信息
	CreateTime   time.Time `gorm:"column:create_time;default:null"` // 创建时间
	Server       string    `gorm:"column:server"`                   // 操作 server, 显示使用
	WorkIP       string    `gorm:"column:work_ip"`                  // 操作 ip, 显示使用
}

func (m SyncTaskLog) TableName() string {
	return "t_sync_task_log"
}

func (m SyncTaskLog) PDLTableName() string {
	return "t_pdl_sync_task_log"
}
