package entity

import "time"

const (
	// SystemIntegratorValid 有效集成商状态
	SystemIntegratorValid = 1
	// SystemIntegratorInvalid 无效集成商状态
	SystemIntegratorInvalid = 0

	// CloudSID 腾讯云直客集成商ID
	CloudSID = 1
	// CloudSIUin 腾讯云直客主账号
	CloudSIUin = ""
	// CloudSISubAccountUin 腾讯云直客子账号
	CloudSISubAccountUin = ""
)

// SystemIntegrator 集成商
type SystemIntegrator struct {
	ID               int       `db:"id"`
	Name             string    `db:"name"`               // 集成商名称
	Status           int8      `db:"status"`             // 集成商状态
	Uin              string    `db:"uin"`                // 集成商主账号
	SubAccountUin    string    `db:"sub_account_uin"`    // 集成商子账号
	IsSelfPermission bool      `db:"is_self_permission"` // 是否集成商自己管理权限
	UpdateTime       time.Time `db:"update_time"`        // 更新时间
	CreateTime       time.Time `db:"create_time"`        // 创建时间
}

// IsValid 是否有效集成商
func (si *SystemIntegrator) IsValid() bool {
	return si.Status == SystemIntegratorValid
}

// IsCloudSI 是否云集成商
func (si *SystemIntegrator) IsCloudSI() bool {
	return si.ID == CloudSID
}

// IsNeedAudit 是否需要审核
func (si *SystemIntegrator) IsNeedAudit() bool {
	return si.ID == CloudSID
}

// IsSelfManagePermission 是否自己管理权限
func (si *SystemIntegrator) IsSelfManagePermission() bool {
	return si.IsSelfPermission
}
