package entity

import (
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
)

const (
	ValueSourceEntry     = 1 // 词条
	ValueSourceInput     = 2 // 用户输入
	ValueSourceEntity    = 3 // 引用实体
	ValueSourceResp      = 4 // 引用接口出参数
	ValueSourceCustomVar = 5 // 引用自定义参数

	SandboxEnvScene = 1 // 评测环境
	ProdEnvScene    = 2 // 线上环境

	EnvTypeProd = 1 // 线上环境
)

const (
	ParamTypeObject = "object" // object
	ParamTypeString = "string" // string类型

	// 任务流程操作类型
	OperateTypeCopy   = "copy"   // 复制任务流程
	OperateTypeImport = "import" // 导入任务流程
)

// TaskFlowPublishHistory 恢复历史任务流程
type TaskFlowPublishHistory struct {
	ID           uint64    `gorm:"column:f_id"`                                           // 主键ID
	FlowID       string    `gorm:"column:f_flow_id"`                                      // 任务流ID
	IntentID     string    `gorm:"column:f_intent_id"`                                    //意图ID
	FlowJson     string    `gorm:"column:f_flow_json"`                                    // 任务流对话树JSON
	Version      string    `gorm:"column:f_version"`                                      // 任务流版本，  历史版本号 1.0.0-timestamp(时间戳)
	ProtoVersion int32     `gorm:"column:f_proto_version"`                                // 对话树(DialogJsonDraft和DialogJsonEnable)的协议版本号
	SaveType     int       `gorm:"column:f_save_type"`                                    // 任务流程历史记录类型，'1:PUBLISHED', '0:DRAFT'
	Uin          string    `gorm:"column:f_uin"`                                          // 主用户ID
	SubUin       string    `gorm:"column:f_sub_uin"`                                      // 子用户ID
	IsDeleted    uint64    `gorm:"column:f_is_deleted"`                                   // 是否删除
	PublishTime  time.Time `gorm:"column:f_publish_time;type:datetime;null;default:null"` // 发布时间
	CreateTime   time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"`  // 创建时间
	UpdateTime   time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"`  // 更新时间
}

// TableName 返回函数名称
func (tfh TaskFlowPublishHistory) TableName() string {
	return "t_task_flow_publish_history"
}

// SaveTypeStr 返回类型字符串化
func (tfh TaskFlowPublishHistory) SaveTypeStr() string {
	if tfh.SaveType == EnumSaveType[SaveDraft] {
		return "DRAFT"
	}
	if tfh.SaveType == EnumSaveType[SavePublished] {
		return "PUBLISHED"
	}
	return ""
}

// TaskFlowHistory 历史任务流程
type TaskFlowHistory struct {
	ID           uint64    `gorm:"column:f_id"`                                          // 主键ID
	FlowID       string    `gorm:"column:f_flow_id"`                                     // 任务流ID
	FlowJson     string    `gorm:"column:f_flow_json"`                                   // 任务流对话树JSON
	Version      string    `gorm:"column:f_version"`                                     // 任务流版本，  历史版本号 1.0.0-timestamp(时间戳)
	Uin          string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin       string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	IsDeleted    uint64    `gorm:"column:f_is_deleted"`                                  // 0未删除 >0已删除
	CreateTime   time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime   time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
	ProtoVersion int32     `gorm:"column:f_proto_version"`                               // 对话树(DialogJsonDraft和DialogJsonEnable)的协议版本号
}

// TableName 返回函数名称
func (tfh TaskFlowHistory) TableName() string {
	return "t_task_flow_history"
}

// TaskFlow 任务流程
type TaskFlow struct {
	ID               uint64    `gorm:"column:f_id"`                                          // 主键ID
	FlowID           string    `gorm:"column:f_flow_id"`                                     // 任务流ID
	IntentName       string    `gorm:"column:f_intent_name"`                                 // 意图名称
	IntentDesc       string    `gorm:"column:f_intent_desc"`                                 // 意图描述
	IntentID         string    `gorm:"column:f_intent_id"`                                   // 意图ID
	FlowState        string    `gorm:"column:f_flow_state"`                                  // 任务流状态：任务流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改;
	FlowType         string    `gorm:"column:f_flow_type"`                                   // 任务流类型 客服or助手
	Version          string    `gorm:"column:f_version"`                                     // 版本, 客服固定写死，版本号三级如：1.0.0, 历史版本号 1.0.0-timestamp(时间戳)
	CopyCount        int       `gorm:"column:f_copy_count"`                                  // 复制次数
	CategoryID       string    `gorm:"column:f_category_id"`                                 // 类别ID
	DialogJsonDraft  string    `gorm:"column:f_dialog_json_draft"`                           // 对话树json字段草稿
	DialogJsonEnable string    `gorm:"column:f_dialog_json_enable"`                          // 启用状态下的节点信息
	Uin              string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin           string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus    string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted        int       `gorm:"column:f_is_deleted"`                                  // 0未删除 1已删除
	Action           string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime       time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime       time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
	ProtoVersion     int32     `gorm:"column:f_proto_version"`                               // 对话树(DialogJsonDraft和DialogJsonEnable)的协议版本号
}

// TTaskFlowColumns 任务流程列
var TTaskFlowColumns = struct {
	ID               string
	FlowID           string
	IntentName       string
	IntentDesc       string
	IntentID         string
	FlowState        string
	FlowType         string
	Version          string
	CopyCount        string
	CategoryID       string
	DialogJsonDraft  string
	DialogJsonEnable string
	Uin              string
	SubUin           string
	ReleaseStatus    string
	IsDeleted        string
	Action           string
	CreateTime       string
	UpdateTime       string
	ProtoVersion     string
}{
	ID:               "f_id",
	FlowID:           "f_flow_id",
	IntentName:       "f_intent_name",
	IntentDesc:       "f_intent_desc",
	IntentID:         "f_intent_id",
	FlowState:        "f_flow_state",
	FlowType:         "f_flow_type",
	Version:          "f_version",
	CopyCount:        "f_copy_count",
	CategoryID:       "f_category_id",
	DialogJsonDraft:  "f_dialog_json_draft",
	DialogJsonEnable: "f_dialog_json_enable",
	Uin:              "f_uin",
	SubUin:           "f_sub_uin",
	ReleaseStatus:    "f_release_status",
	IsDeleted:        "f_is_deleted",
	Action:           "f_action",
	CreateTime:       "f_create_time",
	UpdateTime:       "f_update_time",
	ProtoVersion:     "f_proto_version",
}

// TableName 表名
func (t TaskFlow) TableName() string {
	return "t_task_flow"
}

// IsAllowEdit 节点是否可编辑
func (t *TaskFlow) IsAllowEdit() bool {
	if t == nil {
		return false
	}
	// 发布中  不可编辑
	if t.FlowState == ReleaseStatusPublishing {
		return false
	}
	return true
}

// IsAllowRelease 节点是否可发布
func (t *TaskFlow) IsAllowRelease() bool {
	if t == nil {
		return false
	}
	//f_flow_state 任务流状态：草稿； ==> 不可发布
	if t.FlowState == FlowStateDraft {
		return false
	}
	return true
}

// IsAllowDelete 节点是否可删除
func (t *TaskFlow) IsAllowDelete() bool {
	if t == nil {
		return false
	}
	// f_flow_state 任务流状态：发布中 不可以删除
	if t.ReleaseStatus == ReleaseStatusPublishing {
		return false
	}
	return true
}

// HasAlreadyDeleted 任务流是否已经删除
func (tf *TaskFlow) HasAlreadyDeleted() bool {
	return tf.IsDeleted == TaskFlowDeleted
}

// ListTaskFLowParams 获取流程列表传参数
type ListTaskFLowParams struct {
	Query         string // 筛选意图名
	CateIds       []string
	StartTime     time.Time
	EndTime       time.Time
	Page          uint32
	PageSize      uint32
	BotBizId      string
	FlowType      string
	OrderBy       string
	Actions       []string
	FlowState     []string
	ReleaseStatus []string
}

// ModifyTaskFlowParams 修改任务流程传参数
type ModifyTaskFlowParams struct {
	FlowID           string
	IntentName       string
	IntentDesc       string // 意图描述
	CategoryID       string
	RobotId          string
	DialogJsonDraft  string    // 草稿JSON
	DialogJsonEnable string    // 可用JSON，校验后的
	Uin              string    // 主用户ID
	SubUin           string    // 子用户ID
	UpdateTime       time.Time // 更新时间
	Action           string    // 执行的动作：新增、更新、删除
	ReleaseStatus    string    // 发布状态
	FlowState        string    // 任务流状态
	IsEnable         uint32    // 是否启用任务流
	SlotIDs          []string  // 这个对话树中使用的SlotIDs
	EntryIDs         []string  // 这个对话树中使用的EntryIDs
	VarIDs           []string  // 对话树中使用的自定义变量 VarIds
}

// CreateTaskFlowParams 创建传参数
type CreateTaskFlowParams struct {
	FlowID           string    // 任务流ID
	IntentName       string    // 意图名称
	IntentDesc       string    // 意图描述
	IntentID         string    // 意图ID
	FlowState        string    // 任务流状态
	FlowType         string    // 任务流类型 客服or助手
	IntentType       string    // 意图类型
	IntentSource     string    // 意图来源
	Version          string    // 版本, 客服固定写死，版本号三级如：1.0.0, 历史版本号 1.0.0-timestamp(时间戳)
	CategoryID       string    // 类别ID
	DialogJsonDraft  string    // 对话树json字段草稿
	DialogJsonEnable string    // 启用状态下的节点信息
	Uin              string    // 主用户ID
	SubUin           string    // 子用户ID
	ReleaseStatus    string    // 状态：未发布、发布中、已发布、发布失败
	IsDeleted        int       // 0未删除 1已删除
	Action           string    // 执行的动作：新增、更新、删除
	CreateTime       time.Time // 创建时间
	UpdateTime       time.Time // 更新时间
	RobotId          string    // 机器人ID
	CorpusID         string    // 预料ID
}

const (
	TaskFlowDeleted   = 1
	TaskFlowUnDeleted = 0
)

const (
	// 任务流程列表筛选发布状态前端统一
	FrontStatusUnPublished       = "UNPUBLISHED"        //待发布
	FrontStatusPublishing        = "PUBLISHING"         // 发布中
	FrontStatusPublished         = "PUBLISHED"          //已发布
	FrontStatusPublishedFail     = "FAIL"               //发布失败
	FrontStatusPublishedDraft    = "DRAFT"              //草稿
	FrontStatusChangeUnPublished = "CHANGE_UNPUBLISHED" //待更新发布

	// ReleaseStatusUnPublished 发布状态 未发布
	ReleaseStatusUnPublished = "UNPUBLISHED"
	// ReleaseStatusPublishing 发布状态 发布中
	ReleaseStatusPublishing = "PUBLISHING"
	// ReleaseStatusPublished 发布状态 已发布
	ReleaseStatusPublished = "PUBLISHED"
	// ReleaseStatusFail 发布状态 发布失败
	ReleaseStatusFail = "FAIL"

	// FlowStateDraft 任务流状态 草稿
	FlowStateDraft = "DRAFT"
	// FlowStateEnable 任务流状态 启用
	FlowStateEnable = "ENABLE"
	// FlowStateChangeUnPublish 已修改待发布
	FlowStateChangeUnPublish = "CHANGE_UNPUBLISHED"
	// FlowStatePublishChange 任务流状态 已发布仍有修改
	FlowStatePublishChange = "PUBLISHED_CHANGE"

	// ActionGet 执行动作获取 -- 向量数据库发布使用
	ActionGet = "GET"

	// ActionInsert 执行动作 新增
	ActionInsert = "INSERT"
	// ActionUpdate 执行动作 更新
	ActionUpdate = "UPDATE"
	// ActionDelete 执行动作 删除
	ActionDelete = "DELETE"

	// 发布筛选任务流程为了前端传参数统一 1:新增；2：修改；3：删除；
	QueryActionInsert = "1"
	QueryActionUpdate = "2"
	QueryActionDelete = "3"

	SaveDraft     = "DRAFT"     //0 DRAFT
	SavePublished = "PUBLISHED" //1 PUBLISHED
)

const (
	FlowTypeICS   = "ICS"   // 智能客服
	FlowTypeASSIS = "ASSIS" // 助手
)

var FrontStatusPublishDesc = map[string]string{
	FrontStatusUnPublished:       "待发布",
	FrontStatusPublishing:        "发布中",
	FrontStatusPublished:         "已发布",
	FrontStatusPublishedFail:     "发布失败",
	FrontStatusPublishedDraft:    "草稿",
	FrontStatusChangeUnPublished: "待更新发布",
}

var FlowTypeDesc = map[string]string{
	FlowTypeICS:   "智能客服",
	FlowTypeASSIS: "助手",
}

var flowStateDesc = map[string]string{
	FlowStateDraft:           "草稿",
	FlowStateEnable:          "启用",
	FlowStateChangeUnPublish: "已修改待发布",
	FlowStatePublishChange:   "已发布仍有修改",
}

var flowReleaseStatusDesc = map[string]string{
	ReleaseStatusUnPublished: "未发布",
	ReleaseStatusPublishing:  "发布中",
	ReleaseStatusPublished:   "已发布",
	ReleaseStatusFail:        "发布失败",
}

var PublishActionDesc = map[string]string{
	QueryActionInsert: "INSERT",
	// ActionUpdate 执行动作 更新
	QueryActionUpdate: "UPDATE",
	// ActionDelete 执行动作 删除
	QueryActionDelete: "DELETE",
}

var EnumToAction = map[string]int{
	ActionInsert: 1,
	ActionUpdate: 2,
	ActionDelete: 3,
}

var EnumSaveType = map[string]int{
	SaveDraft:     0,
	SavePublished: 1,
}

var EnumActionDescription = map[string]string{
	ActionInsert: "新增",
	ActionUpdate: "修改",
	ActionDelete: "删除",
}

/***
UNPUBLISHED: 待发布
PUBLISHING: 发布中
PUBLISHED: 已发布
FAIL:发布失败
DRAFT:草稿
CHANGE_UNPUBLISHED:待更新发布
 状态
	- 发布中：不可编辑                           f_release_status： PUBLISHING(发布中)

	- 已发布：已发布到正式环境，且数据与测试环境一致   f_release_status： PUBLISHED(已发布)， f_flow_state：!= DRAFT（草稿态）

	- 待发布： 仅在测试环境，为发布过               f_release_status： UNPUBLISHED(未发布)，
											   f_flow_state：!= DRAFT（草稿态）

	- 草稿： 仅在草稿，未发布到测试环境过			  f_release_status： UNPUBLISHED（未发布）， f_flow_state： DRAFT（草稿）

	- 待更新发布：已发布到正式环境，但数据与测试环境不一致   f_release_status： PUBLISHED（已发布），
											  f_flow_state： PUBLISHED_CHANGE（已发布仍有修改）

	- 发布失败：操作发布后，发布失败(可能在测试环境 or 正式环境) f_release_status：FAIL(发布失败)，f_flow_state：
**/

// FrontReleaseStatus 前端发布状态
func (tf *TaskFlow) FrontReleaseStatus() string {
	if tf == nil {
		return ""
	}
	// 发布中: f_release_status： PUBLISHING(发布中)
	if tf.ReleaseStatus == ReleaseStatusPublishing {
		return FrontStatusPublishing //  发布中
	}

	//已发布: f_release_status： PUBLISHED(已发布)， f_flow_state： ENABLE（已启用）
	if tf.ReleaseStatus == ReleaseStatusPublished && tf.FlowState != FlowStateDraft {
		return FrontStatusPublished //  已发布
	}
	//待发布：f_release_status： UNPUBLISHED(未发布)，
	//       f_flow_state： ENABLE（已启用）， CHANGE_UNPUBLISHED（已修改待发布）
	if tf.ReleaseStatus == ReleaseStatusUnPublished &&
		(tf.FlowState == FlowStateEnable || tf.FlowState == FlowStateChangeUnPublish) {
		return FrontStatusUnPublished // 待发布
	}

	// 草稿：f_release_status： UNPUBLISHED（未发布）， f_flow_state： DRAFT（草稿）
	if tf.ReleaseStatus == ReleaseStatusUnPublished && tf.FlowState == FlowStateDraft {
		return FrontStatusPublishedDraft // 草稿
	}

	//待更新发布: f_release_status： UNPUBLISHED（未发布），
	//			f_flow_state： PUBLISHED_CHANGE（已发布仍有修改）
	if tf.ReleaseStatus == ReleaseStatusUnPublished && tf.FlowState == FlowStatePublishChange {
		return FrontStatusChangeUnPublished // 待更新发布
	}

	//发布失败：f_release_status：FAIL(发布失败)
	if tf.ReleaseStatus == ReleaseStatusFail {
		return FrontStatusPublishedFail // 发布失败
	}
	return ""
}

// IsExitFrontStatusDraft 是否是草稿
func IsExitFrontStatusDraft(frontStatuses []string) bool {

	for _, val := range frontStatuses {
		if val == FrontStatusPublishedDraft {
			return true
		}
	}
	return false
}

// IsExitFrontStatusUnPublished 是否存在待发布
func IsExitFrontStatusUnPublished(frontStatuses []string) bool {
	for _, val := range frontStatuses {
		if val == FrontStatusUnPublished {
			return true
		}
	}
	return false
}

// IsExitFrontStatusChangeUnPublished 是否存在待更新发布
func IsExitFrontStatusChangeUnPublished(frontStatuses []string) bool {
	for _, val := range frontStatuses {
		if val == FrontStatusChangeUnPublished {
			return true
		}
	}
	return false
}

// FrontReleaseStatusDesc 前端发布状态描述
func (tf *TaskFlow) FrontReleaseStatusDesc() string {
	if tf == nil {
		return ""
	}
	tf.ReleaseStatus = tf.FrontReleaseStatus()
	return FrontStatusPublishDesc[tf.ReleaseStatus]
}

// ReleaseStatusDesc 任务流发布状态描述
func (tf *TaskFlow) ReleaseStatusDesc() string {
	if tf == nil {
		return ""
	}
	return flowReleaseStatusDesc[tf.ReleaseStatus]
}

// FlowStateDesc 任务流状态描述
func (tf *TaskFlow) FlowStateDesc() string {
	if tf == nil {
		return ""
	}
	return flowStateDesc[tf.FlowState]
}

// FlowTypeDesc 任务流类型描述
func (tf *TaskFlow) FlowTypeDesc() string {
	if tf == nil {
		return ""
	}
	return FlowTypeDesc[tf.FlowType]
}

const (
	// CopyTaskFlowKey 任务流程复制Key
	CopyTaskFlowKey = "CopyTaskFlow:%s"
)

// GetCopyTaskFlowLockKey 获取任务流程复制锁key
func GetCopyTaskFlowLockKey(flowID string) string {
	return fmt.Sprintf(CopyTaskFlowKey, flowID)
}

// GetReleaseTaskFlowFlows 将taskFlow转化为 TaskFlowPublishHistory
func GetReleaseTaskFlowFlows(taskFlows *[]TaskFlow, timeStamp string, publishTime time.Time) *[]TaskFlowPublishHistory {
	var historyFlowTasks []TaskFlowPublishHistory
	for _, item := range *taskFlows {
		version := config.GetMainConfig().VerifyTaskFlow.Version + "-" +
			fmt.Sprintf("%d", item.ProtoVersion) + "-" + timeStamp
		item.UpdateTime = publishTime
		historyFlowTasks = append(historyFlowTasks, *TaskFlowToHistoryFlowTask(&item, version,
			EnumSaveType[SavePublished]))
	}
	return &historyFlowTasks
}

// TaskFlowToHistoryFlowTask 将 TaskFlow 转为 TaskFlowPublishHistory
func TaskFlowToHistoryFlowTask(tf *TaskFlow, version string, saveType int) *TaskFlowPublishHistory {
	var flowJson string
	if saveType == EnumSaveType[SaveDraft] {
		flowJson = tf.DialogJsonDraft
	} else {
		flowJson = tf.DialogJsonEnable
	}
	return &TaskFlowPublishHistory{
		FlowID:       tf.FlowID,
		IntentID:     tf.IntentID,
		FlowJson:     flowJson,
		Version:      version,
		ProtoVersion: tf.ProtoVersion,
		SaveType:     saveType,
		Uin:          tf.Uin,
		SubUin:       tf.SubUin,
		PublishTime:  tf.UpdateTime,
	}
}
