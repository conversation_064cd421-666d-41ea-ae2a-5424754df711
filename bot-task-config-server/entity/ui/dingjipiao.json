{"TaskFlowID": "17295a98-5efd-4c4c-a5e1-cd6174253a92", "TaskFlowName": "定机票", "Nodes": [{"NodeID": "start", "NodeName": "开始节点", "NodeType": "startNode", "NodeData": {}, "NodeUI": {"X": "100", "Y": "100", "style": "", "Anchors": []}, "Branches": [{"BranchID": "1", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "start"}]}, {"NodeID": "api", "NodeName": "执行机票预定", "NodeType": "APINode", "NodeData": {"RequiredInfo": [{"SlotID": "1", "SlotName": "出发地", "SlotVarName": "startPlace", "ParamType": "string", "ProbeConfig": "auto", "IsRequired": true, "ProbePreview": ["您的出发地是哪？"], "ProbeCustom": ""}, {"SlotID": "2", "SlotName": "目的地", "SlotVarName": "EndPlace", "ParamType": "string", "ProbeConfig": "auto", "IsRequired": true, "ProbePreview": ["您的目的地是哪？"], "ProbeCustom": ""}, {"SlotID": "3", "SlotName": "出发时间", "SlotVarName": "date", "ParamType": "string", "ProbeConfig": "auto", "IsRequired": true, "ProbePreview": ["您的出发时期是什么时候？"], "ProbeCustom": ""}], "APIPath": {"Path": "https:/127.0.0.1/GetJourney", "Method": "GET"}, "APIResponseParams": [{"ParamID": "ParamID11", "ParamName": "预定成功", "ParamVarName": "success", "ParamType": "string", "ParamPath": "data.EndPlace", "IsRequired": true}, {"ParamID": "ParamID22", "ParamName": "预定失败", "ParamVarName": "fail", "ParamType": "string", "ParamPath": "data.Error", "IsRequired": true}], "InvokeConfirm": false}, "NodeUI": {"X": "500", "Y": "100", "style": "", "Anchors": []}, "Branches": [{"BranchID": "BranchID111", "BranchType": "custom", "Conditions": [{"ConditionID": "ConditionID1", "ConditionSource": "APIResponseParam", "NodeID": "api", "NodeName": "执行机票预定", "ParamID": "1", "ParamName": "预定成功", "ParamVarName": "success", "VarType": "string", "Operator": "==", "Values": ["预定成功"]}], "ConditionsLogic": "And", "NextNodeID": "answer11", "PrevNodeID": "api"}, {"BranchID": "BranchID222", "BranchType": "custom", "Conditions": [{"ConditionID": "ConditionID222", "ConditionSource": "APIResponseParam", "NodeID": "api", "NodeName": "执行机票预定", "ParamID": "ParamID22", "ParamName": "预定失败", "ParamVarName": "fail", "VarType": "string", "Operator": "==", "Values": ["预定失败"]}], "ConditionsLogic": "And", "NextNodeID": "answer222", "PrevNodeID": "api"}]}, {"NodeID": "answer11", "NodeName": "答案节点1", "NodeType": "AnswerNode", "NodeData": {"AnswerSource": "auto", "AnswerPreview": ["目的地是北京"], "AnswerCustom": ""}, "NodeUI": {"X": "700", "Y": "100", "style": "", "Anchors": []}, "Branches": []}, {"NodeID": "answer22", "NodeName": "答案节点2", "NodeType": "AnswerNode", "NodeData": {"AnswerSource": "custom", "AnswerPreview": [], "AnswerCustom": "您预定失败，请重试"}, "NodeUI": {"X": "700", "Y": "100", "style": "", "Anchors": []}, "Branches": []}], "Edges": []}