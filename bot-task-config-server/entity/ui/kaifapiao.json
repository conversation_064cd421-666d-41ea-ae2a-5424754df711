{"TaskFlowID": "17295a98-5efd-4c4c-a5e1-cd6174253a92", "TaskFlowName": "开发票", "Nodes": [{"NodeID": "start", "NodeName": "开始节点", "NodeType": "startNode", "NodeData": {}, "NodeUI": {"X": "100", "Y": "100", "style": "", "Anchors": []}, "Branches": [{"BranchID": "1", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "start"}]}, {"NodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "询问节点1", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": true, "OptionContents": ["自开", "代开"], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "开票方式", "SlotVarName": "field_ka<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "NodeUI": {"X": "100", "Y": "200", "style": "", "Anchors": []}, "Branches": [{"BranchID": "2", "BranchType": "custom", "Conditions": [{"ConditionID": "1", "ConditionSource": "RequiredInfo", "NodeID": "request", "NodeName": "询问节点", "ParamID": "1", "ParamName": "开票方式", "ParamVarName": "field_ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "VarType": "string", "Operator": "contains", "Values": ["自开", "代开"]}], "ConditionsLogic": "Or", "NextNodeID": "node-kaipiaopingtai", "PrevNodeID": "start"}, {"BranchID": "3", "BranchType": "custom", "Conditions": [{"ConditionID": "2", "ConditionSource": "RequiredInfo", "NodeID": "request", "NodeName": "询问节点", "ParamID": "1", "ParamName": "开票方式", "ParamVarName": "field_ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "VarType": "string", "Operator": "contains", "Values": ["自开"]}], "ConditionsLogic": "And", "NextNodeID": "node-nashuirenshenfen", "PrevNodeID": "start"}]}, {"NodeID": "node-kaipiaopingtai", "NodeName": "开票平台节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": true, "OptionContents": ["电子发票服务平台", "增值税发票管理系统"], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "开票平台", "SlotVarName": "field_kaipiaopingtai"}}, "NodeUI": {"X": "100", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-kaipiaopingtai", "NodeName": "开票平台节点", "ParamID": "1", "ParamName": "开票平台", "ParamVarName": "field_kaipiaopingtai", "VarType": "string", "Operator": "contains", "Values": ["微信平台", "国家税务"]}], "ConditionsLogic": "And", "NextNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"BranchID": "7", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-kaipiaopingtai", "NodeName": "开票平台节点", "ParamID": "1", "ParamName": "开票平台", "ParamVarName": "field_kaipiaopingtai", "VarType": "string", "Operator": "contains", "Values": ["政务平台", "公司财务"]}], "ConditionsLogic": "And", "NextNodeID": "node-yuan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"NodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "开具问题节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": false, "OptionContents": [], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "开具问题", "SlotVarName": "field_ka<PERSON><PERSON><PERSON><PERSON>"}}, "NodeUI": {"X": "100", "Y": "400", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-answer1", "PrevNodeID": "node-kaipiaopingtai"}]}, {"NodeID": "node-yuan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "原系统开具问题节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": false, "OptionContents": [], "OptionType": "", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "原系统开具问题", "SlotVarName": "field_yuan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "NodeUI": {"X": "200", "Y": "400", "style": "", "Anchors": []}, "Branches": [{"BranchID": "7", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-answer1", "PrevNodeID": "node-kaipiaopingtai"}]}, {"NodeID": "node-answer1", "NodeName": "答案节点1", "NodeType": "AnswerNode", "NodeData": {"AnswerCustom": "", "AnswerPreview": [], "AnswerSource": "auto"}, "NodeUI": {"X": "700", "Y": "100", "style": "", "Anchors": []}, "Branches": []}, {"NodeID": "node-nashuirenshenfen", "NodeName": "纳税人身份节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": true, "OptionContents": ["一般企业及个体工商户", "自然人"], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "纳税人身份", "SlotVarName": "field_nashuirenshenfen"}}, "NodeUI": {"X": "500", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-nashuirenshenfen", "NodeName": "纳税人身份节点", "ParamID": "1", "ParamName": "纳税人身份", "ParamVarName": "field_nashuirenshenfen", "VarType": "string", "Operator": "contains", "Values": ["自然人", "个体户"]}], "ConditionsLogic": "And", "NextNodeID": "node-yibanqiyedaikai", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"BranchID": "7", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-nashuirenshenfen", "NodeName": "纳税人身份节点", "ParamID": "1", "ParamName": "纳税人身份", "ParamVarName": "field_nashuirenshenfen", "VarType": "string", "Operator": "contains", "Values": ["企业主"]}], "ConditionsLogic": "And", "NextNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"NodeID": "node-yibanqiyedaikai", "NodeName": "询问节点2", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": true, "OptionContents": ["代开范围", "代开作废", "代开流程"], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "一般企业代开", "SlotVarName": "field_yibanqiyedai<PERSON>"}}, "NodeUI": {"X": "500", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-yibanqiyedaikai", "NodeName": "一般企业代开节点", "ParamID": "1", "ParamName": "一般企业代开", "ParamVarName": "field_yibanqiyedai<PERSON>", "VarType": "string", "Operator": "contains", "Values": ["月度代开"]}, {"ConditionID": "6", "ConditionSource": "RequiredInfo", "NodeID": "node-yibanqiyedaikai", "NodeName": "一般企业代开节点", "ParamID": "1", "ParamName": "一般企业代开", "ParamVarName": "field_yibanqiyedai<PERSON>", "VarType": "string", "Operator": "contains", "Values": ["年度代开"]}], "ConditionsLogic": "Or", "NextNodeID": "node-answer2", "PrevNodeID": "node-nashuirenshenfen"}, {"BranchID": "7", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-yibanqiyedaikai", "NodeName": "一般企业代开节点", "ParamID": "1", "ParamName": "一般企业代开", "ParamVarName": "field_yibanqiyedai<PERSON>", "VarType": "string", "Operator": "contains", "Values": ["年度代开"]}], "ConditionsLogic": "And", "NextNodeID": "node-s<PERSON><PERSON>ushidiannashuiren", "PrevNodeID": "node-nashuirenshenfen"}]}, {"NodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "代开问题节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": true, "OptionContents": ["代开流程", "涉税情况"], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "代开问题", "SlotVarName": "field_<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "NodeUI": {"X": "500", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "代开问题节点", "ParamID": "1", "ParamName": "代开问题", "ParamVarName": "field_<PERSON><PERSON><PERSON><PERSON><PERSON>", "VarType": "string", "Operator": "contains", "Values": ["手续费多少"]}], "ConditionsLogic": "And", "NextNodeID": "node-s<PERSON><PERSON>ushidiannashuiren", "PrevNodeID": "node-nashuirenshenfen"}, {"BranchID": "7", "BranchType": "custom", "Conditions": [{"ConditionID": "5", "ConditionSource": "RequiredInfo", "NodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "代开问题节点", "ParamID": "1", "ParamName": "代开问题", "ParamVarName": "field_<PERSON><PERSON><PERSON><PERSON><PERSON>", "VarType": "string", "Operator": "contains", "Values": ["费率多少"]}], "ConditionsLogic": "And", "NextNodeID": "node-s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PrevNodeID": "node-nashuirenshenfen"}]}, {"NodeID": "node-answer2", "NodeName": "答案节点2", "NodeType": "AnswerNode", "NodeData": {"AnswerCustom": "", "AnswerPreview": [], "AnswerSource": "auto"}, "NodeUI": {"X": "700", "Y": "100", "style": "", "Anchors": []}, "Branches": []}, {"NodeID": "node-s<PERSON><PERSON>ushidiannashuiren", "NodeName": "是否试点纳税人节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": false, "OptionContents": [], "OptionType": "", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "是否试点纳税人", "SlotVarName": "field_shi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "NodeUI": {"X": "500", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-answer3", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"NodeID": "node-s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeName": "是否是住房节点", "NodeType": "RequestNode", "NodeData": {"APIParams": [], "OptionCards": false, "OptionContents": [], "OptionType": "custom", "RequiredInfo": {"IsRequired": true, "ParamType": "string", "ProbeConfig": "auto", "ProbeCustom": "", "ProbePreview": [], "SlotID": "1", "SlotName": "是否是住房", "SlotVarName": "field_shi<PERSON><PERSON><PERSON><PERSON><PERSON>g"}}, "NodeUI": {"X": "500", "Y": "300", "style": "", "Anchors": []}, "Branches": [{"BranchID": "6", "BranchType": "direct", "Conditions": [], "ConditionsLogic": "And", "NextNodeID": "node-answer3", "PrevNodeID": "node-<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"NodeID": "node-answer3", "NodeName": "答案节点3", "NodeType": "AnswerNode", "NodeData": {"AnswerCustom": "", "AnswerPreview": [], "AnswerSource": "auto"}, "NodeUI": {"X": "700", "Y": "100", "style": "", "Anchors": []}, "Branches": []}], "Edges": [{"EdgeId": "1", "EdgeType": "", "Source": "start", "Target": "request", "Label": ""}, {"EdgeId": "2", "EdgeType": "", "Source": "request", "Target": "api", "Label": ""}, {"EdgeId": "3", "EdgeType": "", "Source": "api", "Target": "answer", "Label": ""}]}