package ui

const (
	//	 ===节点类型
	// 开始节点
	NodeTypeStartNode = "startNode"
	// API节点
	NodeTypeAPINode = "APINode"
	// 判断询问节点
	NodeTypeRequestNode = "RequestNode"
	// 判断询问节点
	NodeTypeAnswerNode = "AnswerNode"
	// ===

	// 节点跳转规则
	NodeBranchDirectType = "direct" // 直接跳转
	NodeBranchCustomType = "custom" // 自定义

	// 询问节点的答案来源
	NodeAnswerSourceAuto   = "auto"   // 大模型自动生成
	NodeCustomSourceCustom = "custom" // 自定义

	StringParamType = "string" // string类型
	IntParamType    = "int"    // int类型
	FloatParamType  = "float"  // float类型
	BoolParamType   = "bool"   // bool类型
	ArrayParamType  = "array"  // array类型

	ConditionsLogicAnd = "And" // 且
	ConditionsLogicOr  = "Or"  // 或

	// 追问语类型 auto: 大模型自动生成， custom 自定义
	SlotInfoProbeConfigTypeAuto   = "auto"
	SlotInfoProbeConfigTypeCustom = "custom"
	RequestNodeOptionTypeCustom   = "custom"
	RequestNodeOptionTypeAPI      = "API"

	ConditionSourceRequiredInfo     = "RequiredInfo"     // 询问节点的槽位信息
	ConditionSourceAPIResponseParam = "APIResponseParam" // API出参
	ConditionSourceVariable         = "Variable"         // 变量

	ApiPathMetGet    = "GET"
	ApiPathMetPost   = "POST"
	APIProtocolHTTP  = "http"
	APIProtocolHTTPs = "https"

	// 符号定义
	ConditionOperatorContains    = "contains"
	ConditionOperatorEqual       = "=="
	ConditionOperatorNotEqual    = "!="
	ConditionOperatorNotContains = "not contains"
	ConditionOperatorMoreThan    = ">"
	ConditionOperatorLessThan    = "<"
	ConditionOperatorFilled      = "filled"
	ConditionOperatorUnfilled    = "unfilled"
	ConditionUserParamName       = "用户ID"
)

// TaskFlow 任务流画布整体
type TaskFlow struct {
	// +require 任务流ID
	TaskFlowID string `json:"TaskFlowID"` // 创建任务流程时后端生成唯一id
	// +require 任务流名称
	TaskFlowName string `json:"TaskFlowName"` // 用户输入
	// +require 节点
	Nodes []*TaskFlowNode `json:"Nodes"`
	// +require 边
	Edges []*Edge `json:"Edges"`
}

// TaskFlowNode 任务流节点
type TaskFlowNode struct {
	// +require 节点ID uuid
	NodeID string `json:"NodeID"` // 前端可以生成唯一的uuid
	// +require 节点名称
	// 前端默认生成，用户可修改（名称画布内唯一）
	NodeName string `json:"NodeName"`
	// +require 节点类型：开始、判断询问、API、答案 "startNode"  "RequestNode"  "APINode"  "AnswerNode"
	NodeType string `json:"NodeType"`
	// +require 节点数据
	// 对应下面的RequestNodeData，AnswerNodeData，APINodeData
	NodeData interface{} `json:"NodeData"`
	//// +require 节点UI
	NodeUI UIParams `json:"NodeUI"` // 节点UI连线相关数据
	// +require 节点流转规则
	Branches []*Branch `json:"Branches"`
}

// Branch 流转规则
type Branch struct {
	// +require 规则ID
	// 流转规则ID，前端生成唯一uuid
	BranchID string `json:"BranchID"`
	// +require 跳转类型：直接跳转、自定义 "direct"  "custom"
	BranchType string `json:"BranchType"`
	// +require 跳转条件
	// 当跳转类型为直接跳转（direct），这里可为空；否则不能为空
	Conditions []*Condition `json:"Conditions"`
	// 且或条件:And  Or
	// 可加多个条件并配置多个条件之间的且/或关系，最多5个条件组合，最少1个
	ConditionsLogic string `json:"ConditionsLogic"`
	// +require 下一个节点ID
	NextNodeID string `json:"NextNodeID"`
	// +require 上一个节点ID
	PrevNodeID string `json:"PrevNodeID"`
}

// Condition 规则信息：信息名称、条件、值
type Condition struct {
	// +require 条件ID
	// 调整规则ID，前端生成唯一uuid
	ConditionID string `json:"ConditionID"`
	// +require 条件来源:所需信息、API出参、用户ID   RequiredInfo  APIResponseParam	Variable
	ConditionSource string `json:"ConditionSource"`
	// +require 节点ID
	// 规则信息来源的NodeID
	NodeID string `json:"NodeID"`
	// +require 节点名称
	// 规则信息来源的节点名称
	NodeName string `json:"NodeName"`
	// +require 参数ID
	// 规则信息来源的参数id，如果是RequiredInfo，ParamID=SlotID
	ParamID string `json:"ParamID"`
	// +require 参数名称
	// 规则信息来源的参数名称 如果是RequiredInfo，ParamName=SlotName
	ParamName string `json:"ParamName"`
	// +require 参数变量名称
	// 如果是RequiredInfo，ParamVarName=SlotVarName
	ParamVarName string `json:"ParamVarName"`
	// +require 参数变量类型
	VarType string `json:"VarType"`
	//// +require 条件名称
	//// 不可自定义输入，只能拉取选择上一个节点已填充定义过的必要信息名称、API出参的信息名称、用户ID
	//ConditionName string `json:"ConditionName"`
	//// +require 条件变量名
	//ConditionVarName string `json:"ConditionVarName"`
	//// +optional 针对API参数的情况
	//ConditionParam ParamOption `json:"ConditionParam"`
	// +require 条件：
	// 信息名称是前面已定义过的必要信息名称时，条件可选包含/不包含（包含内的多个条件是或的关系）、大于/等于/小于；
	// 信息名称是前面API出参的信息名称时，条件可选包含/不包含、大于/等于/小于、已填充/未填充；
	// 信息名称是用户ID时，条件可选填充/未填充；
	//  数组只能支持 包含、不包含、已填充、未填充
	// 操作符：contains / not contains，==/!=，>/<，filled/unfilled，用户ID
	Operator string `json:"Operator"`
	// +require 值
	Values []string `json:"Values"`
}

// StartNodeData 开始节点数据
type StartNodeData struct {
}

// RequestNodeData 询问节点数据 todo 这里需求说明只要一个必要信息，是否考虑多个的情况
type RequestNodeData struct {
	// +require 必要信息 todo 定义为slot？变量名怎么生成。
	RequiredInfo *SlotInfo `json:"RequiredInfo"`
	// +optional 选项卡片
	// true：启用选项卡，false：未启用选项卡
	OptionCards bool `json:"OptionCards"`
	// +optional 选择方式：API 自定义
	// 询问话术，选择方式：自定义(custom)、API(API)
	OptionType string `json:"OptionType"`
	// +optional 选项卡内容配置
	// 选项卡片 自定义 配置选项
	OptionContents []string `json:"OptionContents"`
	// +optional API参数
	// API 选项配置（内容来源）
	// 开启选项卡，选择方式API
	APIParams []*ParamOption `json:"APIParams"` // todo：这里单选 还是多选？
}

// ParamOption 来自API的参数选项
type ParamOption struct {
	// +require 节点ID
	// 所引用节点的ID
	NodeID string `json:"NodeID"`
	// +require 节点名称
	NodeName string `json:"NodeName"`
	// +require 参数ID
	// 引用参数的ID
	ParamID string `json:"ParamID"`
	// +require 参数名称
	// 引用参数的中文名称
	ParamName string `json:"ParamName"`
	// +require 参数变量名称
	// 引用参数的英文名称
	ParamVarName string `json:"ParamVarName"`
}

// AnswerNodeData 答案节点数据
type AnswerNodeData struct {
	// +require 答案来源：大模型自动生成、自定义	"auto" "custom"
	AnswerSource string `json:"AnswerSource"`
	// +require 答案预览
	AnswerPreview []string `json:"AnswerPreview"`
	// 自定义答案
	AnswerCustom string `json:"AnswerCustom"`
}

// APINodeData API节点数据
type APINodeData struct {
	// +require 必要信息 sys.time = time.now 词槽  和 其他变量（用户输入 固定值 or 后台自动获取 的变量 比如 时间之类的，参数来源 分开？
	// 所需信息(入参)
	RequiredInfo []*SlotInfo `json:"RequiredInfo"`
	// +require API路径和方法信息
	// APIPath.Path: 接口链接； APIPath.Method: 获取方式(GET/POST)
	APIPath APIPath `json:"APIPath"`
	// +require API Response 参数
	// 返回(出)参信息
	APIResponseParams []*APIResponseParam `json:"APIResponseParams"`
	//+optional API 调用确认
	InvokeConfirm bool `json:"InvokeConfirm"`
}

// // RequestParam API请求参数
// type RequestParam struct {
//	Params []*SlotInfo `json:"Params"`
//	Uin    string      `json:"Uin"`
//	Limit  int         `json:"Limit"`
//	Offset int         `json:"Offset"`
// }

// SlotInfo 槽位信息
// 兼容询问节点： SlotName：所需信息(中文名)，SlotVarName：所需信息英文名(槽位变量名)
//
//	    ProbeConfig:询问话术类型(大模型生成[auto]/自定义[custom] );  ProbePreview（auto）： 预览话术(最多三条)，
//		ProbeCustom 自定义话术
//
// 兼容API节点的入参信息，SlotName：所需信息(参数中文名)，SlotVarName 入参字段(英文名)， ParamType: 参数类型名
//
//		ProbeConfig:询问话术类型(大模型生成[auto]/自定义[custom])，ProbePreview(auto):追问话术（最多三条）
//	    ProbeCustom：自定义话术
type SlotInfo struct {
	// +require 槽位ID
	// 前端生成唯一uuid
	SlotID string `json:"SlotId"`
	// +require 槽位名称
	SlotName string `json:"SlotName"`
	// +require 槽位变量名
	SlotVarName string `json:"SlotVarName"`
	// +require API输入参数类型  string, int, float, bool, array
	ParamType string `json:"ParamType"`
	// +optional 追问语类型，API节点使用 "auto"  "custom"
	ProbeConfig string `json:"ProbeConfig"`
	// 是否必选 require or optional
	// 默认必选
	IsRequired bool `json:"IsRequired"`
	// 生成效果预览  可能有并列的多条，最多不超过3条
	ProbePreview []string `json:"ProbePreview"`
	// 自定义追问语
	ProbeCustom string `json:"ProbeCustom"`
}

// APIPath API 路径和方法信息
type APIPath struct {
	// +require API路径
	Path string `json:"Path"`
	// +require API方法 GET/POST
	Method string `json:"Method"`
}

//// APIResponse API Response
//type APIResponse struct {
//	// +require API返回代码：前端不可见
//	Code int `json:"Code"`
//	// +require API返回信息：前端不可见
//	Message string `json:"Message"`
//	// +require API Response 参数
//	APIResponseParams []*APIResponseParam `json:"APIResponseParams"`
//}

// APIResponseParam API Response 参数
type APIResponseParam struct {
	// +require API返回参数ID（需前端生成唯一ID）
	ParamID string `json:"ParamID"`
	// +require API返回参数名称
	// 参数中文名
	ParamName string `json:"ParamName"`
	// +require API返回参数字段名称
	// 参数英文名
	ParamVarName string `json:"ParamVarName"` // City
	// +require API返回参数类型  string, int, float, bool, array
	ParamType string `json:"ParamType"`
	// 映射路径
	ParamPath string `json:"ParamPath"` // data.city
	// 是否必选 require or optional
	IsRequired bool `json:"IsRequired"`
}

// UIParams 前端展示用参数
type UIParams struct {
	X       string     `json:"X"`
	Y       string     `json:"Y"`
	Style   string     `json:"style"`
	Anchors [][]string `json:"Anchors"`
}

// Edge 边
type Edge struct {
	EdgeID       string `json:"EdgeId"`
	Source       string `json:"Source"`
	Target       string `json:"Target"`
	SourceAnchor int    `json:"SourceAnchor"`
	TargetAnchor int    `json:"TargetAnchor"`
	Label        string `json:"Label"` // 保存线上的标签
}

//// Point 点的位置和序号
//type Point struct {
//	X     int `json:"X"`
//	Y     int `json:"Y"`
//	Index int `json:"Index"`
//}
