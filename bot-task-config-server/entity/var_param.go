// bot-task-config-server
//
// @(#)var_param.go  星期三, 六月 26, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package entity

import "time"

const UserId = "UserID"

//const (
//	VarStringType      = "STRING"
//	VarIntType         = "INT"
//	VarFloatType       = "FLOAT"
//	VarBoolType        = "BOOL"
//	VarObjectType      = "OBJECT"
//	VarArrayStringType = "ARRAY_STRING"
//	VarArrayIntType    = "ARRAY_INT"
//	VarArrayFloatType  = "ARRAY_FLOAT"
//	VarArrayBoolType   = "ARRAY_BOOL"
//	VarArrayObjectType = "ARRAY_OBJECT"
//	VarFileType        = "FILE"
//	VarDocumentType    = "DOCUMENT"
//	VarImageType       = "IMAGE"
//	VarAudioType       = "AUDIO"
//	//VarPdfType         = "pdf"
//	//VarDocType         = "doc"
//	//VarDocxType        = "docx"
//	//VarPPTType         = "ppt"
//	//VarPPTXType        = "pptx"
//	//VarXlsxType        = "xlsx"
//	//VarXlsType         = "xls"
//	//VarMdType          = "md"
//	//VarTxtType         = "txt"
//	//VarCsvType         = "csv"
//	//VarImageType       = "image"
//	//VarAudioType       = "audio"
//)

// ExportIntentVar 导出变量
type ExportIntentVar struct {
	IntentID           string `gorm:"column:f_intent_id"`
	VarID              string `gorm:"column:f_var_id"`
	VarName            string `gorm:"column:f_var_name"`
	VarDesc            string `gorm:"column:f_var_desc"` // 变量描述
	VarType            string `gorm:"column:f_var_type"`
	VarDefaultValue    string `json:"var_default_value"`     // 变量默认值
	VarDefaultFileName string `json:"var_default_file_name"` // 变量默认值文件名称
}

// IntentVarParams v2.4新增: 变量参数 和 意图 关联关系表
type IntentVarParams struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 自增ID
	IntentID      string    `gorm:"column:f_intent_id"`                                   // 意图ID
	VarID         string    `gorm:"column:f_var_id"`                                      // 变量ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName  意图变量关系表
func (IntentVarParams) TableName() string {
	return "t_intent_var"
}

// VarParams 变量参数表
type VarParams struct {
	ID                 uint64    `gorm:"column:f_id"`                                          // 自增ID
	VarID              string    `gorm:"column:f_var_id"`                                      // 变量ID
	VarName            string    `gorm:"column:f_var_name"`                                    // 变量名称
	VarDesc            string    `gorm:"column:f_var_desc"`                                    // 变量描述
	VarType            string    `gorm:"column:f_var_type"`                                    // 变量类型
	VarDefaultValue    string    `gorm:"column:f_var_default_value"`                           // 变量默认值
	VarDefaultFileName string    `gorm:"column:f_var_default_file_name"`                       // 变量默认值文件名称
	AppID              string    `gorm:"column:f_app_id"`                                      // 词槽示例
	UIN                string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN             string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus      string    `gorm:"column:f_release_status"`                              // 发布状态 【未发布-UNPUBLISHED, 发布中-PUBLISHING, 已发布-PUBLISHED, 发布失败-FAIL】
	IsDeleted          int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action             string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】
	CreateTime         time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime         time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 变量参数表
func (VarParams) TableName() string {
	return "t_var"
}

// VarParamsColumns 变量参数表列
var VarParamsColumns = struct {
	ID            string
	VarID         string
	VarName       string
	VarDesc       string
	VarType       string
	AppID         string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	VarID:         "f_var_id",
	VarName:       "f_var_name",
	VarDesc:       "f_var_desc",
	VarType:       "f_var_type",
	AppID:         "f_app_id",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}
