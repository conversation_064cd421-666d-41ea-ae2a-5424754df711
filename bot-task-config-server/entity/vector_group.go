// bot-task-config-server
//
// @(#)entry_vector_group.go  星期五, 三月 22, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package entity

import (
	"time"
)

const (
	SaveEntryType  = "entry"
	SaveIntentType = "intent"
)

// VectorGroup ...
type VectorGroup struct {
	ID                uint64    `gorm:"column:f_id"`                  // 自增ID
	VectorGroupID     string    `gorm:"column:f_vector_group_id"`     // 向量组
	VectorGroupType   string    `gorm:"column:f_vector_group_type"`   // 环境区分：sandbox-沙箱环境， prod-正式环境
	SaveType          string    `gorm:"column:f_save_type"`           // group类型，entry，intent
	RobotID           string    `gorm:"column:f_robot_id"`            // 机器人ID
	EmbeddingModeName string    `gorm:"column:f_embedding_mode_name"` // 模型名称
	UIN               string    `gorm:"column:f_uin"`                 // 主用户ID
	SubUIN            string    `gorm:"column:f_sub_uin"`             // 子用户ID
	IsDeleted         int       `gorm:"column:f_is_deleted"`          // 是否删除
	CreatedAt         time.Time `gorm:"column:f_create_time"`         // 创建时间
	UpdatedAt         time.Time `gorm:"column:f_update_time"`         // 更新时间
}

// TableName ...
func (VectorGroup) TableName() string {
	return "t_vector_group"
}
