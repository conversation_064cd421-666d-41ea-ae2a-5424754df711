// bot-task-config-server
//
// @(#)workflow-example.go  星期五, 九月 27, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package entity

import "time"

// WorkflowExampleVectorOrg 存向量的原文
type WorkflowExampleVectorOrg struct {
	FlowID           string `json:"workflow_id"` // 工作流ID
	ExampleID        string `json:"example_id"`  // 示例ID
	Example          string `json:"example"`     // 示例问法
	ExampleVectorOrg string `json:"example_vector_org"`
	RobotId          string `json:"robot_id"` // 应用机器人Id
}

// WorkflowExample 关联示例问法
type WorkflowExample struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	FlowID        string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	ExampleID     string    `gorm:"column:f_example_id"`                                  // 示例ID
	Example       string    `gorm:"column:f_example"`                                     // 示例问法
	RobotId       string    `gorm:"column:f_robot_id"`                                    // 应用机器人Id
	Uin           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName ...
func (i WorkflowExample) TableName() string {
	return "t_workflow_example"
}

// WorkflowExampleColumns 关联示例问法列
var WorkflowExampleColumns = struct {
	ID            string
	FlowID        string
	ExampleID     string
	Example       string
	RobotID       string
	Uin           string
	SubUin        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	FlowID:        "f_workflow_id",
	ExampleID:     "f_example_id",
	Example:       "f_example",
	RobotID:       "f_robot_id",
	Uin:           "f_uin",
	SubUin:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}
