package entity

import "time"

// WorkflowGuide 工作流引导信息
type WorkflowGuide struct {
	ID         int64     `gorm:"column:f_id;primaryKey;autoIncrement"` // 自增ID
	Uin        string    `gorm:"column:f_uin"`                         // 主用户ID
	SubUin     string    `gorm:"column:f_sub_uin"`                     // 子用户ID
	Key        string    `gorm:"column:f_key"`                         // 标记已查看新手引导的key; 如： guide, tip, tipKnown (前端定义，后台不理解)
	Viewed     int8      `gorm:"column:f_viewed"`                      // 是否看过新手引导; 0: 没看过; 1: 已看过
	IsDeleted  int8      `gorm:"column:f_is_deleted"`                  // 是否删除; 0: 未删除; 1: 已删除
	CreateTime time.Time `gorm:"column:f_create_time"`                 // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time"`                 // 更新时间
}

func (WorkflowGuide) TableName() string {
	return "t_workflow_guide"
}
