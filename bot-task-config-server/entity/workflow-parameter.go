// bot-task-config-server
//
// @(#)workflow-parameter.go  星期一, 十月 14, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package entity

import (
	"context"
	"fmt"
	"time"
)

var WFParameterEntry = "TaskConfig:ENV:%s:AppID:%s:ParameterEntry"
var WFParameterInvalidEntry = "TaskConfig:ENV:%s:AppID:%s:ParameterInvalidEntry"

const (
	SandboxEnv = "Sandbox"
	ProductEnv = "Product"
)

// GetWFParameterEntryKey 获取正确示例redis的key
func GetWFParameterEntryKey(ctx context.Context, envName, robotID string) string {
	return fmt.Sprintf(WFParameterEntry, envName, robotID)
}

// GetWFParameterInvalidEntryKey 获取错误示例redis的key
func GetWFParameterInvalidEntryKey(ctx context.Context, envName, robotID string) string {
	return fmt.Sprintf(WFParameterInvalidEntry, envName, robotID)
}

// ParameterChecks 创建更新Parameter时的传参
type ParameterChecks struct {
	NodeParamIds      []string
	CorrectExamples   []string
	IncorrectExamples []string
	UnExceptParamID   string
	ParamName         string
	ParamDesc         string
	ParamType         string
	CustomAsk         string
}

// WFParamEntryInfo 正确示例redis
type WFParamEntryInfo struct {
	Value     string   `json:"Value"`
	AliasName []string `json:"AliasName"`
}

// WFParamInvalidEntryInfo 错误示例redis
type WFParamInvalidEntryInfo struct {
	Value string `json:"Value"`
}

// WFImportEntryData 词条导入任务参数
type WFImportEntryData struct {
	ParamID string                  `json:"param_id"` // 参数ID
	Entries []*WFEntryMigrationInfo `json:"entries"`  // 词条信息
}

// WFEntryMigrationInfo 词条迁移信息 -- 导入/导出使用
type WFEntryMigrationInfo struct {
	EntryID    string   `json:"entry_id"`    // 词条ID
	EntryValue string   `json:"entry_value"` // 词条的值
	EntryAlias []string `json:"entry_alias"` // 词条别名
}

// WorkflowParameter 工作流参数的关联表
type WorkflowParameter struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	AppBizID      string    `gorm:"column:f_robot_id"`                                    // 应用ID
	WorkFlowID    string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	NodeID        string    `gorm:"column:f_node_id"`                                     // 工作流中的NodeID
	NodeName      string    `gorm:"column:f_node_name"`                                   // 工作流中的Node名称
	ParameterID   string    `gorm:"column:f_parameter_id"`                                // 参数ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除 0-未删除 >0 删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】`
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 工作流参数的关联表
func (WorkflowParameter) TableName() string {
	return "t_workflow_parameter"
}

// WorkflowParameterColumns 工作流参数的关联表列
var WorkflowParameterColumns = struct {
	ID            string
	AppBizID      string
	WorkFlowID    string
	NodeID        string
	NodeName      string
	ParameterID   string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	AppBizID:      "f_robot_id",
	WorkFlowID:    "f_workflow_id",
	NodeID:        "f_node_id",
	NodeName:      "f_node_name",
	ParameterID:   "f_parameter_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// Parameter 参数表
type Parameter struct {
	ID                uint64    `gorm:"column:f_id"`                                          // 主键ID
	ParameterID       string    `gorm:"column:f_parameter_id"`                                // 参数ID
	ParameterName     string    `gorm:"column:f_parameter_name"`                              // 参数名称
	ParameterDesc     string    `gorm:"column:f_parameter_desc"`                              // 参数描述
	ParameterType     string    `gorm:"column:f_parameter_type"`                              // 参数类型
	CorrectExamples   string    `gorm:"column:f_correct_examples"`                            // 正确示例
	IncorrectExamples string    `gorm:"column:f_incorrect_examples"`                          // 错误示例
	CustomAsk         string    `gorm:"column:f_custom_ask"`                                  // 自定义追问话术
	CustomAskEnable   bool      `gorm:"column:f_custom_ask_enable"`                           //自定义追问话术是否开启
	AppID             string    `gorm:"column:f_robot_id"`                                    // 应用ID
	UIN               string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN            string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus     string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted         int       `gorm:"column:f_is_deleted"`                                  // 是否删除 0-未删除 >0 删除
	Action            string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】`
	CreateTime        time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime        time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 参数表
func (Parameter) TableName() string {
	return "t_parameter"
}

// ParameterColumns 参数表列
var ParameterColumns = struct {
	ID                string
	ParameterID       string
	ParameterName     string
	ParameterDesc     string
	ParameterType     string
	CorrectExamples   string
	IncorrectExamples string
	CustomAsk         string
	CustomAskEnable   string
	AppID             string
	UIN               string
	SubUIN            string
	ReleaseStatus     string
	IsDeleted         string
	Action            string
	CreateTime        string
	UpdateTime        string
}{
	ID:                "f_id",
	ParameterID:       "f_parameter_id",
	ParameterName:     "f_parameter_name",
	ParameterDesc:     "f_parameter_desc",
	ParameterType:     "f_parameter_type",
	CorrectExamples:   "f_correct_examples",
	IncorrectExamples: "f_incorrect_examples",
	CustomAsk:         "f_custom_ask",
	CustomAskEnable:   "f_custom_ask_enable",
	AppID:             "f_robot_id",
	UIN:               "f_uin",
	SubUIN:            "f_sub_uin",
	ReleaseStatus:     "f_release_status",
	IsDeleted:         "f_is_deleted",
	Action:            "f_action",
	CreateTime:        "f_create_time",
	UpdateTime:        "f_update_time",
}

// WorkflowEntry 词条表（某个参数下的词条,正确示例）
type WorkflowEntry struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	AppBizID      string    `gorm:"column:f_robot_id"`                                    // 应用ID
	EntryID       string    `gorm:"column:f_entry_id"`                                    // 词条ID
	ParameterID   string    `gorm:"column:f_parameter_id"`                                // 参数ID
	EntryValue    string    `gorm:"column:f_entry_value"`                                 // 词条值
	EntryAlias    string    `gorm:"column:f_entry_alias"`                                 // 词条别名(同义词)
	UIN           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除 0-未删除 >0 删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】`
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间

}

// TableName 词条表（某个参数下的词条,正确示例）
func (WorkflowEntry) TableName() string {
	return "t_entry"
}

// WorkflowEntryColumns 词条表列
var WorkflowEntryColumns = struct {
	ID            string
	AppBizID      string
	EntryID       string
	ParameterID   string
	EntryValue    string
	EntryAlias    string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	AppBizID:      "f_robot_id",
	EntryID:       "f_entry_id",
	ParameterID:   "f_parameter_id",
	EntryValue:    "f_entry_value",
	EntryAlias:    "f_entry_alias",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// WorkflowInvalidEntry 词条错误示例表（某个参数下的词条,错误示例）
type WorkflowInvalidEntry struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	AppBizID      string    `gorm:"column:f_robot_id"`                                    // 应用ID
	EntryID       string    `gorm:"column:f_entry_id"`                                    // 词条ID
	ParameterID   string    `gorm:"column:f_parameter_id"`                                // 参数ID
	EntryValue    string    `gorm:"column:f_entry_value"`                                 // 词条值
	UIN           string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUIN        string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除 0-未删除 >0 删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作 【INSERT-插入，UPDATE-更新，DELETED-删除】`
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间

}

// TableName 词条表（某个参数下的词条,正确示例）
func (WorkflowInvalidEntry) TableName() string {
	return "t_invalid_entry"
}

// WorkflowInvalidEntryColumns 词条错误示例表列
var WorkflowInvalidEntryColumns = struct {
	ID            string
	AppBizID      string
	EntryID       string
	ParameterID   string
	EntryValue    string
	UIN           string
	SubUIN        string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	AppBizID:      "f_robot_id",
	EntryID:       "f_entry_id",
	ParameterID:   "f_parameter_id",
	EntryValue:    "f_entry_value",
	UIN:           "f_uin",
	SubUIN:        "f_sub_uin",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}
