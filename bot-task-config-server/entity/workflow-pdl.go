/*
 * 2024-12-22
 * Copyright (c) 2024. levon<PERSON>@Tencent. All rights reserved.
 *
 */

package entity

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"time"
)

var FrontWorkflowPdlStatusPublishDesc = map[string]string{
	WorkflowPdlFrontStatusUnableRelease:  "不支持发布",
	WorkflowPdlFrontStatusUnConverted:    "待转换",
	WorkflowPdlFrontStatusUnableConvert:  "不可转换",
	WorkflowPdlFrontStatusConverting:     "转换中",
	WorkflowPdlFrontStatusConvertedFail:  "转换失败",
	WorkflowPdlFrontStatusPublishedDraft: "待调试",
	WorkflowPdlFrontStatusUnPublished:    "待发布",
	WorkflowPdlFrontStatusPublishing:     "发布中",
	WorkflowPdlFrontStatusPublished:      "已发布",
	WorkflowPdlFrontStatusPublishedFail:  "发布失败",
}

const (
	// Agent模式（未开通PDL白名单）
	WorkflowPdlFrontStatusUnableRelease = "UNABLERELEASE" // 不支持发布
	// 工作流PDL前端列表状态
	WorkflowPdlFrontStatusUnConverted    = "UNCONVERTED"    // 待转换
	WorkflowPdlFrontStatusUnableConvert  = "UNABLECONVERT"  // 不可转换
	WorkflowPdlFrontStatusConverting     = "CONVERTING"     // 转换中
	WorkflowPdlFrontStatusConvertedFail  = "CONVERTED_FAIL" // 转换失败
	WorkflowPdlFrontStatusPublishedDraft = "DRAFT"          // 待调试
	WorkflowPdlFrontStatusUnPublished    = "UNPUBLISHED"    // 待发布
	WorkflowPdlFrontStatusPublishing     = "PUBLISHING"     // 发布中
	WorkflowPdlFrontStatusPublished      = "PUBLISHED"      // 已发布
	WorkflowPdlFrontStatusPublishedFail  = "FAIL"           // 发布失败

	// 画布流转PDL状态（不属于PDL的状态，是画布的状态）
	WorkflowPdlStateUnConverted   = "UNCONVERTED"   // 待转换
	WorkflowPdlStateUnableConvert = "UNABLECONVERT" // 不可转换
	// 工作流转换状态
	WorkflowPdlStateConverted              = "CONVERTED"                // 已转换：待调试的状态
	WorkflowPdlStatePublishedConverted     = "PUBLISHED_CONVERTED"      // 已发布-已转换：待调试的状态（已发布）
	WorkflowPdlStateConverting             = "CONVERTING"               // 转换中: 不允许保存
	WorkflowPdlStatePublishedConverting    = "PUBLISHED_CONVERTING"     // 已发布-转换中: 不允许保存（已发布）
	WorkflowPdlStateConvertedFail          = "CONVERTED_FAIL"           // 转换失败：转换失败都无法流转，需要重新触发转换
	WorkflowPdlStatePublishedConvertedFail = "PUBLISHED_CONVERTED_FAIL" // 已发布-转换失败：转换失败都无法流转，需要重新触发转换（已发布）
	// 工作流编辑状态
	WorkflowPdlStateDraft           = "DRAFT"            // 待调试:原始草稿态，第一次新建，且从来没有经过调试及发布的状态
	WorkflowPdlStateEnable          = "ENABLE"           // 启用（待发布）： 从未发布，并经过调试状态
	WorkflowPdlStatePublishedChange = "PUBLISHED_CHANGE" // 工作流状态 已发布仍有修改（待更新发布）：已经发布过，但有修改，经过调试的状态
	WorkflowPdlStatePublishedDraft  = "PUBLISHED_DRAFT"  // 已发布-草稿态： 已发布-草稿态（已经发布过，但有修改，没有经过调试的状态）
	// 工作流发布状态
	WorkflowPdlReleaseStatusUnPublished   = "UNPUBLISHED" // 待发布
	WorkflowPdlReleaseStatusPublishing    = "PUBLISHING"  // 发布中
	WorkflowPdlReleaseStatusPublished     = "PUBLISHED"   // 已发布
	WorkflowPdlReleaseStatusPublishedFail = "FAIL"        // 发布失败

	// pdl不可转换原因的枚举值定义
	PDLReasonWorkflowNotDebugged      = "WORKFLOW_NOT_DEBUGGED"      // 画布未调试
	PDLReasonUnsupportedNode          = "UNSUPPORTED_NODE"           // 画布包含不支持的节点类型
	PDLReasonUnsupportedPlugin        = "UNSUPPORTED_PLUGIN"         // 画布包含不支持的插件类型
	PDLReasonParallelBranches         = "PARALLEL_BRANCHES"          // 画布存在并行分支
	PDLReasonConditionChatHistory     = "CONDITION_CHAT_HISTORY"     // 不支持条件判断节点引用系统变量SYS.ChatHistory
	PDLReasonAnswerChatHistory        = "ANSWER_CHAT_HISTORY"        // 不支持回复节点引用系统变量SYS.ChatHistory
	PDLReasonReferenceConditionOutput = "REFERENCE_CONDITION_OUTPUT" // 不支持变量中引用条件判断节点的条件序号
	// pdl转换失败原因的枚举值定义
	PDLReasonExpressLenExceeded = "EXPRESS_LEN_EXCEEDED" // 条件表达式长度超过2k
	PDLReasonWorkflowIllegal    = "WORKFLOW_ILLEGAL"     // 画布数据异常
	PDLReasonSystemError        = "SYSTEM_ERROR"         // 系统错误

)

// GetPDLReasonDesc 获取pdl不可转换和转换失败等原因枚举值对应的描述
func GetPDLReasonDesc(ctx context.Context, reasonCode string) string {
	if reason, ok := config.GetMainConfig().PDLConfig.ReasonDesc[reasonCode]; ok {
		return reason
	} else {
		log.InfoContextf(ctx, "reasonCode:%s not found in config", reasonCode)
		return ""
	}
}

// WorkflowPDL PDL工作流
type WorkflowPDL struct {
	ID                 uint64    `gorm:"column:f_id"`                                          // 主键ID
	PdlID              string    `gorm:"column:f_pdl_id"`                                      // PDL唯一ID
	PdlSnapshotVersion uint32    `gorm:"column:f_pdl_snapshot_version"`                        // pdl快照版本
	WorkflowID         string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	WorkflowName       string    `gorm:"column:f_workflow_name"`                               // 工作流名称
	WorkflowState      string    `gorm:"column:f_flow_state"`                                  // 工作流状态： 工作流状态：CONVERTING 转换中；CONVERT_FAILED 转换失败；CONVERTED_DRAFT 已转换待调试
	Version            uint64    `gorm:"column:f_version"`                                     // 版本
	RobotId            string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	DialogJsonEnable   string    `gorm:"column:f_dialog_json_enable"`                          // 启用状态下的节点信息
	Parameter          string    `gorm:"column:f_parameter"`                                   // 参数
	PdlContent         string    `gorm:"column:f_pdl_content"`                                 // PDL 字段内容
	ToolsInfo          string    `gorm:"column:f_tools_info"`                                  // PDL 对应的APIInfos
	UserConstraints    string    `gorm:"column:f_user_constraints"`                            // PDL 的约束
	StaffID            uint64    `gorm:"column:f_staff_id"`                                    // 操作人
	ReleaseStatus      string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted          int       `gorm:"column:f_is_deleted"`                                  // 0未删除 1已删除
	IsEnable           bool      `gorm:"column:f_is_enable"`                                   // 生产环境是否启用
	Action             string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	FailReason         string    `gorm:"column:f_fail_reason"`                                 // 转换失败的原因
	PdlCreateTime      time.Time `gorm:"column:f_pdl_time;type:datetime;null;default:null"`    // PDL 创建时间
	CreateTime         time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime         time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// WorkflowPdlColumns 工作流列
var WorkflowPdlColumns = struct {
	ID                 string
	PdlID              string
	PdlSnapshotVersion string
	WorkflowID         string
	WorkflowName       string
	WorkflowState      string
	Version            string
	RobotID            string
	DialogJsonEnable   string
	Parameter          string
	PdlContent         string
	ToolsInfo          string
	UserConstraints    string
	StaffID            string
	ReleaseStatus      string
	IsDeleted          string
	IsEnable           string
	Action             string
	FailReason         string
	PdlCreateTime      string
	CreateTime         string
	UpdateTime         string
}{
	ID:                 "f_id",
	PdlID:              "f_pdl_id",
	PdlSnapshotVersion: "f_pdl_snapshot_version",
	WorkflowID:         "f_workflow_id",
	WorkflowName:       "f_workflow_name",
	WorkflowState:      "f_flow_state",
	Version:            "f_version",
	RobotID:            "f_robot_id",
	DialogJsonEnable:   "f_dialog_json_enable",
	Parameter:          "f_parameter",
	PdlContent:         "f_pdl_content",
	ToolsInfo:          "f_tools_info",
	UserConstraints:    "f_user_constraints",
	StaffID:            "f_staff_id",
	ReleaseStatus:      "f_release_status",
	IsDeleted:          "f_is_deleted",
	IsEnable:           "f_is_enable",
	Action:             "f_action",
	FailReason:         "f_fail_reason",
	PdlCreateTime:      "f_pdl_time",
	CreateTime:         "f_create_time",
	UpdateTime:         "f_update_time",
}

// TableName Workflow表名
func (w WorkflowPDL) TableName() string {
	return "t_workflow_pdl"
}

func (w WorkflowPDL) GetIsEnable() bool {
	return w.IsEnable
}

// IsAllowEdit 节点是否可编辑
func (w *WorkflowPDL) IsAllowEdit() bool {
	if w == nil {
		return false
	}
	// 发布中 || 转换中 不可编辑
	if w.WorkflowState == WorkflowPdlReleaseStatusPublishing ||
		w.IsWorkflowPDLConverting() {
		return false
	}
	return true
}

// IsAllowRelease 节点是否可发布
func (w *WorkflowPDL) IsAllowRelease() bool {
	if w == nil {
		return false
	}
	// 草稿；转换中；转换失败 ==> 不可发布
	if w.WorkflowState == WorkflowPdlStateDraft ||
		w.IsWorkflowPDLConverting() ||
		w.IsWorkflowPDLConvertedFailed() {
		return false
	}
	return true
}

// IsAllowDelete 节点是否可删除
func (w *WorkflowPDL) IsAllowDelete() bool {
	if w == nil {
		return false
	}
	// 发布中/转换中 不可以删除
	if w.ReleaseStatus == WorkflowPdlReleaseStatusPublishing ||
		w.IsWorkflowPDLConverting() {
		return false
	}
	return true
}

// IsAllowConvert 是否可以转换PDL
func (w *WorkflowPDL) IsAllowConvert() bool {
	if w == nil {
		return false
	}
	// 发布中/转换中 不可以转换
	if w.ReleaseStatus == WorkflowPdlReleaseStatusPublishing ||
		w.IsWorkflowPDLConverting() {
		return false
	}
	return true
}

// IsWorkflowUpdate 判断工作流是否有更新
func (w *WorkflowPDL) IsWorkflowUpdate(workflow *Workflow) bool {
	isWorkflowUpdate := w.PdlCreateTime.Before(workflow.UpdateTime)
	return isWorkflowUpdate
}

// FrontReleaseStatus 前端发布状态
func (w *WorkflowPDL) FrontReleaseStatus() string {
	if w == nil {
		return ""
	}
	// PDL不会逆转到工作流，但是如果触发了工作流转PDL，那么State状态就是转换中
	if w.IsWorkflowPDLConverting() {
		return WorkflowPdlFrontStatusConverting
	}

	// 如果转换失败，状态是转换失败，可以通过保存恢复到待发布状态
	if w.IsWorkflowPDLConvertedFailed() {
		return WorkflowPdlFrontStatusConvertedFail
	}

	//  f_flow_state： DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)
	if w.WorkflowState == WorkflowPdlStatePublishedDraft || w.WorkflowState == WorkflowPdlStateDraft {
		return WorkflowPdlFrontStatusPublishedDraft // 草稿
	}

	// 发布中: f_release_status： PUBLISHING(发布中)
	if w.ReleaseStatus == WorkflowPdlReleaseStatusPublishing {
		return WorkflowPdlFrontStatusPublishing //  发布中
	}

	//已发布: f_release_status： PUBLISHED(已发布)， f_flow_state：不是草稿态（ DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)）
	if w.ReleaseStatus == WorkflowPdlReleaseStatusPublished &&
		w.WorkflowState != WorkflowPdlStateDraft && w.WorkflowState != WorkflowPdlStatePublishedDraft {
		return WorkflowPdlFrontStatusPublished //  已发布
	}

	//待发布：f_release_status： UNPUBLISHED(未发布)，
	//      f_flow_state：不是草稿态（ DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)）
	if w.ReleaseStatus == WorkflowPdlReleaseStatusUnPublished &&
		w.WorkflowState != WorkflowPdlStateDraft && w.WorkflowState != WorkflowPdlStatePublishedDraft {
		return WorkflowPdlFrontStatusUnPublished // 待发布
	}

	//发布失败：f_release_status：FAIL(发布失败)
	if w.ReleaseStatus == WorkflowPdlReleaseStatusPublishedFail {
		return WorkflowPdlFrontStatusPublishedFail // 发布失败
	}
	return ""
}

// FrontReleaseStatusDesc 前端发布状态描述
func (w *WorkflowPDL) FrontReleaseStatusDesc() string {
	if w == nil {
		return ""
	}
	w.ReleaseStatus = w.FrontReleaseStatus()
	return FrontWorkflowPdlStatusPublishDesc[w.ReleaseStatus]
}

// IsWorkflowPDLPublished 工作流PDL是否发布过
func (w *WorkflowPDL) IsWorkflowPDLPublished() bool {
	if w.WorkflowState == WorkflowPdlStateDraft ||
		w.WorkflowState == WorkflowPdlStateEnable ||
		w.WorkflowState == WorkflowPdlStateConverting ||
		w.WorkflowState == WorkflowPdlStateConverted ||
		w.WorkflowState == WorkflowPdlStateConvertedFail {
		return false
	}
	return true
}

// IsWorkflowPDLConverting 判断工作流是否正在转换
func (w *WorkflowPDL) IsWorkflowPDLConverting() bool {
	if w.WorkflowState == WorkflowPdlStateConverting || w.WorkflowState == WorkflowPdlStatePublishedConverting {
		return true
	}
	return false
}

// IsWorkflowPDLConvertedFailed 判断工作流是否转换失败
func (w *WorkflowPDL) IsWorkflowPDLConvertedFailed() bool {
	if w.WorkflowState == WorkflowPdlStateConvertedFail || w.WorkflowState == WorkflowPdlStatePublishedConvertedFail {
		return true
	}
	return false
}

// WorkflowPDLPublishHistory PDL发布记录
type WorkflowPDLPublishHistory struct {
	ID               uint64    `gorm:"column:f_id"`                                           // 主键ID
	PdlID            string    `gorm:"column:f_pdl_id"`                                       // PDL唯一ID
	PdlVersion       string    `gorm:"column:f_pdl_version"`                                  // 版本
	RobotId          string    `gorm:"column:f_robot_id"`                                     // 机器人ID
	DialogJsonEnable string    `gorm:"column:f_dialog_json_enable"`                           // 启用状态下的节点信息
	Parameter        string    `gorm:"column:f_parameter"`                                    // 参数
	PdlContent       string    `gorm:"column:f_pdl_content"`                                  // PDL 字段内容
	ToolsInfo        string    `gorm:"column:f_tools_info"`                                   // PDL 对应的APIInfos
	UserConstraints  string    `gorm:"column:f_user_constraints"`                             // PDL 的约束
	IsDeleted        int       `gorm:"column:f_is_deleted"`                                   // 0未删除 1已删除
	PublishTime      time.Time `gorm:"column:f_publish_time;type:datetime;null;default:null"` // 发布时间
	CreateTime       time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"`  // 创建时间
	UpdateTime       time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"`  // 更新时间
}

// TableName 返回函数名称
func (h WorkflowPDLPublishHistory) TableName() string {
	return "t_pdl_history"
}

// ListPDLParams 获取PDL列表传参数
type ListPDLParams struct {
	Query         string    // 筛选 PDL名称
	StartTime     time.Time // 筛选开始时间
	EndTime       time.Time // 筛选结束时间
	Page          uint32    // 分页页码
	PageSize      uint32    // 分页页数
	BotBizId      string    // 应用ID
	OrderBy       string    // 排序顺序
	Actions       []string  // 执行动作
	FlowState     []string  // PDL状态
	ReleaseStatus []string  // 发布状态
	PDLIds        []string  // PDL IDs
}

// ModifyWorkflowPDLParams  修改工作流PDL传参数
type ModifyWorkflowPDLParams struct {
	PdlID              string
	PdlSnapshotVersion uint32 // pdl快照版本
	WorkflowID         string
	WorkflowName       string
	Version            uint64    // 请求带上来的版本号
	AppBizID           string    // 应用ID
	PdlContent         string    // PDL工作流内容
	ApiInfo            string    // API INFO内容
	Constraints        string    // PDL约束
	Parameter          string    // 参数
	StaffID            uint64    // 操作人
	UpdateTime         time.Time // 更新时间
	ReleaseStatus      string    // 发布状态
	FlowState          string    // 工作流状态
	IsDebug            uint32    // 是否调试（保存测试环境）
	IsConverted        bool      // 是否已转换
}

// UpdateWorkflowPDLConvertStateParams 修改工作流PDL转换状态参数
type UpdateWorkflowPDLConvertStateParams struct {
	WorkflowID        string
	AppBizID          string // 应用ID
	IsConverting      bool
	IsConvertedFailed bool
	IsConverted       bool
	FailedReason      string
}

// PDLVersion PDL快照版本表
type PDLVersion struct {
	ID                      int64     `gorm:"column:f_id"`                                          // 主键自增ID
	PdlID                   string    `gorm:"column:f_pdl_id"`                                      // PDL唯一ID
	PdlSnapshotVersion      uint32    `gorm:"column:f_pdl_snapshot_version"`                        // pdl快照版本
	WorkflowID              string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	RobotID                 string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	WorkflowName            string    `gorm:"column:f_workflow_name"`                               // 工作流名称
	DialogJsonEnable        string    `gorm:"column:f_dialog_json_enable"`                          // 画布快照
	Parameter               string    `gorm:"column:f_parameter"`                                   // 参数
	PdlContent              string    `gorm:"column:f_pdl_content"`                                 // 画布转换PDL的结果（修改前）
	ToolsInfo               string    `gorm:"column:f_tools_info"`                                  // PDL对应的APIInfos（修改前）
	UserConstraints         string    `gorm:"column:f_user_constraints"`                            // PDL的约束（修改前）
	PdlContentModified      string    `gorm:"column:f_pdl_content_modified"`                        // 修改后的pdl内容
	ToolsInfoModified       string    `gorm:"column:f_tools_info_modified"`                         // 修改后的APIInfos
	UserConstraintsModified string    `gorm:"column:f_user_constraints_modified"`                   // 修改后的约束
	CreateTime              time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime              time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// PDLVersionColumns PDL版本列名
var PDLVersionColumns = struct {
	ID                      string
	PdlID                   string
	PdlSnapshotVersion      string
	WorkflowID              string
	WorkflowName            string
	RobotID                 string
	DialogJsonEnable        string
	Parameter               string
	PdlContent              string
	ToolsInfo               string
	UserConstraints         string
	PdlContentModified      string
	ToolsInfoModified       string
	UserConstraintsModified string
	CreateTime              string
	UpdateTime              string
}{
	ID:                      "f_id",
	PdlID:                   "f_pdl_id",
	PdlSnapshotVersion:      "f_pdl_snapshot_version",
	WorkflowID:              "f_workflow_id",
	WorkflowName:            "f_workflow_name",
	RobotID:                 "f_robot_id",
	DialogJsonEnable:        "f_dialog_json_enable",
	Parameter:               "f_parameter",
	PdlContent:              "f_pdl_content",
	ToolsInfo:               "f_tools_info",
	UserConstraints:         "f_user_constraints",
	PdlContentModified:      "f_pdl_content_modified",
	ToolsInfoModified:       "f_tools_info_modified",
	UserConstraintsModified: "f_user_constraints_modified",
	CreateTime:              "f_create_time",
	UpdateTime:              "f_update_time",
}

func (m *PDLVersion) TableName() string {
	return "t_pdl_version"
}
