// bot-task-config-server
//
// @(#)workflow_model.go  星期五, 二月 21, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package entity

import "time"

const (
	HighPerformancePrice = "high_performance_price" //高性价比
	Recommend            = "recommend"              //推荐
	DepthThink           = "depth_think"            // 深度推理
	MoreBalanced         = "more_balanced"          // 较均衡
	LimitTimeFree        = "limit_time_free"        // 限时免费
	DefaultSelected      = "default_selected"       // 默认选中

	ThoughtModel = "thought"
)

// WorkflowCustomModel 工作流关联自定义模型
type WorkflowCustomModel struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	AppBizID      string    `gorm:"column:f_robot_id"`                                    // 应用ID
	WorkflowID    string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	NodeID        string    `gorm:"column:f_node_id"`                                     // 工作流中的NodeID
	NodeType      string    `gorm:"column:f_node_type"`                                   // 工作流中的NodeType
	ModelName     string    `gorm:"column:f_model_name"`                                  // 自定义模型名称
	StaffID       uint64    `gorm:"column:f_staff_id"`                                    // 员工ID
	CorpID        uint64    `gorm:"column:f_corp_id"`                                     // 企业ID
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     int       `gorm:"column:f_is_deleted"`                                  // 是否删除 0-未删除 >0 删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName 工作流参数的关联表
func (WorkflowCustomModel) TableName() string {
	return "t_workflow_custom_model"
}

// WorkflowCustomModelColumns ...
var WorkflowCustomModelColumns = struct {
	ID            string
	AppBizID      string
	WorkflowID    string
	NodeID        string
	NodeType      string
	ModelName     string
	StaffID       string
	CorpID        string
	Action        string
	ReleaseStatus string
	IsDeleted     string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	AppBizID:      "f_robot_id",
	WorkflowID:    "f_workflow_id",
	NodeID:        "f_node_id",
	NodeType:      "f_node_type",
	ModelName:     "f_model_name",
	StaffID:       "f_staff_id",
	CorpID:        "f_corp_id",
	Action:        "f_action",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// WorkflowNodeCustomModel 工作流节点使用自定义模型
type WorkflowNodeCustomModel struct {
	NodeID    string // 节点Id
	NodeType  string // 节点类型
	ModelName string // 节点下使用的模型名称
}
