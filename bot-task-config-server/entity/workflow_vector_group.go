// package entity 新向量实体

package entity

import (
	"time"
	"unsafe"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	SaveWorkflowType    = "workflow"
	SaveWFEntryType     = "entry"
	FlowNameExamsDescID = "%s-examples-desc"
)

// VectorStore 向量存储
type VectorStore struct {
	ID         uint64    `gorm:"column:f_id"`              // ID
	BizID      string    `gorm:"column:f_biz_id"`          // 业务外Id，工作流workflowId|示例问法example_id
	RobotID    string    `gorm:"column:f_robot_id"`        // 机器人ID
	SaveType   string    `gorm:"column:f_type"`            // embedding 内容类型，FLOW_NAME:工作流|EXAMPLE:示例问法|FLOW_NAME_EXAMPLE_DESC:名称+示例+描述
	Content    string    `gorm:"column:f_content"`         // content
	ModelName  string    `gorm:"column:f_embedding_model"` // embedding 模型
	VectorRaw  []byte    `gorm:"column:f_vector"`          // 向量
	vector     []float32 // 向量
	IsDeleted  int       `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateTime time.Time `gorm:"column:f_create_time;type:datetime(0);autoCreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;type:datetime(0);autoUpdateTime"` // 更新时间
}

// TableName ...
func (VectorStore) TableName() string {
	return "t_vector_store"
}

// Vector 获取向量
func (m *VectorStore) Vector() []float32 {
	if len(m.vector) > 0 {
		return m.vector
	}
	m.vector = bytesToFloats(m.VectorRaw)
	return m.vector
}

// SetVector 设置向量
func (m *VectorStore) SetVector(v []float32) {
	m.vector = v
	m.VectorRaw = floatsToBytes(v)
	log.Debugf("SetVector|%+v|floatsToBytes:%+v", v, floatsToBytes(v))
}

func floatsToBytes(floats []float32) []byte {
	sizeofFloat32 := int(unsafe.Sizeof(float32(0)))
	if len(floats) == 0 {
		return []byte{}
	}
	ptr := unsafe.Pointer(&floats[0])
	dataLen := len(floats) * sizeofFloat32
	g := make([]byte, dataLen)
	copy(g, (*[1 << 30]byte)(ptr)[:dataLen:dataLen])
	return g
}

func bytesToFloats(bytes []byte) []float32 {
	sizeofFloat32 := int(unsafe.Sizeof(float32(0)))
	if len(bytes) < sizeofFloat32 {
		return []float32{}
	}
	ptr := unsafe.Pointer(&bytes[0])
	dataLen := len(bytes) / sizeofFloat32
	g := make([]float32, dataLen)
	copy(g, (*[1 << 30]float32)(ptr)[:dataLen:dataLen])
	return g
}

// WorkflowVectorGroup ...
type WorkflowVectorGroup struct {
	ID                uint64    `gorm:"column:f_id"`                  // 自增ID
	VectorGroupID     string    `gorm:"column:f_vector_group_id"`     // 向量组
	VectorGroupType   string    `gorm:"column:f_vector_group_type"`   // 环境区分：sandbox-沙箱环境， prod-正式环境
	SaveType          string    `gorm:"column:f_save_type"`           // group类型，workflow
	RobotID           string    `gorm:"column:f_robot_id"`            // 机器人ID
	EmbeddingModeName string    `gorm:"column:f_embedding_mode_name"` // 模型名称
	UIN               string    `gorm:"column:f_uin"`                 // 主用户ID
	SubUIN            string    `gorm:"column:f_sub_uin"`             // 子用户ID
	IsDeleted         int       `gorm:"column:f_is_deleted"`          // 是否删除
	CreatedAt         time.Time `gorm:"column:f_create_time"`         // 创建时间
	UpdatedAt         time.Time `gorm:"column:f_update_time"`         // 更新时间
	UpgradeStatus     int       `gorm:"column:f_upgrade_status"`      // 向量库是否在升级中，0:升级完成|1:升级中
}

// TableName ...
func (WorkflowVectorGroup) TableName() string {
	return "t_vector_group"
}

// 向量库是否在升级中，升级中仅允许查看，不允许增删embedding向量或删除向量库
func (m *WorkflowVectorGroup) IsUpgrading() bool {
	return m.UpgradeStatus == 1
}

// 向量库升级中
func (m *WorkflowVectorGroup) Upgrade() {
	m.UpgradeStatus = 1
}

// 向量库升级完成
func (m *WorkflowVectorGroup) UpgradeDone() {
	m.UpgradeStatus = 0
}
