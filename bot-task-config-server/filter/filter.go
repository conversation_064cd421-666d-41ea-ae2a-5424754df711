// Package filter  trpc的过滤器
package filter

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	adminPB "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// Init 初始化
func Init() {
	filter.Register("session", SessionFilter, nil)
	filter.Register("permission", PermissionFilter, nil)
	filter.Register("client_track", nil, ClientTrackFilter)
}

// SessionFilter 登录态 Filter
var SessionFilter = func(ctx context.Context, req any, next filter.ServerHandleFunc) (rsp any, err error) {
	if ignoreSession(ctx) {
		log.DebugContext(ctx, "SessionFilter|ignoreSession")
		return next(ctx, req)
	}
	sessionReq := &adminPB.CheckSessionReq{}
	sessionRsp, err := rpc.CheckSession(ctx, sessionReq)
	if err != nil {
		return rsp, err
	}
	log.InfoContextf(ctx, "staffId:%d corpId:%d sid:%d", sessionRsp.GetStaffId(), sessionRsp.GetCorpId(),
		sessionRsp.GetSId())
	ctx = util.WithStaffID(ctx, sessionRsp.GetStaffId())
	ctx = util.WithCorpID(ctx, sessionRsp.GetCorpId())
	ctx = util.WithSID(ctx, sessionRsp.GetSId())
	return next(ctx, req)
}

// PermissionFilter 权限过滤
var PermissionFilter = func(ctx context.Context, req any, next filter.ServerHandleFunc) (rsp any, err error) {
	action := util.Action(ctx)
	log.WithContextFields(ctx,
		"RequestID", util.RequestID(ctx),
		cloudparam.RequestId, cloudparam.GetApIParam(ctx, cloudparam.RequestId),
		"CallerServiceName", trpc.Message(ctx).CallerServiceName(),
		"CallerMethod", trpc.Message(ctx).CallerMethod(),
		"action", action,
		cloudparam.Action, cloudparam.GetApIParam(ctx, cloudparam.Action),
		"CalleeServiceName", trpc.Message(ctx).CalleeServiceName(),
		"CalleeMethod", trpc.Message(ctx).CalleeMethod(),
		cloudparam.Uin, cloudparam.GetApIParam(ctx, cloudparam.Uin),
		cloudparam.SubAccountUin, cloudparam.GetApIParam(ctx, cloudparam.SubAccountUin),
		"login_uin", util.LoginUin(ctx),
		"login_sub_account_uin", util.LoginSubAccountUin(ctx),
		"uin", util.Uin(ctx),
		"sub_account_uin", util.SubAccountUin(ctx),
		"SID", strconv.Itoa(util.SID(ctx)),
		"CorpID", strconv.FormatUint(util.CorpID(ctx), 10),
	)
	trpc.SetMetaData(ctx, "_sid_", []byte(encode.GenerateSessionID()))
	if ignorePermission(ctx) {
		log.DebugContext(ctx, "PermissionFilter|ignorePermission")
		return next(ctx, req)
	}
	log.DebugContext(ctx, "PermissionFilter|CheckPermission")

	allow, err := permission.CheckPermission(ctx, action)
	if err != nil {
		log.ErrorContextf(ctx, "CheckPermission|err:%+v", err)
		return rsp, err
	}
	log.InfoContextf(ctx, "CheckPermission|allow:%t", allow)
	if !allow {
		return rsp, errors.ErrPermissionDenied
	}
	return next(ctx, req)
}

func ignoreSession(ctx context.Context) bool {
	callerServiceName := trpc.Message(ctx).CallerServiceName()

	switch callerServiceName {
	case "trpc.KEP.bot-admin-config-server.Admin":
		return true
	case "qbot.qbot.admin.Admin":
		return true
	case "qbot.qbot.chat.Chat":
		return true
	case "qbot.qbot.chat.ChatHttp":
		return true
	case "qbot.qbot.op.Op":
		return true
	case "trpc.KEP.plugin-config-server.PluginConfig":
		return true
	case "trpc.KEP.plugin-config-server.PluginConfigApi":
		return true
	case "trpc.KEP.agent-config-server.AgentConfig":
		return true
	case "trpc.KEP.agent-config-server.AgentConfigApi":
		return true
	}
	return false
}

func ignorePermission(ctx context.Context) bool {
	callerServiceName := trpc.Message(ctx).CallerServiceName()
	switch callerServiceName {
	case "trpc.KEP.bot-admin-config-server.Admin":
		return true
	case "qbot.qbot.admin.Admin":
		return true
	case "qbot.qbot.chat.Chat":
		return true
	case "qbot.qbot.chat.ChatHttp":
		return true
	case "qbot.qbot.op.Op":
		return true
	case "trpc.KEP.plugin-config-server.PluginConfig":
		return true
	case "trpc.KEP.plugin-config-server.PluginConfigApi":
		return true
	case "trpc.KEP.agent-config-server.AgentConfig":
		return true
	case "trpc.KEP.agent-config-server.AgentConfigApi":
		return true
	}
	// 如果是空的action，则忽略权限检查
	return len(util.Action(ctx)) == 0
}

// ClientTrackFilter client 调用追踪 Filter
var ClientTrackFilter = func(ctx context.Context, req, rsp interface{}, next filter.ClientHandleFunc) (err error) {
	err = next(ctx, req, rsp)

	// 链路信息
	msg := trpc.Message(ctx)
	var callerIpPort string
	var calleeIpPort string

	if msg.LocalAddr() != nil {
		callerIpPort = msg.LocalAddr().String()
	}

	if msg.RemoteAddr() != nil {
		calleeIpPort = msg.RemoteAddr().String()
	}

	if len(callerIpPort) > 0 && len(calleeIpPort) > 0 {
		log.InfoContextf(ctx, "ClientTrackFilter|RequestID:%s|"+
			"CallerServiceName:%s|CallerMethod:%s|[CallerIpPort]:%s|"+
			"CalleeServiceName:%s|CalleeMethod:%s|[CalleeIpPort]:%s",
			util.RequestID(ctx),
			trpc.Message(ctx).CallerServiceName(), trpc.Message(ctx).CallerMethod(), callerIpPort,
			trpc.Message(ctx).CalleeServiceName(), trpc.Message(ctx).CalleeMethod(), calleeIpPort)
	}

	if err != nil {
		return err
	}
	return nil
}
