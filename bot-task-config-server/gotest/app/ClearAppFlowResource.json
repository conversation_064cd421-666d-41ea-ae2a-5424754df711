[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ClearAppFlowResource", "Func": "/ClearAppFlowResource", "ReqBody": "trpc.KEP.bot_task_config_server.ClearAppFlowResourceReq", "RspBody": "trpc.KEP.bot_task_config_server.ClearAppFlowResourceRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1856697211100594176", "TaskId": "1856697211100594176"}, "CheckList": [{"JsonPath": "BotBizId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "444wwwcxTZ4NjExMTEx1x"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]