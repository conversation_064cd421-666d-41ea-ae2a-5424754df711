[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetAppShareURL", "Func": "/GetAppShareURL", "ReqBody": "trpc.KEP.bot_task_config_server.GetAppShareURLReq", "RspBody": "trpc.KEP.bot_task_config_server.GetAppShareURLResp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1793260396881641472", "IsRegen": true}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "q111aaa22xMTZ4NjExMTEx1111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]