[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetAppShareURL", "Func": "/GetRobotIdByShareCode", "ReqBody": "trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeReq", "RspBody": "trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeResp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"ShareCode": "1bYkk95zsHe"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "*********************************"}]}]