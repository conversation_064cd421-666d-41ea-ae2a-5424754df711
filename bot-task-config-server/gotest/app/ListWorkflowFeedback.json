[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ListWorkflowFeedback", "Func": "/ListWorkflowFeedback", "ReqBody": "trpc.KEP.bot_task_config_server.ListFlowFeedbackReq", "RspBody": "trpc.KEP.bot_task_config_server.ListFlowFeedbackRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"Page": 1, "PageSize": 15, "Query": "", "Reasons": [], "Status": []}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "q111aaa22xMTZ4NjExMTEx1111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]