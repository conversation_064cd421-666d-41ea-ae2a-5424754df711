[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CreateCategory", "Func": "/CreateCategory", "ReqBody": "trpc.KEP.bot_task_config_server.CreateCategoryReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateCategoryRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1737375624057454592", "Name": "测试分类_001", "ParentBizId": "0"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x64"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]