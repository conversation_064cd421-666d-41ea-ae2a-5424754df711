[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DeleteCategory", "Func": "/DeleteCategory", "ReqBody": "trpc.KEP.bot_task_config_server.DeleteCategoryReq", "RspBody": "trpc.KEP.bot_task_config_server.DeleteCategoryRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1693794149194399744", "CateBizId": "960060083932030720"}, "CheckList": [{"JsonPath": "BotBizId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x6"}, {"TransInfoKey": "login_uin", "TransInfoValue": "10000"}, {"TransInfoKey": "login_sub_account_uin", "TransInfoValue": "119"}]}]