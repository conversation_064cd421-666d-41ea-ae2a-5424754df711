[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetDataSyncTask", "Func": "/GetDataSyncTask", "ReqBody": "trpc.KEP.bot_task_config_server.GetDataSyncTaskReq", "RspBody": "trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1737375624057454592", "TaskID": 392}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "4x41"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]