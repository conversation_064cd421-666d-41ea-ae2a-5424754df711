[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetUnreleasedCount", "Func": "/GetUnreleasedCount", "ReqBody": "trpc.KEP.bot_task_config_server.GetUnreleasedCountReq", "RspBody": "trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1737375624057454592"}, "CheckList": [], "Variables": null, "CaseContext": [], "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "4x42"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]