[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "SendDataSyncTaskEvent", "Func": "/SendDataSyncTaskEvent", "ReqBody": "trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq", "RspBody": "trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1727231073371148288", "BusinessName": "TEXT_ROBOT", "Event": "RELEASE", "TaskID": "1746816822768828416"}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "4x4-3"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]