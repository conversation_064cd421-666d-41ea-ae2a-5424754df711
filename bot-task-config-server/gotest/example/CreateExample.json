[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Func": "/Create<PERSON><PERSON><PERSON>", "ReqBody": "trpc.KEP.bot_task_config_server.CreateExampleReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateExampleRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "FlowId": "881d7742-7dcf-4700-846b-c39ab4955f8f", "Example": "11示例问法测试？"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "2221116x64111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]