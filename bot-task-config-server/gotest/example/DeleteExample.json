[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DeleteExample", "Func": "/DeleteExample", "ReqBody": "trpc.KEP.bot_task_config_server.DeleteExampleReq", "RspBody": "trpc.KEP.bot_task_config_server.DeleteExampleRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "ExampleId": "915b1553-a157-4d33-a68d-0bc9173915e5"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "1116x64111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]