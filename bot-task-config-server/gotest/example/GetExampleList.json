[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetExampleList", "Func": "/GetExampleList", "ReqBody": "trpc.KEP.bot_task_config_server.GetExampleListReq", "RspBody": "trpc.KEP.bot_task_config_server.GetExampleListRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "FlowId": "881d7742-7dcf-4700-846b-c39ab4955f8f", "SaveType": "", "BizVersion": "", "Keyword": ""}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "1116x64111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]