[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "Update<PERSON><PERSON><PERSON>", "Func": "/UpdateExample", "ReqBody": "trpc.KEP.bot_task_config_server.UpdateExampleReq", "RspBody": "trpc.KEP.bot_task_config_server.UpdateExampleRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "ExampleId": "8837b1c7-b31a-44b1-8f77-66ab75009bd9", "Example": "11示例问法333？"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "1116x64111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]