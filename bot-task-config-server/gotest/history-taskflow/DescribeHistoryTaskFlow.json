[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DescribeHistoryTaskFlow", "Func": "/DescribeHistoryTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"FlowId": "12afc8e8-0d4c-4f98-b230-4c0934116452", "BotBizId": "1768196111442378752", "BizVersion": "0.0.1-0-**********", "SaveType": "PUBLISHED"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": "DR"}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x61111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]