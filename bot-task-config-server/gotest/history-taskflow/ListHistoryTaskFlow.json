[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ListHistoryTaskFlow", "Func": "/ListHistoryTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.ListHistoryTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.ListHistoryTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"FlowId": "881d7742-7dcf-4700-846b-c39ab4955f8f", "BotBizId": "1779900692610154496"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "3123326x618888924432"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]