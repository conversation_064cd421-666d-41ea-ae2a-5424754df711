[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "RecoverHistoryTaskFlow", "Func": "/RecoverHistoryTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"FlowId": "194b4fc3-551a-44c9-8cd5-863fee5cfc44", "BotBizId": "1770057150144446464", "BizVersion": "0.0.1-0-**********"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "1142211x6x6111311"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]