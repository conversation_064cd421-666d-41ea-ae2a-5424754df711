#!/bin/bash

# trpc-cli 相关资料
# https://iwiki.woa.com/p/194215409

TRPC_CLI="$HOME/go/bin/trpc-cli"
#TRPC_CLI="$HOME/gopath/bin/trpc-cli" # xinghui

#PROTO_FILE="/Users/<USER>/gosrc0/kep/lke_proto/thirds-pb/bot-task-config-server/task-config.proto"
PROTO_FILE="/Users/<USER>/GolandProjects/dialogue-platform/lke_proto/thirds-pb/bot-task-config-server/task-config.proto" # xinghui

#TARGET="ip://************:8000"
#TARGET="ip://************:8000"
TARGET="ip://*************:8000" # 开发环境

echo "--------------------------------------------------------------"
echo "TRPC_CLI=$TRPC_CLI"
echo "PROTO_FILE=$PROTO_FILE"
echo "TARGET=$TARGET"
echo "--------------------------------------------------------------"


# $TRPC_CLI -protofile="$PROTO_FILE" -interfacelist

# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=GetUnreleasedCount -outputjson=GetUnreleasedCount.json
# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=SendDataSyncTaskEvent -outputjson=SendDataSyncTaskEvent.json

# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=GetWorkflowGuideViewed -outputjson=GetWorkflowGuideViewed.json
# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=MarkWorkflowGuideViewed -outputjson=MarkWorkflowGuideViewed.json


