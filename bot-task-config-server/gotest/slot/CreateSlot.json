[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CreateSlot", "Func": "/CreateSlot", "ReqBody": "trpc.KEP.bot_task_config_server.CreateSlotReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateSlotRsq", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1744211688139128832", "Name": "测试槽位_111", "Desc": "测试槽位描述", "Examples": ["测试"]}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x641111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]