[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DeleteEntry", "Func": "/DeleteEntry", "ReqBody": "trpc.KEP.bot_task_config_server.DeleteEntryReq", "RspBody": "trpc.KEP.bot_task_config_server.DeleteEntryRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "SlotId": "********-35b1-494a-aa81-c55e37ec82ab", "EntryId": "5a749068-6c61-4f42-8e6c-10904796eed5"}, "CheckList": [{"JsonPath": "BotBizId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "**************************"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]