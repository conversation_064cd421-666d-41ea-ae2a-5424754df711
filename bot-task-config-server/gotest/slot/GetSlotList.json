[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetSlotList", "Func": "/GetSlotList", "ReqBody": "trpc.KEP.bot_task_config_server.GetSlotListReq", "RspBody": "trpc.KEP.bot_task_config_server.GetSlotListRsq", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1747446004305481728", "EntityScope": "BOT", "FilterNoEntry": true, "IsReturnEntry": true}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "663226x641111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]