[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CreateTaskFlow", "Func": "/CreateTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.CreateTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1744211688139128832", "CateBizId": "0", "DialogJson": "", "Name": "机票预定测试", "Desc": "意图描述"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]