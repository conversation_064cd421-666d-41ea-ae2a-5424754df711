[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetTaskFlowDetail", "Func": "/GetTaskFlowDetail", "ReqBody": "trpc.KEP.bot_task_config_server.GetTaskFlowDetailReq", "RspBody": "trpc.KEP.bot_task_config_server.GetTaskFlowDetailRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1779900692610154496", "FlowId": "0e5e94c2-21e3-4000-aade-99f44ad8090d"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]