[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetTaskFlowReleaseStatus", "Func": "/GetTaskFlowReleaseStatus", "ReqBody": "trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusReq", "RspBody": "trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusResp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1744211688139128832", "EnvTag": 0}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x67d19e990-cc22-114ae111222"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]