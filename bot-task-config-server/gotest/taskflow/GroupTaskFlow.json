[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GroupTaskFlow", "Func": "/GroupTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.GroupTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.GroupTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"BotBizId": "1693794149194399744", "CateBizId": "959192035227953152", "FlowIds": ["7ad9o4yohy4g"]}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x6"}, {"TransInfoKey": "login_uin", "TransInfoValue": "10000"}, {"TransInfoKey": "login_sub_account_uin", "TransInfoValue": "119"}]}]