[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "InnerExportTaskFlow", "Func": "/InnerExportTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.InnerExportTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.InnerExportTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppId": "1815736855937679360", "FlowIds": ["145fa239-b7ff-41c4-8a39-b9ba8084e325"], "Platform": "op"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]