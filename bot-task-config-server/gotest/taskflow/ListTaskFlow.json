[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ListTaskFlow", "Func": "/ListTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.ListTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.ListTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"Query": "", "CateBizId": "0", "Page": 1, "PageSize": 100, "BotBizId": "1779900692610154496", "Status": ["UNPUBLISHED", "DRAFT", "PUBLISHED", "CHANGE_UNPUBLISHED"]}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x61888892444432"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]