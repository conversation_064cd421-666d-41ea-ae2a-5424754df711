[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ListTaskFlowPreview", "Func": "/ListTaskFlowPreview", "ReqBody": "trpc.KEP.bot_task_config_server.ListTaskFlowPreviewReq", "RspBody": "trpc.KEP.bot_task_config_server.ListTaskFlowPreviewRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"Actions": [2], "BotBizId": "1727231073371148288", "EndTime": 0, "Page": 0, "PageSize": 10, "Query": "德", "StartTime": 0}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "4x44"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]