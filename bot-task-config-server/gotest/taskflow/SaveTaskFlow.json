[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "SaveTaskFlow", "Func": "/SaveTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.SaveTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.SaveTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"FlowId": "d483ec61-4e87-4b83-8530-e997d877237e", "Name": "测试-收集实体-引用接口出参", "Desc": "这里是描述", "CateBizId": "1071540517512996608", "BotBizId": "1798545239144136704", "Flag": 1, "DialogJson": "{\"TaskFlowID\":\"d483ec61-4e87-4b83-8530-e997d877237e\",\"TaskFlowName\":\"测试-收集实体-引用接口出参\",\"TaskFlowDesc\":\"测试\",\"SessionMode\":0,\"Nodes\":[{\"NodeID\":\"start\",\"NodeType\":\"START\",\"NodeName\":\"开始\",\"NodeUI\":{\"X\":\"165\",\"Y\":\"60\"},\"Branches\":[{\"BranchID\":\"edge-0.32047067840128341720061199894\",\"BranchType\":\"DIRECT\",\"ConditionInfo\":{},\"NextNodeID\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\",\"PrevNodeID\":\"start\"}]},{\"NodeID\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\",\"NodeType\":\"API\",\"NodeName\":\"智能接口1\",\"NodeUI\":{\"X\":\"165\",\"Y\":\"220\"},\"Branches\":[{\"BranchID\":\"edge-0.74809484647615791720061207326\",\"BranchType\":\"CUSTOM\",\"ConditionInfo\":{\"Condition\":{\"SourceType\":\"SLOT\",\"Comparison\":\"NOT_IN\",\"InputValues\":[\"汉口\"],\"InInputValueAddition\":{\"ValueSource\":2,\"AllEntry\":false,\"EntryIDs\":[]},\"ComparisonApiRespInfo\":{\"ParamID\":\"019746e2-8f81-eadf-8317-0c65459c7638\",\"AllObjectArrayParams\":false,\"PartOfObjectArrayParamIDs\":[]},\"ComparisonValueSourceType\":4,\"SlotValueData\":{\"SlotID\":\"c95a2f7a-b7e1-42dc-9c31-ad48c09b8905\"}}},\"NextNodeID\":\"c6eae133-b8bf-2af7-c838-39d833f1847c\",\"PrevNodeID\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\"},{\"BranchID\":\"edge-0.81260198682908081720061429087\",\"BranchType\":\"CUSTOM\",\"ConditionInfo\":{\"Condition\":{\"SourceType\":\"API_RESP\",\"Comparison\":\"IN\",\"InputValues\":[\"武昌\"],\"InInputValueAddition\":{},\"ComparisonSlotInfo\":{\"SlotID\":\"c95a2f7a-b7e1-42dc-9c31-ad48c09b8905\",\"AllEntry\":false,\"EntryIDs\":[\"86310432-817a-488d-950b-ae4f2c5baaad\"]},\"ComparisonValueSourceType\":1,\"APIRespValueData\":{\"ParamID\":\"019746e2-8f81-eadf-8317-0c65459c7638\"}}},\"NextNodeID\":\"********-267f-fabc-0d6f-c24973f1b258\",\"PrevNodeID\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\"}],\"ApiNodeData\":{\"API\":{\"URL\":\"http://www.qq.com\",\"Method\":\"GET\"},\"Headers\":[{\"ParamID\":\"73cdc006-8680-073b-eb02-5ef520ad2adb\",\"ParamName\":\"cookies\",\"ParamType\":\"string\",\"SourceType\":\"FIXED\",\"IsOptional\":true,\"FixedValueData\":{\"Value\":\"11\"},\"SubHeader\":[]}],\"Request\":[{\"ParamID\":\"d02745cf-dbd7-1cc4-50b0-41cea90d3e25\",\"ParamName\":\"name\",\"ParamType\":\"string\",\"SourceType\":\"SLOT\",\"IsRequired\":true,\"SlotValueData\":{\"AskType\":\"LLM\",\"CustomAsk\":\"\",\"SlotID\":\"c95a2f7a-b7e1-42dc-9c31-ad48c09b8905\",\"FormatDesc\":\"\"},\"SubRequest\":[]}],\"Response\":[{\"ParamID\":\"019746e2-8f81-eadf-8317-0c65459c7638\",\"ParamName\":\"rsp1\",\"ParamTitle\":\"rspinfo\",\"ParamType\":\"string\",\"JSONPath\":\"data.rsp1\"},{\"ParamID\":\"6284a438-964b-2c2d-8ee0-525791f9c6f3\",\"ParamName\":\"rsp2\",\"ParamTitle\":\"rsp2info\",\"ParamType\":\"string\",\"JSONPath\":\"data.rsp2\"}],\"DoubleCheck\":false,\"LLMAskPreview\":[]}},{\"NodeID\":\"********-267f-fabc-0d6f-c24973f1b258\",\"NodeType\":\"ANSWER\",\"NodeName\":\"结束回复1\",\"NodeUI\":{\"X\":\"260\",\"Y\":\"380\"},\"Branches\":[],\"AnswerNodeData\":{\"AnswerType\":\"LLM\",\"InputAnswerData\":{\"Preview\":\"\"},\"LLMAnswerData\":{\"Preview\":[],\"EnableCustomPromptWord\":false,\"Prompt\":\"\"},\"DocAnswerData\":{\"Preview\":\"\",\"RefInfo\":[]}}},{\"NodeID\":\"c6eae133-b8bf-2af7-c838-39d833f1847c\",\"NodeType\":\"ANSWER\",\"NodeName\":\"结束回复2\",\"NodeUI\":{\"X\":\"70\",\"Y\":\"380\"},\"Branches\":[],\"AnswerNodeData\":{\"AnswerType\":\"LLM\",\"InputAnswerData\":{\"Preview\":\"\"},\"LLMAnswerData\":{\"Preview\":[],\"EnableCustomPromptWord\":false,\"Prompt\":\"\"},\"DocAnswerData\":{\"Preview\":\"\",\"RefInfo\":[]}}}],\"Edges\":[{\"EdgeID\":\"edge-0.32047067840128341720061199894\",\"Source\":\"start\",\"Target\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\",\"SourceAnchor\":0,\"TargetAnchor\":0},{\"EdgeID\":\"edge-0.74809484647615791720061207326\",\"Source\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\",\"Target\":\"c6eae133-b8bf-2af7-c838-39d833f1847c\",\"SourceAnchor\":1,\"TargetAnchor\":0,\"Label\":\"武汉 包含 汉口\"},{\"EdgeID\":\"edge-0.81260198682908081720061429087\",\"Source\":\"9b0d54b2-bce7-42af-6f88-6a5f2d445e44\",\"Target\":\"********-267f-fabc-0d6f-c24973f1b258\",\"SourceAnchor\":1,\"TargetAnchor\":0,\"Label\":\"rspinfo = 武昌\"}],\"Snapshot\":{\"SlotMap\":{\"c95a2f7a-b7e1-42dc-9c31-ad48c09b8905\":\"武汉\"}}}"}, "CheckList": [{"JsonPath": "FlowId", "OP": "NE", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "188991123xxxabssssqa"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]