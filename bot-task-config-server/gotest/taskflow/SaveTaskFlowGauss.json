[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "SaveTaskFlow", "Func": "/SaveTaskFlow", "ReqBody": "trpc.KEP.bot_task_config_server.SaveTaskFlowReq", "RspBody": "trpc.KEP.bot_task_config_server.SaveTaskFlowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"FlowId": "12b0460a-3c21-46fd-bb23-938961973f25", "Name": "gauss测试", "CateBizId": "0", "BotBizId": "1734932415645614080", "DialogJson": "{\"TaskFlowID\":\"12b0460a-3c21-46fd-bb23-938961973f25\",\"TaskFlowName\":\"gauss测试\",\"Nodes\":[{\"NodeID\":\"start\",\"NodeName\":\"开始节点\",\"NodeType\":\"startNode\",\"NodeData\":{},\"NodeUI\":{\"X\":100,\"Y\":100,\"style\":\"\",\"Anchors\":[]},\"Branches\":[{\"BranchID\":\"1\",\"BranchType\":\"direct\",\"Conditions\":[{\"ConditionID\":\"1\",\"ConditionSource\":\"RequiredInfo\",\"NodeID\":\"start\",\"NodeName\":\"开始节点\",\"ParamID\":\"1\",\"ParamName\":\"城市\",\"ParamVarName\":\"city\",\"VarType\":\"string\",\"Operator\":\"contains\",\"Value\":\"北京\"}],\"ConditionsLogic\":\"And\",\"NextNodeID\":\"request\",\"PrevNodeID\":\"start\"}]},{\"NodeID\":\"request\",\"NodeName\":\"询问节点\",\"NodeType\":\"RequestNode\",\"NodeData\":{\"RequiredInfo\":{\"SlotID\":\"1\",\"SlotName\":\"城市\",\"SlotVarName\":\"city\",\"ParamType\":\"string\",\"ProbeConfig\":\"\",\"IsRequired\":true,\"ProbePreview\":[],\"ProbeCustom\":\"\"},\"OptionCards\":true,\"OptionType\":\"custom\",\"OptionContents\":[\"北京\",\"上海\",\"广州\",\"深圳\"],\"APIParams\":[]},\"NodeUI\":{\"X\":300,\"Y\":100,\"style\":\"\",\"Anchors\":[]},\"Branches\":[{\"BranchID\":\"2\",\"BranchType\":\"direct\",\"Conditions\":[{\"ConditionID\":\"1\",\"ConditionSource\":\"RequiredInfo\",\"NodeID\":\"request\",\"NodeName\":\"询问节点\",\"ParamID\":\"1\",\"ParamName\":\"城市\",\"ParamVarName\":\"city\",\"VarType\":\"string\",\"Operator\":\"contains\",\"Value\":\"北京\"}],\"ConditionsLogic\":\"Or\",\"NextNodeID\":\"api\",\"PrevNodeID\":\"request\"},{\"BranchID\":\"3\",\"BranchType\":\"direct\",\"Conditions\":[{\"ConditionID\":\"2\",\"ConditionSource\":\"RequiredInfo\",\"NodeID\":\"request\",\"NodeName\":\"询问节点\",\"ParamID\":\"1\",\"ParamName\":\"城市\",\"ParamVarName\":\"city\",\"VarType\":\"string\",\"Operator\":\"not contains\",\"Value\":\"上海\"}],\"ConditionsLogic\":\"Or\",\"NextNodeID\":\"answer\",\"PrevNodeID\":\"request\"}]},{\"NodeID\":\"api\",\"NodeName\":\"API节点\",\"NodeType\":\"APINode\",\"NodeData\":{\"RequiredInfo\":[{\"SlotID\":\"2\",\"SlotName\":\"日期\",\"SlotVarName\":\"date\",\"ParamType\":\"string\",\"ProbeConfig\":\"\",\"IsRequired\":true,\"ProbePreview\":[],\"ProbeCustom\":\"\"}],\"APIPath\":{\"Path\":\"/weather\",\"Method\":\"GET\"},\"APIResponseParams\":[{\"ParamName\":\"天气\",\"ParamVarName\":\"weather\",\"ParamType\":\"string\",\"ParamPath\":\"data.weather\",\"IsRequired\":true}],\"InvokeConfirm\":false},\"NodeUI\":{\"X\":500,\"Y\":100,\"style\":\"\",\"Anchors\":[]},\"Branches\":[{\"BranchID\":\"6\",\"BranchType\":\"direct\",\"Conditions\":[{\"ConditionID\":\"5\",\"ConditionSource\":\"APIResponseParam\",\"NodeID\":\"api\",\"NodeName\":\"API节点\",\"ParamID\":\"1\",\"ParamName\":\"天气\",\"ParamVarName\":\"weather\",\"VarType\":\"string\",\"Operator\":\"==\",\"Value\":\"晴\"}],\"ConditionsLogic\":\"And\",\"NextNodeID\":\"answer\",\"PrevNodeID\":\"api\"}]},{\"NodeID\":\"answer\",\"NodeName\":\"答案节点\",\"NodeType\":\"AnswerNode\",\"NodeData\":{\"AnswerSource\":\"auto\",\"AnswerPreview\":[\"今天北京天气晴\",\"今天上海天气多云\",\"今天广州天气阴\",\"今天深圳天气雨\"],\"AnswerCustom\":\"\"},\"NodeUI\":{\"X\":700,\"Y\":100,\"style\":\"\",\"Anchors\":[]},\"Branches\":[]}],\"Edges\":[{\"EdgeID\":\"1\",\"EdgeType\":\"\",\"Source\":\"start\",\"Target\":\"request\",\"SourceAnchor\":0,\"TargetAnchor\":0,\"style\":\"\",\"StartPoint\":{\"X\":100,\"Y\":100,\"Index\":0},\"EndPoint\":{\"X\":300,\"Y\":100,\"Index\":0}},{\"EdgeID\":\"2\",\"EdgeType\":\"\",\"Source\":\"request\",\"Target\":\"api\",\"SourceAnchor\":0,\"TargetAnchor\":0,\"style\":\"\",\"StartPoint\":{\"X\":300,\"Y\":100,\"Index\":0},\"EndPoint\":{\"X\":500,\"Y\":100,\"Index\":0}},{\"EdgeID\":\"3\",\"EdgeType\":\"\",\"Source\":\"api\",\"Target\":\"answer\",\"SourceAnchor\":0,\"TargetAnchor\":0,\"style\":\"\",\"StartPoint\":{\"X\":500,\"Y\":100,\"Index\":0},\"EndPoint\":{\"X\":700,\"Y\":100,\"Index\":0}}]}", "Flag": 1}, "CheckList": [{"JsonPath": "FlowId", "OP": "NE", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "gaussguan_2024_0111_12"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]