[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CreateVar", "Func": "/CreateVar", "ReqBody": "trpc.KEP.bot_task_config_server.CreateVarReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateVarRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1793260396881641472", "VarName": "DateTime11"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x64111111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]