[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "UpdateVar", "Func": "/UpdateVar", "ReqBody": "trpc.KEP.bot_task_config_server.UpdateVarReq", "RspBody": "trpc.KEP.bot_task_config_server.UpdateVarRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1793260396881641472", "VarId": "ad3ff694-af6e-47d8-b727-0d2907629079", "VarName": "OrderIdUpdate"}, "CheckList": [{}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "6x641111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]