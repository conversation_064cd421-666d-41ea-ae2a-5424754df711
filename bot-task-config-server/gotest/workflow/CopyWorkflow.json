[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CopyWorkflow", "Func": "/CopyWorkflow", "ReqBody": "trpc.KEP.bot_task_config_server.CopyWorkflowReq", "RspBody": "trpc.KEP.bot_task_config_server.CopyWorkflowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1846899622633275392", "FlowId": "84ab529c-c450-4346-8299-7dadf46cc40a"}, "CheckList": [{"JsonPath": "FlowId", "OP": "NE", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "copy_workflow_2024_1"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]