[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "CreateWorkflow", "Func": "/CreateWorkflow", "ReqBody": "trpc.KEP.bot_task_config_server.CreateWorkflowReq", "RspBody": "trpc.KEP.bot_task_config_server.CreateWorkflowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"Name": "xh-工作流1", "AppBizId": "1798610639288008704", "DialogJson": "", "Desc": "描述"}, "CheckList": [{"JsonPath": "AppBizId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_************"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]