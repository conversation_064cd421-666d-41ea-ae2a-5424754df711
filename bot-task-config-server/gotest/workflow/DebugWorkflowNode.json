[{"CaseName": "debug-api-node", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DebugWorkflowNode", "Func": "/DebugWorkflowNode", "ReqBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeReq", "RspBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1846899622633275392", "FlowId": "92b0134f-3519-485e-8a18-3f760256ccae", "NodeId": "2"}, "CheckList": [{"JsonPath": "TaskResult", "OP": "NE", "TARGET": ""}, {"JsonPath": "Result", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "debug_workflow_api_2024_1"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}, {"CaseName": "debug-code-not-output", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DebugWorkflowNode", "Func": "/DebugWorkflowNode", "ReqBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeReq", "RspBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1846899622633275392", "FlowId": "92b0134f-3519-485e-8a18-3f760256ccae", "NodeId": "3"}, "CheckList": [{"JsonPath": "Result", "OP": "EQ", "TARGET": ""}, {"JsonPath": "TaskResult", "OP": "EQ", "TARGET": "{\"B\": \"\\u6df1\\u5733\\u5929\\u6c14\"}"}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "debug_workflow_code_2024_1"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}, {"CaseName": "debug-code-has-output", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "DebugWorkflowNode", "Func": "/DebugWorkflowNode", "ReqBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeReq", "RspBody": "trpc.KEP.bot_task_config_server.DebugWorkflowNodeRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1846899622633275392", "FlowId": "92b0134f-3519-485e-8a18-3f760256ccae", "NodeId": "4", "Param": "{\"A\":\"A\"}"}, "CheckList": [{"JsonPath": "TaskResult", "OP": "EQ", "TARGET": "{\"A\": \"A\", \"B\": \"test\", \"C\": [2.1, 3.2]}"}, {"JsonPath": "Result", "OP": "EQ", "TARGET": "{\"A\":\"A\",\"B\":\"定义类型为INT,但返回值为test\",\"C\":\"定义类型为ARRAY_STRING,但返回值为[2.1 3.2]\",\"D\":\"参数未返回\"}"}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "debug_workflow_code_2024_1"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]