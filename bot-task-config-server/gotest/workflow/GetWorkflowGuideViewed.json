[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetWorkflowGuideViewed", "Func": "/GetWorkflowGuideViewed", "ReqBody": "trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedRequest", "RspBody": "trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedResponse", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {}, "CheckList": [{"JsonPath": "ViewedKeys", "OP": "EQ", "TARGET": []}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_************-6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]