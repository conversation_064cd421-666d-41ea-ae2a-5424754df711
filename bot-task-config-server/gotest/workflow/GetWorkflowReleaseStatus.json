[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "GetWorkflowReleaseStatus", "Func": "/GetWorkflowReleaseStatus", "ReqBody": "trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusReq", "RspBody": "trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusResp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1861797402826244096", "EnvTag": 0, "WorkflowId": "0a7cb13d-f848-439d-bbc2-441f804f786f"}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_600000561037-6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]