[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ListWorkflowInfoByModelNameInner", "Func": "/ListWorkflowInfoByModelNameInner", "ReqBody": "trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameReq", "RspBody": "trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"CorpId": "157", "ModelName": "************-OpenAI-NflJJjBE"}, "CheckList": [{"JsonPath": "FlowId", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "export_workflow_2024_1"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]