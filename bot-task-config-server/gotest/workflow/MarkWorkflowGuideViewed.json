[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "MarkWorkflowGuideViewed", "Func": "/MarkWorkflowGuideViewed", "ReqBody": "trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedRequest", "RspBody": "trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedResponse", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"Key": "tip-3"}, "CheckList": null, "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_************-6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]