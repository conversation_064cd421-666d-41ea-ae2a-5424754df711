[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "SaveWorkflow", "Func": "/SaveWorkflow", "ReqBody": "trpc.KEP.bot_task_config_server.SaveWorkflowReq", "RspBody": "trpc.KEP.bot_task_config_server.SaveWorkflowRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1798610639288008704", "Desc": "", "DialogJson": "{\"ProtoVersion\":\"V2_6\", \"WorkflowID\":\"f6259160-1cc7-4208-9314-ac886b674520\", \"WorkflowName\":\"xh-工作流2-保存\", \"WorkflowDesc\":\"\", \"Nodes\":[{\"NodeID\":\"n111\", \"NodeName\":\"n1-name\", \"NodeDesc\":\"\", \"NodeType\":\"START\", \"Inputs\":[], \"Outputs\":[], \"NextNodeIDs\":[\"n2\"], \"NodeUI\":\"\"}, {\"NodeID\":\"n2\", \"NodeName\":\"n2-name\", \"NodeDesc\":\"\", \"NodeType\":\"LLM\", \"LLMNodeData\":{\"ModelName\":\"11\", \"Temperature\":0, \"TopP\":0, \"MaxTokens\":0, \"Prompt\":\"sss\"}, \"Inputs\":[], \"Outputs\":[], \"NextNodeIDs\":[\"n3\"], \"NodeUI\":\"\"}, {\"NodeID\":\"n3\", \"NodeName\":\"n3-name\", \"NodeDesc\":\"\", \"NodeType\":\"ANSWER\", \"AnswerNodeData\":{\"Answer\":\"a回复\"}, \"Inputs\":[], \"Outputs\":[], \"NextNodeIDs\":[], \"NodeUI\":\"\"}], \"Edge\":\"\"}", "Name": "xh-工作流2-保存", "SaveType": 1, "WorkflowId": "f6259160-1cc7-4208-9314-ac886b674520", "WorkflowVersion": 15}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_************-6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]