#!/bin/bash

#
# 2024-10-24
# Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
#
#

# 设置错误时退出
set -e

# 检查main.go是否存在
if [ ! -f "gen-workflow/main.go" ]; then
    echo "Error: main.go not found in current directory"
    exit 1
fi

# 运行Go程序
echo "Running main.go..."
go run gen-workflow/main.go

# 检查运行状态
if [ $? -eq 0 ]; then
    echo "Program executed successfully"
else
    echo "Program execution failed"
    exit 1
fi



# 检查 jq 是否已安装
if ! command -v jq &> /dev/null; then
    echo "Error: jq is not installed."
    echo "Please install jq by running the following command:"
    echo "brew install jq"  # macOS 下使用 Homebrew 安装 jq
    exit 1
fi

# 定义文件路径和名称
json_file="SaveWorkflow.json"  # 确保这里路径正确

# 定义从 workflow1.out 读取的新值
workflow_file="gen-workflow/workflow1.out"
if [[ ! -f "$workflow_file" ]]; then
  echo "Error: Workflow file $workflow_file does not exist."
  exit 1
fi

# 从 workflow1.out 文件中读取内容
new_dialog_json_value=$(<"$workflow_file")

# 检查文件是否存在
if [[ ! -f "$json_file" ]]; then
  echo "Error: JSON file $json_file does not exist."
  exit 1
fi

echo "Original DialogJson value:"
jq '.[0].RequestJson.DialogJson' "$json_file"  # 输出原始值

# 使用 jq 修改 DialogJson 的值
jq --arg newValue "$new_dialog_json_value" '.[0].RequestJson.DialogJson = $newValue' "$json_file" > temp.json

# 检查是否成功替换
echo "Updated JSON content:"
cat temp.json  # 输出替换后的文件内容

# 将临时文件覆盖原始文件
mv temp.json "$json_file"

echo "DialogJson 值已更新为: $new_dialog_json_value"


cd `dirname "${BASH_SOURCE[0]}"`

# 引用公共函数和变量
source ../server-env.sh

$TRPC_CLI -datafiles=SaveWorkflow.json -target="$TARGET" -out=SaveWorkflow.out

