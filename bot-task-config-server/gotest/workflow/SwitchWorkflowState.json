[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "SwitchWorkflowState", "Func": "/SwitchWorkflowState", "ReqBody": "trpc.KEP.bot_task_config_server.SwitchWorkflowStateReq", "RspBody": "trpc.KEP.bot_task_config_server.SwitchWorkflowStateRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1846899622633275392", "IsEnable": true, "WorkflowIds": ["7753fb4e-9146-4f0a-bb17-3c3c6027063d"]}, "CheckList": [], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "xh-request_id_************-6"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]