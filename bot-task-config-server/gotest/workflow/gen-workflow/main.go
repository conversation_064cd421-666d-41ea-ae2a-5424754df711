/*
 * 2024-10-24
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package main

import (
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"google.golang.org/protobuf/encoding/protojson"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
)

const (
	//location = "gotest/workflow/gen-workflow/"
	location = "gen-workflow/"
)

// IsValid checks if the input string starts with a letter or underscore and only contains letters, numbers, and underscores
func IsValid(input string) bool {
	// Regular expression to validate the string
	pattern := `^[a-zA-Z_][a-zA-Z0-9_]*$`
	matched, _ := regexp.MatchString(pattern, input)
	return matched
}
func ExtractParams(text string) []string {
	// 正则表达式匹配 {{param}} 的内容
	re := regexp.MustCompile(`{{(.*?)}}`)
	matches := re.FindAllStringSubmatch(text, -1)

	// 创建切片存储参数名称
	var params []string
	for _, match := range matches {
		// match[1] 是括号内捕获的内容，即参数名称
		params = append(params, match[1])
	}

	return params
}

func testExtractParams() {
	tests := []struct {
		input    string
		expected []string
	}{
		// 基础案例，多个占位符
		{"Hello, {{name}}! Today is {{date}}.", []string{"name", "date"}},

		// 重复占位符
		{"Hello, {{name}}! How are you, {{name}}?", []string{"name", "name"}},

		// 单个占位符
		{"Only one {{param}} here.", []string{"param"}},

		// 无占位符
		{"No placeholders here.", []string{}},

		// 多个相连的占位符
		{"{{first}} and {{second}} are connected.", []string{"first", "second"}},

		// 空占位符内容
		{"Empty braces {{}} should be ignored.", []string{""}},

		// 边界情况，占位符位于文本开头和结尾
		{"{{start}} in the beginning and {{end}} at the end.", []string{"start", "end"}},

		// 占位符包含特殊字符
		{"{{special_chars!@#}} and {{another_one}}.", []string{"special_chars!@#", "another_one"}},

		// 连续的多个占位符，未加空格
		{"{{param1}}{{param2}}{{param3}}", []string{"param1", "param2", "param3"}},

		// 占位符包含空格
		{"{{with space}} and {{another with space}}", []string{"with space", "another with space"}},

		// 含有嵌套的占位符 (这应该返回原始占位符)
		{"Nested {{not {{nested}} correctly}} example.", []string{"not {{nested"}},
		//
		//// 边界情况，完全由占位符组成的文本
		{"{{only_param}}", []string{"only_param"}},
	}

	// 测试结果
	for _, test := range tests {
		result := ExtractParams(test.input)
		if reflect.DeepEqual(result, test.expected) {
			fmt.Printf("PASS: Input: %q => Output: %v\n", test.input, result)
		} else {
			fmt.Printf("FAIL: Input: %q => Expected: %v, Got: %v\n", test.input, test.expected, result)
		}
	}
}

func main() {

	testExtractParams()

	text := "Hello, {{name}}! Today is {{date}}. " +
		"How can I assist you with {{topic}}? " +
		"{{{三个}}},  {{sss}}}"
	params := ExtractParams(text)
	fmt.Println(params) // 输出: [name date topic]

	fmt.Println(IsValid("valid_name123"))   // true
	fmt.Println(IsValid("1_invalid_start")) // false
	fmt.Println(IsValid("_valid_start"))    // true
	fmt.Println(IsValid("invalid-char!"))   // false

	s := "{\"InputType\":\"CUSTOM_VARIABLE\",\"CustomVarID\":\"7a316f62-2c81-4a75-ba84-c6a1b494b0ef\"}"
	input := &KEP_WF.Input{}
	//value := &DataExtra{}
	if err1 := protojson.Unmarshal([]byte(s), input); err1 != nil {

		return
	}

	id := "f6259160-1cc7-4208-9314-ac886b674520"
	name := "xh-工作流2-保存"
	wf := buildWorkflow1(id, name)
	//wf := buildWorkflow0(id, name)

	str, err := protoutil.WorkflowToJson(wf)
	if err != nil {
		fmt.Println(err)
		return
	}
	err = WriteFile("workflow1.out", []byte(str))
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(str)

	str = "{ \"WorkflowID\":\"f6259160-1cc7-4208-9314-ac886b674520\", \"WorkflowName\":\"xh-工作流1-保存\", \"WorkflowDesc\":\"\", \"Nodes\":[{\"NodeID\":\"n1\", \"NodeName\":\"n1-name\", \"NodeType\":1, \"NextNodeIDs\":[\"n2\"], \"NodeUI\":\"\"}, {\"NodeID\":\"n2\", \"NodeName\":\"n2-name\", \"NodeType\":\"LLM\", \"LLMNodeData\":{\"ModelName\":\"11\", \"Temperature\":0, \"TopP\":0, \"MaxTokens\":0, \"Prompt\":\"sss\", \"DialogHistoryLimit\":0}, \"NextNodeIDs\":[\"n3\"], \"NodeUI\":\"\"}, {\"NodeID\":\"n3\", \"NodeName\":\"n3-name\", \"NodeType\":\"ANSWER\", \"AnswerNodeData\":{\"Answer\":\"a回复\", \"DialogHistoryLimit\":0}, \"NextNodeIDs\":[], \"NodeUI\":\"\"}], \"Edge\":\"\"}"

	nwf, err := protoutil.JsonToWorkflow(str)
	//nwf, err := protoutil.JsonToWorkflowForPreCheck(str)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, node := range nwf.GetNodes() {
		fmt.Println(node.GetNodeType())
	}
}

//func buildWorkflow0(id, name string) *KEP_WF.Workflow {
//	return &KEP_WF.Workflow{
//		WorkflowID: "wf_123456789",
//		//NodeUI:     []byte(`{"nodes":[{"id":"node_1"}]}`),
//		Edge: `{"edges":[{"id":"edge_1"}]}`,
//	}
//}

func buildWorkflow1(id, name string) *KEP_WF.Workflow {
	//	jsonStr := `
	//{
	//  "Reference":{
	//    "NodeID":"123",
	//    "ReferenceInfo":"output"
	//  },
	//  "SystemVariable":{
	//    "Name":"sys.chat_history",
	//    "DialogHistoryLimit":10
	//  },
	//  "CustomVarID":"123"
	//}
	//`
	//	escaped := url.QueryEscape(jsonStr)

	if len(id) == 0 {
		id = "f6259160-1cc7-4208-9314-ac886b674520"
	}

	if len(name) == 0 {
		name = "xh-工作流1-保存"
	}

	return &KEP_WF.Workflow{
		ProtoVersion: KEP_WF.WorkflowProtoVersion_V2_6,
		WorkflowID:   id,
		WorkflowName: name,
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID:      "n111",
				NodeName:    "n1-name",
				NodeType:    KEP_WF.NodeType_START,
				NextNodeIDs: []string{"n2"},
			},
			{
				NodeID:      "n2",
				NodeName:    "n2-name",
				NodeType:    KEP_WF.NodeType_LLM,
				NextNodeIDs: []string{"n3"},
				NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
					LLMNodeData: &KEP_WF.LLMNodeData{
						ModelName: "11",
						Prompt:    "sss",
					},
				},
			},
			{
				NodeID:      "n3",
				NodeName:    "n3-name",
				NodeType:    KEP_WF.NodeType_ANSWER,
				NextNodeIDs: []string{},
				NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{
					AnswerNodeData: &KEP_WF.AnswerNodeData{
						Answer: "a回复",
					},
				},
			},
		},
	}
}

// WriteFile 写入文件内容，如果文件不存在则创建
// path: 相对于main.go的文件路径
// content: 要写入的内容
func WriteFile(path string, content []byte) error {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前目录失败: %v", err)
	}

	// 组合完整的文件路径
	fullPath := filepath.Join(currentDir, location+path)

	// 写入文件（如果不存在会创建，存在则覆盖）
	err = os.WriteFile(fullPath, content, 0666)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}
