// Package app  bot-task-config-server
// @(#)app.go  星期三, 二月 28, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.
package app

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// GetAppTokenBalance 获取应用的余量状态
func GetAppTokenBalance(ctx context.Context, modelName string,
	appInfo *pb.GetAppInfoRsp) float64 {
	var accountCnt int
	// 账户余量，默认1
	accountCnt = 1
	// 获取体验包余量
	//orderExpCnt, err := rpc.GetAccountBalance(ctx, appInfo)
	//if err != nil {
	//	log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode accountBalance failed:%+v", err)
	//}
	//if orderExpCnt <= 0 {
	//	// 检查云账户
	//	accountCnt = rpc.GetModelToken(ctx, modelName)
	//} else {
	//	accountCnt = int(orderExpCnt)
	//}

	// 获取 应用的状态
	status := rpc.GetDescribeAccountStatus(ctx, appInfo)
	// 状态不可用 status： 0可用, 1不可用
	if status == 1 {
		accountCnt = 0
	}

	return float64(accountCnt)
}

// GetAppChatInputNum 获取对话框输入字符数
func GetAppChatInputNum(ctx context.Context, req *KEP.GetAppChatInputNumReq) (*KEP.GetAppChatInputNumRsp, error) {
	var sid = util.RequestID(ctx)
	var err error
	var rsp = &KEP.GetAppChatInputNumRsp{}
	botBizId := req.GetAppBizId()
	mn := strings.TrimSpace(req.GetModelName())
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return rsp, err
	}
	// 非内部服务进行校验
	if !util.IsInnerService(ctx) {
		appInfo, err := permission.CheckRobot(ctx, 1, botId)
		if mn == "" {
			mn = entity.GetAppModelName(appInfo)
		}
		clues.AddTrackE(ctx, "permission.CheckRobot", botBizId, err)
		if err != nil {
			log.Warnf("P|GetAppChatInputNum|CheckRobot|%s|%v", sid, err)
		}
	}

	//limitNum, err := db.GetAppChatInputNum(ctx, mn)
	limitNum, err := rpc.GetAppChatInputNum(ctx, botBizId, mn)
	if err != nil {
		log.ErrorContextf(ctx, "rpc.GetAppChatInputMun|err:%+v", err)
		return rsp, errors.OpDataFromDBError("获取模型对应输入框字数限制失败")
	}
	rsp.InputLenLimit = limitNum
	return rsp, err

}

// GetRobotIdByShareCode 通过分享码获取机器人ID
func GetRobotIdByShareCode(ctx context.Context,
	req *KEP.GetRobotIdByShareCodeReq) (*KEP.GetRobotIdByShareCodeResp, error) {
	var sid = util.RequestID(ctx)
	rsp := KEP.GetRobotIdByShareCodeResp{}
	shareCode := req.GetShareCode()
	botId, err := db.GetRobotIdByShareCodeFromDB(ctx, shareCode)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|G|GetRobotIdByShareCode "+
			"GetRobotIdByShareCodeFromDB failed: err:%s", sid, err.Error())
		return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
	}
	// 通过botId去redis查找存储的分享信息
	prefixKey := config.GetMainConfig().AppShare.RedisPrefixKey
	redisKey := prefixKey + ":" + botId
	// 1. 先查询是否存在可用(未过期)的shareCode
	codeInfoFromRedis, err := db.GetAppShareCodeFromRedis(ctx, redisKey)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|G|GetAppShareURL,get "+
			"appShareCode failed: err:%+v", sid, err)
		return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
	}
	if codeInfoFromRedis.BotBizId != botId {
		return &rsp, errors.ErrShareUrlRobotNotEqual
	}
	rsp.BotBizId = botId
	return &rsp, nil
}

func delAppShareInfoFromDB(ctx context.Context, redisKey, botBizId string) error {
	var err error
	sid := util.RequestID(ctx)
	//	1. 删除Redis缓存
	if err = db.DeleteAppShareCodeFromRedis(ctx, redisKey); err != nil {
		log.ErrorContextf(ctx, "sid:%s|D|Regen|redis,del appShareCode failed: err:%+v", sid, err)
		return err
	}
	clues.AddTrackE(ctx, "DeleteAppShareCodeFromRedis", redisKey, err)
	// 2. 删除DB里面的shareCode
	if err = db.DeleteAppShareCodeFromDB(ctx, botBizId); err != nil {
		log.ErrorContextf(ctx, "sid:%s|D|Regen|db del appShareCode failed: err:%+v", sid, err)
		return err
	}
	return nil
}
func getAppShareInfoExpired(ctx context.Context) (uint32, time.Time) {
	expired := config.GetMainConfig().AppShare.ExpiredTime
	expiredTime := time.Now()
	if expired > 0 {
		expiredTime = expiredTime.Add(time.Duration(expired) * time.Second)
	} else {
		expiredTime = time.Date(9999, time.December, 31,
			23, 59, 59, 0, time.Local)
	}
	log.InfoContextf(ctx, "sid:%s|expired:%+v|expiredTime:%+v",
		util.RequestID(ctx), expired, expiredTime)
	return expired, expiredTime
}

// GetAppShareURL 获取应用的分享地址
func GetAppShareURL(ctx context.Context,
	req *KEP.GetAppShareURLReq) (*KEP.GetAppShareURLResp, error) {
	var sid = util.RequestID(ctx)
	var botBizId = req.GetAppBizId()
	var appShareInfo db.CodeInfo
	var appShareCode string
	clues.AddTrackData(ctx, "GetAppShareURL", req)
	rsp := KEP.GetAppShareURLResp{}
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return &rsp, err
	}

	// 添加判断机器人是否存在.只涉及线上环境  scene固定为2
	appInfo, err := permission.CheckRobot(ctx, 2, botId)
	clues.AddTrackE(ctx, "permission.CheckRobot", botBizId, err)
	if errors.Is(err, errors.ErrRobotNotFound) || (appInfo != nil && appInfo.Status != 2) {
		log.Warnf("P|GetAppShareURL|sid:%s|appInfo:%+v|err:%+v", sid, appInfo, err)
		return &rsp, nil
	}
	if err != nil {
		log.Errorf("P|GetAppShareURL|%s|%v", sid, err)
		return nil, errors.OpDataFromDBError("获取分享码信息失败")
	}

	redisKey := config.GetMainConfig().AppShare.RedisPrefixKey + ":" + botBizId
	whiteAppIds := config.GetMainConfig().AppShare.WhiteAppId
	expired, expiredTime := getAppShareInfoExpired(ctx)
	// 重新生成且应用Id不在白名单内
	if req.GetIsRegen() && !util.StrInArray(botBizId, whiteAppIds) { //重新生成, 则删掉对应ShareCode
		if err = delAppShareInfoFromDB(ctx, redisKey, botBizId); err != nil {
			log.ErrorContextf(ctx, "sid:%s|D|Regen|del appShareCode|err:%+v", sid, err)
			return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
		}
	}
	codeInfoFromRedis, err := db.GetAppShareCodeFromRedis(ctx, redisKey) // 先查询是否存在可用的shareCode
	clues.AddTrackE(ctx, "GetAppShareCodeFromRedis", codeInfoFromRedis, err)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|G|GetAppShareURL,get code err|%+v", sid, err)
		return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
	}
	if codeInfoFromRedis.Code == "" {
		appShareInfo, err = db.GetShareCodeInDB(ctx, botBizId, expired)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|get appInfo from BD|err:%+v", sid, err)
			return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
		}
		if err = db.SaveShareInfoToRD(ctx, redisKey, appShareInfo, expired); err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|save appShareInfo in redis|err:%+v", sid, err)
			return &rsp, errors.OpDataFromDBError("获取分享码信息失败")
		}
	} else {
		appShareInfo = codeInfoFromRedis
	}

	if appShareInfo.Code == "" { //如果不存在，则生成并保存
		if appShareCode, err = getShareCode(ctx, botBizId); err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|getShareCode|err:%+v", sid, err)
			return &rsp, nil
		}
		_, subUin := util.GetUinAndSubAccountUin(ctx)
		appShareInfo.BotBizId = botBizId
		appShareInfo.Code = appShareCode
		appShareInfo.Expired = expired
		appShareInfo.CreateUser = subUin
		clues.AddTrackE(ctx, "getShareCode", appShareCode, err)
		if err = db.SaveAppShareCodeToDB(ctx, botBizId, appShareCode, expiredTime); err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|save code to db|err:%+v", sid, err)
			return &rsp, nil
		}
		if err = db.SaveShareInfoToRD(ctx, redisKey, appShareInfo, expired); err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|Save code to redis|err:%+v", sid, err)
			return &rsp, nil
		}
	}
	// 3. 拼接分享地址
	if appShareInfo.Code != "" {
		rsp.ShareUrl = config.GetMainConfig().AppShare.BaseUrl + "/" + appShareInfo.Code
		rsp.ImmediatelyExpUrl = config.GetMainConfig().AppShare.ImmediatelyBaseUrl + "/" + appShareInfo.Code
	}
	rsp.Balance = GetAppTokenBalance(ctx, entity.GetAppModelName(appInfo), appInfo)
	return &rsp, nil
}

// getShareCode 分享码
func getShareCode(ctx context.Context, botBizId string) (string, error) {
	// 生成六位随机字符串
	var code string
	var sid = util.RequestID(ctx)
	codeLen := config.GetMainConfig().AppShare.ShareCodeLen

	key := "KEP_bot-task-config-server:getShareCode-" + botBizId
	locker := lock.NewDefaultLocker(key, botBizId, database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, true)
	if lockErr != nil {
		log.ErrorContextf(ctx, "getShareCode locker.Lock Failed, err:%v", lockErr)
		return "", errors.ErrSystem
	}
	if !ok {
		log.WarnContextf(ctx, "getShareCode locker.Lock Failed, err:%v", lockErr)
		return "", errors.ErrSystem
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "getShareCode locker.UnLock Fialed, err:%v", unLockErr)
		}
		log.InfoContextf(ctx, "sid:%s|unlock success", sid)
	}()

	for {
		code = util.GenerateRandCode(codeLen)
		lowerCode := strings.ToLower(code)
		isGen, err := db.CheckShareCodeInDB(ctx, lowerCode)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|G|GetShareCode,"+
				"failed: err:%s", sid, err.Error())
		}
		if !isGen { // 检查数据库中是否存在该随机字符串
			break // 如果不存在，跳出循环
		}
	}
	return code, nil
}
