// bot-task-config-server
//
// @(#)resource.go  星期二, 十一月 05, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package app

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// ClearAppFlowResource 清理App资源
func ClearAppFlowResource(ctx context.Context,
	req *KEP_WF.ClearAppFlowResourceReq) (*KEP_WF.ClearAppFlowResourceRsp, error) {
	appBizId := req.GetBotBizId()
	taskId := req.GetTaskId()
	rsp := new(KEP_WF.ClearAppFlowResourceRsp)
	appId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		log.WarnContextf(ctx, "ClearAppFlowResource|err:%+v", err)
		return nil, fmt.Errorf("robot:%s not exits", appBizId)
	}

	log.DebugContextf(ctx, "ClearAppFlowResource|appBizId:%s|taskId:%+v|rps:%+v", appBizId, taskId)

	clear := resource.NewDao()
	app, err := clear.GetAppByID(ctx, appId)
	if err != nil {
		return nil, fmt.Errorf("get App info err:%+v", err)
	}
	if !app.IsDelete { // 应用未删除不允许删除
		return nil, fmt.Errorf("robot:%d has not been deleted", appId)
	}

	fReq := entity.FlowDeleteParams{
		Name:    entity.TaskTypeNameMap[entity.FlowDeleteTask],
		RobotID: appId,
		CorpID:  util.CorpID(ctx),
		TaskID:  taskId,
	}

	if err := clear.CreateFlowDeleteTask(ctx, fReq); err != nil {
		log.ErrorContextf(ctx, "ClearAppFlowResource CreateFlowDeleteTask err:%+v", err)
		return nil, err
	}

	return rsp, err
}

// DeleteAppResource 标记删除应用下对应的资源
func DeleteAppResource(ctx context.Context, req *KEP_WF.DeleteAppResourceReq) (*KEP_WF.DeleteAppResourceRsp, error) {
	rsp := new(KEP_WF.DeleteAppResourceRsp)
	robotId := req.GetBotBizId()
	appId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteAppResource|err:%+v", err)
		return nil, fmt.Errorf("robot:%s not exits", robotId)
	}
	del := resource.NewDao()
	app, err := del.GetAppByID(ctx, appId)
	if err != nil {
		return nil, fmt.Errorf("get App info err:%+v", err)
	}
	if !app.IsDelete { // 应用未删除不允许删除
		return nil, fmt.Errorf("robot:%d has not been deleted", appId)
	}
	if err := del.DeleteWorkflowResource(ctx, robotId); err != nil {
		log.ErrorContextf(ctx, "DeleteAppResource|err:%+v", err)
		return nil, err
	}

	return rsp, nil
}
