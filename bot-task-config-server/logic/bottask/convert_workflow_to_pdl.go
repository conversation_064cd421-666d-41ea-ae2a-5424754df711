/*
 * 2024-12-26
 * Copyright (c) 2024. le<PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package bottask

import (
	"context"
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/pdlconvert"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	utilErrors "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"gopkg.in/yaml.v3"
)

// ConvertWorkflowToPdlScheduler 转换工作流到PDL
type ConvertWorkflowToPdlScheduler struct {
	task   task_scheduler.Task
	params entity.TaskConvertWorkflowToPDLParams
	err    error
}

func init() {
	task_scheduler.Register(
		entity.TaskConvertWorkflowToPDL,
		func(task task_scheduler.Task, params entity.TaskConvertWorkflowToPDLParams) task_scheduler.TaskHandler {
			return &ConvertWorkflowToPdlScheduler{
				task:   task,
				params: params,
			}
		})
}

// Prepare 数据准备
func (c *ConvertWorkflowToPdlScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (c *ConvertWorkflowToPdlScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, c.params.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|Init, WorkflowID:%s, TaskID:%s",
		c.params.WorkflowID, c.params.TaskID)
	return nil
}

// Process 任务处理
func (c *ConvertWorkflowToPdlScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|process start")

	// 查询转换的工作流（从WF_PDL表取，防止执行时取得和点击接口时的数据不一致）
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息
	// 从工作流转换到PDL的时候画布中节点的某些字段已经是加密的状态，因此这里不做加解密处理
	// 后续通过 db.SaveConvertPDL 保存到PDL表和PDL版本表的时候是直接使用的加密版本
	workflowPDL, err := db.GetWorkflowPDLDetail(ctx, c.params.WorkflowID, c.params.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|GetWorkflowDetail|err:%+v", err)
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		return err
	}
	if workflowPDL == nil || workflowPDL.WorkflowID == "" {
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonWorkflowIllegal))
		return utilErrors.ErrWorkflowNotFound
	}
	if !workflowPDL.IsWorkflowPDLConverting() {
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		return utilErrors.ErrWorkflowConvertStatus
	}

	// jsonToPB
	workflowDialog, err := protoutil.JsonToWorkflow(workflowPDL.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|JsonToWorkflow|id:%s, err:%+v", workflowPDL.WorkflowID, err)
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonWorkflowIllegal))
		return err
	}
	// 获取VarParams(GetVarList)
	var varParams []*entity.VarParams
	varParams, err = db.GetVarsByNameOrAppId(ctx, c.params.RobotID, "", "", false)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|GetVarsByNameOrAppId|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		return err
	}
	// 获取ParamsInfo(GetParameterList)
	paramInfoList, err := protoutil.JsonToParameterInfoList(workflowPDL.Parameter)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|JsonToParameterInfoList|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		return err
	}

	// 转换Convert
	pdlContent, toolsInfo, err := pdlconvert.NewPDL().Convert(ctx, workflowDialog, paramInfoList.List, varParams)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|Convert|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		c.err = err
		return err
	}

	// pdlContent转换yaml
	pdlContentYaml, err := yaml.Marshal(pdlContent)
	if err != nil {
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|Marshal PDLContent|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		return err
	}
	pdlContentYamlStr := string(pdlContentYaml)
	// 转换不做长度校验，调试时校验
	/*pdlTokenLen := pdlconvert.GetTextTokenLen(ctx, pdlContentYamlStr)
	log.InfoContextf(ctx, "pdlContentYaml token len:%d, text len:%d", pdlTokenLen, len([]rune(pdlContentYamlStr)))
	if pdlTokenLen > config.GetMainConfig().PDLConvertConfig.PDLTokenLenLimit {
		c.err = fmt.Errorf("pdlContent token length[%d] exceeded", pdlTokenLen)
		log.WarnContextf(ctx, "ConvertWorkflowToPdl|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		return c.err
	}*/

	// apiInfo转换成json
	apiInfoJson, err := protoutil.PDLToolsInfoToJson(toolsInfo)
	if err != nil {
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|PDLToolsInfoToJson|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		return err
	}
	workflowPDL.PdlContent = pdlContentYamlStr
	workflowPDL.ToolsInfo = apiInfoJson
	workflowPDL.StaffID = c.params.StaffID
	workflowPDL, err = db.SaveConvertPDL(ctx, workflowPDL)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertWorkflowToPdl|SaveConvertPDL|workflowID:%s,err:%+v", c.params.WorkflowID, err)
		c.err = errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
		return err
	}
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|process end|workflowID:%s", c.params.WorkflowID)
	return nil
}

// Done 任务完成回调
func (c *ConvertWorkflowToPdlScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|Done|workflowID:%s", c.params.WorkflowID)
	// 发送通知
	//if err := c.sendWorkflowConvertFinishNotice(ctx, true); err != nil {
	//	return err
	//}
	return nil
}

// Fail 任务失败
func (c *ConvertWorkflowToPdlScheduler) Fail(ctx context.Context) error {
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|Fail|workflowID:%s,reason:%+v", c.params.WorkflowID, c.err)
	failReason := ""
	if c.err != nil {
		failReason = c.err.Error()
		// 按字符截断，避免乱码写入db失败
		reasonLenLimit := config.GetMainConfig().PDLConfig.ReasonLenLimit
		if len([]rune(failReason)) > reasonLenLimit {
			failReason = string([]rune(failReason)[0:reasonLenLimit])
		}
	}

	updateStateParams := entity.UpdateWorkflowPDLConvertStateParams{
		WorkflowID:        c.params.WorkflowID,
		AppBizID:          c.params.RobotID,
		IsConvertedFailed: true,
		FailedReason:      failReason,
	}

	if err := db.UpdateWorkflowPDLConvertState(ctx, updateStateParams); err != nil {
		return err
	}

	// 发送通知
	//if err := c.sendWorkflowConvertFinishNotice(ctx, false); err != nil {
	//	return err
	//}
	return nil
}

// Stop 任务停止，什么情况下触发停止？
func (c *ConvertWorkflowToPdlScheduler) Stop(_ context.Context) error {
	return nil
}

// sendWorkflowConvertFinishNotice
//func (c ConvertWorkflowToPdlScheduler) sendWorkflowConvertFinishNotice(ctx context.Context, result bool) error {
//	var noticeContent, noticeSubject, noticeLevel string
//	if result {
//		noticeContent = fmt.Sprintf(entity.WorkflowConvertPDLNoticeContent, c.params.Name, "成功")
//		noticeSubject = "工作流转换成功"
//		noticeLevel = entity.LevelSuccess
//	} else {
//		noticeContent = fmt.Sprintf(entity.WorkflowConvertPDLNoticeContent, c.params.Name, "失败")
//		noticeSubject = "工作流转换失败"
//		noticeLevel = entity.LevelError
//	}
//	createNoticeReq := &pb.CreateNoticeReq{
//		BotBizId:     uint64(encode.StringToInt64(c.params.RobotID)),
//		PageId:       entity.NoticeWorkflowPageID,
//		Type:         entity.NoticeTypeWorkFlowConvert,
//		Level:        noticeLevel,
//		RelateId:     c.params.TaskID,
//		Subject:      noticeSubject,
//		Content:      noticeContent,
//		IsGlobal:     false,
//		IsAllowClose: true,
//		CorpId:       c.params.CorpID,
//		StaffId:      c.params.StaffID,
//	}
//	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
//		return err
//	}
//	return nil
//}
