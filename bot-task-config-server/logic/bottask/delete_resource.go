// bot-task-config-server
//
// @(#)delete_resource.go  星期四, 十一月 07, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package bottask

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/resource/handler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"github.com/spf13/cast"
)

// FlowDeleteScheduler 知识删除任务
type FlowDeleteScheduler struct {
	dao    resource.Dao
	task   task_scheduler.Task
	params entity.FlowDeleteParams

	sync.RWMutex
	tableHandlerMap map[string]resource.DeleteHandler
}

// init 初始化
func init() {
	task_scheduler.Register(
		entity.FlowDeleteTask,
		func(task task_scheduler.Task, params entity.FlowDeleteParams) task_scheduler.TaskHandler {
			return &FlowDeleteScheduler{
				dao:             resource.NewDao(),
				task:            task,
				params:          params,
				tableHandlerMap: map[string]resource.DeleteHandler{},
			}
		},
	)
}

// tableDataCount 表数据数量
type tableDataCount struct {
	TableName  string `json:"table_name"`  // 表名称
	TableCount int64  `json:"table_count"` // 表数量
}

// Prepare 任务准备
func (k *FlowDeleteScheduler) Prepare(ctx context.Context) (task_scheduler.TaskKV, error) {
	log.InfoContextf(ctx, " FlowDeleteScheduler Prepare, task:%+v, params:%+v", k.task, k.params)
	app, err := k.dao.GetAppByID(ctx, k.params.RobotID)
	if err != nil {
		return nil, fmt.Errorf("get App info err:%+v", err)
	}

	if !app.IsDelete { // 应用未删除不允许删除
		return nil, fmt.Errorf("robot:%d has not been deleted", k.params.RobotID)
	}
	log.InfoContextf(ctx, " FlowDeleteScheduler Prepare, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	return task_scheduler.TaskKV{}, nil
}

// Init 数据初始化
func (k *FlowDeleteScheduler) Init(ctx context.Context, kv task_scheduler.TaskKV) error {
	log.InfoContextf(ctx, " FlowDeleteScheduler Init, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	// 统计应用下还未删除的各个资源数量
	tableCountChan := make(chan tableDataCount,
		len(config.GetMainConfig().FlowDeleteConfig.NeedDeleteTables))
	errChan := make(chan error, len(config.GetMainConfig().FlowDeleteConfig.NeedDeleteTables))
	wg := sync.WaitGroup{}
	for tableName, handlerName := range config.GetMainConfig().FlowDeleteConfig.NeedDeleteTables {
		log.InfoContextf(ctx, "FlowDeleteScheduler:tname:%s|h:%s", tableName, handlerName)

		deleteHandler, err := handler.GetDeleteHandler(handlerName)
		if err != nil {
			return err
		}
		wg.Add(1)
		go func(ctx context.Context, tableName string, deleteHandler resource.DeleteHandler) {
			defer wg.Done()
			newTableName := getWorkflowOrProdTableName(tableName)
			tableCount, err := deleteHandler.CountNeedDeletedData(ctx, k.params.CorpID, k.params.RobotID, newTableName)
			if err != nil {
				errChan <- err
				return
			}
			if tableCount > 0 {
				k.Lock()
				k.tableHandlerMap[tableName] = deleteHandler
				k.Unlock()

				tableCountChan <- tableDataCount{
					TableName:  tableName,
					TableCount: tableCount,
				}
			}
		}(trpc.CloneContext(ctx), tableName, deleteHandler)
	}
	wg.Wait()
	close(tableCountChan)

	select {
	case err := <-errChan:
		log.ErrorContextf(ctx, " FlowDeleteScheduler Init, taskID:%d, traceID:%s, Failed err:%+v",
			k.task.ID, k.task.TraceID, err)
		return err
	default:
		for tableCount := range tableCountChan {
			_, ok := config.GetMainConfig().FlowDeleteConfig.NeedDeleteTables[tableCount.TableName]
			log.InfoContextf(ctx, "FlowDeleteScheduler Process:|TableName:%+v|tableCount:%+v|ok:%+v",
				tableCount.TableName, tableCount.TableCount, ok)
			if ok && tableCount.TableCount > 0 {
				log.DebugContextf(ctx, " FlowDeleteScheduler Init, taskID:%d, traceID:%s, "+
					"table:%s, count:%d", k.task.ID, k.task.TraceID, tableCount.TableName, tableCount.TableCount)
				kv[tableCount.TableName] = fmt.Sprintf("%d", tableCount.TableCount)
			}
		}
		log.InfoContextf(ctx, " FlowDeleteScheduler Init, taskID:%d, traceID:%s, kv:%+v",
			k.task.ID, k.task.TraceID, kv)
		return nil

	}
}

// Process 任务处理
func (k *FlowDeleteScheduler) Process(ctx context.Context, progress *task_scheduler.Progress) error {
	log.InfoContextf(ctx, "FlowDeleteScheduler Process, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	for taskK, taskV := range progress.TaskKV(ctx) {
		log.InfoContextf(ctx, " FlowDeleteScheduler Process, taskID:%d, traceID:%s, Start: k:%s, v:%s",
			k.task.ID, k.task.TraceID, taskK, taskV)
		tableName := taskK
		tableCount := cast.ToInt64(taskV)
		deleteHandler := k.tableHandlerMap[tableName]
		newTableName := getWorkflowOrProdTableName(tableName)

		if err := deleteHandler.DeleteNeedDeletedData(ctx, k.params.CorpID, k.params.RobotID,
			newTableName, tableCount); err != nil {
			log.ErrorContextf(ctx, " FlowDeleteScheduler Process, taskID:%d, traceID:%s, Failed err:%+v",
				k.task.ID, k.task.TraceID, err)
			return err
		}
	}
	log.InfoContextf(ctx, " FlowDeleteScheduler Process, taskID:%d, traceID:%s done",
		k.task.ID, k.task.TraceID)
	return nil
}

// Done 任务完成
func (k *FlowDeleteScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, " FlowDeleteScheduler Done, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	// 回调admin任务状态
	err := k.dao.FlowDeleteResultCallback(ctx, k.params.TaskID, true, "")
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, " FlowDeleteScheduler Done, taskID:%d, traceID:%s done", k.task.ID, k.task.TraceID)
	return nil
}

// Fail 任务失败
func (k *FlowDeleteScheduler) Fail(ctx context.Context) error {
	log.InfoContextf(ctx, " FlowDeleteScheduler Fail, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	// 回调admin任务状态
	err := k.dao.FlowDeleteResultCallback(ctx, k.params.TaskID, false, k.task.Result)
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, " FlowDeleteScheduler Fail, taskID:%d, traceID:%s done", k.task.ID, k.task.TraceID)
	return nil
}

// Stop 任务停止
func (k *FlowDeleteScheduler) Stop(ctx context.Context) error {
	log.InfoContextf(ctx, " FlowDeleteScheduler Stop, taskID:%d, traceID:%s", k.task.ID, k.task.TraceID)
	return nil
}

func getWorkflowOrProdTableName(oldTable string) string {
	newTableName := oldTable
	if strings.HasPrefix(oldTable, "prod|") {
		newTableName = strings.Replace(oldTable, "prod|", "", 1)
	}
	if strings.HasPrefix(oldTable, "w|") {
		newTableName = strings.Replace(oldTable, "w|", "", 1)
	}
	return newTableName
}
