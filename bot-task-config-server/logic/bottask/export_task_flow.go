package bottask

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ExportTaskFlowScheduler 导出任务流程调度任务
type ExportTaskFlowScheduler struct {
	task     task_scheduler.Task
	p        entity.TaskFlowExportParams
	CosPath  string
	FileSize uint64
}

func init() {
	task_scheduler.Register(
		entity.TaskExportTaskFlow,
		func(t task_scheduler.Task, params entity.TaskFlowExportParams) task_scheduler.TaskHandler {
			return &ExportTaskFlowScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (etfs *ExportTaskFlowScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (etfs *ExportTaskFlowScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, etfs.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ExportTaskFlow Init, ExportID:%d", etfs.p.ExportID)
	return nil
}

// Process 任务处理
func (etfs *ExportTaskFlowScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	sid := util.RequestID(ctx)
	// 获取分类信息
	mapCategory, err := db.GetCategoryMap(ctx, etfs.p.RobotID)
	if err != nil {
		return err
	}
	// 获取任务流程信息
	mapTaskFlow, intentIDs, categoryIDs, err := etfs.getTaskFlows(ctx)
	if err != nil {
		return err
	}
	// 获取意图信息
	mapIntent, err := db.GetIntentByIntentIDs(ctx, intentIDs)
	if err != nil {
		return err
	}
	// 获取语料信息
	mapIntentCorpus, err := db.GetCorpusByIntentIDs(ctx, intentIDs)
	if err != nil {
		return err
	}
	// 获取槽位信息
	mapIntentSlotInfo, err := db.GetExportSlotInfos(ctx, etfs.p.RobotID, intentIDs)
	if err != nil {
		return err
	}
	// 获取意图示例问法
	mapIntentExampleInfo, err := db.GetExportIntentExampleInfos(ctx, etfs.p.RobotID, intentIDs)
	if err != nil {
		return err
	}
	mapExportIntentVar, err := db.GetExportIntentVarParams(ctx, etfs.p.RobotID, intentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExportIntentVarParams|err:%+v", sid, err)
		return err
	}

	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)
	// 写入分类文件
	if err := etfs.generateCategoryFile(ctx, zipWriter, mapCategory, categoryIDs); err != nil {
		return err
	}
	// 写入任务流程文件
	if err := etfs.generateTaskFlowFile(ctx, zipWriter, mapTaskFlow); err != nil {
		return err
	}
	// 写入意图文件
	if err := etfs.generateIntentFile(ctx, zipWriter, intentIDs, mapIntent); err != nil {
		return err
	}
	// 写入语料文件
	if err := etfs.generateCorpusFile(ctx, zipWriter, intentIDs, mapIntentCorpus); err != nil {
		return err
	}
	// 写入槽位文件
	if err := etfs.generateSlotFile(ctx, zipWriter, intentIDs, mapIntentSlotInfo); err != nil {
		return err
	}
	// 写入意图示例问法文件
	if err := etfs.generateIntentExampleFile(ctx, zipWriter, intentIDs, mapIntentExampleInfo); err != nil {
		return err
	}
	// 写入意图自定义变量文件
	if err := etfs.generateIntentVarParamsFile(ctx, zipWriter, intentIDs, mapExportIntentVar); err != nil {
		return err
	}

	zipWriter.Close()
	// 上传cos文件
	filename := fmt.Sprintf("export-%d.zip", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(etfs.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, zipBuffer.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject Failed! err:%+v", err)
		return err
	}
	etfs.CosPath = cosPath
	etfs.FileSize = uint64(zipBuffer.Len())

	return nil
}

// getCategoryNodes
func getCategoryNodes(mapCategory map[string]*category.Category, categoryID string) []string {
	var categoryNameFn func(categoryID string)
	categoryNames := make([]string, 0)
	categoryNameFn = func(categoryID string) {
		cate, ok := mapCategory[categoryID]
		if !ok {
			return
		}
		categoryNames = append([]string{cate.CategoryName}, categoryNames...)
		if cate.ParentID == category.AllCateID {
			return
		}
		categoryNameFn(cate.ParentID)
	}
	categoryNameFn(categoryID)
	categoryNames = append([]string{categoryID}, categoryNames...)
	return categoryNames
}

func (etfs *ExportTaskFlowScheduler) generateExcelFile(_ context.Context, writer *zip.Writer, fileName string,
	titles []string, values [][]string) error {
	data := make([][]string, 1) // 预留一行保存头部信息
	data[0] = titles
	data = append(data, values...)
	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	// 保存为xlsx文件
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		return err
	}
	dataBuffer, err := xlFile.WriteToBuffer()
	if err != nil {
		return err
	}
	fileData, err := io.ReadAll(dataBuffer)
	if err != nil {
		return err
	}
	return file.WriteZipData(writer, file.FileInfo{FileName: fileName, Data: fileData})
}

func (etfs *ExportTaskFlowScheduler) generateJsonFile(_ context.Context, writer *zip.Writer, fileName string,
	jsonStr string) error {
	return file.WriteZipData(writer, file.FileInfo{FileName: fileName, Data: []byte(jsonStr)})
}

func (etfs *ExportTaskFlowScheduler) generateTaskFlowFile(ctx context.Context, writer *zip.Writer,
	mapTaskFlow map[string]*entity.TaskFlow) error {
	values := make([][]string, 0)
	for _, flowID := range etfs.p.FlowIDs {
		taskFlow, ok := mapTaskFlow[flowID]
		if !ok {
			continue
		}
		jsonFileName := entity.GetTaskFlowJsonFileKey(taskFlow.FlowID)
		err := etfs.generateJsonFile(ctx, writer, jsonFileName, taskFlow.DialogJsonDraft)
		if err != nil {
			return err
		}
		values = append(values, []string{
			taskFlow.FlowID,
			taskFlow.IntentID,
			taskFlow.IntentName,
			taskFlow.IntentDesc,
			taskFlow.CategoryID,
			jsonFileName,
		})
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.TaskFlowFileName,
		config.GetMainConfig().TaskFlow.TaskFlowHead, values)
}

func (etfs *ExportTaskFlowScheduler) generateIntentFile(ctx context.Context, writer *zip.Writer,
	intentIDs []string, mapIntent map[string]*entity.Intent) error {
	values := make([][]string, 0)
	for _, intentID := range intentIDs {
		intent, ok := mapIntent[intentID]
		if !ok {
			continue
		}
		values = append(values, []string{
			intent.IntentID,
			intent.IntentName,
			intent.IntentDesc,
			intent.IntentType,
			intent.Source,
		})
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.IntentFileName,
		config.GetMainConfig().TaskFlow.IntentHead, values)
}

func (etfs *ExportTaskFlowScheduler) generateCorpusFile(ctx context.Context, writer *zip.Writer,
	intentIDs []string, mapIntentCorpus map[string][]*entity.Corpus) error {
	values := make([][]string, 0)
	for _, intentID := range intentIDs {
		corpus, ok := mapIntentCorpus[intentID]
		if !ok {
			continue
		}
		for _, v := range corpus {
			values = append(values, []string{
				intentID,
				v.Content,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.CorpusFileName,
		config.GetMainConfig().TaskFlow.CorpusHead, values)
}

func (etfs *ExportTaskFlowScheduler) generateSlotFile(ctx context.Context, writer *zip.Writer,
	intentIDs []string, mapIntentSlot map[string][]*entity.SlotMigrationInfo) error {
	values := make([][]string, 0)
	entityFileMap := make(map[string]struct{})
	for _, intentID := range intentIDs {
		slots, ok := mapIntentSlot[intentID]
		if !ok {
			continue
		}
		for _, slot := range slots {
			examples, _ := jsoniter.MarshalToString(slot.SlotExamples)
			entityInfo, _ := jsoniter.MarshalToString(slot.EntityInfo)
			entityFileName := entity.GetSlotEntitiesFileKey(slot.SlotID)
			_, ok = entityFileMap[entityFileName]
			if !ok {
				err := etfs.generateJsonFile(ctx, writer, entityFileName, entityInfo)
				if err != nil {
					return err
				}
				entityFileMap[entityFileName] = struct{}{}
			}
			values = append(values, []string{
				intentID,
				slot.SlotID,
				slot.SlotName,
				slot.SlotDesc,
				examples,
				entityFileName,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.SlotFileName,
		config.GetMainConfig().TaskFlow.SlotHead, values)
}

// generateIntentVarParamsFile 生成变量文件
func (etfs *ExportTaskFlowScheduler) generateIntentVarParamsFile(ctx context.Context, writer *zip.Writer,
	intentIDs []string, mapExportIntentVar map[string][]*entity.ExportIntentVar) error {
	values := make([][]string, 0)
	for _, intentID := range intentIDs {
		intentCorpus, ok := mapExportIntentVar[intentID]
		if !ok {
			continue
		}
		for _, item := range intentCorpus {
			values = append(values, []string{
				intentID,
				item.VarID,
				item.VarName,
				item.VarDesc,
				item.VarType,
				item.VarDefaultValue,
				item.VarDefaultFileName,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.VarParamsFileName,
		config.GetMainConfig().TaskFlow.VarParamsHead, values)
}

// generateIntentExampleFile 生成示例问法文件
func (etfs *ExportTaskFlowScheduler) generateIntentExampleFile(ctx context.Context, writer *zip.Writer,
	intentIDs []string, mapIntentCorpus map[string][]*entity.IntentCorpus) error {
	values := make([][]string, 0)
	for _, intentID := range intentIDs {
		intentCorpus, ok := mapIntentCorpus[intentID]
		if !ok {
			continue
		}
		for _, example := range intentCorpus {
			values = append(values, []string{
				intentID,
				example.CorpusID,
				example.Corpus,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.IntentExampleFileName,
		config.GetMainConfig().TaskFlow.IntentExampleHead, values)
}

func (etfs *ExportTaskFlowScheduler) generateCategoryFile(ctx context.Context, writer *zip.Writer,
	mapCategory map[string]*category.Category, categoryIDs []string) error {
	values := make([][]string, 0)
	for _, catetoryID := range categoryIDs {
		values = append(values, getCategoryNodes(mapCategory, catetoryID))
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().TaskFlow.CategoryFileName,
		config.GetMainConfig().TaskFlow.CategoryHead, values)
}

// getTaskFlows
func (etfs *ExportTaskFlowScheduler) getTaskFlows(ctx context.Context) (map[string]*entity.TaskFlow, []string,
	[]string, error) {
	mapTaskFlow, err := db.GetTaskFlowByFlowIDs(ctx, etfs.p.RobotID, etfs.p.FlowIDs)
	if err != nil {
		return nil, nil, nil, err
	}
	intentIDs := make([]string, 0)
	categoryIDs := make([]string, 0)
	mapCategoryID := make(map[string]struct{})
	for _, v := range mapTaskFlow {
		intentIDs = append(intentIDs, v.IntentID)
		if _, ok := mapCategoryID[v.CategoryID]; !ok {
			categoryIDs = append(categoryIDs, v.CategoryID)
		}
		mapCategoryID[v.CategoryID] = struct{}{}
	}
	return mapTaskFlow, intentIDs, categoryIDs, nil
}

// Fail 任务失败
func (etfs *ExportTaskFlowScheduler) Fail(ctx context.Context) error {
	if etfs.p.Platform == entity.ExportTaskPlatformOp {
		return etfs.setExportTaskFileFinishStatus(ctx, entity.ExportTaskStatusFailed)
	} else {
		return etfs.sendTaskFlowExportFinishNotice(ctx, false)
	}
}

// Stop 任务停止
func (etfs *ExportTaskFlowScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (etfs *ExportTaskFlowScheduler) Done(ctx context.Context) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|ExportTaskFlowScheduler|GetCosPath|params:%+v", sid, etfs.p)
	if etfs.p.Platform == entity.ExportTaskPlatformOp {
		return etfs.setExportTaskFileFinishStatus(ctx, entity.ExportTaskStatusSuccess)
	} else {
		return etfs.sendTaskFlowExportFinishNotice(ctx, true)
	}
}

func (etfs *ExportTaskFlowScheduler) sendTaskFlowExportFinishNotice(ctx context.Context, result bool) error {
	var noticeContent, noticeSubject, noticeLevel string
	if result {
		noticeContent = fmt.Sprintf(entity.TaskFlowExportNoticeContent, "成功")
		noticeSubject = "任务流程导出成功"
		noticeLevel = entity.LevelSuccess
	} else {
		noticeContent = fmt.Sprintf(entity.TaskFlowExportNoticeContent, "失败")
		noticeSubject = "任务流程导出失败"
		noticeLevel = entity.LevelError
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(etfs.p.RobotID)),
		PageId:       entity.NoticeTaskFlowPageID,
		Type:         entity.NoticeTypeTaskFlowExport,
		Level:        noticeLevel,
		RelateId:     etfs.p.ExportID,
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       etfs.p.CorpID,
		StaffId:      etfs.p.StaffID,
	}
	if result {
		createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
			Type:   entity.OpTypeExportDownload,
			Params: &pb.CreateNoticeReq_Operation_Params{CosPath: etfs.CosPath},
		})
		createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
			Type:   entity.OpTypeViewDetail,
			Params: &pb.CreateNoticeReq_Operation_Params{},
		})
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

func (etfs *ExportTaskFlowScheduler) setExportTaskFileFinishStatus(ctx context.Context, status int) error {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()

	if err := db.Table(entity.ExportFile{}.TableName()).
		Where("f_export_id=?", etfs.p.ExportID).Updates(map[string]interface{}{
		"f_cos_url":     etfs.CosPath,
		"f_file_size":   etfs.FileSize,
		"f_status":      status,
		"f_update_time": time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|ExportFile|Update|err:%+v", util.RequestID(ctx), err)
		return err
	}
	return nil
}
