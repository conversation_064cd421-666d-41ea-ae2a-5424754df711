package bottask

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ExportWorkflowScheduler 导出工作流程调度任务
type ExportWorkflowScheduler struct {
	task     task_scheduler.Task
	p        entity.WorkflowExportParams
	CosPath  string
	FileSize uint64
}

func init() {
	task_scheduler.Register(
		entity.TaskExportWorkflow,
		func(t task_scheduler.Task, params entity.WorkflowExportParams) task_scheduler.TaskHandler {
			return &ExportWorkflowScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (etfs *ExportWorkflowScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (etfs *ExportWorkflowScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, etfs.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ExportWorkflow Init, ExportID:%d", etfs.p.ExportID)
	return nil
}

// Process 任务处理
func (etfs *ExportWorkflowScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	// 获取工作流源数据
	workflowMap, workflowRefMap, paramMap, exampleMap, workflowVarMap, PDLMap, err := GetWorkflowSourceData(ctx,
		etfs.p.RobotID, etfs.p.RobotID, etfs.p.FlowIDs, false)
	if err != nil {
		return err
	}
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)
	// 写入工作流程文件
	if err := etfs.generateWorkflowFile(ctx, zipWriter, workflowMap, nil); err != nil {
		return err
	}
	// 写入参数文件
	if err := etfs.generateParamFile(ctx, zipWriter, paramMap); err != nil {
		return err
	}
	// 写入工作流示例问法文件
	if err := etfs.generateWorkflowExampleFile(ctx, zipWriter, exampleMap); err != nil {
		return err
	}
	// 写入工作流自定义变量文件
	if err := etfs.generateWorkflowVarFile(ctx, zipWriter, workflowVarMap); err != nil {
		return err
	}
	// 写入工作流程引用工作流程关系文件
	if err := etfs.generateWorkflowRefFile(ctx, zipWriter, workflowRefMap); err != nil {
		return err
	}
	// 写入工作流pdl文件
	if err := etfs.generateWorkflowPDLFile(ctx, zipWriter, PDLMap); err != nil {
		return err
	}
	zipWriter.Close()
	// 上传cos文件
	filename := fmt.Sprintf("export-%d.zip", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(etfs.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, zipBuffer.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject Failed! err:%+v", err)
		return err
	}
	etfs.CosPath = cosPath
	etfs.FileSize = uint64(zipBuffer.Len())

	return nil
}

// GetWorkflowSourceData 获取源数据，exampleEqual表示是否当前应用下老的示例问法
func GetWorkflowSourceData(ctx context.Context, robotId, sourceRobotId string, sourceWorkflowIds []string,
	exampleEqual bool) (
	map[string]*entity.ExportWorkflow, map[string][]*entity.ExportWorkflowRef,
	map[string][]*entity.ParamMigrationInfo, map[string][]*entity.ExportWorkflowExample,
	map[string][]*entity.ExportWorkflowVar, map[string][]*entity.ExportWorkflowPDL, error) {
	existExample := make(map[string]struct{}, 0)
	// 获取工作流程信息
	workflowList, err := db.GetExportWorkflowDetail(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	workflowMap := make(map[string]*entity.ExportWorkflow, 0)
	for _, item := range workflowList {
		workflowMap[item.WorkflowID] = item
		// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
		workflowMap[item.WorkflowID].DialogJson, err = protoutil.DecryptWorkflowJson(workflowMap[item.WorkflowID].DialogJson)
		if err != nil {
			log.ErrorContextf(ctx, " GetWorkflowSourceData DecryptWorkflowJson err:|%+v", err)
			return nil, nil, nil, nil, nil, nil, err
		}
	}
	// 获取工作流程引用信息
	//workflowRefList, err := db.GetWorkflowRefsByFlowIds(ctx, sourceRobotId, sourceWorkflowIds)
	workflowRefList, err := db.GetWorkflowRefInfosByFlowIds(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	workflowRefMap := make(map[string][]*entity.ExportWorkflowRef)
	for _, workflowRef := range workflowRefList {
		workflowRef.WorkflowRefNewID = workflowRef.WorkflowRefID
		if _, ok := workflowMap[workflowRef.WorkflowRefID]; ok {
			workflowRef.WorkflowRefName = workflowMap[workflowRef.WorkflowRefID].WorkflowName
			workflowRefMap[workflowRef.WorkflowID] = append(workflowRefMap[workflowRef.WorkflowID], workflowRef)
		}
	}
	// 获取参数信息
	paramList, err := db.GetExportParamsDetail(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	paramMap := make(map[string][]*entity.ParamMigrationInfo)
	for _, param := range paramList {
		if _, ok := workflowMap[param.FlowID]; ok {
			paramMap[param.FlowID] = append(paramMap[param.FlowID], param)
		}
	}
	// 获取示例问法
	exampleList, err := db.GetExportExampleDetail(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	if exampleEqual {
		existExample, err = db.GetAllExample(ctx, robotId)
		if err != nil {
			log.ErrorContextf(ctx, "GetWorkflowSourceData GetAllExample err:|%+v", err)
			return nil, nil, nil, nil, nil, nil, err
		}
	}
	exampleMap := make(map[string][]*entity.ExportWorkflowExample)
	for _, example := range exampleList {
		if exampleEqual {
			if _, ok := existExample[example.Example]; ok {
				log.WarnContextf(ctx, "GetWorkflowSourceData example is exist name:%s", example.Example)
				continue
			}
		}
		exampleMap[example.FlowID] = append(exampleMap[example.FlowID], example)
	}
	// 获取变量
	exportWorkflowVar, exportVar, err := db.GetExportVarDetail(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	workflowVarMap := make(map[string][]*entity.ExportWorkflowVar)
	varMap := make(map[string]*entity.ExportWorkflowVar)
	for _, v := range exportVar {
		varMap[v.VarID] = v
	}
	for _, workflowVar := range exportWorkflowVar {
		if _, ok := varMap[workflowVar.VarID]; ok {
			workflowVar.VarName = varMap[workflowVar.VarID].VarName
			workflowVar.VarDesc = varMap[workflowVar.VarID].VarDesc
			workflowVar.VarType = varMap[workflowVar.VarID].VarType
			workflowVar.VarDefaultValue = varMap[workflowVar.VarID].VarDefaultValue
			workflowVar.VarDefaultFileName = varMap[workflowVar.VarID].VarDefaultFileName
			workflowVarMap[workflowVar.FlowID] = append(workflowVarMap[workflowVar.FlowID], workflowVar)
		}
	}
	// 获取pdl
	PDLList, err := db.GetPDLByFlowIds(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	PDLMap := make(map[string][]*entity.ExportWorkflowPDL)
	for _, PDL := range PDLList {
		PDLMap[PDL.WorkflowID] = append(PDLMap[PDL.WorkflowID], PDL)
	}
	return workflowMap, workflowRefMap, paramMap, exampleMap, workflowVarMap, PDLMap, nil
}

func (etfs *ExportWorkflowScheduler) generateExcelFile(_ context.Context, writer *zip.Writer, fileName string,
	titles []string, values [][]string) error {
	data := make([][]string, 1) // 预留一行保存头部信息
	data[0] = titles
	data = append(data, values...)
	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	// 保存为xlsx文件
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		return err
	}
	dataBuffer, err := xlFile.WriteToBuffer()
	if err != nil {
		return err
	}
	fileData, err := io.ReadAll(dataBuffer)
	if err != nil {
		return err
	}
	return file.WriteZipData(writer, file.FileInfo{FileName: fileName, Data: fileData})
}

func (etfs *ExportWorkflowScheduler) generateJsonFile(_ context.Context, writer *zip.Writer, fileName string,
	jsonStr string) error {
	return file.WriteZipData(writer, file.FileInfo{FileName: fileName, Data: []byte(jsonStr)})
}

func (etfs *ExportWorkflowScheduler) generateWorkflowFile(ctx context.Context, writer *zip.Writer,
	mapWorkflow map[string]*entity.ExportWorkflow, refIds map[string][]string) error {
	values := make([][]string, 0, len(mapWorkflow))
	for _, workflow := range mapWorkflow {
		jsonFileName := entity.GetWorkflowJsonFileKey(workflow.WorkflowID)
		err := etfs.generateJsonFile(ctx, writer, jsonFileName, workflow.DialogJson)
		if err != nil {
			return err
		}
		values = append(values, []string{
			workflow.WorkflowID,
			workflow.WorkflowName,
			workflow.WorkflowDesc,
			//util.Object2String(refIds[workflow.WorkflowID]),
			jsonFileName,
		})
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.WorkflowFileName,
		config.GetMainConfig().Workflow.WorkflowHead, values)
}

func (etfs *ExportWorkflowScheduler) generateParamFile(ctx context.Context, writer *zip.Writer,
	mapParam map[string][]*entity.ParamMigrationInfo) error {
	values := make([][]string, 0)
	for flowID, params := range mapParam {
		for _, param := range params {
			values = append(values, []string{
				flowID,
				param.NodeID,
				param.NodeName,
				param.ParamID,
				param.ParamName,
				param.ParamDesc,
				param.ParamType,
				param.CorrectExample,
				param.IncorrectExample,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.ParamFileName,
		config.GetMainConfig().Workflow.ParamHead, values)
}

// generateWorkflowVarFile 生成变量文件
func (etfs *ExportWorkflowScheduler) generateWorkflowVarFile(ctx context.Context, writer *zip.Writer,
	mapWorkflowVar map[string][]*entity.ExportWorkflowVar) error {
	values := make([][]string, 0)
	for flowID, vars := range mapWorkflowVar {
		for _, item := range vars {
			values = append(values, []string{
				flowID,
				item.VarID,
				item.VarName,
				item.VarDesc,
				item.VarType,
				item.VarDefaultValue,
				item.VarDefaultFileName,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.VarFileName,
		config.GetMainConfig().Workflow.VarHead, values)
}

// generateWorkflowRefFile 生成工作流程引用工作流程文件
func (etfs *ExportWorkflowScheduler) generateWorkflowRefFile(ctx context.Context, writer *zip.Writer,
	mapWorkflowRef map[string][]*entity.ExportWorkflowRef) error {
	values := make([][]string, 0)
	for workflowID, workflowRefs := range mapWorkflowRef {
		for _, workflowRef := range workflowRefs {
			values = append(values, []string{
				workflowID,
				workflowRef.NodeID,
				workflowRef.WorkflowRefID,
				workflowRef.WorkflowRefName,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.WorkflowRefFileName,
		config.GetMainConfig().Workflow.WorkflowRefHead, values)
}

// generateWorkflowExampleFile 生成示例问法文件
func (etfs *ExportWorkflowScheduler) generateWorkflowExampleFile(ctx context.Context, writer *zip.Writer,
	mapExample map[string][]*entity.ExportWorkflowExample) error {
	values := make([][]string, 0)
	for flowID, examples := range mapExample {
		for _, example := range examples {
			values = append(values, []string{
				flowID,
				example.ExampleID,
				example.Example,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.ExampleFileName,
		config.GetMainConfig().Workflow.ExampleHead, values)
}

// Fail 任务失败
func (etfs *ExportWorkflowScheduler) Fail(ctx context.Context) error {
	if etfs.p.Platform == entity.ExportTaskPlatformOp {
		return etfs.setExportTaskFileFinishStatus(ctx, entity.ExportTaskStatusFailed)
	} else {
		return etfs.sendWorkflowExportFinishNotice(ctx, false)
	}
}

// Stop 任务停止
func (etfs *ExportWorkflowScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (etfs *ExportWorkflowScheduler) Done(ctx context.Context) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|ExportWorkflowScheduler|GetCosPath|params:%+v", sid, etfs.p)
	if etfs.p.Platform == entity.ExportTaskPlatformOp {
		return etfs.setExportTaskFileFinishStatus(ctx, entity.ExportTaskStatusSuccess)
	} else {
		return etfs.sendWorkflowExportFinishNotice(ctx, true)
	}
}

func (etfs *ExportWorkflowScheduler) sendWorkflowExportFinishNotice(ctx context.Context, result bool) error {
	var noticeContent, noticeSubject, noticeLevel string
	if result {
		noticeContent = fmt.Sprintf(entity.WorkflowExportNoticeContent, "成功")
		noticeSubject = "工作流程导出成功"
		noticeLevel = entity.LevelSuccess
	} else {
		noticeContent = fmt.Sprintf(entity.WorkflowExportNoticeContent, "失败")
		noticeSubject = "工作流程导出失败"
		noticeLevel = entity.LevelError
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(etfs.p.RobotID)),
		PageId:       entity.NoticeWorkflowPageID,
		Type:         entity.NoticeTypeWorkflowExport,
		Level:        noticeLevel,
		RelateId:     etfs.p.ExportID,
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       etfs.p.CorpID,
		StaffId:      etfs.p.StaffID,
	}
	if result {
		createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
			Type:   entity.OpTypeExportDownload,
			Params: &pb.CreateNoticeReq_Operation_Params{CosPath: etfs.CosPath},
		})
		createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
			Type:   entity.OpTypeViewDetail,
			Params: &pb.CreateNoticeReq_Operation_Params{},
		})
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

func (etfs *ExportWorkflowScheduler) setExportTaskFileFinishStatus(ctx context.Context, status int) error {
	return db.SetExportTaskFileFinishStatus(ctx, etfs.p.ExportID, etfs.FileSize, etfs.CosPath, status)
}

// generateWorkflowPDLFile 生成pdl文件
func (etfs *ExportWorkflowScheduler) generateWorkflowPDLFile(ctx context.Context, writer *zip.Writer,
	mapPDL map[string][]*entity.ExportWorkflowPDL) error {
	values := make([][]string, 0)
	for flowID, pdlList := range mapPDL {
		for _, pdl := range pdlList {
			//pdlJsonFileName := entity.GetWorkflowPDLJsonFileKey(pdl.WorkflowID)
			//err := etfs.generateJsonFile(ctx, writer, pdlJsonFileName, pdl.DialogJson)
			//if err != nil {
			//	return err
			//}
			pdlParamJsonFileName := entity.GetWorkflowPDLParamJsonFileKey(pdl.WorkflowID)
			err := etfs.generateJsonFile(ctx, writer, pdlParamJsonFileName, pdl.ParameterJson)
			if err != nil {
				return err
			}
			pdLContentJsonFileName := entity.GetWorkflowPDLContentJsonFileKey(pdl.WorkflowID)
			err = etfs.generateJsonFile(ctx, writer, pdLContentJsonFileName, pdl.ContentJson)
			if err != nil {
				return err
			}
			pdlToolJsonFileName := entity.GetWorkflowPDLToolJsonFileKey(pdl.WorkflowID)
			err = etfs.generateJsonFile(ctx, writer, pdlToolJsonFileName, pdl.ToolJson)
			if err != nil {
				return err
			}
			values = append(values, []string{
				flowID,
				pdl.Constraints,
				//pdlJsonFileName,
				pdlParamJsonFileName,
				pdLContentJsonFileName,
				pdlToolJsonFileName,
			})
		}
	}
	return etfs.generateExcelFile(ctx, writer, config.GetMainConfig().Workflow.WorkflowPDLFileName,
		config.GetMainConfig().Workflow.WorkflowPDLHead, values)
}
