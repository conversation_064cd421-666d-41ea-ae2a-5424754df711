package bottask

import (
	"archive/zip"
	"bytes"
	"context"
	"io"
	"strings"
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"github.com/agiledragon/gomonkey/v2"
)

func TestExportWorkflowScheduler_generateWorkflowFile(t *testing.T) {
	type fields struct {
		task     task_scheduler.Task
		p        entity.WorkflowExportParams
		CosPath  string
		FileSize uint64
	}
	type args struct {
		mapWorkflow map[string]*entity.ExportWorkflow
		refIds      map[string][]string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				task:     task_scheduler.Task{},
				p:        entity.WorkflowExportParams{},
				CosPath:  "123",
				FileSize: 100,
			},
			args: args{
				mapWorkflow: map[string]*entity.ExportWorkflow{
					"flow1": {
						WorkflowID:   "flow1",
						WorkflowName: "test",
						WorkflowDesc: "test",
						DialogJson:   "{}",
					},
				},
			},
			wantErr: false,
		},
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				WorkflowFileName: "工作流程.xlsx",
				WorkflowHead: []string{
					"工作流ID",
					"工作流名称",
					"工作流描述",
					"画布结构"},
			},
		}
	})
	defer mockConfig.Reset()
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			etfs := &ExportWorkflowScheduler{
				task:     tt.fields.task,
				p:        tt.fields.p,
				CosPath:  tt.fields.CosPath,
				FileSize: tt.fields.FileSize,
			}
			if err := etfs.generateWorkflowFile(context.Background(), zipWriter,
				tt.args.mapWorkflow, tt.args.refIds); (err != nil) != tt.wantErr {
				t.Errorf("generateWorkflowFile() error = %v, wantErr %v", err, tt.wantErr)
			}
			zipWriter.Close()
			if !tt.wantErr {

				zipReader, _ := zip.NewReader(bytes.NewReader(zipBuffer.Bytes()), int64(zipBuffer.Len()))
				if len(zipReader.File) != 2 {
					t.Errorf("generateWorkflowFile() want file 2,get %d", len(zipReader.File))
				}
				var excelFile *zip.File
				var jsonFile *zip.File
				for _, f := range zipReader.File {
					if strings.HasSuffix(f.Name, ".json") {
						jsonFile = f
					} else if strings.HasSuffix(f.Name, ".xlsx") {
						excelFile = f
					}
				}
				if excelFile == nil {
					t.Error("generateWorkflowFile() want excel file,but not found ")
				} else if excelFile.Name != "工作流程.xlsx" {
					t.Errorf("generateWorkflowFile() want :工作流程.xlsx,but get: %s ", excelFile.Name)
				}
				if jsonFile == nil {
					t.Error("generateWorkflowFile() want json file,but not found ")
				} else {
					f, _ := jsonFile.Open()
					body, _ := io.ReadAll(f)
					if string(body) != tt.args.mapWorkflow["flow1"].DialogJson {
						t.Errorf("generateWorkflowFile() want json content:%s,but get %s",
							tt.args.mapWorkflow["flow1"].DialogJson, string(body))
					}
				}
			}
		})

	}
}

func TestExportWorkflowScheduler_generateParamFile(t *testing.T) {
	type fields struct {
		task     task_scheduler.Task
		p        entity.WorkflowExportParams
		CosPath  string
		FileSize uint64
	}
	type args struct {
		mapParams map[string][]*entity.ParamMigrationInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				task:     task_scheduler.Task{},
				p:        entity.WorkflowExportParams{},
				CosPath:  "123",
				FileSize: 100,
			},
			args: args{
				mapParams: map[string][]*entity.ParamMigrationInfo{
					"flow1": {
						{
							NodeID:           "node1",
							ParamID:          "param1",
							ParamName:        "param1",
							ParamDesc:        "param1",
							ParamType:        "string",
							CorrectExample:   `["A,"B"]`,
							IncorrectExample: `["C","D"]`,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				ParamFileName: "参数.xlsx",
				ParamHead: []string{
					"工作流ID",
					"工作流节点ID",
					"参数ID",
					"参数名称",
					"参数描述",
					"参数正确示例",
					"参数错误示例"},
			},
		}
	})
	defer mockConfig.Reset()
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			etfs := &ExportWorkflowScheduler{
				task:     tt.fields.task,
				p:        tt.fields.p,
				CosPath:  tt.fields.CosPath,
				FileSize: tt.fields.FileSize,
			}
			if err := etfs.generateParamFile(context.Background(), zipWriter,
				tt.args.mapParams); (err != nil) != tt.wantErr {
				t.Errorf("generateWorkflowFile() error = %v, wantErr %v", err, tt.wantErr)
			}
			zipWriter.Close()
			if !tt.wantErr {

				zipReader, _ := zip.NewReader(bytes.NewReader(zipBuffer.Bytes()), int64(zipBuffer.Len()))
				if len(zipReader.File) != 1 {
					t.Errorf("generateWorkflowFile() want file 3,get %d", len(zipReader.File))
				}
				var excelFile *zip.File
				for _, f := range zipReader.File {
					if strings.HasSuffix(f.Name, ".xlsx") {
						excelFile = f
					}
				}
				if excelFile == nil {
					t.Error("generateWorkflowFile() want excel file,but not found ")
				} else if excelFile.Name != "参数.xlsx" {
					t.Errorf("generateWorkflowFile() want xlsx file name:参数.xlsx,but get %s ", excelFile.Name)
				}
			}
		})

	}
}

func TestExportWorkflowScheduler_generateWorkflowExampleFile(t *testing.T) {
	type fields struct {
		task     task_scheduler.Task
		p        entity.WorkflowExportParams
		CosPath  string
		FileSize uint64
	}
	type args struct {
		mapExample map[string][]*entity.ExportWorkflowExample
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				task:     task_scheduler.Task{},
				p:        entity.WorkflowExportParams{},
				CosPath:  "123",
				FileSize: 100,
			},
			args: args{
				mapExample: map[string][]*entity.ExportWorkflowExample{
					"flow1": {
						{
							FlowID:    "flow1",
							ExampleID: "ex1",
							Example:   "example",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				ExampleFileName: "示例问法.xlsx",
				ExampleHead: []string{
					"工作流ID",
					"示例问法ID",
					"示例问法内容",
				},
			},
		}
	})
	defer mockConfig.Reset()
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			etfs := &ExportWorkflowScheduler{
				task:     tt.fields.task,
				p:        tt.fields.p,
				CosPath:  tt.fields.CosPath,
				FileSize: tt.fields.FileSize,
			}
			if err := etfs.generateWorkflowExampleFile(context.Background(),
				zipWriter, tt.args.mapExample); (err != nil) != tt.wantErr {
				t.Errorf("generateWorkflowFile() error = %v, wantErr %v", err, tt.wantErr)
			}
			zipWriter.Close()
			if !tt.wantErr {

				zipReader, _ := zip.NewReader(bytes.NewReader(zipBuffer.Bytes()), int64(zipBuffer.Len()))
				if len(zipReader.File) != 1 {
					t.Errorf("generateWorkflowFile() want file 1,get %d", len(zipReader.File))
				}
				var excelFile *zip.File
				for _, f := range zipReader.File {
					if strings.HasSuffix(f.Name, ".xlsx") {
						excelFile = f
					}
				}
				if excelFile == nil {
					t.Error("generateWorkflowFile() want excel file,but not found ")
				} else if excelFile.Name != "示例问法.xlsx" {
					t.Errorf("generateWorkflowFile() want name:示例问法.xlsx,but get %s ", excelFile.Name)
				}
			}
		})

	}
}

func TestExportWorkflowScheduler_generateWorkflowVarFile(t *testing.T) {
	type fields struct {
		task     task_scheduler.Task
		p        entity.WorkflowExportParams
		CosPath  string
		FileSize uint64
	}
	type args struct {
		mapWorkflowVar map[string][]*entity.ExportWorkflowVar
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				task:     task_scheduler.Task{},
				p:        entity.WorkflowExportParams{},
				CosPath:  "123",
				FileSize: 100,
			},
			args: args{
				mapWorkflowVar: map[string][]*entity.ExportWorkflowVar{
					"flow1": {
						{
							FlowID:  "flow1",
							VarID:   "va1",
							VarName: "var",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				VarFileName: "变量.xlsx",
				VarHead: []string{
					"工作流ID",
					"示例问法ID",
					"示例问法内容",
				},
			},
		}
	})
	defer mockConfig.Reset()
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			etfs := &ExportWorkflowScheduler{
				task:     tt.fields.task,
				p:        tt.fields.p,
				CosPath:  tt.fields.CosPath,
				FileSize: tt.fields.FileSize,
			}
			if err := etfs.generateWorkflowVarFile(context.Background(),
				zipWriter, tt.args.mapWorkflowVar); (err != nil) != tt.wantErr {
				t.Errorf("generateWorkflowFile() error = %v, wantErr %v", err, tt.wantErr)
			}
			zipWriter.Close()
			if !tt.wantErr {

				zipReader, _ := zip.NewReader(bytes.NewReader(zipBuffer.Bytes()), int64(zipBuffer.Len()))
				if len(zipReader.File) != 1 {
					t.Errorf("generateWorkflowFile() want file 1,get %d", len(zipReader.File))
				}
				var excelFile *zip.File
				for _, f := range zipReader.File {
					if strings.HasSuffix(f.Name, ".xlsx") {
						excelFile = f
					}
				}
				if excelFile == nil {
					t.Error("generateWorkflowFile() want excel file,but not found ")
				} else if excelFile.Name != "变量.xlsx" {
					t.Errorf("generateWorkflowFile() want :变量.xlsx,but get %s ", excelFile.Name)
				}
			}
		})

	}
}
