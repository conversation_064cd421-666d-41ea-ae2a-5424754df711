// bot-task-config-server
//
// @(#)import_entry.go  星期三, 三月 06, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package bottask

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/slot"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ImportEntryTaskScheduler 导入词条流程调度任务
type ImportEntryTaskScheduler struct {
	task task_scheduler.Task
	p    entity.TaskFlowImportParentParams

	totalNum     int
	successNum   int
	conflictNum  int
	conflictInfo []ConflictEntry
}

func init() {
	task_scheduler.Register(
		entity.TaskImportEntryFlow,
		func(task task_scheduler.Task, params entity.TaskFlowImportParentParams) task_scheduler.TaskHandler {
			return &ImportEntryTaskScheduler{
				task: task,
				p:    params,
			}
		})
}

// Prepare 数据准备
func (iets *ImportEntryTaskScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (iets *ImportEntryTaskScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, iets.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportEntryTaskScheduler Init,sid:%s, ImportID:%s", util.RequestID(ctx), iets.p.ImportID)
	return nil
}

// Process 任务处理
func (iets *ImportEntryTaskScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ImportEntryTaskScheduler Process,sid:%s, ImportID:%s", sid, iets.p.ImportID)

	// 更改开始处理标志
	if err := db.UpdateTaskFlowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusWait,
		entity.FlowImportStatusProcessing); err != nil {
		return err
	}

	//查询任务流程导入信息
	taskInfo, err := db.GetTaskFlowImportByID(ctx, iets.p.ImportID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler GetTaskFlowImportByID Failed! sid:%s,err:%+v", sid, err)
		return err
	}

	// 解析出实体对应词条信息
	entryParams := &entity.ImportEntryParamsData{}
	if err := jsoniter.UnmarshalFromString(taskInfo.Params, entryParams); err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler unmarshalFailed! importID:%s,err:%+v",
			iets.p.ImportID, err)
		return err
	}

	// 判断taskFlow是否正在发布，发布时不允许增
	flowInfos, err := slot.CheckFlowStatusBySlotId(ctx, sid, taskInfo.RobotID, entryParams.SlotID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler flow is publishing! sid:%s,err:%+v", sid, err)
		return err
	}

	// 找到当前实体下的所有词条信息
	existEntries, err := db.GetEntryList(ctx, entryParams.EntityID, "")
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler getEntryList Failed! importID:%s,err:%+v",
			iets.p.ImportID, err)
		return err
	}

	// 对比、融合词条，更新词条库
	successNum, conflictNum, insertEntrySlice, updateEntrySlice, conflictInfo := compareEntryInfo(ctx, taskInfo.Uin,
		taskInfo.SubUin, existEntries, entryParams)
	log.InfoContextf(ctx, "ImportEntryTaskScheduler,successNum:%d,conflictNum:%d,insertSlice:%+v,"+
		"updateSlice:%+v,conflictInfo:%+v", successNum, conflictNum, insertEntrySlice, updateEntrySlice, conflictInfo)

	iets.totalNum = len(entryParams.Entries)
	iets.successNum = successNum
	iets.conflictNum = conflictNum
	iets.conflictInfo = conflictInfo

	// 以事务的方式更新与之相关的一系列表
	err = db.TXUpsertDeleteEntry(ctx, taskInfo.RobotID, entryParams.SlotID, entryParams.EntityID, insertEntrySlice,
		updateEntrySlice, nil, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler TXUpsertDeleteEntry Failed! sid:%s,err:%v", sid, err)
		return err
	}

	// 更新最终参数
	err = iets.updateEntryImportFinalParams(ctx, insertEntrySlice, updateEntrySlice)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler updateEntryImportFinalParams Failed! err:%v", err)
		return err
	}

	return nil
}

func (iets *ImportEntryTaskScheduler) updateEntryImportFinalParams(ctx context.Context, insertEntrySlice,
	updateEntrySlice []entity.Entry) error {
	// 最终参数
	var finalSlice []entity.Entry
	finalSlice = append(finalSlice, insertEntrySlice...)
	finalSlice = append(finalSlice, updateEntrySlice...)
	finalParams, err := jsoniter.MarshalToString(finalSlice)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler marshalData Failed data:%+v,err:%v", finalSlice, err)
		return err
	}
	err = db.UpdateEntryImportFinalParams(ctx, iets.p.ImportID, finalParams)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntryTaskScheduler UpdateEntryImportFinalParams Failed err:%v", err)
		return err
	}
	return nil
}

// Fail 任务失败
func (iets *ImportEntryTaskScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateTaskFlowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	return iets.sendImportEntryFinishNotice(ctx, false)

}

// Stop 任务停止
func (iets *ImportEntryTaskScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (iets *ImportEntryTaskScheduler) Done(ctx context.Context) error {
	if err := db.UpdateTaskFlowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	return iets.sendImportEntryFinishNotice(ctx, iets.successNum != 0)
}

func (iets *ImportEntryTaskScheduler) sendImportEntryFinishNotice(ctx context.Context, success bool) error {

	log.InfoContextf(ctx, "ImportEntryTaskScheduler sendImportEntryFinishNotice,iets:%+v,success:%t",
		iets, success)

	var noticeContent, noticeSubject, noticeLevel string
	if success {
		noticeContent = fmt.Sprintf(entity.EntryImportNoticeContent, iets.totalNum, iets.successNum, iets.conflictNum)
		noticeSubject = "词汇导入成功"
		noticeLevel = entity.LevelSuccess
	} else {
		noticeContent = fmt.Sprintf(entity.TaskFlowParentImportFailNoticeContent, iets.p.FileName)
		noticeSubject = "词汇导入失败"
		noticeLevel = entity.LevelError
	}

	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(iets.p.RobotID)),
		PageId:       entity.NoticeTaskFlowPageID,
		Type:         entity.NoticeTypeTaskFlowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(iets.p.ImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       iets.p.CorpID,
		StaffId:      iets.p.StaffID,
	}
	if len(iets.conflictInfo) > 0 {
		_ = iets.getEntryConflictFile(ctx, createNoticeReq)
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// ConflictEntry 词条冲突信息
type ConflictEntry struct {
	entity.EntryMigrationInfo
	ConflictMsg string `json:"string"` // 冲突信息
}

func compareEntryInfo(ctx context.Context, uin, subUin string, existEntries []*entity.Entry,
	newEntries *entity.ImportEntryParamsData) (successNum, conflictNum int, insertEntrySlice,
	updateEntrySlice []entity.Entry, conflictInfo []ConflictEntry) {
	entityID := newEntries.EntityID
	existEntryNameMap, existAliasNameMap, existEntryIDMap := buildEntryMapAliasMap(existEntries)
	log.InfoContextf(ctx, "ImportEntryTaskScheduler ,existEntryNameMap:%+v,existAliasNameMap:%+v,"+
		"existEntryIDMap:%+v", existEntryNameMap, existAliasNameMap, existEntryIDMap)
	maxAliasNum := config.GetMainConfig().Entry.MaxEntryAliasNum
	maxEntryNum := config.GetMainConfig().Entry.MaxEntryNum
	remainEntryNum := maxEntryNum - len(existEntries)
	for _, newEntry := range newEntries.Entries {
		var existEntryID string
		var conflictEntry ConflictEntry
		conflictEntry.EntryValue = newEntry.EntryValue
		conflictEntry.EntryAlias = newEntry.EntryAlias
		if len(newEntry.EntryAlias) > 0 { // 存在同义词
			dupName, sameEntry := getDepulicName(existEntryNameMap, existAliasNameMap, newEntry.EntryValue,
				newEntry.EntryAlias)
			if !sameEntry { // 重复名字不在同一个词条下面
				conflictNum++
				conflictEntry.ConflictMsg = fmt.Sprintf("与已有数据存在重复: %s", strings.Join(dupName, ","))
				conflictInfo = append(conflictInfo, conflictEntry)
				continue
			}
			if len(dupName) > 0 { // 如果有重复的名字存在，需要判断下首先出现的重复名字的entryID,作为词条融合的entryID
				if entryID3, ok3 := existAliasNameMap[dupName[0]]; ok3 {
					existEntryID = entryID3
				}
				if entryID4, ok4 := existEntryNameMap[dupName[0]]; ok4 {
					existEntryID = entryID4
				}
			} else {
				if entryID2, ok2 := existEntryNameMap[newEntry.EntryValue]; ok2 { // 没有重复的名字，看下词条名是否存在
					existEntryID = entryID2
				}
			}
			if existEntryID == "" {
				if len(insertEntrySlice) >= remainEntryNum {
					conflictNum++
					conflictEntry.ConflictMsg = fmt.Sprintf("词汇数量最多为：%d", maxEntryNum)
					conflictInfo = append(conflictInfo, conflictEntry)
				} else {
					successNum++
					insertEntrySlice = append(insertEntrySlice, buildImportEntry(uin, subUin, entityID,
						newEntry.EntryValue, newEntry.EntryAlias))
				}
			} else {
				successNum++
				// 插入数据与数据库中数据有重合，做词组合并操作; 词组合并: 以数据库中的词汇名为最终词汇名，其他的算作同义词做融合
				existEntryInfo := existEntryIDMap[existEntryID]
				var alias []string
				_ = jsoniter.UnmarshalFromString(existEntryInfo.EntryAlias, &alias)
				alias = append(alias, newEntry.EntryValue)
				mergedAlias := util.MergeSlice(alias, newEntry.EntryAlias, existEntryInfo.EntryValue, maxAliasNum)
				mergeAliasStr, _ := jsoniter.MarshalToString(mergedAlias)
				updateEntrySlice = append(updateEntrySlice, entity.Entry{EntityID: existEntryInfo.EntityID,
					EntryID: existEntryInfo.EntryID, EntryValue: existEntryInfo.EntryValue, EntryAlias: mergeAliasStr,
					ReleaseStatus: entity.ReleaseStatusUnPublished})
			}
		} else {
			if _, existEntry := existEntryNameMap[newEntry.EntryValue]; existEntry { // 只有词汇，不存在同义词
				successNum++
				continue
			}
			if _, existAlias := existAliasNameMap[newEntry.EntryValue]; existAlias {
				successNum++
				continue
			}
			if len(insertEntrySlice) >= remainEntryNum {
				conflictNum++
				conflictEntry.ConflictMsg = fmt.Sprintf("词汇数量最多为：%d", maxEntryNum)
				conflictInfo = append(conflictInfo, conflictEntry)
			} else {
				successNum++
				insertEntrySlice = append(insertEntrySlice, buildImportEntry(uin, subUin, entityID,
					newEntry.EntryValue, newEntry.EntryAlias))
			}
		}
	}
	return successNum, conflictNum, insertEntrySlice, updateEntrySlice, conflictInfo
}

func buildEntryMapAliasMap(existEntries []*entity.Entry) (map[string]string, map[string]string,
	map[string]entity.Entry) {
	existEntryNameMap := make(map[string]string, 0)
	existAliasNameMap := make(map[string]string, 0)
	existEntryIDMap := make(map[string]entity.Entry)

	for _, entry := range existEntries {
		existEntryIDMap[entry.EntryID] = *entry
		existEntryNameMap[entry.EntryValue] = entry.EntryID

		var alias []string
		err := jsoniter.UnmarshalFromString(entry.EntryAlias, &alias)
		if err != nil {
			fmt.Println(err)
		}
		for _, alia := range alias {
			existAliasNameMap[alia] = entry.EntryID
		}

	}
	return existEntryNameMap, existAliasNameMap, existEntryIDMap
}

func buildImportEntry(uin, subUIN, entityID, entryName string, entryAlias []string) entity.Entry {
	entryID := idgenerator.NewUUID()

	if len(entryAlias) == 0 {
		entryAlias = []string{}
	}
	entryAliasStr, _ := jsoniter.MarshalToString(entryAlias)

	entry := entity.Entry{
		EntryID:       entryID,
		EntityID:      entityID,
		EntryValue:    entryName,
		EntryAlias:    entryAliasStr,
		UIN:           uin,
		SubUIN:        subUIN,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}
	return entry
}

func (iets *ImportEntryTaskScheduler) getEntryConflictFile(ctx context.Context,
	createNoticeReq *pb.CreateNoticeReq) error {
	sid := util.RequestID(ctx)

	if len(iets.conflictInfo) == 0 {
		return nil
	}

	conflictInfos := iets.conflictInfo

	data := make([][]string, 0)
	data = append(data, []string{"词条（必填）", "同义词（选填）", "错误原因"})
	for _, conflict := range conflictInfos {
		data = append(data, []string{conflict.EntryValue, strings.Join(conflict.EntryAlias, "|"),
			conflict.ConflictMsg})
	}

	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		log.ErrorContextf(ctx, "getEntryConflictFile save2Xlsx Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	buff, err := xlFile.WriteToBuffer()
	if err != nil {
		log.ErrorContextf(ctx, "getEntryConflictFile xlFile.WriteToBuffer Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	// 上传cos文件
	filename := fmt.Sprintf("导入词条错误信息-%d.xlsx", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(iets.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, buff.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject failed! err:%+v", err)
		return err
	}
	// 加入通知
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeExportDownload,
		Params: &pb.CreateNoticeReq_Operation_Params{CosPath: cosPath},
	})

	return nil
}

func getDepulicName(existEntryNameMap, existAliasNameMap map[string]string, entryValue string, names []string) (
	[]string, bool) {

	entryIDS := make([]string, 0)
	sameNames := make([]string, 0)

	names = append(names, entryValue)
	for _, name := range names {
		if entryID, ok := existEntryNameMap[name]; ok {
			entryIDS = append(entryIDS, entryID)
			sameNames = append(sameNames, name)
		}
		if entryID, ok := existAliasNameMap[name]; ok {
			entryIDS = append(entryIDS, entryID)
			sameNames = append(sameNames, name)
		}
	}

	return sameNames, areElementsSameEntry(entryIDS)

}

func areElementsSameEntry(slice []string) bool {
	if len(slice) == 0 {
		return true
	}

	firstElement := slice[0]
	for _, element := range slice {
		if element != firstElement {
			return false
		}
	}

	return true
}
