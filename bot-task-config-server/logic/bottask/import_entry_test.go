// bot-task-config-server
//
// @(#)import_entry_test.go  星期二, 三月 19, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package bottask

import (
	"context"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"testing"
)

func Test_compareEntryInfo(t *testing.T) {
	config.GetMainConfigForUnitTest().Entry = config.Entry{
		MaxEntityNameLen:    10,
		MaxEntityExampleLen: 200,
		MaxEntityExampleNum: 1,
		MaxEntityDescLen:    10,
		MaxEntryNameLen:     10,
		MaxEntryNum:         10,
		MaxEntryAliasLen:    10,
		MaxEntryAliasNum:    20,
		EntryHead:           nil,
	}
	type args struct {
		ctx          context.Context
		uin          string
		subUin       string
		existEntries []*entity.Entry
		newEntries   *entity.ImportEntryParamsData
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "测试1",
			args: args{
				ctx:    context.Background(),
				uin:    "1",
				subUin: "1",
				existEntries: []*entity.Entry{
					{EntryID: "001", EntityID: "001", EntryValue: "A1", EntryAlias: `["A2","A3"]`},
					{EntryID: "002", EntityID: "002", EntryValue: "B1", EntryAlias: `["B2","B3"]`},
					{EntryID: "003", EntityID: "003", EntryValue: "C1", EntryAlias: `["C1"]`},
					{EntryID: "004", EntityID: "004", EntryValue: "D1", EntryAlias: `[]`}},
				newEntries: &entity.ImportEntryParamsData{
					SlotID:   "12535",
					EntityID: "132535",
					Entries: []*entity.EntryMigrationInfo{
						{EntryValue: "A1", EntryAlias: nil},
						{EntryValue: "A2", EntryAlias: nil},
						{EntryValue: "E1", EntryAlias: nil},
						{EntryValue: "A1", EntryAlias: []string{"A2", "A4"}},
						{EntryValue: "F1", EntryAlias: []string{"F2", "F3"}},
						{EntryValue: "G1 ", EntryAlias: []string{"A1"}},
						{EntryValue: "H1", EntryAlias: []string{"A2", "B2"}},
						{EntryValue: "H2", EntryAlias: []string{"A2", "B2"}},
						{EntryValue: "A2", EntryAlias: []string{"A1"}},
						{EntryValue: "A2", EntryAlias: []string{"A6"}},
						{EntryValue: "J1", EntryAlias: []string{"D1"}},
						{EntryValue: "M1", EntryAlias: []string{"D1", "L1"}},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, newEntry := range tt.args.newEntries.Entries {

				var tmpNewEntry entity.ImportEntryParamsData
				tmpNewEntry.SlotID = "12535"
				tmpNewEntry.EntityID = "12535"
				tmpNewEntry.Entries = []*entity.EntryMigrationInfo{newEntry}

				gotSuccessNum, gotConflictNum, gotInsertEntrySlice, gotUpdateEntrySlice, gotConflictInfo := compareEntryInfo(tt.args.ctx, tt.args.uin, tt.args.subUin, tt.args.existEntries, &tmpNewEntry)
				fmt.Printf("gotSuccessNum:%d,gotConflictNum:%d,gotInsertEntrySlice:%+v,gotUpdateEntrySlice:%+v,gotConflictInfo:%+v", gotSuccessNum, gotConflictNum, gotInsertEntrySlice, gotUpdateEntrySlice, gotConflictInfo)
			}
		})
	}
}
