package bottask

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ImportTaskFlowScheduler 导入任务流程父调度任务
type ImportTaskFlowScheduler struct {
	task task_scheduler.Task
	p    entity.TaskFlowImportParams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportTaskFlow,
		func(t task_scheduler.Task, params entity.TaskFlowImportParams) task_scheduler.TaskHandler {
			return &ImportTaskFlowScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (itfs *ImportTaskFlowScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (itfs *ImportTaskFlowScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, itfs.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportTaskFlow Init, ParentImportID:%s, ImportID:%s",
		itfs.p.ParentImportID, itfs.p.ImportID)
	return nil
}

// Process 任务处理
func (itfs *ImportTaskFlowScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	// 查询导入任务信息
	taskFlowImport, err := db.GetTaskFlowImportByID(ctx, itfs.p.ImportID)
	if err != nil {
		return err
	}
	if taskFlowImport.Status != entity.FlowImportStatusProcessing {
		return errors.ErrImportTaskFlowStatus
	}
	// 解析参数
	importParamData := &entity.TaskFlowImportParamsData{}
	if err := jsoniter.UnmarshalFromString(taskFlowImport.Params, importParamData); err != nil {
		log.ErrorContextf(ctx, "task flow import parse params importID:%s,err:%+v", itfs.p.ImportID, err)
		return err
	}
	// 保障同一时刻同一个机器人同一个任务流程名称导入唯一性
	importTaskFlowNameLockKey := entity.GetImportTaskFlowNameLockKey(itfs.p.RobotID,
		importParamData.TaskFlow.IntentName)
	ok, err := database.GetRedis().SetNX(ctx, importTaskFlowNameLockKey, itfs.p.ImportID,
		entity.ImportTaskFlowNameKeyExpTime).Result()
	if err != nil {
		log.ErrorContextf(ctx, "task flow import redis set nx importID:%s,err:%+v", itfs.p.ImportID, err)
		return err
	}
	defer func() { _ = database.GetRedis().Del(ctx, importTaskFlowNameLockKey) }()
	if !ok {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程名称同一时刻重复导入")
		log.WarnContextf(ctx, "task flow import intentname repeated importID:%s,err:%+v", itfs.p.ImportID, err)
		return errors.ErrTaskFlowNameDuplicated
	}
	// 检查任务流名称是否存在（防止任务执行阶段重复）
	tf, err := db.GetTaskFLowDetailsByName(ctx, "", itfs.p.RobotID, importParamData.TaskFlow.IntentName)
	if err != nil {
		return err
	}
	if tf != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程名称已经存在")
		log.ErrorContextf(ctx, "task flow import intentname existed importID:%s,err:%+v", itfs.p.ImportID, err)
		return errors.ErrTaskFlowNameDuplicated
	}
	return itfs.importTaskFlow(ctx, importParamData)
}

// Fail 任务失败
func (itfs *ImportTaskFlowScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateTaskFlowImportStatus(ctx, itfs.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	return itfs.checkImportResult(ctx)
}

// Stop 任务停止
func (itfs *ImportTaskFlowScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (itfs *ImportTaskFlowScheduler) Done(ctx context.Context) error {
	if err := db.UpdateTaskFlowImportStatus(ctx, itfs.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	return itfs.checkImportResult(ctx)
}

// importTaskFlow 导入任务流程
func (itfs *ImportTaskFlowScheduler) importTaskFlow(ctx context.Context,
	importParamData *entity.TaskFlowImportParamsData) error {
	sid := util.RequestID(ctx)
	// 一个槽位可能被多个任务流程引用，这里需要加锁串行处理，保证槽位不会被重复创建
	key := entity.GetImportTaskFlowLockKey(itfs.p.ParentImportID)
	locker := lock.NewDefaultLocker(key, itfs.p.ImportID, database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, true)
	if lockErr != nil {
		log.ErrorContextf(ctx, "importTaskFlow locker.Lock Failed, err:%v", lockErr)
		return lockErr
	}
	if !ok {
		lockErr = errors.ErrTaskFlowCopying
		log.WarnContextf(ctx, "importTaskFlow locker.Lock Failed, err:%v", lockErr)
		return lockErr
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "importTaskFlow locker.UnLock Fialed, err:%v", unLockErr)
		}
	}()

	// 组装 变量 信息
	varParams, err := itfs.fillCreateVarParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程导入变量数据异常")
		log.ErrorContextf(ctx, "sid:%s|importTaskFlow fillCreateVarParams Faield, importID:%s, err:%+v",
			sid, itfs.p.ImportID, err)
		return err
	}

	// 组装所需槽位信息
	slotParams, err := itfs.fillCreateSlotParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程导入槽位数据异常")
		log.ErrorContextf(ctx, "importTaskFlow fillCreateSlotParams Faield, importID:%s, err:%+v",
			itfs.p.ImportID, err)
		return err
	}
	// 组装任务流程信息
	createParams, err := itfs.fillCreateTaskFlowParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程导入数据异常")
		log.ErrorContextf(ctx, "sid:%s|importTaskFlow fillCreateTaskFlowParams Failed, importID:%s, err:%+v",
			sid, itfs.p.ImportID, err)
		return err
	}
	// 组装示例问法信息
	intentExampleParams, err := itfs.fillCreateIntentCorpusParams(ctx, importParamData, createParams.IntentID)
	if err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, "任务流程导入示例问法数据异常")
		log.ErrorContextf(ctx, "sid:%s|importTaskFlow fillCreateIntentCorpusParams Faield, importID:%s, err:%+v",
			sid, itfs.p.ImportID, err)
		return err
	}

	// 执行导入
	if err := db.ImportTaskFlow(ctx, importParamData, varParams, slotParams, intentExampleParams, createParams,
		itfs.p.RobotID, itfs.p.ImportID); err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfs.p.ImportID, fmt.Sprintf("错误：%s", err))
		log.ErrorContextf(ctx, "importTaskFlow ImportTaskFlow Failed, importID:%s, err:%+v",
			itfs.p.ImportID, err)
		return err
	}
	return nil
}

// checkImportResult
func (itfs *ImportTaskFlowScheduler) checkImportResult(ctx context.Context) error {
	parent, err := db.GetTaskFlowImportByID(ctx, itfs.p.ParentImportID)
	if err != nil {
		return err
	}
	// 考虑子任务存在失败后，手动重启的情况，此时父任务已经完成，不需要重新处理
	if parent.Status == entity.FlowImportStatusProcessed {
		return nil
	}
	taskFlowsImports, err := db.GetTaskFlowImportByParentID(ctx, itfs.p.ParentImportID)
	if err != nil {
		return err
	}
	importErrs := make([][]string, 0)
	total, success, fail := len(taskFlowsImports), 0, 0
	for _, v := range taskFlowsImports {
		switch v.Status {
		case entity.FlowImportStatusWait, entity.FlowImportStatusProcessing:
			return nil
		case entity.FlowImportStatusProcessed:
			success++
		case entity.FlowImportStatusProcessFail:
			paramData := &entity.TaskFlowImportParamsData{}
			_ = jsoniter.UnmarshalFromString(v.Params, paramData)
			importErrs = append(importErrs, []string{paramData.TaskFlow.IntentName, v.Message})
			fail++
		}
	}
	// 更新父任务状态为已处理
	if err := db.UpdateTaskFlowImportStatus(ctx, itfs.p.ParentImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	// 发送通知
	var noticeContent, noticeSubject, noticeLevel string
	if total == success {
		noticeContent = fmt.Sprintf(entity.TaskFlowImportSuccessNoticeContent, itfs.p.FileName)
		noticeSubject = "任务流程导入成功"
		noticeLevel = entity.LevelSuccess
		// 全部处理成功删除redis信息
		database.GetRedis().Del(ctx, entity.GetImportTaskFlowCategoryKey(itfs.p.ParentImportID))
	} else {
		noticeContent = fmt.Sprintf(entity.TaskFlowImportFailNoticeContent, itfs.p.FileName, total,
			success, fail)
		noticeSubject = "任务流程导入失败"
		noticeLevel = entity.LevelError
		// 有处理失败时redis数据没有进行清理：
		// 1. 存在手动重启的情况，此时删除redis数据会出问题，存在数据不匹配的问题
		// 2. 导入失败时不期望发生或者很少发生的情况，即使发生，redis中存储的也是相关的ID映射关系，数据量不多
		// 3. t_bot_task表有记录导入时候的数据，需要时可以根据导入的ID去redis删除具体的key
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(itfs.p.RobotID)),
		PageId:       entity.NoticeTaskFlowPageID,
		Type:         entity.NoticeTypeTaskFlowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(itfs.p.ParentImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       itfs.p.CorpID,
		StaffId:      itfs.p.StaffID,
	}
	_ = itfs.getImportErrFile(ctx, createNoticeReq, importErrs)
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// getCategoryID 获取最新分类ID
func (itfs *ImportTaskFlowScheduler) getCategoryID(ctx context.Context, categoryID string) (string, error) {
	result := database.GetRedis().HGet(ctx,
		entity.GetImportTaskFlowCategoryKey(itfs.p.ParentImportID), categoryID)
	return result.Result()
}

// getImportIntentCorpusParams 获取导入时存在的槽信息
func (itfs *ImportTaskFlowScheduler) getImportIntentCorpusParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) (map[string]*entity.IntentCorpus, error) {
	corpusExamples := make([]string, 0)
	for _, corpus := range paramData.IntentCorpus {
		corpusExamples = append(corpusExamples, corpus.Corpus)
	}
	mapCorpusExamples, err := db.GetImportIntentExampleInfos(ctx, itfs.p.RobotID, corpusExamples)
	if err != nil {
		return nil, err
	}
	return mapCorpusExamples, nil
}

// getImportSlotInfos 获取导入时存在的槽信息
func (itfs *ImportTaskFlowScheduler) getImportSlotInfos(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) (map[string]*entity.SlotMigrationInfo, error) {
	slotNames := make([]string, 0)
	for _, slot := range paramData.Slots {
		slotNames = append(slotNames, slot.SlotName)
	}
	slotInfoMap, err := db.GetImportSlotInfos(ctx, itfs.p.RobotID, slotNames)
	if err != nil {
		return nil, err
	}
	return slotInfoMap, nil
}

// getImportVarParams 获取导入时存在的变量信息
func (itfs *ImportTaskFlowScheduler) getImportVarParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) (map[string]*entity.VarParams, error) {
	varParams := make([]string, 0)
	for _, v := range paramData.Vars {
		varParams = append(varParams, v.VarName)
	}
	mapVarParams, err := db.GetImportVarParamsInfos(ctx, itfs.p.RobotID, varParams)
	if err != nil {
		return nil, err
	}
	return mapVarParams, nil
}

// fillCreateVarParams 填充需要导入的变量信息
func (itfs *ImportTaskFlowScheduler) fillCreateVarParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) ([]*entity.VarParams, error) {
	sid := util.RequestID(ctx)

	mapVarParams, err := itfs.getImportVarParams(ctx, paramData)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|fillCreateVarParams|err:%+v", sid, err)
		return nil, err
	}

	// 变量信息校验
	varParams := make([]*entity.VarParams, 0)
	for _, item := range paramData.Vars {
		var newVarID string
		existVarParamInfo, ok := mapVarParams[item.VarName]
		if ok {
			// 现有同名变量判断,如果同名，延用表中重名变量
			newVarID = existVarParamInfo.VarID // 表中存在的变量
			log.WarnContextf(ctx, "sid:%s|fillCreateVarParams VarParam Conflict, importVarID:%s, existVarID:%s",
				sid, item.VarID, newVarID)
		} else {
			// 新生成的 变量
			newVarID = idgenerator.NewUUID()
			varParam := &entity.VarParams{
				VarID:              newVarID,
				VarName:            item.VarName,
				VarDesc:            item.VarDesc,
				VarType:            item.VarType,
				VarDefaultValue:    item.VarDefaultValue,
				VarDefaultFileName: item.VarDefaultFileName,
				AppID:              itfs.p.RobotID,
				UIN:                paramData.Uin,
				SubUIN:             paramData.SubUin,
				ReleaseStatus:      entity.ReleaseStatusUnPublished,
				Action:             entity.ActionInsert,
			}
			varParams = append(varParams, varParam)
		}
		// 任务流Json VarID替换
		paramData.TaskFlow.DialogJson = strings.ReplaceAll(paramData.TaskFlow.DialogJson, item.VarID, newVarID)
		log.InfoContextf(ctx, "fillCreateVarParams,newVarID|%s|%s|%s", item.VarID, newVarID,
			paramData.TaskFlow.DialogJson)
		// 旧的变量替换
		item.VarID = newVarID
	}
	return varParams, nil
}

// fillCreateIntentCorpusParams 填充需要导入的示例问法信息
func (itfs *ImportTaskFlowScheduler) fillCreateIntentCorpusParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData, newIntentId string) ([]*entity.IntentCorpus, error) {
	// 查询现有示例问法信息
	slotIntentCorpusMap, err := itfs.getImportIntentCorpusParams(ctx, paramData)
	if err != nil {
		return nil, err
	}
	// 示例问法信息校验
	intentCorpusParams := make([]*entity.IntentCorpus, 0)
	for _, intentCorpus := range paramData.IntentCorpus {
		var newIntentCorpusId string

		existIntentCorpusInfo, ok := slotIntentCorpusMap[intentCorpus.Corpus]
		if ok {
			// 现有同名槽位判断
			// 槽位冲突 加一个后缀区分
			newIntentCorpusId = existIntentCorpusInfo.CorpusID + "_Conflict"
			log.WarnContextf(ctx, "fillCreateIntentCorpusParams IntentCorpusExample Conflict, "+
				"importCorpusID:%s, existCorpusID:%s", intentCorpus.CorpusID, newIntentCorpusId)
		} else {
			// 新生成的 示例用法
			intentCorpusParam := &entity.IntentCorpus{
				CorpusID:      idgenerator.NewUUID(),
				Corpus:        intentCorpus.Corpus,
				IntentID:      newIntentId,
				RobotId:       itfs.p.RobotID,
				Uin:           paramData.Uin,
				SubUin:        paramData.SubUin,
				ReleaseStatus: entity.ReleaseStatusUnPublished,
				Action:        entity.ActionInsert,
			}
			intentCorpusParams = append(intentCorpusParams, intentCorpusParam)
		}
	}
	return intentCorpusParams, nil
}

// getExitEntityEntriesMap 获取 EntityEntriesMap
func getExitEntityEntriesMap(entities []entity.EntityMigrationInfo) map[string]map[string]string {
	entitiesMap := make(map[string]map[string]string, 0)
	for _, v := range entities {
		entries := v.Entries
		entriesMap := make(map[string]string, 0)
		for _, e := range entries {
			entriesMap[e.EntryValue] = e.EntryID
		}
		entitiesMap[v.EntityName] = entriesMap
	}
	return entitiesMap
}

// fillCreateSlotParams 填充需要导入的槽位信息
func (itfs *ImportTaskFlowScheduler) fillCreateSlotParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) ([]*entity.SlotMigrationInfo, error) {
	// 查询现有槽位信息
	slotInfoMap, err := itfs.getImportSlotInfos(ctx, paramData)
	slotInfoMapStr, _ := jsoniter.MarshalToString(slotInfoMap)
	paramDataStr, _ := jsoniter.MarshalToString(paramData)
	log.InfoContextf(ctx, "fillCreateSlotParams|slotInfoMap:%+v|paramData|+%v", slotInfoMapStr, paramDataStr)
	if err != nil {
		return nil, err
	}
	// 槽位信息校验
	slotParams := make([]*entity.SlotMigrationInfo, 0)
	// todo 在该函数下面构建某个entity下的所有entryInfo， 用于同步到redis中
	for _, slot := range paramData.Slots {
		var newSlotID, newSlotEntityInfo string
		importSlotInfo, err := slot.ConvertToSlotMigrationInfo()
		if err != nil {
			return nil, err
		}
		existSlotInfo, ok := slotInfoMap[importSlotInfo.SlotName]
		if ok {
			// 现有同名槽位判断（包含实体、词条）
			log.InfoContextf(ctx, "fillCreateSlotParams Slot existSlotInfo:%v, importSlotInfo:%s",
				existSlotInfo, importSlotInfo)
			if existSlotInfo.IsEqual(importSlotInfo) {
				// 槽位复用
				newSlotID = existSlotInfo.SlotID
				newSlotEntityInfo, _ = jsoniter.MarshalToString(existSlotInfo.EntityInfo)
				log.InfoContextf(ctx, "fillCreateSlotParams Slot Reuse, importSlotID:%s, existSlotID:%s",
					importSlotInfo.SlotID, existSlotInfo.SlotID)

				// 词条复用
				exitEntityInfos := existSlotInfo.EntityInfo
				entitiesInfosMap := getExitEntityEntriesMap(exitEntityInfos)
				entityInfos := importSlotInfo.EntityInfo
				for i := range entityInfos {
					if entityInfos[i].EntityType == entity.SlotLevelSYS {
						// 系统实体无需新建
						continue
					} else {
						// 复用存在的词条
						for j := range entityInfos[i].Entries {
							newEntryId := entitiesInfosMap[entityInfos[i].EntityName][entityInfos[i].Entries[j].EntryValue]
							log.InfoContextf(ctx, "fillCreateSlotParams|oldEntryID:%s|newEntryId:%s",
								entityInfos[i].Entries[j].EntryID, newEntryId)
							paramData.TaskFlow.DialogJson = strings.ReplaceAll(paramData.TaskFlow.DialogJson,
								entityInfos[i].Entries[j].EntryID, newEntryId)
							// 任务流Json EntryId替换
							entityInfos[i].Entries[j].EntryID = newEntryId
						}
					}
				}

			} else {
				// 槽位冲突 加一个后缀区分 TODO mike 这里应该还有问题，待确认----
				newSlotID = importSlotInfo.SlotID + "_Conflict"
				newSlotEntityInfo, _ = jsoniter.MarshalToString(importSlotInfo.EntityInfo)
				log.WarnContextf(ctx, "fillCreateSlotParams Slot Conflict, importSlotID:%s, existSlotID:%s",
					importSlotInfo.SlotID, existSlotInfo.SlotID)
			}
		} else {
			// 新生成的槽位｜实体｜词条信息
			entityInfos := importSlotInfo.EntityInfo
			log.InfoContextf(ctx, "fillCreateSlotParams|entityInfos:%+v", entityInfos)
			for i := range entityInfos {
				if entityInfos[i].EntityType == entity.SlotLevelSYS {
					// 系统实体无需新建
					continue
				} else {
					// 自定义实体，需新建实体和词条；并最终统一替换任务流程中的实体和词条ID
					entityInfos[i].EntityID = idgenerator.NewUUID()
					for j := range entityInfos[i].Entries {
						newEntryId := idgenerator.NewUUID()
						log.InfoContextf(ctx, "fillCreateSlotParams|oldEntryID:%s|newEntryId:%s",
							entityInfos[i].Entries[j].EntryID, newEntryId)
						paramData.TaskFlow.DialogJson = strings.ReplaceAll(paramData.TaskFlow.DialogJson,
							entityInfos[i].Entries[j].EntryID, newEntryId)
						// 任务流Json EntryId替换
						entityInfos[i].Entries[j].EntryID = newEntryId
					}
				}
			}
			newSlotID = idgenerator.NewUUID()
			newSlotEntityInfo, _ = jsoniter.MarshalToString(entityInfos)
			// 组装槽位信息
			slotParam := &entity.SlotMigrationInfo{
				SlotID:       newSlotID,
				SlotName:     importSlotInfo.SlotName,
				SlotDesc:     importSlotInfo.SlotDesc,
				SlotExamples: importSlotInfo.SlotExamples,
				EntityInfo:   entityInfos,
			}
			slotParams = append(slotParams, slotParam)
		}
		// 任务流Json SlotID替换
		paramData.TaskFlow.DialogJson = strings.ReplaceAll(paramData.TaskFlow.DialogJson, slot.SlotID, newSlotID)
		// 旧槽位ID替换
		slot.SlotID = newSlotID
		// 旧实体信息替换
		slot.EntityInfo = newSlotEntityInfo
	}
	return slotParams, nil
}

// fillCreateTaskFlowParams
func (itfs *ImportTaskFlowScheduler) fillCreateTaskFlowParams(ctx context.Context,
	paramData *entity.TaskFlowImportParamsData) (*entity.CreateTaskFlowParams, error) {
	// 分类ID
	categoryID, err := itfs.getCategoryID(ctx, paramData.TaskFlow.CategoryID)
	if err != nil {
		return &entity.CreateTaskFlowParams{}, err
	}

	// 任务流ID
	taskFlowID := idgenerator.NewUUID()
	// 任务流Json FlowID替换
	paramData.TaskFlow.DialogJson = strings.ReplaceAll(
		paramData.TaskFlow.DialogJson, paramData.TaskFlow.FlowID, taskFlowID)

	// 组装任务流信息
	createParams := &entity.CreateTaskFlowParams{
		FlowID:          taskFlowID,
		IntentName:      paramData.TaskFlow.IntentName,
		IntentDesc:      paramData.TaskFlow.IntentDesc,
		IntentID:        idgenerator.NewUUID(),
		FlowState:       entity.FlowStateDraft, // 导入为草稿态
		FlowType:        entity.FlowTypeICS,
		IntentType:      paramData.Intent.IntentType,
		IntentSource:    paramData.Intent.Source,
		Version:         config.GetMainConfig().VerifyTaskFlow.Version,
		CategoryID:      categoryID,
		DialogJsonDraft: paramData.TaskFlow.DialogJson,
		Uin:             paramData.Uin,
		SubUin:          paramData.SubUin,
		ReleaseStatus:   entity.ReleaseStatusUnPublished,
		Action:          entity.ActionInsert,
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
		RobotId:         itfs.p.RobotID,
		CorpusID:        idgenerator.NewUUID(),
	}

	// 导入参数ID替换
	// 任务流程ID
	paramData.TaskFlow.FlowID = createParams.FlowID
	paramData.TaskFlow.IntentID = createParams.IntentID
	// 分类ID
	paramData.TaskFlow.CategoryID = createParams.CategoryID
	// 意图ID
	paramData.Intent.IntentID = createParams.IntentID
	// 语料意图ID
	for _, corpus := range paramData.Corpora {
		corpus.IntentID = createParams.IntentID
	}
	// 槽位意图ID
	for _, slot := range paramData.Slots {
		slot.IntentID = createParams.IntentID
	}
	// 示例问法意图ID
	for _, intentCorpus := range paramData.IntentCorpus {
		intentCorpus.IntentID = createParams.IntentID
	}
	// 变量
	for _, varItem := range paramData.Vars {
		varItem.IntentID = createParams.IntentID
	}
	return createParams, nil
}

func (itfs *ImportTaskFlowScheduler) getImportErrFile(ctx context.Context, createNoticeReq *pb.CreateNoticeReq,
	importErrs [][]string) error {
	if len(importErrs) == 0 {
		return nil
	}
	data := make([][]string, 0)
	data = append(data, []string{"任务流程名称", "错误原因"})
	data = append(data, importErrs...)
	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		log.ErrorContextf(ctx, "getImportErrFile save data to xlsx err:%+v", err)
		return err
	}
	buff, err := xlFile.WriteToBuffer()
	if err != nil {
		log.ErrorContextf(ctx, "getImportErrFile write to buffer err:%+v", err)
		return err
	}
	// 上传cos文件
	filename := fmt.Sprintf("导入任务流程错误信息-%d.xlsx", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(itfs.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, buff.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject failed! err:%+v", err)
		return err
	}
	// 加入通知
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeExportDownload,
		Params: &pb.CreateNoticeReq_Operation_Params{CosPath: cosPath},
	})
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeViewDetail,
		Params: &pb.CreateNoticeReq_Operation_Params{},
	})
	return nil
}
