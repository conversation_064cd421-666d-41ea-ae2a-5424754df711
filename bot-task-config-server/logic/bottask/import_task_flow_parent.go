package bottask

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ImportTaskFlowParentScheduler 导入任务流程父调度任务
type ImportTaskFlowParentScheduler struct {
	task task_scheduler.Task
	p    entity.TaskFlowImportParentParams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportTaskFlowParent,
		func(t task_scheduler.Task, params entity.TaskFlowImportParentParams) task_scheduler.TaskHandler {
			return &ImportTaskFlowParentScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (itfps *ImportTaskFlowParentScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (itfps *ImportTaskFlowParentScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, itfps.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportTaskFlowParent Init, ImportID:%s", itfps.p.ImportID)
	return nil
}

// Process 任务处理
func (itfps *ImportTaskFlowParentScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	// 查询任务流程导入信息
	parent, err := db.GetTaskFlowImportByID(ctx, itfps.p.ImportID)
	if err != nil {
		return err
	}
	if parent.Status != entity.FlowImportStatusProcessing {
		return errors.ErrImportTaskFlowStatus
	}
	// 解析出分类信息
	parentParamsData := &entity.TaskFlowImportParentParamsData{}
	if err := jsoniter.UnmarshalFromString(parent.Params, parentParamsData); err != nil {
		log.ErrorContextf(ctx, "task flow import parent parse params importID:%s,err:%+v", itfps.p.ImportID, err)
		return err
	}
	// 处理分类问题
	if err := itfps.updateCategory(ctx, parentParamsData); err != nil {
		_ = db.UpdateTaskFlowImportMessage(ctx, itfps.p.ImportID, fmt.Sprintf("错误：%s", err))
		log.ErrorContextf(ctx, "task flow import parent update category  importID:%s,err:%+v",
			itfps.p.ImportID, err)
		return err
	}
	// 创建任务流程子任务调度
	taskFlowImports, err := db.GetTaskFlowImportByParentID(ctx, parent.ImportID)
	if err != nil {
		log.ErrorContextf(ctx, "task flow import parent get imports importID:%s,err:%+v", itfps.p.ImportID, err)
		return err
	}
	for _, v := range taskFlowImports {
		if v.Status != entity.FlowImportStatusWait {
			continue
		}
		if err := db.CreateTaskFlowImportSub(ctx, itfps.p.CorpID, itfps.p.StaffID, itfps.p.RobotID,
			itfps.p.FileName, itfps.p.ImportID, v.ImportID); err != nil {
			return err
		}
	}
	return nil
}

// Fail 任务失败
func (itfps *ImportTaskFlowParentScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateTaskFlowImportStatus(ctx, itfps.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	// 发送通知
	noticeContent := fmt.Sprintf(entity.TaskFlowParentImportFailNoticeContent, itfps.p.FileName)
	noticeSubject := "任务流程导入失败"
	noticeLevel := entity.LevelError
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(itfps.p.RobotID)),
		PageId:       entity.NoticeTaskFlowPageID,
		Type:         entity.NoticeTypeTaskFlowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(itfps.p.ImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       itfps.p.CorpID,
		StaffId:      itfps.p.StaffID,
	}
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeViewDetail,
		Params: &pb.CreateNoticeReq_Operation_Params{},
	})
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// Stop 任务停止
func (itfps *ImportTaskFlowParentScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (itfps *ImportTaskFlowParentScheduler) Done(_ context.Context) error {
	return nil
}

// updateCategory
func (itfps *ImportTaskFlowParentScheduler) updateCategory(ctx context.Context,
	parentParamsData *entity.TaskFlowImportParentParamsData) error {
	currCategorys, err := db.GetCategorys(ctx, itfps.p.RobotID)
	if err != nil {
		return err
	}
	newCategorys := make([]*category.Category, 0)
	tree := category.BuildCateTree(currCategorys)
	for _, v := range parentParamsData.Categories {
		cates, err := itfps.getNewCategorys(ctx, tree, v, parentParamsData)
		if err != nil {
			return err
		}
		newCategorys = append(newCategorys, cates...)
	}

	// 导入参数ID替换
	for _, cg := range parentParamsData.Categories {
		newID, err := database.GetRedis().HGet(ctx,
			entity.GetImportTaskFlowCategoryKey(itfps.p.ImportID), cg.CategoryID).Result()
		if err != nil {
			return err
		}
		cg.CategoryID = newID
	}
	finalParams, _ := jsoniter.MarshalToString(parentParamsData)

	// 导入分类信息
	if err := db.ImportTaskFlowCategory(ctx, newCategorys, itfps.p.ImportID, finalParams); err != nil {
		return err
	}
	return nil
}

// fillNewCategorys
func (itfps *ImportTaskFlowParentScheduler) getNewCategorys(
	ctx context.Context, tree *category.CateNode, categoryRow *entity.CategoryRow,
	parentParamsData *entity.TaskFlowImportParentParamsData) ([]*category.Category, error) {
	newCategorys := tree.Create(categoryRow.CategoryNames)
	lastParentID := category.AllCateID
	var leafNodeID string
	if len(newCategorys) > 0 {
		lastParentID = newCategorys[0].ParentID
	}
	for i, v := range newCategorys {
		v.CategoryID = fmt.Sprintf("%d", idgenerator.NewInt64ID())
		v.ParentID = lastParentID
		v.RobotID = itfps.p.RobotID
		v.Feature = category.TCategoryFeature.Flow
		v.Uin = parentParamsData.Uin
		v.SubUin = parentParamsData.SubUin
		v.CreateTime = time.Now()
		v.UpdateTime = time.Now()
		lastParentID = v.CategoryID
		if i == len(newCategorys)-1 {
			leafNodeID = v.CategoryID
		}
	}
	if len(newCategorys) == 0 {
		leafNodeID = fmt.Sprintf("%d", tree.Find(categoryRow.CategoryNames))
	}
	// 更新到redis
	if err := database.GetRedis().HSet(ctx, entity.GetImportTaskFlowCategoryKey(itfps.p.ImportID),
		categoryRow.CategoryID, leafNodeID).Err(); err != nil {
		return nil, err
	}
	return newCategorys, nil
}
