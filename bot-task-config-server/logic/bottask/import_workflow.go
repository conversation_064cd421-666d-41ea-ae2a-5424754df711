package bottask

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ImportWorkflowScheduler 导入工作流程父调度任务
type ImportWorkflowScheduler struct {
	task task_scheduler.Task
	p    entity.WorkflowImportParams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportWorkflow,
		func(t task_scheduler.Task, params entity.WorkflowImportParams) task_scheduler.TaskHandler {
			return &ImportWorkflowScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (itfs *ImportWorkflowScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (itfs *ImportWorkflowScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, itfs.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportWorkflow Init, ParentImportID:%s, ImportID:%s",
		itfs.p.ParentImportID, itfs.p.ImportID)
	return nil
}

// Process 任务处理
func (itfs *ImportWorkflowScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	log.InfoContextf(ctx, "import_workflow process start")
	// 查询导入任务信息
	workflowImport, err := db.GetWorkflowImportByID(ctx, itfs.p.ImportID)
	if err != nil {
		return err
	}
	if workflowImport.Status != entity.FlowImportStatusProcessing {
		return errors.ErrImportWorkflowStatus
	}
	// 解析参数
	importParamData := &entity.WorkflowImportData{}
	if err := jsoniter.UnmarshalFromString(workflowImport.Params, importParamData); err != nil {
		log.ErrorContextf(ctx, "workflow import parse params importID:%s,err:%+v", itfs.p.ImportID, err)
		return err
	}
	// 保障同一时刻同一个机器人同一个工作流程名称导入唯一性
	importWorkflowNameLockKey := entity.GetImportWorkflowNameLockKey(itfs.p.RobotID,
		importParamData.WorkflowData.WorkflowName)
	ok, err := database.GetRedis().SetNX(ctx, importWorkflowNameLockKey, itfs.p.ImportID,
		entity.ImportWorkflowNameKeyExpTime).Result()
	if err != nil {
		log.ErrorContextf(ctx, "workflow import redis set nx importID:%s,err:%+v", itfs.p.ImportID, err)
		return err
	}
	defer func() { _ = database.GetRedis().Del(ctx, importWorkflowNameLockKey) }()
	if !ok {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程名称同一时刻重复导入")
		log.WarnContextf(ctx, "workflow import workflowName repeated importID:%s,err:%+v", itfs.p.ImportID, err)
		return errors.ErrWorkflowNameDuplicated
	}
	// 检查工作流名称是否存在（防止任务执行阶段重复）
	count, err := db.GetWorkFLowCountByName(ctx, "", itfs.p.RobotID, importParamData.WorkflowData.WorkflowName)
	if err != nil {
		return err
	}
	if count > 0 {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程名称已经存在")
		log.ErrorContextf(ctx, "workflow import workflowName existed importID:%s,err:%+v", itfs.p.ImportID, err)
		return errors.ErrWorkflowNameDuplicated
	}
	// v2.8.5 画布中节点里密钥需要加密存储
	importParamData.WorkflowData.DialogJson, err = protoutil.EncryptWorkflowJson(importParamData.WorkflowData.DialogJson)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程数据加密失败")
		log.ErrorContextf(ctx, "workflow import encrypt workflow dialogJson importID:%s,err:%+v",
			itfs.p.ImportID, err)
		return err
	}
	return itfs.importWorkflow(ctx, importParamData)
}

// Fail 任务失败
func (itfs *ImportWorkflowScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateWorkflowImportStatus(ctx, itfs.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	return itfs.checkImportResult(ctx)
}

// Stop 任务停止
func (itfs *ImportWorkflowScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (itfs *ImportWorkflowScheduler) Done(ctx context.Context) error {
	if err := db.UpdateWorkflowImportStatus(ctx, itfs.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	return itfs.checkImportResult(ctx)
}

// importWorkflow 导入工作流程
func (itfs *ImportWorkflowScheduler) importWorkflow(ctx context.Context,
	importParamData *entity.WorkflowImportData) error {
	sid := util.RequestID(ctx)
	varParams, varRefParams, paramParams, workflowRefParams, createParams, workflowRefPlugins, examples, workflowPDLs,
		err := itfs.ConstructImportWorkflow(ctx, importParamData, sid)
	if err != nil {
		return err
	}

	// 执行导入
	if err := db.ImportWorkflow(ctx, importParamData, varParams, varRefParams, paramParams, examples, createParams,
		workflowRefParams, workflowRefPlugins, workflowPDLs, itfs.p.RobotID, itfs.p.ImportID); err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, fmt.Sprintf("错误：%s", err))
		log.ErrorContextf(ctx, "importWorkflow ImportWorkflow Failed, importID:%s, err:%+v",
			itfs.p.ImportID, err)
		return err
	}
	//workflowSave := &KEP_WF.SaveWorkflowReq{
	//	WorkflowId:      createParams.WorkflowID,
	//	Name:            createParams.WorkflowName,
	//	AppBizId:        createParams.RobotId,
	//	DialogJson:      createParams.DialogJsonDraft,
	//	SaveType:        0,
	//	Desc:            createParams.WorkflowDesc,
	//	WorkflowVersion: createParams.Version,
	//}
	//_, err = saveImportWorkflow(ctx, workflowSave)
	//if err != nil {
	//	log.WarnContextf(ctx, "importWorkflow SaveWorkflow fail workflowName:%s,workflowId:%s",
	//		workflowSave.Name, workflowSave.WorkflowId)
	//}
	log.InfoContextf(ctx, "import_workflow process end")
	return nil
}

// ConstructImportWorkflow 组装导入数据
func (itfs *ImportWorkflowScheduler) ConstructImportWorkflow(ctx context.Context,
	importParamData *entity.WorkflowImportData, sid string) ([]*entity.VarParams, []*entity.WorkflowVar,
	[]*entity.ParamMigrationInfo, []*entity.WorkflowReference, *entity.CreateWorkflowParams,
	[]*entity.WorkflowRefPlugin, []*entity.WorkflowExample, []*entity.WorkflowPDL, error) {
	// 组装 变量 信息
	varParams, varRefParams, err := itfs.fillCreateVarParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入变量数据异常")
		log.ErrorContextf(ctx, "sid:%s|importWorkflow fillCreateVarParams Faield, importID:%s, err:%+v",
			sid, itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	}
	// 组装所需参数信息
	paramParams, err := itfs.fillCreateParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入参数数据异常")
		log.ErrorContextf(ctx, "importWorkflow fillCreateParams Faield, importID:%s, err:%+v",
			itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	}

	// 组装工作流引用信息
	workflowRefParams, err := itfs.fillCreateWorkflowRefs(ctx, importParamData)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入工作流引用工作流数据异常")
		log.ErrorContextf(ctx, "importWorkflow fillCreateWorkflowRefs failed, importID:%s, err:%+v",
			itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	}

	// 组装工作流程信息
	createParams, workflowRefPlugins, err := itfs.fillCreateWorkflowParams(ctx, importParamData)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入数据异常")
		log.ErrorContextf(ctx, "sid:%s|importWorkflow fillCreateWorkflowParams Failed, importID:%s, err:%+v",
			sid, itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	}

	// 组装示例问法信息
	examples, err := itfs.fillCreateWorkflowExampleParams(ctx, importParamData, createParams.WorkflowID)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入示例问法数据异常")
		log.ErrorContextf(ctx, "sid:%s|importWorkflow fillCreateWorkflowExampleParams failed,"+
			" importID:%s, err:%+v", sid, itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	} // 组装pdl信息
	workflowPdlList, err := itfs.fillCreateWorkflowPDLs(ctx, importParamData, createParams)
	if err != nil {
		_ = db.UpdateWorkflowImportMessage(ctx, itfs.p.ImportID, "工作流程导入示例问法数据异常")
		log.ErrorContextf(ctx, "sid:%s|importWorkflow fillCreateWorkflowExampleParams failed,"+
			" importID:%s, err:%+v", sid, itfs.p.ImportID, err)
		return nil, nil, nil, nil, nil, nil, nil, nil, err
	}
	return varParams, varRefParams, paramParams, workflowRefParams, createParams, workflowRefPlugins, examples,
		workflowPdlList, nil
}

// checkImportResult
func (itfs *ImportWorkflowScheduler) checkImportResult(ctx context.Context) error {
	parent, err := db.GetWorkflowImportByID(ctx, itfs.p.ParentImportID)
	if err != nil {
		return err
	}
	// 考虑子任务存在失败后，手动重启的情况，此时父任务已经完成，不需要重新处理
	if parent.Status == entity.FlowImportStatusProcessed {
		return nil
	}
	workflowsImports, err := db.GetWorkflowImportByParentID(ctx, itfs.p.ParentImportID)
	if err != nil {
		return err
	}
	importErrs := make([][]string, 0)
	total, success, fail := len(workflowsImports), 0, 0
	for _, v := range workflowsImports {
		switch v.Status {
		case entity.FlowImportStatusWait, entity.FlowImportStatusProcessing:
			return nil
		case entity.FlowImportStatusProcessed:
			success++
		case entity.FlowImportStatusProcessFail:
			paramData := &entity.WorkflowImportData{}
			_ = jsoniter.UnmarshalFromString(v.Params, paramData)
			importErrs = append(importErrs, []string{paramData.WorkflowData.WorkflowName, v.Message})
			fail++
		}
	}
	// 更新父任务状态为已处理
	if err := db.UpdateWorkflowImportStatus(ctx, itfs.p.ParentImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	// 发送通知
	var noticeContent, noticeSubject, noticeLevel string
	if total == success {
		noticeContent = fmt.Sprintf(entity.WorkflowImportSuccessNoticeContent, itfs.p.FileName)
		noticeSubject = "工作流程导入成功"
		noticeLevel = entity.LevelSuccess
	} else {
		noticeContent = fmt.Sprintf(entity.WorkflowImportFailNoticeContent, itfs.p.FileName, total,
			success, fail)
		noticeSubject = "工作流程导入失败"
		noticeLevel = entity.LevelError
		// 有处理失败时redis数据没有进行清理：
		// 1. 存在手动重启的情况，此时删除redis数据会出问题，存在数据不匹配的问题
		// 2. 导入失败时不期望发生或者很少发生的情况，即使发生，redis中存储的也是相关的ID映射关系，数据量不多
		// 3. t_bot_task表有记录导入时候的数据，需要时可以根据导入的ID去redis删除具体的key
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(itfs.p.RobotID)),
		PageId:       entity.NoticeWorkflowPageID,
		Type:         entity.NoticeTypeWorkflowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(itfs.p.ParentImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       itfs.p.CorpID,
		StaffId:      itfs.p.StaffID,
	}
	_ = itfs.getImportErrFile(ctx, createNoticeReq, importErrs)
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// getImportVarParams 获取导入时存在的变量信息
func (itfs *ImportWorkflowScheduler) getImportVarParams(ctx context.Context,
	paramData *entity.WorkflowImportData) (map[string]*entity.VarParams, error) {
	varParams := make([]string, 0)
	for _, v := range paramData.Vars {
		varParams = append(varParams, v.VarName)
	}
	mapVarParams, err := db.GetImportVarParamsInfos(ctx, itfs.p.RobotID, varParams)
	if err != nil {
		return nil, err
	}
	return mapVarParams, nil
}

// fillCreateVarParams 填充需要导入的变量信息
func (itfs *ImportWorkflowScheduler) fillCreateVarParams(ctx context.Context,
	paramData *entity.WorkflowImportData) ([]*entity.VarParams, []*entity.WorkflowVar, error) {
	sid := util.RequestID(ctx)
	newWorkflowID := paramData.Old2New[paramData.WorkflowData.WorkflowID]
	mapVarParams, err := itfs.getImportVarParams(ctx, paramData)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|fillCreateVarParams|err:%+v", sid, err)
		return nil, nil, err
	}

	// 变量信息校验
	varParams := make([]*entity.VarParams, 0)
	varRefParams := make([]*entity.WorkflowVar, 0)
	old2new := make(map[string]string)
	for _, item := range paramData.Vars {
		var newVarID string
		existVarParamInfo, ok := mapVarParams[item.VarName]
		if ok {
			// 现有同名变量判断,如果同名，延用表中重名变量
			newVarID = existVarParamInfo.VarID // 表中存在的变量
			log.WarnContextf(ctx, "sid:%s|fillCreateVarParams VarParam Conflict, importVarID:%s, existVarID:%s",
				sid, item.VarID, newVarID)
		} else {
			// 新生成的 变量
			newVarID = idgenerator.NewUUID()
			varParam := &entity.VarParams{
				VarID:              newVarID,
				VarName:            item.VarName,
				VarDesc:            item.VarDesc,
				VarType:            item.VarType,
				VarDefaultValue:    item.VarDefaultValue,
				VarDefaultFileName: item.VarDefaultFileName,
				AppID:              itfs.p.RobotID,
				UIN:                paramData.Uin,
				SubUIN:             paramData.SubUin,
				ReleaseStatus:      entity.ReleaseStatusUnPublished,
				Action:             entity.ActionInsert,
			}
			varParams = append(varParams, varParam)
		}
		// 工作流Json VarID替换
		paramData.WorkflowData.DialogJson = strings.ReplaceAll(paramData.WorkflowData.DialogJson, item.VarID, newVarID)
		log.InfoContextf(ctx, "fillCreateVarParams,newVarID|%s|%s|%s", item.VarID, newVarID,
			paramData.WorkflowData.DialogJson)
		for _, workflowPDL := range paramData.WorkflowPDLs {
			//workflowPDL.DialogJson = strings.ReplaceAll(workflowPDL.DialogJson, item.VarID, newVarID)
			//log.InfoContextf(ctx, "fillCreateVarParams,newVarID|%s|%s|%s", item.VarID, newVarID,
			//	workflowPDL.DialogJson)
			workflowPDL.ToolJson = strings.ReplaceAll(workflowPDL.ToolJson, item.VarID, newVarID)
			log.InfoContextf(ctx, "fillCreateVarParams,newVarID|%s|%s|%s", item.VarID, newVarID,
				workflowPDL.ToolJson)
		}
		// 旧的变量替换
		old2new[item.VarID] = newVarID
		item.VarID = newVarID
		varRefParams = append(varRefParams, &entity.WorkflowVar{
			WorkflowID:    newWorkflowID,
			VarID:         newVarID,
			RobotID:       itfs.p.RobotID,
			ReleaseStatus: entity.ReleaseStatusUnPublished,
			Action:        entity.ActionInsert,
			IsDeleted:     0,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		})
	}
	return varParams, varRefParams, nil
}

// fillCreateWorkflowExampleParams 填充需要导入的示例问法信息
func (itfs *ImportWorkflowScheduler) fillCreateWorkflowExampleParams(ctx context.Context,
	paramData *entity.WorkflowImportData, newFlowID string) ([]*entity.WorkflowExample, error) {

	// 示例问法信息校验
	examples := make([]*entity.WorkflowExample, 0)
	for _, example := range paramData.Examples {
		// 新生成的 示例用法
		intentCorpusParam := &entity.WorkflowExample{
			ExampleID:     idgenerator.NewUUID(),
			Example:       example.Example,
			FlowID:        newFlowID,
			RobotId:       itfs.p.RobotID,
			Uin:           paramData.Uin,
			SubUin:        paramData.SubUin,
			ReleaseStatus: entity.ReleaseStatusUnPublished,
			Action:        entity.ActionInsert,
		}
		examples = append(examples, intentCorpusParam)
	}
	return examples, nil
}

// fillCreateWorkflowPDLs 填充需要导入的pdl信息
func (itfs *ImportWorkflowScheduler) fillCreateWorkflowPDLs(ctx context.Context,
	paramData *entity.WorkflowImportData, createWorkflow *entity.CreateWorkflowParams) ([]*entity.WorkflowPDL, error) {
	PDLList := make([]*entity.WorkflowPDL, 0)
	for _, PDL := range paramData.WorkflowPDLs {
		PDL.DialogJson = createWorkflow.DialogJsonDraft
		// 新生成的pdl
		workflowPDL := &entity.WorkflowPDL{
			PdlID:              idgenerator.NewUUID(),
			PdlSnapshotVersion: 1,
			WorkflowID:         createWorkflow.WorkflowID,
			WorkflowName:       createWorkflow.WorkflowName,
			WorkflowState:      entity.WorkflowPdlStateDraft,
			Version:            1,
			RobotId:            itfs.p.RobotID,
			DialogJsonEnable:   PDL.DialogJson,
			Parameter:          PDL.ParameterJson,
			PdlContent:         PDL.ContentJson,
			ToolsInfo:          PDL.ToolJson,
			UserConstraints:    PDL.Constraints,
			StaffID:            itfs.p.StaffID,
			ReleaseStatus:      entity.WorkflowPdlReleaseStatusUnPublished,
			IsDeleted:          0,
			IsEnable:           false,
			Action:             entity.ActionInsert,
			PdlCreateTime:      time.Now(),
			CreateTime:         time.Now(),
			UpdateTime:         time.Now(),
		}
		PDLList = append(PDLList, workflowPDL)
	}
	return PDLList, nil
}

// fillCreateParams 填充需要导入的参数信息
func (itfs *ImportWorkflowScheduler) fillCreateParams(ctx context.Context,
	paramData *entity.WorkflowImportData) ([]*entity.ParamMigrationInfo, error) {
	// 参数信息替换
	for i := range paramData.Params {
		log.InfoContextf(ctx, "fillCreateParams|entityInfos:%+v", paramData.Params[i])
		oldID := paramData.Params[i].ParamID
		paramData.Params[i].ParamID = idgenerator.NewUUID()
		// 工作流Json ParamID替换
		paramData.WorkflowData.DialogJson = strings.ReplaceAll(paramData.WorkflowData.DialogJson,
			oldID, paramData.Params[i].ParamID)
		for _, workflowPDL := range paramData.WorkflowPDLs {
			//// 工作流pdlJson ParamID替换
			//workflowPDL.DialogJson = strings.ReplaceAll(workflowPDL.DialogJson,
			//	oldID, paramData.Params[i].ParamID)
			// 工作流pdlParamJson ParamID替换
			workflowPDL.ParameterJson = strings.ReplaceAll(workflowPDL.ParameterJson,
				oldID, paramData.Params[i].ParamID)
			// 工作流pdlToolJson ParamID替换
			workflowPDL.ToolJson = strings.ReplaceAll(workflowPDL.ToolJson,
				oldID, paramData.Params[i].ParamID)
		}
	}
	return paramData.Params, nil
}

// fillCreateWorkflowRefs 填充需要导入的工作流引用
func (itfs *ImportWorkflowScheduler) fillCreateWorkflowRefs(ctx context.Context,
	importParamData *entity.WorkflowImportData) ([]*entity.WorkflowReference, error) {
	workflowRefs := make([]*entity.WorkflowReference, 0)
	// 参数信息替换
	for i := range importParamData.WorkflowRefs {
		log.InfoContextf(ctx, "fillCreateWorkflowRefs workflowRef:%+v", importParamData.WorkflowRefs[i])
		log.InfoContextf(ctx, "fillCreateWorkflowRefs workflowRef Old2New:%+v", importParamData.Old2New)
		// 工作流引用文件中的ID替换成最终要生成的
		importParamData.WorkflowRefs[i].WorkflowID = importParamData.Old2New[importParamData.WorkflowRefs[i].WorkflowID]
		// 从文件中创建的需要替换；数据库存在的在前面解析导入文件的时候已经替换了
		newRefID, ok := importParamData.Old2New[importParamData.WorkflowRefs[i].WorkflowRefNewID]
		if ok {
			importParamData.WorkflowRefs[i].WorkflowRefNewID = newRefID
		}
		log.InfoContextf(ctx, "fillCreateWorkflowRefs workflowRef new:%+v", importParamData.WorkflowRefs[i])
		// 工作流Json ParamID替换
		importParamData.WorkflowData.DialogJson = strings.ReplaceAll(importParamData.WorkflowData.DialogJson,
			importParamData.WorkflowRefs[i].WorkflowRefID, importParamData.WorkflowRefs[i].WorkflowRefNewID)
		//for _, workflowPDL := range importParamData.WorkflowPDLs {
		//	workflowPDL.DialogJson = strings.ReplaceAll(workflowPDL.DialogJson,
		//		importParamData.WorkflowRefs[i].WorkflowRefID, importParamData.WorkflowRefs[i].WorkflowRefNewID)
		//}
		log.InfoContextf(ctx, "fillCreateWorkflowRefs DialogJson|%s|%s|%s",
			importParamData.WorkflowRefs[i].WorkflowRefID,
			importParamData.WorkflowRefs[i].WorkflowRefNewID,
			importParamData.WorkflowData.DialogJson)
		workflowRefs = append(workflowRefs, &entity.WorkflowReference{
			WorkflowID:    importParamData.WorkflowRefs[i].WorkflowID,
			NodeID:        importParamData.WorkflowRefs[i].NodeID,
			WorkflowRefID: importParamData.WorkflowRefs[i].WorkflowRefNewID,
			RobotID:       itfs.p.RobotID,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		})
	}
	return workflowRefs, nil
}

// fillCreateWorkflowParams
func (itfs *ImportWorkflowScheduler) fillCreateWorkflowParams(ctx context.Context,
	paramData *entity.WorkflowImportData) (*entity.CreateWorkflowParams, []*entity.WorkflowRefPlugin, error) {
	workflowRefPlugins := make([]*entity.WorkflowRefPlugin, 0)
	// 工作流ID
	workflowID := paramData.Old2New[paramData.WorkflowData.WorkflowID]
	tree, err := protoutil.JsonToWorkflow(paramData.WorkflowData.DialogJson)
	if err != nil {
		log.Errorf("fillCreateParams|JsonToWorkflow err:%v", err)
		return nil, workflowRefPlugins, err
	}
	if paramData.WorkflowName != "" {
		tree.WorkflowName = paramData.WorkflowName
		paramData.WorkflowData.WorkflowName = paramData.WorkflowName
	}
	if paramData.WorkflowDesc != "" {
		tree.WorkflowDesc = paramData.WorkflowDesc
		paramData.WorkflowData.WorkflowDesc = paramData.WorkflowDesc
	}
	tree.WorkflowID = workflowID
	for i := range tree.Nodes {
		switch tree.Nodes[i].NodeType {
		case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
			data, ok := tree.Nodes[i].NodeData.(*KEP_WF.WorkflowNode_KnowledgeRetrieverNodeData)
			if ok { // 导入时，查看导入的文档ID是否在新的应用下存在，不存在的移除（标签默认要移除）
				itfs.ReplaceKnowledgeRetrieverNodeDocument(ctx, data)
				tree.Nodes[i].NodeData = data
			}
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
			data, ok := tree.Nodes[i].NodeData.(*KEP_WF.WorkflowNode_LLMKnowledgeQANodeData)
			if ok { // 导入时，移除引用的知识（新应用中并没有原有的知识）（标签默认要移除）
				itfs.ReplaceKnowledgeQANodeDocument(ctx, data)
				tree.Nodes[i].NodeData = data
			}
		//case KEP_WF.NodeType_TOOL:
		//	data, ok := tree.Nodes[i].NodeData.(*KEP_WF.WorkflowNode_ToolNodeData)
		//	if ok {
		//		if len(data.ToolNodeData.API.WorkFlowID) > 0 { //替换引用的工作流id
		//			data.ToolNodeData.API.WorkFlowID = paramData.Old2New[data.ToolNodeData.API.WorkFlowID]
		//		}
		//	}
		case KEP_WF.NodeType_PLUGIN:
			data, ok := tree.Nodes[i].NodeData.(*KEP_WF.WorkflowNode_PluginNodeData)
			if ok {
				if data.PluginNodeData.ToolID != "" && data.PluginNodeData.PluginID != "" {
					workflowRefPlugins = append(workflowRefPlugins, &entity.WorkflowRefPlugin{
						WorkflowID: workflowID,
						NodeID:     tree.Nodes[i].NodeID,
						PluginType: data.PluginNodeData.PluginType.String(),
						PluginID:   data.PluginNodeData.PluginID,
						ToolID:     data.PluginNodeData.ToolID,
						RobotID:    itfs.p.RobotID,
						IsDeleted:  0,
						CreateTime: time.Now(),
						UpdateTime: time.Now(),
					})
				}
			}
		}

	}
	paramData.WorkflowData.DialogJson, err = protoutil.WorkflowToJson(tree)
	if err != nil {
		log.Errorf("WfillCreateParams|orkflowToJson err:%v", err)
		return nil, workflowRefPlugins, err
	}
	// 组装工作流信息
	createParams := &entity.CreateWorkflowParams{
		WorkflowID:      workflowID,
		WorkflowName:    paramData.WorkflowData.WorkflowName,
		WorkflowDesc:    paramData.WorkflowData.WorkflowDesc,
		WorkflowState:   entity.FlowStateDraft,
		Version:         1,
		DialogJsonDraft: paramData.WorkflowData.DialogJson,
		Uin:             paramData.Uin,
		SubUin:          paramData.SubUin,
		StaffID:         itfs.p.StaffID,
		ReleaseStatus:   entity.ReleaseStatusUnPublished,
		Action:          entity.ActionInsert,
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
		RobotId:         itfs.p.RobotID,
	}

	// 导入参数ID替换
	// 工作流程ID
	paramData.WorkflowData.WorkflowID = createParams.WorkflowID
	// 示例问法的工作流ID
	for _, example := range paramData.Examples {
		example.FlowID = createParams.WorkflowID
	}
	// 参数的工作流ID
	for _, pamram := range paramData.Params {
		pamram.FlowID = createParams.WorkflowID
	}
	// 变量
	for _, varItem := range paramData.Vars {
		varItem.FlowID = createParams.WorkflowID
	}
	// pdl

	return createParams, workflowRefPlugins, nil
}

func (itfs *ImportWorkflowScheduler) getImportErrFile(ctx context.Context, createNoticeReq *pb.CreateNoticeReq,
	importErrs [][]string) error {
	if len(importErrs) == 0 {
		return nil
	}
	data := make([][]string, 0)
	data = append(data, []string{"工作流程名称", "错误原因"})
	data = append(data, importErrs...)
	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		log.ErrorContextf(ctx, "getImportErrFile save data to xlsx err:%+v", err)
		return err
	}
	buff, err := xlFile.WriteToBuffer()
	if err != nil {
		log.ErrorContextf(ctx, "getImportErrFile write to buffer err:%+v", err)
		return err
	}
	// 上传cos文件
	filename := fmt.Sprintf("导入工作流程错误信息-%d.xlsx", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(itfs.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, buff.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject failed! err:%+v", err)
		return err
	}
	// 加入通知
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeExportDownload,
		Params: &pb.CreateNoticeReq_Operation_Params{CosPath: cosPath},
	})
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeViewDetail,
		Params: &pb.CreateNoticeReq_Operation_Params{},
	})
	return nil
}

func SetImportWorkflow(importWorkflow ImportWorkflowScheduler, p entity.WorkflowImportParams) ImportWorkflowScheduler {
	importWorkflow.p = p
	return importWorkflow
}

func (itfs *ImportWorkflowScheduler) ReplaceKnowledgeRetrieverNodeDocument(ctx context.Context,
	data *KEP_WF.WorkflowNode_KnowledgeRetrieverNodeData) {
	docBizIDs := make([]string, 0)
	if len(data.KnowledgeRetrieverNodeData.DocBizIDs) > 0 {
		InnerDescribeDocsReq := &bot_knowledge_config_server.InnerDescribeDocsReq{
			BotBizId:  itfs.p.RobotID,
			DocBizIds: data.KnowledgeRetrieverNodeData.DocBizIDs,
		}
		rsp, err := rpc.BatchQueryDocuments(ctx, InnerDescribeDocsReq)
		if err != nil {
			log.WarnContextf(ctx, "fillCreateWorkflowParams BatchQueryDocuments Failed!:%s",
				err.Error())
		} else {
			for _, doc := range rsp.Docs {
				docBizIDs = append(docBizIDs, strconv.FormatUint(doc.DocBizId, 10))
			}
		}
	}
	data.KnowledgeRetrieverNodeData.DocBizIDs = docBizIDs
	data.KnowledgeRetrieverNodeData.Labels = nil
	// todo(hanlynnke): 需要bot-knowledge-config-server提供批量查询文档和文档分类的接口。目前仅处理知识库下的文档，文档分类和标签都会被清空
	if !data.KnowledgeRetrieverNodeData.AllKnowledge && len(data.KnowledgeRetrieverNodeData.GetKnowledgeList()) > 0 {
		// todo(hanlynnke): 需要bot-knowledge-config-server提供查询应用绑定的共享知识库列表的内部接口
		for i, knowledge := range data.KnowledgeRetrieverNodeData.GetKnowledgeList() {
			// 共享知识库 或 默认知识库对应的ID不是导入的应用ID，直接清空
			if knowledge.GetKnowledgeType() == KEP_WF.Knowledge_SHARED ||
				(knowledge.GetKnowledgeType() == KEP_WF.Knowledge_DEFAULT && knowledge.GetKnowledgeBizID() != itfs.p.RobotID) {
				data.KnowledgeRetrieverNodeData.KnowledgeList[i].DocBizIDs = nil
				data.KnowledgeRetrieverNodeData.KnowledgeList[i].CateBizIDs = nil
				data.KnowledgeRetrieverNodeData.KnowledgeList[i].Labels = nil
				continue
			}
			knowledgeDocBizIDs := make([]string, 0)
			InnerDescribeDocsReq := &bot_knowledge_config_server.InnerDescribeDocsReq{
				BotBizId:  knowledge.GetKnowledgeBizID(),
				DocBizIds: knowledge.GetDocBizIDs(),
			}
			rsp, err := rpc.BatchQueryDocuments(ctx, InnerDescribeDocsReq)
			if err != nil {
				log.WarnContextf(ctx, "fillCreateWorkflowParams BatchQueryDocuments Failed!:%s",
					err.Error())
			} else {
				for _, doc := range rsp.Docs {
					knowledgeDocBizIDs = append(knowledgeDocBizIDs, strconv.FormatUint(doc.DocBizId, 10))
				}
			}
			data.KnowledgeRetrieverNodeData.KnowledgeList[i].DocBizIDs = knowledgeDocBizIDs
			data.KnowledgeRetrieverNodeData.KnowledgeList[i].CateBizIDs = nil
			data.KnowledgeRetrieverNodeData.KnowledgeList[i].Labels = nil
		}
	}
}

func (itfs *ImportWorkflowScheduler) ReplaceKnowledgeQANodeDocument(ctx context.Context,
	data *KEP_WF.WorkflowNode_LLMKnowledgeQANodeData) {
	docBizIDs := make([]string, 0)
	if len(data.LLMKnowledgeQANodeData.DocBizIDs) > 0 {
		InnerDescribeDocsReq := &bot_knowledge_config_server.InnerDescribeDocsReq{
			BotBizId:  itfs.p.RobotID,
			DocBizIds: data.LLMKnowledgeQANodeData.DocBizIDs,
		}
		rsp, err := rpc.BatchQueryDocuments(ctx, InnerDescribeDocsReq)
		if err != nil {
			log.WarnContextf(ctx, "fillCreateWorkflowParams BatchQueryDocuments Failed!:%s",
				err.Error())
		} else {
			for _, doc := range rsp.Docs {
				docBizIDs = append(docBizIDs, strconv.FormatUint(doc.DocBizId, 10))
			}
		}
	}
	data.LLMKnowledgeQANodeData.DocBizIDs = docBizIDs
	data.LLMKnowledgeQANodeData.Labels = nil
	// todo(hanlynnke): 需要bot-knowledge-config-server提供批量查询文档和文档分类的接口。目前仅处理知识库下的文档，文档分类和标签都会被清空
	if !data.LLMKnowledgeQANodeData.AllKnowledge && len(data.LLMKnowledgeQANodeData.GetKnowledgeList()) > 0 {
		// todo(hanlynnke): 需要bot-knowledge-config-server提供查询应用绑定的共享知识库列表的内部接口
		for i, knowledge := range data.LLMKnowledgeQANodeData.GetKnowledgeList() {
			// 共享知识库 或 默认知识库对应的ID不是导入的应用ID，直接清空
			if knowledge.GetKnowledgeType() == KEP_WF.Knowledge_SHARED ||
				(knowledge.GetKnowledgeType() == KEP_WF.Knowledge_DEFAULT && knowledge.GetKnowledgeBizID() != itfs.p.RobotID) {
				data.LLMKnowledgeQANodeData.KnowledgeList[i].DocBizIDs = nil
				data.LLMKnowledgeQANodeData.KnowledgeList[i].CateBizIDs = nil
				data.LLMKnowledgeQANodeData.KnowledgeList[i].Labels = nil
				continue
			}
			knowledgeDocBizIDs := make([]string, 0)
			InnerDescribeDocsReq := &bot_knowledge_config_server.InnerDescribeDocsReq{
				BotBizId:  knowledge.GetKnowledgeBizID(),
				DocBizIds: knowledge.GetDocBizIDs(),
			}
			rsp, err := rpc.BatchQueryDocuments(ctx, InnerDescribeDocsReq)
			if err != nil {
				log.WarnContextf(ctx, "fillCreateWorkflowParams BatchQueryDocuments Failed!:%s",
					err.Error())
			} else {
				for _, doc := range rsp.Docs {
					knowledgeDocBizIDs = append(knowledgeDocBizIDs, strconv.FormatUint(doc.DocBizId, 10))
				}
			}
			data.LLMKnowledgeQANodeData.KnowledgeList[i].DocBizIDs = knowledgeDocBizIDs
			data.LLMKnowledgeQANodeData.KnowledgeList[i].CateBizIDs = nil
			data.LLMKnowledgeQANodeData.KnowledgeList[i].Labels = nil
		}
	}
}

////// constructCtx 构造ctx
////func constructCtx(ctx context.Context, c CopyExperienceWorkflowScheduler) context.Context {
////	ctx = util.WithCorpID(ctx, c.params.CorpID)
////	ctx = util.WithStaffID(ctx, c.params.StaffID)
////	uin, _ := strconv.ParseUint(c.params.Uin, 10, 64)
////	subUin, _ := strconv.ParseUint(c.params.SubUin, 10, 64)
////	ctx = util.WithUin(ctx, uin)
////	ctx = util.WithSubUin(ctx, subUin)
////	return ctx
////}
//
//// saveImportWorkflow 导入时候触发保存草稿操作
//func saveImportWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq) (*KEP_WF.SaveWorkflowRsp, error) {
//	var err error
//	appBizID := req.GetAppBizId()
//	workflowName := strings.TrimSpace(req.GetName())
//	workflowJson := strings.TrimSpace(req.GetDialogJson())
//	workflowID := strings.TrimSpace(req.GetWorkflowId())
//	desc := strings.TrimSpace(req.GetDesc())
//	saveType := req.GetSaveType()
//	// 保存草稿的时候有些枚举值可以填空；比如逻辑判断节点中的 不选"等于"等操作符，所以不能用 JsonToWorkflow
//	tree, err := protoutil.JsonToWorkflowForPreCheck(workflowJson)
//	if err != nil {
//		log.WarnContextf(ctx, "saveExperienceWorkflow JsonToWorkflowForPreCheck workflowJson:%s，err:%+v",
//			workflowJson, err)
//		return nil, errs.New(500301, fmt.Sprintf("工作流画布JSON参数错误 %s", err))
//	}
//	if tree == nil {
//		log.WarnContextf(ctx, "saveExperienceWorkflow JsonToWorkflowForPreCheck workflowJson is empty")
//		return nil, errors.ErrWorkflowUIJsonParams
//	}
//	//tree = decodeBase64Code(tree)
//	tree.ProtoVersion = KEP_WF.WorkflowProtoVersion_V2_6
//	workflowJson, err = protoutil.WorkflowToJson(tree)
//	if err != nil {
//		log.WarnContextf(ctx, "saveExperienceWorkflow WorkflowToJson workflowJson:%s|err:%+v",
//			workflowJson, err)
//		return nil, errors.ErrWorkflowUIJsonParams
//	}
//	//// v2.8.5 画布中节点里的密钥需要加密存储
//	//workflowJson, err = protoutil.EncryptWorkflowJson(workflowJson)
//	//if err != nil {
//	//	log.ErrorContextf(ctx, "SaveWorkflow|EncryptWorkflowJson|workflowJson:%s|err:%+v",
//	//		workflowJson, err)
//	//	return nil, errors.ErrWorkflowUIJsonParams
//	//}
//	wf := check.New(ctx, appBizID, tree, saveType)
//	if err := wf.Parse(); err != nil { // 致命错误，不能保存草稿
//		log.WarnContextf(ctx, "saveExperienceWorkflow Parse err:%+v", err)
//		return nil, errors.BadWorkflowReqError(err.Error())
//	}
//	if len(wf.GetNodeErrors()) > 0 {
//		resp := &KEP_WF.SaveWorkflowRsp{WorkflowId: workflowID, WorkflowVersion: req.GetWorkflowVersion()}
//		resp.NodeErrors = make([]*KEP_WF.SaveWorkflowRsp_NodeError, 0, len(wf.GetNodeErrors()))
//		for _, nodeError := range wf.GetNodeErrors() {
//			resp.NodeErrors = append(resp.NodeErrors, &KEP_WF.SaveWorkflowRsp_NodeError{
//				NodeId: nodeError.NodeID, ErrMsg: nodeError.ErrMsg,
//			})
//		}
//		log.WarnContextf(ctx, "saveExperienceWorkflow wf.GetNodeErrors has err:%+v", resp.GetNodeErrors())
//		return resp, nil
//	}
//
//	uin, subUin := util.GetUinAndSubAccountUin(ctx)
//	staffID := util.StaffID(ctx)
//	modifyWorkflowParams := entity.ModifyWorkflowParams{
//		WorkflowID:       workflowID,
//		WorkflowName:     workflowName,
//		WorkflowDesc:     desc,
//		Version:          req.GetWorkflowVersion(),
//		DialogJsonDraft:  workflowJson,
//		Uin:              uin,
//		SubUin:           subUin,
//		StaffID:          staffID,
//		UpdateTime:       time.Now(),
//		AppBizID:         appBizID,
//		IsDebug:          saveType,
//		RefWorkflows:     wf.GetRefWorkflows(),
//		CustomVarIDs:     wf.GetCustomVarIDs(),
//		KnowledgeRef:     wf.GetKnowledgeRef(),
//		Parameters:       wf.GetParameters(),
//		RefPlugins:       wf.GetRefPlugins(),
//		CustomModels:     wf.GetWorkCustomModel(),
//		HasUnPublishData: true,
//	}
//	log.InfoContextf(ctx, "saveExperienceWorkflow modifyWorkflowParams:%+v", modifyWorkflowParams)
//	newWorkflow, err := db.SaveWorkflow(ctx, modifyWorkflowParams)
//	if err != nil {
//		log.ErrorContextf(ctx, "saveExperienceWorkflow db.SaveWorkflow err:%+v", err)
//		return nil, err
//	}
//	return &KEP_WF.SaveWorkflowRsp{
//		WorkflowId:      workflowID,
//		WorkflowVersion: newWorkflow.Version,
//	}, nil
//}
