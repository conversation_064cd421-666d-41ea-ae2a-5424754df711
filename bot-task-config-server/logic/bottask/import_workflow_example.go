// bot-task-config-server
//
// @(#)import_entry.go  星期三, 三月 06, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package bottask

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/json0"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
)

// ImportWfExampleTaskScheduler 导入工作流调度任务
type ImportWfExampleTaskScheduler struct {
	task         task_scheduler.Task
	p            entity.WorkflowImportParentParams
	totalNum     int
	successNum   int
	conflictNum  int
	conflictInfo []ConflictWfExams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportWfExample,
		func(task task_scheduler.Task, params entity.WorkflowImportParentParams) task_scheduler.TaskHandler {
			return &ImportWfExampleTaskScheduler{
				task: task,
				p:    params,
			}
		})
}

// Prepare 数据准备
func (iets *ImportWfExampleTaskScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (iets *ImportWfExampleTaskScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, iets.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportWfExampleTaskScheduler Init,sid:%s, ImportID:%s", util.RequestID(ctx), iets.p.ImportID)
	return nil
}

// Process 任务处理
func (iets *ImportWfExampleTaskScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ImportWfExampleTaskScheduler Process,sid:%s, ImportID:%s", sid, iets.p.ImportID)

	// 更改开始处理标志
	if err := db.UpdateWorkflowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusWait,
		entity.FlowImportStatusProcessing); err != nil {
		return err
	}

	// 根据importId查询导入示例问法的信息
	//查询任务流程导入信息
	importInfo, err := db.GetWorkflowImportByID(ctx, iets.p.ImportID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|GetTaskFlowImportByID Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	// 根据导入信息的获取工作流信息
	examsParams := &entity.WfExampleImportData{}
	log.DebugContextf(ctx, "ImportWfExampleTaskScheduler|importInfo:%s", json0.Marshal2StringNoErr(importInfo))
	if err := jsoniter.UnmarshalFromString(importInfo.Params, examsParams); err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|unmarshalFailed! importID:%s,err:%+v",
			iets.p.ImportID, err)
		return err
	}
	workflowID := examsParams.WorkflowId
	appID := examsParams.AppId
	// 获取工作流信息
	workflow, err := db.GetWorkflowDetail(ctx, workflowID, examsParams.AppId)
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|GetWorkflowDetail|%+v", err)
		return errors.OpDataFromDBError("获取工作流示例问法列表失败")
	}
	// 发布状态中的工作流，不允许更改状态
	if workflow.ReleaseStatus == entity.ReleaseStatusPublishing {
		return errors.ErrWorkflowPublishing
	}
	// 找到当前应用下所有的示例问法（应用与示例问法一对一）
	examples, err := db.ListFlowExamByBotId(ctx, appID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|ListFlowExamByBotAndIntentId|%+v", err)
		return errors.OpDataFromDBError("获取工作流示例问法列表失败")
	}
	// 获取当前工作流下面的示例问法
	_, wfExampleTotla, err := db.ListFlowExamByBotAndIntentId(ctx, "", workflowID, appID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|ListFlowExamByBotAndIntentId|%+v", err)
		return errors.OpDataFromDBError("获取工作流示例问法列表失败")
	}
	log.DebugContextf(ctx, "ImportWfExampleTaskScheduler|appId:%s|%s|existExam:%s|newExams:%s",
		appID, workflowID, json0.Marshal2StringNoErr(examples), json0.Marshal2StringNoErr(examsParams.Examples))
	// 对比导入的的示例问法
	successNum, conflictNum, insertExamsSlice, conflictInfo, err := compareWfExamplesInfo(ctx, workflowID, appID,
		examsParams.Uin, examsParams.SubUin, examples, examsParams.Examples, int(wfExampleTotla))
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|compareWfExamplesInfo|%+v", err)
		return errors.OpDataFromDBError("获取工作流列表失败")
	}
	iets.totalNum = len(examsParams.Examples)
	iets.successNum = successNum
	iets.conflictNum = iets.totalNum - successNum
	if conflictInfo != nil {
		iets.conflictInfo = conflictInfo
	}
	log.DebugContextf(ctx, "ImportWfExampleTaskScheduler|insertExamsSlice:%s|conflictInfo:%s|successNum:%d|conflictNum:%d",
		json0.Marshal2StringNoErr(insertExamsSlice), json0.Marshal2StringNoErr(conflictInfo), successNum, conflictNum)
	// 以事务的方式导入示例问法，并更新相关联的工作流表
	if err := db.TxCreateImportExams(ctx, iets.p.ImportID, workflowID, appID, insertExamsSlice); err != nil {
		log.ErrorContextf(ctx, "ImportWfExampleTaskScheduler|compareWfExamplesInfo|err:%+v", err)
		return errors.OpDataFromDBError("导入示例问法失败")
	}

	return nil
}

// Fail 任务失败
func (iets *ImportWfExampleTaskScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateWorkflowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	return iets.sendImportWfExampleFinishNotice(ctx, false)

}

// Stop 任务停止
func (iets *ImportWfExampleTaskScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (iets *ImportWfExampleTaskScheduler) Done(ctx context.Context) error {
	if err := db.UpdateWorkflowImportStatus(ctx, iets.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessed); err != nil {
		return err
	}
	return iets.sendImportWfExampleFinishNotice(ctx, iets.successNum != 0)
}

func (iets *ImportWfExampleTaskScheduler) sendImportWfExampleFinishNotice(ctx context.Context, success bool) error {

	log.InfoContextf(ctx, "ImportWfExampleTaskScheduler|sendImportWfExampleFinishNotice,iets:%+v,success:%t",
		iets, success)

	var noticeContent, noticeSubject, noticeLevel string
	if success {
		noticeContent = fmt.Sprintf(entity.WfExamImportNoticeContent, iets.totalNum, iets.successNum, iets.conflictNum)
		noticeSubject = "示例问法导入成功"
		noticeLevel = entity.LevelSuccess
	} else {
		noticeContent = fmt.Sprintf(entity.WorkflowParentImportFailNoticeContent, iets.p.FileName)
		noticeSubject = "示例问法导入失败"
		noticeLevel = entity.LevelError
	}

	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(iets.p.RobotID)),
		PageId:       entity.NoticeWorkflowPageID,
		Type:         entity.NoticeTypeWorkflowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(iets.p.ImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       iets.p.CorpID,
		StaffId:      iets.p.StaffID,
	}

	if len(iets.conflictInfo) > 0 {
		_ = iets.getWfExamsConflictFile(ctx, createNoticeReq)
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

func (iets *ImportWfExampleTaskScheduler) getWfExamsConflictFile(ctx context.Context,
	createNoticeReq *pb.CreateNoticeReq) error {
	sid := util.RequestID(ctx)

	if len(iets.conflictInfo) == 0 {
		return nil
	}

	conflictInfos := iets.conflictInfo

	data := make([][]string, 0)
	for _, conflict := range conflictInfos {
		log.DebugContextf(ctx, "getWfExamsConflictFile.Example:%s, ConflictMsg:%s", conflict.Example, conflict.ConflictMsg)
		data = append(data, []string{conflict.Example, conflict.ConflictMsg})
	}

	dataSheetMap := make(map[string][][]string)
	dataSheetMap["Sheet1"] = data
	xlFile, err := file.SaveData2Xlsx(dataSheetMap)
	if err != nil {
		log.ErrorContextf(ctx, "getWfExamsConflictFile|save2Xlsx Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	buff, err := xlFile.WriteToBuffer()
	if err != nil {
		log.ErrorContextf(ctx, "getWfExamsConflictFile|xlFile.WriteToBuffer Failed! sid:%s,err:%+v", sid, err)
		return err
	}
	// 上传cos文件
	filename := fmt.Sprintf("导入示例问法错误信息-%d.xlsx", time.Now().Unix())
	cosPath := cos.GetCorpCOSFilePath(iets.p.CorpID, filename)
	if err := cos.StorageCli.PutObject(ctx, buff.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "pubObject failed! err:%+v", err)
		return err
	}
	// 加入通知
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeExportDownload,
		Params: &pb.CreateNoticeReq_Operation_Params{CosPath: cosPath},
	})

	return nil
}

// ConflictWfExams 示例问法冲突信息
type ConflictWfExams struct {
	entity.ExportWorkflowExample
	ConflictMsg string `json:"string"` // 冲突信息
}

// compareWfExamplesInfo 对比工作流示例问法
func compareWfExamplesInfo(ctx context.Context, wfId, appId, uin, subUin string,
	existExams []*entity.WorkflowExample, newExams []*entity.ExportWorkflowExample, wfExamplesTotal int) (successNum, conflictNum int,
	insertExamsSlice []*entity.WorkflowExample, conflictInfo []ConflictWfExams, err error) {
	maxExamsNum := config.GetMainConfig().ExampleCorpus.IntentExampleMax
	remainExamsNum := maxExamsNum - wfExamplesTotal
	//log.InfoContextf(ctx, "ImportWfExampleTaskScheduler|existEntryNameMap:%+v,existAliasNameMap:%+v,"+
	//	"existEntryIDMap:%+v", existEntryNameMap, existAliasNameMap, existEntryIDMap)
	existExamsContentMap := make(map[string]string, len(existExams))
	existExamsWfIdMap := make(map[string]string, len(existExams))
	// 获取已存在示例问法工作流的信息（获取工作流名称）
	mm := make(map[string]struct{}, 0)
	wfIds := make([]string, 0, len(existExams))
	// _, ok := mm[v.WorkflowID]; !ok
	for _, exam := range existExams {
		eContent := strings.TrimSpace(exam.Example)
		if _, ok := existExamsContentMap[eContent]; !ok {
			existExamsContentMap[eContent] = exam.ExampleID
		}
		if _, ok := mm[exam.FlowID]; !ok {
			flowId := exam.FlowID
			wfIds = append(wfIds, flowId)
			mm[flowId] = struct{}{}
		}
		if _, ok := existExamsWfIdMap[eContent]; !ok {
			existExamsWfIdMap[eContent] = exam.FlowID
		}
	}
	// 获取工作流的nameMap
	workflowsMap, err := db.GetWorkflowDetails(ctx, wfIds, appId)
	if err != nil {
		log.ErrorContextf(ctx, "compareWfExamplesInfo|ImportWfExampleTaskScheduler|err:+%v", err)
		return 0, 0, nil, nil, err
	}

	for _, exam := range newExams {
		// 导入的达到 remainExamsNum数量，则跳出对比
		if successNum >= remainExamsNum {
			break
		}
		newContent := strings.TrimSpace(exam.Example)
		if _, ok := existExamsContentMap[newContent]; !ok {
			successNum++
			examId := idgenerator.NewUUID()
			insertExamsSlice = append(insertExamsSlice, &entity.WorkflowExample{
				FlowID:        wfId,
				ExampleID:     examId,
				Example:       newContent,
				RobotId:       appId,
				Uin:           uin,
				SubUin:        subUin,
				ReleaseStatus: entity.ReleaseStatusUnPublished,
				IsDeleted:     0,
				Action:        entity.ActionInsert,
			})
		} else {
			var conflictExam ConflictWfExams
			// 示例问法重名冲突
			conflictNum++
			wfId := existExamsWfIdMap[newContent]
			workflowInfo := workflowsMap[wfId]
			log.DebugContextf(ctx, "getWfExamsConflictFile.Example:%s|FlowID:%s", newContent, exam.FlowID)
			if workflowInfo != nil {
				conflictExam.ConflictMsg = fmt.Sprintf("该应用的工作流：%s下已经存在示例问法:%s", workflowInfo.WorkflowName, newContent)
			}
			conflictExam.Example = newContent
			conflictInfo = append(conflictInfo, conflictExam)
		}
	}
	return successNum, conflictNum, insertExamsSlice, conflictInfo, nil
}
