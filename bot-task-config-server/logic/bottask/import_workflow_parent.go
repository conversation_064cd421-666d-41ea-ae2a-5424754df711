package bottask

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ImportWorkflowParentScheduler 导入工作流程父调度任务
type ImportWorkflowParentScheduler struct {
	task task_scheduler.Task
	p    entity.WorkflowImportParentParams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportWorkflowParent,
		func(t task_scheduler.Task, params entity.WorkflowImportParentParams) task_scheduler.TaskHandler {
			return &ImportWorkflowParentScheduler{
				task: t,
				p:    params,
			}
		},
	)
}

// Prepare 数据准备
func (itfps *ImportWorkflowParentScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (itfps *ImportWorkflowParentScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, itfps.p.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "ImportWorkflowParent Init, ImportID:%s", itfps.p.ImportID)
	return nil
}

// Process 任务处理
func (itfps *ImportWorkflowParentScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	// 查询工作流程导入信息
	parent, err := db.GetWorkflowImportByID(ctx, itfps.p.ImportID)
	if err != nil {
		return err
	}
	if parent.Status != entity.FlowImportStatusProcessing {
		return errors.ErrImportWorkflowStatus
	}
	// 创建工作流程子任务调度
	taskFlowImports, err := db.GetWorkflowImportByParentID(ctx, parent.ImportID)
	if err != nil {
		log.ErrorContextf(ctx, "task flow import parent get imports importID:%s,err:%+v", itfps.p.ImportID, err)
		return err
	}
	for _, v := range taskFlowImports {
		if v.Status != entity.FlowImportStatusWait {
			continue
		}
		if err := db.CreateWorkflowImportSub(ctx, itfps.p.CorpID, itfps.p.StaffID, itfps.p.RobotID,
			itfps.p.FileName, itfps.p.ImportID, v.ImportID); err != nil {
			return err
		}
	}
	return nil
}

// Fail 任务失败
func (itfps *ImportWorkflowParentScheduler) Fail(ctx context.Context) error {
	if err := db.UpdateWorkflowImportStatus(ctx, itfps.p.ImportID, entity.FlowImportStatusProcessing,
		entity.FlowImportStatusProcessFail); err != nil {
		return err
	}
	// 发送通知
	noticeContent := fmt.Sprintf(entity.WorkflowParentImportFailNoticeContent, itfps.p.FileName)
	noticeSubject := "工作流程导入失败"
	noticeLevel := entity.LevelError
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(itfps.p.RobotID)),
		PageId:       entity.NoticeWorkflowPageID,
		Type:         entity.NoticeTypeWorkflowImport,
		Level:        noticeLevel,
		RelateId:     uint64(encode.StringToInt64(itfps.p.ImportID)),
		Subject:      noticeSubject,
		Content:      noticeContent,
		IsGlobal:     true,
		IsAllowClose: true,
		CorpId:       itfps.p.CorpID,
		StaffId:      itfps.p.StaffID,
	}
	createNoticeReq.Operations = append(createNoticeReq.Operations, &pb.CreateNoticeReq_Operation{
		Type:   entity.OpTypeViewDetail,
		Params: &pb.CreateNoticeReq_Operation_Params{},
	})
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}

// Stop 任务停止
func (itfps *ImportWorkflowParentScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (itfps *ImportWorkflowParentScheduler) Done(_ context.Context) error {
	return nil
}
