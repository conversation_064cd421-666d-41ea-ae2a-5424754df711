// Package category bot-task-config-server
// @(#)category.go  星期三, 十二月 20, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.
package category

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/goredis/redlock"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/spf13/cast"
)

// CreateCategory 创建目录分类
func CreateCategory(ctx context.Context,
	req *KEP.CreateCategoryReq) (*KEP.CreateCategoryRsp, error) {
	robotId := req.GetBotBizId()
	parentId := req.GetParentBizId()
	categoryName := strings.TrimSpace(req.GetName())
	cateId := strconv.FormatInt(idgenerator.NewInt64ID(), 10)
	sid := util.RequestID(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "CreateCategory,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	cates, err := db.GetCategorys(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "CreateCategory,GetCategorys:%s|%s", sid, err.Error())
		return nil, errors.ErrCateNotFound
	}
	tree := category.BuildCateTree(cates)
	categoryNodeLimit := config.GetMainConfig().Category.CategoryNodeLimit
	log.InfoContextf(ctx, "CreateCategory,entity.CategoryNodeLimit:%d", categoryNodeLimit)
	if tree.NodeCount()-1 > categoryNodeLimit {
		log.WarnContextf(ctx, "CreateCategory,over NodeCount:%s|%d", sid, categoryNodeLimit)
		return nil, errors.ErrCateCountExceed
	}

	parent := tree.FindNode(cast.ToUint64(parentId))

	if parent == nil {
		log.ErrorContextf(ctx, "CreateCategory,can't found parent cateNode:%s", sid)
		return nil, errors.ErrCateNotFound
	}
	if parent.IsUncategorized() {
		return nil, errors.ErrInvalidCateID
	}
	log.InfoContextf(ctx, "CreateCategory,entity.ExcelTplCateLen:%d",
		len(config.GetMainConfig().TaskFlow.CategoryHead)-1)
	if parent.Depth > uint(len(config.GetMainConfig().TaskFlow.CategoryHead)-1) {
		return nil, errors.ErrCateDepthExceed
	}

	if parent.IsNameDuplicate(categoryName) {
		log.WarnContextf(ctx, "CreateCategory,ErrCateNameDuplicated:%s", sid)
		return nil, errors.ErrCateNameDuplicated
	}
	uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)
	var category = &category.Category{
		CategoryID:   cateId,
		CategoryName: categoryName,
		RobotID:      robotId,
		ParentID:     parentId,
		Feature:      category.TCategoryFeature.Flow,
		Uin:          uin,
		SubUin:       subAccountUin,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}
	log.InfoContextf(ctx, "CreateCategory:%+v|Uin:%s|SubUin:%s", category, uin, subAccountUin)
	cate, err := db.CreateCategory(ctx, category)
	if err != nil {
		log.Errorf("CreateCategory %s|%v", sid, err)
		return nil, errors.OpDataFromDBError("创建分类失败")
	}
	rsp := &KEP.CreateCategoryRsp{
		CateBizId: cate.CategoryID,
		CanAdd:    parent.Depth+1 < uint(len(config.GetMainConfig().TaskFlow.CategoryHead)-1),
		CanEdit:   true,
		CanDelete: true,
	}
	return rsp, nil
}

// UpdateCategory 更新目录分类
func UpdateCategory(ctx context.Context, req *KEP.UpdateCategoryReq) (*KEP.UpdateCategoryRsp, error) {
	var rsp = new(KEP.UpdateCategoryRsp)
	name := strings.TrimSpace(req.GetName())
	robotId := req.GetBotBizId()
	cateId := req.GetCateBizId()
	sid := util.RequestID(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateCategory,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	if err := checkQACateName(name); err != nil {
		return rsp, err
	}
	cates, err := db.GetCategorys(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateCategory,GetCategorys err: %v", err.Error())
		return rsp, errors.OpDataFromDBError("获取分类失败")
	}

	tree := category.BuildCateTree(cates)
	node := tree.FindNode(cast.ToUint64(cateId))
	if node == nil {
		return rsp, errors.ErrCateNotFound
	}
	parentId := category.StrToUint64(node.ParentID)

	parent := tree.FindNode(parentId)
	log.InfoContextf(ctx, "UpdateCategory:parentId:%v, parent:%s", node.ParentID, parent)
	if parent == nil {
		return rsp, errors.ErrCateNotFound
	}

	if parent.IsNameDuplicate(name) {
		return rsp, errors.ErrCateNameDuplicated
	}
	if node.IsUncategorized() {
		return nil, errors.ErrInvalidCateID
	}
	if err = db.UpdateCategory(ctx, cateId, name, robotId); err != nil {
		log.ErrorContextf(ctx, "UpdateCategory|err:%+v", err)
		return rsp, errors.OpDataFromDBError("更新分类失败")
	}

	return rsp, nil
}

// DeleteCategory 删除目录分类
func DeleteCategory(ctx context.Context, req *KEP.DeleteCategoryReq) (*KEP.DeleteCategoryRsp, error) {
	rsp := new(KEP.DeleteCategoryRsp)
	robotId := req.GetBotBizId()
	cateId := req.GetCateBizId()
	// 判断机器人信息
	sid := util.RequestID(ctx)

	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}

	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteCategory,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	cates, err := db.GetCategorys(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteCategory:%s|%+v", sid, err)
		return rsp, errors.OpDataFromDBError("获取分类失败")
	}

	tree := category.BuildCateTree(cates)
	node := tree.FindNode(cast.ToUint64(cateId))
	if node == nil {
		log.ErrorContextf(ctx, "DeleteCategory:%s|ErrCateNotFound", sid)
		return nil, errors.ErrCateNotFound
	}
	// 未分类不可删除
	if node.IsUncategorized() {
		log.ErrorContextf(ctx, "DeleteCategory:%s|ErrInvalidCateID", sid)
		return nil, errors.ErrInvalidCateID
	}
	ids := append(node.ChildrenIDs(), req.GetCateBizId())

	unCateID := tree.Find([]string{category.UncategorizedCateName})
	log.InfoContextf(ctx, "unCateID:%+v", unCateID)
	if unCateID == -1 {
		return nil, errors.ErrCateNotFound
	}

	if err := db.DeleteCategory(ctx, ids, uint64(unCateID), robotId); err != nil {
		log.ErrorContextf(ctx, "DeleteCategory:%s|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("删除分类失败")
	}
	return rsp, nil
}

// ListCategory 分类列表&过滤
func ListCategory(ctx context.Context, req *KEP.ListCategoryReq) (*KEP.ListCategoryRsp, error) {
	var (
		err       error
		cates     []*category.Category
		statmodel []*category.CateStat
	)
	robotId := req.GetBotBizId()
	sid := util.RequestID(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteCategory,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}
	start := time.Now()
	redisClient := database.GetRedis()
	redLock, err := redlock.New(redisClient)
	if err != nil {
		log.WarnContextf(ctx, "CreateCategory,redlock.New:%s|error:%s", sid, err.Error())
		return &KEP.ListCategoryRsp{}, nil
	}
	key := "KEP_bot-task-config-server:CreateCategory-" + robotId
	opts := []redlock.Option{
		redlock.WithKeyExpiration(1 * time.Second),
		redlock.WithWaitDuration(600 * time.Millisecond),
		redlock.WithSleepDuration(400 * time.Millisecond),
	}
	unlock, err := redLock.Lock(ctx, key, opts...)

	if err != nil {
		log.WarnContextf(ctx, "CreateCategory，redLock.Lock:%s|error:%s", sid, err.Error())
		return &KEP.ListCategoryRsp{}, nil
	}
	defer func() {
		if err = unlock(ctx); err != nil {
			log.WarnContextf(ctx, "CreateCategory，unlock:%s|error:%s", sid, err.Error())
		}
		log.InfoContextf(ctx, "unlock success %s|%v", sid, time.Since(start))
	}()

	isRobotHasUnCate, _ := db.QueryUnCateUnderRobot(ctx, req.GetBotBizId())
	if !isRobotHasUnCate { // 没有未分类，则创建一个
		uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)
		cateId := strconv.FormatInt(idgenerator.NewInt64ID(), 10)
		var category = &category.Category{
			CategoryID: cateId, RobotID: robotId,
			CategoryName: category.UncategorizedCateName,
			ParentID:     category.AllCateID,
			Feature:      category.TCategoryFeature.Flow,
			Uin:          uin, SubUin: subAccountUin,
			CreateTime: time.Now(), UpdateTime: time.Now(),
		}
		_, err = db.CreateCategory(ctx, category)
		if err != nil {
			log.ErrorContextf(ctx, "ListCategory.CreateCategory:%s|err:%+v", sid, err)
			return &KEP.ListCategoryRsp{}, errors.OpDataFromDBError("获取分类失败")
		}
	}

	cates, err = db.GetCategorys(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "ListCategory:%s|err:%s", sid, err.Error())
		return &KEP.ListCategoryRsp{}, errors.OpDataFromDBError("获取分类失败")
	}

	statmodel, err = db.GetCategoryStat(ctx, robotId, category.TCategoryFeature.Flow)
	if err != nil {
		return &KEP.ListCategoryRsp{}, err
	}
	stat := make(map[string]uint32, len(statmodel))
	for _, v := range statmodel {
		stat[v.CategoryID] = v.Total
	}
	tree := category.BuildCateTree(cates)
	return &KEP.ListCategoryRsp{List: []*KEP.ListCategoryRsp_Cate{tree.ToPbCateTree(stat)}}, nil
}

func checkQACateName(name string) error {
	name = strings.TrimSpace(name)
	maxCategoryNameLen := config.GetMainConfig().Category.CategoryNameLen
	minCategoryNameLen := config.GetMainConfig().Category.CategoryNameMinLen
	if len([]rune(name)) < minCategoryNameLen {
		return errs.Newf(errors.ErrCodeCateNameTooShort, "分类标题少于%d个字符，请重新填写", minCategoryNameLen)
	}
	if len([]rune(name)) > maxCategoryNameLen {
		return errs.Newf(errors.ErrCodeCateNameTooLong, "分类标题超过%d个字符，请重新填写", maxCategoryNameLen)
	}
	return nil
}
