// bot-task-config-server
//
// @(#)example.go  星期一, 四月 08, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package corpus

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	tdb "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// CreateExample 新建示例问法
func CreateExample(ctx context.Context, req *KEP.CreateExampleReq) (*KEP.CreateExampleRsp, error) {
	sid := util.RequestID(ctx)
	botBizId := req.GetBotBizId()
	flowId := strings.TrimSpace(req.GetFlowId())
	example := strings.TrimSpace(req.GetExample())
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|CreateExample|%s|%v", sid, err)
		return nil, err
	}

	// 判断当前任务流程是否存在
	taskFlow, err := db.GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|CreateExample|GetTaskFlowDetail,flowId:%s,err:%+v",
			sid, flowId, err)
		return nil, errors.ErrTaskFlowNotFound
	}

	intentId := taskFlow.IntentID
	// 判断示例问法的名称长度及是否在机器人唯独重复
	err = checkExampleLengthAndRepeat(ctx, botBizId, intentId, example)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|CreateExample|checkExampleNameAndLength|flowId:%s|"+
			"example:%s|err:%s", sid, flowId, example, err.Error())
		return nil, err
	}

	corpusId := idgenerator.NewUUID()
	key := "KEP_bot-task-config-server:CreateExample-" + botBizId
	locker := lock.NewDefaultLocker(key, corpusId, database.GetRedis())
	// 加锁
	ok, lockErr := locker.Lock(ctx, true)
	if lockErr != nil {
		log.ErrorContextf(ctx, "CreateExample locker.Lock Failed, err:%v", lockErr)
		return &KEP.CreateExampleRsp{}, nil
	}
	if !ok {
		log.WarnContextf(ctx, "CreateExample locker.Lock Failed, err:%v", lockErr)
		return &KEP.CreateExampleRsp{}, nil
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.WarnContextf(ctx, "CreateExample locker.UnLock Fialed, err:%v", unLockErr)
		}
		log.InfoContextf(ctx, "sid:%s|unlock success", sid)
	}()

	// 创建示例问法
	intentCorpus := &entity.IntentCorpus{
		CorpusID:      corpusId,
		IntentID:      intentId,
		Corpus:        example,
		RobotId:       botBizId,
		Uin:           uin,
		SubUin:        subUin,
		IsDeleted:     0,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		Action:        entity.ActionInsert,
	}
	err = db.CreateExampleIntentCorpus(ctx, intentCorpus)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|CreateExample|CreateExampleIntentCorpus|flowId:%s|"+
			"example:%+v|err:%+v", sid, flowId, intentCorpus, err)
		return nil, errors.OpDataFromDBError("创建示例问法失败")
	}
	return &KEP.CreateExampleRsp{
		ExampleId: intentCorpus.CorpusID,
	}, nil
}

// GetExampleList 获取示例问法列表
func GetExampleList(ctx context.Context, req *KEP.GetExampleListReq) (*KEP.GetExampleListRsp, error) {
	sid := util.RequestID(ctx)
	keyword := strings.TrimSpace(req.GetKeyword())
	botBizId := req.GetBotBizId()
	flowId := strings.TrimSpace(req.GetFlowId())
	bizVersion := strings.TrimSpace(req.GetBizVersion())
	saveType := strings.TrimSpace(req.GetSaveType())
	var examples *[]entity.IntentCorpus
	var total int64
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|GetExampleList|%s|%v", sid, err)
		return nil, err
	}

	// 判断当前任务流程是否存在
	taskFlow, err := db.GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleList|GetTaskFlowDetail,flowId:%s,err:%+v",
			sid, flowId, err)
		return nil, errors.ErrRobotNotFound
	}
	intentId := taskFlow.IntentID

	// 获取历史版本
	if saveType == entity.SaveDraft || saveType == entity.SavePublished {
		examples, total, err = db.GetExampleHistroyListByIntentId(ctx, keyword, botBizId, intentId, bizVersion, saveType)
	} else {
		// 当前示例问法
		examples, total, err = tdb.GetExampleListByBotAndIntentId(ctx, keyword, botBizId, intentId)
	}
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleList|GetExampleListByBotAndIntentId,flowId:%s,err:%s",
			sid, flowId, err.Error())
		return nil, errors.OpDataFromDBError("获取示例问法失败")
	}

	var list []*KEP.GetExampleListRsp_ExampleData
	if examples != nil {
		for _, item := range *examples {
			list = append(list, &KEP.GetExampleListRsp_ExampleData{
				ExampleId: item.CorpusID,
				Example:   item.Corpus,
			})
		}
	}

	return &KEP.GetExampleListRsp{
		List:  list,
		Total: uint32(total),
	}, nil
}

// UpdateExample 更新示例问法
func UpdateExample(ctx context.Context, req *KEP.UpdateExampleReq) (*KEP.UpdateExampleRsp, error) {
	sid := util.RequestID(ctx)
	botBizId := req.GetBotBizId()
	exampleId := strings.TrimSpace(req.GetExampleId())
	exam := strings.TrimSpace(req.GetExample())
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|UpdateExample|%s|%v", sid, err)
		return nil, err
	}

	intentExample, err := db.GetExampleByNameCorpusId(ctx, botBizId, exampleId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateExample|GetExampleByNameCorpusId|exampleId:%s|"+
			"example:%s|err:%+v", sid, exampleId, exam, err)
		return nil, errors.OpDataFromDBError("更新示例问法失败")
	}
	if intentExample != nil {
		// 判断示例问法的名称长度及是否在机器人唯独重复
		err = checkExampleLengthAndRepeat(ctx, botBizId, intentExample.IntentID, exam)
		if err != nil {
			log.WarnContextf(ctx, "sid:%s|UpdateExample|checkExampleNameAndLength|exampleId:%s|"+
				"example:%s|err:%+v", sid, exampleId, exam, err)
			return nil, err
		}

		if err := db.UpdateExampleIntentCorpus(ctx, botBizId, exampleId, exam); err != nil {
			log.ErrorContextf(ctx, "sid:%s|UpdateExample|UpdateExampleIntentCorpus|err:%+v",
				sid, err)
			return nil, errors.OpDataFromDBError("更新示例问法失败")
		}
	} else {
		return nil, errors.ErrRobotExampleNotFound
	}

	return &KEP.UpdateExampleRsp{}, nil
}

// DeleteExample 删除示例问法
func DeleteExample(ctx context.Context, req *KEP.DeleteExampleReq) (*KEP.DeleteExampleRsp, error) {
	sid := util.RequestID(ctx)
	botBizId := req.GetBotBizId()
	exampleId := strings.TrimSpace(req.GetExampleId())
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|DeleteExample|%s|%v", sid, err)
		return nil, err
	}
	if err := db.DeleteExampleIntentCorpus(ctx, botBizId, exampleId); err != nil {
		log.ErrorContextf(ctx, "sid:%s|DeleteExample|DeleteExampleIntentCorpus|err:%+v",
			sid, err)
		return nil, errors.OpDataFromDBError("删除示例问法失败")
	}
	return &KEP.DeleteExampleRsp{}, nil
}

// checkExampleNameAndLength 校验问法示例的长度及是否重复
func checkExampleLengthAndRepeat(ctx context.Context, robotId, intentId, example string) error {
	maxExampleContent := config.GetMainConfig().ExampleCorpus.IntentExampleContentLen
	maxExampleCount := config.GetMainConfig().ExampleCorpus.IntentExampleMax
	sid := util.RequestID(ctx)

	if len([]rune(example)) > maxExampleContent {
		return errs.Newf(errors.ErrExampleContentTooLong, "示例问法超过%d个字符，请重新填写", maxExampleContent)
	}
	// 获取该任务流程下的示例用法总数
	count, err := db.GetExampleForRobotIntentCount(ctx, robotId, intentId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|"+
			"GetExampleForRobotIntent|robotId:%s|intentId:%s", sid, robotId, intentId)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|count:%d|maxExampleCount:%d", sid, count, maxExampleCount)
	if int(count) >= maxExampleCount {
		return errs.Newf(errors.ErrExampleCountTooLong, "该任务流程下示例问法总数不能超过%d个", maxExampleCount)
	}

	// 检查是否重复
	intentCorpus, err := db.GetExampleByNameInRobot(ctx, robotId, example)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|"+
			"GetExampleByNameInRobot|robotId:%s|intentId:%s", sid, robotId, intentId)
		return err
	}
	log.InfoContextf(ctx, "sid:%s|GetExampleByNameInRobot:%+v", sid, intentCorpus)
	if len(intentCorpus) > 0 {
		log.WarnContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|"+
			"robotId:%s|example:%s|%+v", sid, robotId, example, intentCorpus)
		return errors.ErrRobotExampleDuplicated
	}

	return nil
}
