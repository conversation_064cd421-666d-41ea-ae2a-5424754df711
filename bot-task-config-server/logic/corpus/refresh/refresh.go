// bot-task-config-server
//
// @(#)refresh.go  星期四, 八月 01, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

// Package refresh  提供用于刷新数据的工具和函数。
package refresh

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"github.com/avast/retry-go"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"strings"
	"time"
)

const (
	RefreshExampleRedisKey = "R_Exam_Vector-%s"
	RefreshFinished        = "1"
	RefreshFail            = "0"
	RefreshFailExpiredTime = 1 * 60 * 60
)

// RefreshExample ...
type RefreshExample struct {
	appId string
	tx    *gorm.DB
	redis redis.UniversalClient
}

// NewRefreshExample ...
func NewRefreshExample(appId string, tx *gorm.DB, r redis.UniversalClient) *RefreshExample {
	return &RefreshExample{
		appId: appId,
		tx:    tx,
		redis: r,
	}
}

// RefreshAppExample ...
func (r *RefreshExample) RefreshAppExample(ctx context.Context) error {
	sid := util.RequestID(ctx)
	appId := r.appId
	tx := r.tx

	intentIds, err := db.GetIntentIdsInIntentCorpus(ctx, appId, tx)
	if err != nil {
		log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|RefreshExample|GetIntentIdsInIntentCorpus|err:%+v", sid, err)
		return err
	}
	for _, intentId := range intentIds {
		startTime := time.Now() // 记录函数开始时间
		// redis里面没有从DB获取
		intentCorpusDetails, err := db.GetIntentCorpusDetails(ctx, appId, intentId, tx)
		log.InfoContextf(ctx, "sid:%s|intentCorpusDetails:%+v|intentId:%s", sid, intentCorpusDetails, intentId)
		if err != nil {
			log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|GetIntentCorpusDetails|appid:%s|"+
				"intentId:%s|err:%+v", sid, appId, intentId, err)
			continue
		}
		if intentCorpusDetails == nil {
			continue
		}

		log.InfoContextf(ctx, "sid:%s|Details:%+v|intentId:%s|appId:%s", sid, intentCorpusDetails, intentId, appId)
		refreshConfig := config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector
		if err := retry.Do(
			func() error {
				return r.refreshExampleToVector(ctx, intentCorpusDetails)
			},
			retry.Attempts(refreshConfig.Attempts),
			retry.Delay(time.Duration(refreshConfig.Delay)*time.Millisecond),
			retry.MaxDelay(time.Duration(refreshConfig.MaxDelay)*time.Second),
			retry.LastErrorOnly(true),
			retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
				log.ErrorContextf(ctx, "sid:%s|Server fails|err:%+v", err)
				return retry.BackOffDelay(n, err, config)
			}),
			retry.OnRetry(func(n uint, err error) {
				log.ErrorContextf(ctx, "sid:%s|RefreshExample(Refresh)|refreshExampleToVector,fail|appId:%s|"+
					"intentId:%s,retry:%d, err:%v", sid, appId, intentId, n, err)
			})); err != nil {
			// 失败将示例问法详情保存到redis
			log.WarnContextf(ctx, "saveItemInfoInRedis:intentId:%s|intentCorpusDetails:%+v", intentId, intentCorpusDetails)
			if err = r.saveItemInfoInRedis(ctx, appId, intentId, intentCorpusDetails); err != nil {
				log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|appId:%s|intentId:%s|err:%+v",
					sid, r.appId, intentId, err)
			}
			log.ErrorContextf(ctx, "sid:%s|RefreshExample|refreshExampleToVector|appid:%s"+
				"|intentCorpusDetails:%+v|err:%+v", sid, appId, intentCorpusDetails, err)
			continue
		}
		elapsedTime := time.Since(startTime) // 计算经过的时间
		log.InfoContextf(ctx, "sid:%s|refreshExampleToVector|intentId:%s|appId:%s｜spend:%+v",
			sid, intentId, appId, elapsedTime)
	}
	return nil
}

// RetryFailRefreshCorpus 处理redis里面失败的示例问法
func RetryFailRefreshCorpus(ctx context.Context, tx *gorm.DB, rdb redis.UniversalClient) {
	sid := util.RequestID(ctx)
	r := NewRefreshExample("", tx, rdb)
	keys := r.getFailCorpusRedisKeys(ctx)
	finishKey := GetRefreshExampleRedisKey("RefreshExampleComplete")
	log.InfoContextf(ctx, "sid:%s|RefreshExample|len:%d|keys:%+v", sid, len(keys), keys)
	if len(keys) > 0 {
		for _, key := range keys {
			appId, IntentId := r.getCorpusExampleIdsInfo(ctx, key)
			detail, _ := r.getItemInfoInRedis(ctx, appId, IntentId)
			log.InfoContextf(ctx, "sid:%d|RetryFailRefreshCorpus|appId:%s|IntentId:%s|detail:%+v|flag:%+v",
				sid, appId, IntentId, detail)
			if detail != nil {
				err := r.refreshExampleToVector(ctx, detail)
				if err == nil {
					// 同步成功，删除redis缓存 detail
					_ = r.delItemInfoInRedis(ctx, appId, IntentId)
				} else {
					_, err = rdb.Set(ctx, finishKey, RefreshFail, 0).Result()
					log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|delItemInfoInRedis:err:%+v", sid, err)
					continue
				}
			}
		}
	} else {
		// 刷完
		_, err := rdb.Set(ctx, finishKey, RefreshFinished, 0).Result()
		if err != nil {
			log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|set finished:err:%+v", sid, err)
		}
	}
}

func (r *RefreshExample) getCorpusExampleIdsInfo(ctx context.Context, key string) (string, string) {
	ks := strings.Split(key, ":")
	ln := len(ks)
	if ln > 0 {
		log.InfoContextf(ctx, "sid:%s|getCorpusExample|appId:%s|IntentId:%s",
			util.RequestID(ctx), ks[ln-2], ks[ln-1])
		return ks[ln-2], ks[ln-1]
	}
	return "", ""
}

func (r *RefreshExample) getFailCorpusRedisKeys(ctx context.Context) []string {
	// 使用 SCAN 命令迭代所有键
	sid := util.RequestID(ctx)
	keyPrefix := GetRefreshExampleRedisKey("FAIL:")
	keys := make([]string, 0)
	ks, cur, err := r.redis.Scan(ctx, 0, keyPrefix+"*", 10).Result()
	keys = append(keys, ks...)
	if err != nil {
		log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|getFailCorpusRedisKeys:err:%+v", sid, err)
	}
	for cur != 0 {
		ks, cur, err = r.redis.Scan(ctx, cur, keyPrefix+"*", 10).Result()
		if err != nil {
			panic(err)
		}
		keys = append(keys, ks...)
	}
	return keys
}

func (r *RefreshExample) saveItemInfoInRedis(ctx context.Context, appId, intentId string,
	detail *db.IntentCorpusDetail) error {
	sid := util.RequestID(ctx)
	key := r.getIntentCorpusDetailRdKey(appId, intentId)
	detailStr := json0.Marshal2StringNoErr(detail)
	// 保存前检查是否存在数据
	rDetail, _ := r.getItemInfoInRedis(ctx, appId, intentId)
	log.InfoContextf(ctx, "saveItemInfoInRedis|appId:%s|intentId:%s|rDetail:%+v", r.appId, intentId, rDetail)
	if rDetail == nil {
		if err := db.SaveRefreshInfosToRD(ctx, key, detailStr, r.redis, RefreshFailExpiredTime); err != nil {
			log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|appId:%s|intentId:%s|err:%+v",
				sid, r.appId, intentId, err)
			return err
		}
	}
	return nil
}

func (r *RefreshExample) getItemInfoInRedis(ctx context.Context, appId string,
	intentId string) (*db.IntentCorpusDetail, error) {
	sid := util.RequestID(ctx)
	key := r.getIntentCorpusDetailRdKey(appId, intentId)
	itemStr, err := db.GetRefreshInfosInRD(ctx, key, r.redis)
	if err != nil {
		log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|appId:%s|intentId:%s|err:%+v",
			sid, r.appId, intentId, err)
		return nil, err
	}
	var detail = db.IntentCorpusDetail{}
	err = json0.UnmarshalStr(itemStr, &detail)
	if err != nil {
		log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|UnmarshalStr|err:%+v", sid, err)
		return nil, err
	}
	return &detail, nil
}

func (r *RefreshExample) delItemInfoInRedis(ctx context.Context, appId, intentId string) error {
	sid := util.RequestID(ctx)
	key := r.getIntentCorpusDetailRdKey(appId, intentId)
	log.InfoContextf(ctx, "delItemInfoInRedis|intentId:%s|key:%s", intentId, key)
	if err := db.DelRefreshInfosInRD(ctx, key, r.redis); err != nil {
		log.ErrorContextf(ctx, "[NeedManHandleErr]|sid:%s|appId:%s|intentId:%s|err:%+v",
			sid, r.appId, intentId, err)
		return err
	}
	return nil
}

func (r *RefreshExample) getIntentCorpusDetailRdKey(appId, intentId string) string {
	if r.appId == "" {
		r.appId = appId
	}
	return GetRefreshExampleRedisKey(fmt.Sprintf("FAIL:%s:%s", r.appId, intentId))
}

// refreshExampleToVector 刷数据，同步向量库
func (r *RefreshExample) refreshExampleToVector(ctx context.Context,
	intentCorpusDetail *db.IntentCorpusDetail) error {
	vdb := vdao.NewDao()
	sid := util.RequestID(ctx)
	if r.appId == "" {
		return nil
	}
	sandboxGroupID, prodGroupID, err := vdb.GetIntentVectorGroupId(ctx, r.tx, r.appId)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "sid:%s|intentCorpusDetail:%+v", sid, intentCorpusDetail)
	if intentCorpusDetail != nil {
		// 保存到sandbox向量库 intentExamples
		if err = vdb.SaveCorpusVector(ctx, r.appId, intentCorpusDetail.IntentCorpusID, intentCorpusDetail.IntentID,
			intentCorpusDetail.Content, sandboxGroupID, intentCorpusDetail.Examples); err != nil {
			return err
		}

		// 获取examplesId
		eCorpusIDs := make([]string, 0)
		appInfo := &vector_db_manager.AppInfo{
			Biz:    config.GetMainConfig().VectorGroup.Biz,
			AppKey: r.appId,
			Secret: config.GetMainConfig().VectorGroup.Secret,
		}
		examples := *intentCorpusDetail.Examples
		for _, e := range examples {
			eCorpusIDs = append(eCorpusIDs, e.CorpusID)
		}
		maxIds := config.GetMainConfig().VectorGroup.OperationMaxIDs
		for _, ids := range types.SplitStringSlice(eCorpusIDs, maxIds) {
			corpusVectors, err := vdb.GetVectors(ctx, sid, sandboxGroupID, appInfo, ids)
			if err != nil {
				return err
			}
			// 发布到prod向量库
			if err = vdb.UpdateVectors(ctx, sid, prodGroupID, appInfo, corpusVectors); err != nil {
				return err
			}
		}
	}

	return nil
}

// GetRefreshExampleRedisKey 获取导入任务流程key
func GetRefreshExampleRedisKey(rk string) string {
	return fmt.Sprintf(RefreshExampleRedisKey, rk)
}
