// Package cron 定时任务相关
// @author: halelv
// @date: 2023/12/18 16:40
package cron

import (
	"context"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/LK4D4/trylock"
)

// TimerService timer logic interface
type TimerService interface {
	// CheckTaskFlowMigration 任务流迁移任务定时校验
	CheckTaskFlowMigration(ctx context.Context) error
}

// timerService time logic
type timerService struct {
	muMap sync.Map
}

// NewTimerService new timer logic
func NewTimerService() TimerService {
	return &timerService{
		muMap: sync.Map{},
	}
}

// lock hold timer lock
func (t *timerService) lock(key string) bool {
	lock := &trylock.Mutex{}
	value, ok := t.muMap.LoadOrStore(key, lock)
	if ok {
		lock = value.(*trylock.Mutex)
	}
	if lock.TryLock() {
		log.Infof("lock key: %s", key)
		return true
	}
	log.Infof("key: %s locked", key)
	return false
}

// unlock release timer lock
func (t *timerService) unlock(key string) {
	value, ok := t.muMap.Load(key)
	if ok {
		log.Infof("unlock key: %s", key)
		t.muMap.Delete(key)
		lock := value.(*trylock.Mutex)
		lock.Unlock()
	}
}
