// Package cron 定时任务相关
// @author: halelv
// @date: 2023/12/20 15:14
package cron

import (
	"context"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
)

// CheckTaskFlowMigration 任务流迁移任务定时校验
func (t *timerService) CheckTaskFlowMigration(ctx context.Context) error {
	ctx = cloudparam.NewContext(ctx, cloudparam.BaseParam{
		RequestId: idgenerator.NewUUID(),
	})
	log.WithContextFields(ctx, cloudparam.RequestId, cloudparam.GetApIParam(ctx, cloudparam.RequestId))

	log.InfoContextf(ctx, "CheckTaskFlowMigration...")

	lockKey := "CheckTaskFlowMigration"
	if !t.lock(lockKey) {
		log.InfoContextf(ctx, "%s is processing, wait next scheduler", lockKey)
		return nil
	}
	defer t.unlock(lockKey)

	wg := sync.WaitGroup{}
	errChan := make(chan error, 2)

	wg.Add(2)

	go func() {
		defer wg.Done()
		// 调度任务
		err := t.checkSchedulerTasks(trpc.CloneContext(ctx))
		if err != nil {
			errChan <- err
		}
	}()

	go func() {
		defer wg.Done()
		// 导入任务
		err := t.checkTaskFlowImportTasks(trpc.CloneContext(ctx))
		if err != nil {
			errChan <- err
		}
	}()

	wg.Wait()

	select {
	case err := <-errChan:
		log.ErrorContextf(ctx, "CheckTaskFlowMigration err:%v", err)
		return err
	default:
		log.InfoContextf(ctx, "CheckTaskFlowMigration success")
		return nil
	}
}

// checkSchedulerTasks 校验调度任务
func (t *timerService) checkSchedulerTasks(ctx context.Context) error {
	log.InfoContextf(ctx, "checkSchedulerTasks...")
	// 查询超过任务最大超时时间的任务
	alterTasks, err := scheduler.GetBotAvailableTasks(ctx, time.Now().Add(-1*config.GetMainConfig().TaskMaxTimeout))
	if err != nil {
		return err
	}

	if len(alterTasks) == 0 {
		log.InfoContextf(ctx, "checkSchedulerTasks success, no alterTasks, wait next scheduler")
		return nil
	}

	// 任务超过最大超时时间告警
	for _, task := range alterTasks {
		// 通过CLS收集[CheckTaskFlowMigration Alarm]错误日志告警
		log.ErrorContextf(ctx, "[CheckTaskFlowMigration Alarm] checkSchedulerTasks alter task:%v", *task)
	}
	log.InfoContextf(ctx, "checkSchedulerTasks success, len(alterTasks):%d", len(alterTasks))
	return nil
}

// checkTaskFlowImportTasks 校验导入任务
func (t *timerService) checkTaskFlowImportTasks(ctx context.Context) error {
	log.InfoContextf(ctx, "checkTaskFlowImportTasks...")
	// 查询超过任务最大超时时间的任务
	alterTasks, err := db.GetTaskFlowAvailableImports(ctx, time.Now().Add(-1*config.GetMainConfig().TaskMaxTimeout))
	if err != nil {
		return err
	}

	if len(alterTasks) == 0 {
		log.InfoContextf(ctx, "checkTaskFlowImportTasks success, no alterTasks, wait next scheduler")
		return nil
	}

	// 任务超过最大超时时间告警
	for _, task := range alterTasks {
		// 通过CLS收集[CheckTaskFlowMigration Alarm]错误日志告警
		log.ErrorContextf(ctx, "[CheckTaskFlowMigration Alarm] checkTaskFlowImportTasks alter task:%v", *task)
	}
	log.InfoContextf(ctx, "checkTaskFlowImportTasks success, len(alterTasks):%d", len(alterTasks))
	return nil
}
