package cron

import (
	"context"
	"errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	refresh_vector "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/corpus/refresh"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	"github.com/robfig/cron"
)

// RefreshJob 查询发布状态的job
type RefreshJob struct {
	ctx context.Context
}

// StartRefreshCronJob 刷示例问法
func StartRefreshCronJob() {
	if !config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.Enable {
		log.Info("StartRefreshCronJob|disable")
		return
	}
	log.Info("StartRefreshCronJob|enable")

	c := cron.New()
	spec := config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.Spec
	if len(spec) == 0 {
		spec = "02 */1 * * * *"
	}
	log.Infof("StartRefreshCronJob|spec:%s", spec)
	err := c.AddJob(spec, &RefreshJob{})
	if err != nil {
		log.Errorf("StartRefreshCronJob|err", err)
		return
	}
	go c.Start()
}

// Run cron jobs.
func (a *RefreshJob) Run() {
	if !config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.Enable {
		log.Info("StartRefreshCronJob|Run|disable")
		return
	}
	log.Infof("---------------- [StartRefreshCronJob|Start] ------------")
	startTime := time.Now() // 记录开始时间
	reqID := idgenerator.NewUUID()
	finishKey := refresh_vector.GetRefreshExampleRedisKey("RefreshExampleComplete")
	failAppIDKey := refresh_vector.GetRefreshExampleRedisKey("Fail_AppID")
	a.ctx = cloudparam.NewContext(trpc.BackgroundContext(), cloudparam.BaseParam{
		RequestId: reqID,
	})
	util.WithRequestID(a.ctx, reqID)
	tx := database.GetLLMRobotTaskGORM().WithContext(a.ctx).Debug()
	rdb := database.GetRedis()
	failRefreshAppIds := make([]string, 0)

	if config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.RetryAll {
		_ = rdb.Del(a.ctx, finishKey)
	}
	hasRefresh, _ := rdb.Get(a.ctx, finishKey).Result()
	log.InfoContextf(a.ctx, "sid:%s|StartRefreshCronJob,RefreshExample|hasRefresh:%+v", util.RequestID(a.ctx), hasRefresh)
	if string(hasRefresh) == refresh_vector.RefreshFinished { // 刷数据完成,不往后面走了
		return
	}

	// 获取刷失败的AppId
	AppIds, _ := rdb.LRange(a.ctx, failAppIDKey, 0, -1).Result()
	if len(AppIds) > 0 {
		for _, appId := range AppIds {
			if !util.StrInArray(appId, failRefreshAppIds) {
				failRefreshAppIds = append(failRefreshAppIds, appId)
			}
		}
	}
	log.WarnContextf(a.ctx, "[RefreshExample]|hasRefresh:%+v|len:%d|RetryRefreshFailAppIds:%+v",
		hasRefresh, len(failRefreshAppIds), failRefreshAppIds)

	// 只刷白名单机器人
	for i, appId := range config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.WhiteBotList {
		err := a.runBot(appId, tx, rdb, i)
		if err != nil {
			log.ErrorContextf(a.ctx, "sid:%s|appId:%s|RefreshExample,fail:%+v", reqID, appId, err)
			if !util.StrInArray(appId, failRefreshAppIds) {
				failRefreshAppIds = append(failRefreshAppIds, appId)
			}
		}
	}

	// 全量
	if config.GetMainConfig().TaskFlowConfig.RefreshExampleToVector.RefreshAllBot {
		var listAppIds []string
		var err error
		if string(hasRefresh) != refresh_vector.RefreshFinished && len(AppIds) > 0 {
			log.WarnContextf(a.ctx, "[RefreshExample]|len:%d|RetryRefreshFailAppIds:%+v", len(listAppIds), listAppIds)
			listAppIds = AppIds
		} else {
			listAppIds, err = db.GetRobotIdListFromCorpus(a.ctx, tx)
			if err != nil {
				log.ErrorContextf(a.ctx, "RefreshExample|GetRobotIdListFromCorpus|err:%+v", err)
				return
			}
		}

		log.InfoContextf(a.ctx, "RefreshExample|Run|listAppIds|len:%d", len(listAppIds))
		for i, appId := range listAppIds {
			if len(appId) == 0 { // 测试环境异常数据处理
				continue
			}
			err := a.runBot(appId, tx, rdb, i)
			log.InfoContextf(a.ctx, "RefreshExample|Run|appId:%s|[%d]|err:%+v", appId, i, err)
			if err != nil {
				log.ErrorContextf(a.ctx, "sid:%s|appId:%s|RefreshExample,fail,err:%+v", util.RequestID(a.ctx), appId, err)
				if !util.StrInArray(appId, failRefreshAppIds) {
					failRefreshAppIds = append(failRefreshAppIds, appId)
				}
				continue
			} else {
				if len(failRefreshAppIds) > 0 {
					// 再次刷如果之前有失败的appIds，重刷减掉刷的appId
					_, _ = rdb.LRem(a.ctx, finishKey, 0, appId).Result()
					failRefreshAppIds = util.RemoveValueFromStrArray(appId, failRefreshAppIds)
				}
			}
		}

		log.InfoContextf(a.ctx, "failRefreshAppIds:%d", len(failRefreshAppIds))
		if len(failRefreshAppIds) == 0 {
			// 刷数据完成, 设置数据完成
			a.setJobFinish(a.ctx, finishKey, rdb)
		} else {
			// 设置数据未刷完
			_, err = rdb.Set(a.ctx, finishKey, refresh_vector.RefreshFail, 0).Result()
			if err != nil {
				log.ErrorContextf(a.ctx, "[NeedManHandleErr]|sid:%s|Set,finishKey:%s|err:%+v",
					util.RequestID(a.ctx), finishKey, err)
				return
			}
			// 将 failRefreshAppIds 保存到 redis
			log.InfoContextf(a.ctx, "sid:%s|RefreshExample|not finish|failRefreshAppIds:%+v",
				util.RequestID(a.ctx), failRefreshAppIds)
			_, _ = rdb.Del(a.ctx, failAppIDKey).Result()
			for _, id := range failRefreshAppIds {
				err = rdb.LPush(a.ctx, failAppIDKey, id).Err()
				if err != nil {
					log.ErrorContextf(a.ctx, "[NeedManHandleErr]|sid:%s|Set,finishKey:%s|err:%+v",
						util.RequestID(a.ctx), finishKey, err)
					return
				}
			}
		}
	}
	// 处理刷失败的数据
	refresh_vector.RetryFailRefreshCorpus(a.ctx, tx, rdb)
	elapsedTime := time.Since(startTime) // 计算经过的时间
	log.InfoContextf(a.ctx, "RefreshExample|all robot finished|spend:%+v", elapsedTime)
}

func (a *RefreshJob) runBot(botBizId string, tx *gorm.DB, rdb redis.UniversalClient, i int) error {
	ctx := trpc.CloneContext(a.ctx)
	log.WithContextFields(ctx, "botBizId", botBizId)
	log.InfoContextf(ctx, "RefreshExample|Run|%s", time.Now().String())

	log.InfoContextf(ctx, "[RefreshExample]开始升级机器人:%d|%s", i, botBizId)

	// 获取分布式锁
	key := refresh_vector.GetRefreshExampleRedisKey(botBizId)
	locker := lock.NewDefaultLocker(key, botBizId, database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "RefreshExample|Run|locker.Lock Failed, err:%v", lockErr)
		return lockErr
	}
	if !ok {
		log.WarnContextf(ctx, "RefreshExample|Run|locker.Lock Failed|botBizId:%s|err:%v", botBizId, "upgrading")
		return errors.New("locker.Lock")
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "RefreshExample|Run|locker.UnLock Failed, err:%v", unLockErr)
		}
	}()
	// 设置数据未刷完
	finishKey := refresh_vector.GetRefreshExampleRedisKey("RefreshExampleComplete")
	refresh := refresh_vector.NewRefreshExample(botBizId, tx, rdb)
	err := refresh.RefreshAppExample(trpc.CloneContext(ctx))
	if err != nil {
		log.InfoContextf(ctx, "[RefreshExample]刷数据机器人失败|RefreshJob|err:%+v", err)
		_, _ = rdb.Set(a.ctx, finishKey, refresh_vector.RefreshFail, 0).Result()
		return err
	}
	log.InfoContextf(ctx, "[RefreshExample]升级机器人完成[%d]:%s", i, botBizId)
	return nil
}

func (a *RefreshJob) setJobFinish(ctx context.Context, finishKey string, rdb redis.UniversalClient) {
	ok, err := rdb.SetNX(a.ctx, finishKey, refresh_vector.RefreshFinished, 0).Result()
	if err != nil {
		log.ErrorContextf(a.ctx, "[NeedManHandleErr]|sid:%s|SetNX,finishKey:%s|err:%+v",
			util.RequestID(a.ctx), finishKey, err)
		return
	}
	if !ok {
		// 存在finishKey
		_, err = rdb.Set(a.ctx, finishKey, refresh_vector.RefreshFinished, 0).Result()
		if err != nil {
			log.ErrorContextf(a.ctx, "[NeedManHandleErr]|sid:%s|Set,finishKey:%s|err:%+v",
				util.RequestID(a.ctx), finishKey, err)
			return
		}
	}
	log.InfoContextf(a.ctx, "sid:%s|RefreshExample|hasRefresh:%+v",
		util.RequestID(a.ctx), rdb.Get(a.ctx, finishKey))
}
