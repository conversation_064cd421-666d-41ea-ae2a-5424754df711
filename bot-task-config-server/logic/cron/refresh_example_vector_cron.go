// bot-task-config-server
//
// @(#)refresh_example_vector_cron.go  星期一, 十月 14, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package cron

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	"github.com/robfig/cron"
	"gorm.io/gorm"
	"strings"
)

// RefreshExampleJob 查询发布状态的job
type RefreshExampleJob struct {
	ctx context.Context
}

// StartRefreshIntentExampleCronJob 刷示例问法
func StartRefreshIntentExampleCronJob() {
	log.Info("StartRefreshIntentExampleCronJob|disable",
		config.GetMainConfig().TaskFlowConfig.RefreshDelExampleToVector.Enable)
	if !config.GetMainConfig().TaskFlowConfig.RefreshDelExampleToVector.Enable {
		log.Info("StartRefreshIntentExampleCronJob|disable")
		return
	}

	c := cron.New()
	spec := config.GetMainConfig().TaskFlowConfig.RefreshDelExampleToVector.Spec
	if len(spec) == 0 {
		spec = "0 */2 * * * *"
	}
	log.Infof("StartRefreshIntentExampleCronJob|spec:%s", spec)
	err := c.AddJob(spec, &RefreshExampleJob{})
	if err != nil {
		log.Errorf("StartRefreshIntentExampleCronJob|err", err)
		return
	}
	go c.Start()
}

// Run ...
func (a *RefreshExampleJob) Run() {

	log.Infof("---------------- [StartRefreshIntentExampleCronJob] ------------")

	reqID := idgenerator.NewUUID()
	a.ctx = cloudparam.NewContext(trpc.BackgroundContext(), cloudparam.BaseParam{
		RequestId: reqID,
	})
	log.WithContextFields(a.ctx,
		cloudparam.RequestId, reqID,
		"upgrade", "refresh example to vector",
	)
	util.WithRequestID(a.ctx, reqID)

	// 全量
	if config.GetMainConfig().TaskFlowConfig.RefreshDelExampleToVector.RefreshAll {
		intentIds, err := db.GetIntentIdsByDeleteUnPublishedExample(a.ctx)
		if err != nil {
			log.ErrorContextf(a.ctx, "StartRefreshIntentExampleCronJob|err:%v", err)
			return
		}
		a.RunIntent(a.ctx, intentIds)
		return
	}

	// 只刷白名单
	intentIds := config.GetMainConfig().TaskFlowConfig.RefreshDelExampleToVector.WhiteIntentIdsList
	a.RunIntent(a.ctx, intentIds)

}

// RunIntent ...
func (a *RefreshExampleJob) RunIntent(ctx context.Context, intentIds []string) {
	var err error
	sid := util.RequestID(ctx)

	log.Infof("StartRefreshIntentExampleCronJob|intentIds:%+v", intentIds)
	// 获取未发布的示例问法
	rExaMap, robotIds, err := db.GetRobotExampleMap(ctx, intentIds)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshIntentExampleVectorDB fail, err:%+v", err)
		return
	}

	// 发布
	for _, rId := range robotIds {
		examples := rExaMap[rId]
		log.Infof("StartRefreshIntentExampleCronJob|robotId:%+v|%+v", rId, json0.Marshal2StringNoErr(examples))
		if err := RefreshIntentExampleVectorDB(ctx, rId, examples); err != nil {
			log.ErrorContextf(ctx, "sid:%s|RefreshIntentExampleVectorDB fail, err:%+v", sid, err)
			return
		}
	}
}

// RefreshIntentExampleVectorDB 刷新意图的向量发布  发布(以应用的维度)
func RefreshIntentExampleVectorDB(ctx context.Context, robotId string, examples []*entity.IntentCorpus) error {
	var err error
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotTaskProdGORM().WithContext(ctx).Debug()
	vp := vector.NewDao()
	dp := publish.NewDao()

	// 获取分布式锁
	key := getUpgradeTaskFlowLockKey(robotId)
	locker := lock.NewDefaultLocker(key, robotId, database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "StartRefreshIntentExampleCronJob"+
			"|Run|locker.Lock Failed, err:%v", lockErr)
		return lockErr
	}
	if !ok {
		log.WarnContextf(ctx, "StartRefreshIntentExampleCronJob"+
			"|Run|locker.Lock Failed|botBizId:%s|err:%v", robotId, "upgrading")
		return nil
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "StartRefreshIntentExampleCronJob|Run|locker.UnLock Failed, err:%v", unLockErr)
		}
	}()

	if err := db.Transaction(func(tx *gorm.DB) error {
		// 发布向量
		_, err = vp.PublishIntentExampleVector(ctx, robotId, examples)

		if err != nil && !strings.Contains(err.Error(), "group not exist") {
			return err
		}
		// 发布表数据
		err = dp.PublishIntentExample(ctx, tx, examples)
		if err != nil {
			return err
		}
		// 更改发布状态
		err = dp.UpdateIntentExamplesReleaseStatus(ctx, examples, "PUBLISHED")
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "sid:%s|StartRefreshIntentExampleCronJob fail, err:%+v", sid, err)
		return err
	}

	return nil
}
