// Package cron 定时任务相关
// @author: halelv
// @date: 2023/12/18 16:52
package cron

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// Scheduler 调度策略
type Scheduler struct {
}

// NewScheduler new
func NewScheduler() *Scheduler {
	return &Scheduler{}
}

// Schedule 互斥任务定时器； holdTime 默认1s
func (s *Scheduler) Schedule(serviceName string, newNode string, holdTime time.Duration) (nowNode string, err error) {
	ctx := context.TODO()
	key := "KEP_bot-task-config-server:" + serviceName
	res := database.GetRedis().SetNX(ctx, key, newNode, holdTime)
	if res.Val() {
		return newNode, nil
	}

	nowNode = database.GetRedis().Get(ctx, key).Val()
	log.Infof("schedule %s is locked, nowNode:%s", key, nowNode)
	return nowNode, fmt.Errorf("schedule %s is locked", key)
}
