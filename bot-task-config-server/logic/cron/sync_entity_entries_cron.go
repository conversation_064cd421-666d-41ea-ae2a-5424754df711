// bot-task-config-server
//
// @(#)sync_entity_entries_cron.go  星期二, 五月 21, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package cron

import (
	"context"
	"encoding/json"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"github.com/robfig/cron"
	"time"
)

const SyncEntityEntryRedisKey = "SYNC_ENTITY_ENTRIES"
const SyncEntityEntrySuccessKey = "SYNC_ENTITY_ENTRIES_SUCCESS"

// SyncEntityEntriesToRedisCronJob ...
type SyncEntityEntriesToRedisCronJob struct {
}

// StartSyncEntityEntriesToRedisCronJob 同步实体词条数据到redis
func StartSyncEntityEntriesToRedisCronJob() {

	if !config.GetMainConfig().SyncEntityEntriesToRedis.Enable {
		log.Info("SyncEntriesToRedis |disable")
		return
	}
	log.Infof("SyncEntriesToRedis,configs:%+v", config.GetMainConfig().SyncEntityEntriesToRedis)

	c := cron.New()
	spec := config.GetMainConfig().SyncEntityEntriesToRedis.Spec
	if len(spec) == 0 {
		spec = "0 */10 * * * *"
	}
	log.Infof("SyncEntriesToRedis|spec:%s", spec)
	err := c.AddFunc(spec, func() {
		SyncEntriesToRedis()
	})
	if err != nil {
		log.Errorf("SyncEntriesToRedis|err:%+v", err)
		return
	}
	go c.Start()
	log.Info("SyncEntriesToRedis CronJob Success")
}

// SyncEntriesToRedis 同步数据到redis
func SyncEntriesToRedis() {

	log.Infof("SyncEntriesToRedis run,time:%s", time.Now().Format("2006-01-02 15:04:05"))

	reqID := idgenerator.NewUUID()
	ctx := context.Background()
	util.WithRequestID(ctx, reqID)
	ctx = log.WithContextFields(ctx, "RequestID", reqID)

	// 兼容私有化从2.1升级到2.4刷数据逻辑, 定时任务只需要执成功执行一次既可以了
	syncedSuccess := database.GetRedis().Get(ctx, SyncEntityEntrySuccessKey).Val()
	if syncedSuccess == SyncEntityEntrySuccessKey {
		log.InfoContextf(ctx, "SyncEntriesToRedis SYNC_ENTITY_ENTRIES_SUCCESS 数据已经同步完成了！！！")
		return
	}

	log.InfoContextf(ctx, "---------------- [SyncEntriesToRedis]------------")

	// 获取分布式锁
	key := SyncEntityEntryRedisKey
	locker := lock.NewDefaultLocker(key, "sync_entry", database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "SyncEntriesToRedis|Run|locker.Lock Failed, err:%v", lockErr)
		return
	}
	if !ok {
		log.ErrorContextf(ctx, "SyncEntriesToRedis|Run|locker.Lock Failed|err:%v", "upgrading")
		return
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "SyncEntriesToRedis|Run|locker.UnLock Failed, err:%v", unLockErr)
		}
	}()
	log.InfoContextf(ctx, "SyncEntityEntriesToRedis ,after get local")

	// 全量
	if config.GetMainConfig().SyncEntityEntriesToRedis.SyncAllBot {
		log.InfoContextf(ctx, "[SyncEntriesToRedis] SyncAllBot")
		// Sandbox环境
		err := syncDataToRedis(ctx, db.SandboxEnv, "")
		if err != nil {
			return
		}

		// Product环境
		err = syncDataToRedis(ctx, db.ProductEnv, "")
		if err != nil {
			return
		}
	} else {
		log.InfoContextf(ctx, "[SyncEntriesToRedis] WhiteList :%+v",
			config.GetMainConfig().SyncEntityEntriesToRedis.WhiteBotList)
		// 只刷白名单机器人
		for _, botBizId := range config.GetMainConfig().SyncEntityEntriesToRedis.WhiteBotList {
			log.InfoContextf(ctx, "SyncEntriesToRedis,botBizId:%s", botBizId)
			err := syncDataToRedis(ctx, db.SandboxEnv, botBizId)
			if err != nil {
				return
			}
			// Product环境
			err = syncDataToRedis(ctx, db.ProductEnv, botBizId)
			if err != nil {
				return
			}
		}
	}

	// 设置一个标识， 表明刷数据成功了
	database.GetRedis().Set(ctx, SyncEntityEntrySuccessKey, SyncEntityEntrySuccessKey, 0)

	log.InfoContextf(ctx, "[SyncEntriesToRedis]升级完成")
}

// EntryValueInfo 词条信息
type EntryValueInfo struct {
	Value     string   `json:"Value"`
	AliasName []string `json:"AliasName"`
}

func syncDataToRedis(ctx context.Context, envName string, robotID string) error {
	var entityEntryInfo []*db.RobotEntityEntryInfo
	var err error
	entityEntryInfo, err = db.GetSandboxOrProductEntityEntriesInfo(ctx, robotID, envName)

	if err != nil {
		log.ErrorContextf(ctx, "SyncEntriesToRedis,GetEntityEntriesInfo Failed! err:%+v", err)
		return err
	}
	sandBoxEntryInfoMap := generateEntryInfo(entityEntryInfo)
	for robotId, entityInfos := range sandBoxEntryInfoMap {
		log.InfoContextf(ctx, "SyncEntriesToRedis,robotID:%+v,entityInfos:%+v", robotID, entityInfos)
		key := db.GetEntityEntriesKey(ctx, envName, robotId)
		values := make([]interface{}, 0)
		for entityID, entryInfos := range entityInfos {
			jsonData, err := json.Marshal(entryInfos)
			if err != nil {
				log.ErrorContextf(ctx, "SyncEntriesToRedis json.Marshal Failed! err:%+v", err)
				return err
			}
			values = append(values, entityID, string(jsonData))
		}
		err := database.GetRedis().HMSet(ctx, key, values...).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncEntriesToRedis SyncToRedis Failed!,key:%s,vals:%+v,err:%+v", key, values, err)
			return err
		}
		log.InfoContextf(ctx, "SyncEntriesToRedis,key:%s,values:%+v", key, values)
	}
	return nil
}

func generateEntryInfo(entries []*db.RobotEntityEntryInfo) map[string]map[string]map[string]EntryValueInfo {
	entryInfoMap := make(map[string]map[string]map[string]EntryValueInfo, 0)

	for _, entry := range entries {
		robotID := entry.RobotID
		entityID := entry.EntityID
		entryID := entry.EntryID
		entryValue := entry.EntryValue

		entryAlias := entry.EntryAlias
		entryAliasArr := []string{}
		_ = json.Unmarshal([]byte(entryAlias), &entryAliasArr)

		if _, ok := entryInfoMap[robotID]; !ok {
			entryInfoMap[robotID] = make(map[string]map[string]EntryValueInfo)
		}

		if _, ok := entryInfoMap[robotID][entityID]; !ok {
			entryInfoMap[robotID][entityID] = make(map[string]EntryValueInfo)
		}

		entryInfoMap[robotID][entityID][entryID] = EntryValueInfo{
			Value:     entryValue,
			AliasName: entryAliasArr,
		}
	}

	return entryInfoMap
}
