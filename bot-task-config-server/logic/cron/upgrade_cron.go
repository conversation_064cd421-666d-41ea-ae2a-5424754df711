package cron

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/taskflow/compatible"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	"github.com/robfig/cron"
)

// [升级v1.7对话树的协议到 v2.1]

const UpgradeTaskFlowRedisKey = "UPGRADE_21_TO_24:%s"

// StartUpgradeCronJob 升级对话树协议的定时任务
func StartUpgradeCronJob() {
	if !config.GetMainConfig().TaskFlowConfig.UpgradeProtoVersionFrom21To24.Enable {
		log.Info("StartUpgradeCronJob|disable")
		return
	}
	log.Info("StartUpgradeCronJob|enable")

	c := cron.New()
	spec := config.GetMainConfig().TaskFlowConfig.UpgradeProtoVersionFrom21To24.Spec
	if len(spec) == 0 {
		spec = "01 */1 * * * *"
	}
	log.Infof("StartUpgradeCronJob|spec:%s", spec)
	//job := newUpgradeJob()
	err := c.AddJob(spec, &UpgradeJob{})
	if err != nil {
		log.Errorf("StartUpgradeCronJob|err", err)
		return
	}
	go c.Start()
}

// UpgradeJob 查询发布状态的job
type UpgradeJob struct {
	ctx context.Context
}

//func newUpgradeJob() *UpgradeJob {
//	return &UpgradeJob{}
//}

// getUpgradeTaskFlowLockKey 获取导入任务流程key
func getUpgradeTaskFlowLockKey(botBizId string) string {
	return fmt.Sprintf(UpgradeTaskFlowRedisKey, botBizId)
}

// Run cron jobs.
func (a *UpgradeJob) Run() {
	if !config.GetMainConfig().TaskFlowConfig.UpgradeProtoVersionFrom21To24.Enable {
		log.Info("StartUpgradeCronJob|Run|disable")
		return
	}

	log.Infof("---------------- [upgrade21to24] ------------")

	reqID := idgenerator.NewUUID()
	a.ctx = cloudparam.NewContext(trpc.BackgroundContext(), cloudparam.BaseParam{
		RequestId: reqID,
	})
	log.WithContextFields(a.ctx,
		cloudparam.RequestId, reqID,
		"upgrade", "v2.1 --> V2.4",
	)
	util.WithRequestID(a.ctx, reqID)

	// 全量
	if config.GetMainConfig().TaskFlowConfig.UpgradeProtoVersionFrom21To24.UpgradeAllBot {
		//SELECT DISTINCT f_robot_id FROM t_robot_intent WHERE f_is_deleted = 0;
		list, err := db.GetRobotIDList(a.ctx)
		if err != nil {
			log.ErrorContextf(a.ctx, "UpgradeJob|Run|GetRobotIDList|err:%v", err)
			return
		}
		for i, botBizId := range list {
			log.InfoContextf(a.ctx, "UpgradeJob|Run|GetRobotIDList|[%d]", i)
			a.runBot(botBizId)
		}
		return
	}

	// 只刷白名单机器人
	for _, botBizId := range config.GetMainConfig().TaskFlowConfig.UpgradeProtoVersionFrom21To24.WhiteBotList {
		a.runBot(botBizId)
	}
}

func (a *UpgradeJob) runBot(botBizId string) {
	ctx := trpc.CloneContext(a.ctx)
	log.WithContextFields(ctx, "botBizId", botBizId)
	log.InfoContextf(ctx, "UpgradeJob|Run|%s", time.Now().String())

	log.InfoContextf(ctx, "[upgrade21to24]开始升级机器人: %s", botBizId)

	// 获取分布式锁
	key := getUpgradeTaskFlowLockKey(botBizId)
	locker := lock.NewDefaultLocker(key, botBizId, database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "UpgradeJob|Run|locker.Lock Failed, err:%v", lockErr)
		return
	}
	if !ok {
		log.WarnContextf(ctx, "UpgradeJob|Run|locker.Lock Failed|botBizId:%s|err:%v", botBizId, "upgrading")
		return
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "UpgradeJob|Run|locker.UnLock Failed, err:%v", unLockErr)
		}
	}()

	err := compatible.Upgrade_V24(trpc.CloneContext(ctx), botBizId)
	if err != nil {
		log.InfoContextf(ctx, "[upgrade21to24]升级机器人失败|UpgradeJob|err:%+v", err)
		return
	}

	log.InfoContextf(ctx, "[upgrade21to24]升级机器人完成 :%s", botBizId)

}
