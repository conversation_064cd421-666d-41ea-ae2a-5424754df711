// bot-task-config-server
//
// @(#)refresh_workflow_vector_corn.go  星期三, 十二月 25, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"github.com/robfig/cron"
	"gorm.io/gorm"
)

// RefreshWorkflowVectorJob 处理工作流向量
type RefreshWorkflowVectorJob struct {
	ctx context.Context
}

// StartRefreshWorkflowVectorJob 刷示工作流向量
func StartRefreshWorkflowVectorJob() {
	log.Info("StartRefreshWorkflowVectorJob|disable", config.GetMainConfig().RefreshWorkflow.Vector.Enable)
	if !config.GetMainConfig().RefreshWorkflow.Vector.Enable {
		log.Info("StartRefreshWorkflowVectorJob|disable")
		return
	}

	c := cron.New()
	spec := config.GetMainConfig().RefreshWorkflow.Vector.Spec
	if len(spec) == 0 {
		spec = "0 */2 * * * *"
	}
	log.Infof("RefreshWorkflowVectorJob|spec:%s", spec)
	err := c.AddJob(spec, &RefreshWorkflowVectorJob{})
	if err != nil {
		log.Errorf("RefreshWorkflowVectorJob|err", err)
		return
	}
	go c.Start()
}

// Run ...
func (a *RefreshWorkflowVectorJob) Run() {
	log.Infof("---------------- [RefreshWorkflowVectorJob] ------------")
	reqID := idgenerator.NewUUID()
	a.ctx = cloudparam.NewContext(trpc.BackgroundContext(), cloudparam.BaseParam{
		RequestId: reqID,
	})
	log.WithContextFields(a.ctx,
		cloudparam.RequestId, reqID,
		"upgrade", "refresh workflow data to vector",
	)
	util.WithRequestID(a.ctx, reqID)

	// 全量
	if config.GetMainConfig().RefreshWorkflow.Vector.RefreshAll {
		robotIds, err := getAllWaitDebugWorkflowRobotIds(a.ctx)
		if err != nil {
			log.ErrorContextf(a.ctx, "RefreshWorkflowVectorJob|getAllWaitDebugWorkflowRobotIds:err:%+v", err)
			return
		}
		log.DebugContextf(a.ctx, "RefreshWorkflowVectorJob|getAllWaitDebugWorkflowRobotIds:%+v", robotIds)
		for _, id := range robotIds {
			a.RunRobotWorkflow(a.ctx, id)
		}
	}

	// 只刷白名单
	robotIds := config.GetMainConfig().RefreshWorkflow.Vector.WhiteRobotIdsList
	if len(robotIds) > 0 {
		log.DebugContextf(a.ctx, "RefreshWorkflowVectorJob|WhiteRobotIdsList:%+v", robotIds)
		for _, robotId := range robotIds {
			a.RunRobotWorkflow(a.ctx, robotId)
		}
	}
	log.DebugContextf(a.ctx, "RefreshWorkflowVectorJob|end")
}

// RunRobotWorkflow ...
func (a *RefreshWorkflowVectorJob) RunRobotWorkflow(ctx context.Context, robotId string) {
	var err error
	sid := util.RequestID(ctx)
	ids := make([]string, 0)

	// 获取原始草稿态的工作流及其IDs
	draftWorkflowIds := make([]string, 0)
	// 已发布-草稿态
	publishedDraftWorkflowIds := make([]string, 0)

	workflows, err := db.GetWorkflowsByRobotId(ctx, robotId)
	for _, v := range workflows {
		if v.WorkflowState == entity.WorkflowStateDraft {
			draftWorkflowIds = append(draftWorkflowIds, v.WorkflowID)
		}

		if v.WorkflowState == entity.WorkflowStatePublishedDraft {
			publishedDraftWorkflowIds = append(publishedDraftWorkflowIds, v.WorkflowID)
		}
	}

	// 获取对应的示例问法
	draftExams, err := db.GetFlowExampleByFlowIds(ctx, draftWorkflowIds)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshWorkflowVectorJob|GetFlowExampleByFlowIds|err:%+v", err)
	}
	draftExamsIds := make([]string, 0)
	if len(draftExams) > 0 {
		for _, v := range draftExams {
			draftExamsIds = append(draftExamsIds, v.ExampleID)
		}
	}

	vdb := vdao.NewDao()
	useModelInfo := config.GetUsingVectorModelInfo(ctx)
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: robotId,
		Secret: useModelInfo.Secret,
	}
	sandboxGroupID := fmt.Sprintf("%s-workflow-%s-%s",
		useModelInfo.Biz, "sandbox", robotId)
	suffix := useModelInfo.GroupSuffix
	if len(suffix) > 0 {
		sandboxGroupID = fmt.Sprintf("%s-%s", sandboxGroupID, suffix)
	}

	// 处理原始草稿态
	ids = append(ids, draftWorkflowIds...)
	ids = append(ids, draftExamsIds...)
	if len(types.Unique(ids)) > 0 {
		for _, is := range types.SplitStringSlice(types.Unique(ids),
			useModelInfo.OperationMaxIDs) {
			err = vdb.DeleteVectors(ctx, sid, sandboxGroupID, appInfo, is)
			if err != nil && !strings.Contains(err.Error(), "group not exist") {
				log.ErrorContextf(ctx, "RefreshWorkflowVectorJob|DeleteVectors fail sandboxGroupID:%+s|ids:%+v|err:%+v",
					sandboxGroupID, is, err)
			}
		}

	}

	log.DebugContextf(ctx, "RefreshWorkflowVectorJob|publishedDraftWorkflowIds:%+v", publishedDraftWorkflowIds)
	// 处理已发布-草稿态
	for _, flowId := range publishedDraftWorkflowIds {
		err := refreshUpdateWorkflowCorpusToVector(ctx, robotId, flowId)
		if err != nil {
			log.ErrorContextf(ctx, "RefreshWorkflowVectorJob|err:%+v", err)
		}
		time.Sleep(300 * time.Millisecond)
	}
}

// getAllWaitDebugWorkflowRobotIds ...
func getAllWaitDebugWorkflowRobotIds(ctx context.Context) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var robotIds []string
	if err := db.Table(entity.Workflow{}.TableName()).Select("DISTINCT f_robot_id").
		Where("f_is_deleted=0 AND f_flow_state IN ?",
			[]string{entity.WorkflowStateDraft, entity.WorkflowStatePublishedDraft}).
		Scan(&robotIds).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByFlowState|err:%+v", err)
		return robotIds, err
	}
	return types.Unique(robotIds), nil
}

// refreshUpdateWorkflowCorpusToVector 刷工作流向量
func refreshUpdateWorkflowCorpusToVector(ctx context.Context, robotID, workflowId string) error {
	datadb := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := datadb.Transaction(func(tx *gorm.DB) error {
		var examples []*entity.WorkflowExample
		var workflow *entity.Workflow
		var err error
		vdb := vdao.NewDao()
		sid := util.RequestID(ctx)

		// 1. 获取工作流的信息
		workflow, err = db.GetWorkflowDetailTx(ctx, tx, workflowId, robotID)
		if err != nil {
			return err
		}
		workflow.IsEnable = true
		// 2. 获取工作流下面示例问法的信息
		examples, err = db.ListFlowExamByBootIdTx(ctx, tx, workflowId, robotID)
		if err != nil {
			return err
		}
		// 获取sandboxGroupID
		log.DebugContextf(ctx, "RefreshWorkflowVectorJob|robotID:%s|workflow:%+v",
			robotID, workflow)
		err = vdb.SaveWorkflowToVector(ctx, tx, robotID, examples, workflow)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|SaveWorkflowCorpusVector,err:%s", sid, err)
			return err
		}
		workflowStatus := vdao.GetWfVectorEnableByFlowState(workflow.WorkflowState, workflow.IsEnable)
		if err := vdao.WorkflowEnableSetRedis(ctx, robotID, workflowId, entity.SandboxEnv, workflowStatus); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}
