// KEP.bot-task-config-server
//
// @(#)doc.go  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

/*
Package logic is logic layer top-level directory.

Service Layer is an abstraction over domain logic. It defines  an application's boundary with a layer of services that
establishes a set of available operations and coordinates the application's response in each operation.

The DAO layer's main goal is to handle the details of the persistence mechanism. While the logic layer stands on top
of it to handle business requirements.
*/
package logic
