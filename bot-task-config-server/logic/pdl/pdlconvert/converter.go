package pdlconvert

import (
	"context"
	"errors"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"golang.org/x/exp/slices"
)

// Converter 转换PDL接口
type Converter interface {
	Convert(ctx context.Context, workflow *KEP_WF.Workflow, paramInfos []*KEP_WF.ParameterInfo,
		varParams []*entity.VarParams) (*PDLContent, *KEP_WF.PDLToolsInfo, error)
}

// RuleConverter 规则转换 基于工程方式生成procedure伪代码
type RuleConverter struct {
	// 参数提取信息
	parameterMap map[string]*KEP_WF.ParameterInfo
	// API参数(变量参数)
	varParamMap map[string]*entity.VarParams
	// 画布节点map
	nodeMap map[string]*KEP_WF.WorkflowNode
	// 开始节点
	startNode *KEP_WF.WorkflowNode

	// 大模型/工具/插件/代码节点输出变量名称映射 nodeID -> {Output_1}
	varNameMap map[string]string
	// 抽取函数的节点 nodeID -> {FuncNode}
	funcNodeMap map[string]FuncNode
	// 按序输出函数ID的实现
	funcNodeIDs []string

	// answers内容
	answers []PDLAnswer

	// 节点对应已转换的伪代码
	nodePseudocodeMap map[string][]string
}

// NewRuleConverter .
func NewRuleConverter() Converter {
	return &RuleConverter{
		parameterMap:      make(map[string]*KEP_WF.ParameterInfo),
		varParamMap:       make(map[string]*entity.VarParams),
		nodeMap:           make(map[string]*KEP_WF.WorkflowNode),
		varNameMap:        make(map[string]string),
		funcNodeMap:       make(map[string]FuncNode),
		nodePseudocodeMap: make(map[string][]string),
	}
}

// FuncNode 抽取成伪代码函数的节点
type FuncNode struct {
	NodeID     string
	FuncName   string
	Parameters []string
}

// 是否为TOOL/PLUGIN/LLM/CODE_EXECUTOR这四种节点
func isAPINode(nodeType KEP_WF.NodeType) bool {
	return nodeType == KEP_WF.NodeType_TOOL || nodeType == KEP_WF.NodeType_PLUGIN ||
		nodeType == KEP_WF.NodeType_CODE_EXECUTOR || nodeType == KEP_WF.NodeType_LLM
}

// Convert 转换画布为pdl内容
func (c *RuleConverter) Convert(ctx context.Context, workflow *KEP_WF.Workflow, paramInfos []*KEP_WF.ParameterInfo,
	varParams []*entity.VarParams) (*PDLContent, *KEP_WF.PDLToolsInfo, error) {
	for _, param := range paramInfos {
		c.parameterMap[param.GetParameterId()] = param
	}
	for _, varParam := range varParams {
		c.varParamMap[varParam.VarID] = varParam
	}
	//var startNode *KEP_WF.WorkflowNode
	var varIndex int32
	for _, node := range workflow.GetNodes() {
		if node != nil {
			if node.GetNodeType() == KEP_WF.NodeType_START {
				c.startNode = node
			}
			c.nodeMap[node.GetNodeID()] = node
		}
		if isAPINode(node.GetNodeType()) {
			// API节点替换输出变量名称
			varIndex++
			c.varNameMap[node.GetNodeID()] = fmt.Sprintf("Output_%d", varIndex)
		}
	}
	if c.startNode == nil {
		log.ErrorContextf(ctx, "start node is nil, workflowID:%s", workflow.GetWorkflowID())
		return nil, nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonWorkflowIllegal))
	}
	// 抽取函数的处理
	if config.GetMainConfig().PDLConvertConfig.RuleConvert.ExtractFuncEnable {
		c.funcNodeMap = c.extractFuncNodes(ctx, c.startNode, c.nodeMap)
	}
	// 生成伪代码
	pseudocode, err := c.convertWorkflowNode(ctx, c.startNode, 0)
	if err != nil {
		log.ErrorContextf(ctx, "convertWorkflowNode error: %s, workflowID:%s", err.Error(), workflow.GetWorkflowID())
		return nil, nil, err
	}
	// 函数的伪代码
	funcPseudocodeList := make([]string, 0)
	for len(c.funcNodeIDs) > 0 {
		funcNodeID := c.funcNodeIDs[0]
		c.funcNodeIDs = c.funcNodeIDs[1:]
		if funcNode, ok := c.funcNodeMap[funcNodeID]; ok {
			delete(c.funcNodeMap, funcNodeID)
			funcPseudocode, err := c.genFuncDefCode(ctx, &funcNode)
			if err != nil {
				log.ErrorContextf(ctx, "genFuncDefCode error: %s, workflowID:%s", err.Error(), workflow.GetWorkflowID())
				return nil, nil, err
			}
			funcPseudocodeList = append(funcPseudocodeList, funcPseudocode...)
		}
	}
	pseudocode = append(funcPseudocodeList, pseudocode...)

	slots := genSlots(paramInfos)
	procedure := genProcedure(pseudocode)
	pdlContent := &PDLContent{
		Name:      workflow.GetWorkflowName(),
		Desc:      workflow.GetWorkflowDesc(),
		Slots:     slots,
		APIs:      genAPIs(c.startNode, c.nodeMap),
		Answers:   c.answers,
		Procedure: CustomString(procedure),
	}
	// 工具信息 bfs排序
	bfsSortNodes := WFBFSSort(c.startNode, c.nodeMap)
	pdlToolsInfo := genPDLToolsInfo(ctx, bfsSortNodes)
	log.DebugContextf(ctx, "rule convert pdlContent:%+v, pdlToolsInfo:%+v", pdlContent, pdlToolsInfo)
	return pdlContent, pdlToolsInfo, nil
}

// genProcedure 拼接每行伪代码输出Procedure
func genProcedure(pseudocode []string) string {
	var builder strings.Builder
	for i, line := range pseudocode {
		if i > 0 {
			builder.WriteString("\n")
		}
		builder.WriteString(line)
	}
	return builder.String()
}

func genSlots(paramInfos []*KEP_WF.ParameterInfo) []PDLSlot {
	slots := make([]PDLSlot, 0, len(paramInfos))
	for _, paramInfo := range paramInfos {
		desc := paramInfo.Desc
		if len(paramInfo.GetCorrectExamples()) > 0 {
			desc += fmt.Sprintf("\n正确: [%s]", strings.Join(paramInfo.GetCorrectExamples(), ","))
		}
		if len(paramInfo.GetIncorrectExamples()) > 0 {
			desc += fmt.Sprintf("\n错误: [%s]", strings.Join(paramInfo.GetIncorrectExamples(), ","))
		}
		slot := PDLSlot{
			Name: paramInfo.Name,
			Desc: desc,
			Type: strings.ToLower(paramInfo.Type),
		}
		slots = append(slots, slot)
	}
	return slots
}

func genAPIs(startNode *KEP_WF.WorkflowNode, nodeMap map[string]*KEP_WF.WorkflowNode) []PDLApi {
	apis := make([]PDLApi, 0)
	sortNodes := WFTopologicalSort(startNode, nodeMap)
	parentIDsMap := genParentIDsMap(sortNodes)
	// 每个节点对应的前置工具节点
	preToolNodeIDsMap := make(map[string][]string)
	for _, node := range sortNodes {
		parentIDs := parentIDsMap[node.GetNodeID()]
		if len(parentIDs) == 0 {
			continue
		} else if len(parentIDs) == 1 {
			// 单个父节点
			parentID := parentIDs[0]
			parentNode := nodeMap[parentID]
			// 父节点是工具节点或插件节点 则取其为前置依赖；否则则继承父节点的前置依赖
			if parentNode.GetNodeType() == KEP_WF.NodeType_TOOL || parentNode.GetNodeType() == KEP_WF.NodeType_PLUGIN {
				preToolNodeIDsMap[node.GetNodeID()] = []string{parentID}
			} else {
				preToolNodeIDsMap[node.GetNodeID()] = preToolNodeIDsMap[parentID]
			}
		} else {
			var preToolNodeIDs []string
			for _, parentID := range parentIDs {
				parentNode := nodeMap[parentID]
				// 父节点是工具节点或插件节点 则取其为前置依赖；否则则继承父节点的前置依赖
				if parentNode.GetNodeType() == KEP_WF.NodeType_TOOL || parentNode.GetNodeType() == KEP_WF.NodeType_PLUGIN {
					preToolNodeIDs = append(preToolNodeIDs, parentID)
				} else {
					preToolNodeIDs = append(preToolNodeIDs, preToolNodeIDsMap[parentID]...)
				}
			}
			// 不找最近公共祖先，去重后都作为前置依赖，相当于or的关系
			uniqPreToolNodeIDs := types.Unique(preToolNodeIDs)
			preToolNodeIDsMap[node.GetNodeID()] = uniqPreToolNodeIDs
		}
	}
	for _, node := range sortNodes {
		nodeType := node.GetNodeType()
		if !isAPINode(nodeType) {
			continue
		}
		// LLM/CODE 节点的信息仅仅注册name到apis, 但不计算precondition; TOOL/PLUGIN需要计算precondition
		precondition := make([]string, 0)
		if nodeType == KEP_WF.NodeType_TOOL || nodeType == KEP_WF.NodeType_PLUGIN {
			if preToolNodeIDs, ok := preToolNodeIDsMap[node.GetNodeID()]; ok {
				for _, preToolNodeID := range preToolNodeIDs {
					if preNode, ok2 := nodeMap[preToolNodeID]; ok2 {
						precondition = append(precondition, preNode.GetNodeName())
					}
				}
			}
		}

		apis = append(apis, PDLApi{
			Name:         node.GetNodeName(),
			Precondition: precondition,
		})
	}
	return apis
}

// genParentIDsMap 遍历画布节点 生成每个节点ID对应的父节点ID的map
func genParentIDsMap(sortNodes []*KEP_WF.WorkflowNode) map[string][]string {
	parentIDsMap := make(map[string][]string)
	// 使用排序后的结果，不用map，保持多次处理的顺序一致性
	for _, node := range sortNodes {
		for _, nextNodeID := range getNextNodeIDs(node) {
			parentIDsMap[nextNodeID] = append(parentIDsMap[nextNodeID], node.GetNodeID())
		}
	}
	return parentIDsMap
}

// getNextNodeIDs 获取节点后继节点列表，包括条件节点的多个分支
func getNextNodeIDs(node *KEP_WF.WorkflowNode) []string {
	var allNextNodeIDs []string
	if node.GetNodeType() == KEP_WF.NodeType_LOGIC_EVALUATOR {
		for _, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
			allNextNodeIDs = append(allNextNodeIDs, group.GetNextNodeIDs()...)
		}
	} else {
		allNextNodeIDs = node.GetNextNodeIDs()
	}
	return types.Unique(allNextNodeIDs)
}

func genIndent(indentCnt int) string {
	indent := ""
	for i := 0; i < indentCnt; i++ {
		indent += "    "
	}
	return indent
}

// convertWorkflowNode 转换节点 生成伪代码（多行）
func (c *RuleConverter) convertWorkflowNode(ctx context.Context, node *KEP_WF.WorkflowNode, indentCnt int) ([]string,
	error) {
	if funcNode, ok := c.funcNodeMap[node.GetNodeID()]; ok {
		// 抽取函数的节点 转成 func1(xxx)的调用
		code := genFuncCallCode(&funcNode)
		pseudocode := []string{fmt.Sprintf("%s%s", genIndent(indentCnt), code)}
		if !slices.Contains(c.funcNodeIDs, node.GetNodeID()) {
			c.funcNodeIDs = append(c.funcNodeIDs, node.GetNodeID())
		}
		return pseudocode, nil
	}
	pseudocode, ok := c.nodePseudocodeMap[node.GetNodeID()]
	if !ok {
		switch node.NodeType {
		// 开始结束节点不做处理
		case KEP_WF.NodeType_START, KEP_WF.NodeType_END:
		case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
			pseudocode = c.convertParameterExtractorNode(node)
		case KEP_WF.NodeType_LLM:
			pseudocode = c.convertLLMNode(node)
		case KEP_WF.NodeType_TOOL:
			pseudocode = c.convertToolNode(node)
		case KEP_WF.NodeType_PLUGIN:
			pseudocode = c.convertPluginNode(node)
		case KEP_WF.NodeType_CODE_EXECUTOR:
			pseudocode = c.convertCodeExecutorNode(node)
		case KEP_WF.NodeType_ANSWER:
			pseudocode = c.convertAnswerNode(node)
		case KEP_WF.NodeType_LOGIC_EVALUATOR:
			var err error
			pseudocode, err = c.convertLogicEvaluatorNode(ctx, node)
			if err != nil {
				return nil, err
			}
		default:
			// 不支持的节点类型
			log.InfoContextf(ctx, "unsupported node type: %s", node.NodeType)
			return nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonUnsupportedNode))
		}

		c.nodePseudocodeMap[node.GetNodeID()] = pseudocode
	}
	nextNodeIDs := types.Unique(node.GetNextNodeIDs())
	if len(nextNodeIDs) > 1 {
		// 并行分支 不支持
		log.InfoContextf(ctx, "more than one next node: %s", node.GetNodeID())
		return nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonParallelBranches))
	} else if len(nextNodeIDs) == 1 {
		nextNodeID := node.GetNextNodeIDs()[0]
		if nextNode, ok := c.nodeMap[nextNodeID]; ok {
			subPseudocode, err := c.convertWorkflowNode(ctx, nextNode, 0)
			if err != nil {
				return nil, err
			}
			pseudocode = append(pseudocode, subPseudocode...)
		}
	}
	// 拼上缩进
	intent := genIndent(indentCnt)
	newPseudocode := make([]string, 0, len(pseudocode))
	for _, line := range pseudocode {
		newPseudocode = append(newPseudocode, fmt.Sprintf("%s%s", intent, line))
	}
	return newPseudocode, nil
}

// convertParameterExtractorNode 转换ParameterExtractor节点为伪代码
func (c *RuleConverter) convertParameterExtractorNode(node *KEP_WF.WorkflowNode) []string {
	paramNames := make([]string, 0)
	for _, parameter := range node.GetParameterExtractorNodeData().GetParameters() {
		paramID := parameter.GetRefParameterID()
		if param, ok := c.parameterMap[paramID]; ok {
			paramNames = append(paramNames, param.GetName())
		}
	}
	// 同回复节点的处理
	inputParamNames := make([]string, 0)
	// 提示词作为描述
	userConstraint := node.GetParameterExtractorNodeData().GetUserConstraint()
	for _, input := range node.GetInputs() {
		paramName := input.GetName()
		inputParamName := c.transInput(input.GetInput(), false)
		inputParamNames = append(inputParamNames, inputParamName)
		oldName := fmt.Sprintf("{{%s}}", paramName)
		newName := fmt.Sprintf("{{%s}}", inputParamName)
		// 将提示词的变量名称替换实际名称
		userConstraint = strings.Replace(userConstraint, oldName, newName, -1)
	}
	c.answers = append(c.answers, PDLAnswer{
		Name: node.GetNodeName(),
		Desc: userConstraint,
	})
	return []string{c.transFuncPseudocode("ANSWER", node.GetNodeName(), inputParamNames, paramNames)}
	//return []string{fmt.Sprintf("[%s] = ANSWER.请用户提供必要信息()", strings.Join(paramNames, ","))}
}

// convertLLMNode 转换LLM节点
func (c *RuleConverter) convertLLMNode(node *KEP_WF.WorkflowNode) []string {
	inputParamNames := make([]string, 0)
	for _, input := range node.GetInputs() {
		// 引用开始节点的输出变量也过滤掉
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT &&
			input.GetInput().GetReference().GetNodeID() != c.startNode.GetNodeID() {
			paramName := c.transInputReferenceOutput(input.GetInput())
			inputParamNames = append(inputParamNames, paramName)
		}
	}
	varName := c.varNameMap[node.GetNodeID()]
	return []string{c.transFuncPseudocode("TOOL", node.GetNodeName(), inputParamNames, []string{varName})}
}

// convertToolNode 转换工具节点
func (c *RuleConverter) convertToolNode(node *KEP_WF.WorkflowNode) []string {
	return c.convertToolNodeData(node.GetNodeID(), node.GetNodeName(), node.GetToolNodeData(), node.GetOutputs())
}

// convertPluginNode 转换插件节点
func (c *RuleConverter) convertPluginNode(node *KEP_WF.WorkflowNode) []string {
	return c.convertToolNodeData(node.GetNodeID(), node.GetNodeName(), node.GetPluginNodeData().GetToolInputs(), node.GetOutputs())
}

// convertToolNodeData 工具和插件节点处理类似，封装统一处理
func (c *RuleConverter) convertToolNodeData(nodeID, nodeName string, toolNodeData *KEP_WF.ToolNodeData, outputs []*KEP_WF.OutputParam) []string {
	requestParams := make([]*KEP_WF.ToolNodeData_RequestParam, 0)
	requestParams = append(requestParams, toolNodeData.GetHeader()...)
	requestParams = append(requestParams, toolNodeData.GetQuery()...)
	requestParams = append(requestParams, toolNodeData.GetBody()...)
	inputParamNames := make([]string, 0)
	for _, param := range requestParams {
		// 非引用变量 如系统变量、API参数和固定值则过滤掉; 引用开始节点的输出变量也过滤掉
		if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT &&
			param.GetInput().GetReference().GetNodeID() != c.startNode.GetNodeID() {
			paramName := c.transInputReferenceOutput(param.GetInput())
			inputParamNames = append(inputParamNames, paramName)
		}
	}
	/*outputParamNames := make([]string, 0)
	for _, output := range outputs {
		for _, property := range output.GetProperties() {
			outputParamNames = append(outputParamNames, property.GetTitle())
		}
	}*/
	// 输出变量统一为Output_x
	varName := c.varNameMap[nodeID]
	return []string{c.transFuncPseudocode("TOOL", nodeName, inputParamNames, []string{varName})}
}

// convertCodeExecutorNode 转换代码节点
func (c *RuleConverter) convertCodeExecutorNode(node *KEP_WF.WorkflowNode) []string {
	// 代码节点处理跟大模型节点一致
	return c.convertLLMNode(node)
}

func (c *RuleConverter) convertAnswerNode(node *KEP_WF.WorkflowNode) []string {
	inputParamNames := make([]string, 0)
	answer := node.GetAnswerNodeData().GetAnswer()
	for _, input := range node.GetInputs() {
		// 回复节点的变量名称
		paramName := input.GetName()
		inputParamName := c.transInput(input.GetInput(), false)
		inputParamNames = append(inputParamNames, inputParamName)
		oldName := fmt.Sprintf("{{%s}}", paramName)
		newName := fmt.Sprintf("{{%s}}", inputParamName)
		// 将回复内容里面的变量名称替换实际名称
		answer = strings.Replace(answer, oldName, newName, -1)
	}
	c.answers = append(c.answers, PDLAnswer{
		Name: node.GetNodeName(),
		Desc: answer,
	})
	return []string{c.transFuncPseudocode("ANSWER", node.GetNodeName(), inputParamNames, nil)}
}

// transFuncPseudocode 转换方法调用的伪代码
// 如：[invoicing_method] = API.查询开票方式([订单编号])
func (c *RuleConverter) transFuncPseudocode(prefix, funcName string, inputParams, outputParams []string) string {
	input := ""
	if len(inputParams) > 0 {
		input = fmt.Sprintf("[%s]", strings.Join(inputParams, ","))
	}
	output := ""
	if len(outputParams) > 0 {
		output = fmt.Sprintf("[%s]", strings.Join(outputParams, ","))
	}
	if output != "" {
		return fmt.Sprintf("%s = %s.%s(%s)", output, prefix, funcName, input)
	} else {
		return fmt.Sprintf("%s.%s(%s)", prefix, funcName, input)
	}
}

// convertLogicEvaluatorNode 转换LogicEvaluator节点
func (c *RuleConverter) convertLogicEvaluatorNode(ctx context.Context, node *KEP_WF.WorkflowNode) ([]string, error) {
	var pseudocode []string
	// 组装条件
	for i, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
		var line string
		if group.GetLogical() != nil {
			condExpression := c.buildCondExpression(group.GetLogical())
			if condExpression == "" {
				log.ErrorContextf(ctx, "condition expression is empty, nodeID:%s", node.GetNodeID())
				return nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonWorkflowIllegal))
			}
			exprLen := len([]rune(condExpression))
			if exprLen > config.GetMainConfig().PDLConvertConfig.CondExpressLenLimit {
				// 条件表达式长度超过限制
				log.InfoContextf(ctx, "condition expression length[%d] exceeded, condExpression:%s", exprLen, condExpression)
				return nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonExpressLenExceeded))
			}
			if i == 0 {
				line = fmt.Sprintf("if %s:", condExpression)
			} else {
				line = fmt.Sprintf("elif %s:", condExpression)
			}
		} else {
			line = "else:"
		}
		pseudocode = append(pseudocode, line)
		nextNodeIDs := types.Unique(group.GetNextNodeIDs())
		// 条件表达式后增加缩进
		if len(nextNodeIDs) > 1 {
			log.InfoContextf(ctx, "more than one next node: %s", node.GetNodeID())
			return nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonParallelBranches))
		} else if len(nextNodeIDs) == 1 {
			nextNodeID := group.GetNextNodeIDs()[0]
			if nextNode, ok := c.nodeMap[nextNodeID]; ok {
				subPseudocode, err := c.convertWorkflowNode(ctx, nextNode, 1)
				if err != nil {
					return nil, err
				}
				pseudocode = append(pseudocode, subPseudocode...)
			}
		}
	}
	return pseudocode, nil
}

// buildCondExpression 构建条件表达式
func (c *RuleConverter) buildCondExpression(logical *KEP_WF.LogicalExpression) string {
	if logical.GetLogicalOperator() == KEP_WF.LogicalExpression_UNSPECIFIED {
		comparison := logical.GetComparison()
		var left, right, op string
		isValueArray := false
		if comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_IN ||
			comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN {
			isValueArray = true
		}
		if comparison.GetLeft() != nil {
			left = c.transInput(comparison.GetLeft(), isValueArray)
		}
		op = transformOperator(comparison.GetOperator())
		if comparison.GetRight() != nil && comparison.GetOperator() != KEP_WF.LogicalExpression_ComparisonExpression_IS_SET &&
			comparison.GetOperator() != KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET {
			right = c.transInput(comparison.GetRight(), isValueArray)
		}
		if comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS ||
			comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS {
			// 包含关系转成in 并互换左右侧位置
			left, right = right, left
		}
		var expression string
		if right != "" {
			expression = fmt.Sprintf("%s %s %s", left, op, right)
		} else {
			expression = fmt.Sprintf("%s %s", left, op)
		}
		return expression
	} else {
		var logicOpr string
		if logical.GetLogicalOperator() == KEP_WF.LogicalExpression_AND {
			logicOpr = "and"
		} else {
			logicOpr = "or"
		}
		var expressions []string
		for _, subLogical := range logical.GetCompound() {
			expression := c.buildCondExpression(subLogical)
			if expression != "" {
				expression = "(" + expression + ")"
				expressions = append(expressions, expression)
			}
		}
		return strings.Join(expressions, " "+logicOpr+" ")
	}
}

// transInputReferenceOutput 转换输入变量为引用输出的名称
// input类型为InputSourceEnum_REFERENCE_OUTPUT
func (c *RuleConverter) transInputReferenceOutput(input *KEP_WF.Input) string {
	// 引用输出变量
	if varName, ok := c.varNameMap[input.GetReference().GetNodeID()]; ok {
		// 将Output替换为Output_x
		return strings.Replace(input.GetReference().GetJsonPath(), "Output", varName, 1)
	} else if input.GetReference().GetNodeID() == c.startNode.GetNodeID() {
		// 引用开始节点的输出变量 则加上前缀CUSTOM.
		return "CUSTOM." + input.GetReference().GetJsonPath()
	} else {
		// 其他节点，则取输出变量名称(去掉Output.)
		return trimOutputPrefix(input.GetReference().GetJsonPath())
	}
}

// transInput 转换提取input输入
// 引用节点输出：大模型节点则替换为变量x，其他则取output后的变量名称
// 系统变量：取对应的名称
// API参数：根据id取对应的名称
// 固定值转换名称
func (c *RuleConverter) transInput(input *KEP_WF.Input, isValueArray bool) string {
	if input == nil {
		return ""
	}
	switch input.GetInputType() {
	case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT:
		return c.transInputReferenceOutput(input)
	case KEP_WF.InputSourceEnum_USER_INPUT:
		if input.GetUserInputValue() != nil && len(input.GetUserInputValue().GetValues()) > 0 {
			values := input.GetUserInputValue().GetValues()
			if isValueArray {
				return fmt.Sprintf("[\"%s\"]", strings.Join(values, "\",\""))
			} else {
				return fmt.Sprintf("\"%s\"", values[0])
			}
		}
	case KEP_WF.InputSourceEnum_SYSTEM_VARIABLE:
		if input.GetSystemVariable() != nil {
			return input.GetSystemVariable().GetName()
		}
	case KEP_WF.InputSourceEnum_CUSTOM_VARIABLE:
		// API参数加上前缀API.
		if varParam, ok := c.varParamMap[input.GetCustomVarID()]; ok {
			return "API." + varParam.VarName
		}
	}
	return ""
}

// transformOperator 将运算符转换为字符串
func transformOperator(operator KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum) string {
	switch operator {
	case KEP_WF.LogicalExpression_ComparisonExpression_EQ:
		return "=="
	case KEP_WF.LogicalExpression_ComparisonExpression_NE:
		return "!="
	case KEP_WF.LogicalExpression_ComparisonExpression_GT:
		return ">"
	case KEP_WF.LogicalExpression_ComparisonExpression_GE:
		return ">="
	case KEP_WF.LogicalExpression_ComparisonExpression_LT:
		return "<"
	case KEP_WF.LogicalExpression_ComparisonExpression_LE:
		return "<="
	case KEP_WF.LogicalExpression_ComparisonExpression_IN:
		return "in"
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN:
		return "not in"
	case KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS: // 包含关系变成in 左右侧更换位置
		return "in"
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS:
		return "not in"
	case KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:
		return "is not None"
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET:
		return "is None"
	}
	return ""
}

// trimOutputPrefix 去除Output.前缀
func trimOutputPrefix(name string) string {
	return strings.TrimPrefix(name, "Output.")
}

// genPDLToolsInfo 生成PDLToolsInfo
func genPDLToolsInfo(ctx context.Context, bfsSortNodes []*KEP_WF.WorkflowNode) *KEP_WF.PDLToolsInfo {
	pdlTools := make([]*KEP_WF.PDLTool, 0)
	for _, node := range bfsSortNodes {
		if node.GetNodeType() == KEP_WF.NodeType_TOOL || node.GetNodeType() == KEP_WF.NodeType_LLM ||
			node.GetNodeType() == KEP_WF.NodeType_CODE_EXECUTOR || node.GetNodeType() == KEP_WF.NodeType_PLUGIN {
			pdlTool := genPDLTool(ctx, node)
			pdlTools = append(pdlTools, pdlTool)
		}
	}
	return &KEP_WF.PDLToolsInfo{
		Tools: pdlTools,
	}
}

// genPDLTool 转成PDLTool工具信息
func genPDLTool(ctx context.Context, node *KEP_WF.WorkflowNode) *KEP_WF.PDLTool {
	// 目前没有合并节点 取单个节点转
	pdlTool := &KEP_WF.PDLTool{
		ToolName: node.GetNodeName(),
		ToolDesc: node.GetNodeDesc(),
	}
	pdlTool.ToolNodes = make([]*KEP_WF.PDLToolNode, 0)
	pdlToolNode := &KEP_WF.PDLToolNode{
		NodeID:      node.GetNodeID(),
		NodeName:    node.GetNodeName(),
		NodeDesc:    node.GetNodeDesc(),
		NodeType:    node.GetNodeType(),
		Inputs:      node.GetInputs(),
		Outputs:     node.GetOutputs(),
		NextNodeIDs: []string{},
	}
	switch node.GetNodeType() {
	case KEP_WF.NodeType_TOOL:
		pdlToolNode.ToolNodeData = node.GetToolNodeData()
	case KEP_WF.NodeType_LLM:
		pdlToolNode.LLMNodeData = node.GetLLMNodeData()
	case KEP_WF.NodeType_CODE_EXECUTOR:
		pdlToolNode.CodeExecutorNodeData = node.GetCodeExecutorNodeData()
	case KEP_WF.NodeType_PLUGIN:
		pdlToolNode.PluginNodeData = node.GetPluginNodeData()
	default:
		log.ErrorContextf(ctx, "genPDLTool invalid node type:%s, nodeID:%s", node.GetNodeType(), node.GetNodeID())
		return nil
	}
	pdlTool.ToolNodes = append(pdlTool.ToolNodes, pdlToolNode)

	// 处理输入输出
	if len(node.GetInputs()) > 0 {
		for _, input := range node.GetInputs() {
			// 入参只需要引用的 其他的过滤掉
			if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
				pdlTool.Inputs = append(pdlTool.Inputs, input)
			}
		}
	}
	// 处理工具节点和插件节点的输入转换
	if node.GetNodeType() == KEP_WF.NodeType_TOOL {
		pdlTool.Inputs = genPDLToolNodeInput(node.GetToolNodeData())
	} else if node.GetNodeType() == KEP_WF.NodeType_PLUGIN {
		pdlTool.Inputs = genPDLToolNodeInput(node.GetPluginNodeData().GetToolInputs())
	}
	// 输出
	pdlTool.Outputs = node.GetOutputs()
	// 大模型节点不做特殊处理 没有输出
	/*
		if node.GetNodeType() == KEP_WF.NodeType_LLM {
			pdlTool.Outputs = genLLMNodeOutput()
		}*/
	return pdlTool
}

// genPDLToolNodeInput 生成工具节点转换为PDLTool的输入信息
func genPDLToolNodeInput(toolData *KEP_WF.ToolNodeData) []*KEP_WF.InputParam {
	requestParams := make([]*KEP_WF.ToolNodeData_RequestParam, 0)
	requestParams = append(requestParams, toolData.GetHeader()...)
	requestParams = append(requestParams, toolData.GetQuery()...)
	requestParams = append(requestParams, toolData.GetBody()...)

	inputParams := make([]*KEP_WF.InputParam, 0, len(requestParams))
	for _, requestParam := range requestParams {
		// 入参只需要引用的 其他的过滤掉
		if requestParam.GetInput().GetInputType() != KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
			continue
		}
		inputParam := &KEP_WF.InputParam{
			Name:       requestParam.GetParamName(),
			Type:       requestParam.GetParamType(),
			Desc:       requestParam.GetParamDesc(),
			Input:      requestParam.GetInput(),
			IsRequired: requestParam.GetIsRequired(),
		}
		inputParams = append(inputParams, inputParam)
	}
	return inputParams
}

/*
// genLLMNodeOutput 大模型节点的output是空的，输出是固定的content，按格式组装
func genLLMNodeOutput() []*KEP_WF.OutputParam {
	output := &KEP_WF.OutputParam{
		Title: "Output",
		Type:  KEP_WF.TypeEnum_OBJECT,
		Desc:  "输出内容",
		Properties: []*KEP_WF.OutputParam{
			{
				Title: "Content",
				Type:  KEP_WF.TypeEnum_STRING,
				Desc:  "大模型运行后输出内容",
			},
		},
	}
	return []*KEP_WF.OutputParam{output}
}*/

// extractFuncNodes 遍历画布 获取需抽取伪代码函数的节点
func (c *RuleConverter) extractFuncNodes(ctx context.Context, startNode *KEP_WF.WorkflowNode,
	nodeMap map[string]*KEP_WF.WorkflowNode) map[string]FuncNode {
	funcNodeMap := make(map[string]FuncNode, 0)
	// 每个节点的入度数
	inDegree := make(map[string]int)
	// 每个节点的所有后继节点，可能有重复
	successorMap := make(map[string][]string)
	visited := make(map[string]bool)
	// dfs遍历的nodeID列表
	dfsNodeIDs := make([]string, 0, len(nodeMap))
	// dfs遍历计算入度和后继节点
	dfsCalInDegreeAndSuccessors(ctx, startNode, nodeMap, visited, inDegree, successorMap, &dfsNodeIDs)
	funcNameIndex := 0
	// 按dfs遍历的顺序，而不是map的顺序，保证多次执行的一致性
	for _, nodeID := range dfsNodeIDs {
		inDegreeCnt := inDegree[nodeID]
		successorList := types.Unique(successorMap[nodeID])
		// 入度>=2和后继节点数>=2 则需要抽取函数伪代码
		funcInDegreeNum := config.GetMainConfig().PDLConvertConfig.RuleConvert.FuncInDegreeNum
		funcSuccessorNum := config.GetMainConfig().PDLConvertConfig.RuleConvert.FuncSuccessorNum
		if inDegreeCnt >= funcInDegreeNum && len(successorList) >= funcSuccessorNum {
			// 将当前节点也添加进去 用于判断变量是否引用函数外部的
			successorList = append(successorList, nodeID)
			funcNameIndex++
			funcNode := FuncNode{
				NodeID:     nodeID,
				FuncName:   fmt.Sprintf("func_%d", funcNameIndex),
				Parameters: c.genFuncNodeParameter(nodeMap, successorList),
			}
			funcNodeMap[nodeID] = funcNode
		}
	}
	return funcNodeMap
}

func dfsCalInDegreeAndSuccessors(ctx context.Context, node *KEP_WF.WorkflowNode,
	nodeMap map[string]*KEP_WF.WorkflowNode, visited map[string]bool, inDegree map[string]int,
	successorMap map[string][]string, dfsNodeIDs *[]string) []string {
	if node == nil {
		return nil
	}
	if visited[node.GetNodeID()] {
		return successorMap[node.GetNodeID()]
	}

	visited[node.GetNodeID()] = true
	*dfsNodeIDs = append(*dfsNodeIDs, node.GetNodeID())
	var successors []string
	for _, nextNodeID := range getNextNodeIDs(node) {
		inDegree[nextNodeID]++
		successors = append(successors, nextNodeID)
		nextNode := nodeMap[nextNodeID]
		// 递归处理，添加下一个节点后继节点
		subSuccessors := dfsCalInDegreeAndSuccessors(ctx, nextNode, nodeMap, visited, inDegree, successorMap, dfsNodeIDs)
		successors = append(successors, subSuccessors...)
	}
	successorMap[node.GetNodeID()] = successors
	return successors
}

// genFuncNodeParameter 生成伪代码函数的参数
func (c *RuleConverter) genFuncNodeParameter(nodeMap map[string]*KEP_WF.WorkflowNode,
	successorList []string) []string {
	var parameters []string
	nodeIDSet := make(map[string]struct{})
	for _, successorID := range successorList {
		nodeIDSet[successorID] = struct{}{}
	}
	for _, nodeID := range successorList {
		node := nodeMap[nodeID]
		inputs := getNodeReferenceInputs(node)
		for _, input := range inputs {
			referenceID := input.GetReference().GetNodeID()
			// 不在nodeIDSet即为引用外部的变量，需要作为函数的参数
			if _, ok := nodeIDSet[referenceID]; !ok {
				//param := c.transInputReferenceOutput(input)
				// 方法的参数直接用Output_x, 方法的实现再取具体字段，如Output_x.Content
				var param string
				if varName, ok1 := c.varNameMap[input.GetReference().GetNodeID()]; ok1 {
					param = varName
				} else {
					// 其他节点，则取输出变量名称(去掉Output.)
					param = trimOutputPrefix(input.GetReference().GetJsonPath())
				}
				parameters = append(parameters, param)
			}
		}
	}
	// 参数做去重，可能有引用不同节点但变量重名了？
	return types.Unique(parameters)
}

// getNodeReferenceInputs 获取节点所有引用其他节点的输入参数
func getNodeReferenceInputs(node *KEP_WF.WorkflowNode) []*KEP_WF.Input {
	var inputs []*KEP_WF.Input
	if node.GetNodeType() == KEP_WF.NodeType_TOOL || node.GetNodeType() == KEP_WF.NodeType_PLUGIN {
		var toolNodeData *KEP_WF.ToolNodeData
		if node.GetNodeType() == KEP_WF.NodeType_TOOL {
			toolNodeData = node.GetToolNodeData()
		} else {
			toolNodeData = node.GetPluginNodeData().GetToolInputs()
		}
		requestParams := make([]*KEP_WF.ToolNodeData_RequestParam, 0)
		requestParams = append(requestParams, toolNodeData.GetHeader()...)
		requestParams = append(requestParams, toolNodeData.GetQuery()...)
		requestParams = append(requestParams, toolNodeData.GetBody()...)
		for _, requestParam := range requestParams {
			if requestParam.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
				inputs = append(inputs, requestParam.GetInput())
			}
		}
	} else {
		for _, input := range node.GetInputs() {
			if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
				inputs = append(inputs, input.GetInput())
			}
		}
	}
	return inputs
}

// genFuncCallCode 生成函数调用的伪代码 func1([xx])
func genFuncCallCode(funcNode *FuncNode) string {
	input := ""
	if len(funcNode.Parameters) > 0 {
		input = fmt.Sprintf("[%s]", strings.Join(funcNode.Parameters, ","))
	}
	return fmt.Sprintf("%s(%s)", funcNode.FuncName, input)
}

// genFuncDefCode 生成函数定义的伪代码 def func1([xx]):
func (c *RuleConverter) genFuncDefCode(ctx context.Context, funcNode *FuncNode) ([]string, error) {
	var funcPseudocode []string
	funcDefCode := fmt.Sprintf("def %s:", genFuncCallCode(funcNode))
	funcPseudocode = append(funcPseudocode, funcDefCode)
	node := c.nodeMap[funcNode.NodeID]
	// 生成该函数的处理流程
	subPseudocode, err := c.convertWorkflowNode(ctx, node, 1)
	if err != nil {
		return nil, err
	}
	funcPseudocode = append(funcPseudocode, subPseudocode...)
	// 子函数后加一个空行
	funcPseudocode = append(funcPseudocode, "")
	return funcPseudocode, nil
}
