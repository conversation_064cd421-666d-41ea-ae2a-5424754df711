package pdlconvert

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"io"
	"net/http"
	"net/url"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// Message .
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatRequest .
type ChatRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature"`
}

// ChatResponse .
type ChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Message      Message `json:"message"`
		FinishReason string  `json:"finish_reason"`
		Index        int     `json:"index"`
	} `json:"choices"`
}

func chatByGpt(ctx context.Context, prompt string) (string, error) {
	messages := []Message{
		{Role: "user", Content: prompt},
	}

	requestBody := ChatRequest{
		Model:       "gpt-4o", // 使用适当的模型名称
		Messages:    messages,
		Temperature: 0.5,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		log.ErrorContextf(ctx, "json marshal error:%v, requestBody:%+v", err, requestBody)
		return "", err
	}
	apiURL := config.GetMainConfig().PDLConvertConfig.LLMConvert.LLMApiURL
	apiKey := config.GetMainConfig().PDLConvertConfig.LLMConvert.LLMApiKey
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		log.ErrorContextf(ctx, "http NewRequest error:%v, requestBody:%+v", err, requestBody)
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	transport := &http.Transport{}
	if config.GetMainConfig().PDLConvertConfig.LLMConvert.ProxyEnable {
		proxyURL := config.GetMainConfig().PDLConvertConfig.LLMConvert.ProxyURL
		proxy, err := url.Parse(proxyURL)
		if err != nil {
			log.ErrorContextf(ctx, "url Parse error:%v, proxyURL:%s", err, proxyURL)
			return "", err
		}
		transport.Proxy = http.ProxyURL(proxy)
	}
	client := &http.Client{
		Transport: transport,
	}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorContextf(ctx, "http request error:%v, req:%+v", err, req)
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "io.ReadAll error:%v", err)
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		log.ErrorContextf(ctx, "resp status code:%d, body:%s", resp.StatusCode, string(body))
		return "", fmt.Errorf("resp status NOT 200, code:%d", resp.StatusCode)
	}

	var chatResponse ChatResponse
	err = json.Unmarshal(body, &chatResponse)
	if err != nil {
		log.ErrorContextf(ctx, "json Unmarshal error:%v, chatResponse:%+v", err, chatResponse)
		return "", err
	}
	// log.InfoContextf(ctx, "chatResponse:%+v", chatResponse)
	if len(chatResponse.Choices) == 0 {
		return "", fmt.Errorf("chatResponse.Choices is empty")
	}
	return chatResponse.Choices[0].Message.Content, nil
}
