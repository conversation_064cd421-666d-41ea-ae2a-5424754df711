package pdlconvert

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"regexp"
	"strings"
	"text/template"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gopkg.in/yaml.v2"
)

// LLMConverter 基于大模型转换生成procedure伪代码
type LLMConverter struct {
	// 参数提取信息
	// parameterMap map[string]*KEP_WF.ParameterInfo
	// API参数(变量参数)
	// varParamMap map[string]*entity.VarParams
	// 画布节点map
	nodeMap map[string]*KEP_WF.WorkflowNode
	// bfs遍历排序的节点
	bfsSortNodes []*KEP_WF.WorkflowNode

	prompt string
}

// NewLLMConverter TODO
func NewLLMConverter() Converter {
	return &LLMConverter{
		// parameterMap: make(map[string]*KEP_WF.ParameterInfo),
		// varParamMap:  make(map[string]*entity.VarParams),
		nodeMap: make(map[string]*KEP_WF.WorkflowNode),
	}
}

// PDLPromptTplCtx prompt模板输入的参数
type PDLPromptTplCtx struct {
	Meta   string // 基础信息
	Params string // API参数信息
	Nodes  string // 相关节点
	Edges  string // 节点连边
}

// PDLPromptNode node内容 yaml格式
type PDLPromptNode struct {
	NodeID   string `yaml:"NodeID"`
	NodeName string `yaml:"NodeName"`
	NodeDesc string `yaml:"NodeDesc,omitempty"`
	NodeType string `yaml:"NodeType"`
	Inputs   string `yaml:"Inputs,omitempty"`
	Outputs  string `yaml:"Outputs,omitempty"`
	NodeData string `yaml:"NodeData,omitempty"`
}

// Convert 转换画布为pdl内容
func (c *LLMConverter) Convert(ctx context.Context, workflow *KEP_WF.Workflow, paramInfos []*KEP_WF.ParameterInfo,
	varParams []*entity.VarParams) (*PDLContent, *KEP_WF.PDLToolsInfo, error) {
	var startNode *KEP_WF.WorkflowNode
	for _, node := range workflow.GetNodes() {
		if node != nil {
			if node.GetNodeType() == KEP_WF.NodeType_START {
				startNode = node
			}
			c.nodeMap[node.GetNodeID()] = node
		}
	}
	if startNode == nil {
		log.ErrorContextf(ctx, "start node is nil, workflowID:%s", workflow.GetWorkflowID())
		return nil, nil, errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonWorkflowIllegal))
	}
	c.bfsSortNodes = WFBFSSort(startNode, c.nodeMap)
	procedure, err := c.genProcedure(ctx, workflow, paramInfos)
	if err != nil {
		return nil, nil, err
	}
	pdlContent := &PDLContent{
		Name:      workflow.GetWorkflowName(),
		Desc:      workflow.GetWorkflowDesc(),
		Slots:     genSlots(paramInfos),
		APIs:      genAPIs(startNode, c.nodeMap),
		Answers:   c.genAnswers(c.bfsSortNodes),
		Procedure: CustomString(procedure),
	}
	// 工具信息
	pdlToolsInfo := genPDLToolsInfo(ctx, c.bfsSortNodes)
	log.DebugContextf(ctx, "llm convert pdlContent:%+v, pdlToolsInfo:%+v", pdlContent, pdlToolsInfo)
	return pdlContent, pdlToolsInfo, nil
}

// genAnswers 生成pdl answer内容，不做变量替换的原始内容
func (c *LLMConverter) genAnswers(nodes []*KEP_WF.WorkflowNode) []PDLAnswer {
	var answers []PDLAnswer
	for _, node := range nodes {
		if node.GetNodeType() == KEP_WF.NodeType_ANSWER {
			answer := PDLAnswer{
				Name: node.GetNodeName(),
				Desc: node.GetAnswerNodeData().GetAnswer(),
			}
			answers = append(answers, answer)
		}
	}
	return answers
}

//var promptTpl = "<任务>\n1. 对于一个图结构的工作流, 请你将其核心流程转为 Procedure 的形式.\n2. Procedure 语法:\n    2.1 整体上采用 Python/伪代码 的形式, 其中包括 API 和 ANSWER 两类特殊函数;\n    2.2 通过 `[变量] = API.NAME([参数])` 的形式调用 API;\n    2.3 通过 `ANSWER.NAME()` 的形式调用 ANSWER.\n3. 转换规则:\n    3.1 节点类型: 将原本图结构中 TOOL, LLM, CODE_EXECUTOR 类型的节点转为 API 节点. ANSWER 节点保持不变.\n    3.2 节点名称: 用原本图节点中的 `NodeName` 作为 Procedure 中的 API/ANSWER 名称.\n    3.3 条件判断: 将原本图结构中 LOGIC_EVALUATOR 类型的节点转为 Python 形式的条件判断.\n4. 请给出完整的 Procedure 表述!\n</任务>\n\n<示例>\n相关信息:\n```yaml\nName: 新闻查询\nDesc: 根据特定需求查询新闻\nSLOTs:\n- name: news_location\n  desc: 新闻发生地\n- name: news_type\n  desc: 新闻类型\n- name: news_time\n  desc: 新闻时间\nAPIs:\n- name: check_location\n  request: [news_location]\n  response: [是否支持]\n- name: query_news\n  request: [news_location, news_type, news_time]\n  response: [新闻列表]\nANSWERs:\n- name: 获取新闻成功\n  desc: 查询成功，返回查询到的新闻列表\n- name: 获取新闻失败\n  desc: 查询失败，转为播报当日头条新闻\n- name: 其他自由回复问题\n- name: 请用户提供必要信息\n```\n核心 Procedure:\n```python\nwhile True:\n    [news_location] = ANSWER.请用户提供必要信息()\n    if API.check_location([news_location]) == True:\n        break\n[news_type, news_time] = ANSWER.请用户提供必要信息()\n[news_list] = API.query_news([news_location, news_type, news_time])\nif news_list is not None:\n    ANSWER.获取新闻成功()\nelse:\n    ANSWER.获取新闻失败()\n```\n</示例>\n\n<输入>\n基础信息:\n```\n{{.Meta}}\n```\nAPI参数信息:\n```\n{{.Params}}\n```\n相关节点:\n```\n{{.Nodes}}\n```\n节点连边:\n```\n{{.Edges}}\n```\n</输入>\n\n下面, 请基于输入的信息, 直接给出转换后的流程表述 Procedure:"

// genProcedure 生成procedure伪代码内容
func (c *LLMConverter) genProcedure(ctx context.Context, workflow *KEP_WF.Workflow,
	paramInfos []*KEP_WF.ParameterInfo) (string, error) {
	promptNodes, err := c.genNodes(ctx, c.bfsSortNodes)
	if err != nil {
		log.ErrorContextf(ctx, "genNodes error: %v", err)
		return "", err
	}
	promptTplCtx := &PDLPromptTplCtx{
		Meta:   c.genMeta(workflow.GetWorkflowName(), workflow.GetWorkflowDesc()),
		Params: c.genParameters(paramInfos),
		Nodes:  promptNodes,
		Edges:  c.genEdges(workflow.GetNodes()),
	}
	promptTpl := config.GetMainConfig().PDLConvertConfig.LLMConvert.ProcedurePromptTpl
	// 生成prompt
	prompt, err := render(ctx, promptTpl, promptTplCtx)
	if err != nil {
		log.ErrorContextf(ctx, "render error: %v", err)
		return "", errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
	}
	log.InfoContextf(ctx, "prompt: %s", prompt)
	c.prompt = prompt
	procedure, err := chatByGpt(ctx, prompt)
	if err != nil {
		log.ErrorContextf(ctx, "chatByGpt error: %v, prompt:%s", err, prompt)
		return "", errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
	}
	log.InfoContextf(ctx, "chatByGpt procedure: %s", procedure)
	procedure = extractCode(ctx, procedure)
	return procedure, nil
}

func extractCode(ctx context.Context, content string) string {
	// 定义正则表达式来匹配Python代码块
	re := regexp.MustCompile("(?s)```python(.*?)```")

	// 查找匹配的内容
	matches := re.FindStringSubmatch(content)
	if len(matches) < 2 {
		log.InfoContextf(ctx, "no code block found, content:%s", content)
		return content
	}

	// 返回匹配的代码内容
	return matches[1]
}

// render 模版渲染
func render(ctx context.Context, tpl string, req any) (string, error) {
	e, err := template.New("").Parse(tpl)
	if err != nil {
		log.WarnContextf(ctx, "Compile template失败  tpl:%s err:%+v", tpl, err)
		return "", err
	}
	b := &bytes.Buffer{}
	if err := e.Execute(b, req); err != nil {
		log.WarnContextf(ctx, "Execute template失败 tpl:%s, req:%+v err:%+v", tpl, req, err)
		return "", err
	}
	return b.String(), nil
}

// genMeta 将工作流的名称和描述生成prompt里的meta信息
func (c *LLMConverter) genMeta(name, desc string) string {
	return fmt.Sprintf("Name: %s\nDesc: %s", name, desc)
}

// genParameters 生成prompt里的api参数信息
func (c *LLMConverter) genParameters(paramInfos []*KEP_WF.ParameterInfo) string {
	var builder strings.Builder
	for i, paramInfo := range paramInfos {
		// Parameter(name=会员卡ID, id=03882afc-e74b-42a2-b4d7-b28594a0e3b3, desc=用户的会员卡ID号码，一般是一串阿拉伯数字, type=TypeEnum.STRING)
		parameter := fmt.Sprintf("Parameter(name=%s, id=%s, desc=%s, type=%s", paramInfo.GetName(),
			paramInfo.GetParameterId(), paramInfo.GetDesc(), paramInfo.GetType())
		if i > 0 {
			builder.WriteString("\n")
		}
		builder.WriteString(parameter)
	}
	return builder.String()
}

// genNodes 生成prompt里的节点nodes信息
func (c *LLMConverter) genNodes(ctx context.Context, nodes []*KEP_WF.WorkflowNode) (string, error) {
	var promptNodes []*PDLPromptNode
	for _, node := range nodes {
		nodeData, err := c.convertNodeData(ctx, node)
		if err != nil {
			return "", err
		}
		promptNode := &PDLPromptNode{
			NodeID:   node.GetNodeID(),
			NodeName: node.GetNodeName(),
			NodeDesc: node.GetNodeDesc(),
			NodeType: node.GetNodeType().String(),
			Inputs:   c.convertNodeInputs(node.GetInputs()),
			Outputs:  c.convertNodeOutputs(node.GetOutputs()),
			NodeData: nodeData,
		}
		promptNodes = append(promptNodes, promptNode)
	}
	promptNodesYaml, err := yaml.Marshal(promptNodes)
	if err != nil {
		log.ErrorContextf(ctx, "yaml.Marshal error: %v, promptNodes:%+v", err, promptNodes)
		return "", errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonSystemError))
	}
	return string(promptNodesYaml), nil
}

// convertNodeInputs 转成prompt里面的节点输入信息
func (c *LLMConverter) convertNodeInputs(inputs []*KEP_WF.InputParam) string {
	if len(inputs) == 0 {
		return ""
	}
	nodeInputs := make([]string, 0, len(inputs))
	for _, inputParam := range inputs {
		// 格式: [(Name=hos_name, Type=STRING, Input=Reference[63a5ebb1-4f0d-872e-0abb-28f14bbcad13.Output.Content], Desc=医院名称)]
		// 将里面的input转成对应的内容
		input := inputParam.GetInput()
		inputData := c.convertInputParam(input)
		nodeInput := fmt.Sprintf("(Name=%s, Type=%s, Input=%s, Desc=%s)", inputParam.GetName(),
			inputParam.GetType().String(), inputData, inputParam.GetDesc())
		nodeInputs = append(nodeInputs, nodeInput)
	}
	return fmt.Sprintf("[%s]", strings.Join(nodeInputs, ", "))
}

func (c *LLMConverter) convertInputParam(input *KEP_WF.Input) string {
	var inputData string
	switch input.GetInputType() {
	case KEP_WF.InputSourceEnum_USER_INPUT:
		inputData = fmt.Sprintf("UserInputValue[Values=[%s]]", strings.Join(input.GetUserInputValue().GetValues(), ","))
	case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT:
		inputData = fmt.Sprintf("Reference[%s.%s]", input.GetReference().GetNodeID(), input.GetReference().GetJsonPath())
	case KEP_WF.InputSourceEnum_SYSTEM_VARIABLE:
		inputData = fmt.Sprintf("SystemVariable[%s]", input.GetSystemVariable().GetName())
	case KEP_WF.InputSourceEnum_CUSTOM_VARIABLE:
		inputData = fmt.Sprintf("CustomVarID[%s]", input.GetCustomVarID())
	case KEP_WF.InputSourceEnum_NODE_INPUT_PARAM:
		inputData = fmt.Sprintf("NodeInputParamName[%s]", input.GetNodeInputParamName())
	}
	return inputData
}

func (c *LLMConverter) convertNodeOutputs(outputs []*KEP_WF.OutputParam) string {
	if len(outputs) == 0 {
		return ""
	}
	nodeOutputs := make([]string, 0, len(outputs))
	for _, outputParam := range outputs {
		// 格式: [[(Name=Output.hos_name, Type=STRING, Desc=可选医院名称), (Name=Output.num, Type=INT, Desc=可选医院数量)]]
		outputProperties := make([]string, 0, len(outputParam.GetProperties()))
		for _, property := range outputParam.GetProperties() {
			outputProperty := fmt.Sprintf("(Name=%s.%s, Type=%s, Desc=%s)", outputParam.GetTitle(), property.GetTitle(),
				property.GetType().String(), property.GetDesc())
			outputProperties = append(outputProperties, outputProperty)
		}
		nodeOutput := fmt.Sprintf("[%s]", strings.Join(outputProperties, ", "))
		nodeOutputs = append(nodeOutputs, nodeOutput)
	}
	return fmt.Sprintf("[%s]", strings.Join(nodeOutputs, ", "))
}

func (c *LLMConverter) convertNodeData(ctx context.Context, node *KEP_WF.WorkflowNode) (string, error) {
	var nodeData string
	var err error
	switch node.GetNodeType() {
	case KEP_WF.NodeType_TOOL:
		nodeData = c.covertToolNodeData(node.GetToolNodeData())
	case KEP_WF.NodeType_PLUGIN:
		nodeData = c.covertToolNodeData(node.GetPluginNodeData().GetToolInputs())
	case KEP_WF.NodeType_LLM:
		llmPrompt := truncateContent(node.GetLLMNodeData().GetPrompt())
		nodeData = fmt.Sprintf("ModelName=%s, prompt=%s", node.GetLLMNodeData().GetModelName(), llmPrompt)
	case KEP_WF.NodeType_CODE_EXECUTOR:
		nodeData = fmt.Sprintf("Code=%s", truncateContent(node.GetCodeExecutorNodeData().GetCode()))
	case KEP_WF.NodeType_LOGIC_EVALUATOR:
		nodeData, err = c.covertLogicEvaluatorNodeData(ctx, node.GetLogicEvaluatorNodeData())
	case KEP_WF.NodeType_ANSWER:
		nodeData = fmt.Sprintf("Answer=%s", truncateContent(node.GetAnswerNodeData().GetAnswer()))
	case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
		nodeData = c.convertParameterExtractor(node.GetParameterExtractorNodeData())
	}
	return nodeData, err
}

func (c *LLMConverter) covertToolNodeData(toolNode *KEP_WF.ToolNodeData) string {
	var list []string
	apiInfo := fmt.Sprintf("API=API(Method=%s URL=%s)", toolNode.GetAPI().GetMethod(), toolNode.GetAPI().GetURL())
	list = append(list, apiInfo)
	if len(toolNode.GetQuery()) > 0 {
		query := fmt.Sprintf("Query=%s", c.convertRequestParam(toolNode.GetQuery()))
		list = append(list, query)
	}
	if len(toolNode.GetBody()) > 0 {
		body := fmt.Sprintf("Body=%s", c.convertRequestParam(toolNode.GetBody()))
		list = append(list, body)
	}
	return strings.Join(list, ", ")
}

func (c *LLMConverter) convertRequestParam(requestParams []*KEP_WF.ToolNodeData_RequestParam) string {
	if len(requestParams) == 0 {
		return ""
	}
	requestParamsStr := make([]string, 0, len(requestParams))
	for _, requestParam := range requestParams {
		requestParamStr := fmt.Sprintf("Param(Name=%s, Type=%s, Input=%s)", requestParam.GetParamName(),
			requestParam.GetParamType().String(), c.convertInputParam(requestParam.GetInput()))
		requestParamsStr = append(requestParamsStr, requestParamStr)
	}
	return fmt.Sprintf("[%s]", strings.Join(requestParamsStr, ", "))
}

func (c *LLMConverter) covertLogicEvaluatorNodeData(ctx context.Context, logicNode *KEP_WF.LogicEvaluatorNodeData) (string, error) {
	var groupExprList []string
	for _, group := range logicNode.GetGroup() {
		logical := c.convertLogicalExpression(group.Logical)
		logicalLen := len([]rune(logical))
		if logicalLen > config.GetMainConfig().PDLConvertConfig.CondExpressLenLimit {
			// 条件表达式长度超过限制
			log.InfoContextf(ctx, "condition expression length[%d] exceeded, condExpression:%s", logicalLen, logical)
			return "", errors.New(entity.GetPDLReasonDesc(ctx, entity.PDLReasonExpressLenExceeded))
		}
		groupExpr := fmt.Sprintf("Group(Logical=%s, NextNodeIDs=[%s])", logical, strings.Join(group.GetNextNodeIDs(), ", "))
		groupExprList = append(groupExprList, groupExpr)
	}
	return fmt.Sprintf("[%s]", strings.Join(groupExprList, ", ")), nil
}

func (c *LLMConverter) convertLogicalExpression(logicalExpression *KEP_WF.LogicalExpression) string {
	if logicalExpression == nil {
		return "None"
	}
	if logicalExpression.GetLogicalOperator() == KEP_WF.LogicalExpression_UNSPECIFIED {
		comparison := logicalExpression.GetComparison()
		return fmt.Sprintf("Comparison(Left=%s, Operator=%s, Right=%s)", c.convertInputParam(comparison.GetLeft()),
			comparison.GetOperator().String(), c.convertInputParam(comparison.GetRight()))
	} else {
		operator := logicalExpression.GetLogicalOperator().String()
		expressions := make([]string, 0)
		for _, subLogical := range logicalExpression.GetCompound() {
			expression := c.convertLogicalExpression(subLogical)
			expressions = append(expressions, expression)
		}
		return fmt.Sprintf("LogicalExpression(LogicalOperator=%s, Compound=[%s])",
			operator, strings.Join(expressions, ", "))
	}
}

// convertParameterExtractor 转换参数提取节点
func (c *LLMConverter) convertParameterExtractor(parameterNode *KEP_WF.ParameterExtractorNodeData) string {
	// Parameters=[(RefParameterID=89f2a582-d7c0-4518-bb5b-ab1225e5145f, Required=True)], UserConstraint="当需要对“发票类型”参数进行追问时，请严格按照以下内容进行追问：“ 您好，根据您的需求，您需要开发..
	var paramStrList []string
	for _, parameter := range parameterNode.GetParameters() {
		paramStr := fmt.Sprintf("(RefParameterID=%s, Required=%t)", parameter.GetRefParameterID(),
			parameter.GetRequired())
		paramStrList = append(paramStrList, paramStr)
	}
	return fmt.Sprintf("[%s], UserConstraint=%s", strings.Join(paramStrList, ", "),
		truncateContent(parameterNode.GetUserConstraint()))
}

func truncateContent(content string) string {
	truncateLen := config.GetMainConfig().PDLConvertConfig.LLMConvert.ContentTruncateLen
	if truncateLen <= 0 {
		return content
	}
	if utf8.RuneCountInString(content) > truncateLen {
		return fmt.Sprintf("%s...", string([]rune(content)[:truncateLen]))
	}
	return content
}

// genEdges 生成prompt里的节点连边edges信息
func (c *LLMConverter) genEdges(nodes []*KEP_WF.WorkflowNode) string {
	var edges []string
	for _, node := range nodes {
		nextNodeIDs := getNextNodeIDs(node)
		for _, nextNodeID := range nextNodeIDs {
			// d18b4d6a-e637-acbb-f80c-24f3a1d0831b -> fdbd7dd2-d89a-5be2-d3d9-520626b7b738
			edge := fmt.Sprintf("%s -> %s", node.GetNodeID(), nextNodeID)
			edges = append(edges, edge)
		}
	}
	return strings.Join(edges, "\n")
}
