// Package pdlconvert TODO
package pdlconvert

import (
	"context"
	"strings"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gopkg.in/yaml.v3"
)

// PDL .
type PDL interface {
	// CheckConvertible 校验画布是否可转换为PDL 返回是否可转换以及不可转换的原因
	CheckConvertible(ctx context.Context, workflow *KEP_WF.Workflow) (bool, string)

	// Convert 画布转换为PDL内容和对应的工具信息
	Convert(ctx context.Context, workflow *KEP_WF.Workflow, paramInfos []*KEP_WF.ParameterInfo,
		varParams []*entity.VarParams) (*PDLContent, *KEP_WF.PDLToolsInfo, error)

	// CheckPDLValid 校验PDL内容是否有效
	CheckPDLValid(ctx context.Context, pdlContent *PDLContent, toolsInfo *KEP_WF.PDLToolsInfo) (bool, string)
}

type pdlImpl struct {
}

// NewPDL .
func NewPDL() PDL {
	return &pdlImpl{}
}

// PDLSlot 参数信息
type PDLSlot struct {
	Name string `yaml:"name"`
	Desc string `yaml:"desc,omitempty"`
	Type string `yaml:"type"`
}

// PDLApi API信息
type PDLApi struct {
	Name         string   `yaml:"name"`
	Precondition []string `yaml:"precondition,omitempty"`
}

// PDLAnswer 回复信息
type PDLAnswer struct {
	Name string `yaml:"name"`
	Desc string `yaml:"desc,omitempty"`
}

// CustomString is a custom type to handle long strings with |-
type CustomString string

// MarshalYAML customizes the YAML output for CustomString
func (cs CustomString) MarshalYAML() (interface{}, error) {
	convertedString := removeNewLineBlock(string(cs))
	node := &yaml.Node{
		Kind:  yaml.ScalarNode,
		Style: yaml.LiteralStyle,
		Value: convertedString,
	}
	return node, nil
}

func removeNewLineBlock(s string) string {
	lines := strings.Split(s, "\n")
	for i, line := range lines {
		lines[i] = strings.TrimRight(line, " ")
	}
	return strings.Join(lines, "\n")
}

// Config is the structure we want to serialize to YAML
type Config struct {
	Procedure CustomString `yaml:"procedure"`
}

// PDLContent pdl yaml内容
type PDLContent struct {
	Name      string       `yaml:"Name"`
	Desc      string       `yaml:"Desc"`
	Slots     []PDLSlot    `yaml:"SLOTs"`
	APIs      []PDLApi     `yaml:"APIs"`
	Answers   []PDLAnswer  `yaml:"ANSWERs"`
	Procedure CustomString `yaml:"Procedure"`
}
