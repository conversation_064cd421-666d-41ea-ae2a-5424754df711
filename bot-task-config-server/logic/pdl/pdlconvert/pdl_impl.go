package pdlconvert

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

var SupportedNodeTypes = []KEP_WF.NodeType{
	KEP_WF.NodeType_START,
	KEP_WF.NodeType_PARAMETER_EXTRACTOR,
	KEP_WF.NodeType_LLM,
	KEP_WF.NodeType_CODE_EXECUTOR,
	KEP_WF.NodeType_TOOL,
	KEP_WF.NodeType_LOGIC_EVALUATOR,
	KEP_WF.NodeType_ANSWER,
	KEP_WF.NodeType_PLUGIN,
	KEP_WF.NodeType_END,
}

func (p *pdlImpl) CheckConvertible(ctx context.Context, workflow *KEP_WF.Workflow) (isConvertible bool, reasonCode string) {
	var msg string
	defer func() {
		log.InfoContextf(ctx, "checkConvertible isConvertible:%t, workflowID:%s, workflowName:%s, reasonCode:%s, msg:%s",
			isConvertible, workflow.GetWorkflowID(), workflow.GetWorkflowName(), reasonCode, msg)
	}()
	// 节点ID对应的类型
	nodeIDTypeMap := make(map[string]KEP_WF.NodeType)
	for _, node := range workflow.GetNodes() {
		if node != nil {
			nodeIDTypeMap[node.GetNodeID()] = node.GetNodeType()
		}
	}
	for _, node := range workflow.GetNodes() {
		// 改成按支持的节点类型判断，避免新增节点类型遗漏处理
		var isSupportedNode bool
		for _, supportNodeType := range SupportedNodeTypes {
			if node.GetNodeType() == supportNodeType {
				isSupportedNode = true
				break
			}
		}
		if !isSupportedNode {
			msg = fmt.Sprintf("包含不支持的节点类型[%s], 节点名称[%s]", node.GetNodeType().String(), node.GetNodeName())
			return false, entity.PDLReasonUnsupportedNode
		}
		if node.GetNodeType() == KEP_WF.NodeType_PLUGIN && node.GetPluginNodeData() != nil {
			for _, toolID := range config.GetMainConfig().PDLConvertConfig.PluginToolBlackList {
				if node.GetPluginNodeData().GetToolID() == toolID {
					msg = fmt.Sprintf("包含不支持转换的插件, 节点名称[%s]", node.GetNodeName())
					return false, entity.PDLReasonUnsupportedPlugin
				}
			}
		}
		// 条件表达式包含系统变量SYS.ChatHistory
		if node.GetNodeType() == KEP_WF.NodeType_LOGIC_EVALUATOR {
			for _, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
				if group != nil && group.GetLogical() != nil {
					if isConvertible, reasonCode, msg = checkExpressionConvertible(node, group.GetLogical(), nodeIDTypeMap); !isConvertible {
						return false, reasonCode
					}
				}
				if len(types.Unique(group.GetNextNodeIDs())) > 1 {
					msg = fmt.Sprintf("包含并行分支, 节点名称[%s]", node.GetNodeName())
					return false, entity.PDLReasonParallelBranches
				}
			}
		} else {
			// 不支持并行分支
			if len(types.Unique(node.GetNextNodeIDs())) > 1 {
				msg = fmt.Sprintf("包含并行分支, 节点名称[%s]", node.GetNodeName())
				return false, entity.PDLReasonParallelBranches
			}
			// 不支持引用条件输出序号
			if node.GetNodeType() == KEP_WF.NodeType_TOOL {
				requestParams := make([]*KEP_WF.ToolNodeData_RequestParam, 0)
				requestParams = append(requestParams, node.GetToolNodeData().GetHeader()...)
				requestParams = append(requestParams, node.GetToolNodeData().GetQuery()...)
				requestParams = append(requestParams, node.GetToolNodeData().GetBody()...)

				for _, param := range requestParams {
					if isConvertible, reasonCode, msg = checkInputConvertible(node, param.GetInput(), nodeIDTypeMap); !isConvertible {
						return false, reasonCode
					}
				}
			} else {
				for _, inputParam := range node.GetInputs() {
					// 回复节点不能包含历史会话的系统变量
					if node.GetNodeType() == KEP_WF.NodeType_ANSWER {
						if inputParam.GetInput().GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE && inputParam.GetInput().GetSystemVariable() != nil &&
							inputParam.GetInput().GetSystemVariable().GetName() == "SYS.ChatHistory" {
							msg = fmt.Sprintf("回复节点包含系统变量SYS.ChatHistory, 节点名称[%s]", node.GetNodeName())
							return false, entity.PDLReasonAnswerChatHistory
						}
					}
					if isConvertible, reasonCode, msg = checkInputConvertible(node, inputParam.GetInput(), nodeIDTypeMap); !isConvertible {
						return false, reasonCode
					}
				}
			}
		}

	}
	return true, ""
}

func checkExpressionConvertible(node *KEP_WF.WorkflowNode, expression *KEP_WF.LogicalExpression,
	nodeIDTypeMap map[string]KEP_WF.NodeType) (bool, string, string) {
	if len(expression.GetCompound()) > 0 {
		for _, compound := range expression.GetCompound() {
			if isConvertible, reasonCode, msg := checkExpressionConvertible(node, compound, nodeIDTypeMap); !isConvertible {
				return false, reasonCode, msg
			}
		}
	}
	if expression.GetComparison() != nil {
		// 检测表达式左右侧是否符合转换条件
		left := expression.GetComparison().GetLeft()
		if left != nil {
			if isConvertible, reasonCode, msg := checkExpressionInput(node, left, nodeIDTypeMap); !isConvertible {
				return false, reasonCode, msg
			}
		}
		right := expression.GetComparison().GetRight()
		if right != nil {
			if isConvertible, reasonCode, msg := checkExpressionInput(node, right, nodeIDTypeMap); !isConvertible {
				return false, reasonCode, msg
			}
		}
	}
	return true, "", ""
}

// checkExpressionInput 检测条件表达式左值和右值输入是否支持转换
func checkExpressionInput(node *KEP_WF.WorkflowNode, input *KEP_WF.Input, nodeIDTypeMap map[string]KEP_WF.NodeType) (
	bool, string, string) {
	if input == nil {
		return true, "", ""
	}
	if input.GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE && input.GetSystemVariable() != nil &&
		input.GetSystemVariable().GetName() == "SYS.ChatHistory" {
		msg := fmt.Sprintf("条件判断节点包含系统变量SYS.ChatHistory, 节点名称[%s]", node.GetNodeName())
		return false, entity.PDLReasonConditionChatHistory, msg
	}
	if isConvertible, reasonCode, msg := checkInputConvertible(node, input, nodeIDTypeMap); !isConvertible {
		return false, reasonCode, msg
	}
	return true, "", ""
}

// checkInputConvertible 检查输入变量的值是否可转换，包含条件输出的序号则不可转换
func checkInputConvertible(node *KEP_WF.WorkflowNode, input *KEP_WF.Input, nodeIDTypeMap map[string]KEP_WF.NodeType) (
	bool, string, string) {
	if input == nil {
		return true, "", ""
	}
	if input.GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT && input.GetReference() != nil {
		nodeType := nodeIDTypeMap[input.GetReference().GetNodeID()]
		if nodeType == KEP_WF.NodeType_LOGIC_EVALUATOR {
			msg := fmt.Sprintf("引用条件判断节点的输出变量, 节点名称[%s]", node.GetNodeName())
			return false, entity.PDLReasonReferenceConditionOutput, msg
		}
	}
	return true, "", ""
}

// Convert 画布转换为PDL内容和对应的工具信息
func (p *pdlImpl) Convert(ctx context.Context, workflow *KEP_WF.Workflow, paramInfos []*KEP_WF.ParameterInfo,
	varParams []*entity.VarParams) (*PDLContent, *KEP_WF.PDLToolsInfo, error) {
	t0 := time.Now()
	method := config.GetMainConfig().PDLConvertConfig.ConvertMethod
	log.InfoContextf(ctx, "convert request, workflow:%+v, paramInfos:%+v, varParams:%+v",
		workflow, paramInfos, varParams)
	defer func() {
		log.InfoContextf(ctx, "convert method:%s, cost time:%d ms,workflowID:%s, workflowName:%s",
			method, time.Since(t0).Milliseconds(), workflow.GetWorkflowID(), workflow.GetWorkflowName())
	}()
	var converter Converter
	if method == "rule" {
		// 工程规则转换
		converter = NewRuleConverter()
	} else {
		// 大模型转换
		converter = NewLLMConverter()
	}
	return converter.Convert(ctx, workflow, paramInfos, varParams)
}

// CheckPDLValid 检查PDL内容是否有效
func (p *pdlImpl) CheckPDLValid(ctx context.Context, pdlContent *PDLContent, toolsInfo *KEP_WF.PDLToolsInfo) (bool,
	string) {
	if pdlContent.Name == "" {
		return false, "PDL Name不能为空"
	}
	if pdlContent.Procedure == "" {
		return false, "PDL Procedure不能为空"
	}
	for _, slot := range pdlContent.Slots {
		if slot.Name == "" {
			return false, "PDL SLOTs Name不能为空"
		}
	}
	for _, answer := range pdlContent.Answers {
		if answer.Name == "" {
			return false, "PDL ANSWERs Name不能为空"
		}
	}
	toolNameSet := make(map[string]struct{})
	for _, tool := range toolsInfo.GetTools() {
		toolNameSet[tool.GetToolName()] = struct{}{}
	}
	for _, api := range pdlContent.APIs {
		if api.Name == "" {
			return false, "PDL APIs Name不能为空"
		}
		if _, ok := toolNameSet[api.Name]; !ok {
			return false, fmt.Sprintf("PDL APIs Name[%s]不在API列表里", api.Name)
		}
	}
	return true, ""
}
