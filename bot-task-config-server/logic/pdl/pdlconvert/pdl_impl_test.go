package pdlconvert

import (
	"context"
	"encoding/json"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCheckConvertible(t *testing.T) {
	ctx := context.Background()
	p := &pdlImpl{}

	tests := []struct {
		name     string
		nodes    []*KEP_WF.WorkflowNode
		expected bool
	}{
		{
			name: "包含不支持的节点类型",
			nodes: []*KEP_WF.WorkflowNode{
				{NodeName: "node1", NodeType: KEP_WF.NodeType_LLM_KNOWLEDGE_QA},
			},
			expected: false,
		},
		{
			name: "包含并行分支",
			nodes: []*KEP_WF.WorkflowNode{
				{NodeName: "node1", NodeType: KEP_WF.NodeType_TOOL, NextNodeIDs: []string{"node2", "node3"}},
			},
			expected: false,
		},
		{
			name:     "所有节点都支持",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			workflow := &KEP_WF.Workflow{Nodes: tt.nodes}
			isConvertible, reasonCode := p.CheckConvertible(ctx, workflow)
			t.Logf("isConvertible:%t, reasonCode:%s", isConvertible, reasonCode)
			if tt.expected != isConvertible {
				t.Errorf("Expected %v, but got %v", t, isConvertible)
			}
		})
	}
}

func TestConvert(t *testing.T) {
	config.GetMainConfigForUnitTest().PDLConvertConfig = config.PDLConvertConfig{
		ConvertMethod:       "rule",
		CondExpressLenLimit: 2000,
		RuleConvert: config.PDLRuleConvertConfig{
			ExtractFuncEnable: true,
			FuncInDegreeNum:   2,
			FuncSuccessorNum:  1,
		},
	}
	ctx := context.Background()
	p := &pdlImpl{}

	tests := []struct {
		name          string
		workflowJson  string
		paramInfoJson string
		varParamsJson string
		expectedError error
		expectedPDL   *PDLContent
		expectedTools *KEP_WF.PDLToolsInfo
	}{
		{
			name:          "test",
			workflowJson:  "{\"ProtoVersion\":\"V2_6\",\"WorkflowID\":\"225d878d-ec88-408d-8ac1-d844ecca2c5f\",\"WorkflowName\":\"test\",\"WorkflowDesc\":\"test\",\"Nodes\":[{\"NodeID\":\"74b3cb2e-2f2d-f961-b64b-c1807cf6c6c2\",\"NodeName\":\"开始\",\"NodeDesc\":\"\",\"NodeType\":\"START\",\"StartNodeData\":{},\"Inputs\":[{\"Name\":\"content\",\"Type\":\"STRING\",\"Desc\":\"xxxs\",\"IsRequired\":false}],\"Outputs\":[],\"NextNodeIDs\":[\"20208b08-bfe3-fb9b-90ad-8c0b5cc6aadc\"],\"NodeUI\":\"\"},{\"NodeID\":\"6a70ce6d-302c-587c-0e49-bede050e5ef7\",\"NodeName\":\"结束\",\"NodeDesc\":\"\",\"NodeType\":\"END\",\"EndNodeData\":{},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[],\"NodeUI\":\"\"},{\"NodeID\":\"f2985eb6-986a-a93a-a028-5bc42d0e8841\",\"NodeName\":\"工具1\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://api.restful-api.dev/objects\",\"Method\":\"GET\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[{\"ParamName\":\"TYPE\",\"ParamDesc\":\"\",\"ParamType\":\"OBJECT\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"20208b08-bfe3-fb9b-90ad-8c0b5cc6aadc\",\"JsonPath\":\"Output\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"query\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"SYSTEM_VARIABLE\",\"SystemVariable\":{\"Name\":\"SYS.UserQuery\",\"DialogHistoryLimit\":15}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"sessionID\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"CUSTOM_VARIABLE\",\"CustomVarID\":\"3c412c08-c285-486a-9e24-d146c6f60ffc\"},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"value\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"深圳\"]}},\"IsRequired\":false,\"SubParams\":[]}],\"Body\":[]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"subData1\",\"Type\":\"ARRAY_OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"subsubdata1\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"}],\"Desc\":\"\"},{\"Title\":\"subdata2\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"}],\"Desc\":\"数据\"},{\"Title\":\"value\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"fb40701a-e580-e67f-3d0e-fa526a8502bd\"],\"NodeUI\":\"\"},{\"NodeID\":\"fb40701a-e580-e67f-3d0e-fa526a8502bd\",\"NodeName\":\"大模型1\",\"NodeDesc\":\"\",\"NodeType\":\"LLM\",\"LLMNodeData\":{\"ModelName\":\"cs-normal-70b\",\"Temperature\":0.7,\"TopP\":0.6,\"MaxTokens\":4000,\"Prompt\":\"是了肯德基风尚反馈时代峰峻\"},\"Inputs\":[{\"Name\":\"type123\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"20208b08-bfe3-fb9b-90ad-8c0b5cc6aadc\",\"JsonPath\":\"Output.type123\"}},\"Desc\":\"xxx\",\"IsRequired\":false},{\"Name\":\"subData1\",\"Type\":\"ARRAY_OBJECT\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f2985eb6-986a-a93a-a028-5bc42d0e8841\",\"JsonPath\":\"Output.data.subData1\"}},\"Desc\":\"\",\"IsRequired\":false},{\"Name\":\"value\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f2985eb6-986a-a93a-a028-5bc42d0e8841\",\"JsonPath\":\"Output.value\"}},\"Desc\":\"\",\"IsRequired\":false}],\"Outputs\":[],\"NextNodeIDs\":[\"5b98a64b-dbb8-a7cc-9dea-899b2057415c\"],\"NodeUI\":\"\"},{\"NodeID\":\"20208b08-bfe3-fb9b-90ad-8c0b5cc6aadc\",\"NodeName\":\"参数提取1\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"6b9c39f0-d08a-475d-bef6-2d8f34dcd6a2\",\"Required\":true},{\"RefParameterID\":\"40be9f5f-cd14-4228-8979-42547dcb6e33\",\"Required\":true}],\"UserConstraint\":\"请输入类型{{type}}\"},\"Inputs\":[{\"Name\":\"type\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"haha\"]}},\"Desc\":\"\",\"IsRequired\":false}],\"Outputs\":[],\"NextNodeIDs\":[\"f2985eb6-986a-a93a-a028-5bc42d0e8841\"],\"NodeUI\":\"\"},{\"NodeID\":\"8e936789-b3e8-5e1b-fbc8-883bc432f052\",\"NodeName\":\"回复1\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{test}} {{xxx}}\"},\"Inputs\":[{\"Name\":\"test\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"SYSTEM_VARIABLE\",\"SystemVariable\":{\"Name\":\"SYS.UserQuery\",\"DialogHistoryLimit\":15}},\"Desc\":\"desc\",\"IsRequired\":false},{\"Name\":\"xxx\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"ssss\"]}},\"Desc\":\"\",\"IsRequired\":false}],\"Outputs\":[],\"NextNodeIDs\":[\"d9957ace-fb20-3f16-8c07-cb4091183abb\"],\"NodeUI\":\"\"},{\"NodeID\":\"cfb71f8c-dfa0-de28-358d-9a8d4476803c\",\"NodeName\":\"参数提取2\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"ddcdc4b4-cacf-4967-ac1d-91392bd3d862\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"8e936789-b3e8-5e1b-fbc8-883bc432f052\"],\"NodeUI\":\"\"},{\"NodeID\":\"5b98a64b-dbb8-a7cc-9dea-899b2057415c\",\"NodeName\":\"条件判断1\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"cfb71f8c-dfa0-de28-358d-9a8d4476803c\"],\"Logical\":{\"LogicalOperator\":\"AND\",\"Compound\":[{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"fb40701a-e580-e67f-3d0e-fa526a8502bd\",\"JsonPath\":\"Output\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"aaa\"]}},\"MatchType\":\"SEMANTIC\"}},{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"fb40701a-e580-e67f-3d0e-fa526a8502bd\",\"JsonPath\":\"Output.Content\"}},\"LeftType\":\"STRING\",\"Operator\":\"NE\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"bbb\"]}},\"MatchType\":\"SEMANTIC\"}},{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"CUSTOM_VARIABLE\",\"CustomVarID\":\"3c412c08-c285-486a-9e24-d146c6f60ffc\"},\"LeftType\":\"STRING\",\"Operator\":\"CONTAINS\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"abc\"]}},\"MatchType\":\"SEMANTIC\"}},{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"SYSTEM_VARIABLE\",\"SystemVariable\":{\"Name\":\"SYS.UserQuery\",\"DialogHistoryLimit\":15}},\"LeftType\":\"STRING\",\"Operator\":\"NE\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"哈哈哈\"]}},\"MatchType\":\"SEMANTIC\"}}]}},{\"NextNodeIDs\":[\"8e936789-b3e8-5e1b-fbc8-883bc432f052\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"\"},{\"NodeID\":\"d9957ace-fb20-3f16-8c07-cb4091183abb\",\"NodeName\":\"新闻\",\"NodeDesc\":\"支持top20+新闻标签类别（要闻|财经|体育|科技|娱乐|房产|汽车|军事|国际|热点|热门|时政|国内…等）的查询，最多支持返回5篇\",\"NodeType\":\"PLUGIN\",\"PluginNodeData\":{\"PluginType\":\"CUSTOM\",\"PluginID\":\"93b86329-9503-4219-8748-526883bdfe3d\",\"ToolID\":\"ec6f8b82-2638-4153-b41c-ae40e5deebeb\",\"ToolInputs\":{\"API\":{\"URL\":\"\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"Keyword\",\"ParamDesc\":\"搜索新闻的关键词\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f2985eb6-986a-a93a-a028-5bc42d0e8841\",\"JsonPath\":\"Output.value\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"DateTime\",\"ParamDesc\":\"新闻事件\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"SYSTEM_VARIABLE\",\"SystemVariable\":{\"Name\":\"SYS.CurrentTime\",\"DialogHistoryLimit\":15}},\"IsRequired\":false,\"SubParams\":[]}]}},\"Inputs\":[],\"Outputs\":[{\"Title\":\"\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"返回码。0正常，非0异常\"},{\"Title\":\"\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"返回信息。Code为0：success，Code非0: 异常信息\"},{\"Title\":\"\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[],\"Desc\":\"返回数据。Code非0：{}，Code为0：NewsContent\"}],\"NextNodeIDs\":[\"59159030-c7e4-684f-b7a5-f254d4f90b24\"],\"NodeUI\":\"\"},{\"NodeID\":\"59159030-c7e4-684f-b7a5-f254d4f90b24\",\"NodeName\":\"代码1\",\"NodeDesc\":\"\",\"NodeType\":\"CODE_EXECUTOR\",\"CodeExecutorNodeData\":{\"Code\":\"\\n# 仅支持数据转换或运算等操作, 请勿手动import, 已引入numpy和pandas以及部分内置的运算相关的包；不支持IO操作，如读取文件，网络通信等。\\n# 请保存函数名为main,输入输出均为dict；最终结果会以json字符串方式返回，请勿直接返回不支持json.dumps的对象（numpy和pandas已增加额外处理）\\ndef main(params: dict) -> dict:\\n    return {\\n        'result': params.get('input', 0)\\n    }\\n\",\"Language\":\"PYTHON3\"},\"Inputs\":[{\"Name\":\"sss\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"8e936789-b3e8-5e1b-fbc8-883bc432f052\",\"JsonPath\":\"Output.Answer\"}},\"Desc\":\"\",\"IsRequired\":false}],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"xxx\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"xx\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"6a70ce6d-302c-587c-0e49-bede050e5ef7\"],\"NodeUI\":\"\"}],\"Edge\":\"\"}",
			paramInfoJson: "[{\"ParameterId\":\"40be9f5f-cd14-4228-8979-42547dcb6e33\",\"Name\":\"xxxx\",\"Desc\":\"sss\",\"Type\":\"ARRAY_STRING\",\"CorrectExamples\":[],\"IncorrectExamples\":[]},{\"ParameterId\":\"6b9c39f0-d08a-475d-bef6-2d8f34dcd6a2\",\"Name\":\"type123\",\"Desc\":\"输入类型123\",\"Type\":\"STRING\",\"CorrectExamples\":[\"正确类型1\",\"正确类型2\"],\"IncorrectExamples\":[\"错误类型1\",\"错误类型2\"]},{\"ParameterId\":\"ddcdc4b4-cacf-4967-ac1d-91392bd3d862\",\"Name\":\"网页地址\",\"Desc\":\"输入网页地址用于解析\",\"Type\":\"STRING\",\"CorrectExamples\":[],\"IncorrectExamples\":[]}]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			workflow, err := protoutil.JsonToWorkflow(tt.workflowJson)
			if err != nil {
				t.Errorf("JsonToWorkflow err: %v", err)
				return
			}
			var paramInfos []*KEP_WF.ParameterInfo
			var varParams []*entity.VarParams
			if tt.paramInfoJson != "" {
				err = json.Unmarshal([]byte(tt.paramInfoJson), &paramInfos)
				if err != nil {
					t.Errorf("json.Unmarshal paramInfos err: %v", err)
					return
				}
			}
			if tt.varParamsJson != "" {
				err = json.Unmarshal([]byte(tt.varParamsJson), &varParams)
				if err != nil {
					t.Errorf("json.Unmarshal varParams err: %v", err)
					return
				}
			}
			pdlContent, toolsInfo, err := p.Convert(ctx, workflow, paramInfos, varParams)
			t.Logf("Convert pdlContent:%v, toolsInfo:%v, err:%v", pdlContent, toolsInfo, err)
		})
	}
}

func Test_ClearUI(t *testing.T) {
	workflowJson := ""
	workflow, err := protoutil.JsonToWorkflow(workflowJson)
	if err != nil {
		t.Errorf("JsonToWorkflow err: %v", err)
		return
	}
	workflow.Edge = ""
	for _, node := range workflow.Nodes {
		node.NodeUI = ""
	}
	workflowJson, err = protoutil.WorkflowToJson(workflow)
	if err != nil {
		t.Errorf("WorkflowToJson err: %v", err)
		return
	}
	t.Logf(workflowJson)
}

func TestPDLImpl_CheckPDLValid(t *testing.T) {
	tests := []struct {
		name      string
		pdl       *PDLContent
		toolsInfo *KEP_WF.PDLToolsInfo
		want      bool
		wantMsg   string
	}{
		{
			name: "Valid PDL Content",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "test procedure",
				Slots: []PDLSlot{
					{Name: "slot1"},
					{Name: "slot2"},
				},
				Answers: []PDLAnswer{
					{Name: "answer1"},
					{Name: "answer2"},
				},
				APIs: []PDLApi{
					{Name: "tool1"},
					{Name: "tool2"},
				},
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{
				Tools: []*KEP_WF.PDLTool{
					{ToolName: "tool1"},
					{ToolName: "tool2"},
				},
			},
			want:    true,
			wantMsg: "",
		},
		{
			name: "Empty PDL Name",
			pdl: &PDLContent{
				Name:      "",
				Procedure: "test procedure",
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{},
			want:      false,
			wantMsg:   "PDL Name is empty",
		},
		{
			name: "Empty PDL Procedure",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "",
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{},
			want:      false,
			wantMsg:   "PDL Procedure is empty",
		},
		{
			name: "Empty Slot Name",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "test procedure",
				Slots: []PDLSlot{
					{Name: ""},
				},
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{},
			want:      false,
			wantMsg:   "PDL SLOTs Name is empty",
		},
		{
			name: "Empty Answer Name",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "test procedure",
				Slots:     []PDLSlot{{Name: "slot1"}},
				Answers: []PDLAnswer{
					{Name: ""},
				},
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{},
			want:      false,
			wantMsg:   "PDL ANSWERs Name is empty",
		},
		{
			name: "Empty API Name",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "test procedure",
				Slots:     []PDLSlot{{Name: "slot1"}},
				Answers:   []PDLAnswer{{Name: "answer1"}},
				APIs: []PDLApi{
					{Name: ""},
				},
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{},
			want:      false,
			wantMsg:   "PDL APIs Name is empty",
		},
		{
			name: "Invalid API Tool Name",
			pdl: &PDLContent{
				Name:      "test_pdl",
				Procedure: "test procedure",
				Slots:     []PDLSlot{{Name: "slot1"}},
				Answers:   []PDLAnswer{{Name: "answer1"}},
				APIs: []PDLApi{
					{Name: "invalid_tool"},
				},
			},
			toolsInfo: &KEP_WF.PDLToolsInfo{
				Tools: []*KEP_WF.PDLTool{
					{ToolName: "tool1"},
				},
			},
			want:    false,
			wantMsg: "PDL APIs Name[invalid_tool] is not in API list",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &pdlImpl{}
			got, gotMsg := p.CheckPDLValid(context.Background(), tt.pdl, tt.toolsInfo)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantMsg, gotMsg)
		})
	}
}
