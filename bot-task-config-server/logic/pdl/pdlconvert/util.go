package pdlconvert

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	botllm "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_llm_server"
)

// WFTopologicalSort 对画布做拓扑排序 返回排序后的结果
func WFTopologicalSort(startNode *KEP_WF.WorkflowNode, nodeMap map[string]*KEP_WF.WorkflowNode) []*KEP_WF.WorkflowNode {
	sortNodes := make([]*KEP_WF.WorkflowNode, 0, len(nodeMap))
	visited := make(map[string]bool)
	stack := make([]*KEP_WF.WorkflowNode, 0)

	topologicalSortUtil(nodeMap, startNode, visited, &stack)
	// Reverse the stack to get the topological order
	for i := len(stack) - 1; i >= 0; i-- {
		sortNodes = append(sortNodes, stack[i])
	}
	return sortNodes
}

func topologicalSortUtil(nodeMap map[string]*KEP_WF.WorkflowNode, node *KEP_WF.WorkflowNode, visited map[string]bool,
	stack *[]*KEP_WF.WorkflowNode) {
	for _, nextNodeID := range getNextNodeIDs(node) {
		if !visited[nextNodeID] {
			topologicalSortUtil(nodeMap, nodeMap[nextNodeID], visited, stack)
		}
	}

	*stack = append(*stack, node)
	visited[node.GetNodeID()] = true
}

// WFBFSSort 对画布做广度优先遍历 按遍历顺序返回结果
func WFBFSSort(startNode *KEP_WF.WorkflowNode, nodeMap map[string]*KEP_WF.WorkflowNode) []*KEP_WF.WorkflowNode {
	sortNodes := make([]*KEP_WF.WorkflowNode, 0, len(nodeMap))
	visited := make(map[string]bool)
	queue := []*KEP_WF.WorkflowNode{startNode}

	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]

		if !visited[node.GetNodeID()] {
			sortNodes = append(sortNodes, node)
			visited[node.GetNodeID()] = true
			for _, nextNodeID := range getNextNodeIDs(node) {
				if !visited[nextNodeID] {
					queue = append(queue, nodeMap[nextNodeID])
				}
			}
		}
	}
	return sortNodes
}

// GetTextTokenLen 获取文本的token量
func GetTextTokenLen(ctx context.Context, text string) int {
	if len(text) == 0 {
		return 0
	}
	rsp, err := proxy.GetBotLLMProxy().GetToken(ctx, &botllm.GetTokenReq{
		RequestId: util.RequestID(ctx),
		Text:      text,
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetTextTokenLen,err:%+v", err)
		return len([]rune(text))
	}
	return int(rsp.GetLength())
}
