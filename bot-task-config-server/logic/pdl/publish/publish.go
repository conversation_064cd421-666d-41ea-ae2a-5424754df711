package publish

import (
	"context"

	pdlPublishDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/pdl/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
)

// Publish 发布相关接口
type Publish interface {
	// PDLPublish 工作流发布
	PDLPublish(ctx context.Context, robotID string, taskID uint64, pdlIDs []string) error

	// IsPDLPublished 判断机器人是否有PDL发布
	IsPDLPublished(ctx context.Context, robotID string, envType uint32) (bool, error)

	// GetPublishedPDL 获取发布后的PDL
	GetPublishedPDL(ctx context.Context, robotID string, pdlIDs []string) (
		map[string]*entity.WorkflowPDL, error)

	// IsPDLPublishedQueryById 判断应用下工作流是否发布
	IsPDLPublishedQueryById(ctx context.Context, robotID, workflowId string,
		envType uint32) (bool, error)
}

// publish ...
type publish struct {
	pdlPublishDao pdlPublishDao.Dao
}

// NewPublish new publish logic
func NewPublish() Publish {
	return &publish{
		pdlPublishDao: pdlPublishDao.NewDao(),
	}
}
