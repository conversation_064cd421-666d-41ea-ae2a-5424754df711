package publish

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"gorm.io/gorm"
)

// PDLPublish PDL发布
func (p publish) PDLPublish(ctx context.Context, robotID string, taskID uint64, pdlIDs []string) error {
	ctx = log.WithContextFields(ctx, "TaskID", strconv.FormatUint(taskID, 10))
	log.InfoContextf(ctx, "PDLPublish, robotID: %s, taskID: %d, pdlIDs: %v",
		robotID, taskID, pdlIDs)
	if len(robotID) == 0 || taskID <= 0 || len(pdlIDs) == 0 {
		return nil
	}

	// 待发布PDL t_workflow_pdl
	workflowPDLs, err := p.pdlPublishDao.GetUnPublishPDL(ctx, robotID, pdlIDs)
	if err != nil || len(workflowPDLs) == 0 {
		log.ErrorContextf(ctx, "PDLPublish GetUnPublishPDL, workflowPDLs: %+v, err: %v", workflowPDLs, err)
		return errors.ErrSyncConfigDataLoadFailed
	}

	err = p.publishWorkflowPDL(ctx, robotID, workflowPDLs)
	if err != nil {
		log.ErrorContextf(ctx, "PDLPublish publishWorkflowPDL failed, err: %+v", err)
		return err
	}
	log.InfoContextf(ctx, "PDLPublish success|len(workflowPDLs):%d", len(workflowPDLs))
	return nil
}

// publishWorkflow 发布工作流
func (p publish) publishWorkflowPDL(ctx context.Context, robotID string,
	workflowPDLs []*entity.WorkflowPDL) (err error) {
	// 前处理
	preErr := p.prePublish(ctx, workflowPDLs)

	// 后处理
	defer func() {
		postErr := p.postPublish(ctx, err, workflowPDLs)
		if postErr != nil {
			err = appendErrMsg(err, fmt.Errorf("postErr:%+v", postErr))
		}
	}()

	if preErr != nil {
		err = appendErrMsg(err, fmt.Errorf("preErr:%+v", preErr))
		return err
	}

	// 开启正式环境事物
	workflowTX := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowProdGORM())
	defer func() {
		// 事物提交或者回滚
		workflowTXErr := db.CommitOrRollbackTx(ctx, workflowTX, err)
		if workflowTXErr != nil {
			err = appendErrMsg(err, workflowTXErr)
		}
	}()

	// 配置端数据
	if configErr := p.publishConfigData(ctx, workflowTX, workflowPDLs); configErr != nil {
		err = errors.ErrSyncConfigDataReleaseFailed
		return err
	}

	//// 运行时数据
	//if runtimeErr := p.publishRuntimeData(ctx, robotID, workflowPDLs); runtimeErr != nil {
	//	err = errors.ErrSyncRunDataReleaseFailed
	//	return err
	//}

	return nil
}

// prePublish 发布前处理
func (p publish) prePublish(ctx context.Context, workflowPDLs []*entity.WorkflowPDL) error {
	// 发布之前：更新工作流当前修改人
	staffID := util.StaffID(ctx)
	log.InfoContextf(ctx, "prePublish staffID:%v", staffID)
	if staffID != 0 {
		if err := p.pdlPublishDao.UpdatePDLStaffID(ctx, database.GetLLMRobotWorkflowGORM(),
			workflowPDLs, staffID); err != nil {
			return err
		}
	}
	// 发布之前：发布状态设置为发布中
	releaseStatus := entity.ReleaseStatusPublishing
	if err := p.updatePublishDataReleaseStatus(ctx, releaseStatus, workflowPDLs); err != nil {
		return err
	}
	return nil
}

// postPublish 发布后处理
func (p publish) postPublish(ctx context.Context, err error, workflowPDLs []*entity.WorkflowPDL) error {
	// 发布之后：发布状态根据情况设置
	var releaseStatus string
	if err == nil {
		// 发布成功
		releaseStatus = entity.ReleaseStatusPublished
	} else {
		// 发布失败
		releaseStatus = entity.ReleaseStatusFail
	}
	if err := p.updatePublishDataReleaseStatus(ctx, releaseStatus, workflowPDLs); err != nil {
		return err
	}
	return nil
}

// updatePublishDataReleaseStatus 更新发布数据发布状态
func (p publish) updatePublishDataReleaseStatus(ctx context.Context, releaseStatus string,
	workflowPDLs []*entity.WorkflowPDL) (err error) {
	log.InfoContextf(ctx, "updatePublishDataReleaseStatus|releaseStatus:%s|"+
		"len(workflowPDLs):%d", releaseStatus, len(workflowPDLs))

	// 开启测试环境事物
	workflowTX := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事物提交或者回滚
		workflowTXErr := db.CommitOrRollbackTx(ctx, workflowTX, err)
		if workflowTX != nil {
			err = appendErrMsg(err, workflowTXErr)
		}
		if err != nil {
			log.ErrorContextf(ctx, "updatePublishDataReleaseStatus failed, err:%+v", err)
		}
	}()

	if err = p.pdlPublishDao.UpdatePDLReleaseStatus(ctx, workflowTX, workflowPDLs, releaseStatus); err != nil {
		return err
	}

	log.InfoContextf(ctx, "updatePublishDataReleaseStatus done")
	return nil
}

// appendErrMsg 添加错误信息
func appendErrMsg(err, newErr error) error {
	if err == nil {
		return newErr
	}
	if newErr == nil {
		return err
	}
	return fmt.Errorf(err.Error() + newErr.Error())
}

// publishConfigData 配置端数据发布
func (p publish) publishConfigData(ctx context.Context, workflowTX *gorm.DB,
	workflowPDLs []*entity.WorkflowPDL) error {
	// t_workflow_pdl
	if err := p.pdlPublishDao.PublishPDL(ctx, workflowTX, workflowPDLs); err != nil {
		return err
	}

	return nil
}

//// publishRuntimeData 运行时数据发布
//func (p publish) publishRuntimeData(ctx context.Context, robotID string,
//	workflowPDLs []*entity.WorkflowPDL) error {
//	// Redis数据
//	// TODO(halelv): redis
//
//	// Vector数据
//	// TODO(halelv): vector
//
//	// Workflow-chat数据
//	// TODO(halelv): chat
//
//	return nil
//}

// IsPDLPublished 判断机器人是否有PDL发布
func (p publish) IsPDLPublished(ctx context.Context, robotID string, envType uint32) (bool, error) {
	log.InfoContextf(ctx, "IsPDLPublished robotID:%s, envType:%d", robotID, envType)
	count, err := p.pdlPublishDao.CountPublishedPDLs(ctx, robotID, envType)
	if err != nil || count <= 0 {
		return false, err
	}
	return true, nil
}

// GetPublishedPDL 获取发布后的PDL
func (p publish) GetPublishedPDL(ctx context.Context, robotID string, workflowPDLIDs []string) (
	map[string]*entity.WorkflowPDL, error) {
	log.InfoContextf(ctx, "GetPublishedPDL, robotID: %s, workflowPDLIDs: %v", robotID, workflowPDLIDs)
	workflowPDLs, err := p.pdlPublishDao.GetPublishedPDLs(ctx, robotID, workflowPDLIDs)
	if err != nil {
		return nil, err
	}
	pdlMap := make(map[string]*entity.WorkflowPDL)
	for _, pdl := range workflowPDLs {
		pdlMap[pdl.PdlID] = pdl
	}
	return pdlMap, nil
}

// IsPDLPublishedQueryById 通过ID判断PDL是否发布
func (p publish) IsPDLPublishedQueryById(ctx context.Context, robotID, workflowId string,
	envType uint32) (bool, error) {
	return p.pdlPublishDao.IsPDLPublishedById(ctx, robotID, workflowId, envType)
}
