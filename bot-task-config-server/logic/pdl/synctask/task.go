package synctask

import (
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/admin"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/pdl/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/pdl/synctask/synclog"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"sort"
	"strings"
	"time"
)

// GetPDLReleaseStatus 查询发布状态
func GetPDLReleaseStatus(ctx context.Context, req *KEP.GetTaskFlowReleaseStatusReq) (
	*KEP.GetTaskFlowReleaseStatusResp, error) {
	var sid = util.RequestID(ctx)
	var envType = req.GetEnvTag()
	botBizId := req.GetBotBizId()
	var scene uint32 = 1
	if envType == 1 {
		scene = 2
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobotWithNoUserLogin(ctx, scene, botId)
	if errors.Is(err, errors.ErrRobotNotFound) {
		log.Warnf("P|GetPDLReleaseStatus|%s|%v", sid, err)
		return &KEP.GetTaskFlowReleaseStatusResp{
			IsRelease: false,
		}, nil
	}
	if err != nil {
		log.Errorf("P|GetPDLReleaseStatus|%s|%v", sid, err)
		return nil, err
	}

	isRelease, err := publish.NewPublish().IsPDLPublished(ctx, botBizId, envType)
	rsp := KEP.GetTaskFlowReleaseStatusResp{
		IsRelease: isRelease,
	}
	log.Infof("O|GetPDLReleaseStatus|%s|%s|ERR:%v", sid, botBizId, envType)
	if err != nil {
		log.Errorf("E|GetPDLReleaseStatus|%s|%s", sid, err.Error())
		return &rsp, err
	}
	return &rsp, nil
}

// ListPDLPreview 获取发布列表
func ListPDLPreview(ctx context.Context, req *KEP.ListTaskFlowPreviewReq) (*KEP.ListTaskFlowPreviewRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "ListPDLPreview:req", req)

	sid := util.RequestID(ctx)
	clues.AddTrackData(ctx, "sid", sid)

	// 获取任务发布内容
	if req.GetReleaseBizId() > 0 {
		req0 := &KEP.GetDataSyncTaskReq{
			BotBizId:      req.GetBotBizId(),
			TaskID:        req.GetReleaseBizId(),
			ReleaseStatus: req.ReleaseStatus,
		}
		if len(req0.ReleaseStatus) == 0 {
			req0.ReleaseStatus = []uint32{4}
		}
		resp0, err := GetDataSyncTask(ctx, req0)
		log.Infof("R|ListPDLPreview|GetDataSyncTask|%s|%+v|%+v|%+v", sid, req0, resp0, err)
		if err != nil {
			return nil, err
		}
		resp := &KEP.ListTaskFlowPreviewRsp{Total: uint32(len(resp0.GetTask().GetSyncItems()))}
		for _, t := range resp0.GetTask().GetSyncItems() {
			e := &KEP.ListTaskFlowPreviewRsp_TaskFlow{
				FlowId:     t.GetID(),
				Name:       t.GetTitle(),
				UpdateTime: t.GetUpdateTime() / 1000,
				Action:     uint32(entity.EnumToAction[t.Action]),
				ActionDesc: entity.EnumActionDescription[t.Action],
				Message:    t.GetMessage(),
			}
			resp.List = append(resp.List, e)
		}
		return resp, nil
	}

	// 如果有正在发布的任务, 返回空列表
	corpID := util.CorpID(ctx)
	releaseTaskID, releaseTaskStatus, releaseError := admin.DescribeLatestReleaseStatus(ctx, req.GetBotBizId(), corpID)
	log.Infof("DescribeLatestReleaseStatus|%s|BOT:%v|CORP:%v|TASK:%v|%v|ERR:%v",
		sid, req.GetBotBizId(), corpID, releaseTaskID, releaseTaskStatus, releaseError)
	// 正在发布中
	if releaseError == nil && releaseTaskStatus == 1 {
		return &KEP.ListTaskFlowPreviewRsp{}, nil
	}

	robotID := fmt.Sprintf("%d", req.GetBotBizId())
	params := entity.ListPDLParams{
		Query:         strings.TrimSpace(req.GetQuery()),
		Page:          req.GetPageNumber(),
		PageSize:      req.GetPageSize(),
		BotBizId:      fmt.Sprintf("%d", req.GetBotBizId()),
		FlowState:     []string{entity.WorkflowPdlStateEnable, entity.WorkflowPdlStatePublishedChange},
		ReleaseStatus: []string{entity.ReleaseStatusUnPublished, entity.ReleaseStatusFail},
	}
	for _, val := range req.GetActions() {
		params.Actions = append(params.Actions, entity.PublishActionDesc[fmt.Sprintf("%d", val)])
	}
	if req.GetStartTime() != 0 {
		params.StartTime = time.Unix(int64(req.GetStartTime()), 0)
	}
	if req.GetEndTime() != 0 {
		params.EndTime = time.Unix(int64(req.GetEndTime()), 0)
	}
	t0 := time.Now()
	log.Infof("I|ListPDLPreview|%s|%+v", sid, params)
	total := synctask.CountUnpublishedPDLWithQuery(ctx, robotID, params)
	workflowPDLs, err := synctask.ListUnpublishedPDLWithQuery(ctx, robotID, params)
	log.Infof("O|ListPDLPreview|%s|%d|len:%d|%+v|ERR:%v|%s",
		sid, total, len(workflowPDLs), workflowPDLs, err, time.Since(t0))
	if err != nil {
		log.Errorf("E|ListPDLPreview|%s|%s", sid, err.Error())
		return nil, errors.ErrTaskFlowLoadFailed
	}
	resp := &KEP.ListTaskFlowPreviewRsp{Total: uint32(total)}
	for _, t := range workflowPDLs {
		e := &KEP.ListTaskFlowPreviewRsp_TaskFlow{
			FlowId:     t.PdlID,
			Name:       t.WorkflowName,
			UpdateTime: uint64(t.UpdateTime.Unix()),
			Action:     uint32(entity.EnumToAction[t.Action]),
			ActionDesc: entity.EnumActionDescription[t.Action],
		}
		resp.List = append(resp.List, e)
	}

	return resp, nil
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
func GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error) {
	sid := util.RequestID(ctx)
	robotID := toRobotID(req.GetBotBizId())

	st, e1 := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
	if e1 != nil {
		log.Errorf("GetDataSyncTask|e1|%s|ERR:%v", sid, e1)
		return nil, errors.ErrSyncTaskLoadFailed
	}
	if st.TaskID == 0 { // 任务不存在
		log.Warnf("GetDataSyncTask|st|%s|%+v:is nil", sid, st)
		return &KEP.GetDataSyncTaskRsp{}, nil
	}

	var taskLogs []entity.SyncTaskLog
	var e2 error
	if len(req.ReleaseStatus) == 0 {
		taskLogs, e2 = synclog.ListSyncTaskLogByTask(ctx, robotID, req.GetTaskID())
	} else {
		detailStatus := taskStatusConvert(req.ReleaseStatus)
		has := hasStatus(st.Status, detailStatus)
		if has {
			taskLogs, e2 = synclog.ListSyncTaskLogByTask(ctx, robotID, req.GetTaskID())
		}
	}
	if e2 != nil {
		log.Errorf("GetDataSyncTask|e2|%s|ERR:%v", sid, e2)
		return nil, errors.ErrSyncTaskLogLoadFailed
	}

	resp := &KEP.GetDataSyncTaskRsp{
		Task: &KEP.DataSyncTask{
			TaskID:       req.GetTaskID(),
			Operator:     st.UserID,
			Status:       uint32(st.Status),
			SyncItems:    nil,
			SuccessCount: st.SuccessCount,
			FailCount:    st.FailedCount,
		},
	}
	if !st.DoneTime.IsZero() {
		resp.Task.UpdateTime = uint64(st.DoneTime.UnixMilli())
	}
	var syncItems []*KEP.SyncItem
	for i := 0; i < len(taskLogs); i++ {
		if taskLogs[i].Type != string(entity.LogTypeResult) {
			continue
		}
		var items []*KEP.SyncItem
		err := json.Unmarshal([]byte(taskLogs[i].LogContent), &items)
		if err != nil {
			log.Errorf("%d|GetDataSyncTask|%d|json.Unmarshal|%s|%v", i, sid, taskLogs[i].LogContent, err)
			continue
		}
		syncItems = append(syncItems, items...)
	}

	// 下发数据, 需要去重, 优先输出最新的数据
	sort.Slice(syncItems, func(i, j int) bool { return syncItems[i].UpdateTime > syncItems[j].UpdateTime })
	uniqItem := make(map[string]struct{}, 2*len(syncItems))
	for _, si := range syncItems {
		if _, ok := uniqItem[si.GetID()]; ok {
			continue
		}
		uniqItem[si.GetID()] = struct{}{}
		resp.Task.SyncItems = append(resp.Task.SyncItems, si)
	}
	return resp, nil
}

// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
func GetDataSyncTasks(ctx context.Context, req *KEP.GetDataSyncTasksReq) (*KEP.GetDataSyncTasksRsp, error) {
	//TODO implement me
	panic("implement me")
}

// GetUnreleasedCount 未发布数量
func GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error) {
	c := uint32(synctask.CountUnpublishedPDL(ctx, toRobotID(req.BotBizId)))
	return &KEP.GetUnreleasedCountRsp{
		Count: c,
	}, nil
}

// CheckRobotReady 检查机器人, 是否准备好
func CheckRobotReady(ctx context.Context, req *KEP.CheckRobotReadyReq) (*KEP.CheckRobotReadyRsp, error) {
	return &KEP.CheckRobotReadyRsp{
		Ready: true,
	}, nil
}

func taskStatusConvert(releaseStatus []uint32) []int {
	// 发布状态, 任务状态, 关系映射
	var releaseStatusAndTaskStatusMapping = map[uint32]int{
		2: 1, // 待发布
		4: 3, // 已发布
		5: 4, // 发布失败
	}
	var t []int
	for i := 0; i < len(releaseStatus); i++ {
		if v, ok := releaseStatusAndTaskStatusMapping[releaseStatus[i]]; ok {
			t = append(t, v)
		}
	}
	return t
}

func hasStatus(s int, full []int) bool {
	if len(full) == 0 {
		return false
	}
	for i := 0; i < len(full); i++ {
		if s == full[i] {
			return true
		}
	}
	return false
}

func toRobotID(botBizID uint64) string {
	return fmt.Sprintf("%d", botBizID)
}
