package synctask

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/pdl/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"strconv"
	"time"
)

// CheckUnfinishedTask 检查未执行完成的任务
func CheckUnfinishedTask() {
	ticker := time.NewTicker(5 * time.Second)
	go func() {
		for range ticker.C {
			log.Infof("TIMER|CheckUnfinishedTask|%s", time.Now())
			processQueueTask()
		}
	}()
}

// processQueueTask 执行队列任务
func processQueueTask() {
	// 系统可以同时运行的任务数, 如果超出, 则不执行
	ctx := trpc.BackgroundContext()
	sid := "TIMER:" + encode.GenerateSessionID()
	ctx = log.WithContextFields(ctx, "RequestID", sid)
	util.WithRequestID(ctx, sid)
	c := synctask.CountSyncTaskByStatus(ctx, entity.TaskStatusProcessing)
	log.Infof("RunningReleaseTask|%s|%c", sid, c)
	if c >= config.GetMainConfig().DataSyncTask.ReleaseQueueLength {
		return
	}
	tasks := listQueuingSyncTasks(ctx, int(config.GetMainConfig().DataSyncTask.ReleaseQueueLength-c))
	if len(tasks) == 0 {
		log.Infof("processQueueTask|%s|NOTING TO DO", sid)
		return
	}
	log.Infof("processQueueTask|Queuing|%s|len:%d|%v", sid, len(tasks), tasks)
	for i := 0; i < len(tasks); i++ {
		ctx2 := trpc.CloneContext(ctx)
		go func(t entity.SyncTask) {
			e := GetLastSyncTaskEvent(ctx2, t.RobotID, t.TaskID)
			bizID, _ := strconv.ParseUint(t.RobotID, 10, 64)
			releaseTasks(ctx2, bizID, t.TaskID, e)
		}(tasks[i])
	}
}

// listQueuingSyncTasks 查出当现排队的任务, 不分机器人
func listQueuingSyncTasks(ctx context.Context, limit int) []entity.SyncTask {
	sid := util.RequestID(ctx)
	tasks, err := synctask.ListSyncTasksByStatus(ctx, entity.TaskStatusQueuing, limit)
	if err != nil {
		log.Errorf("R|ListQueuingSyncTasks|%s|ERR:%v", sid, err)
		return nil
	}
	return tasks
}
