/*
 * 2024-12-22
 * Copyright (c) 2024. le<PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package pdl

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/pdlconvert"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/ghodss/yaml"
	"gorm.io/gorm"
)

// SaveWorkflowPDL 编辑更新PDL，检查白名单
func SaveWorkflowPDL(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq) (*KEP_WF.SaveAgentWorkflowRsp, error) {
	var err error
	var workflowPDL *entity.WorkflowPDL
	sid := util.RequestID(ctx)
	appBizID := req.GetAppBizId()
	workflowName := strings.TrimSpace(req.GetName())
	workflowPdlContent := req.GetPDLContent()
	workflowApiInfo := strings.TrimSpace(req.GetApiInfo())
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	saveType := req.GetSaveType()
	constraints := req.GetUserAdditionalConstraints()
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflowPDL|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	// 检查PDL白名单
	usePDL := appInfo.GetKnowledgeQa().GetWorkflow().GetUsePdl()
	log.InfoContextf(ctx, "SaveWorkflowPDL|usePDL:%s", usePDL)
	if !usePDL {
		return nil, errors.ErrPermissionDenied
	}
	// 调试保存才做校验PDL工作流内容
	if saveType == uint32(KEP_WF.SaveAgentWorkflowReq_ENABLE) {
		err = checkPdlAndApiValid(ctx, workflowPdlContent, workflowApiInfo)
		if err != nil {
			log.WarnContextf(ctx, "SaveWorkflowPDL|checkPdlAndApiValid|err:%+v",
				workflowPdlContent, err)
			return nil, err
		}
	}

	// 检查工作流是否存在
	if workflowPDL, err = db.GetWorkflowPDLDetail(ctx, workflowID, appBizID); err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.ErrWorkflowNotFound
		}
		log.ErrorContextf(ctx, "SaveWorkflowPDL|GetWorkflowDetail err:%+v", err)
		return nil, err
	}
	if workflowPDL == nil || len(workflowPDL.WorkflowID) == 0 {
		return nil, errors.ErrWorkflowNotFound
	}
	// 检查版本
	if req.GetWorkflowVersion() != workflowPDL.Version {
		log.InfoContextf(ctx, "SaveWorkflowPDL|Req.version:%d|db.version:%d",
			req.GetWorkflowVersion(), workflowPDL.Version)
		return nil, errors.ErrWorkflowWrongVersion
	}
	// 检查状态
	if workflowPDL.IsWorkflowPDLConverting() {
		return nil, errors.ErrWorkflowConvertingNotAllowEdit
	}
	if workflowPDL.ReleaseStatus == entity.WorkflowPdlReleaseStatusPublishing {
		return nil, errors.ErrWorkflowPublishingNotAllowEdit
	}
	// 保存调试
	staffID := util.StaffID(ctx)
	modifyWorkflowParams := entity.ModifyWorkflowPDLParams{
		PdlID:              workflowPDL.PdlID,
		PdlSnapshotVersion: workflowPDL.PdlSnapshotVersion,
		WorkflowID:         workflowID,
		WorkflowName:       workflowName,
		Version:            req.GetWorkflowVersion(),
		PdlContent:         workflowPdlContent, // Yaml
		ApiInfo:            workflowApiInfo,    // Json
		Constraints:        constraints,
		StaffID:            staffID,
		UpdateTime:         time.Now(),
		AppBizID:           appBizID,
		IsDebug:            saveType,
	}
	newWorkflow, err := db.SaveWorkflowPDL(ctx, modifyWorkflowParams)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflowPDL|db.SaveWorkflowPDL|err:%+v", err)
		return nil, err
	}

	return &KEP_WF.SaveAgentWorkflowRsp{
		WorkflowId:      workflowID,
		WorkflowVersion: newWorkflow.Version,
	}, nil
}

// checkPdlContent
func checkPdlAndApiValid(ctx context.Context, workflowPdlContent, workflowApiInfo string) error {
	var err error

	if len(workflowPdlContent) == 0 {
		return errors.WorkflowPdlContentError("PDL内容不能为空")
	}

	pdlTokenLen := pdlconvert.GetTextTokenLen(ctx, workflowPdlContent)
	log.InfoContextf(ctx, "checkPdlContent|pdlContentYaml token len:%d, text len:%d", pdlTokenLen, len([]rune(workflowPdlContent)))
	if pdlTokenLen > config.GetMainConfig().PDLConvertConfig.PDLTokenLenLimit {
		err = fmt.Errorf("PDL token长度(%d)超过限制(%d)", pdlTokenLen, config.GetMainConfig().PDLConvertConfig.PDLTokenLenLimit)
		log.WarnContextf(ctx, "checkPdlContent|err:%+v", err)
		return errors.WorkflowPdlContentError(err.Error())
	}

	// 检验PDL INFO内容
	pdlContent := pdlconvert.PDLContent{}
	err = yaml.Unmarshal([]byte(workflowPdlContent), &pdlContent)
	if err != nil {
		log.WarnContextf(ctx, "checkPdlContent|Unmarshal|err:%+v", err)
		return errors.WorkflowPdlContentError(err.Error())
	}

	// 检验API INFO内容，看是否可以转换成ToolsInfo PB
	toolsInfo, err := protoutil.JsonToPDLToolsInfo(workflowApiInfo)
	if err != nil {
		log.WarnContextf(ctx, "SaveWorkflowPDL|JsonToPDLToolsInfo|err:%+v", err)
		return errors.WorkflowApiInfoError(err.Error())
	}

	// 检查PdlContent和ApiInfo合法性
	valid, errStr := pdlconvert.NewPDL().CheckPDLValid(ctx, &pdlContent, toolsInfo)
	if !valid {
		log.WarnContextf(ctx, "SaveWorkflowPDL|CheckPDLValid|err:%+v", errStr)
		return errors.WorkflowPdlContentError(errStr)
	}

	return nil
}

// ListWorkflowPDL pdl工作流列表，检查白名单
func ListWorkflowPDL(ctx context.Context,
	req *KEP_WF.ListAgentWorkflowReq) (*KEP_WF.ListAgentWorkflowRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.ListAgentWorkflowRsp{}
	sid := util.RequestID(ctx)
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowPDL|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	usePDL := appInfo.GetKnowledgeQa().GetWorkflow().GetUsePdl()
	log.InfoContextf(ctx, "ListWorkflowPDL|usePDL:%s", usePDL)
	// 获取工作流列表
	params := entity.ListWorkflowParams{
		Query:     strings.TrimSpace(req.GetQuery()),
		StartTime: time.Unix(int64(req.GetStartTime()), 0),
		EndTime:   time.Unix(int64(req.GetEndTime()), 0),
		Page:      req.GetPage(),
		PageSize:  req.GetPageSize(),
		BotBizId:  appBizId,
	}
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息，但这里不涉及获取DialogJson，不做加解密处理
	workflows, total, err := db.ListWorkflow(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowPDL|%s|%v", sid, err)
		return nil, errors.ErrWorkflowNotFound
	}

	// 添加WorkflowPDL参数，获取PDL列表
	workflowIds := make([]string, 0)
	workflowPdlMap := make(map[string]*entity.WorkflowPDL)
	// 查看白名单
	if usePDL {
		for _, workflowElem := range workflows {
			workflowIds = append(workflowIds, workflowElem.WorkflowID)
		}

		// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息，但这里不涉及获取DialogJson，不做加解密处理
		workflowPdls, err := db.ListWorkflowPDL(ctx, workflowIds)
		if err != nil {
			log.ErrorContextf(ctx, "ListWorkflow|%s|%v", sid, err)
			return nil, err
		}

		for _, workflowElem := range workflowPdls {
			workflowPdlMap[workflowElem.WorkflowID] = workflowElem
		}
	}

	// 获取staffIds
	staffIds := make([]uint64, 0)
	mm := make(map[uint64]struct{}, 0)

	for _, workflowElem := range workflows {
		staffId := workflowElem.StaffID
		if workflowPDL, isPDL := workflowPdlMap[workflowElem.WorkflowID]; isPDL {
			staffId = workflowPDL.StaffID
		}
		if _, ok := mm[staffId]; !ok {
			staffIds = append(staffIds, staffId)
			mm[staffId] = struct{}{}
		}
	}

	staffInfosMap, err := getStaffInfoMapByStaffIds(ctx, staffIds)
	if err != nil {
		return nil, errors.OpDataFromDBError(fmt.Sprintf("获取员工信息失败：%s", err.Error()))
	}

	list := make([]*KEP_WF.ListAgentWorkflowRsp_Workflow, 0, len(workflows))
	for _, workflowElem := range workflows {
		if workflowPDL, ok := workflowPdlMap[workflowElem.WorkflowID]; ok {
			nickName := staffInfosMap[workflowPDL.StaffID]
			if len(nickName) == 0 {
				nickName = workflowElem.SubUin
			}
			// PDL工作流
			list = append(list, &KEP_WF.ListAgentWorkflowRsp_Workflow{
				WorkflowId:     workflowPDL.WorkflowID,
				Name:           workflowElem.WorkflowName,
				Status:         workflowPDL.FrontReleaseStatus(),
				StatusDesc:     workflowPDL.FrontReleaseStatusDesc(),
				UpdateTime:     uint32(workflowPDL.UpdateTime.Unix()),
				CreateTime:     uint32(workflowPDL.PdlCreateTime.Unix()), // 触发转换时画布的更新时间
				IsAllowEdit:    workflowPDL.IsAllowEdit(),
				IsAllowDelete:  workflowPDL.IsAllowDelete(),
				IsAllowRelease: workflowPDL.IsAllowRelease(),
				IsEnable:       workflowPDL.GetIsEnable(),
				Desc:           workflowElem.WorkflowDesc,
				UpdateUser:     nickName,
				Type:           frontDisplayType(workflowPDL),
				Reason:         workflowPDL.FailReason,
			})
		} else {
			canvasState, reasonCode, err := checkWorkflowConvertibleState(ctx, workflowElem, usePDL)
			log.InfoContextf(ctx, "ListWorkflow|checkWorkflowConvertibleState|state:%s, reasonCode:%s, err:%v",
				canvasState, reasonCode, err)
			var reason string // 不可转换的原因
			if reasonCode != "" {
				reason = entity.GetPDLReasonDesc(ctx, reasonCode)
			}
			nickName := staffInfosMap[workflowElem.StaffID]
			if len(nickName) == 0 {
				nickName = workflowElem.SubUin
			}
			// 工作流画布
			list = append(list, &KEP_WF.ListAgentWorkflowRsp_Workflow{
				WorkflowId:     workflowElem.WorkflowID,
				Name:           workflowElem.WorkflowName,
				Status:         canvasState,
				StatusDesc:     entity.FrontWorkflowPdlStatusPublishDesc[canvasState],
				UpdateTime:     uint32(workflowElem.UpdateTime.Unix()),
				CreateTime:     uint32(workflowElem.CreateTime.Unix()),
				IsAllowEdit:    canvasState == entity.WorkflowPdlFrontStatusUnConverted, // 待转换=>可编辑
				IsAllowDelete:  false,                                                   // 不可删除
				IsAllowRelease: false,                                                   // 不可发布
				IsEnable:       false,                                                   // 不可可用
				Desc:           workflowElem.WorkflowDesc,
				UpdateUser:     nickName,
				Type:           KEP_WF.AgentWorkflowType_CANVAS,
				Reason:         reason,
			})
		}
	}
	rsp.List = list
	rsp.Total = uint32(total)

	return rsp, nil
}

// GetWorkflowInfo 获取某个pdl的运行时信息（内部对话服务使用）
func GetWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq) (*KEP_WF.GetAgentWorkflowInfoRsp, error) {
	var err error
	sid := util.RequestID(ctx)
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	appBizID := strings.TrimSpace(req.GetAppBizId())
	var envType = req.GetEnvTag()
	rsp := &KEP_WF.GetAgentWorkflowInfoRsp{}
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在（不检查登陆态）
	_, err = permission.CheckRobotWithNoUserLogin(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowInfo|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}

	// 检查PDL是否存在且未删除
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息，但这里不涉及获取DialogJson，不做加解密处理
	workflowPDLs, err := db.GetWorkflowPDLInfo(ctx, workflowID, appBizID, envType)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	for _, workflow := range workflowPDLs {
		rsp.AgentWorkflowInfoList = append(rsp.AgentWorkflowInfoList, &KEP_WF.GetAgentWorkflowInfoRsp_AgentWorkflowInfo{
			WorkflowId:                workflow.WorkflowID,
			PDLContent:                workflow.PdlContent,
			ApiInfo:                   workflow.ToolsInfo,
			Parameters:                workflow.Parameter,
			UserAdditionalConstraints: workflow.UserConstraints,
		})
	}

	return rsp, nil
}

// GetWorkflowPDLDetail 获取某个pdl的详细信息，检查白名单
func GetWorkflowPDLDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq) (*KEP_WF.GetAgentWorkflowDetailRsp, error) {
	sid := util.RequestID(ctx)
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	appBizID := strings.TrimSpace(req.GetAppBizId())
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLDetail|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}

	usePDL := appInfo.GetKnowledgeQa().GetWorkflow().GetUsePdl()
	log.InfoContextf(ctx, "GetWorkflowPDLDetail|usePDL:%s", usePDL)
	// 获取工作流，如果不存在就返回错误
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息
	workflow, err := db.GetWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.ErrWorkflowNotFound
		}
		return nil, err
	}

	if workflow == nil || workflow.WorkflowID == "" {
		return nil, errors.ErrWorkflowNotFound
	}

	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
	decryptedDialogJsonDraft, err := protoutil.DecryptWorkflowJson(workflow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLDetail|DecryptWorkflowJson:%s|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("画布信息解密失败")
	}
	workflow.DialogJsonDraft = decryptedDialogJsonDraft
	decryptedDialogJsonEnable, err := protoutil.DecryptWorkflowJson(workflow.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLDetail|DecryptWorkflowJson:%s|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("画布信息解密失败")
	}
	workflow.DialogJsonEnable = decryptedDialogJsonEnable

	// 检查PDL是否存在
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息
	workflowPDL, errFindPDL := db.GetWorkflowPDLDetail(ctx, workflowID, appBizID)
	if errFindPDL != nil && !errors.Is(errFindPDL, gorm.ErrRecordNotFound) {
		return nil, errFindPDL
	}

	// 如果没有查到PDL，则返回Workflow
	if !usePDL || (errFindPDL != nil && errors.Is(errFindPDL, gorm.ErrRecordNotFound)) {
		canvasConvertState, _, _ := checkWorkflowConvertibleState(ctx, workflow, usePDL)
		return &KEP_WF.GetAgentWorkflowDetailRsp{
			WorkflowId:      workflowID,
			WorkflowVersion: workflow.Version,
			Name:            workflow.WorkflowName,
			Desc:            workflow.WorkflowDesc,
			//FlowState:                 canvasConvertState,
			//FlowStateDesc:             entity.FrontWorkflowPdlStatusPublishDesc[canvasConvertState],
			ReleaseStatus:             canvasConvertState,
			ReleaseStatusDesc:         entity.FrontWorkflowPdlStatusPublishDesc[canvasConvertState],
			CreateTime:                uint32(workflow.CreateTime.Unix()),
			UpdateTime:                uint32(workflow.UpdateTime.Unix()),
			IsAllowEdit:               false,
			IsAllowDelete:             false,
			IsAllowRelease:            false,
			IsWorkflowUpdate:          false,
			DialogJson:                workflow.DialogJsonDraft,
			PdlContent:                "",
			ApiInfo:                   "",
			UserAdditionalConstraints: "",
			Type:                      KEP_WF.AgentWorkflowType_CANVAS,
		}, nil
	}

	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
	decryptedPDLDialogJsonEnable, err := protoutil.DecryptWorkflowJson(workflowPDL.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowPDLDetail|DecryptWorkflowJson:%s|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("画布信息解密失败")
	}
	workflowPDL.DialogJsonEnable = decryptedPDLDialogJsonEnable

	// 返回PDL详情
	return &KEP_WF.GetAgentWorkflowDetailRsp{
		WorkflowId:      workflowID,
		WorkflowVersion: workflowPDL.Version,
		Name:            workflow.WorkflowName,
		Desc:            workflow.WorkflowDesc,
		//FlowState:         workflowPDL.WorkflowState,
		//FlowStateDesc:     workflowPDL.FrontReleaseStatus(),
		ReleaseStatus:             workflowPDL.FrontReleaseStatus(),
		ReleaseStatusDesc:         workflowPDL.FrontReleaseStatusDesc(),
		CreateTime:                uint32(workflowPDL.PdlCreateTime.Unix()), // 触发转换时画布的更新时间
		UpdateTime:                uint32(workflowPDL.UpdateTime.Unix()),
		IsAllowEdit:               workflowPDL.IsAllowEdit(),
		IsAllowDelete:             workflowPDL.IsAllowDelete(),
		IsAllowRelease:            workflowPDL.IsAllowRelease(),
		IsWorkflowUpdate:          workflowPDL.IsWorkflowUpdate(workflow),
		DialogJson:                workflowPDL.DialogJsonEnable,
		PdlContent:                workflowPDL.PdlContent,
		ApiInfo:                   workflowPDL.ToolsInfo,
		UserAdditionalConstraints: workflowPDL.UserConstraints,
		Type:                      frontDisplayType(workflowPDL),
		PdlId:                     workflowPDL.PdlID,
		PdlVersion:                workflowPDL.PdlSnapshotVersion,
	}, nil
}

// SwitchWorkflowState 开启某个pdl工作流
// 目前限制同时开启启用10个，检查白名单
func SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq) (*KEP_WF.SwitchAgentWorkflowStateRsp, error) {
	appBizId := req.GetAppBizId()
	workflowIds := req.GetWorkflowIds()
	isEnable := req.GetIsEnable()
	sid := util.RequestID(ctx)

	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	// 检查白名单
	usePDL := appInfo.GetKnowledgeQa().GetWorkflow().GetUsePdl()
	log.InfoContextf(ctx, "SwitchWorkflowState|usePDL:%s", usePDL)
	if !usePDL {
		return nil, errors.ErrPermissionDenied
	}
	// 校验 workflowIds 是否合法
	workflows, err := db.GetWorkflowPDLsByFlowIds(ctx, workflowIds, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState|GetWorkflowPDLsByFlowIds|err:%+v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	// 获取已经开启的 PDL
	enabledWorkflows, err := db.GetEnabledWorkflowPDLs(ctx, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState|GetEnabledWorkflowPDLs|err:%+v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	// NOTE:暂时检查是否开启超过10个以上的 PDL，后续由配置实现
	limitEnabledWorkflowPDL := 10
	totalEnalbed := len(enabledWorkflows) + len(workflows)
	if isEnable && (totalEnalbed > limitEnabledWorkflowPDL) {
		log.WarnContextf(ctx, "SwitchWorkflowState|totalEnalbed:%v,enabled:%v", totalEnalbed, len(enabledWorkflows))
		return nil, errors.ErrWorkflowLimit
	}

	enableFlowIds := make([]string, 0)
	for _, v := range workflows {
		enableFlowIds = append(enableFlowIds, v.WorkflowID)
	}
	log.InfoContextf(ctx, "SwitchWorkflowState|enableFlowIds:%+v", enableFlowIds)
	// 判断PDL的状态
	rsp, err := updateTestEnvEnable(ctx, appBizId, enableFlowIds, isEnable)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetWorkflowState 获取某个pdl的状态，检查白名单
func GetWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq) (*KEP_WF.GetAgentWorkflowStateRsp, error) {
	getDetailReq := &KEP_WF.GetAgentWorkflowDetailReq{
		WorkflowId: req.GetWorkflowId(),
		AppBizId:   req.GetAppBizId(),
	}

	getDetailRsp, err := GetWorkflowPDLDetail(ctx, getDetailReq)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowState|err:%+v", err)
		return nil, err
	}

	return &KEP_WF.GetAgentWorkflowStateRsp{
		//FlowState:         workflow.WorkflowState,
		//FlowStateDesc:     workflow.FrontReleaseStatus(),
		ReleaseStatus:     getDetailRsp.GetReleaseStatus(),
		ReleaseStatusDesc: getDetailRsp.GetReleaseStatusDesc(),
		IsAllowEdit:       getDetailRsp.GetIsAllowEdit(),
		IsAllowDelete:     getDetailRsp.GetIsAllowDelete(),
		IsAllowRelease:    getDetailRsp.GetIsAllowRelease(),
		IsWorkflowUpdate:  getDetailRsp.GetIsWorkflowUpdate(),
	}, nil
}

// ConvertToAgentWorkflow 工作流画布转换成PDL，检查白名单
func ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq) (*KEP_WF.ConvertToAgentWorkflowRsp, error) {
	log.InfoContextf(ctx, "ConvertToAgentWorkflow|start")
	rsp := new(KEP_WF.ConvertToAgentWorkflowRsp)
	sid := util.RequestID(ctx)
	workflowID := req.GetWorkflowId()
	appBizID := strings.TrimSpace(req.GetAppBizId())
	staffID := util.StaffID(ctx)
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertToAgentWorkflow|appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "ConvertToAgentWorkflow|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	// 查看白名单
	usePDL := appInfo.GetKnowledgeQa().GetWorkflow().GetUsePdl()
	log.InfoContextf(ctx, "ConvertToAgentWorkflow|usePDL:%s", usePDL)
	if !usePDL {
		return nil, errors.ErrPermissionDenied
	}
	// 获取工作流，如果不存在就返回错误
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息
	// 从工作流转换到PDL的时候画布中节点的某些字段已经是加密的状态，因此这里不做加解密处理
	workflow, err := db.GetWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.ErrWorkflowNotFound
		}
		return nil, err
	}
	log.InfoContextf(ctx, "ConvertToAgentWorkflow|WF: %+v", workflow)
	if workflow == nil || workflow.WorkflowID == "" {
		return nil, errors.ErrWorkflowNotFound
	}
	// 检查工作流是否可以转换
	canvasConvertState, _, err := checkWorkflowConvertibleState(ctx, workflow, usePDL)
	if canvasConvertState != entity.WorkflowPdlFrontStatusUnConverted {
		return nil, err
	}

	// 创建转换任务
	workflow.StaffID = staffID
	workflowPDL, err := db.ConvertWorkflowToPDL(ctx, appInfo.CorpId, workflow)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "ConvertToAgentWorkflow|convert new workflowPDL:%+v", workflowPDL)
	return rsp, nil
}

// updateTestEnvEnable 更新测试环境工作流状态
func updateTestEnvEnable(ctx context.Context, appBizId string,
	workflowIds []string, isEnable bool) (*KEP_WF.SwitchAgentWorkflowStateRsp, error) {
	rsp := &KEP_WF.SwitchAgentWorkflowStateRsp{}
	var ErrMsg string
	errSwitchId := make([]string, 0)
	workflowNameMap := make(map[string]string)

	if len(workflowIds) > 0 {
		workflows, err := db.GetWorkflowPDLsByFlowIds(ctx, workflowIds, appBizId)
		if err != nil {
			return rsp, err
		}
		for _, v := range workflows {
			workflowNameMap[v.WorkflowID] = v.WorkflowName
		}
	}
	var wg sync.WaitGroup
	errorCh := make(chan *string, len(workflowIds))
	for _, wid := range workflowIds {
		wg.Add(1) // 为每个goroutine增加计数
		go func(wid string) {
			defer wg.Done()
			doneUpdateTestEnvEnable(ctx, isEnable, wid, appBizId, errorCh)
		}(wid)
	}

	go func() {
		wg.Wait()
		close(errorCh) // 关闭通道
	}()

	for wid := range errorCh {
		errSwitchId = append(errSwitchId, *wid)
		ErrMsg += workflowNameMap[*wid] + ", "
	}

	successCnt := len(workflowIds) - len(errSwitchId)
	msg, code := switchHandlerErr(isEnable, ErrMsg, successCnt)
	rsp.Msg = msg
	rsp.Code = uint32(code)

	return rsp, nil
}

// doneUpdateTestEnvEnable 切换状态处理
func doneUpdateTestEnvEnable(ctx context.Context, isEnable bool, wid, appBizId string,
	errorCh chan *string) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "SwitchWorkflowState:%+v", r)
		}
	}()
	// 以工作流级别保证向量及表中的数据切换正常
	if err := db.SwitchWorkflowPDLState(ctx, appBizId, wid, isEnable); err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState:%+v", err)
		// 发送失败的wid到errorCh
		errorCh <- &wid
	}
}

// switchHandlerErr ...
func switchHandlerErr(isEnable bool, errMsg string, successCnt int) (string, int) {
	if len(errMsg) > 0 {
		errMsg = "工作流:" + errMsg + "状态切换失败;" + fmt.Sprintf("状态成功切换：%d个", successCnt)
		return errMsg, entity.SwitchFailed
	} else {
		if isEnable {
			errMsg = fmt.Sprintf("已启用%d个工作流，发布应用后在生产环境中生效", successCnt)
		} else {
			errMsg = fmt.Sprintf("已停用%d个工作流，发布应用后在生产环境中生效", successCnt)
		}
		return errMsg, entity.SwitchSuccess
	}
}

// getStaffInfoMapByStaffIds 获取员工Map
func getStaffInfoMapByStaffIds(ctx context.Context, staffIds []uint64) (map[uint64]string, error) {
	// 获取员工信息
	staffInfosMap := make(map[uint64]string)
	staffIdInfoRsp, err := rpc.ListCorpStaffByIds(ctx, staffIds)
	if err != nil {
		return staffInfosMap, err
	}
	if staffIdInfoRsp != nil && len(staffIdInfoRsp.GetList()) > 0 {
		for _, v := range staffIdInfoRsp.GetList() {
			staffInfosMap[v.Id] = v.NickName
		}
	}
	return staffInfosMap, nil
}

// checkWorkflowConvertibleState 解析工作流画布，并计算是否可转换
// 1、非白名单，返回不支持发布
// 2、检查画布是否可以转换PDL
func checkWorkflowConvertibleState(ctx context.Context, workflow *entity.Workflow, usePDL bool) (string, string, error) {
	if !usePDL {
		return entity.WorkflowPdlFrontStatusUnableRelease, "", nil
	}
	var canvasConvertState = entity.WorkflowPdlFrontStatusUnableConvert
	if workflow == nil {
		return canvasConvertState, "", errors.ErrWorkflowNotFound
	}
	// 草稿 --> 不支持转换
	if workflow.WorkflowState == entity.WorkflowPdlStateDraft || workflow.WorkflowState == entity.WorkflowPdlStatePublishedDraft {
		log.InfoContextf(ctx, "checkWorkflowConvertibleState|id:%s|state:%v", workflow.WorkflowID, workflow.WorkflowState)
		reasonCode := entity.PDLReasonWorkflowNotDebugged
		return canvasConvertState, reasonCode, errors.WorkflowUnableConvertError(entity.GetPDLReasonDesc(ctx, reasonCode))
	}
	// 序列化
	workflowDialog, err := protoutil.JsonToWorkflow(workflow.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "checkWorkflowConvertibleState|id:%s|err:%v", workflow.WorkflowID, err)
		reasonCode := entity.PDLReasonWorkflowIllegal
		return canvasConvertState, reasonCode, errors.WorkflowUnableConvertError(entity.GetPDLReasonDesc(ctx, reasonCode))
	}
	// 检查是否可以转换
	canvasConvertible, reasonCode := pdlconvert.NewPDL().CheckConvertible(ctx, workflowDialog)
	log.InfoContextf(ctx, "checkWorkflowConvertibleState|id:%s|canvasConvertible:%v|reasonCode:%s",
		workflow.WorkflowID, canvasConvertible, reasonCode)
	if canvasConvertible {
		canvasConvertState = entity.WorkflowPdlFrontStatusUnConverted
	} else {
		return canvasConvertState, reasonCode, errors.WorkflowUnableConvertError(entity.GetPDLReasonDesc(ctx, reasonCode))
	}

	return canvasConvertState, "", nil
}

// frontDisplayType 判断PDL的编辑形态（画布，PDL）
func frontDisplayType(w *entity.WorkflowPDL) KEP_WF.AgentWorkflowType {
	if w == nil {
		return KEP_WF.AgentWorkflowType_CANVAS
	}
	// 转换中；第一次转换转换失败；--> 展示画布快照
	if w.IsWorkflowPDLConverting() ||
		(w.IsWorkflowPDLConvertedFailed() && len(w.PdlContent) == 0) {
		return KEP_WF.AgentWorkflowType_CANVAS
	}
	// 如果转换失败，返回上一次转换成功的内容
	return KEP_WF.AgentWorkflowType_PDL
}

// GetPDLReleaseStatus 获取PDL的发布状态
func GetPDLReleaseStatus(ctx context.Context,
	req *KEP_WF.GetWorkflowReleaseStatusReq) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	rsp := new(KEP_WF.GetWorkflowReleaseStatusResp)
	workflowId := req.GetWorkflowId()
	appId := req.GetAppBizId()
	envType := req.GetEnvTag()
	var scene uint32 = 1
	if envType == 1 {
		scene = 2
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobotWithNoUserLogin(ctx, scene, botId)
	if errors.Is(err, errors.ErrRobotNotFound) {
		log.WarnContextf(ctx, "P|GetPDLReleaseStatus|%v", err)
		return rsp, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "P|GetPDLReleaseStatus|%v", err)
		return rsp, err
	}

	// 如果传 工作流Id，则获取工作流的发布状态
	if len(workflowId) > 0 {
		// 判断该应用下工作流是否存在
		workflow, err := getPDLExist(ctx, appId, workflowId, envType)
		if errors.Is(err, gorm.ErrRecordNotFound) || workflow == nil {
			rsp.IsExist = false
			return rsp, nil
		} else if err != nil {
			log.ErrorContextf(ctx, "GetPDLReleaseStatus|err:%+v", err)
			rsp.IsExist = false
			return rsp, err
		} else {
			rsp.IsExist = true
		}

		// 工作流存在，判断工作流是否处于调试模式
		isDebug := getPDLIsDebug(workflow, envType)
		// 判断工作流是否发布
		isRelease, err := publish.NewPublish().IsPDLPublishedQueryById(ctx, appId, workflowId, envType)
		if err != nil {
			return rsp, err
		}
		// 判断工作流是否开启
		isEnable := getPDLIsEnable(workflow, envType)
		rsp.IsDebug = isDebug
		rsp.IsRelease = isRelease
		rsp.IsEnable = isEnable
		return rsp, nil
	}

	// 如果不传工作流Id，则获取应用下工作流的发布状态,只要有已经发布过的则isRelease为true
	isRelease, err := publish.NewPublish().IsPDLPublished(ctx, appId, envType)
	rsp = &KEP_WF.GetWorkflowReleaseStatusResp{
		IsRelease: isRelease,
	}
	log.Infof("O|GetPDLReleaseStatus|%s|%s|ERR:%v", appId, envType)
	if err != nil {
		log.Errorf("E|GetPDLReleaseStatus|%s", err.Error())
		return rsp, err
	}
	return rsp, nil
}

// getPDLExist 获取工作流是否存在
func getPDLExist(ctx context.Context, appId, workflowId string, envType uint32) (*entity.WorkflowPDL, error) {
	env := KEP_WF.GetWorkflowReleaseStatusReq_EnvType(int32(envType))
	workflow := &entity.WorkflowPDL{}
	var err error
	//  测试环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_TEST {
		workflow, err = db.GetWorkflowPDLDetail(ctx, workflowId, appId)
		if err != nil {
			return workflow, err
		}
	}

	// 生产环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_PROD {
		workflow, err = db.GetProdPDLDetail(ctx, workflowId, appId)
		if err != nil {
			return workflow, err
		}
	}

	return workflow, nil
}

// getPDLIsDebug 判断工作流是否处于调试模式
func getPDLIsDebug(w *entity.WorkflowPDL, envType uint32) bool {
	env := KEP_WF.GetWorkflowReleaseStatusReq_EnvType(int32(envType))

	// 生产环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_PROD {
		// 生产环境的工作流没有调试模式
		return false
	}
	//  测试环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_TEST {
		// 测试环境 草稿态 及 已发布-草稿态，属于调试模式
		if w.WorkflowState == entity.WorkflowPdlStatePublishedDraft ||
			w.WorkflowState == entity.WorkflowPdlStateDraft {
			return true // 调试模式
		}
	}
	return false
}
func getPDLIsEnable(w *entity.WorkflowPDL, envType uint32) bool {
	env := KEP_WF.GetWorkflowReleaseStatusReq_EnvType(int32(envType))
	// 生产环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_PROD {
		// env为生产环境时，获取到的就是生产库里面的工作流信息, 待调试不会进行发布
		return w.IsEnable
	}
	//  测试环境
	if env == KEP_WF.GetWorkflowReleaseStatusReq_TEST {
		// 测试环境 草稿态 及 已发布-草稿态，属于调试模式，调试模式不可检索（启用状态无效）
		if w.WorkflowState == entity.WorkflowPdlStatePublishedDraft ||
			w.WorkflowState == entity.WorkflowPdlStateDraft {
			return false // 调试模式
		}

		// 待发布 / 已发布 的启用有效
		return w.IsEnable
	}
	return false
}

func ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq) (*KEP_WF.ListPDLVersionRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.ListPDLVersionRsp{}
	sid := util.RequestID(ctx)
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "ListPDLVersion|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	// 获取PDL版本列表
	pdlVersions, err := db.ListPDLVersion(ctx, appBizId, req.GetPdlId())
	if err != nil {
		log.ErrorContextf(ctx, "ListPDLVersion|%s|%v", sid, err)
		return nil, errors.ErrPDLVersionNotFound
	}
	list := make([]*KEP_WF.ListPDLVersionRsp_PdlVersionInfo, 0, len(pdlVersions))
	for _, pdlVersion := range pdlVersions {
		pdlVersionInfo := &KEP_WF.ListPDLVersionRsp_PdlVersionInfo{
			PdlVersion: pdlVersion.PdlSnapshotVersion,
			CreateTime: uint32(pdlVersion.CreateTime.Unix()),
			UpdateTime: uint32(pdlVersion.UpdateTime.Unix()),
		}
		list = append(list, pdlVersionInfo)

	}
	rsp.List = list
	return rsp, nil
}

func GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq) (*KEP_WF.GetPDLVersionDetailRsp, error) {
	appBizId := req.GetAppBizId()
	sid := util.RequestID(ctx)
	// 尝试将字符串转换为 uint64
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "GetPDLVersionDetail|CheckRobot:%s|%+v", sid, err)
		return nil, err
	}
	// 获取PDL版本列表
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息
	pdlVersion, err := db.GetPDLVersionDetail(ctx, req.GetPdlId(), req.GetPdlVersion())
	if err != nil {
		log.ErrorContextf(ctx, "GetPDLVersionDetail|%s|%v", sid, err)
		return nil, errors.ErrPDLVersionNotFound
	}
	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
	decryptedPDLDialogJsonEnable, err := protoutil.DecryptWorkflowJson(pdlVersion.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "GetPDLVersionDetail|DecryptWorkflowJson:%s|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("画布信息解密失败")
	}
	pdlVersion.DialogJsonEnable = decryptedPDLDialogJsonEnable

	rsp := &KEP_WF.GetPDLVersionDetailRsp{
		PdlId:                             pdlVersion.PdlID,
		PdlVersion:                        pdlVersion.PdlSnapshotVersion,
		DialogJson:                        pdlVersion.DialogJsonEnable,
		PdlContent:                        pdlVersion.PdlContent,
		ApiInfo:                           pdlVersion.ToolsInfo,
		UserAdditionalConstraints:         pdlVersion.UserConstraints,
		PdlContentModified:                pdlVersion.PdlContentModified,
		ApiInfoModified:                   pdlVersion.ToolsInfoModified,
		UserAdditionalConstraintsModified: pdlVersion.UserConstraintsModified,
	}

	return rsp, nil
}
