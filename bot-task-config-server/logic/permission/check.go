package permission

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// CheckRobotWithNoUserLogin 仅校验机器人，不校验登录
func CheckRobotWithNoUserLogin(ctx context.Context, scenes uint32,
	businessID uint64) (*pb.GetAppInfoRsp, error) {
	robot, err := rpc.GetAppInfo(ctx, scenes, businessID)
	if err != nil {
		log.WarnContext(ctx, "CheckRobotWithNoUserLogin|GetRobotInfo"+
			"|businessID:%d|err=%+v|", businessID, err)
		return nil, errors.ErrRobotNotFound
	}
	if robot == nil {
		log.WarnContext(ctx, "CheckRobotWithNoUserLogin|GetRobotInfo"+
			"|businessID:%d|robot is nil", businessID)
		return nil, errors.ErrRobotNotFound
	}

	corpID := util.CorpID(ctx)
	if corpID != 0 && corpID != robot.CorpId {
		log.ErrorContextf(ctx, "当前企业与应用归属企业不一致 businessID:%d corpID:%d robot:%+v",
			businessID, corpID, robot)
		return nil, errors.ErrRobotNotFound
	}
	return robot, nil
}

// CheckRobot 检查机器人有效性
func CheckRobot(ctx context.Context, scene uint32, businessID uint64) (*pb.GetAppInfoRsp, error) {

	// // 私有化环境增加license是否过期判断
	if config.GetMainConfig().PrivacyEnv {
		licenseReq := &pb.GetDescribeLicenseReq{AppType: "knowledge_qa"}
		licenseInfo, err := rpc.GetDescribeLicense(ctx, licenseReq)
		if err != nil {
			log.WarnContext(ctx, "CheckRobot|DescribeLicense failed,req:%+v,err:%+v", licenseReq, err)
			return nil, errors.ErrSystem
		}

		if licenseInfo.State == "invalid" || licenseInfo.State == "expired" {
			log.InfoContextf(ctx, "licenseInvalied or expired")
			return nil, errors.ErrLicenseInvalid
		}
	}

	robot, err := rpc.GetAppInfo(ctx, scene, businessID)
	if err != nil {
		log.WarnContext(ctx, "CheckRobot|GetRobotInfo|businessID:%d|err=%+v|", businessID, err)
		return nil, errors.ErrRobotNotFound
	}
	if robot == nil {
		log.WarnContext(ctx, "CheckRobot|GetRobotInfo|businessID:%d|robot is nil", businessID)
		return nil, errors.ErrRobotNotFound
	}
	//如果是体验中心，就直接返回放行
	if robot.IsExpApp {
		return robot, nil
	}
	log.InfoContextf(ctx, "CheckRobot is not exp app")
	corpID := util.CorpID(ctx)
	if corpID != 0 && corpID != robot.CorpId {
		log.WarnContextf(ctx, "当前企业与应用归属企业不一致 businessID:%d corpID:%d robot:%+v",
			businessID, corpID, robot)
		return nil, errors.ErrRobotNotFound
	}
	uin, subAccountUin := getLoginUinAndSubAccountUin(ctx)
	si, err := permission.GetSystemIntegratorByID(ctx, uin, subAccountUin, util.SID(ctx))
	if err != nil {
		log.WarnContext(ctx, "CheckRobot|GetSystemIntegratorByID|businessID:%d|err:%+v", businessID, err)
		return nil, errors.ErrRobotNotFound
	}
	if si.Status == entity.SystemIntegratorValid && si.IsSelfPermission {
		log.InfoContextf(ctx, "CheckRobot|GetSystemIntegratorByID|businessID:%d|si:%+v", businessID, si)
		return robot, nil
	}
	log.InfoContextf(ctx, "CheckRobot|GetSystemIntegratorByID|businessID:%d|si:%+v", businessID, si)

	verify, err := permission.VerifyResource(ctx, uin, subAccountUin, businessID)
	if err != nil {
		log.WarnContextf(ctx, "CheckRobot|VerifyResource|businessID:%d|err:%+v", businessID, err)
		return nil, errors.ErrRobotNotFound
	}
	if !verify {
		log.WarnContextf(ctx, "CheckRobot|VerifyResource|businessID:%d|not allow", businessID)
		return nil, errors.ErrResourcePermissionDenied
	}
	log.InfoContextf(ctx, "CheckRobot|VerifyResource|businessID:%d|verify:%t", businessID, verify)

	return robot, nil
}

// CheckPermission 权限校验
func CheckPermission(ctx context.Context, action string) (bool, error) {
	uin, subAccountUin := getLoginUinAndSubAccountUin(ctx)
	si, err := permission.GetSystemIntegratorByID(ctx, uin, subAccountUin, util.SID(ctx))
	if err != nil || si == nil {
		log.WarnContext(ctx, "CheckPermission|getSystemIntegratorByID|err=%+v|si:%+v", err, si)
		return false, nil
	}
	log.DebugContextf(ctx, "CheckPermission|si:%+v", si)
	if si.Status == entity.SystemIntegratorValid && si.IsSelfPermission {
		return true, nil
	}

	allow, err := permission.VerifyPermission(ctx, uin, subAccountUin, action)
	if err != nil {
		log.ErrorContextf(ctx, "CheckPermission|err:%+v", err)
		return false, err
	}
	return allow, nil
}

func getLoginUinAndSubAccountUin(ctx context.Context) (string, string) {
	uin := util.Uin(ctx)
	subAccountUin := util.SubAccountUin(ctx)
	return uin, subAccountUin
}
