// Package publish ...
// @Author: halelv
// @Date: 2023/12/21 16:53
package publish

import (
	"context"

	publishDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/publish"
	vectorDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// Publish 发布相关接口
type Publish interface {
	// ICSTaskFlowPublish 客服任务流发布
	ICSTaskFlowPublish(ctx context.Context, robotID string, taskID uint64, taskFlowIDs []string) error

	// ICSTaskFlowReleaseStatus 查询任务流发布状态
	ICSTaskFlowReleaseStatus(ctx context.Context, robotID string,
		envType KEP.GetTaskFlowReleaseStatusReq_EnvType) (isPublish bool, err error)
}

// publish ...
type publish struct {
	dbDao     publishDao.Dao
	vectorDao vectorDao.Dao
}

// NewPublish new publish logic
func NewPublish() Publish {
	return &publish{
		dbDao:     publishDao.NewDao(),
		vectorDao: vectorDao.NewDao(),
	}
}
