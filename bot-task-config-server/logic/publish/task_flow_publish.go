// Package publish ...
// @Author: halelv
// @Date: 2023/12/21 17:08
package publish

import (
	"context"
	"strconv"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	daoDB "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"gorm.io/gorm"
)

// ICSTaskFlowPublish ...
//
//	@Description: 客服任务流发布
//	@receiver p
//	@param ctx
//	@param robotID     发布BotID
//	@param taskID      发布任务ID
//	@param taskFlowIDs 待发布任务流ID列表
//	@return error
func (p publish) ICSTaskFlowPublish(ctx context.Context, robotID string, taskID uint64, taskFlowIDs []string) error {
	sid := util.RequestID(ctx)
	ctx = log.WithContextFields(ctx, "RequestID", sid, "TaskID", strconv.FormatUint(taskID, 10))
	log.InfoContextf(ctx, "ICSTaskFlowPublish, robotID:%s, taskID:%d, taskFlowIDs:%v",
		robotID, taskID, taskFlowIDs)
	if len(robotID) == 0 || taskID <= 0 || len(taskFlowIDs) == 0 {
		return nil
	}
	// 查询待发布任务流
	taskFlows, err, flowIDs, intentIDs := p.getTaskFlowIDList(ctx, robotID, taskFlowIDs)
	if err != nil {
		return err
	}
	errChan := make(chan error, 3)
	wg := sync.WaitGroup{}
	wg.Add(3)
	// 查询待发布 槽位｜实体｜词条｜槽位-实体｜意图-槽位
	intentEntries := make([]*entity.IntentEntry, 0)
	intentSlots := make([]*entity.IntentSlot, 0)
	slots := make([]*entity.Slot, 0)
	slotEntities := make([]*entity.SlotEntity, 0)
	entities := make([]*entity.Entity, 0)
	entries := make([]*entity.Entry, 0)
	entityEntryMap := make(map[string]string, 0)
	go func() {
		if intentEntries, intentSlots, slots, slotEntities, entities, entries, entityEntryMap, err =
			p.getUnPublishSlotRelations(ctx, robotID, intentIDs); err != nil {
			errChan <- err
		}
		wg.Done()
	}()
	// 查询待发布 意图｜语料｜意图-任务流｜意图-机器人
	intents := make([]*entity.Intent, 0)
	corpora := make([]*entity.Corpus, 0)
	intentFlows := make([]*entity.IntentFlow, 0)
	robotIntents := make([]*entity.RobotIntent, 0)
	intentCorpusExamples := make([]*entity.IntentCorpus, 0)
	go func() {
		if intents, corpora, intentFlows, robotIntents, intentCorpusExamples, err =
			p.getUnPublishIntentRelations(ctx, robotID, flowIDs, intentIDs); err != nil {
			errChan <- err
		}
		wg.Done()
	}()
	// 查询待发布 自定义变量 ｜ 意图 - 自定义变量
	varParams := make([]*entity.VarParams, 0)
	varParamsIntents := make([]*entity.IntentVarParams, 0)
	go func() {
		if varParams, varParamsIntents, err =
			p.getUnPublishVarParamsRelations(ctx, intentIDs); err != nil {
			errChan <- err
		}
		wg.Done()
	}()
	wg.Wait()
	select {
	case err = <-errChan:
		log.ErrorContextf(ctx, "sid:%s|ICSTaskFlowPublish publishICSTaskFlow err:%v", sid, err)
		return errors.ErrSyncConfigDataLoadFailed
	default:
		// 执行发布
		err = p.publishICSTaskFlow(ctx, robotID, varParams, varParamsIntents, slots,
			entities, entries, slotEntities, entityEntryMap, intents, corpora,
			intentEntries, intentSlots, robotIntents, taskFlows, intentFlows, intentCorpusExamples)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|ICSTaskFlowPublish publishICSTaskFlow err:%v", sid, err)
			return err
		}
		log.InfoContextf(ctx, "sid:%s|ICSTaskFlowPublish success,len(varParams):%d|len(varParamsIntents):%d|"+
			"len(slots):%d|len(entities):%d|len(entries):%d|len(slotEntities):%d|len(intents):%d|len(corpora):%d,"+
			"len(intentEntries):%d|len(intentSlots):%d|len(robotIntents):%d|len(taskFlows):%d|len(intentFlows):%d,"+
			"len(intentCorpusExamples):%d,len(len(entityEntryMap):%d", sid, len(varParams), len(varParamsIntents),
			len(slots), len(entities), len(entries), len(slotEntities), len(intents), len(corpora), len(intentEntries),
			len(intentSlots), len(robotIntents), len(taskFlows), len(intentFlows),
			len(intentCorpusExamples), len(entityEntryMap))
		return nil
	}
}

// getTaskFlowIDList 获取未发布的任务流程和意图ID
func (p publish) getTaskFlowIDList(ctx context.Context, robotID string, taskFlowIDs []string) ([]*entity.TaskFlow,
	error, []string, []string) {
	// 查询待发布任务流
	taskFlows, err := p.dbDao.GetUnPublishTaskFlow(ctx, robotID, taskFlowIDs, entity.IntentSourceIcs)
	flowIDs := make([]string, 0)
	intentIDs := make([]string, 0)
	if len(taskFlows) == 0 || err != nil {
		return taskFlows, errors.ErrSyncConfigDataLoadFailed, flowIDs, intentIDs
	}
	for _, flow := range taskFlows {
		flowIDs = append(flowIDs, flow.FlowID)
		intentIDs = append(intentIDs, flow.IntentID)
	}
	return taskFlows, nil, flowIDs, intentIDs
}

// getUnPublishVarParamsRelations 获取未发布的关联自定义变量
func (p publish) getUnPublishVarParamsRelations(ctx context.Context, intentIDs []string) (
	[]*entity.VarParams, []*entity.IntentVarParams, error) {
	sid := util.RequestID(ctx)
	intentVarParams, err := p.dbDao.GetUnPublishIntentVarParams(ctx, intentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|getUnPublishVarParamsRelations|GetUnPublishIntentVarParams|err:%+v", sid, err)
		return nil, nil, err
	}
	varParamIds := make([]string, 0)
	for _, item := range intentVarParams {
		varParamIds = append(varParamIds, item.VarID)
	}
	varParams, err := p.dbDao.GetUnPublishVarParams(ctx, varParamIds)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|getUnPublishVarParamsRelations|GetUnPublishVarParams|err:%+v", sid, err)
		return nil, nil, err
	}

	return varParams, intentVarParams, nil

}

// getUnPublishSlotRelations 获取待发布槽位的相关信息
func (p publish) getUnPublishSlotRelations(ctx context.Context, robotID string, intentIDs []string) (
	[]*entity.IntentEntry, []*entity.IntentSlot, []*entity.Slot, []*entity.SlotEntity,
	[]*entity.Entity, []*entity.Entry, map[string]string, error) {

	// 意图-词条
	intententrys, err := p.dbDao.GetUnPublishIntentEntries(ctx, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	// 意图-槽位
	intentSlots, err := p.dbDao.GetUnPublishIntentSlot(ctx, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	slotIDs := make([]string, 0)
	for _, intentSlot := range intentSlots {
		slotIDs = append(slotIDs, intentSlot.SlotID)
	}

	// 槽位
	slots, err := p.dbDao.GetUnPublishSlot(ctx, slotIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	// 槽位-实体
	slotEntities, err := p.dbDao.GetUnPublishSlotEntity(ctx, slotIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	entityIDs := make([]string, 0)
	for _, slotEntity := range slotEntities {
		entityIDs = append(entityIDs, slotEntity.EntityID)
	}

	// 实体
	entities, err := p.dbDao.GetUnPublishEntity(ctx, entityIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	// 词条
	entries, err := p.dbDao.GetUnPublishEntry(ctx, entityIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	key := daoDB.GetEntityEntriesKey(ctx, daoDB.SandboxEnv, robotID)
	entityEntryMap, err := database.GetRedis().HGetAll(ctx, key).Result()
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	return intententrys, intentSlots, slots, slotEntities, entities, entries, entityEntryMap, nil
}

// getUnPublishIntentRelations 获取待发布意图的相关信息
func (p publish) getUnPublishIntentRelations(ctx context.Context, robotID string, flowIDs, intentIDs []string) (
	[]*entity.Intent, []*entity.Corpus, []*entity.IntentFlow, []*entity.RobotIntent, []*entity.IntentCorpus, error) {
	// 意图
	intents, err := p.dbDao.GetUnPublishIntent(ctx, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 语料
	corpora, err := p.dbDao.GetUnPublishCorpus(ctx, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 意图-任务流
	intentFlows, err := p.dbDao.GetUnPublishIntentFlow(ctx, flowIDs, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 意图-机器人
	robotIntents, err := p.dbDao.GetUnPublishRobotIntent(ctx, robotID, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 意图-机器人
	intentExamples, err := p.dbDao.GetUnPublishIntentExamples(ctx, robotID, intentIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	log.InfoContextf(ctx, "sid:%s|getUnPublishIntentRelations|len(intents):%d|len(corpora):%d|len(intentFlows):%d|"+
		"len(robotIntents):%d|len(intentExamples):%d", util.RequestID(ctx), len(intents),
		len(corpora), len(intentFlows), len(robotIntents), len(intentExamples))
	return intents, corpora, intentFlows, robotIntents, intentExamples, nil
}

// publishICSTaskFlow ...
//
//		@Description: 发布客服任务流
//		@receiver p
//		@param ctx
//		@param robotID       待发布机器人ID
//		@param slots         待发布槽位
//		@param entities      待发布实体
//		@param entries       待发布词条
//		@param slotEntities  待发布槽位-实体关联
//		@param intents       待发布意图
//		@param corpora       待发布语料
//		@param intentSlots   待发布意图-槽位关联
//		@param robotIntents  待发布机器人-意图关联
//		@param taskFlows     待发布任务流
//		@param intentFlows   待发布意图-任务流关联
//	 @param intentExample 待发布示例问法 - 意图关联
//		@return err
func (p publish) publishICSTaskFlow(ctx context.Context, robotID string, varParams []*entity.VarParams,
	varParamsIntents []*entity.IntentVarParams, slots []*entity.Slot, entities []*entity.Entity,
	entries []*entity.Entry, slotEntities []*entity.SlotEntity, entityEntryMap map[string]string,
	intents []*entity.Intent, corpora []*entity.Corpus, intentEntries []*entity.IntentEntry,
	intentSlots []*entity.IntentSlot, robotIntents []*entity.RobotIntent, taskFlows []*entity.TaskFlow,
	intentFlows []*entity.IntentFlow, intentExamples []*entity.IntentCorpus) (err error) {
	// 向量GroupID
	var corpusProdGroupID, entryProdGroupID string

	// 发布前处理
	preErr := p.publishICSPreProcess(ctx, varParams, varParamsIntents, slots, entities, entries,
		slotEntities, intents, corpora, intentEntries, intentSlots, robotIntents, taskFlows,
		intentFlows, intentExamples)
	// 发布后处理
	defer func() {
		postErr := p.publishICSPostProcess(ctx, robotID, varParams, varParamsIntents, slots,
			entities, entries, slotEntities, intents, corpora, intentEntries, intentSlots, robotIntents,
			taskFlows, intentFlows, intentExamples, corpusProdGroupID, entryProdGroupID, err)
		if postErr != nil {
			err = postErr
		}
	}()
	if preErr != nil {
		err = preErr
		return err
	}

	// 开启正式环境事物
	tx := db.BeginDBTx(ctx, database.GetLLMRobotTaskProdGORM())
	defer func() {
		// 事物提交或者回滚
		txErr := db.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	// =============db=============
	err = p.PublishTaskFlowDB(ctx, robotID, varParams, varParamsIntents, slots, entities, entries, slotEntities,
		entityEntryMap, intents, corpora, intentEntries, intentSlots, robotIntents, taskFlows, intentFlows,
		intentExamples, tx)
	if err != nil {
		return err
	}

	// =============向量=============
	// 发布向量需要放在最后进行发布，如果向量发布失败，DB的操作会通过事物回滚
	// TODO(halelv): 向量的发布目前不支持回滚，出现异常的情况可能会出现脏数据，后续增加对账来处理这种情况
	if corpusProdGroupID, err = p.vectorDao.PublishCorpusVector(ctx, robotID, corpora); err != nil {
		return err
	}
	if corpusProdGroupID, err = p.vectorDao.PublishIntentExampleVector(ctx, robotID, intentExamples); err != nil {
		return err
	}
	if entryProdGroupID, err = p.vectorDao.PublishEntryVector(ctx, robotID, entries); err != nil {
		return err
	}
	return nil
}

// PublishTaskFlowDB 发布任务流程相关db
func (p publish) PublishTaskFlowDB(ctx context.Context, robotID string, varParams []*entity.VarParams,
	varParamsIntents []*entity.IntentVarParams, slots []*entity.Slot, entities []*entity.Entity,
	entries []*entity.Entry, slotEntities []*entity.SlotEntity, entityEntryMap map[string]string,
	intents []*entity.Intent, corpora []*entity.Corpus, intentEntries []*entity.IntentEntry,
	intentSlots []*entity.IntentSlot, robotIntents []*entity.RobotIntent, taskFlows []*entity.TaskFlow,
	intentFlows []*entity.IntentFlow, intentExamples []*entity.IntentCorpus, tx *gorm.DB) (err error) {
	// ============= 自定义变量 ｜ 意图-变量关联关系 ==============
	if err = p.dbDao.PublishVar(ctx, tx, varParams); err != nil {
		log.ErrorContextf(ctx, "PublishVar|err:%+v", err)
		return err
	}
	if err := p.dbDao.PublishIntentVar(ctx, tx, varParamsIntents); err != nil {
		log.ErrorContextf(ctx, "PublishIntentVar|err:%+v", err)
		return err
	}

	// =============槽位｜实体｜词条｜=============
	// 发布槽位
	if err = p.dbDao.PublishSlot(ctx, tx, slots); err != nil {
		return err
	}
	// 发布实体
	if err = p.dbDao.PublishEntity(ctx, tx, entities); err != nil {
		return err
	}
	// 发布词条
	if err = p.dbDao.PublishEntry(ctx, tx, entries); err != nil {
		return err
	}
	// 词条同步到redis
	if err = p.dbDao.PublishEntityEntryToRedis(ctx, robotID, entityEntryMap); err != nil {
		return err
	}

	// 发布槽位-实体关联
	if err = p.dbDao.PublishSlotEntity(ctx, tx, slotEntities); err != nil {
		return err
	}

	// =============意图｜语料=============
	// 发布意图
	if err = p.dbDao.PublishIntent(ctx, tx, intents); err != nil {
		return err
	}
	if err = p.dbDao.PublishCorpus(ctx, tx, robotID, corpora); err != nil {
		return err
	}
	// 发布意图-词条关联
	if err = p.dbDao.PublishIntentEntry(ctx, tx, intentEntries); err != nil {
		return err
	}
	// 发布意图-槽位关联
	if err = p.dbDao.PublishIntentSlot(ctx, tx, intentSlots); err != nil {
		return err
	}
	// 发布意图-机器人关联
	if err = p.dbDao.PublishRobotIntent(ctx, tx, robotIntents); err != nil {
		return err
	}

	// =============任务流=============
	// 发布任务流
	if err = p.dbDao.PublishTaskFlow(ctx, tx, taskFlows); err != nil {
		return err
	}
	// 发布意图-任务关联
	if err = p.dbDao.PublishIntentFlow(ctx, tx, intentFlows); err != nil {
		return err
	}

	// 发布示例问法 -意图关联
	if err = p.dbDao.PublishIntentExample(ctx, tx, intentExamples); err != nil {
		return err
	}
	return nil
}

// publishICSPreProcess ...
//
//		@Description: 任务流发布前处理
//		@receiver p
//		@param ctx
//		@param slots        待发布槽位
//		@param entities     待发布实体
//		@param entries      待发布词条
//		@param slotEntities 待发布槽位-实体关联
//		@param intents      待发布意图
//		@param corpora      待发布语料
//		@param intentSlots  待发布意图-槽位关联
//		@param robotIntents 待发布机器人-意图关联
//		@param taskFlows    待发布任务流
//		@param intentFlows  待发布意图-任务流关联
//	    @param intentExamples 待发布示例问法-意图关联
//
//		@return error
func (p publish) publishICSPreProcess(ctx context.Context, varParams []*entity.VarParams,
	varParamsIntents []*entity.IntentVarParams, slots []*entity.Slot, entities []*entity.Entity,
	entries []*entity.Entry, slotEntities []*entity.SlotEntity, intents []*entity.Intent,
	corpora []*entity.Corpus, intentEntries []*entity.IntentEntry, intentSlots []*entity.IntentSlot,
	robotIntents []*entity.RobotIntent, taskFlows []*entity.TaskFlow,
	intentFlows []*entity.IntentFlow, intentExamples []*entity.IntentCorpus) error {
	// 发布之前：发布状态设置为发布中
	releaseStatus := entity.ReleaseStatusPublishing

	// 状态更新
	if err := p.updateDataReleaseStatus(ctx, varParams, varParamsIntents, slots, entities,
		entries, slotEntities, intents, corpora, intentEntries, intentSlots, robotIntents,
		taskFlows, intentFlows, intentExamples, releaseStatus); err != nil {
		log.ErrorContextf(ctx, "sid:%s|p.updateDataReleaseStatus: err:%+v",
			util.RequestID(ctx), err)
		return err
	}
	return nil
}

// publishICSPostProcess ...
//
//	@Description: 任务流发布后处理
//	@receiver p
//	@param ctx
//	@param robotID           待更新机器人ID
//	@param slots             待更新槽位
//	@param entities          待更新实体
//	@param entries           待更新词条
//	@param slotEntities      待更新槽位-实体关联
//	@param intents           待更新意图
//	@param corpora           待更新语料
//	@param intentSlots       待更新意图-槽位关联
//	@param robotIntents      待更新机器人-意图关联
//	@param taskFlows         待发布任务流
//	@param intentFlows       待发布意图-任务流关联
//	@param corpusProdGroupID 语料Prod向量库GroupID
//	@param entryProdGroupID  词条Prod向量库GroupID
//	@param err
//	@return error
func (p publish) publishICSPostProcess(ctx context.Context, robotID string,
	varParams []*entity.VarParams, varParamsIntents []*entity.IntentVarParams, slots []*entity.Slot,
	entities []*entity.Entity, entries []*entity.Entry, slotEntities []*entity.SlotEntity,
	intents []*entity.Intent, corpora []*entity.Corpus, intentEntries []*entity.IntentEntry,
	intentSlots []*entity.IntentSlot, robotIntents []*entity.RobotIntent, taskFlows []*entity.TaskFlow,
	intentFlows []*entity.IntentFlow, intentExamples []*entity.IntentCorpus,
	corpusProdGroupID, entryProdGroupID string, err error) error {
	var releaseStatus string
	sid := util.RequestID(ctx)
	if err == nil {
		// 发布成功：发布状态设置为已发布
		releaseStatus = entity.ReleaseStatusPublished
	} else {
		// 发布异常：发布状态设置为发布失败
		releaseStatus = entity.ReleaseStatusFail
	}

	// 状态更新
	if updateErr := p.updateDataReleaseStatus(ctx, varParams, varParamsIntents, slots, entities,
		entries, slotEntities, intents, corpora, intentEntries, intentSlots, robotIntents,
		taskFlows, intentFlows, intentExamples, releaseStatus); updateErr != nil || err != nil {
		log.ErrorContextf(ctx, "sid:%s|p.updateDataReleaseStatus:updateErr:%+v, err:%+v",
			sid, updateErr, err)
		return errors.ErrSyncConfigDataReleaseFailed
	}

	if releaseStatus == entity.ReleaseStatusPublished {
		// 通知DM
		if noticeErr := p.dbDao.PublishNoticeDM(ctx, robotID, slots, intents,
			corpusProdGroupID, entryProdGroupID); noticeErr != nil {
			log.ErrorContextf(ctx, "sid:%s|p.dbDao.PublishNoticeDM: noticeErr:%s",
				util.RequestID(ctx), noticeErr.Error())
			return errors.ErrSyncRunDataReleaseFailed
		}
	}
	return nil
}

// updateDataReleaseStatus ...
//
//	@Description: 更新发布数据状态
//	@receiver p
//	@param ctx
//	@param slots         待更新槽位
//	@param entities      待更新实体
//	@param entries       待更新词条
//	@param slotEntities  待更新槽位-实体关联
//	@param intents       待更新意图
//	@param corpora       待更新语料
//	@param intentSlots   待更新意图-槽位关联
//	@param robotIntents  待更新机器人-意图关联
//	@param taskFlows     待更新任务流
//	@param intentFlows   待更新意图-任务流关联
//	@param releaseStatus 待更新发布状态
//	@return error
func (p publish) updateDataReleaseStatus(ctx context.Context, varParams []*entity.VarParams,
	varParamsIntents []*entity.IntentVarParams, slots []*entity.Slot, entities []*entity.Entity,
	entries []*entity.Entry, slotEntities []*entity.SlotEntity, intents []*entity.Intent, corpora []*entity.Corpus,
	intentEntries []*entity.IntentEntry, intentSlots []*entity.IntentSlot, robotIntents []*entity.RobotIntent,
	taskFlows []*entity.TaskFlow, intentFlows []*entity.IntentFlow, intentExamples []*entity.IntentCorpus,
	releaseStatus string) error {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "sid:%s|updateDataReleaseStatus|len(varParams):%d|len(varParamsIntents):%d|"+
		"len(slots):%d|len(entities):%d|len(entries):%d|len(slotEntities):%d|len(intents):%d|len(corpora):%d|"+
		"len(intentEntries):%d,len(intentSlots):%d, len(robotIntents):%d, len(taskFlows):%d, len(intentFlows):%d, "+
		"len(intentExamples):%d,releaseStatus:%s", sid, len(varParams), len(varParamsIntents), len(slots), len(entities),
		len(entries), len(slotEntities), len(intents), len(corpora), len(intentEntries), len(intentSlots),
		len(robotIntents), len(taskFlows), len(intentFlows), len(intentExamples), releaseStatus)

	if err := p.dbDao.UpdateVarReleaseStatus(ctx, varParams, releaseStatus); err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateVarReleaseStatus|err:%+v", sid, err)
		return err
	}
	if err := p.dbDao.UpdateIntentVarReleaseStatus(ctx, varParamsIntents, releaseStatus); err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateIntentVarReleaseStatus|err:%+v", sid, err)
		return err
	}
	if err := p.dbDao.UpdateSlotReleaseStatus(ctx, slots, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateEntityReleaseStatus(ctx, entities, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateEntryReleaseStatus(ctx, entries, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateSlotEntityReleaseStatus(ctx, slotEntities, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateIntentReleaseStatus(ctx, intents, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateCorpusReleaseStatus(ctx, corpora, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateIntentEntriesReleaseStatus(ctx, intentEntries, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateIntentSlotReleaseStatus(ctx, intentSlots, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateRobotIntentReleaseStatus(ctx, robotIntents, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateTaskFlowReleaseStatus(ctx, taskFlows, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateIntentFlowReleaseStatus(ctx, intentFlows, releaseStatus); err != nil {
		return err
	}
	if err := p.dbDao.UpdateIntentExamplesReleaseStatus(ctx, intentExamples, releaseStatus); err != nil {
		return err
	}
	return nil
}

// ICSTaskFlowReleaseStatus ...
//
//	@Description: 获取任务流程的发布状态
//	@receiver p
//	@param ctx
//	@param robotID     发布BotID
//	@param envType 环境类型（测试环境和线上环境）
//	@return error
func (p publish) ICSTaskFlowReleaseStatus(ctx context.Context, robotID string,
	envType KEP.GetTaskFlowReleaseStatusReq_EnvType) (isRelease bool, err error) {
	ctx = log.WithContextFields(ctx,
		"RequestID", util.RequestID(ctx), "robotID", robotID, "envType", string(envType))
	log.InfoContextf(ctx, "ICSTaskFlowReleaseStatus, robotID:%s,envType:%v",
		robotID, envType)
	if len(robotID) == 0 || len(string(envType)) == 0 {
		return false, nil
	}
	isRelease, err = p.dbDao.GetTaskFlowReleaseStatus(ctx, robotID, "ICS", envType)
	return isRelease, err
}
