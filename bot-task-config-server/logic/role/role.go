// bot-task-config-server
//
// @(#)role.go  星期一, 四月 08, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

// Package role 角色描述
package role

import (
	"context"
	"strconv"

	"git.woa.com/raven/three-eyed-raven/encode"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// GetRoleDefaultTemplateList 角色预设模版获取
func GetRoleDefaultTemplateList(ctx context.Context, req *KEP.RoleDefaultTemplateListReq) (
	rsp *KEP.RoleDefaultTemplateListRsp, err error) {

	log.InfoContextf(ctx, "GetRoleDefaultTemplateList start req:%v", utils.ToJsonString(req))
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	rsp = new(KEP.RoleDefaultTemplateListRsp)
	roleList := make([]*KEP.RoleDesc, 0)
	var roleDefaultTemplates = config.GetMainConfig().RoleDefaultTemplates
	for i := range roleDefaultTemplates {
		roleDesc := new(KEP.RoleDesc)
		roleDesc.Name = roleDefaultTemplates[i].Name
		roleDesc.Text = roleDefaultTemplates[i].Text
		roleList = append(roleList, roleDesc)
	}
	rsp.List = roleList
	log.InfoContextf(ctx, "GetRoleDefaultTemplateList rsp:%v", utils.ToJsonString(rsp))
	return rsp, nil

}

// GetPromptWordTemplateList 获取提示词模版
func GetPromptWordTemplateList(ctx context.Context, req *KEP.GetPromptWordTemplateListReq) (
	rsp *KEP.GetPromptWordTemplateListRsp, err error) {
	_, err = strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "GetPromptWordTemplateList appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(req.GetAppBizId())))
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	rsp = new(KEP.GetPromptWordTemplateListRsp)
	promptWordList := make([]*KEP.PromptWordTemplate, 0)
	var promptWordTemplates = config.GetMainConfig().PromptWordTemplates
	for i := range promptWordTemplates {
		promptWordTemplate := new(KEP.PromptWordTemplate)
		promptWordTemplate.Name = promptWordTemplates[i].Name
		promptWordTemplate.Text = promptWordTemplates[i].Text
		promptWordList = append(promptWordList, promptWordTemplate)
	}
	rsp.List = promptWordList
	log.InfoContextf(ctx, "GetPromptWordTemplateList rsp:%v", utils.ToJsonString(rsp))
	return rsp, nil
}
