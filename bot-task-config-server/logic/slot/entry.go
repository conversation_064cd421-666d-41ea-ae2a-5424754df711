// bot-task-config-server
//
// @(#)entry.go  星期四, 二月 29, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package slot

import (
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"regexp"
	"strings"
	"unicode/utf8"
)

func getTrimEntryValueAlias(entryValue string, entryAlias []string) (string, []string) {

	entryName := strings.TrimSpace(entryValue)
	entryAliasArr := []string{}
	for _, alias := range entryAlias {
		if len(strings.TrimSpace(alias)) == 0 {
			continue
		}
		entryAliasArr = append(entryAliasArr, strings.TrimSpace(alias))
	}
	return entryName, entryAliasArr
}

// CreateEntry 创建词条
func CreateEntry(ctx context.Context, req *KEP.CreateEntryReq) (*KEP.CreateEntryRsp, error) {
	sid := util.RequestID(ctx)

	robotId := req.GetBotBizId()
	slotID := req.GetSlotId()
	entryName, entryAlias := getTrimEntryValueAlias(req.GetValue(), req.GetAlias())

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntry,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntry.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	// 获取当前entityID下的所有词条
	existEntries, err := db.GetEntryList(ctx, slotEntityInfo.EntityID, "")
	if err != nil {
		log.ErrorContextf(ctx, "GetEntriesByEntityID Failed,err:%+v", err)
		return nil, errors.ErrSystem
	}

	// 命名以及数量限制校验
	err = checkEntryNameAndCount(ctx, existEntries, entryName, "", entryAlias)
	if err != nil {
		log.WarnContextf(ctx, "CreateEntry.checkEntryNameAndCount Failed! |%s|%s", sid, err.Error())
		return nil, err
	}

	// 判断taskFlow是否正在发布，发布时不允许增删改
	flowInfos, err := CheckFlowStatusBySlotId(ctx, sid, robotId, slotID)
	if err != nil {
		return nil, err
	}

	// 以事务的方式更新与之相关的一些列表
	entryID := idgenerator.NewUUID()

	insertEntry := buildEntryInfo(ctx, slotEntityInfo.EntityID, entryID, entryName,
		getEntryAliasJsonStr(sid, entryAlias))
	err = db.TXUpsertDeleteEntry(ctx, robotId, slotID, slotEntityInfo.EntityID, []entity.Entry{insertEntry},
		nil, nil, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntry.TXUpsertDeleteEntry Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}

	rsp := &KEP.CreateEntryRsp{EntryId: entryID}
	return rsp, nil
}

// DeleteEntry 删除词条
func DeleteEntry(sid string, ctx context.Context, req *KEP.DeleteEntryReq) (*KEP.DeleteEntryRsp, error) {
	robotId := req.GetBotBizId()
	slotID := req.GetSlotId()
	entryID := req.GetEntryId()
	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntry,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}
	// 判断taskFlow是否正在发布，发布时不允许增删改
	flowInfos, err := CheckFlowStatusBySlotId(ctx, sid, robotId, slotID)
	if err != nil {
		return nil, err
	}

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteEntry.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	// 删除时判断是否关联意图，如果关联则不能删除
	taskFlowInfos, err := db.GetTaskFlowsByEntryId(ctx, robotId, entryID)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|DeleteEntry.GetTaskFlowsByEntryId Failed!|err:%+v", sid, err)
		return nil, err
	}
	if taskFlowInfos != nil {
		if len(taskFlowInfos) > 0 {
			var flowInfos []entity.FlowInfos
			for _, flow := range taskFlowInfos {
				flowInfos = append(flowInfos, entity.FlowInfos{
					FlowID:   flow.FlowID,
					FlowName: flow.IntentName,
				})
			}
			if len(flowInfos) > 0 {
				inUseInfo, err := jsoniter.MarshalToString(flowInfos)
				if err != nil {
					log.ErrorContextf(ctx, "sid:%s|DeleteSlot,marshalFailed!|flowInfos:%+v|err:%+v", sid, flowInfos, err)
					return nil, errors.ErrSystem
				}
				log.WarnContextf(ctx, "sid:%s|EntryNotAllowedDeleted|inUseInfo:%+v", sid, inUseInfo)
				return nil, errors.EntryNotAllowedDeleted(inUseInfo)
			}

			// 判断是否正在发布，发布中的不能删除
			for _, flow := range taskFlowInfos {
				if flow.ReleaseStatus == entity.ReleaseStatusPublishing {
					log.WarnContextf(ctx, "sid:%s|ErrTaskFlowPublishingNotAllowDeleted|flow:%+v", sid, flow)
					return nil, errors.ErrTaskFlowPublishingNotAllowDeleted
				}
			}
		}
	}

	// 以事务的方式更新与之相关的一些列表
	deletedEntry := entity.Entry{
		EntryID:       entryID,
		EntityID:      slotEntityInfo.EntityID,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		Action:        entity.ActionDelete,
		IsDeleted:     entity.DeleteByEntry,
	}

	err = db.TXUpsertDeleteEntry(ctx, robotId, slotID, slotEntityInfo.EntityID, nil, nil,
		[]entity.Entry{deletedEntry}, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteEntry.TXUpsertDeleteEntry Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}
	return new(KEP.DeleteEntryRsp), nil
}

// UpdateEntry 更新词条
func UpdateEntry(ctx context.Context, req *KEP.UpdateEntryReq) (*KEP.UpdateEntryRsp, error) {
	sid := util.RequestID(ctx)

	robotId := req.GetBotBizId()
	slotID := req.GetSlotId()
	entryID := req.GetEntryId()
	entryName, entryAlias := getTrimEntryValueAlias(req.GetValue(), req.GetAlias())

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryList,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntry.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	// 获取当前entityID下的所有词条
	existEntries, err := db.GetEntryList(ctx, slotEntityInfo.EntityID, "")
	if err != nil {
		log.ErrorContextf(ctx, "GetEntriesByEntityID Failed,err:%+v", err)
		return nil, errors.ErrSystem
	}

	// 命名以及数量限制校验
	if len(entryName) > 0 {
		err = checkEntryNameAndCount(ctx, existEntries, entryName, entryID, entryAlias)
		if err != nil {
			log.WarnContextf(ctx, "UpdateEntry.checkEntryNameAndCount Failed! |%s|%s", sid, err.Error())
			return nil, err
		}
	} else {
		return nil, errors.ErrEntryEmptyName
	}

	// 判断taskFlow是否正在发布，发布时不允许增删改
	flowInfos, err := CheckFlowStatusBySlotId(ctx, sid, robotId, slotID)
	if err != nil {
		return nil, err
	}

	// 以事务的方式更新与之相关的一些列表
	updateEntry := entity.Entry{
		EntryID:       entryID,
		EntityID:      slotEntityInfo.EntityID,
		EntryValue:    entryName,
		EntryAlias:    getEntryAliasJsonStr(sid, entryAlias),
		ReleaseStatus: entity.ReleaseStatusUnPublished,
	}

	err = db.TXUpsertDeleteEntry(ctx, robotId, slotID, slotEntityInfo.EntityID, nil,
		[]entity.Entry{updateEntry}, nil, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateEntry.TXUpsertDeleteEntry Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}

	return &KEP.UpdateEntryRsp{EntryId: req.EntryId}, nil

}

// GetEntryList 获取词条列表
func GetEntryList(ctx context.Context, req *KEP.GetEntryListReq) (*KEP.GetEntryListRsp, error) {
	sid := util.RequestID(ctx)

	robotId := req.GetBotBizId()
	slotID := req.GetSlotId()
	queryKeyWord := strings.TrimSpace(req.GetKeyword())

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryList,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryList.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	entryInfos, err := db.GetEntryList(ctx, slotEntityInfo.EntityID, queryKeyWord)
	if err != nil {
		log.ErrorContextf(ctx, "GetEntryList.dbGetEntryList Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}

	var list []*KEP.GetEntryListRsp_Entry
	for _, entryInfo := range entryInfos {
		// 同义词
		var alias []string
		err := jsoniter.Unmarshal([]byte(entryInfo.EntryAlias), &alias)
		if err != nil {
			log.ErrorContextf(ctx, "Unmarshal Failed! sid:%s,data:%+v,err:%v", sid, entryInfo.EntryAlias, err)
		}
		list = append(list, &KEP.GetEntryListRsp_Entry{
			EntryId: entryInfo.EntryID,
			Value:   entryInfo.EntryValue,
			Alias:   alias,
		})
	}

	return &KEP.GetEntryListRsp{
		SlotId: req.SlotId,
		Total:  cast.ToUint32(len(entryInfos)),
		List:   list,
	}, nil
}

// ImportEntry 导入词条
func ImportEntry(ctx context.Context, req *KEP.ImportEntryReq) (*KEP.ImportEntryRsp, error) {
	rsp := new(KEP.ImportEntryRsp)

	sid := util.RequestID(ctx)

	staffID := util.StaffID(ctx)
	slotID := req.GetSlotId()
	robotId := req.GetBotBizId()
	fileName := req.GetFileName()

	// 机器人信息校验
	robotInfo, err := permission.CheckRobot(ctx, 1, robotId)
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	corpID := robotInfo.CorpId

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotID)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntry.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	// 检查数据的有效性
	errRsp, entriesInfo, err := parseEntryXlsx(ctx, req, fileName, corpID)
	if err != nil || errRsp != nil {
		return errRsp, err
	}

	importEntryParams := entity.ImportEntryParamsData{
		SlotID:   slotID,
		EntityID: slotEntityInfo.EntityID,
		Entries:  entriesInfo,
	}

	parmaStr, err := jsoniter.MarshalToString(importEntryParams)
	if err != nil {
		log.ErrorContextf(ctx, "ImportEntry marshalEntryParams Failed! params:%+v,err:%v",
			importEntryParams, err)
		return nil, errors.ErrSystem
	}

	// 创建导入任务
	importID := fmt.Sprintf("%d", idgenerator.NewInt64ID())
	task := fillEntryTaskFlowImport(ctx, importID, cast.ToString(robotId), parmaStr)

	err = db.CreateEntryImportTask(ctx, corpID, staffID, importID, cast.ToString(robotId), fileName, task)
	if err != nil {
		log.ErrorContextf(ctx, "CreateEntryImportTask Failed! sid:%s,err:%v", sid, err)
		return nil, err
	}
	return rsp, nil
}

func checkEntryNameAndCount(ctx context.Context, existEntries []*entity.Entry, entryName, entryID string,
	aliasNames []string) (err error) {
	maxEntryNum := config.GetMainConfig().Entry.MaxEntryNum
	maxNameLen := config.GetMainConfig().Entry.MaxEntryNameLen
	maxAliasNameLen := config.GetMainConfig().Entry.MaxEntryAliasLen
	maxAliasNum := config.GetMainConfig().Entry.MaxEntryAliasNum

	if len(entryName) == 0 {
		return errors.ErrEntryEmptyName
	}

	if len([]rune(entryName)) > maxNameLen {
		return errs.Newf(errors.ErrEntryNameTooLong, "词条名超过%d个字符，请重新填写", maxNameLen)
	}

	// 词条别名个数及长度限制
	if len(aliasNames) > maxAliasNum {
		return errs.Newf(errors.ErrEntryAliasCountExceed, "词条同义词数量超过%d,请重新填写", maxAliasNum)
	}
	duplicateAlias := getDuplicateName(aliasNames)
	if len(duplicateAlias) > 0 {
		return errs.Newf(errors.ErrEntryAliasDuplicated, "重复同义词: %s", duplicateAlias)
	}

	// 当前名称
	uniqueNameMap := make(map[string]int, 0)
	for _, existEntry := range existEntries {
		if existEntry.EntryID == entryID {
			continue
		}
		uniqueNameMap[strings.TrimSpace(existEntry.EntryValue)] = 1
		if len(existEntry.EntryAlias) > 0 {
			var names []string
			_ = json.Unmarshal([]byte(existEntry.EntryAlias), &names)
			for _, name := range names {
				if len(strings.TrimSpace(name)) == 0 {
					continue
				}
				uniqueNameMap[strings.TrimSpace(name)] = 1
			}
		}

	}

	for _, alias := range aliasNames {
		if alias == entryName {
			return errors.ErrEntryNameEqualsAliasName
		}
		if len([]rune(alias)) > maxAliasNameLen {
			return errs.Newf(errors.ErrEntryAliasNameTooLong, "词条同义词名称超过%d个字符,请重新填写", maxAliasNameLen)
		}

		if _, ok := uniqueNameMap[alias]; ok {
			return errs.Newf(errors.ErrEntryAliasDuplicated, "重复同义词: %s", alias)
		}

	}

	if _, ok := uniqueNameMap[entryName]; ok {
		return errs.Newf(errors.ErrEntryAliasDuplicated, "重复同义词: %s", entryName)
	}

	total := len(existEntries)
	if total+1 > maxEntryNum {
		return errs.Newf(errors.ErrEntryCountExceed, "词条数量超过%d个", maxEntryNum)
	}
	return nil
}

func getDuplicateName(slice []string) string {
	seen := make(map[string]bool)
	for _, item := range slice {
		if seen[item] {
			return item
		}
		seen[item] = true
	}
	return ""
}

func parseEntryXlsx(ctx context.Context, req *KEP.ImportEntryReq, fileName string, corpID uint64) (
	*KEP.ImportEntryRsp, []*entity.EntryMigrationInfo, error) {

	sid := util.RequestID(ctx)

	// 下载文件，校验文件格式为xlsx
	excelContent, err := cos.StorageCli.GetObject(ctx, req.GetCosUrl())
	if err != nil {
		log.ErrorContextf(ctx, "parserEntryXlsx downloadFile Failed! sid:%s,err:%v", sid, err)
		return nil, nil, errors.ErrSystem
	}

	minRow := 0
	maxRow := config.GetMainConfig().Entry.MaxEntryNum

	entryHead := config.GetMainConfig().Entry.EntryHead

	// 检查数据
	check := func(rowIndex int, row []string, uniqueMap map[string]int) string {
		entryRow := getEntryInfoFromRow(row)
		errMessages := checkEntryRow(entryRow, rowIndex, uniqueMap)
		if len(errMessages) > 0 {
			return strings.Join(errMessages, "；")
		}
		return ""
	}

	rows, checkErrContent, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, entryHead, excelContent, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.Errorf("ImportEntry parseEntryXlsx Failed! sid:%s,err:%+v", sid, err)
		return nil, nil, err
	}
	log.InfoContextf(ctx, "ImportEntry excelContents,sid:%s,rows:%+v", sid, rows)

	// excel内容检测有问题
	if errors.Is(err, errors.ErrExcelContent) {
		checkFileName := fmt.Sprintf("错误_%s", req.GetFileName())
		cosPath := cos.GetCorpCOSFilePath(corpID, checkFileName)
		if err := cos.StorageCli.PutObject(ctx, checkErrContent, cosPath); err != nil {
			log.ErrorContextf(ctx, "putObject Failed! err:%+v", err)
			return nil, nil, err
		}
		url, err := cos.StorageCli.GetPreSignedURL(ctx, cosPath)
		if err != nil {
			return nil, nil, err
		}

		return &KEP.ImportEntryRsp{ErrorMsg: "文件数据存在错误，请下载并查看错误标注文件", ErrorLink: url,
			ErrorLinkText: "下载"}, nil, nil
	}

	entryRows := make([]*entity.EntryMigrationInfo, 0)
	for _, row := range rows {
		entryRows = append(entryRows, getEntryInfoFromRowWithChecked(row))
	}
	log.InfoContextf(ctx, "ImportEntry ,sid:%s,entryRows:%+v", sid, entryRows)

	return nil, entryRows, nil

}

func getEntryInfoFromRow(row []string) *entity.EntryMigrationInfo {
	entryRow := &entity.EntryMigrationInfo{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelEntryValue:
			entryRow.EntryValue = strings.TrimSpace(cell)
		case entity.ExcelEntryAlias:
			entryRow.EntryAlias = []string{cell}
		}
	}
	return entryRow
}

func getEntryInfoFromRowWithChecked(row []string) *entity.EntryMigrationInfo {
	entryRow := &entity.EntryMigrationInfo{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelEntryValue:
			entryRow.EntryValue = strings.TrimSpace(cell)
		case entity.ExcelEntryAlias:
			var finalAlias []string
			originAlias := strings.Split(cell, "|")
			for _, alias := range originAlias {
				if strings.TrimSpace(alias) != "" {
					finalAlias = append(finalAlias, strings.TrimSpace(alias))
				}
			}
			entryRow.EntryAlias = finalAlias
		}
	}
	return entryRow
}

// / checkEntryRow 导入词汇校验
func checkEntryRow(entryRow *entity.EntryMigrationInfo, rowIndex int, uniqueName map[string]int) []string {
	errMessages := make([]string, 0)

	maxEntryNameLen := config.GetMainConfig().Entry.MaxEntryNameLen
	maxEntryNum := config.GetMainConfig().Entry.MaxEntryNum
	maxEntryAliasLen := config.GetMainConfig().Entry.MaxEntryAliasLen
	maxEntryAliasNum := config.GetMainConfig().Entry.MaxEntryAliasNum

	// 词汇长度名称校验
	entryNameLen := utf8.RuneCountInString(strings.TrimSpace(entryRow.EntryValue))
	if entryNameLen == 0 {
		errMessages = append(errMessages, "词汇为空")
	} else if entryNameLen > maxEntryNameLen {
		errMessages = append(errMessages, fmt.Sprintf("词汇名称超过最大长度:%d", maxEntryNameLen))
	} else {
		if rowIndex, ok := uniqueName[entryRow.EntryValue]; ok {
			errMessages = append(errMessages, fmt.Sprintf("存在重复： %s", entryRow.EntryValue))
		} else {
			uniqueName[entryRow.EntryValue] = rowIndex
		}
	}

	// 同义词校验
	if len(entryRow.EntryAlias) > 0 {

		if !isMatchSeparator(entryRow.EntryAlias[0]) {
			errMessages = append(errMessages, "请用|进行同义词的分割")
		}
		entryAlias := strings.Split(entryRow.EntryAlias[0], "|")
		if len(entryAlias) > maxEntryAliasNum {
			errMessages = append(errMessages, fmt.Sprintf("同义词最多%d个", maxEntryAliasNum))
		}
		for _, alias := range entryAlias {
			if rowIndex, ok := uniqueName[alias]; ok {
				errMessages = append(errMessages, fmt.Sprintf("存在重复: %s", alias))
				break
			} else {
				uniqueName[alias] = rowIndex
			}
			if utf8.RuneCountInString(alias) > maxEntryAliasLen {
				errMessages = append(errMessages, fmt.Sprintf("单个同义词长度超过%d", maxEntryAliasLen))
				break
			}
		}
	}

	if rowIndex > maxEntryNum {
		errMessages = append(errMessages, fmt.Sprintf("最多导入%d条", maxEntryNum))
	}

	return errMessages

}

func fillEntryTaskFlowImport(ctx context.Context, importID, robotID, params string) *entity.TaskFlowImport {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)

	return &entity.TaskFlowImport{
		ImportID: importID,
		RobotID:  robotID,
		Params:   params,
		Uin:      uin,
		SubUin:   subUin,
	}

}

// isMatchSeparator 正则表达式，匹配除了Unicode字符和 | 之外的任何字符
func isMatchSeparator(data string) bool {
	pattern := `[^[:alnum:]\p{Han}|\s]`
	match, _ := regexp.MatchString(pattern, data)
	return !match
}

func buildEntryInfo(ctx context.Context, entityID, entryID, entryName, entryAliasStr string) (entryInfo entity.Entry) {
	uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)
	entry := entity.Entry{
		EntryID:       entryID,
		EntityID:      entityID,
		EntryValue:    entryName,
		EntryAlias:    entryAliasStr,
		UIN:           uin,
		SubUIN:        subAccountUin,
		ReleaseStatus: entity.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity.ActionInsert,
	}
	return entry
}

func getEntryAliasJsonStr(sid string, reqAlias []string) string {
	aliasStr, err := jsoniter.MarshalToString(reqAlias)
	if err != nil {
		log.Errorf("getAliasJsonStr Failed! sid:%s,data:%s,err:%v", sid, reqAlias, err)
		return "[]"
	}
	return aliasStr
}

// GetEntryIdsByEntityId 获取实体下所有词条
func GetEntryIdsByEntityId(ctx context.Context, entityId string) ([]string, error) {
	entryIds := make([]string, 0)
	entryInfos, err := db.GetEntryList(ctx, entityId, "")
	if err != nil {
		return []string{}, err
	}
	if len(entryInfos) > 0 {
		for _, i := range entryInfos {
			entryIds = append(entryIds, i.EntryID)
		}
	}

	return entryIds, nil
}
