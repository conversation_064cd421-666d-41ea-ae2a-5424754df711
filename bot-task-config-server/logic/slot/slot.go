// Package slot 包用于处理位槽相关的逻辑。
package slot

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	entity2 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

// CreateSlot 创建词槽
func CreateSlot(ctx context.Context, req *KEP.CreateSlotReq) (*KEP.CreateSlotRsp, error) {
	sid := util.RequestID(ctx)
	robotId := req.GetBotBizId()
	slotName := strings.TrimSpace(req.GetName())
	slotDesc := strings.TrimSpace(req.GetDesc())
	slotExamples := req.GetExamples()

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "CreateSlot,permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}

	// 槽位名长度校验
	err = checkSlotNameAndLength(ctx, sid, robotId, "", slotName, slotDesc, slotExamples)
	if err != nil {
		return nil, err
	}

	// 生成槽位id, 实体id
	slotID := idgenerator.NewUUID()
	entityID := idgenerator.NewUUID()

	slotInfo, entityInfo, slotEntityInfo := buildSlotEntityInfo(ctx, slotID, entityID, slotName, slotDesc,
		getSlotExamplesJsonStr(sid, slotExamples), robotId)
	err = db.TXCreateSlotEntityTable(ctx, slotInfo, entityInfo, slotEntityInfo)
	if err != nil {
		log.ErrorContextf(ctx, "CreateSlot.TXCreateSlotEntityTable Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}

	rsp := &KEP.CreateSlotRsp{SlotId: slotID}
	return rsp, nil

}

// UpdateSlot 更新词槽信息
func UpdateSlot(ctx context.Context, req *KEP.UpdateSlotReq) (*KEP.UpdateSlotRsp, error) {
	sid := util.RequestID(ctx)

	robotId := req.GetBotBizId()
	slotId := req.GetSlotId()
	slotName := strings.TrimSpace(req.GetName())
	slotDesc := strings.TrimSpace(req.GetDesc())
	slotExamples := req.GetExamples()

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlot,permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}

	// 槽位名长度校验
	err = checkSlotNameAndLength(ctx, sid, robotId, slotId, slotName, slotDesc, slotExamples)
	if err != nil {
		return nil, err
	}

	// 通过slot-entity表找到对应的entity信息
	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotId)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlot.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSlotEntityNotFound
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}
	// 判断taskFlow是否正在发布，发布时不允许增删改
	flowInfos, err := CheckFlowStatusBySlotId(ctx, sid, robotId, slotId)
	if err != nil {
		return nil, err
	}

	// 通过tx更新
	err = db.TXUpdateSlotEntityInfo(ctx, robotId, slotId, slotEntityInfo.EntityID, slotName, slotDesc,
		getSlotExamplesJsonStr(sid, slotExamples), slotExamples, flowInfos)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlot.TXUpdateSlotEntityTable Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	return new(KEP.UpdateSlotRsp), nil

}

// GetSlotList 获取槽位列表
func GetSlotList(ctx context.Context, req *KEP.GetSlotListReq) (*KEP.GetSlotListRsp, error) {
	sid := util.RequestID(ctx)
	robotId := req.GetBotBizId()
	entityScope := req.GetEntityScope()
	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	slotIds := req.GetSlotIds()
	filterNoEntry := req.GetFilterNoEntry()
	isReturnEntry := req.GetIsReturnEntry()
	queryKeyWord := strings.TrimSpace(req.GetKeyword())
	// 参数验证
	if entityScope != entity2.SlotLevelSYS && len(robotId) == 0 {
		log.ErrorContextf(ctx, "GetSlotList,params failed!")
		return nil, errors.BadRequestError("botID为空")
	}
	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotList,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}
	// 临时兼容一下分阶段提测的机器人，在获取词槽的时候创建词条的向量库，并通知DM
	_, _, _ = vdao.NewDao().GetEntryVectorGroupID(ctx, robotId)
	// 从槽位表中获取
	total, slotInfos, err := db.GetSlotFromSlotTable(ctx, robotId, entityScope, queryKeyWord, slotIds,
		offset, limit, filterNoEntry)
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotList,QueryFromDB Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}
	slotEntriesMap, err := db.GetSlotEntriesBySlotId(ctx, slotIds)
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotList,QueryFromDB Failed! sid:%s,err:%v", sid, err)
		return nil, errors.ErrSystem
	}

	list := make([]*KEP.GetSlotListRsp_Slot, 0)
	for _, slot := range slotInfos {
		var entries []*KEP.GetSlotListRsp_Entry
		// 槽位示例
		var slotExamples []string
		err := jsoniter.Unmarshal([]byte(slot.Examples), &slotExamples)
		if err != nil {
			log.ErrorContextf(ctx, "Unmarshal Failed! sid:%s,data:%+v,err:%v", sid, slot.Examples, err)
		}
		slotRsp := &KEP.GetSlotListRsp_Slot{
			SlotId:   slot.SlotID,
			Name:     slot.SlotName,
			Scope:    req.EntityScope,
			Desc:     slot.SlotDesc,
			Examples: slotExamples,
		}
		log.InfoContextf(ctx, "GetEntryList|isReturnEntry:%+v|filterNoEntry:%+v", isReturnEntry, filterNoEntry)
		// 获取词条
		if isReturnEntry {
			entrysInfo := slotEntriesMap[slot.SlotID]

			log.InfoContextf(ctx, "sid:%s|slotEntriesMap:%+v", sid, entrysInfo)
			if len(entrysInfo) > 0 {
				for _, item := range entrysInfo {
					entries = append(entries, &KEP.GetSlotListRsp_Entry{
						EntryId: item.EntryID,
						Value:   item.EntryValue,
					})
				}
				slotRsp.Entries = entries
			}
		}
		list = append(list, slotRsp)
	}
	return &KEP.GetSlotListRsp{
		Total: cast.ToUint32(total),
		List:  list,
	}, nil
}

// DeleteSlot 删除槽位
func DeleteSlot(ctx context.Context, req *KEP.DeleteSlotReq) (*KEP.DeleteSlotRsp, error) {
	sid := util.RequestID(ctx)

	robotId := req.GetBotBizId()
	slotId := req.GetSlotId()

	// 权限验证
	_, err := permission.CheckRobot(ctx, 1, cast.ToUint64(robotId))
	if err != nil {
		log.ErrorContextf(ctx, "UpdateSlot,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	// check槽位是否被引用
	taskFlowInfos, err := db.GetTaskFlowInfoBySlotID(ctx, robotId, slotId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteSlot.GetTaskFlowInfoBySlotID Failed, sid:%s,err:%s", sid, err.Error())
		return nil, errors.ErrSystem
	}
	if len(taskFlowInfos) > 0 {
		var flowInfos []entity2.FlowInfos
		for _, flow := range taskFlowInfos {
			if flow.IntentSlotDeleted != 0 {
				continue
			}
			flowInfos = append(flowInfos, entity2.FlowInfos{
				FlowID:   flow.TaskFlowID,
				FlowName: flow.TaskFlowName,
			})
		}
		if len(flowInfos) > 0 {
			inUseInfo, err := jsoniter.MarshalToString(flowInfos)
			if err != nil {
				log.ErrorContextf(ctx, "DeleteSlot,marshalFailed! data:%+v,err:%s", flowInfos, err.Error())
				return nil, errors.ErrSystem
			}
			return nil, errors.SlotNotAllowedDeleted(inUseInfo)
		}
	}

	// 通过slot-entity表找到对应的entity信息
	slotEntityInfo, err := db.GetSlotEntityRelationBySlotID(ctx, slotId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteSlot.GetSlotEntityRelationBySlotID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSlotEntityNotFound
	}
	if slotEntityInfo.EntityID == "" {
		return nil, errors.ErrSlotEntityNotFound
	}

	// 判断是否正在发布，发布是不允许增删改
	_, err = CheckFlowStatusBySlotId(ctx, sid, robotId, slotId)
	if err != nil {
		return nil, err
	}

	// 通过tx删除
	err = db.TXDeleteSlotEntityInfo(ctx, robotId, slotId, slotEntityInfo.EntityID)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteSlot.TXDeleteSlotEntityInfo Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSystem
	}

	return new(KEP.DeleteSlotRsp), nil

}

func getSlotExamplesJsonStr(sid string, reqSlotExamples []string) string {
	if reqSlotExamples == nil {
		reqSlotExamples = []string{}
	}
	aliasStr, err := jsoniter.MarshalToString(reqSlotExamples)
	if err != nil {
		log.Errorf("getSlotExamplesJsonStr Failed! sid:%s,data:%s,err:%v", sid, reqSlotExamples, err)
		return "[]"
	}
	return aliasStr
}

func buildSlotEntityInfo(ctx context.Context, slotID, entityID, slotName, slotDesc, examples, robotId string) (
	entity2.Slot, entity2.Entity, entity2.SlotEntity) {
	uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)

	entityInfo := entity2.Entity{
		EntityID:      entityID,
		EntityName:    slotName,
		EntityDesc:    slotDesc,
		Examples:      examples,
		LevelType:     entity2.SlotLevelBOT,
		RobotID:       robotId,
		UIN:           uin,
		SubUIN:        subAccountUin,
		ReleaseStatus: entity2.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity2.ActionInsert,
	}

	slotInfo := entity2.Slot{
		SlotID:        slotID,
		SlotName:      slotName,
		SlotDesc:      slotDesc,
		Examples:      examples,
		RobotID:       robotId,
		UIN:           uin,
		SubUIN:        subAccountUin,
		ReleaseStatus: entity2.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity2.ActionInsert,
	}

	slotEntityInfo := entity2.SlotEntity{
		SlotID:        slotID,
		EntityID:      entityID,
		ReleaseStatus: entity2.ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        entity2.ActionInsert,
	}

	return slotInfo, entityInfo, slotEntityInfo
}

func checkSlotNameAndLength(ctx context.Context, sid, botBizId, slotID, slotName, slotDesc string,
	slotExamples []string) error {
	maxEntityNameLen := config.GetMainConfig().Entry.MaxEntityNameLen
	maxEntityExampleLen := config.GetMainConfig().Entry.MaxEntityExampleLen
	maxEntityExampleNum := config.GetMainConfig().Entry.MaxEntityExampleNum
	maxEntityDescLen := config.GetMainConfig().Entry.MaxEntityDescLen

	if len([]rune(slotName)) > maxEntityNameLen {
		return errs.Newf(errors.ErrEntryNameTooLong, "实体名称超过%d个字符，请重新填写", maxEntityNameLen)
	}

	if len([]rune(slotDesc)) > maxEntityDescLen {
		return errs.Newf(errors.ErrEntryDescTooLong, "实体描述超过%d个字符，请重新填写", maxEntityDescLen)
	}

	if len(slotExamples) > maxEntityExampleNum {
		return errs.Newf(errors.ErrEntryExampleCountExceed, "实体示例数量超过%d个，请重新填写", maxEntityExampleNum)
	}

	for _, example := range slotExamples {
		if len([]rune(example)) > maxEntityNameLen {
			return errs.Newf(errors.ErrEntryExampleTooLong, "实体示例超过%d个字符，请重新填写", maxEntityExampleLen)
		}
	}

	// 查询槽位，判断是否重名[当前槽位和实体是一个概念，所以直接从槽位找数据就行了]
	slotInfo, err := db.GetSlotByName(ctx, botBizId, slotName)
	if err != nil {
		log.ErrorContextf(ctx, "CreateSlot,GetSlotByName:%s|%s", sid, err.Error())
		return errors.ErrSystem
	}
	if len(slotInfo) > 1 || (len(slotInfo) == 1 && slotInfo[0].SlotID != slotID) {
		log.WarnContextf(ctx, "CreateSlot,ErrSlotNameDuplicated:%s", sid)
		return errors.ErrSlotNameDuplicated
	}
	return nil
}

// CheckFlowStatusBySlotId ...
func CheckFlowStatusBySlotId(ctx context.Context, sid, robotId, slotId string) ([]db.TaskFlowInfo, error) {
	flowInfos, err := db.GetTaskFlowInfoBySlotID(ctx, robotId, slotId)
	if err != nil {
		log.ErrorContextf(ctx, "GetSlotInUseFlowID Failed! |%s|%s", sid, err.Error())
		return nil, errors.ErrSlotEntityNotFound
	}
	// 如果正处在发布状态，不允许增删改
	for _, flowInfo := range flowInfos {
		if flowInfo.TaskReleaseStatus == entity2.ReleaseStatusPublishing {
			return nil, errors.ErrTaskFlowCantNotPublish
		}
	}
	return flowInfos, nil
}
