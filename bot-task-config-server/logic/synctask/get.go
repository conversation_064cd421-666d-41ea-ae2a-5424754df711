// bot-task-config-server
//
// @(#)get.go  Tuesday, December 12, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"encoding/json"
	"sort"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/synctask/synclog"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error) {
	c := CountUnpublishedTaskFlow(ctx, req.BotBizId)
	return &KEP.GetUnreleasedCountRsp{
		Count: c,
	}, nil
}

func taskStatusConvert(releaseStatus []uint32) []int {
	// 发布状态, 任务状态, 关系映射
	var releaseStatusAndTaskStatusMapping = map[uint32]int{
		2: 1, // 待发布
		4: 3, // 已发布
		5: 4, // 发布失败
	}
	var t []int
	for i := 0; i < len(releaseStatus); i++ {
		if v, ok := releaseStatusAndTaskStatusMapping[releaseStatus[i]]; ok {
			t = append(t, v)
		}
	}
	return t
}

func hasStatus(s int, full []int) bool {
	if len(full) == 0 {
		return false
	}
	for i := 0; i < len(full); i++ {
		if s == full[i] {
			return true
		}
	}
	return false
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
func GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "GetDataSyncTask:req", req)
	sid := util.RequestID(ctx)
	robotID := toRobotID(req.GetBotBizId())
	st, e1 := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
	clues.AddTrackDataWithError(ctx, "GetDataSyncTask:synctask.GetSyncTask",
		clues.M{"robotID": robotID, "taskID": req.GetTaskID(), "st": st}, e1)
	if e1 != nil {
		log.Errorf("GetDataSyncTask|e1|%s|ERR:%v", sid, e1)
		return nil, errors.ErrSyncTaskLoadFailed
	}
	if st.TaskID == 0 { // 任务不存在
		log.Warnf("GetDataSyncTask|st|%s|%+v:is nil", sid, st)
		return &KEP.GetDataSyncTaskRsp{}, nil
	}

	var taskLogs []entity.SyncTaskLog
	var e2 error
	if len(req.ReleaseStatus) == 0 {
		taskLogs, e2 = synclog.ListSyncTaskLogByTask(ctx, robotID, req.GetTaskID())
	} else {
		detailStatus := taskStatusConvert(req.ReleaseStatus)
		has := hasStatus(st.Status, detailStatus)
		clues.AddTrackData(ctx, "detailStatus", clues.M{"detailStatus": detailStatus, "has": has})
		if has {
			taskLogs, e2 = synclog.ListSyncTaskLogByTask(ctx, robotID, req.GetTaskID())
		}
	}
	clues.AddTrackDataWithError(ctx, "GetDataSyncTask:synclog.ListSyncTaskLogByTask",
		clues.M{"robotID": robotID, "taskID": req.GetTaskID(), "taskLogs": taskLogs}, e2)
	if e2 != nil {
		log.Errorf("GetDataSyncTask|e2|%s|ERR:%v", sid, e2)
		return nil, errors.ErrSyncTaskLogLoadFailed
	}
	resp := &KEP.GetDataSyncTaskRsp{
		Task: &KEP.DataSyncTask{
			TaskID:       req.GetTaskID(),
			Operator:     st.UserID,
			Status:       uint32(st.Status),
			SyncItems:    nil,
			SuccessCount: st.SuccessCount,
			FailCount:    st.FailedCount,
		},
	}
	if !st.DoneTime.IsZero() {
		resp.Task.UpdateTime = uint64(st.DoneTime.UnixMilli())
	}
	var syncItems []*KEP.SyncItem
	for i := 0; i < len(taskLogs); i++ {
		if taskLogs[i].Type != string(entity.LogTypeResult) {
			continue
		}
		var items []*KEP.SyncItem
		err := json.Unmarshal([]byte(taskLogs[i].LogContent), &items)
		if err != nil {
			log.Errorf("%s|GetDataSyncTask|%d|json.Unmarshal|%s|%v", i, sid, taskLogs[i].LogContent, err)
			continue
		}
		syncItems = append(syncItems, items...)
	}

	// 下发数据, 需要去重, 优先输出最新的数据
	sort.Slice(syncItems, func(i, j int) bool { return syncItems[i].UpdateTime > syncItems[j].UpdateTime })
	uniqItem := make(map[string]struct{}, 2*len(syncItems))
	for _, si := range syncItems {
		if _, ok := uniqItem[si.GetID()]; ok {
			continue
		}
		uniqItem[si.GetID()] = struct{}{}
		resp.Task.SyncItems = append(resp.Task.SyncItems, si)
	}
	return resp, nil
}

// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
func GetDataSyncTasks(ctx context.Context, req *KEP.GetDataSyncTasksReq) (*KEP.GetDataSyncTasksRsp, error) {
	//TODO implement me
	panic("implement me")
}

// CheckRobotReady 检查机器人, 是否准备好
func CheckRobotReady(ctx context.Context, req *KEP.CheckRobotReadyReq) (*KEP.CheckRobotReadyRsp, error) {
	return &KEP.CheckRobotReadyRsp{
		Ready: true,
	}, nil
}
