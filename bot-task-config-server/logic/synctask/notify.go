// bot-task-config-server
//
// @(#)notify.go  Tuesday, January 02, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/admin"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/synctask/synclog"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
)

// ReleaseNotify 通知发布状态
func ReleaseNotify(ctx context.Context, botBizID, taskID uint64, success bool, message string) error {
	sid := util.RequestID(ctx)
	err := admin.ReleaseNotify(ctx, botBizID, taskID, success, message)
	log.Infof("R|ReleaseNotify|%s|%v|%v|%v|%v|%v", sid, botBizID, taskID, success, message, err)
	var msg string
	if err == nil {
		msg = "OK"
	} else {
		msg = "FAILED"
	}
	msg0 := fmt.Sprintf(`{"success":%t,"message":"%s","invoke":"%s"}`, success, message, msg)
	synclog.RecordSyncTaskLog(ctx, toRobotID(botBizID), taskID, entity.LogTypeNotify, msg0, err)
	return err
}
