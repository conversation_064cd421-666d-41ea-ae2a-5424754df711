// bot-task-config-server
//
// @(#)sync-log.go  Tuesday, December 26, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synclog

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/synctask/synclog"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/go-comm/trpc0"
)

// RecordSyncTaskLog 记录任务发布 log
func RecordSyncTaskLog(ctx context.Context, robotID string, taskID uint64, lt entity.LogType, logContent string, err error) {
	taskLog := entity.SyncTaskLog{
		RobotID:    robotID,
		SyncTaskID: taskID,
		SessionID:  util.RequestID(ctx),
		Type:       string(lt),
		LogContent: logContent,
		ErrorMessage: func() string {
			if err != nil {
				return err.Error()
			}
			return ""
		}(),
		Server: trpc0.AppServer(),
		WorkIP: trpc0.LocalIP(),
	}
	_ = synclog.RecordSyncTaskLog(ctx, taskLog)
}

// GetLastSyncTaskEvent 获取最后的任务事件
func GetLastSyncTaskEvent(ctx context.Context, robotID string, taskID uint64) string {
	sid := util.RequestID(ctx)
	logItems, err := synclog.ListSyncTaskLogByEvent(ctx, robotID, taskID)
	log.Infof("R|GetLastSyncTaskEvent|%s|%s|%d|len:%d|%v|%v", sid, robotID, taskID, len(logItems), logItems, err)
	if err != nil || len(logItems) == 0 {
		return ""
	}
	e := logItems[len(logItems)-1].LogContent
	log.Infof("R|GetLastSyncTaskEvent|%s|%s|%d|%v", sid, robotID, taskID, e)
	return e
}
