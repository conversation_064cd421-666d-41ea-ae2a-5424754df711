// bot-task-config-server
//
// @(#)synctask.go  Monday, December 25, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/synctask/synclog"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/trpc0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// CountUnpublishedTaskFlow ...
func CountUnpublishedTaskFlow(ctx context.Context, botBizID uint64) uint32 {
	return uint32(synctask.CountUnpublishedTaskFlow(ctx, toRobotID(botBizID)))
}

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
func SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error) {
	sid := util.RequestID(ctx)
	if len(codec.Message(ctx).ServerMetaData()["request_id"]) == 0 {
		util.WithRequestID(ctx, sid)
	}
	robotID := toRobotID(req.GetBotBizId())
	synclog.RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeEvent, req.GetEvent(), nil)
	log.Infof("SendDataSyncTaskEvent|%s|%s|%s|%v", req.GetEvent(), sid, robotID, time.Now())

	switch req.GetEvent() {
	case "COLLECT":
		// 检查任务是否存在, 存在则返回错误
		st0, _ := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
		// 查到任务
		if st0.TaskID == req.GetTaskID() {
			return nil, errors.BadRequestError("发布任务已经存在, 请勿重新创建")
		}

		uin, subUin := util.GetUinAndSubAccountUin(ctx)
		st := entity.SyncTask{
			UserID:    subUin,
			UserName:  uin,
			Scene:     req.GetBusinessName(),
			RobotID:   robotID,
			TaskID:    req.GetTaskID(),
			Status:    int(entity.TaskStatusCollect),
			SessionID: sid,
			Server:    trpc0.AppServer(),
			WorkIP:    trpc0.LocalIP(),
			Note:      fmt.Sprintf(`{"corp-id":"%d","staff-id":"%d"}`, req.GetCorpID(), req.GetStaffID()),
		}
		err1 := synctask.CreateSyncTask(ctx, st)
		content, err2 := json.Marshal(st)
		if err2 != nil {
			content = []byte(err2.Error())
		}
		synclog.RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask, string(content), err1)
		log.Infof("SendDataSyncTaskEvent|COLLECT|%s|%v|%v", sid, err1, err2)

		// 将本次要发布的任务, 更新为发布中状态
		items, err3 := ListUnpublishedTaskFlow(ctx, robotID)
		synclog.RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask, fmt.Sprintf("COLLECT|ListUnpublishedTaskFlow|len:%d", len(items)), err3)
		if err3 != nil {
			return &KEP.SendDataSyncTaskEventRsp{TaskID: req.TaskID}, err3
		}
		if len(items) == 0 {
			return &KEP.SendDataSyncTaskEventRsp{TaskID: req.TaskID}, nil
		}

		// 更新任务流状态为发布中
		flowIDs := make([]string, len(items))
		for i := 0; i < len(items); i++ {
			flowIDs[i] = items[i].FlowID
		}
		err4 := synctask.UpdateTaskFlowReleaseStatus(ctx, robotID, flowIDs, entity.ReleaseStatusPublishing)
		m0 := fmt.Sprintf(`COLLECT|UpdateTaskFlowReleaseStatus|len:%d|["%s"]`, len(items), strings.Join(flowIDs, `","`))
		synclog.RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask, m0, err4)

		// collect 时, 记录本次要发布的任务流程
		recordSyncItems(ctx, robotID, req.GetTaskID(), items, req.GetEvent())

		return &KEP.SendDataSyncTaskEventRsp{TaskID: req.TaskID}, err1

	case "RELEASE", "RETRY":
		// 检查任务是否存在, 不存在则返回错误
		st, e1 := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
		if e1 != nil {
			log.Errorf("GetDataSyncTask|%s|%v", sid, e1)
			return nil, errors.ErrSyncTaskLoadFailed
		}
		// 没有查到任务
		if st.TaskID == 0 {
			return nil, errors.BadRequestError("发布任务不存在")
		}

		// 检查任务是否可以马上执行
		ctx2 := clues.NewTrackContext(trpc.CloneContext(ctx))
		go saveTaskForSync(ctx2, req)
		return &KEP.SendDataSyncTaskEventRsp{TaskID: req.TaskID}, nil

	case "PAUSE":

	}
	return nil, nil
}

func saveTaskForSync(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) {
	// 系统可以同时运行的任务数, 如果超出, 则需要等待
	c := CountRunningReleaseTask(ctx)
	if c < config.GetMainConfig().DataSyncTask.ReleaseQueueLength {
		releaseTasks(ctx, req.GetBotBizId(), req.GetTaskID(), req.GetEvent())
		return
	}
	queueTasks(ctx, req)
}

func releaseTasks(ctx context.Context, botBizID, taskID uint64, event string) {
	sid := util.RequestID(ctx)
	robotID := toRobotID(botBizID)
	err := synctask.UpdateSyncTaskStatus(ctx, robotID, taskID, entity.TaskStatusProcessing)
	synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, `{"status":2,"desc":"ing"}`, err)
	// 执行发布任务
	items, err1 := ListCollectedTaskFlow(ctx, robotID, event)
	if err1 != nil {
		synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, "查询任务流失败", err1)
		// 回调错误通知
		e0 := ReleaseNotify(ctx, botBizID, taskID, false, err1.Error())
		log.Errorf("ReleaseNotify|%s|%v", sid, e0)

		// 失败记录
		e1 := synctask.UpdateSyncTaskStatusAndCount(ctx, robotID, taskID, entity.TaskStatusSyncFailed, 0, len(items))
		log.Infof("UpdateSyncTaskStatusAndCount|%s|%v", sid, e1)
		return
	}

	// 发布记数
	var successCount int

	p := publish.NewPublish()
	// 批次处理
	batch := len(items) / int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch)
	log.Infof("releaseTasks|%s|TOTAL:%d|BATCH:%d", sid, len(items), batch)
	clues.AddTrackData(ctx, "CONFIG:ReleaseItemsPerBatch", config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch)
	for i := 0; i <= batch; i++ {
		begin := int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch) * i
		end := int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch) * (i + 1)
		if end > len(items) {
			end = len(items)
		}
		clues.AddTrackData(ctx, "BATCH", map[string]any{"batch": batch, "i": i, "begin": begin, "end": end})
		t1 := time.Now()
		items0 := items[begin:end]
		itemIDs := make([]string, len(items0))
		for j := 0; j < len(items0); j++ {
			itemIDs[j] = items0[j].FlowID
		}

		var insertIDs, updateIDs, deleteIDs []string
		var insertItems, updateItems, deleteItems []entity.TaskFlow
		for _, item := range items0 {
			switch item.Action {
			case entity.ActionInsert:
				insertIDs = append(insertIDs, item.FlowID)
				insertItems = append(insertItems, item)
			case entity.ActionUpdate:
				updateIDs = append(updateIDs, item.FlowID)
				updateItems = append(updateItems, item)
			case entity.ActionDelete:
				deleteIDs = append(deleteIDs, item.FlowID)
				deleteItems = append(deleteItems, item)
			}
		}

		err2 := p.ICSTaskFlowPublish(ctx, robotID, taskID, itemIDs)
		log.Infof("%s|%s|releaseTasks()|batch:%d/%d [%d,%d)|%s|%v", robotID, sid, i, batch, begin, end, time.Since(t1), "")
		if err2 != nil { // 任务流程发布错误
			publishMsg := fmt.Sprintf("INSERT:[%s],UPDATE:[%s],DELETE:[%s]",
				strings.Join(insertIDs, ","),
				strings.Join(updateIDs, ","),
				strings.Join(deleteIDs, ","),
			)
			synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, publishMsg, err2)
			log.Errorf("ICSTaskFlowPublish|%s|%v|%v|%v|%v", sid, robotID, batch, clues.GetTrackDataJSON(ctx), err2.Error())
			recordSyncItems(ctx, robotID, taskID, items0, errs.Msg(err2))
			continue
		}

		recordDiff(ctx, robotID, taskID, batch, insertIDs, updateIDs, deleteIDs, insertItems, updateItems)
		recordRelease(ctx, robotID, taskID, batch, insertItems, updateItems, deleteItems)
		// 记录历史发布任务
		log.Infof("sid:%s|recordReleaseHistory|robotId:%s|batch:%d/%d [%d,%d)|%s|%v", sid,
			robotID, i, batch, begin, end, time.Since(t1), "")
		err3 := recordReleaseHistory(ctx, insertItems, updateItems, deleteItems)
		if err3 != nil {
			saveReleaseHistoryMsg := fmt.Sprintf("INSERT:[%s],UPDATE:[%s],DELETE:[%s]",
				strings.Join(insertIDs, ","),
				strings.Join(updateIDs, ","),
				strings.Join(deleteIDs, ","),
			)
			synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeSaveRelease, saveReleaseHistoryMsg, err3)
			log.Errorf("E|recordReleaseHistory|%s|%v|%v|%v|ERR:%v", sid, robotID, batch,
				clues.GetTrackDataJSON(ctx), err3.Error())
			recordSyncItems(ctx, robotID, taskID, items0, "保存历史记录错误")
			continue
		}

		// 记数统计
		successCount += len(items0)
		recordSyncItems(ctx, robotID, taskID, items0, "")
	}
	// 发布状态
	var syncStatus entity.TaskStatus
	failedCount := len(items) - successCount
	if failedCount == 0 {
		syncStatus = entity.TaskStatusSyncSuccess
	} else {
		syncStatus = entity.TaskStatusSyncFailed
	}
	e1 := synctask.UpdateSyncTaskStatusAndCount(ctx, robotID, taskID, syncStatus, successCount, failedCount)
	msg0 := fmt.Sprintf(`{"status";"success","count":%d,"failed":%d}`, successCount, failedCount)
	synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, msg0, e1)
	log.Infof("UpdateSyncTaskStatusAndCount|%s|%v", sid, e1)

	// 回调发布成功通知
	e0 := ReleaseNotify(ctx, botBizID, taskID, syncStatus == entity.TaskStatusSyncSuccess, msg0)
	log.Infof("ReleaseNotify|%s|%v", sid, e0)
}

func recordSyncItems(ctx context.Context, robotID string, taskID uint64, items0 []entity.TaskFlow, message string) {
	// 发步结果记录
	releaseItems := make([]KEP.SyncItem, len(items0))
	for j := 0; j < len(items0); j++ {
		releaseItems[j] = KEP.SyncItem{
			ID:         items0[j].FlowID,
			Title:      items0[j].IntentName,
			Type:       "TASK_FLOW",
			Action:     items0[j].Action,
			UpdateTime: uint64(time.Now().UnixMilli()),
			Message:    message,
		}
	}
	rj, erj := json.Marshal(releaseItems)
	synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeResult, string(rj), erj)
}

func queueTasks(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) {
	sid := util.RequestID(ctx)
	robotID := toRobotID(req.GetBotBizId())
	err := synctask.UpdateSyncTaskStatus(ctx, robotID, req.GetTaskID(), entity.TaskStatusQueuing)
	log.Infof("ENTER-QUEUE|%s|%v|%v", sid, robotID, req.GetTaskID())
	synclog.RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask, `{"status":55,"desc":"queuing"}`, err)
}

// CountRunningReleaseTask 统计正在处理的任务
func CountRunningReleaseTask(ctx context.Context) uint32 {
	return synctask.CountSyncTaskByStatus(ctx, entity.TaskStatusProcessing)
}

// HasSyncTask 是否有同步任务
func HasSyncTask(ctx context.Context, robotID string) bool {
	return synctask.CountRunningSyncTask(ctx, robotID) > 0
}

func toRobotID(botBizID uint64) string {
	return fmt.Sprintf("%d", botBizID)
}

func recordDiff(ctx context.Context, robotID string, taskID uint64, batch int,
	insertIDs, updateIDs, deleteIDs []string,
	insertItems, updateItems []entity.TaskFlow) {
	sid := util.RequestID(ctx)
	m0 := map[string]any{"batch": batch, "INSERT-IDs": insertIDs, "UPDATE-IDs": updateIDs, "DELETE-IDs": deleteIDs}
	clues.AddTrackData(ctx, "BATCH:DIFF", m0)

	updateProdItems, err1 := ListProdTaskFlowByIDs(ctx, robotID, updateIDs)
	log.Infof("recordDiff|update|%s|%v|%v", sid, len(updateProdItems), err1)
	deleteProdItems, err2 := ListProdTaskFlowByIDs(ctx, robotID, deleteIDs)
	log.Infof("recordDiff|delete|%s|%v|%v", sid, len(deleteProdItems), err2)

	m1 := map[string]any{
		"batch":      batch,
		"INSERT-IDs": insertIDs, "UPDATE-IDs": updateIDs, "DELETE-IDs": deleteIDs,
		"INSERT-ITEMs": insertItems, "UPDATE-ITEMs": updateItems,
		"UPDATE-PROD-ITEMs": updateProdItems, "DELETE-PROD-ITEMs": deleteProdItems,
	}
	if err1 != nil {
		m1["list-update-prod-items-error"] = err1.Error()
	}
	if err2 != nil {
		m1["list-delete-prod-items-error"] = err2.Error()
	}

	m1j, m1err := json.Marshal(m1)
	synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeDiff, string(m1j), m1err)
}

func recordRelease(ctx context.Context, robotID string, taskID uint64, batch int,
	insertItems, updateItems, deleteItems []entity.TaskFlow) {
	m1 := map[string]any{"batch": batch, "INSERT": insertItems, "UPDATE": updateItems, "DELETE": deleteItems}
	m1j, m1err := json.Marshal(m1)
	synclog.RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeRelease, string(m1j), m1err)
}

func getReleaseHistoryIds(deleteItems *[]entity.TaskFlow) []string {
	var historyFlowTasksIds []string
	if deleteItems != nil {
		for _, item := range *deleteItems {
			historyFlowTasksIds = append(historyFlowTasksIds, item.FlowID)
		}
	}
	return historyFlowTasksIds
}

// recordReleaseHistory 记录发布版本信息
func recordReleaseHistory(ctx context.Context, insertItems, updateItems, deleteItems []entity.TaskFlow) error {
	sid := util.RequestID(ctx)
	// 保存更新 发布任务流新记录
	var flowTasks []entity.TaskFlow
	if insertItems != nil {
		flowTasks = append(flowTasks, insertItems...)
	}
	if updateItems != nil {
		flowTasks = append(flowTasks, updateItems...)
	}
	if flowTasks != nil {
		timeStamp := strconv.Itoa(int(time.Now().Unix()))
		publishTime := time.Now()
		insertReleaseTaskFlows := entity.GetReleaseTaskFlowFlows(&flowTasks, timeStamp, publishTime)
		log.DebugContextf(ctx, "sid:%s|recordReleaseHistory,GetReleaseTaskFlowFlows:%+v",
			sid, insertReleaseTaskFlows)
		err := synctask.RecordSyncTaskFlowRelease(ctx, insertReleaseTaskFlows)
		if err != nil {
			log.ErrorContextf(ctx, "I|RecordSyncTaskFlowRelease|sid:%s|insertItems:%+v|err:%s",
				sid, insertReleaseTaskFlows, err.Error())
			return err
		}

		// 通过任务流程获取 示例问法并记录到 历史发布示例问法关联表
		insertReleaseIntentCorpus, err := synctask.GetIntentCorpusByTaskflows(ctx, &flowTasks, timeStamp, publishTime)
		if err != nil {
			log.ErrorContextf(ctx, "I|RecordSyncTaskFlowRelease|GetIntentCorpusByTaskflows|"+
				"sid:%s|flowTasks:%+v|err:%+v", sid, flowTasks, err)
			return err
		}
		if insertReleaseIntentCorpus != nil && len(*insertReleaseIntentCorpus) > 0 {
			err = synctask.RecordSyncIntentCorpusRelease(ctx, insertReleaseIntentCorpus)
			if err != nil {
				log.ErrorContextf(ctx, "I|RecordSyncIntentCorpusRelease|sid:%s|"+
					"insertReleaseIntentCorpus:%+v|err:%+v", sid, insertReleaseIntentCorpus, err)
				return err
			}
		}
	}

	// 发布删除任务流
	if deleteItems != nil {
		deleteFlowIds := getReleaseHistoryIds(&deleteItems)
		log.DebugContextf(ctx, "sid:sid|recordReleaseHistory,getReleaseHistoryIds:%+v", deleteFlowIds)
		err := synctask.DeleteSyncTaskFlowRelease(ctx, deleteFlowIds)
		if err != nil {
			log.ErrorContextf(ctx, "I|RecordSyncTaskFlowRelease|sid:%s|deleteItems:%+v|err:%s",
				sid, deleteItems, err.Error())
			return err
		}
	}
	return nil
}

// ListUnpublishedTaskFlow 查询未发布的任务
func ListUnpublishedTaskFlow(ctx context.Context, robotID string) ([]entity.TaskFlow, error) {
	return synctask.ListUnpublishedTaskFlow(ctx, robotID, []string{entity.ReleaseStatusUnPublished})
}

// ListCollectedTaskFlow 查询准备发布的任务
func ListCollectedTaskFlow(ctx context.Context, robotID, event string) ([]entity.TaskFlow, error) {
	if event == "RETRY" {
		return synctask.ListUnpublishedTaskFlow(ctx, robotID, []string{entity.ReleaseStatusPublishing, entity.ReleaseStatusFail})
	}
	return synctask.ListUnpublishedTaskFlow(ctx, robotID, []string{entity.ReleaseStatusPublishing})
}

// CountUnpublishedTaskFlowWithQuery 查询未发布的任务数量
func CountUnpublishedTaskFlowWithQuery(ctx context.Context, robotID string, params entity.ListTaskFLowParams) int64 {
	return synctask.CountUnpublishedTaskFlowWithQuery(ctx, robotID, params)
}

// ListUnpublishedTaskFlowWithQuery 查询未发布的任务
func ListUnpublishedTaskFlowWithQuery(ctx context.Context, robotID string, params entity.ListTaskFLowParams) ([]entity.TaskFlow, error) {
	return synctask.ListUnpublishedTaskFlowWithQuery(ctx, robotID, params)
}
