// bot-task-config-server
//
// @(#)task-flow.go  Monday, January 08, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package synctask

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// ListSandboxTaskFlowByIDs 按 id 查对应的任务流程
func ListSandboxTaskFlowByIDs(ctx context.Context, robotID string, ids []string) ([]entity.TaskFlow, error) {
	return synctask.ListTaskFlowByIDs(ctx, database.GetLLMRobotTaskGORM(), robotID, ids)
}

// ListProdTaskFlowByIDs 按 id 查对应的任务流程
func ListProdTaskFlowByIDs(ctx context.Context, robotID string, ids []string) ([]entity.TaskFlow, error) {
	return synctask.ListTaskFlowByIDs(ctx, database.GetLLMRobotTaskProdGORM(), robotID, ids)
}
