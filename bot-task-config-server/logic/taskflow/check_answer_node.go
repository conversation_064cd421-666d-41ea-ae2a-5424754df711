package taskflow

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// checkAnswerNode 答案节点的数据校验
func checkAnswerNode(ctx context.Context, node *KEP.AnswerNodeData) error {
	if node == nil {
		log.ErrorContextf(ctx, "AnswerNodeData is nil")
		return errors.TaskFLowVerifyError("答案节点解析错误")
	}
	if node.GetAnswerType() != KEP.AnswerNodeData_LLM &&
		node.GetAnswerType() != KEP.AnswerNodeData_INPUT &&
		node.GetAnswerType() != KEP.AnswerNodeData_DOC {
		log.WarnContextf(ctx, "checkAnswerNode|type:%s", node.GetAnswerType().String())
		return errors.TaskFLowVerifyError(fmt.Sprintf("答案节点数据来源不对%s", node.GetAnswerType().String()))
	}

	if node.GetAnswerType() == KEP.AnswerNodeData_INPUT {
		// 校验自定义答案的长度
		customMax := config.GetMainConfig().VerifyTaskFlow.AnswerNodeCustomMax
		previewLength := len([]rune(RemoveRichText(node.GetInputAnswerData().GetPreview())))
		if previewLength > customMax {
			log.WarnContextf(ctx, "checkAnswerNode|previewLength:%d|max:%d", previewLength, customMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("答案节点自定义答案超过最大长度%d限制", customMax))
		}
	}
	if node.GetAnswerType() == KEP.AnswerNodeData_LLM {
		// 校验自定义提示词的长度
		if node.GetLLMAnswerData().GetEnableCustomPromptWord() {
			promptMax := config.GetMainConfig().VerifyTaskFlow.AnswerNodePromptMax
			promptLength := len([]rune(RemoveRichText(node.GetLLMAnswerData().GetPrompt())))
			if promptLength > promptMax {
				log.WarnContextf(ctx, "checkAnswerNode|promptLength:%d|max:%d", promptLength, promptMax)
				return errors.TaskFLowVerifyError(fmt.Sprintf("答案节点智能回复自定义提示词长度超过最大长度%d限制",
					promptMax))
			}
		}
	}
	return nil
}
