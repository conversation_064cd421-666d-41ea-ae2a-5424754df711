package taskflow

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func Test_checkAnswerNode(t *testing.T) {
	type args struct {
		ctx  context.Context
		node *KEP.AnswerNodeData
	}
	config.GetMainConfigForUnitTest().VerifyTaskFlow = config.VerifyTaskFlow{
		AnswerNodeCustomMax: 3000,
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "标准-大模型生成的答案",
			wantErr: false,
			args: args{
				ctx: context.Background(),
				node: &KEP.AnswerNodeData{
					AnswerType: KEP.AnswerNodeData_LLM,
					LLMAnswerData: &KEP.AnswerNodeData_LLMAnswer{
						Preview: []string{
							"大模型生成的答案内容111111",
							"大模型生成的答案内容222222",
						},
					},
				},
			},
		},
		{
			name:    "标准-用户输入的",
			wantErr: false,
			args: args{
				ctx: context.Background(),
				node: &KEP.AnswerNodeData{
					AnswerType: KEP.AnswerNodeData_INPUT,
					InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
						Preview: "标准-用户输入的",
					},
				},
			},
		},
		{
			name:    "标准-文档的",
			wantErr: false,
			args: args{
				ctx: context.Background(),
				node: &KEP.AnswerNodeData{
					AnswerType: KEP.AnswerNodeData_DOC,
					DocAnswerData: &KEP.AnswerNodeData_DocAnswer{
						Preview: "aaa",
						RefInfo: []*KEP.AnswerNodeData_DocAnswer_DocRefInfo{
							{
								DocType: 1,
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkAnswerNode(tt.args.ctx, tt.args.node); (err != nil) != tt.wantErr {
				t.Errorf("checkAnswerNode() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
