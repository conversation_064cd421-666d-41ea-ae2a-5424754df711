package taskflow

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/security"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

var (
	// for unit test
	checkSecurityIfDenyAccess = security.IfDenyAccess
)

// checkAPINode 校验API节点
func checkAPINode(ctx context.Context, tCtx *TreeContext, node *KEP.APINodeData) error {
	if node == nil {
		log.ErrorContextf(ctx, "APINodeData is nil")
		return errors.TaskFLowVerifyError("API节点解析错误")
	}

	// 1. 校验API的必要节点(入参)
	err := checkAPINodeRequest(ctx, tCtx, node)
	if err != nil {
		return err
	}

	// 2. 校验API的Header
	err = checkAPINodeHeaders(ctx, tCtx, node)
	if err != nil {
		return err
	}

	// 3. 大模型生成的询问内容预览
	llmAskPreviewMaxItems := config.GetMainConfig().VerifyTaskFlow.RequiredProbePreviewMax
	if len(node.GetLLMAskPreview()) > llmAskPreviewMaxItems {
		log.WarnContextf(ctx, "checkRequest|LLMAskPreviewItems=%d|llmAskPreviewMaxItems=%d",
			len(node.GetLLMAskPreview()), llmAskPreviewMaxItems)
		return errors.TaskFLowVerifyError(fmt.Sprintf("询问内容超过最大限制%d", llmAskPreviewMaxItems))
	}

	// 4. APIPath校验
	err = checkAPIPath(ctx, node.GetAPI(), tCtx.SafeUrls)
	if err != nil {
		return err
	}
	// 5. APINode 出参校验
	err = checkAPINodeResponse(ctx, node)
	if err != nil {
		return err
	}

	return nil
}

// checkAPINodeRequestParamNameDuplicate 检验参数名是否重复
func checkAPINodeRequestParamNameDuplicate(ctx context.Context, duplicateParamName map[string]struct{},
	request *KEP.APINodeData_RequestParam) error {

	_, ok := duplicateParamName[request.GetParamName()]
	if ok {
		log.WarnContextf(ctx, "checkAPINodeRequest|duplicateParamName:%s", request.GetParamName())
		return errors.TaskFLowVerifyError("入参参数名重复:" + request.GetParamName())
	}
	duplicateParamName[request.GetParamName()] = struct{}{}

	return nil
}

func checkAPINodeRequestItem(ctx context.Context, tCtx *TreeContext, request *KEP.APINodeData_RequestParam) error {
	duplicateParamName := make(map[string]struct{})
	maxRequestSubDepth := config.GetMainConfig().VerifyTaskFlow.APIRequestParamSubMaxDepth
	subRequestDepth := getAPINodeDataRequestParamDepth(request)
	subIsReq := 0
	//log.InfoContextf(ctx, "sid:%s|checkAPINodeRequestItem|subHeadDepth:%d", sid, subRequestDepth)
	if subRequestDepth > maxRequestSubDepth {
		log.WarnContextf(ctx, "checkAPINodeRequestItem|subRequestDepth：%d over limit %d", subRequestDepth,
			maxRequestSubDepth)
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数层级超最大限制%d", request.GetParamName(),
			maxRequestSubDepth))
	}
	err := checkAPINodeDataRequestParam(ctx, tCtx, request)
	if err != nil {
		return err
	}

	if len(request.GetSubRequest()) > 0 {
		for _, subReq := range request.GetSubRequest() {
			// 重名称检测
			err = checkAPINodeRequestParamNameDuplicate(ctx, duplicateParamName, subReq)
			if err != nil {
				return err
			}
			if subReq.GetIsRequired() {
				subIsReq += 1
			}
			err = checkAPINodeRequestItem(ctx, tCtx, subReq)
			if err != nil {
				return err
			}
		}

		if request.GetIsRequired() && subIsReq == 0 {
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数必填，其子参数必有至少一项必填", request.GetParamName()))
		}
	}

	return nil
}

// checkAPINodeRequest API节点的入参信息判断
func checkAPINodeRequest(ctx context.Context, tCtx *TreeContext, apiNode *KEP.APINodeData) error {
	sid := util.RequestID(ctx)
	maxCount := config.GetMainConfig().VerifyTaskFlow.APINodeRequiredInfoMax
	if len(apiNode.GetRequest()) > maxCount {
		log.WarnContextf(ctx, "checkAPINodeRequest|Request.len=%d|maxCount=%d", len(apiNode.GetRequest()), maxCount)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点请求参数数量超过%d限制", maxCount))
	}
	minCount := config.GetMainConfig().VerifyTaskFlow.APINodeRequiredInfoMin
	if len(apiNode.GetRequest()) < minCount {
		log.WarnContextf(ctx, "checkAPINodeRequest|Request.len=%d|minCount=%d", len(apiNode.GetRequest()), minCount)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点请求参数数量少于%d个", minCount))
	}

	// 产品结论：只判断 paramName的重复；
	// 同一个节点 参数名不能重复, 不同层级可以重复
	duplicateParamName := make(map[string]struct{})
	for _, req := range apiNode.GetRequest() {
		// 重名称检测
		err := checkAPINodeRequestParamNameDuplicate(ctx, duplicateParamName, req)
		if err != nil {
			return err
		}
		err = checkAPINodeRequestItem(ctx, tCtx, req)
		if err != nil {
			log.WarnContextf(ctx, "sid:%s|checkAPINodeRequest|err:%+v", sid, err)
			return err
		}
	}
	return nil
}

// getAPINodeDataRequestParamDepth 获取API入参数的层级
func getAPINodeDataRequestParamDepth(requestParam *KEP.APINodeData_RequestParam) int {
	var depth int
	if requestParam == nil {
		return 0
	}
	if requestParam.GetSubRequest() == nil || len(requestParam.GetSubRequest()) == 0 {
		return 1
	}
	depth += 1
	var subDepth int
	for _, sub := range requestParam.GetSubRequest() {
		temp := getAPINodeDataRequestParamDepth(sub)
		if temp > subDepth {
			// 拿到最深的
			subDepth = temp
		}
	}
	depth += subDepth
	return depth
}
func checkAPINodeDataRequestParam(ctx context.Context, tCtx *TreeContext, param *KEP.APINodeData_RequestParam) error {
	// 检查api节点query的参数名称
	err := checkAPIRequestParamName(ctx, param)
	if err != nil {
		return err
	}
	paramType := strings.ToLower(param.GetParamType())
	switch paramType {
	case ParamTypeString, ParamTypeInt, ParamTypeFloat, ParamTypeBool, ParamTypeArray, ParamTypeObject:
		break
	default:
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName:%s|ParamType%s", param.GetParamName(),
			param.GetParamType())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点输入参数%s类型错误", param.GetParamName()))
	}

	if paramType != ParamTypeObject && len(param.GetSubRequest()) > 0 {
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName:%s|ParamType%s can't has SubRequest %+v",
			param.GetParamName(), param.GetParamType(), param.GetSubRequest())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点请求参数%s的类型为%s，不存在子参数%+v",
			param.GetParamName(), param.GetParamType(), param.GetSubRequest()))
	}

	if paramType == ParamTypeObject {
		if len(param.GetSubRequest()) == 0 {
			log.WarnContextf(ctx, "paramType is object, SubRequest is empty")
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数为%s类型，其子参数至少一项必填",
				param.GetParamName(), param.GetParamType()))
		}

		if len(param.GetSourceType().String()) > 0 && param.GetSourceType() != KEP.APINodeData_RequestParam_EMPTY {
			log.WarnContextf(ctx, "ParamType is object, SourceType is not empty")
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数为%s类型，其数据来源不可选",
				param.GetParamName(), param.GetParamType()))
		}
	}

	id := getRequestID(param)
	if len(id) == 0 && paramType != ParamTypeObject {
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName:%s|ParamType:%s|value is empty",
			param.GetParamName(), param.GetParamType())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点输入参数%s的数据来源为空", param.GetParamName()))
	}

	switch param.GetSourceType() {
	case KEP.APINodeData_RequestParam_SLOT:
		// slot有效性在 checkSlot 统一检查，这里不用再检查一次了
		err := checkRequestParamSlotAsk(ctx, param)
		if err != nil {
			return err
		}
		err = checkAPIReqSlotFormatDesc(ctx, param)
		if err != nil {
			return err
		}
	case KEP.APINodeData_RequestParam_API_RESP:
		// 输入参数中使用的其他节点API的出参有效性（看是否在其他api节点的出参中）
		_, ok := tCtx.APIResponseParamTypes[id]
		if !ok {
			log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName:%s|API_RESP|ParamID:%s not exist",
				param.GetParamName(), id)
			return errors.TaskFLowVerifyError(fmt.Sprintf("API节点输入参数的%s的接口出参[%s]不存在",
				param.GetParamName(), id))
		}
	case KEP.APINodeData_RequestParam_FIXED:
		fixedValueMaxCount := config.GetMainConfig().VerifyTaskFlow.APINodeFixedValueMaxCount
		if len([]rune(id)) > fixedValueMaxCount {
			log.WarnContextf(ctx, "checkAPINodeDataRequestParam|fixedValue.len=%d|fixedValueMaxCount=%d",
				len([]rune(id)), fixedValueMaxCount)
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s超过最大限制%d", param.GetParamName(),
				fixedValueMaxCount))
		}
	case KEP.APINodeData_RequestParam_SYSTEM:
	}
	return nil
}

// checkAPIRequestParamName 检查api节点query的参数名称
func checkAPIRequestParamName(ctx context.Context, param *KEP.APINodeData_RequestParam) error {
	if len(strings.TrimSpace(param.GetParamName())) == 0 {
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName is empty")
		return errors.TaskFLowVerifyError("输入参数名不能为空")
	}
	if !isValidJSONField(param.GetParamName()) {
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|isValidJSONField|ParamName:%s", param.GetParamName())
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s不支持的参数名", param.GetParamName()))
	}
	// 校验必要信息名称字数最大限制(不超过50字符)
	nameMaxLen := config.GetMainConfig().VerifyTaskFlow.RequiredInfoNameMax
	if len([]rune(param.GetParamName())) > nameMaxLen {
		log.WarnContextf(ctx, "checkAPINodeDataRequestParam|ParamName.len=%d|nameMaxLen=%d",
			len([]rune(param.GetParamName())), nameMaxLen)
		return errors.TaskFLowVerifyError(fmt.Sprintf("参数名称超过最大限制%d", nameMaxLen))
	}
	return nil
}

// getAPINodeHeadersDepth 获取API节点Header参数的层级
func getAPINodeHeadersDepth(headerParam *KEP.APINodeData_Header) int {
	var depth int
	if headerParam == nil {
		return 0
	}
	if headerParam.GetSubHeader() == nil || len(headerParam.GetSubHeader()) == 0 {
		return 1
	}
	depth += 1
	var subDepth int
	for _, sub := range headerParam.GetSubHeader() {
		temp := getAPINodeHeadersDepth(sub)
		if temp > subDepth {
			// 拿到最深的
			subDepth = temp
		}
	}
	depth += subDepth
	return depth
}

// checkAPINodeHeadParamNameDuplicate 检验参数名是否重复
func checkAPINodeHeadParamNameDuplicate(ctx context.Context, duplicateHeaderKey map[string]struct{},
	header *KEP.APINodeData_Header) error {
	_, ok := duplicateHeaderKey[header.GetParamName()]
	if ok {
		log.WarnContextf(ctx, "checkAPINodeHeaders|duplicateHeaderKey:%s", header.GetParamName())
		return errors.TaskFLowVerifyError("Header参数名重复:" + header.GetParamName())
	}
	duplicateHeaderKey[header.GetParamName()] = struct{}{}
	return nil
}
func checkAPINodeHeadItem(ctx context.Context, tCtx *TreeContext, isOpt bool, header *KEP.APINodeData_Header) error {
	sid := util.RequestID(ctx)
	duplicateHeaderKey := make(map[string]struct{})
	maxHeaderSubDepth := config.GetMainConfig().VerifyTaskFlow.APINodeHeadersParamMaxDepth
	subHeadDepth := getAPINodeHeadersDepth(header)
	subIsOpt := 0
	log.InfoContextf(ctx, "sid:%s|checkAPINodeHeaders|subHeadDepth:%d", sid, subHeadDepth)
	if subHeadDepth > maxHeaderSubDepth {
		log.WarnContextf(ctx, "checkAPINodeHeaders|subHeadDepth %d is over limit maxHeaderSubDepth:%d",
			subHeadDepth, maxHeaderSubDepth)
		return errors.TaskFLowVerifyError(fmt.Sprintf("Header参数层级超最大限制%d", maxHeaderSubDepth))
	}
	err := checkAPINodeDataHeaderParam(ctx, tCtx, header)
	if err != nil {
		return err
	}

	if len(header.GetSubHeader()) > 0 {
		for _, subHeader := range header.GetSubHeader() {
			// 子级同级重复名称检测
			err = checkAPINodeHeadParamNameDuplicate(ctx, duplicateHeaderKey, subHeader)
			if err != nil {
				return err
			}

			if subHeader.GetIsOptional() {
				subIsOpt += 1
			}
			err = checkAPINodeHeadItem(ctx, tCtx, header.GetIsOptional(), subHeader)
			if err != nil {
				return err
			}
		}
		if isOpt && subIsOpt == 0 {
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数必填，其子参数必有至少一项必填", header.GetParamName()))
		}
	}
	return nil
}

// checkAPINodeHeaders API节点的Header信息判断
func checkAPINodeHeaders(ctx context.Context, tCtx *TreeContext, apiNode *KEP.APINodeData) error {
	sid := util.RequestID(ctx)
	maxCount := config.GetMainConfig().VerifyTaskFlow.APINodeHeadersMaxCount
	if len(apiNode.GetHeaders()) > maxCount {
		log.WarnContextf(ctx, "checkAPINodeHeaders|Header.len=%d|maxCount=%d", len(apiNode.GetHeaders()), maxCount)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点Header数量超过%d限制", maxCount))
	}

	// 产品结论：只判断 paramName的重复；
	// 同一个节点 参数名不能重复, 不同层级可以重复
	duplicateHeaderKey := make(map[string]struct{})
	for _, header := range apiNode.GetHeaders() {
		err := checkAPINodeHeadParamNameDuplicate(ctx, duplicateHeaderKey, header)
		if err != nil {
			return err
		}
		err = checkAPINodeHeadItem(ctx, tCtx, header.GetIsOptional(), header)
		if err != nil {
			log.WarnContextf(ctx, "sid:%s|checkAPINodeHeaders|err:%+v", sid, err)
			return err
		}
	}
	return nil
}

// checkAPINodeDataHeaderParam 校验HeaderParam
func checkAPINodeDataHeaderParam(ctx context.Context, tCtx *TreeContext, header *KEP.APINodeData_Header) error {
	if len(strings.TrimSpace(header.GetParamName())) == 0 {
		if header.GetIsOptional() {
			log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|IsOptional:%+v|ParamName is empty",
				header.GetIsOptional())
			return errors.TaskFLowVerifyError("header必填参数的参数名不能为空")
		} else {
			log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName is empty")
			return nil
		}
	}
	if !isValidJSONField(header.GetParamName()) {
		log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|isValidJSONField|ParamName:%s", header.GetParamName())
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s不支持的Header名", header.GetParamName()))
	}
	// 校验必要信息名称字数最大限制(不超过50字符)
	nameMaxLen := config.GetMainConfig().VerifyTaskFlow.RequiredInfoNameMax
	if len([]rune(header.GetParamName())) > nameMaxLen {
		log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName.len=%d|nameMaxLen=%d",
			len([]rune(header.GetParamName())), nameMaxLen)
		return errors.TaskFLowVerifyError(fmt.Sprintf("Header名超过最大限制%d", nameMaxLen))
	}
	paramType := strings.ToLower(header.GetParamType())
	if paramType != ParamTypeString && paramType != ParamTypeObject {
		log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName:%s|ParamType%s", header.GetParamName(),
			header.GetParamType())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点Header的%s类型错误，不支持%s", header.GetParamName(),
			header.GetParamType()))
	}
	//log.InfoContextf(ctx, "sid:%s|checkAPINodeDataHeaderParam|paramType:%s|GetSubHeader.Len:%d",
	//	sid, ParamTypeString, len(header.GetSubHeader()))
	if paramType == ParamTypeString && len(header.GetSubHeader()) > 0 {
		log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName:%s|ParamType%s can't has subHeader %+v",
			header.GetParamName(), header.GetParamType(), header.GetSubHeader())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点Header的%s类型为%s，不存在子参数%+v",
			header.GetParamName(), header.GetParamType(), header.GetSubHeader()))
	}
	if header.GetParamType() == ParamTypeObject {
		if len(header.GetSubHeader()) == 0 {
			log.WarnContextf(ctx, "ParamType:%s|SubHeaderLen:%d",
				header.GetParamType(), len(header.GetSubHeader()))
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数为%s类型，其子参数至少一项必填",
				header.GetParamName(), header.GetParamType()))
		}
		log.InfoContextf(ctx, "SourceType:%+v|%+v", header.GetSourceType(), KEP.APINodeData_Header_EMPTY)
		if len(header.GetSourceType().String()) > 0 && header.GetSourceType() != KEP.APINodeData_Header_EMPTY {
			log.WarnContextf(ctx, "ParamType:%s|SubHeadeLen:%d|SourceType:%+v|EMPTY:%+v",
				header.GetParamType(), len(header.GetSubHeader()), header.GetSourceType(), KEP.APINodeData_Header_EMPTY)
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s 参数为%s类型，其数据来源不可选",
				header.GetParamName(), header.GetParamType()))
		}
	}
	id := getHeaderID(header)
	if len(id) == 0 && paramType != ParamTypeObject {
		log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName:%s|ParamType:%s|value is empty",
			header.GetParamName(), header.GetParamType())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点输入参数%s的数据来源为空", header.GetParamName()))
	}
	switch header.GetSourceType() {
	case KEP.APINodeData_Header_API_RESP:
		// Header中使用的其他节点API的出参有效性（看是否在其他api节点的出参中）
		_, ok := tCtx.APIResponseParamTypes[id]
		if !ok {
			log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|ParamName:%s|API_RESP|ParamID:%s not exist",
				header.GetParamName(), id)
			return errors.TaskFLowVerifyError(fmt.Sprintf("API节点Header的%s的接口出参[%s]不存在", header.GetParamName(), id))
		}
	case KEP.APINodeData_Header_FIXED:
		fixedValueMaxCount := config.GetMainConfig().VerifyTaskFlow.APINodeFixedValueMaxCount
		if len([]rune(id)) > fixedValueMaxCount {
			log.WarnContextf(ctx, "checkAPINodeDataHeaderParam|fixedValue.len=%d|fixedValueMaxCount=%d",
				len([]rune(id)), fixedValueMaxCount)
			return errors.TaskFLowVerifyError(fmt.Sprintf("%s超过最大限制%d", header.GetParamName(),
				fixedValueMaxCount))
		}
	case KEP.APINodeData_Header_SYSTEM:
	}
	return nil
}

func checkRequestParamSlotAsk(ctx context.Context, param *KEP.APINodeData_RequestParam) error {
	switch param.GetSlotValueData().GetAskType() {
	case KEP.APINodeData_RequestParam_SlotValue_INPUT:
		if param.GetSlotValueData().GetCustomAsk() == "" {
			log.WarnContext(ctx, "checkRequestParamSlotAsk|CustomAsk is empty")
			return errors.TaskFLowVerifyError("API节点精准询问话术不能为空")
		}
		probeCustomMax := config.GetMainConfig().VerifyTaskFlow.RequiredProbeCustomMax
		askLen := len([]rune(RemoveRichText(param.GetSlotValueData().GetCustomAsk())))
		if askLen > probeCustomMax {
			log.WarnContextf(ctx, "checkRequestParamSlotAsk|askLen=%d|probeCustomMax=%d", askLen, probeCustomMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("精准询问话术超过最大限制%d", probeCustomMax))
		}
	case KEP.APINodeData_RequestParam_SlotValue_LLM:
		//ignore
	}
	return nil
}

// checkAPIReqSlotFormatDesc 检查APINode的入参实体格式
func checkAPIReqSlotFormatDesc(ctx context.Context, param *KEP.APINodeData_RequestParam) error {
	formatDesc := utf8.RuneCountInString(param.GetSlotValueData().GetFormatDesc())
	if formatDesc != 0 {
		formatDescTrim := strings.TrimSpace(param.GetSlotValueData().GetFormatDesc())
		if formatDescTrim == "" {
			log.WarnContext(ctx, "checkRequestParamSlot|FormatDesc is all space")
			return errors.TaskFLowVerifyError("API入参格式描述不能全是空格")
		}
		formatDescMax := config.GetMainConfig().VerifyTaskFlow.APIReqSlotFormatDescMax
		if formatDesc > formatDescMax {
			log.WarnContext(ctx, "checkRequestParamSlot|FormatDesc is too len")
			return errors.TaskFLowVerifyError(fmt.Sprintf("API入参格式描述超过最大限制%d", formatDescMax))
		}
	}
	return nil
}

// checkAPINodeDataRsp 检查APINode的 出参
func checkAPINodeResponse(ctx context.Context, apiNode *KEP.APINodeData) error {

	//	3. 校验API参数信息 参数：APIResponseParams 最多20个，最少0个
	apiRspParamsLen := len(apiNode.GetResponse())
	// API出参数
	apiRspMax := config.GetMainConfig().VerifyTaskFlow.APIRspParamsMax
	if apiRspParamsLen > apiRspMax {
		log.WarnContextf(ctx, "checkAPINodeResponse|apiRspParamsLen=%d|apiRspMax=%d", apiRspParamsLen, apiRspMax)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点参数信息数量超过%d", apiRspMax))
	}
	apiRspMin := config.GetMainConfig().VerifyTaskFlow.APIRspParamsMin
	if apiRspParamsLen < apiRspMin {
		log.WarnContextf(ctx, "checkAPINodeResponse|apiRspParamsLen=%d|apiRspMin=%d", apiRspParamsLen, apiRspMin)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点参数信息数量少于%d", apiRspMax))
	}

	// 产品结论：只判断 paramName的重复；
	// 同一个节点 参数名不能重复
	duplicateParamName := make(map[string]struct{})
	for _, param := range apiNode.GetResponse() {
		err := checkAPIResponseParams(ctx, param)
		if err != nil {
			return err
		}
		_, ok := duplicateParamName[param.GetParamName()]
		if ok {
			log.WarnContextf(ctx, "checkAPINodeResponse|duplicateParamName:%s", param.GetParamName())
			return errors.TaskFLowVerifyError("出参参数名重复:" + param.GetParamName())
		}
		duplicateParamName[param.GetParamName()] = struct{}{}
	}

	return nil

}

// getAPIResponseParamsDepth 获取API子参数的层级
func getAPIResponseParamsDepth(resParam *KEP.APINodeData_ResponseParam) int {
	var depth int
	if resParam == nil {
		return 0
	}
	if resParam.GetSubParams() == nil {
		return 1
	}
	depth += 1
	var subDepth int
	for _, sub := range resParam.GetSubParams() {
		temp := getAPIResponseParamsDepth(sub)
		if temp > subDepth {
			// 拿到最深的
			subDepth = temp
		}
	}
	depth += subDepth
	return depth
}

// checkAPISubResponseParams 校验API节点的出参
func checkAPISubResponseParamsLen(ctx context.Context, param *KEP.APINodeData_ResponseParam) error {
	// 节点子层级校验
	subResponses := param.GetSubParams()
	apiSubRspParamsLen := len(subResponses)
	apiSubRspMax := config.GetMainConfig().VerifyTaskFlow.APIRspSubParamMaxCount
	log.InfoContextf(ctx, "sid:%s|apiSubRspParamsLen:%d|apiSubRspMax:%d",
		util.RequestID(ctx), apiSubRspParamsLen, apiSubRspMax)
	if apiSubRspParamsLen > apiSubRspMax {
		log.WarnContextf(ctx, "checkAPINodeResponse|apiSubRspParamsLen=%d|apiSubRspMax=%d",
			apiSubRspParamsLen, apiSubRspMax)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点子出参参数信息数量超过%d", apiSubRspMax))
	}
	subDuplicateParamName := make(map[string]struct{})
	for _, subParam := range subResponses {
		// 子出参类型不能为ParamTypeArrayObject（array<object>）
		if subParam.GetParamType() == ParamTypeArrayObject {
			return errors.TaskFLowVerifyError("子出参类型不能为" + ParamTypeArrayObject + "类型")
		}
		err := checkAPIResponseParams(ctx, subParam)
		if err != nil {
			return err
		}
		_, ok := subDuplicateParamName[subParam.GetParamName()]
		if ok {
			log.WarnContextf(ctx, "checkAPINodeResponse|subDuplicateParamName:%s", subParam.GetParamName())
			return errors.TaskFLowVerifyError("API节点" + param.GetParamName() + "的子层级出参参数名重复:" + subParam.GetParamName())
		}
		subDuplicateParamName[subParam.GetParamName()] = struct{}{}
	}
	return nil
}

// checkAPIResponseParams 校验API参数信息
func checkAPIResponseParams(ctx context.Context, param *KEP.APINodeData_ResponseParam) error {
	if len(param.GetParamName()) == 0 {
		log.WarnContext(ctx, "checkAPIResponseParams|ParamName is empty")
		return errors.TaskFLowVerifyError("输出参数名不能为空")
	}
	if !isValidJSONField(param.GetParamName()) {
		log.WarnContextf(ctx, "checkAPIResponseParams|isValidJSONField|ParamName:%s", param.GetParamName())
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s不支持的参数名", param.GetParamName()))
	}
	switch strings.ToLower(param.GetParamType()) {
	case ParamTypeString, ParamTypeInt, ParamTypeFloat, ParamTypeBool, ParamTypeArray, ParamTypeArrayObject:
		break
	default:
		log.WarnContextf(ctx, "checkAPIResponseParams|ParamType%s", param.GetParamType())
		return errors.TaskFLowVerifyError("API节点参数类型错误")
	}

	if len(param.GetJSONPath()) == 0 && strings.ToLower(param.GetParamType()) != ParamTypeArrayObject {
		log.WarnContextf(ctx, "checkAPIResponseParams|ParamName:%s|JSONPath is empty", param.GetParamName())
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点输出参数%s解析路径必填", param.GetParamName()))
	}

	//获取参数层级
	depth := getAPIResponseParamsDepth(param)
	maxDepth := config.GetMainConfig().VerifyTaskFlow.APIRspParamMaxDepth
	log.InfoContextf(ctx, "sid:%s|depth:%d|maxDepth:%d", util.RequestID(ctx), depth, maxDepth)
	if depth > maxDepth {
		return errors.TaskFLowVerifyError(fmt.Sprintf("出参的层级限制不能超过%d层", maxDepth))
	}

	// 子出参数校验
	err := checkAPISubResponseParamsLen(ctx, param)
	if err != nil {
		return err
	}

	return nil
}

// checkAPIPath API节点APIPath校验
func checkAPIPath(ctx context.Context, apiPath *KEP.APINodeData_APIInfo, safeUrls []string) error {

	// 判断 API地址是否填写
	if len(strings.TrimSpace(apiPath.GetURL())) == 0 {
		log.WarnContext(ctx, "checkAPIPath|URL is empty")
		return errors.TaskFLowVerifyError("API地址不能为空")
	}

	// 请求API路径协议：仅限http、https,strings.ToLower
	decodeUrl, err := url.QueryUnescape(apiPath.GetURL())
	if err != nil {
		log.ErrorContextf(ctx, "checkAPIPath|URL解析错误：%s", err.Error())
		return errors.TaskFLowVerifyError("API节点：接口请求URL解析错误")
	}
	path := strings.TrimSpace(strings.ToLower(decodeUrl))
	log.InfoContextf(ctx, "checkAPIPath|path:%s", path)

	if len(path) == 0 {
		log.ErrorContextf(ctx, "checkAPIPath|path is empty")
		return errors.TaskFLowVerifyError("API地址不能为空")
	}

	maxPathTextLen := config.GetMainConfig().VerifyTaskFlow.APIPathTextMax
	if len([]rune(path)) > maxPathTextLen {
		log.WarnContextf(ctx, "checkAPINodeRequest|path.len=%d|maxCount=%d", len([]rune(path)), maxPathTextLen)
		return errors.TaskFLowVerifyError(fmt.Sprintf("API节点：接口请求路径长度超过最大长度%d限制", maxPathTextLen))
	}
	if !strings.Contains(path, APIProtocolHTTP) &&
		!strings.Contains(path, APIProtocolHTTPS) {
		log.WarnContext(ctx, "checkAPINodeRequest|path not contain http/https")
		return errors.TaskFLowVerifyError("API节点：接口请求协议需是https或http")
	}
	// 请求方法 POST,GET
	if strings.ToUpper(apiPath.GetMethod()) != APIMethodGet &&
		strings.ToUpper(apiPath.GetMethod()) != APIMethodPost {
		log.WarnContextf(ctx, "checkAPINodeRequest|method:%s", apiPath.GetMethod())
		return errors.TaskFLowVerifyError("API节点：接口请求类型需要：GET或POST方法")
	}

	// 安全校验
	configWhiteIps := config.GetMainConfig().ApiPathSafety.WhiteIp
	ips := make([]string, 0, len(configWhiteIps)+len(safeUrls))
	if len(configWhiteIps) > 0 {
		ips = append(ips, configWhiteIps...)
	}
	if len(safeUrls) > 0 {
		ips = append(ips, safeUrls...)
	}
	deny := checkSecurityIfDenyAccess(path, ips)
	log.InfoContextf(ctx, "checkAPIPath|IfDenyAccess.WhiteIp:%s|ips:%+v|%v", path, ips, deny)
	if deny {
		log.ErrorContextf(ctx, "checkAPIPath|apiPath.Path:%s IP, 需要加白名单", path)
		return errors.TaskFLowVerifyError(fmt.Sprintf("接口地址:%s 无法访问，请使用可访问的http、https公网地址", path))
	}

	return nil
}

// isValidJSONField API节点出入参中 JSON字段合法性的正则
func isValidJSONField(str string) bool {
	rex := config.GetMainConfig().VerifyTaskFlow.APINodeValidJSONFieldRex
	if len(rex) == 0 {
		// 允许数字开头
		// 因为标准的json中的key允许是数字开头的，再者说有些第三方API没准儿协议中的入参真有数字开头的，所以保留
		rex = `^[a-zA-Z0-9_]+$`
		// 不允许数字开头
		//rex = `^[a-zA-Z_]+[a-zA-Z0-9_]*$`
	}

	//for _, c := range str {
	//	// 是否在 ASCII 可打印字符范围内 https://ascii-code-table.nxm.ro/
	//	if c < 0x20 || c > 0x7E {
	//		return false
	//	}
	//}
	return regexp.MustCompile(rex).MatchString(str)
}

func getRequestID(r *KEP.APINodeData_RequestParam) string {
	switch r.GetSourceType() {
	case KEP.APINodeData_RequestParam_SLOT:
		return r.GetSlotValueData().GetSlotID()
	case KEP.APINodeData_RequestParam_API_RESP:
		return r.GetAPIRespValueData().GetParamID()
	case KEP.APINodeData_RequestParam_FIXED:
		return r.GetFixedValueData().GetValue()
	case KEP.APINodeData_RequestParam_SYSTEM:
		return r.GetSystemValueData().GetValue()
	case KEP.APINodeData_RequestParam_CUSTOM_VAR:
		return r.GetCustomVarValueData().GetValue()
	default:
		return ""
	}
}

func getHeaderID(r *KEP.APINodeData_Header) string {
	switch r.GetSourceType() {
	case KEP.APINodeData_Header_API_RESP:
		return r.GetAPIRespValueData().GetParamID()
	case KEP.APINodeData_Header_FIXED:
		return r.GetFixedValueData().GetValue()
	case KEP.APINodeData_Header_SYSTEM:
		return r.GetSystemValueData().GetValue()
	case KEP.APINodeData_Header_CUSTOM_VAR:
		return r.GetCustomVarValueData().GetValue()
	default:
		return ""
	}
}
