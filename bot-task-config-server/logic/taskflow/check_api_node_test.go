package taskflow

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func Test_checkAPINode(t *testing.T) {
	config.GetMainConfigForUnitTest().VerifyTaskFlow = config.VerifyTaskFlow{
		APINodeRequiredInfoMax:  20,
		RequiredInfoNameMax:     50,
		RequiredProbeCustomMax:  3000,
		RequiredProbePreviewMax: 3,
		APIPathTextMax:          500,
		APIRspParamsMax:         20,
	}
	type args struct {
		ctx  context.Context
		tCtx *TreeContext
		node *KEP.APINodeData
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "标准",
			wantErr: false,
			args: args{
				ctx: context.Background(),
				tCtx: &TreeContext{
					SlotIDs: map[string]string{
						"aa": "111",
					},
					APIResponseParamTypes: map[string]string{
						"output-1": "string",
					},
				},
				node: &KEP.APINodeData{
					API: &KEP.APINodeData_APIInfo{
						URL:    "https://www.5idhl.com/api/wiplus/base/onlineChat/getCountryGoodsList ",
						Method: "POST",
					},
					Request: []*KEP.APINodeData_RequestParam{
						{
							ParamID:    "api-request-param-id-1",
							ParamName:  "car_model",
							ParamType:  ParamTypeString,
							SourceType: KEP.APINodeData_RequestParam_SLOT,
							SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
								SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
									SlotID:    "slot-id-1",
									AskType:   KEP.APINodeData_RequestParam_SlotValue_LLM,
									CustomAsk: "",
								},
							},
							IsRequired: true,
						},
						{
							ParamID:    "api-1-input-2",
							ParamName:  "car_color",
							ParamType:  ParamTypeString,
							SourceType: KEP.APINodeData_RequestParam_SLOT,
							SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
								SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
									SlotID:    "slot-id-2  // 这个是通过单独的 slot（交互稿上是实体）相关的接口拿到的",
									AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
									CustomAsk: "请问您感兴趣的车身颜色是？我们目前提供红、蓝、黑、灰四种颜色。",
								},
							},
							IsRequired: true,
						},
						{
							ParamID:    "api-1-input-3",
							ParamName:  "resp_output_param_name_1",
							ParamType:  ParamTypeString,
							SourceType: KEP.APINodeData_RequestParam_API_RESP,
							SourceValue: &KEP.APINodeData_RequestParam_APIRespValueData{
								APIRespValueData: &KEP.APINodeData_RequestParam_APIRespValue{
									ParamID: "output-1",
								},
							},
							IsRequired: true,
						},
						{
							ParamID:    "api-1-input-4",
							ParamName:  "gudingzhi",
							ParamType:  ParamTypeFloat,
							SourceType: KEP.APINodeData_RequestParam_FIXED,
							SourceValue: &KEP.APINodeData_RequestParam_FixedValueData{
								FixedValueData: &KEP.APINodeData_RequestParam_FixedValue{
									Value: "// 这里是固定值示例",
								},
							},
							IsRequired: true,
						},
						{
							ParamID:    "api-1-input-5",
							ParamName:  "system_user_id",
							ParamType:  ParamTypeString,
							SourceType: KEP.APINodeData_RequestParam_SYSTEM,
							SourceValue: &KEP.APINodeData_RequestParam_SystemValueData{
								SystemValueData: &KEP.APINodeData_RequestParam_SystemValue{
									Value: "USER_ID",
								},
							},
							IsRequired: true,
						},
					},
					LLMAskPreview: []string{
						"请告诉我您感兴趣的车辆型号，方面为您报价",
					},
					Response: []*KEP.APINodeData_ResponseParam{
						{
							ParamID:    "output-id-1",
							ParamName:  "name",
							ParamType:  ParamTypeFloat,
							ParamTitle: "显示用的说明",
							JSONPath:   "$.store.book[0].title",
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkAPINode(tt.args.ctx, tt.args.tCtx, tt.args.node); (err != nil) != tt.wantErr {
				t.Errorf("checkAPINode() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
