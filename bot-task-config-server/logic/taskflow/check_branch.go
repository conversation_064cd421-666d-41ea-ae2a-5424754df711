package taskflow

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func checkBranches(ctx context.Context, tCtx *TreeContext, node *KEP.TaskFlowNode) error {
	// 校验结束节点的类型
	if len(node.GetBranches()) == 0 && node.GetNodeType() != KEP.NodeType_ANSWER {
		log.WarnContextf(ctx, "checkBranches|wrong end node|NodeType:%s", node.GetNodeType().String())
		return errors.TaskFLowVerifyError("结束节点的类型必需是答案节点")
	}

	maxBranchCount := config.GetMainConfig().VerifyTaskFlow.NodeBranchCountMax
	if len(node.GetBranches()) > maxBranchCount {
		log.WarnContextf(ctx, "checkBranches|branch.len:%d|maxBranchCount:%d", len(node.GetBranches()), maxBranchCount)
		return errors.TaskFLowVerifyError(fmt.Sprintf("节点超过最大节点分支数：%d", maxBranchCount))
	}

	for _, branch := range node.GetBranches() {
		log.InfoContextf(ctx, "checkBranches|branch:%s", json0.Marshal2StringNoErr(branch))
		err := checkBranchType(ctx, branch)
		if err != nil {
			log.WarnContextf(ctx, "checkBranches|checkBranchType|BranchID:%s|err:%+v", branch.GetBranchID(), err)
			return err
		}
		err = checkCondition(ctx, tCtx, branch, node, node.GetNodeType())
		if err != nil {
			log.WarnContextf(ctx, "checkBranches|checkCondition|BranchID:%s|err:%+v", branch.GetBranchID(), err)
			return err
		}
	}
	return nil
}

// checkBranchType 校验流转规则类型
func checkBranchType(ctx context.Context, br *KEP.Branch) error {
	log.InfoContextf(ctx, "checkBranchType|BranchID:%s|BranchType:%s|ConditionInfo=%t", br.GetBranchID(),
		br.GetBranchType().String(), br.GetConditionInfo() == nil)
	// 1. 校验流转规则类型
	switch br.GetBranchType() {
	case KEP.Branch_DIRECT:
		if br.GetConditionInfo().GetConditionsLogic() != KEP.ConditionInfo_UNSPECIFIED ||
			br.GetConditionInfo().GetCondition() != nil {
			log.WarnContextf(ctx, "checkBranchType|wrong branch type|BranchType:%s|ConditionInfo is not empty",
				br.GetBranchType())
			return errors.TaskFLowVerifyError("校验流转规则类型错误, DIRECT时条件应为空")
		}
	case KEP.Branch_CUSTOM:
		if br.GetConditionInfo().GetConditionsLogic() == KEP.ConditionInfo_UNSPECIFIED &&
			br.GetConditionInfo().GetCondition() == nil {
			log.WarnContextf(ctx, "checkBranchType|wrong branch type|BranchType:%s|ConditionInfo is nil",
				br.GetBranchType())
			return errors.TaskFLowVerifyError("校验流转规则类型错误, CUSTOM时条件不应为空")
		}
	default:
		log.WarnContextf(ctx, "checkBranchType|wrong branch type|BranchType:%s", br.GetBranchType().String())
		return errors.TaskFLowVerifyError(fmt.Sprintf("校验流转规则类型错误, 不支持%s", br.GetBranchType().String()))
	}

	// 2. 校验流转规则组合是否符合 And  Or
	// 调用检查函数
	depth := getConditionDepth(br.GetConditionInfo())
	maxDepth := config.GetMainConfig().VerifyTaskFlow.BranchConditionMaxDepth
	if depth > maxDepth {
		log.WarnContextf(ctx, "checkBranchType|depth:%d|maxDepth:%d", depth, maxDepth)
		return errors.TaskFLowVerifyError(fmt.Sprintf("节点跳转条件最多只支持%d层", maxDepth))
	}

	return nil
}

func checkConditionInfoValid(ctx context.Context, tCtx *TreeContext, node *KEP.TaskFlowNode,
	ci *KEP.ConditionInfo) error {
	if ci.GetConditionsLogic() == KEP.ConditionInfo_UNSPECIFIED && ci.GetCondition() != nil {
		return checkBranchCondition(ctx, tCtx, node, ci.GetCondition())
	}

	if ci.GetConditionsLogic() != KEP.ConditionInfo_UNSPECIFIED && len(ci.GetConditionInfo()) == 0 {
		return errors.TaskFLowVerifyError(fmt.Sprintf("配置了%s，但是ConditionInfo是空的", ci.GetConditionsLogic().String()))
	}

	for _, sub := range ci.GetConditionInfo() {
		err := checkConditionInfoValid(ctx, tCtx, node, sub)
		if err != nil {
			return err
		}
	}
	return nil
}

func checkBranchCondition(ctx context.Context, tCtx *TreeContext, node *KEP.TaskFlowNode,
	cc *KEP.ConditionInfo_BranchCondition) error {
	id := getConditionRefID(cc)
	if len(id) == 0 {
		log.WarnContextf(ctx, "checkBranchCondition|SourceType:%s|value is empty", cc.GetSourceType().String())
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s的值不应为空", cc.GetSourceType().String()))
	}

	if cc.GetSourceType() == KEP.ConditionInfo_BranchCondition_SLOT {
		// 1. 检查 slotID 是否在当前这棵树下用过了，在api节点和询问节点中；
		_, ok := tCtx.SlotIDs[id]
		if !ok {
			log.WarnContext(ctx, "checkBranchCondition|SLOT|SlotID:%s not exist", id)
			return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中引用了不存在的实体:%s", id))
		}
		// 2. slotID 有效性检查,因为 tCtx.SlotIDs 在前面做过检查，而在这里用的SlotID都是属于 tCtx.SlotIDs的，所以不需要在重复检查一次
	}
	return checkConditionComparison(ctx, tCtx, node, cc)
}

// 在条件分支可以支持的运算符
//
// 产品结论：
//
//  1. 槽位(收集实体)： 包含、不包含、等于、不等于、大于、小于、已填充、未填充、属于、不属于
//
//  2. API出
//     a. int：支持等于、大于、小于、已填充、未填充、不等于、属于、不属于
//     b. float：支持等于、大于、小于、已填充、未填充、不等于、属于、不属于
//     c. bool：支持等于、已填充、未填充、属于、不属于
//     d. string：支持等于、不等于、已填充、未填充、属于、不属于
//     e. array：包含、不包含、已填充、未填充、属于、不属于
//
//  3. 系统参数: 已填充、未填充
//
//  4. 意向判断： 只能是"等于"(前端置灰不可选），值只能是 true 或 false
//
// - 从 https://tapd.woa.com/70080800/prong/stories/view/1070080800115954801 整理

func checkConditionComparison(ctx context.Context, tCtx *TreeContext, node *KEP.TaskFlowNode,
	cc *KEP.ConditionInfo_BranchCondition) error {
	sid := util.RequestID(ctx)
	switch cc.GetComparison() {
	case KEP.ConditionInfo_BranchCondition_IN, KEP.ConditionInfo_BranchCondition_NOT_IN:
		// 手动输入 条件为属于时，输入数量为2000， 单个长度不超过50
		inValuesMax := config.GetMainConfig().VerifyTaskFlow.ConditionInValueMax
		valueSource := cc.GetInInputValueAddition().GetValueSource() // // 值来源：1: 词条； 2: 用户输入的词
		cValueSourceType := cc.GetComparisonValueSourceType()
		inputValueLen := len(cc.GetInputValues())
		if valueSource == entity.ValueSourceInput || cValueSourceType == entity.ValueSourceInput {
			log.InfoContextf(ctx, "sid:%s|checkConditionComparison|inputValueLen:%d|inValuesMax:%d", sid,
				inputValueLen, inValuesMax)
			if inputValueLen > inValuesMax {
				log.WarnContextf(ctx, "checkConditionComparison|InputValues.len:%d|containsValueMax:%d",
					inputValueLen, inValuesMax)
				return errors.TaskFLowVerifyError(fmt.Sprintf("流转规则组合条件为属于用户输入时，其值个数超过最大%d个限制", inValuesMax))
			}
			valueTextLen := config.GetMainConfig().Entry.MaxEntryNameLen
			for _, v := range cc.GetInputValues() {
				if len([]rune(v)) > valueTextLen {
					return errs.Newf(errors.ErrEntryNameTooLong, "流转规则组合条件为%s的用户输入值长度不超过%d个字符，请重新填写", cc.GetComparison(), valueTextLen)
				}
			}

			// 对用户输入自定义InputValues进行重复校验
			duplicateSlice := types.GetDuplicateStringFromSlice(cc.GetInputValues())
			if len(duplicateSlice) > 0 {
				duplicateStr := strings.Join(duplicateSlice, ",")
				log.WarnContextf(ctx, "checkConditionComparison|InputValues duplicate:%s", duplicateStr)
				return errors.TaskFLowVerifyError(fmt.Sprintf("条件分支属于，用户输入:%s 重复了", duplicateStr))
			}

		}
		// 如果valueSource为选择词条(ValueSourceEntry), 词条数量本身有限制为1000上线，这个地方就可以不用做校验
		if cc.GetSourceType() == KEP.ConditionInfo_BranchCondition_SLOT &&
			(valueSource == entity.ValueSourceEntry || cc.GetComparisonValueSourceType() == entity.ValueSourceEntry) &&
			!cc.GetInInputValueAddition().GetAllEntry() {
			selectEntryIds := cc.GetInInputValueAddition().GetEntryIDs()
			if len(selectEntryIds) == 0 {
				return errors.TaskFLowVerifyError("流转规则组合条件为属于且选择词条时，选择词条不为空")
			}
			// 词条的有效性检查
			if len(selectEntryIds) > 0 {
				for _, entryId := range selectEntryIds {
					_, ok := tCtx.EntryIDs[entryId]
					if !ok {
						log.WarnContext(ctx, "checkConditionComparison|SLOT|Entry:%s not exist", entryId)
						return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中属于或不属于引用了不存在的词条:%s", entryId))
					}
				}
			}

		}

	case KEP.ConditionInfo_BranchCondition_CONTAINS, KEP.ConditionInfo_BranchCondition_NOT_CONTAINS:
		// 如果组合条件是包含时，对参数值内容校验
		containsValueMax := config.GetMainConfig().VerifyTaskFlow.ConditionContainsValueMax
		containsValueMin := config.GetMainConfig().VerifyTaskFlow.ConditionContainsValueMin
		log.InfoContextf(ctx, "checkConditionComparison|len(cc.GetInputValues()):%d", len(cc.GetInputValues()))
		if len(cc.GetInputValues()) > containsValueMax {
			log.WarnContextf(ctx, "checkConditionComparison|InputValues.len:%d|containsValueMax:%d",
				len(cc.GetInputValues()), containsValueMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("流转规则组合条件为包含时，其值个数超过最大%d个限制", containsValueMax))
		}
		if len(cc.GetInputValues()) < containsValueMin {
			log.WarnContextf(ctx, "checkConditionComparison|InputValues.len:%d|containsValueMin:%d",
				len(cc.GetInputValues()), containsValueMin)
			return errors.TaskFLowVerifyError(fmt.Sprintf("流转规则组合条件为包含时，其值个数少于最少%d个限制", containsValueMin))
		}
	case
		KEP.ConditionInfo_BranchCondition_EQ, KEP.ConditionInfo_BranchCondition_NE,
		KEP.ConditionInfo_BranchCondition_LT, KEP.ConditionInfo_BranchCondition_LE,
		KEP.ConditionInfo_BranchCondition_GT, KEP.ConditionInfo_BranchCondition_GE:
		// 比较类的，不能没有值
		if len(cc.GetInputValues()) == 0 {
			log.WarnContextf(ctx, "checkConditionComparison|Comparison:%s|input value is empty", cc.GetComparison().String())
			return errors.TaskFLowVerifyError("%s时，值不能为空")
		}
	}

	var paramType string
	var ok bool

	switch cc.GetSourceType() {
	case KEP.ConditionInfo_BranchCondition_SLOT:
		tips, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_CONTAINS,
			KEP.ConditionInfo_BranchCondition_NOT_CONTAINS,
			KEP.ConditionInfo_BranchCondition_GT,
			KEP.ConditionInfo_BranchCondition_LT,
			KEP.ConditionInfo_BranchCondition_GE,
			KEP.ConditionInfo_BranchCondition_LE,
			KEP.ConditionInfo_BranchCondition_EQ,
			KEP.ConditionInfo_BranchCondition_NE,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN, // V2.4 新增
		})
		if !ok {
			log.WarnContextf(ctx, "checkConditionComparison|SourceType:%s not support %s",
				cc.GetSourceType().String(), cc.GetComparison().String())
			return errors.TaskFLowVerifyError(fmt.Sprintf("SLOT时不支持%s，只支持%s", cc.GetComparison().String(), tips))
		}
		// 判断收集实体中引用的接口出参数是否存在
		if cc.GetComparisonValueSourceType() == entity.ValueSourceResp {
			// 父参数
			id := cc.GetComparisonApiRespInfo().GetParamID()
			_, ok = tCtx.APIResponseParamTypes[id]
			if !ok {
				log.WarnContext(ctx, "checkConditionComparison|SLOT|API_RESP|ParamID:%s not exist", id)
				return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中收集实体引用了不存在的出参:%s", id))
			}
			// 判断子参数
			if len(cc.GetComparisonApiRespInfo().GetPartOfObjectArrayParamIDs()) > 0 {
				for _, subParamID := range cc.GetComparisonApiRespInfo().GetPartOfObjectArrayParamIDs() {
					_, ok = tCtx.APIResponseParamTypes[subParamID]
					if !ok {
						log.WarnContext(ctx, "checkConditionComparison|SLOT|API_RESP|ParamID:%s not exist", subParamID)
						return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中收集实体引用了不存在的出参:%s", subParamID))
					}
				}
			}
		}

	case KEP.ConditionInfo_BranchCondition_API_RESP:
		// 判断引用的接口出参是否存在
		id := cc.GetAPIRespValueData().GetParamID()
		paramType, ok = tCtx.APIResponseParamTypes[id]
		if !ok {
			log.WarnContext(ctx, "checkConditionComparison|API_RESP|ParamID:%s not exist", id)
			return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中引用了不存在的出参:%s", id))
		}

		// 判断 接口出参 中 引用的实体是否存在
		if cc.GetComparisonValueSourceType() == entity.ValueSourceEntity ||
			cc.GetComparisonValueSourceType() == entity.ValueSourceEntry {
			// 接口出参 引用实体， 不支持 包含 or 不包含； 只支持 属于/不属于（v2.4新增）
			// https://tapd.woa.com/project_qrobot/prong/tasks/view/1070080800075124080
			tips, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
				KEP.ConditionInfo_BranchCondition_IN,
				KEP.ConditionInfo_BranchCondition_NOT_IN, // V2.4 新增
			})
			if !ok {
				log.WarnContextf(ctx, "checkBranchCondition|API_RESP|SLOT|SourceType:%s not support %s",
					cc.GetSourceType().String(), cc.GetComparison().String())
				return errors.TaskFLowVerifyError(fmt.Sprintf("分支条件接口出参引用实体不支持%s，只支持%s",
					cc.GetComparison().String(), tips))
			}
			// 1. 检查 slotID 是否在当前这棵树下用过了，在api节点和询问节点中；
			slotId := cc.GetComparisonSlotInfo().GetSlotID()
			_, ok = tCtx.SlotIDs[slotId]
			if !ok {
				log.WarnContext(ctx, "checkBranchCondition|API_RESP|SLOT|SlotID:%s not exist", slotId)
				return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中接口出参引用了不存在的实体:%s", slotId))
			}

			// 词条的有效性检查
			if !cc.GetComparisonSlotInfo().GetAllEntry() {
				entryIds := cc.GetComparisonSlotInfo().GetEntryIDs()
				for _, entryId := range entryIds {
					_, ok := tCtx.EntryIDs[entryId]
					if !ok {
						log.WarnContext(ctx, "checkBranchCondition|API_RESP|SLOT|Entry:%s not exist", entryId)
						return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中接口出参引用了不存在的词条:%s", entryId))
					}
				}
			}
		}

		return checkAPIRespAllowComparison(ctx, paramType, cc.GetComparison())
	case KEP.ConditionInfo_BranchCondition_SYSTEM:
		tips, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
		})
		if !ok {
			log.WarnContextf(ctx, "checkConditionComparison|SourceType:%s not support %s",
				cc.GetSourceType().String(), cc.GetComparison().String())
			return errors.TaskFLowVerifyError(fmt.Sprintf("SYSTEM时不支持%s，只支持%s", cc.GetComparison(), tips))
		}
	case KEP.ConditionInfo_BranchCondition_PURPOSE:
		return checkBranchConditionPurpose(ctx, node, cc)

	case KEP.ConditionInfo_BranchCondition_CUSTOM_VAR:
		// 校验自定义参数的值 是否在 引用的 VarIDs 之中
		varParamId := cc.GetCustomVarValueData().GetValue()
		_, ok := tCtx.VarIDs[varParamId]
		if !ok {
			log.WarnContext(ctx, "checkConditionComparison|CUSTOM_VAR|varParamId:%s not exist", varParamId)
			return errors.TaskFLowVerifyError(fmt.Sprintf("在条件分支中引用了不存在的自定义变量Id:%s", varParamId))
		}

	}
	return checkConditionMatchType(ctx, cc)
}

// checkConditionMatchType 检查分支 匹配方式，
// v2.4 新增：https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800118359138?jump_count=1
func checkConditionMatchType(ctx context.Context, cc *KEP.ConditionInfo_BranchCondition) error {
	if cc.GetMatchType() == KEP.ConditionInfo_BranchCondition_PRECISE {
		// 全部默认为语义匹配
		//  收集实体 不在  包含/不包含， 等于/不等于， 属于/不属于 只默认支持语义匹配
		switch cc.GetSourceType() {

		case KEP.ConditionInfo_BranchCondition_SLOT:
			_, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
				KEP.ConditionInfo_BranchCondition_CONTAINS,
				KEP.ConditionInfo_BranchCondition_NOT_CONTAINS,
				KEP.ConditionInfo_BranchCondition_EQ,
				KEP.ConditionInfo_BranchCondition_NE,
				KEP.ConditionInfo_BranchCondition_IN,
				KEP.ConditionInfo_BranchCondition_NOT_IN, // V2.4 新增
			})

			if !ok {
				log.WarnContextf(ctx, "checkConditionMatchType|SourceType:%s not support %s",
					cc.GetSourceType().String(), cc.GetComparison().String())
				return errors.TaskFLowVerifyError(fmt.Sprintf("分支条件为收集实体，条件为 %s,不支持选择匹配方式", cc.GetComparison().String()))
			}
		case KEP.ConditionInfo_BranchCondition_API_RESP: // 接口出参
			// 接口出参 支持  等于/不等于， 属于/不属于
			_, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
				KEP.ConditionInfo_BranchCondition_EQ,
				KEP.ConditionInfo_BranchCondition_NE,
				KEP.ConditionInfo_BranchCondition_IN,
				KEP.ConditionInfo_BranchCondition_NOT_IN, // V2.4 新增
			})
			if !ok {
				log.WarnContextf(ctx, "checkConditionMatchType|SourceType:%s not support %s",
					cc.GetSourceType().String(), cc.GetComparison().String())
				return errors.TaskFLowVerifyError(fmt.Sprintf("分支条件为接口出参 条件为 %s，不支持选择匹配方式",
					cc.GetComparison().String()))
			}
		}
	}
	return nil
}

func checkBranchConditionPurpose(ctx context.Context, node *KEP.TaskFlowNode,
	cc *KEP.ConditionInfo_BranchCondition) error {

	if node.GetNodeType() != KEP.NodeType_REQUEST ||
		node.GetRequestNodeData().GetRequest().GetRequestType() != KEP.RequestNodeData_RequestInfo_PURPOSE {
		log.WarnContextf(ctx, "checkBranchConditionPurpose|NodeType:%s|RequestType:%s",
			node.GetNodeType(), node.GetRequestNodeData().GetRequest().GetRequestType())
		return errors.TaskFLowVerifyError("只能在有意向判断的询问节点中直接连线意向判断的条件")
	}

	tips, ok := checkSupported(cc.GetComparison(), []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
		KEP.ConditionInfo_BranchCondition_EQ,
	})
	for _, v := range cc.GetInputValues() {
		if v == "true" || v == "false" {
			continue
		}
		return errors.TaskFLowVerifyError(fmt.Sprintf("意向判断时InputValues只能为:true, false. %s", v))
	}
	if !ok {
		log.WarnContextf(ctx, "checkBranchConditionPurpose|SourceType:%s not support %s",
			cc.GetSourceType().String(), cc.GetComparison().String())
		return errors.TaskFLowVerifyError(fmt.Sprintf("意向判断时不支持%s，只支持%s", cc.GetComparison(), tips))
	}

	// 检查语义匹配

	return nil
}

func checkAPIRespAllowComparison(ctx context.Context, paramType string,
	dest KEP.ConditionInfo_BranchCondition_ComparisonEnum) error {
	var supported []KEP.ConditionInfo_BranchCondition_ComparisonEnum
	switch paramType {
	case ParamTypeInt:
		supported = []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_GT,
			KEP.ConditionInfo_BranchCondition_LT,
			KEP.ConditionInfo_BranchCondition_GE,
			KEP.ConditionInfo_BranchCondition_LE,
			KEP.ConditionInfo_BranchCondition_EQ,
			KEP.ConditionInfo_BranchCondition_NE,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN,
		}
	case ParamTypeFloat:
		supported = []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_GT,
			KEP.ConditionInfo_BranchCondition_LT,
			KEP.ConditionInfo_BranchCondition_GE,
			KEP.ConditionInfo_BranchCondition_LE,
			KEP.ConditionInfo_BranchCondition_EQ,
			KEP.ConditionInfo_BranchCondition_NE,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN,
		}
	case ParamTypeBool:
		supported = []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_EQ,
			KEP.ConditionInfo_BranchCondition_NE,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN,
		}
	case ParamTypeString:
		supported = []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_EQ,
			KEP.ConditionInfo_BranchCondition_NE,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN,
		}
	case ParamTypeArray:
		supported = []KEP.ConditionInfo_BranchCondition_ComparisonEnum{
			KEP.ConditionInfo_BranchCondition_CONTAINS,
			KEP.ConditionInfo_BranchCondition_NOT_CONTAINS,
			KEP.ConditionInfo_BranchCondition_IS_SET,
			KEP.ConditionInfo_BranchCondition_NOT_SET,
			KEP.ConditionInfo_BranchCondition_IN,
			KEP.ConditionInfo_BranchCondition_NOT_IN,
		}
	default:
		// 条件分支内不可选择结构体类型(array<object>)的出参
		log.WarnContextf(ctx, "checkAPIRespAllowComparison|not support %s", paramType)
		return errors.TaskFLowVerifyError(fmt.Sprintf("[%s]不支持的参数类型", paramType))
	}

	tips, ok := checkSupported(dest, supported)
	if ok {
		return nil
	}
	log.WarnContextf(ctx, "checkAPIRespAllowComparison|%s not supported|ParamType:%s", dest.String(), paramType)
	return errors.TaskFLowVerifyError(fmt.Sprintf("%s不支持在参数类型为%s时使用，只支持%s", dest.String(), paramType, tips))
}

func checkSupported(dest KEP.ConditionInfo_BranchCondition_ComparisonEnum,
	supported []KEP.ConditionInfo_BranchCondition_ComparisonEnum) (string, bool) {
	for _, s := range supported {
		if s == dest {
			// 合法
			return "", true
		}
	}
	var tips string
	for i, s := range supported {
		if i == 0 {
			tips = s.String()
		} else {
			tips += "," + s.String()
		}
	}
	return tips, false
}

// checkCondition 流转规则校验
func checkCondition(ctx context.Context, tCtx *TreeContext, br *KEP.Branch, node *KEP.TaskFlowNode,
	nodeType KEP.NodeType) error {

	if nodeType != KEP.NodeType_START && br.GetBranchType() != KEP.Branch_DIRECT {
		// 节点跳转组合条件个数，最多10个，最少1个
		count := getConditionCount(br.GetConditionInfo())
		conditionMax := config.GetMainConfig().VerifyTaskFlow.ConditionCountMax
		if count > conditionMax {
			log.WarnContextf(ctx, "checkCondition|count:%d|conditionMax:%d", count, conditionMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("节点自定义跳转分支条件数量超过最大%d", conditionMax))
		}
		conditionMin := config.GetMainConfig().VerifyTaskFlow.ConditionCountMin
		if count < conditionMin {
			log.WarnContextf(ctx, "checkCondition|count:%d|conditionMin:%d", count, conditionMin)
			return errors.TaskFLowVerifyError(fmt.Sprintf("节点自定义跳转分支数量少于%d个限制", conditionMin))
		}
	}

	return checkConditionInfoValid(ctx, tCtx, node, br.GetConditionInfo())
}

func getConditionCount(info *KEP.ConditionInfo) int {
	if info.GetConditionsLogic() == KEP.ConditionInfo_UNSPECIFIED && info.GetCondition() != nil {
		return 1
	}
	totalCount := 0
	for _, sub := range info.GetConditionInfo() {
		count := getConditionCount(sub)
		totalCount += count
	}
	return totalCount
}

func getConditionDepth(info *KEP.ConditionInfo) int {
	var depth int
	if info.GetCondition() != nil {
		return 1
	}

	var subDepth int
	for _, sub := range info.GetConditionInfo() {
		if info.GetConditionsLogic() != KEP.ConditionInfo_UNSPECIFIED && len(sub.GetConditionInfo()) > 0 {
			temp := getConditionDepth(sub)
			if temp > subDepth {
				// 拿到最深的
				subDepth = temp
			}
		} else if sub.GetCondition() != nil {
			depth = 1
		}
	}
	depth += subDepth
	return depth
}

func getConditionRefID(r *KEP.ConditionInfo_BranchCondition) string {
	switch r.GetSourceType() {
	case KEP.ConditionInfo_BranchCondition_SLOT:
		return r.GetSlotValueData().GetSlotID()
	case KEP.ConditionInfo_BranchCondition_API_RESP:
		return r.GetAPIRespValueData().GetParamID()
	case KEP.ConditionInfo_BranchCondition_SYSTEM:
		return r.GetSystemValueData().GetValue()
	case KEP.ConditionInfo_BranchCondition_PURPOSE:
		return r.GetPurposeValueData().GetValue()
	case KEP.ConditionInfo_BranchCondition_CUSTOM_VAR:
		return r.GetCustomVarValueData().GetValue()
	default:
		return ""
	}
}
