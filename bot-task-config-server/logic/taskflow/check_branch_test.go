package taskflow

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func Test_getConditionCount(t *testing.T) {
	type args struct {
		info *KEP.ConditionInfo
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "个数-1",
			want: 1,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
								SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
									SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
										SlotID: "slot-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_NE,
								InputValues: []string{"aabb"},
							},
						},
					},
				},
			},
		},
		{
			name: "个数-1-兼容的数据-带ConditionsLogic的话，必须要有ConditionInfo，并且Condition是空的",
			want: 1,
			args: args{
				info: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
						SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
							SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
								SlotID: "slot-1",
							},
						},
						Comparison:  KEP.ConditionInfo_BranchCondition_NE,
						InputValues: []string{"aabb"},
					},
				},
			},
		},
		{
			name: "个数-2",
			want: 2,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
					},
				},
			},
		},
		{
			name: "个数-4",
			want: 4,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
								SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
									SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
										SlotID: "slot-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_NE,
								InputValues: []string{"aabb"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
								SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
									APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
										ParamID: "output-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_LT,
								InputValues: []string{"10"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-2",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
										InputValues: nil,
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_SYSTEM,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-1",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
										InputValues: []string{"10"},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "个数-2-OR里有单个",
			want: 2,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
								SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
									SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
										SlotID: "slot-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_NE,
								InputValues: []string{"aabb"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-2",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
										InputValues: nil,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getConditionCount(tt.args.info); got != tt.want {
				t.Errorf("getConditionCount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getConditionDepth(t *testing.T) {
	type args struct {
		info *KEP.ConditionInfo
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "深度-0",
			want: 0,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo:   []*KEP.ConditionInfo{},
				},
			},
		},
		{
			name: "深度-1",
			want: 1,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
								InputValues: []string{"省略"},
							},
						},
					},
				},
			},
		},
		{
			name: "深度-1- 一层",
			want: 1,
			args: args{
				info: &KEP.ConditionInfo{
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
						SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
							SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
								SlotID: "slot-1",
							},
						},
						Comparison:  KEP.ConditionInfo_BranchCondition_NE,
						InputValues: []string{"aabb"},
					},
				},
			},
		},
		{
			name: "深度-2",
			want: 2,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
								InputValues: []string{"省略"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
										InputValues: []string{"省略"},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "深度-2-多个2层",
			want: 2,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
								InputValues: []string{"省略"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
										InputValues: []string{"省略"},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
										InputValues: []string{"省略"},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "深度-3-多个2层",
			want: 3,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
								InputValues: []string{"省略"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									ConditionsLogic: KEP.ConditionInfo_OR,
									ConditionInfo: []*KEP.ConditionInfo{
										{
											Condition: &KEP.ConditionInfo_BranchCondition{
												SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
												InputValues: []string{"省略"},
											},
										},
										{
											Condition: &KEP.ConditionInfo_BranchCondition{
												SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
												InputValues: []string{"省略"},
											},
										},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
										InputValues: []string{"省略"},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "深度-3",
			want: 3,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_SLOT,
								InputValues: []string{"省略"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
								InputValues: []string{"省略"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
										InputValues: []string{"省略"},
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
										InputValues: []string{"省略"},
									},
								},
								{
									ConditionsLogic: KEP.ConditionInfo_AND,
									ConditionInfo: []*KEP.ConditionInfo{
										{
											Condition: &KEP.ConditionInfo_BranchCondition{
												SourceType:  KEP.ConditionInfo_BranchCondition_API_RESP,
												InputValues: []string{"省略"},
											},
										},
										{
											Condition: &KEP.ConditionInfo_BranchCondition{
												SourceType:  KEP.ConditionInfo_BranchCondition_SYSTEM,
												InputValues: []string{"省略"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "个数-2-OR里有单个",
			want: 2,
			args: args{
				info: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
								SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
									SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
										SlotID: "slot-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_NE,
								InputValues: []string{"aabb"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-2",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
										InputValues: nil,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getConditionDepth(tt.args.info); got != tt.want {
				t.Errorf("getConditionDepth() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checkBranches(t *testing.T) {
	type args struct {
		ctx  context.Context
		tCtx *TreeContext
		node *KEP.TaskFlowNode
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "标准",
			wantErr: false,
			args: args{
				ctx: trpc.BackgroundContext(),
				tCtx: &TreeContext{
					SlotIDs: map[string]string{
						"slot-id-checkBranches-1": "aa",
					},
					APIResponseParamTypes: map[string]string{
						"output-id-checkBranches-1": "int",
						"output-id-checkBranches-2": "string",
					},
				},
				node: &KEP.TaskFlowNode{
					NodeType: KEP.NodeType_API,
					Branches: []*KEP.Branch{
						{
							BranchID:   "branch-id-2 ",
							BranchType: KEP.Branch_CUSTOM,
							ConditionInfo: &KEP.ConditionInfo{
								ConditionsLogic: KEP.ConditionInfo_AND,
								ConditionInfo: []*KEP.ConditionInfo{
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
											SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
												SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
													SlotID: "slot-id-checkBranches-1",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_NE,
											InputValues: []string{"aabb"},
										},
									},
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
											SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
												APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
													ParamID: "output-id-checkBranches-1",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_LT,
											InputValues: []string{"10"},
										},
									},
									{
										ConditionsLogic: KEP.ConditionInfo_OR,
										ConditionInfo: []*KEP.ConditionInfo{
											{
												Condition: &KEP.ConditionInfo_BranchCondition{
													SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
													SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
														APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
															ParamID: "output-id-checkBranches-2",
														},
													},
													Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
													InputValues: []string{"10"},
												},
											},
											{
												Condition: &KEP.ConditionInfo_BranchCondition{
													SourceType: KEP.ConditionInfo_BranchCondition_SYSTEM,
													SourceValue: &KEP.ConditionInfo_BranchCondition_SystemValueData{
														SystemValueData: &KEP.ConditionInfo_BranchCondition_SystemValue{
															Value: "aaa",
														},
													},
													Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
													InputValues: []string{"10"},
												},
											},
										},
									},
								},
							},
							NextNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
							PrevNodeID: "77220626-5adc-786a-e3f6-a991545728d3",
						},
					},
				},
			},
		},
		{
			name:    "错误-三个层级",
			wantErr: true,
			args: args{
				ctx: trpc.BackgroundContext(),
				tCtx: &TreeContext{
					SlotIDs: map[string]string{
						"slot-id-checkBranches-1": "aa",
					},
					APIResponseParamTypes: map[string]string{
						"output-id-checkBranches-1": "int",
						"output-id-checkBranches-2": "string",
					},
				},
				node: &KEP.TaskFlowNode{
					NodeType: KEP.NodeType_API,
					Branches: []*KEP.Branch{
						{
							BranchID:   "branch-id-2 ",
							BranchType: KEP.Branch_CUSTOM,
							ConditionInfo: &KEP.ConditionInfo{
								ConditionsLogic: KEP.ConditionInfo_AND,
								ConditionInfo: []*KEP.ConditionInfo{
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
											SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
												SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
													SlotID: "slot-id-checkBranches-1",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_NE,
											InputValues: []string{"aabb"},
										},
									},
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
											SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
												APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
													ParamID: "output-id-checkBranches-1",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_LT,
											InputValues: []string{"10"},
										},
									},
									{
										ConditionsLogic: KEP.ConditionInfo_OR,
										ConditionInfo: []*KEP.ConditionInfo{
											{
												Condition: &KEP.ConditionInfo_BranchCondition{
													SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
													SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
														APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
															ParamID: "output-id-checkBranches-2",
														},
													},
													Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
													InputValues: []string{"10"},
												},
											},
											{
												Condition: &KEP.ConditionInfo_BranchCondition{
													SourceType: KEP.ConditionInfo_BranchCondition_SYSTEM,
													SourceValue: &KEP.ConditionInfo_BranchCondition_SystemValueData{
														SystemValueData: &KEP.ConditionInfo_BranchCondition_SystemValue{
															Value: "aaa",
														},
													},
													Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
													InputValues: []string{"10"},
												},
											},
											{
												ConditionsLogic: KEP.ConditionInfo_OR,
												ConditionInfo: []*KEP.ConditionInfo{
													{
														Condition: &KEP.ConditionInfo_BranchCondition{
															SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
															SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
																APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
																	ParamID: "output-id-checkBranches-2",
																},
															},
															Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
															InputValues: []string{"10"},
														},
													},
													{
														Condition: &KEP.ConditionInfo_BranchCondition{
															SourceType: KEP.ConditionInfo_BranchCondition_SYSTEM,
															SourceValue: &KEP.ConditionInfo_BranchCondition_SystemValueData{
																SystemValueData: &KEP.ConditionInfo_BranchCondition_SystemValue{
																	Value: "aaa",
																},
															},
															Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
															InputValues: []string{"10"},
														},
													},
												},
											},
										},
									},
								},
							},
							NextNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
							PrevNodeID: "77220626-5adc-786a-e3f6-a991545728d3",
						},
					},
				},
			},
		},
		{
			name:    "错误-配置了AND 或 OR，但是没有条件",
			wantErr: true,
			args: args{
				ctx: trpc.BackgroundContext(),
				tCtx: &TreeContext{
					SlotIDs: map[string]string{
						"slot-id-checkBranches-1": "aa",
					},
					APIResponseParamTypes: map[string]string{
						"output-id-checkBranches-1": "int",
						"output-id-checkBranches-2": "string",
					},
				},
				node: &KEP.TaskFlowNode{
					NodeType: KEP.NodeType_API,
					Branches: []*KEP.Branch{
						{
							BranchID:   "branch-id-2 ",
							BranchType: KEP.Branch_CUSTOM,
							ConditionInfo: &KEP.ConditionInfo{
								ConditionsLogic: KEP.ConditionInfo_AND,
								ConditionInfo:   nil,
							},
						},
					},
				},
			},
		},
	}

	config.GetMainConfigForUnitTest().VerifyTaskFlow = config.VerifyTaskFlow{
		NodeBranchCountMax:        20,
		ConditionContainsValueMax: 100,
		ConditionCountMax:         10,
		BranchConditionMaxDepth:   2,
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkBranches(tt.args.ctx, tt.args.tCtx, tt.args.node); (err != nil) != tt.wantErr {
				t.Errorf("checkBranches() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
