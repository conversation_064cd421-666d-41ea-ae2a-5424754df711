package taskflow

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// checkRequestNode 询问节点的数据校验
func checkRequestNode(ctx context.Context, tCtx *TreeContext, node *KEP.RequestNodeData) error {
	if node == nil {
		log.ErrorContextf(ctx, "RequestNodeData is nil")
		return errors.TaskFLowVerifyError("询问节点解析错误")
	}
	// 1. 校验询问节点的必要信息
	if err := checkRequest(ctx, tCtx, node.GetRequest()); err != nil {
		return err
	}
	// 2. 检查选项卡
	return checkCard(ctx, tCtx, node)
}

func checkRequest(ctx context.Context, tCtx *TreeContext, request *KEP.RequestNodeData_RequestInfo) error {
	switch request.GetRequestType() {
	case KEP.RequestNodeData_RequestInfo_SLOT:
		return checkSlotRequest(ctx, tCtx, request)
	case KEP.RequestNodeData_RequestInfo_PURPOSE:
		return checkPurposeRequest(ctx, tCtx, request)
	default:
		log.WarnContextf(ctx, "checkRequest|[%s]|wrong RequestType", request.GetRequestType().String())
		return nil
	}
}

func checkPurposeRequest(ctx context.Context, tCtx *TreeContext, request *KEP.RequestNodeData_RequestInfo) error {
	if request.GetRequestType() != KEP.RequestNodeData_RequestInfo_PURPOSE &&
		request.GetAskType() != KEP.RequestNodeData_RequestInfo_INPUT {
		log.WarnContext(ctx, "checkPurposeRequest|wrong AskType|[%s]", request.GetAskType())
		return nil
	}
	if len(request.GetCustomAsk()) == 0 {
		log.WarnContext(ctx, "checkPurposeRequest|CustomAsk is EMPTY.")
		return errors.TaskFLowVerifyError("信息澄清节点-判断意图的询问话术不能为空")
	}
	probeCustomMax := config.GetMainConfig().VerifyTaskFlow.RequiredProbeCustomMax
	inputTextLen := len([]rune(RemoveRichText(request.GetCustomAsk())))
	if inputTextLen > probeCustomMax {
		log.WarnContextf(ctx, "checkPurposeRequest|inputTextLen=%d|probeCustomMax=%d", inputTextLen, probeCustomMax)
		return errors.TaskFLowVerifyError(fmt.Sprintf("信息澄清节点-判断意图的询问话术超过最大限制%d", probeCustomMax))
	}
	return nil
}

func checkSlotRequest(ctx context.Context, tCtx *TreeContext, request *KEP.RequestNodeData_RequestInfo) error {
	if request.GetRequestType() != KEP.RequestNodeData_RequestInfo_SLOT {
		log.WarnContextf(ctx, "checkSlotRequest|requestType=%s", request.GetRequestType())
		return nil
	}

	nameMaxLen := config.GetMainConfig().VerifyTaskFlow.RequiredInfoNameMax
	// 校验必要信息名称字数最大限制(不超过50字符)
	requestValueLength := len([]rune(request.GetRequestValue()))
	if requestValueLength > nameMaxLen {
		log.WarnContextf(ctx, "checkSlotRequest|requestValueLength=%d|nameMaxLen=%d", requestValueLength, nameMaxLen)
		return errors.TaskFLowVerifyError(fmt.Sprintf("必要信息超过最大限制%d", nameMaxLen))
	}

	// slot有效性在 checkSlot 统一检查，这里不用再检查一次了

	switch request.GetAskType() {
	case KEP.RequestNodeData_RequestInfo_INPUT:
		if len(request.GetCustomAsk()) == 0 {
			log.WarnContext(ctx, "checkSlotRequest|CustomAsk is EMPTY.")
			return errors.TaskFLowVerifyError("信息澄清节点询问话术为自定义时，自定义话术不能为空")
		}
		probeCustomMax := config.GetMainConfig().VerifyTaskFlow.RequiredProbeCustomMax
		inputTextLen := len([]rune(RemoveRichText(request.GetCustomAsk())))
		if inputTextLen > probeCustomMax {
			log.WarnContextf(ctx, "checkSlotRequest|inputTextLen=%d|probeCustomMax=%d", inputTextLen, probeCustomMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("自定义话术超过最大限制%d", probeCustomMax))
		}
	case KEP.RequestNodeData_RequestInfo_LLM:
		llmAskPreviewMaxItems := config.GetMainConfig().VerifyTaskFlow.RequiredProbePreviewMax
		if len(request.GetLLMAskPreview()) > llmAskPreviewMaxItems {
			log.WarnContextf(ctx, "checkSlotRequest|LLMAskPreviewItems=%d|llmAskPreviewMaxItems=%d",
				len(request.GetLLMAskPreview()), llmAskPreviewMaxItems)
			return errors.TaskFLowVerifyError(fmt.Sprintf("追问语句超过最大限制%d", llmAskPreviewMaxItems))
		}
	}

	return nil
}

func checkCard(ctx context.Context, tCtx *TreeContext, node *KEP.RequestNodeData) error {
	if node.GetRequest().GetRequestType() == KEP.RequestNodeData_RequestInfo_PURPOSE {
		log.WarnContext(ctx, "checkCard|PURPOSE|no card")
		return nil
	}
	// 选项卡启用的判断
	if !node.GetEnableCard() {
		log.InfoContextf(ctx, "checkCard|no card")
		return nil
	}

	switch node.GetCardFrom() {
	case KEP.RequestNodeData_API:
		if len(node.GetApiCardRef().GetParamID()) == 0 {
			log.WarnContext(ctx, "checkCard|API|ParamID is empty")
			return errors.TaskFLowVerifyError("选项卡API方式，ParamID不能为空")
		}
		// 输入参数中使用的其他节点API的出参有效性（看是否在其他api节点的出参中）
		paramType, ok := tCtx.APIResponseParamTypes[node.GetApiCardRef().GetParamID()]
		if !ok {
			log.WarnContext(ctx, "checkCard|API_RESP|ParamID:%s not exist", node.GetApiCardRef().GetParamID())
			return errors.TaskFLowVerifyError(fmt.Sprintf("选项卡API方式，ParamID:%s出参不存在",
				node.GetApiCardRef().GetParamID()))
		}
		// 当选项卡参数类型为array<object>时，ApiRespCardRefInfo下的	AllObjectArrayParams 和 PartOfObjectArrayParamIDs才用到
		if paramType != ParamTypeArrayObject && (node.GetApiCardRef().GetAllObjectArrayParams() ||
			len(node.GetApiCardRef().GetPartOfObjectArrayParamIDs()) > 0) {
			log.WarnContext(ctx, "checkCard|API_RESP|ParamID:%s not exist", node.GetApiCardRef().GetParamID())
			return errors.TaskFLowVerifyError(fmt.Sprintf("选项卡API方式，ParamID:%s出参为%s类型,不存在子参数",
				node.GetApiCardRef().GetParamID(), paramType))
		}
		if len(node.GetApiCardRef().GetPartOfObjectArrayParamIDs()) > 0 {
			// 输入子参数中使用的其他节点API的出参有效性（看是否在其他api节点的出参中）
			for _, subParamId := range node.GetApiCardRef().GetPartOfObjectArrayParamIDs() {
				_, ok := tCtx.APIResponseParamTypes[subParamId]
				if !ok {
					log.WarnContext(ctx, "checkCard|API_RESP|ParamID:%s not exist", subParamId)
					return errors.TaskFLowVerifyError(fmt.Sprintf("选项卡API方式，子参数ParamID:%s出参不存在", subParamId))
				}
			}
		}
	case KEP.RequestNodeData_INPUT:
		contentList := node.GetInputCard().GetInputCardContent()
		if len(contentList) == 0 {
			log.WarnContext(ctx, "checkCard|INPUT|Content is empty")
			return errors.TaskFLowVerifyError("选项卡自定义方式，配置选项不能为空")
		}

		// 检查个数
		optMax := config.GetMainConfig().VerifyTaskFlow.OptionContentsMax
		if len(contentList) > optMax {
			log.WarnContextf(ctx, "checkCard|INPUT|contentList:%d|optMax:%d", len(contentList), optMax)
			return errors.TaskFLowVerifyError(fmt.Sprintf("询问节点选项配置超过最大限制:%d", optMax))
		}
		optMin := config.GetMainConfig().VerifyTaskFlow.OptionContentsMin
		if len(contentList) < optMin {
			log.WarnContextf(ctx, "checkCard|INPUT|contentList:%d|optMin:%d", len(contentList), optMin)
			return errors.TaskFLowVerifyError(fmt.Sprintf("询问节点选项配置不能小于:%d", optMin))
		}

		// 对内容长度进行校验
		for _, value := range contentList {
			optTextMax := config.GetMainConfig().VerifyTaskFlow.OptionContentsTextMax
			if len([]rune(value)) > optTextMax {
				log.WarnContextf(ctx, "checkCard|INPUT|value.len:%d|optTextMax:%d", len([]rune(value)), optTextMax)
				return errors.TaskFLowVerifyError(fmt.Sprintf("询问节点选项配置[%s]超过最大长度%d", value, optTextMax))
			}
		}
	}
	return nil
}
