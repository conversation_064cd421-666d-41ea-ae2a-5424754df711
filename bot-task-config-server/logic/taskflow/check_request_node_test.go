package taskflow

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"testing"
)

func Test_checkRequestNode(t *testing.T) {

	config.GetMainConfigForUnitTest().VerifyTaskFlow = config.VerifyTaskFlow{
		APINodeRequiredInfoMax:  20,
		RequiredInfoNameMax:     50,
		RequiredProbeCustomMax:  3000,
		RequiredProbePreviewMax: 3,
		APIPathTextMax:          500,
		APIRspParamsMax:         20,
		NodeBranchCountMax:      10,
		BranchConditionMaxDepth: 2,
		ConditionCountMax:       10,
	}

	type args struct {
		ctx  context.Context
		tCtx *TreeContext
		node *KEP.TaskFlowNode
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "正确的",
			wantErr: false,
			args: args{
				ctx: trpc.BackgroundContext(),
				tCtx: &TreeContext{
					SlotIDs:               map[string]string{},
					APIResponseParamTypes: map[string]string{},
				},
				node: &KEP.TaskFlowNode{
					NodeID:   "sssbbbbb",
					NodeName: "意向判断的节点",
					NodeType: KEP.NodeType_REQUEST,
					NodeData: &KEP.TaskFlowNode_RequestNodeData{
						RequestNodeData: &KEP.RequestNodeData{
							Request: &KEP.RequestNodeData_RequestInfo{
								ID:          "req-id-purpos-1",
								RequestType: KEP.RequestNodeData_RequestInfo_PURPOSE,
								CustomAsk:   "意向判断的问题",
								IsRequired:  true,
							},
						},
					},
					Branches: []*KEP.Branch{
						{
							BranchID:   "branch-id-4",
							BranchType: KEP.Branch_CUSTOM,
							ConditionInfo: &KEP.ConditionInfo{
								ConditionsLogic: KEP.ConditionInfo_AND,
								ConditionInfo: []*KEP.ConditionInfo{
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_PURPOSE,
											SourceValue: &KEP.ConditionInfo_BranchCondition_PurposeValueData{
												PurposeValueData: &KEP.ConditionInfo_BranchCondition_PurposeValue{
													Value: "LastAcceptance",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_EQ,
											InputValues: []string{"true"},
										},
									},
								},
							},
							NextNodeID: "f8549ed6-8638-b002-e427-0c5226e8eb45",
							PrevNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
						},
					},
				},
			},
		},
		{
			name:    "不是意向判断的节点中不能使用PURPOSE的branch",
			wantErr: true,
			args: args{
				ctx: trpc.BackgroundContext(),
				tCtx: &TreeContext{
					SlotIDs:               map[string]string{},
					APIResponseParamTypes: map[string]string{},
				},
				node: &KEP.TaskFlowNode{
					NodeID:   "sssbbbbb",
					NodeName: "不是意向判断的节点",
					NodeType: KEP.NodeType_REQUEST,
					NodeData: &KEP.TaskFlowNode_RequestNodeData{
						RequestNodeData: &KEP.RequestNodeData{
							Request: &KEP.RequestNodeData_RequestInfo{
								ID:          "req-id-purpos-1",
								RequestType: KEP.RequestNodeData_RequestInfo_SLOT,
								CustomAsk:   "意向判断的问题",
								IsRequired:  true,
							},
						},
					},
					Branches: []*KEP.Branch{
						{
							BranchID:   "branch-id-4",
							BranchType: KEP.Branch_CUSTOM,
							ConditionInfo: &KEP.ConditionInfo{
								ConditionsLogic: KEP.ConditionInfo_AND,
								ConditionInfo: []*KEP.ConditionInfo{
									{
										Condition: &KEP.ConditionInfo_BranchCondition{
											SourceType: KEP.ConditionInfo_BranchCondition_PURPOSE,
											SourceValue: &KEP.ConditionInfo_BranchCondition_PurposeValueData{
												PurposeValueData: &KEP.ConditionInfo_BranchCondition_PurposeValue{
													Value: "LastAcceptance",
												},
											},
											Comparison:  KEP.ConditionInfo_BranchCondition_NE,
											InputValues: []string{"aabb"},
										},
									},
								},
							},
							NextNodeID: "f8549ed6-8638-b002-e427-0c5226e8eb45",
							PrevNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
						},
					},
				},
			},
		},
	}

	ff := func(ctx context.Context, tCtx *TreeContext, node *KEP.TaskFlowNode) error {
		err := checkRequestNode(ctx, tCtx, node.GetRequestNodeData())
		if err != nil {
			t.Errorf("checkRequestNode() error = %v", err)
			return err
		}

		err = checkBranches(ctx, tCtx, node)
		if err != nil {
			t.Errorf("checkRequestNode() error = %v", err)
			return err
		}
		return nil
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ff(tt.args.ctx, tt.args.tCtx, tt.args.node); (err != nil) != tt.wantErr {
				t.Errorf("checkRequestNode() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}
