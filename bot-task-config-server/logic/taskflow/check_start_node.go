package taskflow

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// checkStartNode 开始节点的数据校验
func checkStartNode(ctx context.Context, node *KEP.TaskFlowNode) error {
	if len(node.GetBranches()) <= 0 {
		log.ErrorContextf(ctx, "checkStartNode|branch is empty")
		return errors.TaskFLowVerifyError("开始节点流转规则有误")
	}

	// 只能连接一个
	if len(node.GetBranches()) > 1 {
		log.ErrorContextf(ctx, "checkStartNode|branch.len=%d", len(node.GetBranches()))
		return errors.TaskFLowVerifyError(fmt.Sprintf("开始节点流转分支数只能为一条,%d", len(node.GetBranches())))
	}

	return nil
}
