package taskflow

import (
	"context"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

var (
	// for unit test
	getSlotsWithIDs = db.GetSlotsWithIDs
)

func checkTree(ctx context.Context, tCtx *TreeContext) *NodeVerifyErrMsg {

	err := checkSlot(ctx, tCtx)
	if err != nil {
		return &NodeVerifyErrMsg{Err: err}
	}

	for _, node := range tCtx.Tree.GetNodes() {
		var err error
		nodeTypeStr := node.GetNodeType().String()

		// 1.检查每个节点
		switch node.GetNodeType() {
		case KEP.NodeType_START:
			err = checkStartNode(ctx, node)
		case KEP.NodeType_API:
			err = checkAPINode(ctx, tCtx, node.GetApiNodeData())
		case KEP.NodeType_REQUEST:
			err = checkRequestNode(ctx, tCtx, node.GetRequestNodeData())
		case KEP.NodeType_ANSWER:
			err = checkAnswerNode(ctx, node.GetAnswerNodeData())
		default:
			err = errors.TaskFLowVerifyError(fmt.Sprintf("%s节点类型错误", nodeTypeStr))
		}
		if err != nil {
			log.ErrorContextf(ctx, "checkTree|nodeType:%s|node:%+v|err:%+v", nodeTypeStr, node, err)
			return verityErrNode(node, err)
		}

		// 2.节点流转规则校验(规则类型、组合条件校验)
		err = checkBranches(ctx, tCtx, node)
		if err != nil {
			log.ErrorContextf(ctx, "checkTree|checkBranches|nodeType:%s|node:%+v|err:%+v", nodeTypeStr, node, err)
			return verityErrNode(node, err)
		}
	}
	return nil
}

// 应该有2个地方引用1. api节点入参； 2. 询问节点的入参
func checkSlot(ctx context.Context, tCtx *TreeContext) error {
	if tCtx == nil || len(tCtx.BotBizId) == 0 {
		log.ErrorContextf(ctx, "checkSlot|tCtx:%+v|", tCtx)
		return errors.TaskFLowVerifyError("BotBizId is empty")
	}

	var slotIDs []string
	for slotID := range tCtx.SlotIDs {
		slotIDs = append(slotIDs, slotID)
	}

	slots, err := getSlotsWithIDs(ctx, tCtx.BotBizId, slotIDs)
	if err != nil {
		log.ErrorContextf(ctx, "checkSlot|slotIDs:%+v|err:%+v", slotIDs, err)
		return err
	}
	if len(slots) == len(slotIDs) {
		return nil
	}
	log.InfoContextf(ctx, "sid:%s|slotIDs:%+v", util.RequestID(ctx), slotIDs)
	for _, id := range slotIDs {
		if containSlotIDs(id, slots) {
			continue
		}
		log.ErrorContextf(ctx, "checkSlot|nodeID:%s|slotIDStr:%s not number", tCtx.SlotIDs[id], id)
		return errors.TaskFLowVerifyError(fmt.Sprintf("%s节点的SlotID:%s不存在", tCtx.SlotIDs[id], id))
	}
	return nil
}

func containSlotIDs(slotID string, slots []*entity.Slot) bool {
	for _, slot := range slots {
		if slot.SlotID == slotID {
			return true
		}
	}
	return false
}
