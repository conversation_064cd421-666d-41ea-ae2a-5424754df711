// bot-task-config-server
//
// @(#taskflow_ui.go  星期一, 七月 15, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package compatible

// TaskFlow 任务流画布整体
type TaskFlow struct {
	// +require 任务流ID
	TaskFlowID string `json:"TaskFlowID"` // 创建任务流程时后端生成唯一id
	// +require 任务流名称
	TaskFlowName string `json:"TaskFlowName"` // 用户输入
	TaskFlowDesc string `json:"TaskFlowDesc"`
	// +require 节点
	Nodes []*TaskFlowNode `json:"Nodes"`
	// +require 边
	Edges        []*Edge  `json:"Edges"`
	Snapshot     Snapshot `json:"Snapshot"`
	SessionMode  int32    `json:"SessionMode"`
	ProtoVersion string   `json:"ProtoVersion,omitempty"`
}

// Snapshot 快照
type Snapshot struct {
	SlotMap      map[string]string `json:"SlotMap"`                // 实体（槽位）数据, Key: SlotID, Value: SlotName
	CustomVarMap map[string]string `json:"CustomVarMap,omitempty"` // 自定义参数（变量数据）数据, Key: VarID, Value: VarName
}

// TaskFlowNode 任务流节点
type TaskFlowNode struct {
	// +require 节点ID uuid
	NodeID string `json:"NodeID"` // 前端可以生成唯一的uuid
	// +require 节点名称
	// 前端默认生成，用户可修改（名称画布内唯一）
	NodeName string `json:"NodeName"`
	// +require 节点类型：开始、判断询问、API、答案 "startNode"  "RequestNode"  "APINode"  "AnswerNode"
	NodeType string `json:"NodeType"`
	//+require 节点数据
	//对应下面的RequestNodeData，AnswerNodeData，APINodeData
	//NodesData interface{} `json:"Nodes"`
	StartNodeData   *StartNodeData   `json:"StartNodeData,omitempty"`
	APINodeData     *APINodeData     `json:"ApiNodeData,omitempty"`
	RequestNodeData *RequestNodeData `json:"RequestNodeData,omitempty"`
	AnswerNodeData  *AnswerNodeData  `json:"AnswerNodeData,omitempty"`
	// +require 节点UI
	NodeUI *UIParams `json:"NodeUI,omitempty"` // 节点UI连线相关数据
	// +require 节点流转规则
	Branches []*Branch `json:"Branches"`
}

// Branch 流转规则
type Branch struct {
	// +require 规则ID
	// 流转规则ID，前端生成唯一uuid
	BranchID string `json:"BranchID"`
	// +require 跳转类型：直接跳转、自定义 "direct"  "custom"
	BranchType string `json:"BranchType"`
	// +require 跳转条件
	// 当跳转类型为直接跳转（direct），这里可为空；否则不能为空
	ConditionInfo *ConditionInfo `json:"ConditionInfo,omitempty"`
	// +require 下一个节点ID
	NextNodeID string `json:"NextNodeID"`
	// +require 上一个节点ID
	PrevNodeID string `json:"PrevNodeID"`
}

// ConditionInfo ...
type ConditionInfo struct {
	ConditionsLogic string           `json:"ConditionsLogic,omitempty"`
	ConditionInfo   []*ConditionInfo `json:"ConditionInfo,omitempty"`
	Condition       *BranchCondition `json:"Condition,omitempty"`
}

// SlotValue ...
type SlotValue struct {
	SlotID string `json:"SlotID,omitempty"`
}

// APIRespValue ...
type APIRespValue struct {
	ParamID string `json:"ParamID,omitempty"`
}

// SystemValue ...
type SystemValue struct {
	Value string `json:"Value,omitempty"`
}

// PurposeValue ...
type PurposeValue struct {
	Value string `json:"Value,omitempty"`
}

// FixedValue ...
type FixedValue struct {
	Value string `json:"Value,omitempty"`
}

// ComparisonInInputValueAddition ...
type ComparisonInInputValueAddition struct {
	// TODO mike 会有用户输入的词吗？
	ValueSource int32    `json:"ValueSource,omitempty"` // 值来源：1: 词条； 2: 用户输入的词
	AllEntry    bool     `json:"AllEntry"`              // 只有在 SourceType 是 SLOT 并且 ValueSource 是1时才生效，代表所有词条
	EntryIDs    []string `json:"EntryIDs"`              // ValueSource 是1时，词条的ID（EntryID）； ValueSource 是2时，用户输入的值在外层的 InputValues， 这里为空
}

// CustomVarValue ...
type CustomVarValue struct {
	Value string `json:"Value,omitempty"`
}

// ComparisonSlotInfoData ...
type ComparisonSlotInfoData struct {
	SlotID   string   `json:"SlotID,omitempty"`   // 值来源：1: 词条； 2: 用户输入的词
	AllEntry bool     `json:"AllEntry,omitempty"` // 只有在 SourceType 是 SLOT 并且 ValueSource 是1时才生效，代表所有词条
	EntryIDs []string `json:"EntryIDs,omitempty"` // ValueSou
}

// ComparisonApiRespInfoData ...
type ComparisonApiRespInfoData struct {
	ParamID                   string   `json:"ParamID,omitempty"`              // API 节点的出参ID;(如果选择的API出参有SubParams，那这里指的是父ParamID， 如：【目标API出参是 A -> B(array<object) -> [C1, C2,...]，则引用C1/C2时，ParamID是B的】)
	AllObjectArrayParams      bool     `json:"AllObjectArrayParams,omitempty"` // 只有在引用的 ParamID 是 array<object> 类型时才用到，代表的是"全部子参数"
	PartOfObjectArrayParamIDs []string `json:"PartOfObjectArrayParamIDs"`
}

// BranchCondition ...
type BranchCondition struct {
	//数据来源类型，SLOT：收集实体；API_RESP：接口出参；CUSTOM_VAR：自定义参数
	SourceType string `json:"SourceType,omitempty"`
	//数据来源为收集实体
	SlotValueData *SlotValue `json:"SlotValueData,omitempty"`
	//数据来源为接口出参
	APIRespValueData *APIRespValue `json:"APIRespValueData,omitempty"`
	SystemValueData  *SystemValue  `json:"SystemValueData,omitempty"`
	//数据来源为意图判断
	PurposeValueData *PurposeValue `json:"PurposeValueData,omitempty"`
	//数据来源为自定义参数
	CustomVarValueData *CustomVarValue `json:"CustomVarValueData,omitempty"`
	// 判断符号
	Comparison string `json:"Comparison,omitempty"`
	// 手动输入的值或者意图判断的值（true，false）
	InputValues []string `json:"InputValues,omitempty"`
	// 对应ComparisonValueSourceType为1的选择词条
	InInputValueAddition *ComparisonInInputValueAddition `json:"InInputValueAddition,omitempty"`
	// 对应ComparisonValueSourceType为3的引用实体-词条
	ComparisonSlotInfo *ComparisonSlotInfoData `json:"ComparisonSlotInfo,omitempty"`
	// 对应ComparisonValueSourceType为4的引用接口出参
	ComparisonApiRespInfo *ComparisonApiRespInfoData `json:"ComparisonApiRespInfo,omitempty"`
	// 输入值类型，1：选择词条，2：手动输入；3：引用实体；4：引用接口出参
	ComparisonValueSourceType int32  `json:"ComparisonValueSourceType,omitempty"`
	MatchType                 string `json:"MatchType,omitempty"`
}

// StartNodeData 开始节点数据
type StartNodeData struct {
}

// RequestInfo ...
type RequestInfo struct {
	ID            string   `json:"ID"`
	RequestType   string   `json:"RequestType"`
	RequestValue  string   `json:"RequestValue"`
	AskType       string   `json:"AskType"`
	CustomAsk     string   `json:"CustomAsk"`
	LLMAskPreview []string `json:"LLMAskPreview"`
	IsRequired    bool     `json:"IsRequired"`
}

// InputCardContent ...
type InputCardContent struct {
	InputCardContent []string `json:"InputCardContent"`
}

// ApiRespCardRefInfo ...
type ApiRespCardRefInfo struct {
	ParamID                   string   `json:"ParamID"`                        // API 节点的出参ID;(如果选择的API出参有SubParams，那这里指的是父ParamID， 如：【目标API出参是 A -> B(array<object) -> [C1, C2,...]，则引用C1/C2时，ParamID是B的】)
	AllObjectArrayParams      bool     `json:"AllObjectArrayParams,omitempty"` // 只有在引用的 ParamID 是 array<object> 类型时才用到，代表的是"全部子参数"
	PartOfObjectArrayParamIDs []string `json:"PartOfObjectArrayParamIDs,omitempty"`
}

// RequestNodeData 询问节点数据
type RequestNodeData struct {
	Request    RequestInfo        `json:"Request"`
	EnableCard bool               `json:"EnableCard"`
	CardFrom   string             `json:"CardFrom"`
	InputCard  InputCardContent   `json:"InputCard,omitempty"`
	ApiCardRef ApiRespCardRefInfo `json:"ApiCardRef,omitempty"`
}

// LLMAnswer ...
type LLMAnswer struct {
	Preview                []string `json:"Preview"`
	EnableCustomPromptWord bool     `json:"EnableCustomPromptWord,omitempty"`
	Prompt                 string   `json:"Prompt"`
}

// InputAnswer ...
type InputAnswer struct {
	Preview string `json:"Preview"`
}

// DocAnswer ...
type DocAnswer struct {
	Preview string       `json:"Preview"`
	RefInfo []DocRefInfo `json:"RefInfo"`
}

// DocRefInfo ...
type DocRefInfo struct {
	DocId   string `json:"DocId"`
	DocType uint32 `json:"DocType"`
	DocName string `json:"DocName"`
}

// AnswerNodeData 答案节点数据
type AnswerNodeData struct {
	// 三种回复类型，LLM：智能回复；INPUT：自定义精准回复；DOC：引用知识文档
	AnswerType      string      `json:"AnswerType"`
	LLMAnswerData   LLMAnswer   `json:"LLMAnswerData,omitempty"`
	InputAnswerData InputAnswer `json:"InputAnswerData,omitempty"`
	DocAnswerData   DocAnswer   `json:"DocAnswerData,omitempty"`
}

// APINodeData ...
type APINodeData struct {
	API           APIInfo         `json:"API"`
	Request       []RequestParam  `json:"Request"`
	Headers       []Header        `json:"Headers"`
	LLMAskPreview []string        `json:"LLMAskPreview"`
	Response      []ResponseParam `json:"Response"`
	DoubleCheck   bool            `json:"DoubleCheck"`
}

// APIInfo ...
type APIInfo struct {
	URL    string `json:"URL"`
	Method string `json:"Method"`
}

// RequestSlotValue ...
type RequestSlotValue struct {
	SlotID string `json:"SlotID"`
	// 询问话术生成方式
	AskTypeEnum string `json:"AskType"`    // 询问话术生成类型，字符串： LLM(大模型) 或 INPUT(用户输入)
	CustomAsk   string `json:"CustomAsk"`  // 用户输入的询问内容
	FormatDesc  string `json:"FormatDesc"` // 格式描述
}

// RequestParam ...
type RequestParam struct {
	ParamID            string            `json:"ParamID"`
	ParamName          string            `json:"ParamName"`
	ParamType          string            `json:"ParamType"`
	SourceType         string            `json:"SourceType"`
	SlotValueData      *RequestSlotValue `json:"SlotValueData,omitempty"`
	APIRespValueData   *APIRespValue     `json:"APIRespValueData,omitempty"`
	FixedValueData     *FixedValue       `json:"FixedValueData,omitempty"`
	SystemValueData    *SystemValue      `json:"SystemValueData,omitempty"`
	CustomVarValueData *CustomVarValue   `json:"CustomVarValueData,omitempty"`
	SubRequest         []RequestParam    `json:"SubRequest"`
	IsRequired         bool              `json:"IsRequired"`
}

// Header 请求体
type Header struct {
	ParamID            string          `json:"ParamID"`
	ParamName          string          `json:"ParamName"`
	ParamType          string          `json:"ParamType"`
	SourceType         string          `json:"SourceType"`
	APIRespValueData   *APIRespValue   `json:"APIRespValueData,omitempty"`
	FixedValueData     *FixedValue     `json:"FixedValueData,omitempty"`
	SystemValueData    *SystemValue    `json:"SystemValueData,omitempty"`
	CustomVarValueData *CustomVarValue `json:"CustomVarValueData,omitempty"`
	SubHeader          []Header        `json:"SubHeader"`
	IsOptional         bool            `json:"IsOptional"`
}

// ResponseParam ...
type ResponseParam struct {
	ParamID    string          `json:"ParamID"`
	ParamName  string          `json:"ParamName"`
	ParamType  string          `json:"ParamType"`
	ParamTitle string          `json:"ParamTitle"`
	JSONPath   string          `json:"JSONPath"`
	SubParams  []ResponseParam `json:"SubParams,omitempty"`
}

// UIParams 前端展示用参数
type UIParams struct {
	X string `json:"X,omitempty"`
	Y string `json:"Y,omitempty"`
}

// Edge 边
type Edge struct {
	EdgeID       string `json:"EdgeID"`
	Source       string `json:"Source"`
	Target       string `json:"Target"`
	SourceAnchor int    `json:"SourceAnchor"`
	TargetAnchor int    `json:"TargetAnchor"`
	Label        string `json:"Label"` // 保存线上的标签
}
