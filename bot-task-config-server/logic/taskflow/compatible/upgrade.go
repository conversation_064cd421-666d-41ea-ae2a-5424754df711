package compatible

//
//import (
//	"context"
//	"fmt"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idget"
//	"math/rand"
//	"net/url"
//	"strings"
//	"time"
//
//	"git.code.oa.com/trpc-go/trpc-go"
//	"git.code.oa.com/trpc-go/trpc-go/errs"
//	"git.code.oa.com/trpc-go/trpc-go/log"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/ui"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/publish"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/binding"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
//	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
//	"git.woa.com/dialogue-platform/go-comm/json0"
//	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
//	"github.com/PuerkitoBio/goquery"
//	"golang.org/x/exp/maps"
//)
//
//// [升级v1.7对话树的协议到 v2.1]
//
//var (
//	// EnableDataInvalid 原始数据数据有误
//	EnableDataInvalid = errs.New(-250001, "原始数据数据有误")
//)
//
//// UpgradeTaskFlow 刷数据上下文信息
//type UpgradeTaskFlow struct {
//	botBizId               string
//	oldTaskFlowJson        string
//	OldTreeSlotName        map[string]struct{} // 从旧的对话树提取的槽位名称（API节点的入参，询问节点的入参）
//	AllSlotMap             map[string]string   // 这个对话树上的所有Slot（存在的+新建的）key: slotName, value: slotID
//	needCreateSlotNameList map[string]string   // 这个对话树上需要新建的Slot key: slotName, value: slotID
//
//	// 用于替换 "引用"API节点时的场景; key: NodeID+"_"+ParamVarName, value: ApiRespParamID
//	ApiNodeResponseForRequestNodeCardApiRef map[string]string
//
//	// key: <node_id>_<SlotVarName>, value: SlotID
//	ApiNodeRequestForAnswerNode map[string]string
//
//	// key: <node_id>_<ParamVarName>, value: ParamID
//	ApiNodeResponseForAnswerNode map[string]string
//
//	// key: RequestNode.NodeData.RequiredInfo.SlotVarName(值是 "field_<前端生成的ID>" ), value: slotID
//	RequestNodeRequestForAnswerNode map[string]string
//}

// SpanDataInfo  答案节点中前端用到的 span标签中的 data-info 属性的值
type SpanDataInfo struct {
	Name string `json:"name"`
	Type string `json:"type"`
	Text string `json:"text"`
	//NodeId string `json:"nodeId"`
}

//func newUpgradeTaskFlow(botBizId string, oldTaskFlowJson string) *UpgradeTaskFlow {
//	return &UpgradeTaskFlow{
//		botBizId:               botBizId,
//		oldTaskFlowJson:        oldTaskFlowJson,
//		OldTreeSlotName:        make(map[string]struct{}),
//		AllSlotMap:             make(map[string]string),
//		needCreateSlotNameList: make(map[string]string),
//
//		ApiNodeResponseForRequestNodeCardApiRef: make(map[string]string),
//
//		ApiNodeRequestForAnswerNode:     make(map[string]string),
//		ApiNodeResponseForAnswerNode:    make(map[string]string),
//		RequestNodeRequestForAnswerNode: make(map[string]string),
//	}
//}
//
//// Upgrade KEP.TaskFlowProtoVersion_V1_7 -> KEP.TaskFlowProtoVersion_V2_1
//func Upgrade(ctx context.Context, botBizId string) error {
//	//taskFlowJson string
//
//	// for test
//	//flowId := "99cd6253-ef26-402b-a769-519d88fa6438" // 买奥迪
//	// for test
//	// step1. 取旧的对话树
//	oldTrees, err := db.FetchTaskFlow(ctx, botBizId, "")
//	if err != nil {
//		log.ErrorContextf(ctx, "Upgrade|FetchTaskFlow|err:%+v", err)
//		return err
//	}
//
//	// step2. 只有从草稿的 对话树部分要 标记删除？
//	tfs, err := db.GetDirtyTaskFlow(ctx, botBizId)
//	if err != nil {
//		log.ErrorContextf(ctx, "Upgrade|GetDirtyTaskFlow|botBizId:%s|err:%+v", botBizId, err)
//		return err
//	}
//
//	if len(oldTrees) == 0 && len(tfs) == 0 {
//		log.InfoContextf(ctx, "[upgrade17to21]bot:%s|无需升级", botBizId)
//		return nil
//	}
//
//	//if len(oldTrees) == 0 {
//	//	log.InfoContextf(ctx, "[upgrade17to21]无需要升级的对话树: %s", botBizId)
//	//	log.InfoContextf(ctx, "Upgrade|FetchTaskFlow oldTrees.len=0")
//	//	return nil
//	//}
//
//	taskFlowIDs := make([]string, 0)
//
//	var upgradeSuccess []string
//	var upgradeFailed []string
//
//	for _, old := range oldTrees {
//		flowCtx := trpc.CloneContext(ctx)
//		log.WithContextFields(flowCtx, "flowId", old.FlowID)
//		log.InfoContextf(flowCtx, "[upgrade17to21]bot:%s|开始升级 flowID:%s", botBizId, old.FlowID)
//
//		log.InfoContextf(flowCtx, "Upgrade|old.DialogJsonEnable:%s", old.DialogJsonEnable)
//		t := newUpgradeTaskFlow(botBizId, old.DialogJsonEnable)
//		err = t.getSlotListFromOldTree(flowCtx)
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|getSlotListFromOldTree|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//		err = t.prepareSlotInfo(flowCtx)
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|prepareSlotInfo|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//
//		err = t.collectIDsForAnswerNode(flowCtx)
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|collectIDsForAnswerNode|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//
//		// step3. 升级对话树JSON
//		newTree, err := t.upgradeTaskFlowJson(flowCtx)
//		if err == EnableDataInvalid {
//			upgradeFailed = append(upgradeFailed, fmt.Sprintf("%s(%s)", old.FlowID, old.IntentName))
//			// 需要手动处理，先忽略
//			log.InfoContextf(flowCtx, "[upgrade17to21][ERROR]bot:%s|升级失败，可能是脏数据，请人为处理 flowID:%s", botBizId, old.FlowID)
//			log.ErrorContextf(flowCtx, "Upgrade|NEED_HANDLE|EnableDataInvalid|flowId:%s|err:%+v", old.FlowID, err)
//			continue
//		}
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|upgradeTaskFlowJson|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//
//		log.InfoContextf(flowCtx, "Upgrade|newTree:%+v", newTree)
//		newJson, err := protoutil.TaskFlowToJson(newTree)
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|TaskFlowToJson|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//		log.InfoContextf(flowCtx, "Upgrade|newJson:%s", newJson)
//		old.DialogJsonEnable = newJson
//		old.DialogJsonDraft = newJson
//
//		tf := entity.ModifyTaskFlowParams{
//			FlowID:           old.FlowID,
//			IntentName:       old.IntentName,
//			IntentDesc:       old.IntentDesc,
//			CategoryID:       old.CategoryID,
//			RobotId:          botBizId,
//			DialogJsonDraft:  newJson,
//			DialogJsonEnable: newJson,
//			Uin:              old.Uin,
//			SubUin:           old.SubUin,
//			UpdateTime:       time.Now(),
//			//	Action: old.
//			//ReleaseStatus: old.
//			//FlowState: old. // 发布相关的状态？
//			IsEnable: uint32(KEP.SaveTaskFlowReq_ENABLE),
//			SlotIDs:  idget.GetRelatedSlotIDs(flowCtx, newTree),
//		}
//		// step4. create slots & SaveTaskFlow (发布到测试环境)
//		err = db.SaveUpgrade(flowCtx, old.Uin, old.SubUin, botBizId, t.needCreateSlotNameList, tf)
//		if err != nil {
//			log.ErrorContextf(flowCtx, "Upgrade|Save|flowId:%s|err:%+v", old.FlowID, err)
//			return err
//		}
//
//		taskFlowIDs = append(taskFlowIDs, tf.FlowID)
//		upgradeSuccess = append(upgradeSuccess, fmt.Sprintf("%s(%s)", tf.FlowID, tf.IntentName))
//		log.InfoContextf(flowCtx, "[upgrade17to21]bot:%s|升级完成|flowID:%s", botBizId, old.FlowID)
//		log.InfoContextf(flowCtx, ">>>>>>>>>>> end flowId upgrade[%s] ----------------------", old.FlowID)
//	}
//
//	log.InfoContextf(ctx, "Upgrade|delete-draft-taskFlow|flowIDs:%s", strings.Join(maps.Keys(tfs), ","))
//	//err = db.DeleteTaskFlowWithIgnoreDMErr(ctx, botBizId, maps.Keys(tfs))
//	//if err != nil {
//	//	log.ErrorContextf(ctx, "Upgrade|DeleteTaskFlowWithIgnoreDMErr|botBizId:%s|err:%+v", botBizId, err)
//	//	return err
//	//}
//	if len(maps.Keys(tfs)) > 0 {
//		log.InfoContextf(ctx, "[upgrade17to21]bot:%s|该应用下v1.7草稿的对话树|flowIDs:%s", botBizId,
//			strings.Join(maps.Keys(tfs), ","))
//	}
//
//	var draft []string
//	for id, flow := range tfs {
//		draft = append(draft, fmt.Sprintf("%s(%s)", id, flow.IntentName))
//	}
//
//	// step5. 发布上线
//	// 发布轮询查状态？
//	rand.Seed(time.Now().UnixNano())
//	taskID := rand.Uint64()
//	p := publish.NewPublish()
//	if len(taskFlowIDs) > 0 {
//		err = p.ICSTaskFlowPublish(ctx, botBizId, taskID, taskFlowIDs)
//	}
//
//	tips := fmt.Sprintf("|升级成功的:%s|脏数据的:%s|草稿的:%s",
//		strings.Join(upgradeSuccess, ","),
//		strings.Join(upgradeFailed, ","),
//		strings.Join(draft, ","),
//	)
//	if err != nil {
//		log.InfoContextf(ctx, "[upgrade17to21]bot:%s|发布失败，请手动处理|%s|err:%+v", botBizId, tips, err)
//		return nil
//	}
//	log.InfoContextf(ctx, "[upgrade17to21]bot:%s|发布成功|%s", botBizId, tips)
//
//	return nil
//}
//
//func (t *UpgradeTaskFlow) getSlotListFromOldTree(ctx context.Context) error {
//	var oldTaskFlow ui.TaskFlow
//	err := json0.UnmarshalStr(t.oldTaskFlowJson, &oldTaskFlow)
//	if err != nil {
//		log.ErrorContextf(ctx, "getSlotListFromOldTree|UnmarshalStr|err:%+v", err)
//		return err
//	}
//	for _, old := range oldTaskFlow.Nodes {
//		switch old.NodeType {
//		case ui.NodeTypeStartNode, ui.NodeTypeAnswerNode:
//			//ignore
//		case ui.NodeTypeAPINode:
//			var oldApiNodeData ui.APINodeData
//			if err = binding.BindJSON(json0.Marshal2StringNoErr(old.NodeData), &oldApiNodeData); err != nil {
//				log.ErrorContextf(ctx, "getSlotListFromOldTree|err:%+v", err)
//				return err
//			}
//			for _, info := range oldApiNodeData.RequiredInfo {
//				t.OldTreeSlotName[info.SlotName] = struct{}{}
//			}
//			for _, resp := range oldApiNodeData.APIResponseParams {
//				t.ApiNodeResponseForRequestNodeCardApiRef[old.NodeID+"_"+resp.ParamVarName] = resp.ParamID
//			}
//		case ui.NodeTypeRequestNode:
//			var oldRequestNode ui.RequestNodeData
//			if err := binding.BindJSON(json0.Marshal2StringNoErr(old.NodeData), &oldRequestNode); err != nil {
//				log.ErrorContextf(ctx, "getSlotListFromOldTree|err:%+v", err)
//				return err
//			}
//			t.OldTreeSlotName[oldRequestNode.RequiredInfo.SlotName] = struct{}{}
//		}
//	}
//
//	return nil
//}
//
//func (t *UpgradeTaskFlow) collectIDsForAnswerNode(ctx context.Context) error {
//	var oldTaskFlow ui.TaskFlow
//	err := json0.UnmarshalStr(t.oldTaskFlowJson, &oldTaskFlow)
//	if err != nil {
//		log.ErrorContextf(ctx, "collectIDsForAnswerNode|UnmarshalStr|err:%+v", err)
//		return err
//	}
//	for _, old := range oldTaskFlow.Nodes {
//		switch old.NodeType {
//		case ui.NodeTypeStartNode, ui.NodeTypeAnswerNode:
//			//ignore
//		case ui.NodeTypeAPINode:
//			var oldApiNodeData ui.APINodeData
//			if err = binding.BindJSON(json0.Marshal2StringNoErr(old.NodeData), &oldApiNodeData); err != nil {
//				log.ErrorContextf(ctx, "collectIDsForAnswerNode|err:%+v", err)
//				return err
//			}
//			for _, info := range oldApiNodeData.RequiredInfo {
//				t.ApiNodeRequestForAnswerNode[old.NodeID+"_"+info.SlotVarName] = t.AllSlotMap[info.SlotName]
//			}
//			for _, resp := range oldApiNodeData.APIResponseParams {
//				t.ApiNodeResponseForAnswerNode[old.NodeID+"_"+resp.ParamVarName] = resp.ParamID
//			}
//		case ui.NodeTypeRequestNode:
//			var oldRequestNode ui.RequestNodeData
//			if err := binding.BindJSON(json0.Marshal2StringNoErr(old.NodeData), &oldRequestNode); err != nil {
//				log.ErrorContextf(ctx, "collectIDsForAnswerNode|err:%+v", err)
//				return err
//			}
//			if oldRequestNode.RequiredInfo != nil {
//				t.RequestNodeRequestForAnswerNode[oldRequestNode.RequiredInfo.SlotVarName] =
//					t.AllSlotMap[oldRequestNode.RequiredInfo.SlotName]
//			}
//		}
//	}
//
//	return nil
//}
//
////func (t *UpgradeTaskFlow) Save(ctx context.Context, uin, subUin string, robotID string,
////tf entity.ModifyTaskFlowParams) error {
////
////	return db.SaveUpgrade(ctx, uin, subUin, robotID, t.needCreateSlotNameList, tf)
////}
//
//func (t *UpgradeTaskFlow) prepareSlotInfo(ctx context.Context) error {
//	slotNames := make([]string, 0)
//	for k := range t.OldTreeSlotName {
//		slotNames = append(slotNames, k)
//	}
//
//	// 这个机器人下已经存在的 slot信息
//	existSlots, err := db.GetSlotListWithNames(ctx, t.botBizId, slotNames)
//	if err != nil {
//		return err
//	}
//	existSlotsMap := make(map[string]string)
//	for _, existSlot := range existSlots {
//		existSlotsMap[existSlot.SlotName] = existSlot.SlotID
//	}
//
//	for slotName := range t.OldTreeSlotName {
//		if id, ok := existSlotsMap[slotName]; ok {
//			t.AllSlotMap[slotName] = id
//		} else {
//			newSlotID := idgenerator.NewUUID()
//			t.AllSlotMap[slotName] = newSlotID
//			t.needCreateSlotNameList[slotName] = newSlotID
//		}
//	}
//
//	return nil
//}
//
//func (t *UpgradeTaskFlow) upgradeTaskFlowJson(ctx context.Context) (*KEP.TaskFlow, error) {
//	var old ui.TaskFlow
//	err := json0.UnmarshalStr(t.oldTaskFlowJson, &old)
//	if err != nil {
//		log.ErrorContextf(ctx, "upgradeTaskFlowJson|UnmarshalStr|err:%+v", err)
//		return nil, err
//	}
//
//	nodes, err := t.upgradeNodes(ctx, old.Nodes)
//	if err != nil {
//		return nil, err
//	}
//	tf := &KEP.TaskFlow{
//		TaskFlowID:   old.TaskFlowID,
//		TaskFlowName: old.TaskFlowName,
//		Nodes:        nodes,
//		Edges:        t.upgradeEdge(old.Edges),
//	}
//	return tf, nil
//}
//
//func (t *UpgradeTaskFlow) upgradeNodes(ctx context.Context, oldNodes []*ui.TaskFlowNode) (
//[]*KEP.TaskFlowNode, error) {
//	newNodes := make([]*KEP.TaskFlowNode, 0, len(oldNodes))
//	for _, old := range oldNodes {
//		newTaskNodeData := &KEP.TaskFlowNode{
//			NodeID:   old.NodeID,
//			NodeName: old.NodeName,
//			NodeType: t.upgradeNodeType(old.NodeType),
//			NodeUI:   &KEP.UIParams{X: old.NodeUI.X, Y: old.NodeUI.Y},
//			Branches: t.upgradeBranches(old.Branches),
//		}
//		if len(old.NodeUI.X) == 0 || len(old.NodeUI.Y) == 0 {
//			newTaskNodeData.NodeUI = nil
//		}
//
//		switch newTaskNodeData.NodeType {
//		case KEP.NodeType_START:
//			newTaskNodeData.NodeData = t.upgradeStartNodeData()
//		case KEP.NodeType_API:
//			newApiNode, err := t.upgradeApiNodeData(ctx, old)
//			if err != nil {
//				return nil, err
//			}
//			newTaskNodeData.NodeData = newApiNode
//		case KEP.NodeType_REQUEST:
//			newApiNode, err := t.upgradeRequestNodeData(ctx, old)
//			if err != nil {
//				return nil, err
//			}
//			newTaskNodeData.NodeData = newApiNode
//		case KEP.NodeType_ANSWER:
//			continue
//			//	newApiNode, err := t.upgradeAnswerNodeData(ctx, old.NodeData)
//			//	if err != nil {
//			//		return nil, err
//			//	}
//			//	newTaskNodeData.NodeData = newApiNode
//		}
//		newNodes = append(newNodes, newTaskNodeData)
//	}
//
//	// 最后再刷 答案节点的数据
//	for _, old := range oldNodes {
//		newTaskNodeData := &KEP.TaskFlowNode{
//			NodeID:   old.NodeID,
//			NodeName: old.NodeName,
//			NodeType: t.upgradeNodeType(old.NodeType),
//			NodeUI:   &KEP.UIParams{X: old.NodeUI.X, Y: old.NodeUI.Y},
//			Branches: t.upgradeBranches(old.Branches),
//		}
//		if len(old.NodeUI.X) == 0 || len(old.NodeUI.Y) == 0 {
//			newTaskNodeData.NodeUI = nil
//		}
//		if newTaskNodeData.NodeType != KEP.NodeType_ANSWER {
//			continue
//		}
//		newApiNode, err := t.upgradeAnswerNodeData(ctx, old.NodeData)
//		if err != nil {
//			return nil, err
//		}
//		newTaskNodeData.NodeData = newApiNode
//		newNodes = append(newNodes, newTaskNodeData)
//	}
//
//	return newNodes, nil
//}
//
//func (t *UpgradeTaskFlow) upgradeStartNodeData() *KEP.TaskFlowNode_StartNodeData {
//	return &KEP.TaskFlowNode_StartNodeData{StartNodeData: &KEP.StartNodeData{}}
//}
//
//func (t *UpgradeTaskFlow) upgradeApiNodeData(ctx context.Context, oldNode *ui.TaskFlowNode) (
//*KEP.TaskFlowNode_ApiNodeData, error) {
//	var old ui.APINodeData
//	if err := binding.BindJSON(json0.Marshal2StringNoErr(oldNode.NodeData), &old); err != nil {
//		log.ErrorContextf(ctx, "upgradeApiNodeData|err:%+v", err)
//		return nil, err
//	}
//
//	if len(old.APIPath.Path) == 0 {
//		return nil, EnableDataInvalid
//	}
//
//	newApiNode := &KEP.APINodeData{
//		API: &KEP.APINodeData_APIInfo{
//			URL:    old.APIPath.Path,
//			Method: old.APIPath.Method,
//		},
//		DoubleCheck: old.InvokeConfirm,
//	}
//
//	newApiNode.Request = make([]*KEP.APINodeData_RequestParam, 0, len(old.RequiredInfo))
//	for _, info := range old.RequiredInfo {
//		requestParam := &KEP.APINodeData_RequestParam{
//			ParamID:    info.SlotID,
//			ParamName:  info.SlotVarName,
//			ParamType:  info.ParamType,
//			SourceType: KEP.APINodeData_RequestParam_SLOT,
//			IsRequired: info.IsRequired,
//		}
//		switch info.ProbeConfig {
//		case ui.NodeAnswerSourceAuto:
//			requestParam.SourceValue = &KEP.APINodeData_RequestParam_SlotValueData{
//				SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
//					SlotID:  t.AllSlotMap[info.SlotName],
//					AskType: KEP.APINodeData_RequestParam_SlotValue_LLM,
//				},
//			}
//			newApiNode.LLMAskPreview = append(newApiNode.LLMAskPreview, info.ProbePreview...)
//		case ui.NodeCustomSourceCustom:
//			requestParam.SourceValue = &KEP.APINodeData_RequestParam_SlotValueData{
//				SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
//					SlotID:    t.AllSlotMap[info.SlotName],
//					AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
//					CustomAsk: info.ProbeCustom,
//				},
//			}
//		}
//		// 挪到 collectIDsForAnswerNode 先提取
//		//t.ApiNodeRequestForAnswerNode[oldNode.NodeID+"_"+info.SlotVarName] = t.AllSlotMap[info.SlotName]
//
//		newApiNode.Request = append(newApiNode.Request, requestParam)
//	}
//
//	newApiNode.Response = make([]*KEP.APINodeData_ResponseParam, 0, len(old.APIResponseParams))
//	for _, oldResp := range old.APIResponseParams {
//		newApiNode.Response = append(newApiNode.Response, &KEP.APINodeData_ResponseParam{
//			ParamID:    oldResp.ParamID,
//			ParamName:  oldResp.ParamVarName,
//			ParamType:  oldResp.ParamType,
//			ParamTitle: oldResp.ParamName,
//			JSONPath:   oldResp.ParamPath,
//		})
//		// 挪到 collectIDsForAnswerNode 先提取
//		//t.ApiNodeResponseForAnswerNode[oldNode.NodeID+"_"+oldResp.ParamVarName] = oldResp.ParamID
//	}
//
//	return &KEP.TaskFlowNode_ApiNodeData{ApiNodeData: newApiNode}, nil
//}
//
//func (t *UpgradeTaskFlow) upgradeRequestNodeData(ctx context.Context,
//	oldNode *ui.TaskFlowNode) (*KEP.TaskFlowNode_RequestNodeData, error) {
//	var old ui.RequestNodeData
//	if err := binding.BindJSON(json0.Marshal2StringNoErr(oldNode.NodeData), &old); err != nil {
//		log.ErrorContextf(ctx, "upgradeRequestNodeData|err:%+v", err)
//		return nil, err
//	}
//
//	newNode := &KEP.RequestNodeData{}
//
//	// 挪到 collectIDsForAnswerNode 先提取
//	//t.RequestNodeRequestForAnswerNode[old.RequiredInfo.SlotVarName] = t.AllSlotMap[old.RequiredInfo.SlotName]
//
//	newNode.Request = &KEP.RequestNodeData_RequestInfo{
//		ID:            old.RequiredInfo.SlotID,
//		RequestType:   KEP.RequestNodeData_RequestInfo_SLOT,
//		RequestValue:  t.AllSlotMap[old.RequiredInfo.SlotName],
//		CustomAsk:     t.upgradeRichText(ctx, old.RequiredInfo.ProbeCustom),
//		LLMAskPreview: old.RequiredInfo.ProbePreview,
//		IsRequired:    old.RequiredInfo.IsRequired,
//	}
//	switch old.RequiredInfo.ProbeConfig {
//	case ui.NodeAnswerSourceAuto:
//		newNode.Request.AskType = KEP.RequestNodeData_RequestInfo_LLM
//	case ui.NodeCustomSourceCustom:
//		newNode.Request.AskType = KEP.RequestNodeData_RequestInfo_INPUT
//	}
//
//	newNode.EnableCard = old.OptionCards
//	switch old.OptionType {
//	case ui.RequestNodeOptionTypeCustom:
//		newNode.CardFrom = KEP.RequestNodeData_INPUT
//	case ui.RequestNodeOptionTypeAPI:
//		newNode.CardFrom = KEP.RequestNodeData_API
//	}
//	newNode.InputCard = &KEP.RequestNodeData_InputCardContent{
//		InputCardContent: old.OptionContents,
//	}
//
//	if len(old.APIParams) > 0 {
//		apiRespParamID, ok := t.ApiNodeResponseForRequestNodeCardApiRef[old.APIParams[0].NodeID+"_"+
//			old.APIParams[0].ParamVarName]
//		if ok {
//			newNode.ApiCardRef = &KEP.RequestNodeData_ApiRespCardRefInfo{
//				ParamID: apiRespParamID,
//			}
//		}
//	}
//
//	if newNode.ApiCardRef == nil { // slot 找不到的时候
//		// 前端需要，不然画布显示会有问题
//		newNode.ApiCardRef = &KEP.RequestNodeData_ApiRespCardRefInfo{
//			ParamID: "",
//		}
//	}
//
//	return &KEP.TaskFlowNode_RequestNodeData{RequestNodeData: newNode}, nil
//}
//
//func (t *UpgradeTaskFlow) upgradeAnswerNodeData(ctx context.Context, data interface{}) (
//*KEP.TaskFlowNode_AnswerNodeData, error) {
//	var old ui.AnswerNodeData
//	if err := binding.BindJSON(json0.Marshal2StringNoErr(data), &old); err != nil {
//		log.ErrorContextf(ctx, "upgradeAnswerNodeData|err:%+v", err)
//		return nil, err
//	}
//	newNode := &KEP.AnswerNodeData{}
//	switch old.AnswerSource {
//	case ui.NodeAnswerSourceAuto:
//		newNode.AnswerType = KEP.AnswerNodeData_LLM
//
//	case ui.NodeCustomSourceCustom:
//		newNode.AnswerType = KEP.AnswerNodeData_INPUT
//
//	}
//	newNode.LLMAnswerData = &KEP.AnswerNodeData_LLMAnswer{
//		Preview: old.AnswerPreview,
//	}
//	newNode.InputAnswerData = &KEP.AnswerNodeData_InputAnswer{
//		Preview: t.upgradeRichText(ctx, old.AnswerCustom),
//	}
//
//	newNode.DocAnswerData = &KEP.AnswerNodeData_DocAnswer{
//		Preview: "",
//		RefInfo: []*KEP.AnswerNodeData_DocAnswer_DocRefInfo{},
//	}
//
//	return &KEP.TaskFlowNode_AnswerNodeData{AnswerNodeData: newNode}, nil
//}
//
//func (t *UpgradeTaskFlow) upgradeRichText(ctx context.Context, source string) string {
//	if len(source) == 0 {
//		return ""
//	}
//
//	log.InfoContextf(ctx, "[upgrade17to21]upgradeRichText|source:%s|", source)
//	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
//	if err != nil {
//		log.ErrorContextf(ctx, "NewDocumentFromReader failed,err:%v", err)
//		return ""
//	}
//
//	doc.Find("span").Each(func(i int, s *goquery.Selection) {
//		dataValue, _ := s.Attr("data-value")
//		log.InfoContextf(ctx, "convertUIDataByHtml|dataValue=%s", dataValue)
//
//		dataInfo, _ := s.Attr("data-info")
//		log.InfoContextf(ctx, "convertUIDataByHtml|dataInfo-1=%s", dataInfo)
//		dataInfo, err = url.QueryUnescape(dataInfo)
//		if err != nil {
//			log.Fatal(err)
//			return
//		}
//
//		di := SpanDataInfo{}
//		_ = json0.Unmarshal([]byte(dataInfo), &di)
//
//		if v, ok := t.ApiNodeRequestForAnswerNode[dataValue]; ok {
//			s.SetAttr("data-value", v)
//			s.SetAttr("data-type", "SLOT")
//			di.Type = "SLOT"
//			di.Text = "@" + di.Name
//		}
//
//		if v, ok := t.ApiNodeResponseForAnswerNode[dataValue]; ok && di.Type == "params" {
//			s.SetAttr("data-value", v)
//			s.SetAttr("data-type", "API_RESP")
//			di.Type = "API_RESP"
//			di.Text = "$" + di.Name
//		}
//
//		if v, ok := t.RequestNodeRequestForAnswerNode[dataValue]; ok {
//			s.SetAttr("data-value", v)
//			s.SetAttr("data-type", "SLOT")
//			di.Type = "SLOT"
//			di.Text = "@" + di.Name
//		}
//
//		dataInfo = json0.Marshal2StringNoErr(di)
//
//		log.InfoContextf(ctx, "convertUIDataByHtml|dataInfo-2=%s", dataInfo)
//		dataInfo = url.QueryEscape(dataInfo)
//		log.InfoContextf(ctx, "convertUIDataByHtml|dataInfo-4=%s", dataInfo)
//		s.SetAttr("data-info", dataInfo)
//	})
//
//	newHTML, err := doc.Find("body").First().Html()
//	if err != nil {
//		log.ErrorContextf(ctx, "new html failed,err:%v", err)
//		return ""
//	}
//	log.InfoContextf(ctx, "[upgrade17to21]upgradeRichText|newHTML:%s|", newHTML)
//	return newHTML
//}
//
//func (t *UpgradeTaskFlow) upgradeNodeType(oldType string) KEP.NodeType {
//	switch oldType {
//	case ui.NodeTypeStartNode:
//		return KEP.NodeType_START
//	case ui.NodeTypeAPINode:
//		return KEP.NodeType_API
//	case ui.NodeTypeRequestNode:
//		return KEP.NodeType_REQUEST
//	case ui.NodeTypeAnswerNode:
//		return KEP.NodeType_ANSWER
//	default:
//		return KEP.NodeType_UNKNOWN
//	}
//}
//
//func (t *UpgradeTaskFlow) upgradeBranches(oldBranches []*ui.Branch) []*KEP.Branch {
//	newBranches := make([]*KEP.Branch, 0, len(oldBranches))
//	upgradeBranchType := func(oldType string) KEP.Branch_BranchTypeInfo {
//		switch oldType {
//		case ui.NodeBranchDirectType:
//			return KEP.Branch_DIRECT
//		case ui.NodeBranchCustomType:
//			return KEP.Branch_CUSTOM
//		default:
//			return KEP.Branch_UNSPECIFIED
//		}
//	}
//	for _, old := range oldBranches {
//		newBranches = append(newBranches, &KEP.Branch{
//			BranchID:      old.BranchID,
//			BranchType:    upgradeBranchType(old.BranchType),
//			ConditionInfo: t.upgradeBranchCondition(old.ConditionsLogic, old.Conditions),
//			NextNodeID:    old.NextNodeID,
//			PrevNodeID:    old.PrevNodeID,
//		})
//	}
//	return newBranches
//}
//
//func (t *UpgradeTaskFlow) upgradeBranchCondition(oldConditionsLogic string, oldConditions []*ui.Condition)
//*KEP.ConditionInfo {
//
//	if len(oldConditions) == 0 {
//		return new(KEP.ConditionInfo)
//	}
//
//	if len(oldConditions) == 1 {
//		cc := &KEP.ConditionInfo{
//			Condition: &KEP.ConditionInfo_BranchCondition{
//				Comparison:  t.upgradeBranchComparison(oldConditions[0].Operator),
//				InputValues: oldConditions[0].Values,
//			},
//		}
//		switch oldConditions[0].ConditionSource {
//		case ui.ConditionSourceRequiredInfo:
//			cc.Condition.SourceType = KEP.ConditionInfo_BranchCondition_SLOT
//			cc.Condition.SourceValue = &KEP.ConditionInfo_BranchCondition_SlotValueData{
//				SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
//					SlotID: t.AllSlotMap[oldConditions[0].ParamName],
//				},
//			}
//		case ui.ConditionSourceAPIResponseParam:
//			cc.Condition.SourceType = KEP.ConditionInfo_BranchCondition_API_RESP
//			cc.Condition.SourceValue = &KEP.ConditionInfo_BranchCondition_APIRespValueData{
//				APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
//					ParamID: oldConditions[0].ParamID,
//				},
//			}
//		}
//		return cc
//	}
//
//	upgradeLogic := func(oldLogic string) KEP.ConditionInfo_ConditionsLogicEnum {
//		switch oldLogic {
//		case ui.ConditionsLogicAnd:
//			return KEP.ConditionInfo_AND
//		case ui.ConditionsLogicOr:
//			return KEP.ConditionInfo_OR
//		default:
//			return KEP.ConditionInfo_UNSPECIFIED
//		}
//	}
//
//	newCondition := &KEP.ConditionInfo{
//		ConditionsLogic: upgradeLogic(oldConditionsLogic),
//		ConditionInfo:   make([]*KEP.ConditionInfo, 0, len(oldConditions)),
//	}
//	for _, oldCondition := range oldConditions {
//		cc := &KEP.ConditionInfo{
//			Condition: &KEP.ConditionInfo_BranchCondition{
//				Comparison:  t.upgradeBranchComparison(oldCondition.Operator),
//				InputValues: oldCondition.Values,
//			},
//		}
//		switch oldCondition.ConditionSource {
//		case ui.ConditionSourceRequiredInfo:
//			cc.Condition.SourceType = KEP.ConditionInfo_BranchCondition_SLOT
//			cc.Condition.SourceValue = &KEP.ConditionInfo_BranchCondition_SlotValueData{
//				SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
//					SlotID: t.AllSlotMap[oldCondition.ParamName],
//				},
//			}
//		case ui.ConditionSourceAPIResponseParam:
//			cc.Condition.SourceType = KEP.ConditionInfo_BranchCondition_API_RESP
//			cc.Condition.SourceValue = &KEP.ConditionInfo_BranchCondition_APIRespValueData{
//				APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
//					ParamID: oldCondition.ParamID,
//				},
//			}
//		}
//		newCondition.ConditionInfo = append(newCondition.ConditionInfo, cc)
//	}
//
//	return newCondition
//}
//
//func (t *UpgradeTaskFlow) upgradeBranchComparison(old string) KEP.ConditionInfo_BranchCondition_ComparisonEnum {
//	switch old {
//
//	case ui.ConditionOperatorContains:
//		return KEP.ConditionInfo_BranchCondition_CONTAINS
//	case ui.ConditionOperatorEqual:
//		return KEP.ConditionInfo_BranchCondition_EQ
//	case ui.ConditionOperatorNotEqual:
//		return KEP.ConditionInfo_BranchCondition_NE
//	case ui.ConditionOperatorNotContains:
//		return KEP.ConditionInfo_BranchCondition_NOT_CONTAINS
//	case ui.ConditionOperatorMoreThan:
//		return KEP.ConditionInfo_BranchCondition_GT
//	case ui.ConditionOperatorLessThan:
//		return KEP.ConditionInfo_BranchCondition_LT
//	case ui.ConditionOperatorFilled:
//		return KEP.ConditionInfo_BranchCondition_IS_SET
//	case ui.ConditionOperatorUnfilled:
//		return KEP.ConditionInfo_BranchCondition_NOT_SET
//	default:
//		return KEP.ConditionInfo_BranchCondition_UNSPECIFIED
//	}
//}
//
//func (t *UpgradeTaskFlow) upgradeEdge(oldEdges []*ui.Edge) []*KEP.Edge {
//	newEdges := make([]*KEP.Edge, 0, len(oldEdges))
//	for _, old := range oldEdges {
//		newEdges = append(newEdges, &KEP.Edge{
//			EdgeID:       old.EdgeID,
//			Source:       old.Source,
//			Target:       old.Target,
//			SourceAnchor: int32(old.SourceAnchor),
//			TargetAnchor: int32(old.TargetAnchor),
//			Label:        old.Label,
//		})
//	}
//	return newEdges
//}
//
////func (t *UpgradeTaskFlow) getCurrentSlotInfos(ctx context.Context) (map[string]*entity.SlotMigrationInfo, error) {
////	slotNames := make([]string, 0)
////	for k := range t.OldTreeSlotName {
////		slotNames = append(slotNames, k)
////	}
////	slotInfoMap, err := db.GetImportSlotInfos(ctx, t.botBizId, slotNames)
////	if err != nil {
////		return nil, err
////	}
////	return slotInfoMap, nil
////}
//
//// fillCreateSlotParams 填充需要新建的的槽位信息
////func (t *UpgradeTaskFlow) fillCreateSlotParams(ctx context.Context) ([]*entity.SlotMigrationInfo, error) {
////
////	slotRows := make([]*entity.SlotRow, 0)
////	for k := range t.OldTreeSlotName {
////		slotRows = append(slotRows, &entity.SlotRow{
////			IntentID:     "",
////			SlotID:       "",
////			SlotName:     k,
////			SlotDesc:     "",
////			SlotExamples: "",
////			EntityInfo:   "",
////		})
////	}
////
////	// 查询现有槽位信息
////	slotInfoMap, err := t.getCurrentSlotInfos(ctx)
////	if err != nil {
////		return nil, err
////	}
////
////	// 槽位信息校验
////	slotParams := make([]*entity.SlotMigrationInfo, 0)
////	for _, slot := range slotRows {
////		var newSlotID, newSlotEntityInfo string
////
////		importSlotInfo, err := slot.ConvertToSlotMigrationInfo()
////		if err != nil {
////			return nil, err
////		}
////
////		existSlotInfo, ok := slotInfoMap[importSlotInfo.SlotName]
////		if ok {
////			// 现有同名槽位判断
////			if existSlotInfo.IsEqual(importSlotInfo) {
////				// 槽位复用
////				newSlotID = existSlotInfo.SlotID
////				newSlotEntityInfo, _ = jsoniter.MarshalToString(existSlotInfo.EntityInfo)
////				log.InfoContextf(ctx, "fillCreateSlotParams Slot Reuse, importSlotID:%s, existSlotID:%s",
////					importSlotInfo.SlotID, existSlotInfo.SlotID)
////			} else {
////				// 槽位冲突
////				newSlotID = importSlotInfo.SlotID
////				newSlotEntityInfo, _ = jsoniter.MarshalToString(importSlotInfo.EntityInfo)
////				log.WarnContextf(ctx, "fillCreateSlotParams Slot Conflict, importSlotID:%s, existSlotID:%s",
////					importSlotInfo.SlotID, existSlotInfo.SlotID)
////			}
////		} else {
////			// 新生成的槽位｜实体｜词条信息
////			entityInfos := importSlotInfo.EntityInfo
////			for i := range entityInfos {
////				if entityInfos[i].EntityType == entity.SlotLevelSYS {
////					// 系统实体无需新建
////					continue
////				} else {
////					// 自定义实体需新建
////
////					// 新生成实体ID
////					entityInfos[i].EntityID = idgenerator.NewUUID()
////					// 新生成词条ID
////					for j := range entityInfos[i].Entries {
////						entityInfos[i].Entries[j].EntryID = idgenerator.NewUUID()
////					}
////				}
////			}
////			newSlotID = idgenerator.NewUUID()
////			newSlotEntityInfo, _ = jsoniter.MarshalToString(entityInfos)
////
////			// 组装槽位信息
////			slotParam := &entity.SlotMigrationInfo{
////				SlotID:       newSlotID,
////				SlotName:     importSlotInfo.SlotName,
////				SlotDesc:     importSlotInfo.SlotDesc,
////				SlotExamples: importSlotInfo.SlotExamples,
////				EntityInfo:   entityInfos,
////			}
////			slotParams = append(slotParams, slotParam)
////		}
////
////		// 任务流Json SlotID替换
////		//paramData.TaskFlow.DialogJson = strings.ReplaceAll(paramData.TaskFlow.DialogJson, slot.SlotID, newSlotID)
////
////		// 旧槽位ID替换
////		slot.SlotID = newSlotID
////		// 旧实体信息替换
////		slot.EntityInfo = newSlotEntityInfo
////
////	}
////	return slotParams, nil
////}
