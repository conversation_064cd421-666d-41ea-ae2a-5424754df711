package compatible

import (
	"context"
	"encoding/json"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
)

// [升级v1.7对话树的协议到 v2.1]

func Test_upgradeTaskFlowJson(t *testing.T) {
	type args struct {
		ctx          context.Context
		taskFlowJson string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		//want    *KEP.TaskFlow
	}{
		//{
		//	name: "答案节点包含 field和 node_id的",
		//	args: args{
		//		ctx:          trpc.BackgroundContext(),
		//		taskFlowJson: oldJson,
		//	},
		//},
		{
			name: "奥迪",
			args: args{
				ctx:          trpc.BackgroundContext(),
				taskFlowJson: "{\"TaskFlowID\":\"aaecafaa-81c3-45e4-8e6b-871a55e46945\", \"TaskFlowName\":\"发布测试1-1\", \"TaskFlowDesc\":\"\", \"Nodes\":[{\"NodeID\":\"start\", \"NodeName\":\"开始\", \"NodeType\":\"START\", \"StartNodeData\":{}, \"Branches\":[{\"BranchID\":\"edge-0.0306907317931048861705478946903\", \"BranchType\":\"DIRECT\", \"ConditionInfo\":{\"ConditionsLogic\":\"UNSPECIFIED\", \"ConditionInfo\":[]}, \"NextNodeID\":\"4cf799a1-e3e6-5ca7-7f3b-53caf51bacce\", \"PrevNodeID\":\"start\"}]}, {\"NodeID\":\"4cf799a1-e3e6-5ca7-7f3b-53caf51bacce\", \"NodeName\":\"信息澄清3\", \"NodeType\":\"REQUEST\", \"RequestNodeData\":{\"Request\":{\"ID\":\"b8c2a397-b76e-f596-abe7-5a6037edc7e3\", \"RequestType\":\"SLOT\", \"RequestValue\":\"ba065b86-1a82-4894-a706-5cb48e874a36\", \"AskType\":\"LLM\", \"CustomAsk\":\"\", \"LLMAskPreview\":[\"您好，请问您需要订机票吗？如果是，请告诉我您的具体需求，例如出发地、目的地、出行日期等信息。\"], \"IsRequired\":true}, \"EnableCard\":false, \"CardFrom\":\"INPUT\", \"InputCard\":{\"InputCardContent\":[]}, \"ApiCardRef\":{\"ParamID\":\"\"}}, \"Branches\":[{\"BranchID\":\"edge-0.60012720458594121705479060222\", \"BranchType\":\"DIRECT\", \"ConditionInfo\":{\"ConditionsLogic\":\"UNSPECIFIED\", \"ConditionInfo\":[]}, \"NextNodeID\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\", \"PrevNodeID\":\"4cf799a1-e3e6-5ca7-7f3b-53caf51bacce\"}]}, {\"NodeID\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\", \"NodeName\":\"信息澄清5\", \"NodeType\":\"REQUEST\", \"RequestNodeData\":{\"Request\":{\"ID\":\"16e8bc32-3c2b-5d5a-abe9-21ef5336deab\", \"RequestType\":\"SLOT\", \"RequestValue\":\"a43d0219-7065-4647-ba30-b090eb09f80a\", \"AskType\":\"LLM\", \"CustomAsk\":\"\", \"LLMAskPreview\":[\"您好，请问您需要订什么航线的机票？出行日期是什么时候呢？\"], \"IsRequired\":true}, \"EnableCard\":false, \"CardFrom\":\"INPUT\", \"InputCard\":{\"InputCardContent\":[]}, \"ApiCardRef\":{\"ParamID\":\"\"}}, \"Branches\":[{\"BranchID\":\"edge-0.79494428559993021705479081077\", \"BranchType\":\"DIRECT\", \"ConditionInfo\":{\"ConditionsLogic\":\"UNSPECIFIED\", \"ConditionInfo\":[]}, \"NextNodeID\":\"48998990-6efb-4577-29de-83574bc867e2\", \"PrevNodeID\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\"}, {\"BranchID\":\"edge-0.352964105904694851705481194952\", \"BranchType\":\"CUSTOM\", \"ConditionInfo\":{\"ConditionsLogic\":\"AND\", \"ConditionInfo\":[{\"ConditionsLogic\":\"UNSPECIFIED\", \"ConditionInfo\":[], \"Condition\":{\"SourceType\":\"SLOT\", \"SlotValueData\":{\"SlotID\":\"ba065b86-1a82-4894-a706-5cb48e874a36\"}, \"Comparison\":\"CONTAINS\", \"InputValues\":[\"\"]}}]}, \"NextNodeID\":\"152f9686-317b-1d06-71d4-797999d3c0eb\", \"PrevNodeID\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\"}]}, {\"NodeID\":\"152f9686-317b-1d06-71d4-797999d3c0eb\", \"NodeName\":\"信息澄清6\", \"NodeType\":\"REQUEST\", \"RequestNodeData\":{\"Request\":{\"ID\":\"511ee8fc-3acd-d1b7-6e4a-34518fd1ee84\", \"RequestType\":\"SLOT\", \"RequestValue\":\"28405a63-741e-48c0-9637-631b82ab792b\", \"AskType\":\"LLM\", \"CustomAsk\":\"\", \"LLMAskPreview\":[\"您好，根据您的信息，我注意到有一个名为“sss”的槽位，但没有提供具体的信息。请问您能否提供更多关于“sss”的详细信息？\"], \"IsRequired\":true}, \"EnableCard\":false, \"CardFrom\":\"INPUT\", \"InputCard\":{\"InputCardContent\":[]}, \"ApiCardRef\":{\"ParamID\":\"\"}}, \"Branches\":[{\"BranchID\":\"edge-0.73958025794174361705481294172\", \"BranchType\":\"DIRECT\", \"ConditionInfo\":{\"ConditionsLogic\":\"UNSPECIFIED\", \"ConditionInfo\":[]}, \"NextNodeID\":\"c66a3527-6d30-a703-a9ce-1a0bcd5d3dc2\", \"PrevNodeID\":\"152f9686-317b-1d06-71d4-797999d3c0eb\"}]}, {\"NodeID\":\"48998990-6efb-4577-29de-83574bc867e2\", \"NodeName\":\"结束回复4\", \"NodeType\":\"ANSWER\", \"AnswerNodeData\":{\"AnswerType\":\"LLM\", \"LLMAnswerData\":{\"Preview\":[\"尊敬的客户，如果您满足以下条件，就可以触发以下结果：首先，您需要在网站上注册并登录；其次，您需要选择目的地和出发日期；最后，您需要填写乘客信息并选择支付方式。如果您满足以上条件，就可以轻松地订机票了。\"]}, \"InputAnswerData\":{\"Preview\":\"\"}, \"DocAnswerData\":{\"Preview\":\"\", \"RefInfo\":[]}}, \"Branches\":[]}, {\"NodeID\":\"c66a3527-6d30-a703-a9ce-1a0bcd5d3dc2\", \"NodeName\":\"结束回复5\", \"NodeType\":\"ANSWER\", \"AnswerNodeData\":{\"AnswerType\":\"INPUT\", \"LLMAnswerData\":{\"Preview\":[\"尊敬的客户，如果您满足以下条件，就可以触发以下结果：首先，您需要在网站上注册并登录；其次，您需要选择目的地和出发日期；最后，您需要填写乘客信息并选择支付方式。如果您满足以上条件，就可以轻松地订机票了。\"]}, \"InputAnswerData\":{\"Preview\":\"<p>111</p>\"}, \"DocAnswerData\":{\"Preview\":\"\", \"RefInfo\":[]}}, \"Branches\":[]}], \"Edges\":[{\"EdgeID\":\"edge-0.0306907317931048861705478946903\", \"Source\":\"start\", \"Target\":\"4cf799a1-e3e6-5ca7-7f3b-53caf51bacce\", \"SourceAnchor\":0, \"TargetAnchor\":0, \"Label\":\"\"}, {\"EdgeID\":\"edge-0.60012720458594121705479060222\", \"Source\":\"4cf799a1-e3e6-5ca7-7f3b-53caf51bacce\", \"Target\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\", \"SourceAnchor\":1, \"TargetAnchor\":0, \"Label\":\"\"}, {\"EdgeID\":\"edge-0.79494428559993021705479081077\", \"Source\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\", \"Target\":\"48998990-6efb-4577-29de-83574bc867e2\", \"SourceAnchor\":1, \"TargetAnchor\":0, \"Label\":\"\"}, {\"EdgeID\":\"edge-0.352964105904694851705481194952\", \"Source\":\"3b7cb1bd-fcec-7350-9d41-910e5364b21b\", \"Target\":\"152f9686-317b-1d06-71d4-797999d3c0eb\", \"SourceAnchor\":1, \"TargetAnchor\":0, \"Label\":\"订机票 包含\"}, {\"EdgeID\":\"edge-0.73958025794174361705481294172\", \"Source\":\"152f9686-317b-1d06-71d4-797999d3c0eb\", \"Target\":\"c66a3527-6d30-a703-a9ce-1a0bcd5d3dc2\", \"SourceAnchor\":1, \"TargetAnchor\":0, \"Label\":\"\"}]}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonStr := tt.args.taskFlowJson
			taskflow := &entity.TaskFlow{
				Uin:              "600000562346",
				SubUin:           "600000562346",
				DialogJsonDraft: jsonStr,
				DialogJsonEnable: jsonStr,
			}
			flow := NewUpgradeTaskFlowV24("1747446004305481728", taskflow)
			_ = flow.PrepareUserIdVarParamInfo(tt.args.ctx)
			got, err := flow.upgradeTaskFlowJson(tt.args.ctx, jsonStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("upgradeTaskFlowJson() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//jsonStr := json0.Marshal2StringNoErr(got)
			//t.Logf(jsonStr)

			newJson, err := TaskFlowToJsonUpgrade(got)
			//newJson = strings.ReplaceAll(newJson, "'ConditionsLogic':'UNSPECIFIED','ConditionInfo':[]", "{}")

			if err != nil {
				t.Fatalf("Failed to marshal JSON: %v", err)
			}
			t.Logf(newJson)
			//_, err = protoutil.JsonToTaskFlow(newJson)
			tf := TaskFlow{}
			err = json.Unmarshal([]byte(newJson), &tf)
			if err != nil {
				t.Fatalf("Failed to marshal JSON: %v", err)
			}

			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("upgradeTaskFlowJson() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

//var oldJson = `{
//  "TaskFlowID": "6c16ebd7-0cdd-40e1-ab7b-a10c2520b464",
//  "TaskFlowName": "查订单",
//  "Nodes": [
//    {
//      "NodeID": "start",
//      "NodeType": "startNode",
//      "NodeName": "开始",
//      "NodeUI": {},
//      "Branches": [
//        {
//          "BranchID": "edge-0.19458335676105221705581844413",
//          "BranchType": "direct",
//          "Conditions": [],
//          "ConditionsLogic": "And",
//          "NextNodeID": "25b4683a-0c42-4855-d87b-22762f5757ef",
//          "PrevNodeID": "start"
//        }
//      ]
//    },
//    {
//      "NodeID": "25b4683a-0c42-4855-d87b-22762f5757ef",
//      "NodeType": "RequestNode",
//      "NodeName": "信息澄清1",
//      "NodeUI": {},
//      "Branches": [
//        {
//          "BranchID": "edge-0.76035242182832061705581911876",
//          "BranchType": "custom",
//          "Conditions": [
//            {
//              "ConditionID": "d5e4b389-1476-3361-2c86-106c8b27eb5e",
//              "ConditionSource": "RequiredInfo",
//              "NodeID": "25b4683a-0c42-4855-d87b-22762f5757ef",
//              "NodeName": "信息澄清1",
//              "ParamID": "b6a814ce-1e3c-ef0b-a087-23866196cd5d",
//              "ParamName": "订单数量",
//              "ParamVarName": "field_a71ccca9-5a7e-be73-33d6-231dd1d860d9",
//              "VarType": "string",
//              "Operator": "==",
//              "Values": [
//                "1"
//              ]
//            }
//          ],
//          "ConditionsLogic": "And",
//          "NextNodeID": "adddba19-283b-021c-a1a5-1b3da8e71b21",
//          "PrevNodeID": "25b4683a-0c42-4855-d87b-22762f5757ef"
//        },
//        {
//          "BranchID": "edge-0.75786281194177031705581945448",
//          "BranchType": "custom",
//          "Conditions": [
//            {
//              "ConditionID": "2e55cda7-b752-3393-c99b-cc031dc4b1a4",
//              "ConditionSource": "RequiredInfo",
//              "NodeID": "25b4683a-0c42-4855-d87b-22762f5757ef",
//              "NodeName": "信息澄清1",
//              "ParamID": "b6a814ce-1e3c-ef0b-a087-23866196cd5d",
//              "ParamName": "订单数量",
//              "ParamVarName": "field_a71ccca9-5a7e-be73-33d6-231dd1d860d9",
//              "VarType": "string",
//              "Operator": "==",
//              "Values": [
//                "2"
//              ]
//            }
//          ],
//          "ConditionsLogic": "And",
//          "NextNodeID": "f2269a00-927f-5404-3c20-7e9929126ee0",
//          "PrevNodeID": "25b4683a-0c42-4855-d87b-22762f5757ef"
//        }
//      ],
//      "NodeData": {
//        "NodeName": "",
//        "RequiredInfo": {
//          "SlotID": "b6a814ce-1e3c-ef0b-a087-23866196cd5d",
//          "SlotName": "订单数量",
//          "SlotVarName": "field_a71ccca9-5a7e-be73-33d6-231dd1d860d9",
//          "ParamType": "string",
//          "ProbeConfig": "auto",
//          "IsRequired": true,
//          "ProbePreview": [
//            "您好，请问您需要查询的订单数量是多少？"
//          ],
//          "ProbeCustom": ""
//        },
//        "OptionCards": true,
//        "OptionType": "custom",
//        "APIParams": [],
//        "OptionContents": [
//          "1",
//          "2"
//        ]
//      }
//    },
//    {
//      "NodeID": "adddba19-283b-021c-a1a5-1b3da8e71b21",
//      "NodeType": "APINode",
//      "NodeName": "智能接口1",
//      "NodeUI": {},
//      "Branches": [
//        {
//          "BranchID": "edge-0.71448485634041831705582013829",
//          "BranchType": "direct",
//          "Conditions": [],
//          "ConditionsLogic": "And",
//          "NextNodeID": "3272e401-95d8-e14f-9101-82a49225529d",
//          "PrevNodeID": "adddba19-283b-021c-a1a5-1b3da8e71b21"
//        }
//      ],
//      "NodeData": {
//        "NodeName": "",
//        "APIPath": {
//          "Path": "http://*************:18080",
//          "Method": "GET"
//        },
//        "RequiredInfo": [],
//        "APIResponseParams": [
//          {
//            "ParamID": "23e4387f-6d81-612e-06f2-6abe00f5aac4",
//            "ParamName": "订单金额",
//            "ParamVarName": "Int",
//            "ParamType": "int",
//            "ParamPath": "Int",
//            "IsRequired": true
//          }
//        ],
//        "InvokeConfirm": false
//      }
//    },
//    {
//      "NodeID": "f2269a00-927f-5404-3c20-7e9929126ee0",
//      "NodeType": "RequestNode",
//      "NodeName": "信息澄清3",
//      "NodeUI": {},
//      "Branches": [
//        {
//          "BranchID": "edge-0.179180771579889871705582036625",
//          "BranchType": "direct",
//          "Conditions": [],
//          "ConditionsLogic": "And",
//          "NextNodeID": "3272e401-95d8-e14f-9101-82a49225529d",
//          "PrevNodeID": "f2269a00-927f-5404-3c20-7e9929126ee0"
//        }
//      ],
//      "NodeData": {
//        "NodeName": "",
//        "RequiredInfo": {
//          "SlotID": "3b82238e-8aa4-42fb-a523-fd32fb53fc85",
//          "SlotName": "订单类型",
//          "SlotVarName": "field_a8d4a6fe-b281-9351-c07d-acff1b35d524",
//          "ParamType": "string",
//          "ProbeConfig": "custom",
//          "IsRequired": true,
//          "ProbePreview": [
//            "您好，请问您需要查询哪种类型的订单？"
//          ],
//          "ProbeCustom": "<p>请问你要查什么类型的订单？</p>"
//        },
//        "OptionCards": false,
//        "OptionType": "custom",
//        "APIParams": [],
//        "OptionContents": []
//      }
//    },
//    {
//      "NodeID": "3272e401-95d8-e14f-9101-82a49225529d",
//      "NodeType": "RequestNode",
//      "NodeName": "信息澄清4",
//      "NodeUI": {},
//      "Branches": [
//        {
//          "BranchID": "edge-0.68488589812215111705582041335",
//          "BranchType": "direct",
//          "Conditions": [],
//          "ConditionsLogic": "And",
//          "NextNodeID": "a7a46703-9f50-70c0-006a-f9d48a6860f8",
//          "PrevNodeID": "3272e401-95d8-e14f-9101-82a49225529d"
//        }
//      ],
//      "NodeData": {
//        "NodeName": "",
//        "RequiredInfo": {
//          "SlotID": "11c0c4da-87e9-f128-ab29-63197601694c",
//          "SlotName": "订单时间",
//          "SlotVarName": "field_4c7572b5-a897-fe12-9c17-6d630d62da36",
//          "ParamType": "string",
//          "ProbeConfig": "custom",
//          "IsRequired": true,
//          "ProbePreview": [
//            "您好，请问您需要查询的订单时间是什么时间段呢？"
//          ],
//          "ProbeCustom": "<p>请问你要查什么时间的订单？</p>"
//        },
//        "OptionCards": false,
//        "OptionType": "custom",
//        "APIParams": [],
//        "OptionContents": []
//      }
//    },
//    {
//      "NodeID": "a7a46703-9f50-70c0-006a-f9d48a6860f8",
//      "NodeType": "AnswerNode",
//      "NodeName": "结束回复2",
//      "NodeUI": {},
//      "Branches": [],
//      "NodeData": {
//        "NodeName": "",
//        "AnswerSource": "custom",
//        "AnswerCustom": "<p>订单数量：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_a71ccca9-5a7e-be73-33d6-231dd1d860d9\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E6%95%B0%E9%87%8F%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%2225b4683a-0c42-4855-d87b-22762f5757ef%22%7D\">@订单数量</span></p><p>订单金额：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"adddba19-283b-021c-a1a5-1b3da8e71b21_Int\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A31-%E8%AE%A2%E5%8D%95%E9%87%91%E9%A2%9D%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%22adddba19-283b-021c-a1a5-1b3da8e71b21%22%7D\">$智能接口1-订单金额</span></p><p>订单类型：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_a8d4a6fe-b281-9351-c07d-acff1b35d524\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E7%B1%BB%E5%9E%8B%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%22f2269a00-927f-5404-3c20-7e9929126ee0%22%7D\">@订单类型</span></p><p>订单时间：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_4c7572b5-a897-fe12-9c17-6d630d62da36\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E6%97%B6%E9%97%B4%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%223272e401-95d8-e14f-9101-82a49225529d%22%7D\">@订单时间</span></p>",
//        "AnswerPreview": [
//          "您好，如果您需要查看订单信息，请按照以下步骤操作：\n1.登录您的账户。\n2.点击“订单”选项。\n3.在订单列表中找到您需要查看的订单。\n4.点击该订单，查看订单详情。\n请注意，如果您的订单状态为“已发货”或“已收货”，则无法查看订单详情。如果您有任何其他问题，请随时联系我们的客服团队，我们将竭诚为您服务。"
//        ],
//        "AutoComplete": true
//      }
//    }
//  ],
//  "Edges": [
//    {
//      "EdgeID": "edge-0.19458335676105221705581844413",
//      "Source": "start",
//      "Target": "25b4683a-0c42-4855-d87b-22762f5757ef",
//      "SourceAnchor": 0,
//      "TargetAnchor": 0
//    },
//    {
//      "EdgeID": "edge-0.76035242182832061705581911876",
//      "Source": "25b4683a-0c42-4855-d87b-22762f5757ef",
//      "Target": "adddba19-283b-021c-a1a5-1b3da8e71b21",
//      "SourceAnchor": 1,
//      "TargetAnchor": 0,
//      "Label": "订单数量 = 1"
//    },
//    {
//      "EdgeID": "edge-0.75786281194177031705581945448",
//      "Source": "25b4683a-0c42-4855-d87b-22762f5757ef",
//      "Target": "f2269a00-927f-5404-3c20-7e9929126ee0",
//      "SourceAnchor": 1,
//      "TargetAnchor": 0,
//      "Label": "订单数量 = 2"
//    },
//    {
//      "EdgeID": "edge-0.71448485634041831705582013829",
//      "Source": "adddba19-283b-021c-a1a5-1b3da8e71b21",
//      "Target": "3272e401-95d8-e14f-9101-82a49225529d",
//      "SourceAnchor": 1,
//      "TargetAnchor": 0
//    },
//    {
//      "EdgeID": "edge-0.179180771579889871705582036625",
//      "Source": "f2269a00-927f-5404-3c20-7e9929126ee0",
//      "Target": "3272e401-95d8-e14f-9101-82a49225529d",
//      "SourceAnchor": 1,
//      "TargetAnchor": 0
//    },
//    {
//      "EdgeID": "edge-0.68488589812215111705582041335",
//      "Source": "3272e401-95d8-e14f-9101-82a49225529d",
//      "Target": "a7a46703-9f50-70c0-006a-f9d48a6860f8",
//      "SourceAnchor": 1,
//      "TargetAnchor": 0
//    }
//  ]
//}`

func Test_upgradeRichText(t *testing.T) {
	type args struct {
		ctx    context.Context
		source string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "正确的",
			args: args{
				ctx:    trpc.BackgroundContext(),
				source: "<p>订单数量：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_a71ccca9-5a7e-be73-33d6-231dd1d860d9\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E6%95%B0%E9%87%8F%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%2225b4683a-0c42-4855-d87b-22762f5757ef%22%7D\">@订单数量</span></p><p>订单金额：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"adddba19-283b-021c-a1a5-1b3da8e71b21_Int\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A31-%E8%AE%A2%E5%8D%95%E9%87%91%E9%A2%9D%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%22adddba19-283b-021c-a1a5-1b3da8e71b21%22%7D\">$智能接口1-订单金额</span></p><p>订单类型：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_a8d4a6fe-b281-9351-c07d-acff1b35d524\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E7%B1%BB%E5%9E%8B%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%22f2269a00-927f-5404-3c20-7e9929126ee0%22%7D\">@订单类型</span></p><p>订单时间：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"field_4c7572b5-a897-fe12-9c17-6d630d62da36\" data-info=\"%7B%22name%22%3A%22%E8%AE%A2%E5%8D%95%E6%97%B6%E9%97%B4%22%2C%22type%22%3A%22%22%2C%22nodeId%22%3A%223272e401-95d8-e14f-9101-82a49225529d%22%7D\">@订单时间</span></p>",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//if got := upgradeRichText(tt.args.ctx, tt.args.source); got != tt.want {
			//	t.Errorf("upgradeRichText() = %v, want %v", got, tt.want)
			//}
		})
	}
}
