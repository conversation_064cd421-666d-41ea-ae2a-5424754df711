// bot-task-config-server
//
// @(#)upgrade_v2.4.go  星期二, 七月 09, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package compatible

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/PuerkitoBio/goquery"
)

const (
	UserId    = "UserID"
	CustomVar = "CUSTOM_VAR"
)

// 2.4 刷数据的位置
// 1. 富文本
//   信息澄清节点（询问节点）
//		RequestNodeData-Request-CustomAsk
//
//	  答案节点
//	   AnswerNodeData-InputAnswerData-Preview : 自定义精准询问
//     AnswerNodeData - LLMAnswerData - Prompt: 提示词
//
// 2.分支条件及入参:  Headers / Request
//
// 分支条件的系统参数: Branches
// 	  SystemValueData-Value ==> CustomVarValueData: xxx-xxx
//	  SourceType ===> CUSTOM_VAR
//    Edges - Label "用户ID"

// 3. 收集实体的 引用词条 valueSource ====> ComparisonValueSourceType

// UpgradeTaskFlow24 2.4 刷数据上下文
type UpgradeTaskFlow24 struct {
	botBizId    string
	oldTaskFlow *entity.TaskFlow // 旧的草稿态的json
	isUserID    bool             // 判断是否有UserID
	UserIDKey   string
	UserIDValue string
}

// Upgrade_V24 ...
func Upgrade_V24(ctx context.Context, botBizId string) error {
	sid := util.RequestID(ctx)
	oldTaskFlows, err := db.FetchTaskFlowV24(ctx, botBizId, "")
	if err != nil {
		log.ErrorContextf(ctx, "UpgradeProtoVersionFrom21To24|FetchTaskFlow|err:%+v", err)
		return err
	}

	for _, oldFlow := range oldTaskFlows {
		// 先判断草稿态的json是否存在UserID，enable态的存在，草稿draft态的必定存在
		flowCtx := trpc.CloneContext(ctx)
		log.InfoContextf(flowCtx, "[UpgradeProtoVersionFrom21To24]bot:%s|开始升级 "+
			"flowID:%s", botBizId, oldFlow.FlowID)
		t := NewUpgradeTaskFlowV24(botBizId, oldFlow)

		// 判断准备UserID
		err = t.PrepareUserIdVarParamInfo(flowCtx)
		if err != nil {
			log.ErrorContextf(flowCtx, "UpgradeProtoVersionFrom21To24|"+
				"PrepareUserIdVarParamInfo|flowId:%s|err:%+v", oldFlow.FlowID, err)
			return err
		}
		var wg sync.WaitGroup
		var newDraftJsonStr string
		var newEnableJsonStr string
		errChan := make(chan error, 2)

		wg.Add(2) // 增加计数器的值

		// 启动协程更新草稿json
		go func() {
			defer wg.Done()
			newDraftJsonStr, err = t.GetUpgradeDraftJson(flowCtx, oldFlow)
			log.InfoContextf(ctx, "[UpgradeProtoVersionFrom21To24]|sid:%s|"+
				"GetUpgradeDraftJson:%s", sid, newDraftJsonStr)
			if err != nil {
				errChan <- err
			}
		}()

		// 启动协程更新enable json
		go func() {
			defer wg.Done()
			newEnableJsonStr, err = t.getUpgradeEnableJson(flowCtx, botBizId, oldFlow)
			log.InfoContextf(ctx, "[UpgradeProtoVersionFrom21To24]|sid:%s|getUpgradeEnableJson:%s", sid, newEnableJsonStr)
			if err != nil {
				errChan <- err
			}
		}()

		// 等待所有协程完成
		wg.Wait()

		select {
		case err = <-errChan:
			log.ErrorContextf(flowCtx, "sid:%s|UpgradeProtoVersionFrom21To24|err:%+v", sid, err)
			return err
		default:
			tf := entity.ModifyTaskFlowParams{
				FlowID:           oldFlow.FlowID,
				IntentName:       oldFlow.IntentName,
				IntentDesc:       oldFlow.IntentDesc,
				CategoryID:       oldFlow.CategoryID,
				RobotId:          botBizId,
				DialogJsonDraft:  newDraftJsonStr,
				DialogJsonEnable: newEnableJsonStr,
				Uin:              oldFlow.Uin,
				SubUin:           oldFlow.SubUin,
				UpdateTime:       time.Now(),
				IsEnable:         uint32(KEP.SaveTaskFlowReq_DRAFT),
			}
			if err = db.SaveUpgrade_V24(ctx, tf); err != nil {
				log.ErrorContextf(flowCtx, "sid:%s|UpgradeProtoVersionFrom21To24|err:%+v", sid, err)
				return err
			}

			// ======= 升级 history 表 ====
			tfps, err := db.FetchPublishedTaskFlowV24(ctx, oldFlow.FlowID)
			if err != nil {
				log.ErrorContextf(flowCtx, "sid:%s|UpgradeProtoVersionFrom21To24|"+
					"FetchPublishedTaskFlowV24|err:%+v", sid, err)
				return err
			}
			// 更新tfps json
			for _, tf := range tfps {
				if len(tf.FlowJson) > 0 {
					tfpjson, err := t.upgradeTaskFlowJson(ctx, tf.FlowJson)
					if err != nil {
						log.ErrorContextf(flowCtx, "UpgradeProtoVersionFrom21To24|"+
							"upgradeTaskFlowJson|flowId:%s|err:%+v", tf.FlowID, err)
						//return err
					}
					log.InfoContextf(flowCtx, "UpgradeProtoVersionFrom21To24|tfpjson:%+v", tfpjson)
					tfpjsonStr, err := TaskFlowToJsonUpgrade(tfpjson)
					if err != nil {
						log.ErrorContextf(flowCtx, "UpgradeProtoVersionFrom21To24|TaskFlowToJsonUpgrade|"+
							"flowId:%s|err:%+v", tfpjson.TaskFlowID, err)
						//return err
					}
					log.InfoContextf(flowCtx, "UpgradeProtoVersionFrom21To24|tfpjsonStr:%s", tfpjsonStr)

					// 保存更新后的历史版本
					err = db.SaveUpgradePublishedTaskFlowV24(ctx, tf.ID, tfpjsonStr)
					if err != nil {
						log.InfoContextf(flowCtx, "UpgradeProtoVersionFrom21To24|"+
							"SaveUpgradePublishedTaskFlowV24|err:%+v", err)
					}
				}

			}

			// ====== 升级 prod json ====
			ptf, err := db.FetchProdTaskFlowV24(ctx, oldFlow.FlowID)
			if err != nil {
				log.ErrorContextf(flowCtx, "sid:%s|FetchProdTaskFlowV24|"+
					"FetchPublishedTaskFlowV24|err:%+v", sid, err)
				return err
			}
			if len(ptf) > 0 && len(oldFlow.DialogJsonEnable) > 0 {
				err = db.SaveUpgradeProdTaskFlowV24(ctx, oldFlow.ID, tf)
				if err != nil {
					log.ErrorContextf(flowCtx, "sid:%s|SaveUpgradeProdTaskFlowV24|"+
						"SaveUpgradeProtoVersionFrom21To24|err:%+v", sid, err)
					return err
				}
			}
		}

		return nil
	}

	return nil
}
func (t *UpgradeTaskFlow24) getUpgradeEnableJson(ctx context.Context, botBizId string,
	oldFlow *entity.TaskFlow) (string, error) {
	// 升级对话树
	if len(oldFlow.DialogJsonEnable) == 0 {
		return "", nil
	}
	newEnableJson, err := t.upgradeTaskFlowJson(ctx, oldFlow.DialogJsonEnable)
	if err != nil {
		log.ErrorContextf(ctx, "UpgradeProtoVersionFrom21To24|upgradeTaskFlowJson|"+
			"flowId:%s|err:%+v", oldFlow.FlowID, err)
		return "", err
	}

	newEnableJsonStr, err := TaskFlowToJsonUpgrade(newEnableJson)
	if err != nil {
		log.ErrorContextf(ctx, "UpgradeProtoVersionFrom21To24|jsonToStr|flowId:%s|err:%+v",
			newEnableJson.TaskFlowID, err)
		return "", err
	}
	log.InfoContextf(ctx, "UpgradeProtoVersionFrom21To24|newEnableJsonStr:%s", newEnableJsonStr)

	return newEnableJsonStr, nil
}

// GetUpgradeDraftJson ...
func (t *UpgradeTaskFlow24) GetUpgradeDraftJson(ctx context.Context, oldFlow *entity.TaskFlow) (string, error) {
	// 升级对话树
	// 升级 草稿态
	newDraftJson, err := t.upgradeTaskFlowJson(ctx, oldFlow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "UpgradeProtoVersionFrom21To24|upgradeTaskFlowJson|flowId:%s|err:%+v",
			oldFlow.FlowID, err)
		return "", nil
	}
	newDraftJsonStr, err := TaskFlowToJsonUpgrade(newDraftJson)

	if err != nil {
		log.ErrorContextf(ctx, "UpgradeProtoVersionFrom21To24|jsonToStr|Draft|flowId:%s|err:%+v",
			newDraftJson.TaskFlowID, err)
		return "", nil
	}
	return newDraftJsonStr, nil
}

// NewUpgradeTaskFlowV24 ...
func NewUpgradeTaskFlowV24(botBizId string, taskflow *entity.TaskFlow) *UpgradeTaskFlow24 {
	return &UpgradeTaskFlow24{
		botBizId:    botBizId,
		oldTaskFlow: taskflow,
	}
}

// upgradeTaskFlowJson 更新flowJson
func (t *UpgradeTaskFlow24) upgradeTaskFlowJson(ctx context.Context, tfJson string) (*TaskFlow, error) {
	sid := util.RequestID(ctx)
	hJson := TaskFlow{}

	var jsonStr string
	// DocType 原本是uint类型，草稿态有些json前端传的是 ""， 导致升级草稿态json序列化报错
	if strings.Contains(tfJson, "\"DocType\":\"\"") {
		jsonStr = strings.ReplaceAll(tfJson, "\"DocType\":\"\"", "\"DocType\":0")
	} else {
		jsonStr = tfJson
	}

	err := json0.UnmarshalStr(jsonStr, &hJson)

	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|flowId:%s|upgradeTaskFlowJson,JsonToTaskFlow|err:%s",
			sid, hJson.TaskFlowID, err.Error())
		return nil, err
	}
	nodes, err := t.upgradeNodes(ctx, hJson.Nodes)
	if err != nil {
		return nil, err
	}
	tf := &TaskFlow{
		TaskFlowID:   hJson.TaskFlowID,
		TaskFlowName: hJson.TaskFlowName,
		TaskFlowDesc: hJson.TaskFlowDesc,
		Nodes:        nodes,
		Edges:        t.upgradeEdge(hJson.Edges),
		Snapshot:     hJson.Snapshot,
		SessionMode:  hJson.SessionMode,
		ProtoVersion: KEP.TaskFlowProtoVersion_V2_4.String(),
	}
	return tf, nil
}

func (t *UpgradeTaskFlow24) upgradeNodes(ctx context.Context, oldNodes []*TaskFlowNode) ([]*TaskFlowNode, error) {
	newNodes := make([]*TaskFlowNode, 0)

	for _, old := range oldNodes {
		newTaskNodeData := &TaskFlowNode{
			NodeID:   old.NodeID,
			NodeName: old.NodeName,
			NodeType: old.NodeType,
			NodeUI:   old.NodeUI,
			Branches: t.upgradeBranches(old.Branches),
		}

		// 刷nodeType 数据
		switch newTaskNodeData.NodeType {
		case KEP.NodeType_START.String():
			startNode := old.StartNodeData
			newTaskNodeData.StartNodeData = startNode
		case KEP.NodeType_API.String():
			var apiData *APINodeData
			nodeStr := json0.Marshal2StringUnescapeHTMLNoErr(old.APINodeData)
			err := json0.UnmarshalStr(nodeStr, &apiData)
			if err != nil {
				log.ErrorContextf(ctx, "getSlotListFromOldTree|UnmarshalStr|err:%+v", err)
			}
			newApiNode, err := t.upgradeApiNodeData(ctx, apiData)
			if err != nil {
				return nil, err
			}
			newTaskNodeData.APINodeData = newApiNode
		case KEP.NodeType_REQUEST.String():
			var reqNode *RequestNodeData
			nodeStr := json0.Marshal2StringUnescapeHTMLNoErr(old.RequestNodeData)
			err := json0.UnmarshalStr(nodeStr, &reqNode)
			if err != nil {
				log.ErrorContextf(ctx, "getSlotListFromOldTree|UnmarshalStr|err:%+v", err)
			}
			newReqNode, err := t.upgradeRequestNodeData(ctx, reqNode)
			if err != nil {
				return nil, err
			}
			newTaskNodeData.RequestNodeData = newReqNode
		case KEP.NodeType_ANSWER.String():
			var ansNode *AnswerNodeData
			nodeStr := json0.Marshal2StringUnescapeHTMLNoErr(old.AnswerNodeData)
			err := json0.UnmarshalStr(nodeStr, &ansNode)
			if err != nil {
				log.ErrorContextf(ctx, "getSlotListFromOldTree|UnmarshalStr|err:%+v", err)
				return nil, err
			}
			newAnswerNode, err := t.upgradeAnswerNodeData(ctx, ansNode)
			if err != nil {
				return nil, err
			}
			newTaskNodeData.AnswerNodeData = newAnswerNode
		}
		newNodes = append(newNodes, newTaskNodeData)
	}

	return newNodes, nil
}

func (t *UpgradeTaskFlow24) upgradeAnswerNodeData(ctx context.Context, anNode *AnswerNodeData) (*AnswerNodeData, error) {
	if anNode != nil && anNode.LLMAnswerData.Prompt != "" {
		anNode.LLMAnswerData.Prompt = t.upgradeRichText(ctx, anNode.LLMAnswerData.Prompt)
	}
	if anNode != nil && anNode.InputAnswerData.Preview != "" {
		anNode.InputAnswerData.Preview = t.upgradeRichText(ctx, anNode.InputAnswerData.Preview)
	}
	return anNode, nil
}

// upgradeApiNodeData 更新 询问节点
func (t *UpgradeTaskFlow24) upgradeRequestNodeData(ctx context.Context, reqNode *RequestNodeData) (*RequestNodeData, error) {
	if reqNode != nil && reqNode.Request.CustomAsk != "" {
		reqNode.Request.CustomAsk = t.upgradeRichText(ctx, reqNode.Request.CustomAsk)
	}

	return reqNode, nil
}

// upgradeApiNodeData 更新 API节点
func (t *UpgradeTaskFlow24) upgradeApiNodeData(ctx context.Context, apiNode *APINodeData) (*APINodeData, error) {
	if apiNode != nil {
		apiNode.Request = t.upgradeApiNodeDataRequestParam(ctx, apiNode.Request)
		apiNode.Headers = t.upgradeApiNodeDataHeader(ctx, apiNode.Headers)
	}

	return apiNode, nil

}

func (t *UpgradeTaskFlow24) upgradeRichText(ctx context.Context, source string) string {

	if len(source) == 0 {
		return ""
	}
	log.InfoContextf(ctx, "[UpgradeProtoVersionFrom21To24]upgradeRichText|source:%s|", source)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
	if err != nil {
		log.ErrorContextf(ctx, "NewDocumentFromReader failed,err:%v", err)
		return ""
	}

	doc.Find("span[data-type='SYSTEM']").Each(func(i int, s *goquery.Selection) {
		// 富文本里面引用系统参数UserID与节点无关
		di := SpanDataInfo{}
		di.Text = UserId
		di.Name = UserId
		di.Type = CustomVar
		dataInfo := json0.Marshal2StringNoErr(di)
		log.InfoContextf(ctx, "convertUIDataByHtml|dataInfo-2=%s", dataInfo)
		dataInfo = url.QueryEscape(dataInfo)
		//ds := json0.Marshal2ByteNoErr(dataInfo)
		log.InfoContextf(ctx, "convertUIDataByHtml|dataInfo-4=%s", dataInfo)
		s.SetAttr("data-info", dataInfo)
		s.SetAttr("data-type", CustomVar)
		s.SetAttr("data-value", t.UserIDKey)

		s.SetText(UserId)
	})

	newHTML, err := doc.Find("body").First().Html()
	if err != nil {
		log.ErrorContextf(ctx, "new html failed,err:%v", err)
		return ""
	}
	log.InfoContextf(ctx, "[UpgradeProtoVersionFrom21To24]upgradeRichText|newHTML:%s|", newHTML)

	return newHTML
}

func (t *UpgradeTaskFlow24) upgradeApiNodeDataRequestParam(ctx context.Context, reqs []RequestParam) []RequestParam {
	requests := make([]RequestParam, 0)

	for _, info := range reqs {
		info.SubRequest = t.upgradeApiNodeDataRequestParam(ctx, info.SubRequest)

		switch info.SourceType {
		case KEP.APINodeData_RequestParam_SYSTEM.String():
			// 处理 系统变量
			if info.SystemValueData != nil &&
				info.SystemValueData.Value == UserId {
				info.SourceType = KEP.APINodeData_RequestParam_CUSTOM_VAR.String()
				info.CustomVarValueData = &CustomVarValue{
					Value: t.UserIDKey,
				}
				info.SystemValueData = nil
			}

		}

		requests = append(requests, info)
	}
	return requests
}

func (t *UpgradeTaskFlow24) upgradeApiNodeDataHeader(ctx context.Context, headers []Header) []Header {
	reqHeaders := make([]Header, 0)

	for _, info := range headers {
		info.SubHeader = t.upgradeApiNodeDataHeader(ctx, info.SubHeader)

		switch info.SourceType {
		case KEP.APINodeData_Header_SYSTEM.String():
			// todo... update 处理 系统变量
			if info.SystemValueData != nil &&
				info.SystemValueData.Value == UserId {
				info.SourceType = KEP.APINodeData_Header_CUSTOM_VAR.String()
				info.CustomVarValueData = &CustomVarValue{Value: t.UserIDKey}
				info.SystemValueData = nil
			}
		}
		//header.SourceType = sourceType
		reqHeaders = append(reqHeaders, info)
	}
	return reqHeaders
}

// 更新 upgradeEdge
func (t *UpgradeTaskFlow24) upgradeEdge(oldEdges []*Edge) []*Edge {
	// 替换条件中的UserID
	newEdges := make([]*Edge, 0)
	for _, old := range oldEdges {
		newEdges = append(newEdges, &Edge{
			EdgeID:       old.EdgeID,
			Source:       old.Source,
			Target:       old.Target,
			SourceAnchor: old.SourceAnchor,
			TargetAnchor: old.TargetAnchor,
			Label:        strings.ReplaceAll(old.Label, "用户ID", UserId),
		})
	}
	return newEdges
}

// 更新 Branches
func (t *UpgradeTaskFlow24) upgradeBranches(oldBranches []*Branch) []*Branch {
	newBranches := make([]*Branch, 0)

	for _, old := range oldBranches {
		if old.BranchType == KEP.Branch_CUSTOM.String() {
			newBranchCondition := t.upgradeBranchCondition(old.ConditionInfo)
			old.ConditionInfo = newBranchCondition
		} else {
			old.ConditionInfo = &ConditionInfo{}
		}

		newBranches = append(newBranches, old)
	}
	return newBranches
}

func (t *UpgradeTaskFlow24) upgradeBranchCondition(cond *ConditionInfo) *ConditionInfo {
	//newCond := &KEP.ConditionInfo{}
	newConds := make([]*ConditionInfo, 0)
	//newCond.ConditionsLogic = oldCond.ConditionsLogic
	newCondition := t.upgradeCondition(cond.Condition)
	if newCondition != nil {
		cond.Condition = newCondition
	}
	if len(cond.ConditionInfo) > 0 {
		for _, item := range cond.ConditionInfo {
			newConds = append(newConds, t.upgradeBranchCondition(item))
		}
	}
	return cond
}

func (t *UpgradeTaskFlow24) upgradeCondition(cond *BranchCondition) *BranchCondition {
	//newCond := &KEP.ConditionInfo_BranchCondition{}
	if cond != nil {
		switch cond.SourceType {

		case KEP.ConditionInfo_BranchCondition_API_RESP.String():
			//  匹配方式 刷数据逻辑判断
			// 接口出参， 包含/不包含 展示为 精准匹配
			if cond.Comparison == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
				cond.Comparison == KEP.ConditionInfo_BranchCondition_NOT_CONTAINS.String() {
				cond.MatchType = KEP.ConditionInfo_BranchCondition_PRECISE.String()
			} else {
				// 接口出参，其他类型，展示为 语义匹配
				cond.MatchType = KEP.ConditionInfo_BranchCondition_SEMANTIC.String()
			}

			if len(cond.InputValues) > 0 {
				cond.ComparisonValueSourceType = entity.ValueSourceInput
			}
		case KEP.ConditionInfo_BranchCondition_SYSTEM.String():
			// 分支条件刷数据，系统参数 UserID ==> 自定义变量 UserID
			cond.SourceType = KEP.ConditionInfo_BranchCondition_CUSTOM_VAR.String()
			cond.CustomVarValueData = &CustomVarValue{
				Value: t.UserIDKey,
			}
			cond.SystemValueData = nil
			cond.ComparisonValueSourceType = entity.ValueSourceCustomVar
		case KEP.ConditionInfo_BranchCondition_SLOT.String():
			// 将 ComparisonInInputValueAddition ==> ValueSource 刷到  ComparisonValueSourceType
			if cond.InInputValueAddition != nil && cond.ComparisonValueSourceType == 0 &&
				(cond.Comparison == KEP.ConditionInfo_BranchCondition_IN.String() ||
					cond.Comparison == KEP.ConditionInfo_BranchCondition_NOT_IN.String()) {
				cond.ComparisonValueSourceType = cond.InInputValueAddition.ValueSource
				cond.InInputValueAddition.ValueSource = 0
			}
			// 用户输入
			if len(cond.InputValues) > 0 && (cond.Comparison != KEP.ConditionInfo_BranchCondition_IN.String() &&
				cond.Comparison != KEP.ConditionInfo_BranchCondition_NOT_IN.String()) {
				cond.ComparisonValueSourceType = entity.ValueSourceInput
			}

		}

	}

	return cond
}

// PrepareUserIdVarParamInfo 判断是否需要创建 UserID的变量
func (t *UpgradeTaskFlow24) PrepareUserIdVarParamInfo(ctx context.Context) error {
	sid := util.RequestID(ctx)
	hasUserID := t.isTreeHasUserId(ctx, t.oldTaskFlow.DialogJsonDraft)
	t.isUserID = hasUserID
	// 所有老数据内置 UserID,设true，如果只有UserID才内置，设置 if isUserID
	if hasUserID { // 如果存在系统变量 UserID， 则需要新建
		userIdVarParam, err := db.CreateCustomVarUserID(ctx, t.botBizId, UserId,
			t.oldTaskFlow.Uin, t.oldTaskFlow.SubUin, false)
		if err != nil {
			log.ErrorContextf(ctx, "sid:%s|PrepareUserIdVarParamInfo,err:%+v", sid, err)
			return err
		}
		t.UserIDKey = userIdVarParam.VarID
		t.UserIDValue = userIdVarParam.VarName
		return nil
	}
	return nil
}

// isTreeHasUserId 判断画布是否有UserID
func (t *UpgradeTaskFlow24) isTreeHasUserId(ctx context.Context, tfjson string) bool {
	hasUserID := false
	tf := TaskFlow{}
	err := json.Unmarshal([]byte(tfjson), &tf)
	if err != nil {
		log.ErrorContextf(ctx, "upgradeTaskFlowJson|UnmarshalStr|err:%+v", err)
		return hasUserID
	}
	hasUserID = t.checkNodesHasUserId(ctx, tf.Nodes)
	return hasUserID // 老数据都内置UserID
}

func (t *UpgradeTaskFlow24) checkNodesHasUserId(ctx context.Context, nodes []*TaskFlowNode) bool {
	// 判断分支是否包含UserID
	var hasUserID bool
	hasUserID = false
	for _, node := range nodes {
		log.InfoContextf(ctx, "node:%s", node.NodeName)
		// 检查分支是否包含UserID
		hasUserID = t.isBranchHasUserID(ctx, node)
		if hasUserID {
			return hasUserID
		}
		switch node.NodeType {
		//case KEP.NodeType_START:
		case KEP.NodeType_API.String(): // API节点

			hasUserID = t.checkAPINodeHasUserID(ctx, node.APINodeData)
			if hasUserID {
				return hasUserID
			}
		case KEP.NodeType_REQUEST.String(): // 询问节点
			hasUserID = t.checkRequestNodeHasUserID(ctx, node.RequestNodeData)
			if hasUserID {
				return hasUserID
			}

		case KEP.NodeType_ANSWER.String(): // 答案节点
			hasUserID = t.checkAnswerNodeHasUserID(ctx, node.AnswerNodeData)
			if hasUserID {
				return hasUserID
			}

		}

	}
	return hasUserID
}

func (t *UpgradeTaskFlow24) checkAnswerNodeHasUserID(ctx context.Context, node *AnswerNodeData) bool {
	var hasUserId bool
	if node == nil {
		return false
	}

	inputAnswerPreview := node.InputAnswerData.Preview
	if len(inputAnswerPreview) > 0 {
		//自定义精准回复
		hasUserId = t.isRichTextHasUserID(ctx, inputAnswerPreview)
		if hasUserId {
			return hasUserId
		}
	}

	prompt := node.LLMAnswerData.Prompt
	if len(prompt) > 0 {
		hasUserId = t.isRichTextHasUserID(ctx, prompt)
		if hasUserId {
			return hasUserId
		}
	}

	return false
}

func (t *UpgradeTaskFlow24) checkRequestNodeHasUserID(ctx context.Context, node *RequestNodeData) bool {
	if node == nil {
		return false
	}
	req := node.Request
	customAsk := req.CustomAsk
	if len(customAsk) > 0 {
		return t.isRichTextHasUserID(ctx, customAsk)
	}
	return false
}

// checkAPINodeHasUserID
func (t *UpgradeTaskFlow24) checkAPINodeHasUserID(ctx context.Context, node *APINodeData) bool {
	if node == nil {
		return false
	}
	// 1. 检查入参数
	var hasUserId bool
	hasUserId = t.checkAPINodeRequest(ctx, node)
	if hasUserId {
		return hasUserId
	}
	// 2. 检查header
	hasUserId = t.checkAPINodeHeaders(ctx, node)
	if hasUserId {
		return hasUserId
	}
	return false

}

// checkAPINodeRequest API节点的入参信息判断
func (t *UpgradeTaskFlow24) checkAPINodeRequest(ctx context.Context, apiNode *APINodeData) bool {
	var hasUserId bool
	if apiNode == nil {
		return false
	}

	if len(apiNode.Request) > 0 {
		for _, req := range apiNode.Request {
			hasUserId = t.checkAPINodeRequestItem(ctx, &req)
			if hasUserId {
				return hasUserId
			}
		}
	}

	return hasUserId
}

func (t *UpgradeTaskFlow24) isRichTextHasUserID(ctx context.Context, source string) bool {
	var hasUserID bool
	if len(source) == 0 {
		hasUserID = false
	}

	log.InfoContextf(ctx, "[upgrade21to24]upgradeRichText|source:%s|", source)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
	if err != nil {
		log.ErrorContextf(ctx, "NewDocumentFromReader failed,err:%v", err)
		hasUserID = false
	}

	doc.Find("span[data-type='SYSTEM']").Each(func(i int, s *goquery.Selection) {
		dataValue, _ := s.Attr("data-value")
		log.InfoContextf(ctx, "convertUIDataByHtml|dataValue=%s", dataValue)
		if dataValue == UserId {
			hasUserID = true
		} else {
			hasUserID = false
		}
	})
	return hasUserID
}

func (t *UpgradeTaskFlow24) checkAPINodeRequestItem(ctx context.Context, request *RequestParam) bool {
	if request == nil {
		return false
	}
	var hasUserId bool
	if request.SystemValueData != nil {
		userIdStr := request.SystemValueData.Value

		if userIdStr == UserId {
			return true
		}
	}

	if len(request.SubRequest) > 0 {
		for _, subReq := range request.SubRequest {
			hasUserId = t.checkAPINodeRequestItem(ctx, &subReq)
			if hasUserId {
				return hasUserId
			}
		}
	}
	return hasUserId
}

func (t *UpgradeTaskFlow24) checkAPINodeHeaders(ctx context.Context, apiNode *APINodeData) bool {
	if apiNode == nil {
		return false
	}
	var hasUserId bool
	//sid := util.RequestID(ctx)

	if len(apiNode.Headers) > 0 {
		for _, header := range apiNode.Headers {
			hasUserId = t.checkAPINodeHeadItem(ctx, &header)
			if hasUserId {
				return hasUserId
			}

		}
	}

	return false
}

func (t *UpgradeTaskFlow24) checkAPINodeHeadItem(ctx context.Context, header *Header) bool {
	//sid := util.RequestID(ctx)
	if header == nil {
		return false
	}
	var hasUserId bool

	if header.SystemValueData != nil {
		userIdStr := header.SystemValueData.Value
		if userIdStr == UserId {
			return true
		}
	}

	if len(header.SubHeader) > 0 {
		for _, subHeader := range header.SubHeader {
			hasUserId = t.checkAPINodeHeadItem(ctx, &subHeader)
			if hasUserId {
				return hasUserId
			}
		}
	}
	return false
}

func (t *UpgradeTaskFlow24) isBranchHasUserID(ctx context.Context, node *TaskFlowNode) bool {
	if node == nil {
		return false
	}
	var hasUserId bool

	if len(node.Branches) > 0 {
		for _, branch := range node.Branches {
			log.InfoContextf(ctx, "branch:===:%s", branch.BranchType)
			hasUserId = t.isConditionInfoGHasUserId(ctx, branch.ConditionInfo)
			if hasUserId {
				return hasUserId
			}
		}
	}

	return false
}

func (t *UpgradeTaskFlow24) isConditionInfoGHasUserId(ctx context.Context, ci *ConditionInfo) bool {
	if ci == nil {
		return false
	}
	var hasUserId bool
	cd := ci.Condition

	if cd != nil && cd.SystemValueData != nil {
		if cd.SourceType == "SYSTEM" && cd.SystemValueData.Value == UserId {
			return true
		}
	}

	if len(ci.ConditionInfo) > 0 {
		for _, sub := range ci.ConditionInfo {
			hasUserId = t.isConditionInfoGHasUserId(ctx, sub)
			if hasUserId {
				return hasUserId
			}
		}
	}
	return hasUserId
}

// TaskFlowToJsonUpgrade 对话树转JSON字符串
func TaskFlowToJsonUpgrade(taskFlow *TaskFlow) (string, error) {
	jsonStr := json0.Marshal2StringUnescapeHTMLNoErr(taskFlow)
	return jsonStr, nil
}
