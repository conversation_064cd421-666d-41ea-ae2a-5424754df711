package taskflow

import (
	"context"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// ExportTaskFlow 导出任务流程
func ExportTaskFlow(ctx context.Context, req *KEP.ExportTaskFlowReq) (*KEP.ExportTaskFlowRsp, error) {
	rsp := new(KEP.ExportTaskFlowRsp)
	staffID := util.StaffID(ctx)
	if len(req.GetFlowIds()) > config.GetMainConfig().TaskFlow.ExportLimit {
		return nil, errors.ErrTaskFlowExportLimit
	}
	robotInfo, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	robotID := fmt.Sprintf("%d", req.GetBotBizId())
	list, err := db.GetTaskFlowByFlowIDs(ctx, robotID, req.GetFlowIds())
	if err != nil {
		return rsp, err
	}
	if len(req.GetFlowIds()) != len(list) {
		return rsp, errors.ErrTaskFlowNotFound
	}
	if err := db.CreateExportTaskFlow(ctx, robotInfo.GetCorpId(), staffID, robotID,
		req.GetFlowIds()); err != nil {
		return rsp, errors.ErrCreateTaskFlowExport
	}
	return rsp, nil
}

// InnerExportTaskFlow 内部平台导出任务流程
func InnerExportTaskFlow(ctx context.Context,
	req *KEP.InnerExportTaskFlowReq) (*KEP.InnerExportTaskFlowRsp, error) {
	rsp := new(KEP.InnerExportTaskFlowRsp)
	staffID := util.StaffID(ctx)
	appId := req.GetAppId()
	if len(req.GetFlowIds()) > config.GetMainConfig().TaskFlow.ExportLimit {
		return nil, errors.ErrTaskFlowExportLimit
	}
	robotInfo, err := permission.CheckRobot(ctx, 1, appId)
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	robotID := fmt.Sprintf("%d", appId)
	exportId := uint64(idgenerator.NewInt64ID())

	//InnerCreateExportWorkflow
	if req.GetWorkflow() == entity.WorkflowNew {
		if err := db.InnerCreateExportWorkflow(ctx, robotInfo.GetCorpId(), staffID, exportId,
			robotID, req.GetPlatform(), req.GetFlowIds()); err != nil {
			return rsp, errors.ErrCreateTaskFlowExport
		}
	} else {
		if err := db.InnerCreateExportTaskFlow(ctx, robotInfo.GetCorpId(), staffID, exportId,
			robotID, req.GetPlatform(), req.GetFlowIds()); err != nil {
			return rsp, errors.ErrCreateTaskFlowExport
		}
	}

	rsp.ExportID = exportId
	return rsp, nil
}
