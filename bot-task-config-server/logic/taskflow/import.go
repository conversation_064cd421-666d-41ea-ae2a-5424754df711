package taskflow

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"math"
	"path/filepath"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/taskflow/compatible"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// ImportTaskFlow 导入任务流程
func ImportTaskFlow(ctx context.Context, req *KEP.ImportTaskFlowReq) (*KEP.ImportTaskFlowRsp, error) {
	rsp := new(KEP.ImportTaskFlowRsp)
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffID := util.StaffID(ctx)
	robotInfo, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		if errors.Is(err, errors.ErrLicenseInvalid) {
			return nil, errors.ErrLicenseInvalid
		}
		return nil, errors.ErrRobotNotFound
	}
	robotID := fmt.Sprintf("%d", req.GetBotBizId())
	mapIntentName, err := getIntentNamesByRobotID(ctx, robotID)
	if err != nil {
		return nil, err
	}
	// 解析校验导入zip数据
	importData, errRsp, err := parseImportTaskFlowFile(ctx, req, mapIntentName,
		robotInfo.GetCorpId(), robotID)
	if err != nil || errRsp != nil {
		return errRsp, err
	}
	// 构造导入任务数据
	parentTask, tasks, err := fillTaskFlowImport(ctx, robotID, uin, subUin, importData)
	if err != nil {
		return nil, err
	}
	// 创建导入任务
	err = db.CreateTaskFlowImport(ctx, robotInfo.GetCorpId(), staffID, robotID, req.GetFileName(), parentTask,
		tasks)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// getIntentCorpusByRobotID
func getIntentCorpusByRobotID(ctx context.Context, robotID string) (map[string]struct{}, error) {
	examples, err := db.GetIntentExampleByRobotId(ctx, robotID)
	if err != nil {
		return nil, err
	}
	if examples == nil {
		return nil, nil
	}
	mapIntentExample := make(map[string]struct{}, 0)
	for _, v := range *examples {
		mapIntentExample[v.Corpus] = struct{}{}
	}
	return mapIntentExample, nil
}

// getIntentNamesByRobotID 获取应用下所有的意图名称
func getIntentNamesByRobotID(ctx context.Context, robotID string) (map[string]struct{}, error) {
	taskFlows, err := db.GetTaskFlowsByRobotID(ctx, robotID)
	if err != nil {
		return nil, err
	}
	mapIntentName := make(map[string]struct{}, 0)
	for _, v := range taskFlows {
		mapIntentName[v.IntentName] = struct{}{}
	}
	return mapIntentName, nil
}

// fillTaskFlowImport 组装任务流程数据
func fillTaskFlowImport(ctx context.Context, robotID, uin, subUin string, importData *entity.TaskFlowImportData) (
	*entity.TaskFlowImport, []*entity.TaskFlowImport, error) {
	// 构造父级分类参数
	parentParamsData := &entity.TaskFlowImportParentParamsData{
		Categories: importData.CategoryData,
		Uin:        uin,
		SubUin:     subUin,
	}
	parentParamsDataJson, _ := jsoniter.MarshalToString(parentParamsData)
	// 构造子级任务流程集合
	paramDataList, err := constructParamData(ctx, importData, uin, subUin)
	if err != nil {
		return nil, nil, err
	}
	importID := fmt.Sprintf("%d", idgenerator.NewInt64ID())
	parentTask := &entity.TaskFlowImport{
		ImportID: importID,
		RobotID:  robotID,
		Params:   parentParamsDataJson,
		Status:   entity.FlowImportStatusProcessing,
		Message:  "",
		Uin:      uin,
		SubUin:   subUin,
		// 使用数据库时间
		// CreateTime: time.Now(),
		// UpdateTime: time.Now(),
	}
	tasks := make([]*entity.TaskFlowImport, 0)
	for _, v := range paramDataList {
		paramDataJson, _ := jsoniter.MarshalToString(v)
		tasks = append(tasks, &entity.TaskFlowImport{
			ImportID:       fmt.Sprintf("%d", idgenerator.NewInt64ID()),
			RobotID:        robotID,
			ParentImportID: parentTask.ImportID,
			Params:         paramDataJson,
			Status:         entity.FlowImportStatusWait,
			Message:        "",
			Uin:            uin,
			SubUin:         subUin,
			// 使用数据库时间
			// CreateTime:     time.Now(),
			// UpdateTime:     time.Now(),
		})
	}
	return parentTask, tasks, nil
}

// constructParamData 构建导入的任务流程数据
func constructParamData(ctx context.Context, importData *entity.TaskFlowImportData, uin string, subUin string) (
	[]*entity.TaskFlowImportParamsData, error) {
	paramDataList := make([]*entity.TaskFlowImportParamsData, 0)    // 任务流程相关数据
	mapCategory := make(map[string]*entity.CategoryRow)             // 分类
	mapIntent := make(map[string]*entity.IntentRow)                 // 意图-任务流程名称
	mapCorpus := make(map[string][]*entity.CorpusRow)               // 语料-任务流程名称
	mapIntentSlot := make(map[string][]*entity.SlotRow)             // 槽位-实体
	mapExampleCorpus := make(map[string][]*entity.IntentExampleRow) // 示例问法语料
	mapIntentVar := make(map[string][]*entity.IntentVarRow)         // 自定义变量
	for _, category := range importData.CategoryData {
		mapCategory[category.CategoryID] = category
	}
	for _, intent := range importData.IntentData {
		mapIntent[intent.IntentID] = intent
	}
	for _, corpus := range importData.CorpusData {
		mapCorpus[corpus.IntentID] = append(mapCorpus[corpus.IntentID], corpus)
	}
	for _, slot := range importData.SlotData {
		mapIntentSlot[slot.IntentID] = append(mapIntentSlot[slot.IntentID], slot)
	}
	for _, exampleCorpus := range importData.IntentCorpusData {
		mapExampleCorpus[exampleCorpus.IntentID] = append(mapExampleCorpus[exampleCorpus.IntentID], exampleCorpus)
	}
	for _, varData := range importData.IntentVarData {
		mapIntentVar[varData.IntentID] = append(mapIntentVar[varData.IntentID], varData)
	}
	for _, taskFlow := range importData.TaskFlowData {
		if _, ok := mapCategory[taskFlow.CategoryID]; !ok {
			log.WarnContextf(ctx, "constructParamData mapCategory err")
			return paramDataList, errors.ErrImportTaskFlowFileIncomplete
		}
		intent, ok := mapIntent[taskFlow.IntentID]
		if !ok {
			log.WarnContextf(ctx, "constructParamData mapIntent err")
			return paramDataList, errors.ErrImportTaskFlowFileIncomplete
		}
		corpora, ok := mapCorpus[taskFlow.IntentID]
		if !ok {
			log.WarnContextf(ctx, "constructParamData corpora err")
			return paramDataList, errors.ErrImportTaskFlowFileIncomplete
		}
		// 任务流有可能没有关联槽位
		slots, ok := mapIntentSlot[taskFlow.IntentID]
		if !ok {
			slots = make([]*entity.SlotRow, 0)
		}
		// 任务流可能没关联示例问法
		intentExamples, ok := mapExampleCorpus[taskFlow.IntentID]
		if !ok {
			log.WarnContextf(ctx, "constructParamData intentExamples err")
			intentExamples = make([]*entity.IntentExampleRow, 0)
		}
		// 任务流可能没关联变量
		intentVar, ok := mapIntentVar[taskFlow.IntentID]
		if !ok {
			log.WarnContextf(ctx, "constructParamData intentVar err")
			intentVar = make([]*entity.IntentVarRow, 0)
		}
		paramDataList = append(paramDataList, &entity.TaskFlowImportParamsData{
			TaskFlow:     taskFlow,
			Intent:       intent,
			Corpora:      corpora,
			Slots:        slots,
			IntentCorpus: intentExamples,
			Vars:         intentVar,
			Uin:          uin,
			SubUin:       subUin,
		})
	}
	return paramDataList, nil
}

// getImportTaskFlowFile
func getImportTaskFlowFile(ctx context.Context, req *KEP.ImportTaskFlowReq) (map[string]*zip.File, error) {
	//body, err := cos.GetCos().DownloadFile(req.GetCosUrl())
	body, err := cos.StorageCli.GetObject(ctx, req.GetCosUrl())
	if err != nil {

		log.ErrorContextf(ctx, "storage GetObject Failed! err:%+v", err)
		return nil, errors.ErrSystem
	}
	zipFileName := strings.TrimSuffix(req.GetFileName(), ".zip")
	if len(zipFileName) == 0 {
		return nil, errors.ErrInvalidFileName
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		return nil, errors.ErrSystem
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		if r.Flags == 0 { // 使用win rar压缩的文件名是gbk编码
			b, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(r.Name)),
				simplifiedchinese.GB18030.NewDecoder()))
			if err != nil {
				log.Errorf("decode file name err:%+v", err)
				return nil, errors.ErrSystem
			}
			fileName = string(b)
		}
		mapZipFile[fileName] = r
	}
	// 检查文件是否存在
	if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.CategoryFileName]; !ok {
		return nil, errors.ErrImportTaskFlowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.TaskFlowFileName]; !ok {
		return nil, errors.ErrImportTaskFlowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.IntentFileName]; !ok {
		return nil, errors.ErrImportTaskFlowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.CorpusFileName]; !ok {
		return nil, errors.ErrImportTaskFlowFileNotFound
	}
	// TODO mike 如果直接进来就到答案节点，是不是就没slot，这种情况按页面是应该允许导入的
	if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.SlotFileName]; !ok {
		return nil, errors.ErrImportTaskFlowFileNotFound
	}
	// 兼容没有示例问法文件的
	//if _, ok := mapZipFile[config.GetMainConfig().TaskFlow.IntentExampleFileName]; !ok {
	//	return nil, errors.ErrImportTaskFlowFileNotFound
	//}
	return mapZipFile, nil
}

// parseTaskFlowXlsx 解析任务流程导入xlsx
func parseTaskFlowXlsx(ctx context.Context, mapZipFile map[string]*zip.File, taskFlowFileName string,
	mapDBIntentName map[string]struct{}, robotId string) (
	[]*entity.TaskFlowRow, []byte, bool, error) {
	r, err := mapZipFile[taskFlowFileName].Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	minRow, maxRow := config.GetMainConfig().TaskFlow.MinRow, config.GetMainConfig().TaskFlow.MaxRow
	// 这里还需要限制整体任务流数量的限制
	maxRow = int(math.Min(
		float64(maxRow), float64(config.GetMainConfig().VerifyTaskFlow.TaskFlowLimit-len(mapDBIntentName))))
	head := config.GetMainConfig().TaskFlow.TaskFlowHead
	// 检查数据
	mapIntentID := make(map[string]int)
	mapIntentName := make(map[string]int)
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		taskFlowRow, ok := getTaskFlowFromRow(ctx, row, mapZipFile, robotId)
		if !ok {
			return fmt.Sprintf("任务流程json文件:%s不存在或名称不合法；", taskFlowRow.DialogJson)
		}
		errMsgs := checkTaskFlowFromRow(ctx, taskFlowRow, i, uniqueKeys, mapIntentID, mapIntentName,
			mapDBIntentName)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, taskFlowFileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse task flow xlsx err:%+v", err)
		return nil, nil, false, err
	}
	taskFlowRows := make([]*entity.TaskFlowRow, 0)
	for _, row := range rows {
		taskFlowRow, _ := getTaskFlowFromRow(ctx, row, mapZipFile, robotId)
		taskFlowRows = append(taskFlowRows, taskFlowRow)
	}
	return taskFlowRows, bs, err == nil, nil
}

func getUpgradeTaskFlowStr(ctx context.Context, flowId, robotId, treeStr,
	protoVersion string) (string, error) {
	sid := util.RequestID(ctx)
	if protoVersion != KEP.TaskFlowProtoVersion_V2_4.String() {
		uin, subUin := util.GetUinAndSubAccountUin(ctx)
		log.InfoContextf(ctx, "sid:%s|getTaskFlowFromRow|%+v|uin:%+v", sid, protoVersion, uin)
		// 获取json，如果是之前版本的json数据，刷新json数据
		taskFlow := &entity.TaskFlow{
			FlowID:          flowId,
			Uin:             uin,
			SubUin:          subUin,
			DialogJsonDraft: treeStr,
		}
		t := compatible.NewUpgradeTaskFlowV24(robotId, taskFlow)
		flowCtx := trpc.CloneContext(ctx)
		// 判断准备UserID
		err := t.PrepareUserIdVarParamInfo(flowCtx)
		if err != nil {
			log.ErrorContextf(flowCtx, "getTaskFlowFromRow|prepareUserIdVarParamInfo|"+
				"flowId:%s|err:%+v", taskFlow.FlowID, err)

			return treeStr, err
		}
		newDraftJsonStr, err := t.GetUpgradeDraftJson(flowCtx, taskFlow)
		if err != nil {
			log.ErrorContextf(flowCtx, "getTaskFlowFromRow|prepareUserIdVarParamInfo|"+
				"flowId:%s|err:%+v", taskFlow.FlowID, err)
			return treeStr, err
		}
		return newDraftJsonStr, nil
	} else {
		return treeStr, nil
	}
}

// getTaskFlowFromRow
func getTaskFlowFromRow(ctx context.Context, row []string,
	mapZipFile map[string]*zip.File, robotId string) (*entity.TaskFlowRow, bool) {
	sid := util.RequestID(ctx)
	taskFlowRow := &entity.TaskFlowRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelTaskFlowTplID:
			taskFlowRow.FlowID = strings.TrimSpace(cell)
		case entity.ExcelTaskFlowTplIntentID:
			taskFlowRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelTaskFlowTplIntentName:
			taskFlowRow.IntentName = strings.TrimSpace(cell)
		case entity.ExcelTaskFlowTplIntentDesc:
			taskFlowRow.IntentDesc = strings.TrimSpace(cell)
		case entity.ExcelTaskFlowTplCategoryID:
			taskFlowRow.CategoryID = strings.TrimSpace(cell)
		case entity.ExcelTaskFlowTplDialogJson:
			var dialogJsonStr string
			// 兼容历史数据
			// 旧的数据从excel中取
			taskFlowRow.DialogJson = strings.TrimSpace(cell)
			tree, err := protoutil.JsonToTaskFlow(taskFlowRow.DialogJson)
			if err == nil {
				continue
			}

			// 新的数据从json文件中取
			if taskFlowRow.DialogJson != entity.GetTaskFlowJsonFileKey(taskFlowRow.FlowID) {
				// 如果画布版本不是最新版本，需要刷数据
				dialogJsonStr, _ = getUpgradeTaskFlowStr(ctx, tree.TaskFlowID, robotId, taskFlowRow.DialogJson,
					tree.GetProtoVersion().String())
				taskFlowRow.DialogJson = dialogJsonStr
				return taskFlowRow, false
			}
			jsonFile, ok := mapZipFile[taskFlowRow.DialogJson]
			if !ok {
				return taskFlowRow, false
			}
			r, err := jsonFile.Open()
			if err != nil {
				return taskFlowRow, false
			}
			body, err := io.ReadAll(r)
			if err != nil {
				return taskFlowRow, false
			}
			dialogJsonStr = string(body)
			tree, err = protoutil.JsonToTaskFlowForPreCheck(dialogJsonStr)
			if err != nil {
				log.ErrorContextf(ctx, "getTaskFlowFromRow,JsonToTaskFlowForPreCheck err:%s|%+v", sid, err)
				return taskFlowRow, false
			}
			// 如果画布版本不是最新版本，需要刷数据
			dialogJsonStr, err = getUpgradeTaskFlowStr(ctx, tree.TaskFlowID, robotId, dialogJsonStr,
				tree.GetProtoVersion().String())

			if err != nil {
				log.ErrorContextf(ctx, "getTaskFlowFromRow,getUpgradeTaskFlow err:%s|%+v", sid, err)
				return taskFlowRow, false
			}
			taskFlowRow.DialogJson = dialogJsonStr
			err = r.Close()
			if err != nil {
				return taskFlowRow, false
			}
		}
	}
	return taskFlowRow, true
}

// checkTaskFlowFromRow
func checkTaskFlowFromRow(ctx context.Context, taskFlowRow *entity.TaskFlowRow, i int, uniqueKeys, mapIntentID,
	mapIntentName map[string]int, mapDBIntentName map[string]struct{}) []string {
	errMsgs := make([]string, 0)
	// 任务流ID
	flowIDLen := utf8.RuneCountInString(taskFlowRow.FlowID)
	if flowIDLen == 0 {
		errMsgs = append(errMsgs, "任务流ID为空")
	} else {
		if rowIndex, ok := uniqueKeys[taskFlowRow.FlowID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("任务流ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[taskFlowRow.FlowID] = i
		}
	}
	// 意图ID
	intentIDLen := utf8.RuneCountInString(taskFlowRow.IntentID)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if rowIndex, ok := mapIntentID[taskFlowRow.IntentID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("意图ID与第%d行重复", rowIndex+1))
		} else {
			mapIntentID[taskFlowRow.IntentID] = i
		}
	}
	// 意图名称
	intentNameLen := utf8.RuneCountInString(taskFlowRow.IntentName)
	if intentNameLen == 0 {
		errMsgs = append(errMsgs, "意图名称为空")
	} else {
		if intentNameLen > config.GetMainConfig().VerifyTaskFlow.IntentNameLen {
			errMsgs = append(errMsgs, "意图名称长度超过字符限制")
		}
		if rowIndex, ok := mapIntentName[taskFlowRow.IntentName]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("意图名称与第%d行重复", rowIndex+1))
		} else {
			mapIntentName[taskFlowRow.IntentName] = i
		}
		if _, ok := mapDBIntentName[taskFlowRow.IntentName]; ok {
			errMsgs = append(errMsgs, "意图名称与管理后台重复")
		}
	}

	// 意图描述(可为空及重复)
	intentDescLen := utf8.RuneCountInString(taskFlowRow.IntentDesc)
	if intentDescLen > config.GetMainConfig().VerifyTaskFlow.IntentDescLen {
		errMsgs = append(errMsgs, "意图描述长度超过字符限制")
	}

	// 分类ID
	if utf8.RuneCountInString(taskFlowRow.CategoryID) == 0 {
		errMsgs = append(errMsgs, "分类ID为空")
	}
	// 画布结构校验
	errMsgs = append(errMsgs, checkImportDialogJson(ctx, taskFlowRow.FlowID, taskFlowRow.IntentName,
		taskFlowRow.DialogJson)...)
	return errMsgs
}

// checkImportDialogJson
func checkImportDialogJson(_ context.Context, flowID, flowName, dialogJson string) []string {
	errMsgs := make([]string, 0)
	if utf8.RuneCountInString(dialogJson) == 0 {
		return errMsgs
	}
	tree, err := protoutil.JsonToTaskFlow(dialogJson)
	if err != nil {
		errMsgs = append(errMsgs, "画布结构解析错误")
		return errMsgs
	}
	if flowID != tree.TaskFlowID {
		errMsgs = append(errMsgs, "任务流程ID和画布结构中的流程ID不匹配")
	}
	if flowName != tree.TaskFlowName {
		errMsgs = append(errMsgs, "任务流程名称和画布结构中的流程名称不匹配")
	}
	return errMsgs
}

// parseCategoryXlsx
func parseCategoryXlsx(ctx context.Context, zipFile *zip.File, _ map[string]struct{}) (
	[]*entity.CategoryRow, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.CategoryFileName
	minRow, maxRow := config.GetMainConfig().TaskFlow.MinRow, config.GetMainConfig().TaskFlow.MaxRow
	head := config.GetMainConfig().TaskFlow.CategoryHead
	// 检查数据
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		categoryRow := getCategoryFromRow(row)
		errMsgs := checkCategoryRow(categoryRow, i, uniqueKeys)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse category xlsx err:%+v", err)
		return nil, nil, false, err
	}
	categoryRows := make([]*entity.CategoryRow, 0)
	for _, row := range rows {
		categoryRows = append(categoryRows, getCategoryFromRow(row))
	}
	return categoryRows, bs, err == nil, nil
}

// getCategoryFromRow
func getCategoryFromRow(row []string) *entity.CategoryRow {
	categoryRow := &entity.CategoryRow{}
	for cellIndex, cell := range row {
		switch {
		case cellIndex == entity.ExcelCategoryTplID:
			categoryRow.CategoryID = strings.TrimSpace(cell)
		case cellIndex <= entity.ExcelCategoryTplMaxLevel:
			categoryRow.CategoryNames = append(categoryRow.CategoryNames, strings.TrimSpace(cell))
		}
	}
	return categoryRow
}

// checkCategoryRow
func checkCategoryRow(categoryRow *entity.CategoryRow, i int, uniqueKeys map[string]int) []string {
	errMsgs := make([]string, 0)
	// 分类ID
	categoryIDLen := utf8.RuneCountInString(categoryRow.CategoryID)
	if categoryIDLen == 0 {
		errMsgs = append(errMsgs, "分类ID为空")
	} else {
		if rowIndex, ok := uniqueKeys[categoryRow.CategoryID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("分类ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[categoryRow.CategoryID] = i
		}
	}
	// 分类名称
	if len(categoryRow.CategoryNames) == 0 {
		errMsgs = append(errMsgs, "分类名称为空")
	}
	for level, categoryName := range categoryRow.CategoryNames {
		categoryNameLen := utf8.RuneCountInString(categoryName)
		if categoryNameLen == 0 {
			errMsgs = append(errMsgs, fmt.Sprintf("第%d级分类为空", level+1))
		} else if categoryNameLen > config.GetMainConfig().Category.CategoryNameLen {
			errMsgs = append(errMsgs, fmt.Sprintf("第%d级分类名称长度超过字符限制", level+1))
		}
	}
	return errMsgs
}

// parseIntentXlsx
func parseIntentXlsx(ctx context.Context, zipFile *zip.File, mapIntent map[string]entity.IntentRow) (
	[]*entity.IntentRow, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.IntentFileName
	minRow, maxRow := config.GetMainConfig().TaskFlow.MinRow, config.GetMainConfig().TaskFlow.MaxRow
	head := config.GetMainConfig().TaskFlow.IntentHead
	// 检查数据
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		intentRow := getIntentFromRow(row)
		errMsgs := checkIntentRow(intentRow, i, uniqueKeys, mapIntent)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse intent xlsx err:%+v", err)
		return nil, nil, false, err
	}
	intentRows := make([]*entity.IntentRow, 0)
	for _, row := range rows {
		intentRows = append(intentRows, getIntentFromRow(row))
	}
	return intentRows, bs, err == nil, nil
}

// getIntentFromRow
func getIntentFromRow(row []string) *entity.IntentRow {
	intentRow := &entity.IntentRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelIntentTplID:
			intentRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelIntentTplName:
			intentRow.IntentName = strings.TrimSpace(cell)
		case entity.ExcelIntentTplDesc:
			intentRow.IntentDesc = strings.TrimSpace(cell)
		case entity.ExcelIntentTplType:
			intentRow.IntentType = strings.TrimSpace(cell)
		case entity.ExcelIntentTplSource:
			intentRow.Source = strings.TrimSpace(cell)
		}
	}
	return intentRow
}

// checkIntentRow
func checkIntentRow(intentRow *entity.IntentRow, i int, uniqueKeys map[string]int,
	mapIntent map[string]entity.IntentRow) []string {
	errMsgs := make([]string, 0)
	// 意图ID
	intentIDLen := utf8.RuneCountInString(intentRow.IntentID)
	intentNameLen := utf8.RuneCountInString(intentRow.IntentName)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if rowIndex, ok := uniqueKeys[intentRow.IntentID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("意图ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[intentRow.IntentID] = i
		}
		if intent, ok := mapIntent[intentRow.IntentID]; !ok {
			errMsgs = append(errMsgs, "意图ID在任务流程文件中不存在")
		} else if intentNameLen > 0 {
			if intent.IntentName != intentRow.IntentName {
				errMsgs = append(errMsgs, "意图名称和任务流程文件中对应的意图名称不匹配")
			}
			// 意图描述可以不存在
			if intent.IntentDesc != intentRow.IntentDesc {
				errMsgs = append(errMsgs, "意图描述和任务流程文件中对应的意图描述不匹配")
			}
		}
	}
	// 意图名称
	if intentNameLen == 0 {
		errMsgs = append(errMsgs, "意图名称为空")
	}

	// 意图描述（可空、可重复）
	intentDescLen := utf8.RuneCountInString(intentRow.IntentDesc)
	if intentDescLen > config.GetMainConfig().VerifyTaskFlow.IntentDescLen {
		errMsgs = append(errMsgs, "意图描述长度超过字符限制")
	}

	// 意图类型
	if !entity.IsInvalidIntentType(intentRow.IntentType) {
		errMsgs = append(errMsgs, "意图类型错误")
	}
	// 业务来源
	if !entity.IsInvalidIntentSource(intentRow.Source) {
		errMsgs = append(errMsgs, "业务来源错误")
	}
	return errMsgs
}

// varParamCheck 变量数据校验
type varParamCheck struct {
	Index int      // Excel中Index
	Param []string // [变量ID,变量名称]
}

// isLegal 校验槽位是否合法
func (s varParamCheck) isLegal(varID, varName string) bool {
	if len(s.Param) != 2 {
		return false
	}
	return s.Param[0] == varID && s.Param[1] == varName
}

// parseIntentVarXlsx 解析变量文件
func parseIntentVarXlsx(ctx context.Context, zipFile *zip.File,
	mapIntent map[string]entity.IntentRow, robotId string) (
	[]*entity.IntentVarRow, []byte, bool, error) {
	sid := util.RequestID(ctx)
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.VarParamsFileName
	// 变量文件条数限制说明
	// 一个任务流最少的变量限制：minRow = 0
	// 最大变量限制：maxRow = 1000
	minRow := 0
	maxRow := config.GetMainConfig().VerifyTaskFlow.VarParamsCountMax
	head := config.GetMainConfig().TaskFlow.VarParamsHead
	// 检查数据
	mapVarIDCheck := make(map[string]varParamCheck)
	mapVarNameCheck := make(map[string]varParamCheck)
	mapDBIntentVars, err := db.GetImportVarParamsInfos(ctx, robotId, []string{})
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetImportVarParamsInfos|err:%+v", sid, err)
		return nil, nil, false, err
	}
	check := func(i int, row []string, uniqueKeys, totalMap map[string]int) (string, string) {
		varRow := getVarInfoFromRow(row)

		errMsgs, countErr := checkVarRow(varRow, i, uniqueKeys, totalMap, mapIntent, mapVarIDCheck, mapVarNameCheck,
			mapDBIntentVars, maxRow)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；"), countErr
		}
		return "", countErr
	}

	//rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	rows, bs, err := file.CheckVarXlsxContentCompatibilityHistory(ctx, fileName, minRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse var xlsx err:%+v", err)
		return nil, nil, false, err
	}
	varRows := make([]*entity.IntentVarRow, 0)
	for _, row := range rows {
		varRow := getVarInfoFromRow(row)
		if len(varRow.VarDesc) == 0 {
			varRow.VarDesc = "-"
		}
		if len(varRow.VarType) == 0 {
			varRow.VarType = KEP_WF.TypeEnum_STRING.String()
		}
		varRows = append(varRows, varRow)
	}

	return varRows, bs, err == nil, nil
}

// checkVarRow 变量信息导入行 校验
func checkVarRow(varRow *entity.IntentVarRow, i int, uniquesKeys, totalMap map[string]int,
	mapIntent map[string]entity.IntentRow, mapVarIDCheck, mapVarNameCheck map[string]varParamCheck,
	mapDBIntentVars map[string]*entity.VarParams, maxRow int) ([]string, string) {
	errMsgs := make([]string, 0)

	// 意图ID
	intentIDLen := utf8.RuneCountInString(varRow.IntentID)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if _, ok := mapIntent[varRow.IntentID]; !ok {
			errMsgs = append(errMsgs, "意图ID在任务流程文件中不存在")
		}
		totalMap[varRow.IntentID] += 1
		//if totalMap[varRow.IntentID] > maxRow {
		//	return errMsgs, fmt.Sprintf("变量表格条数超过%d条，请修改后重新上传", maxRow)
		//}
	}

	// 变量ID
	varIDLen := utf8.RuneCountInString(varRow.VarID)
	if varIDLen == 0 {
		errMsgs = append(errMsgs, "变量ID为空")
	} else {
		intentSlotKey := varRow.IntentID + "_" + varRow.VarID
		if rowIndex, ok := uniquesKeys[intentSlotKey]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("意图ID和变量ID与第%d行重复", rowIndex+1))
		} else {
			uniquesKeys[intentSlotKey] = i
		}

		if mapDBIntentVars != nil {
			if _, ok := mapDBIntentVars[varRow.VarName]; ok {
				errMsgs = append(errMsgs, fmt.Sprintf("导入变量:%s,与应用内其它变量重复", varRow.VarName))
			}
		}

		if check, ok := mapVarIDCheck[varRow.VarID]; ok &&
			!check.isLegal(varRow.VarID, varRow.VarName) {
			errMsgs = append(errMsgs, fmt.Sprintf("变量数据与第%d行冲突", check.Index+1))
		} else {
			mapVarIDCheck[varRow.VarID] = varParamCheck{
				Index: i,
				Param: []string{varRow.VarID, varRow.VarName},
			}
		}
	}

	// 变量名称
	varNameLen := utf8.RuneCountInString(varRow.VarName)
	if varNameLen == 0 {
		errMsgs = append(errMsgs, "变量名称为空")
	} else {
		maxLimit := config.GetMainConfig().VerifyTaskFlow.VarParamsTextMax
		if varNameLen > maxLimit {
			errMsgs = append(errMsgs, fmt.Sprintf("变量名称长度超过字符%d限制", maxLimit))
		}
		if check, ok := mapVarNameCheck[varRow.VarName]; ok &&
			!check.isLegal(varRow.VarID, varRow.VarName) {
			errMsgs = append(errMsgs, fmt.Sprintf("变量数据与第%d行冲突", check.Index+1))
		} else {
			mapVarNameCheck[varRow.VarName] = varParamCheck{
				Index: i,
				Param: []string{varRow.VarID, varRow.VarName},
			}
		}
	}

	return errMsgs, ""
}

// getVarInfoFromRow 从导入行中获取变量信息
func getVarInfoFromRow(row []string) *entity.IntentVarRow {
	varRow := &entity.IntentVarRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelIntentVarIntentID:
			varRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelIntentVarID:
			varRow.VarID = strings.TrimSpace(cell)
		case entity.ExcelIntentVarName:
			varRow.VarName = strings.TrimSpace(cell)
		case entity.ExcelIntentVarDesc:
			varRow.VarDesc = strings.TrimSpace(cell)
		case entity.ExcelIntentVarType:
			varRow.VarType = strings.TrimSpace(cell)
		case entity.ExcelIntentVarDefaultValue:
			varRow.VarDefaultValue = strings.TrimSpace(cell)
		case entity.ExcelIntentVarDefaultFileName:
			varRow.VarDefaultFileName = strings.TrimSpace(cell)
		}
	}
	return varRow
}

// parseIntentCorpusExampleXlsx 解析示例问法文件
func parseIntentCorpusExampleXlsx(ctx context.Context, zipFile *zip.File,
	mapIntent map[string]entity.IntentRow, robotId string) (
	[]*entity.IntentExampleRow, []byte, bool, error) {
	sid := util.RequestID(ctx)
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.IntentExampleFileName

	// 示例文件条数限制说明：
	// - 一个任务流最多的槽位限制：maxRow = 最大任务流程数 * 每个任务流程下允许的最大示例问法数
	// - 一个任务流最少的槽位限制：minRow = 0
	minRow := 0
	maxRow := config.GetMainConfig().TaskFlow.MaxRow
	maxRow = maxRow * config.GetMainConfig().ExampleCorpus.IntentExampleMax
	head := config.GetMainConfig().TaskFlow.IntentExampleHead
	mapDBIntentExample, err := getIntentCorpusByRobotID(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|getIntentCorpusByRobotID|err:%+v", sid, err)
		return nil, nil, false, err
	}

	check := func(i int, row []string, uniqueKeys map[string]int) string {
		mapIntentExampleRow := getIntentExampleFromRow(row)
		errMsgs := checkIntentExampleRow(mapIntentExampleRow, i, uniqueKeys,
			mapIntent, mapDBIntentExample)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.ErrorContextf(ctx, "sid:%s|parseIntentCorpusExampleXlsx|err:%+v", sid, err)
		return nil, nil, false, err
	}
	intentExampleRow := make([]*entity.IntentExampleRow, 0)
	for _, row := range rows {
		intentExampleRow = append(intentExampleRow, getIntentExampleFromRow(row))
	}
	log.InfoContextf(ctx, "sid:%s|parseIntentCorpusExampleXlsx,intentExampleRow:%+v", sid, intentExampleRow)
	return intentExampleRow, bs, err == nil, nil
}

// getIntentExampleFromRow
func getIntentExampleFromRow(row []string) *entity.IntentExampleRow {
	intentExampleRow := &entity.IntentExampleRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelIntentExampleIntentID:
			intentExampleRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelIntentExampleID:
			intentExampleRow.CorpusID = strings.TrimSpace(cell)
		case entity.ExcelIntentExampleCorpus:
			intentExampleRow.Corpus = strings.TrimSpace(cell)
		}
	}
	return intentExampleRow
}

// checkIntentExampleRow
func checkIntentExampleRow(intentExampleRow *entity.IntentExampleRow, i int, uniqueKeys map[string]int,
	mapIntent map[string]entity.IntentRow, mapDBIntentExample map[string]struct{}) []string {
	errMsgs := make([]string, 0)
	// 意图ID
	intentIDLen := utf8.RuneCountInString(intentExampleRow.IntentID)
	intentExampleLen := utf8.RuneCountInString(intentExampleRow.Corpus)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if _, ok := mapIntent[intentExampleRow.IntentID]; !ok {
			errMsgs = append(errMsgs, "意图ID在任务流程文件中不存在")
		}

	}
	// 示例问法ID
	CorpusIDLen := utf8.RuneCountInString(intentExampleRow.CorpusID)
	if CorpusIDLen == 0 {
		errMsgs = append(errMsgs, "示例问法ID为空")
	} else {
		if rowIndex, ok := uniqueKeys[intentExampleRow.CorpusID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("示例问法ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[intentExampleRow.CorpusID] = i
		}
	}

	// 示例问法
	if intentExampleLen == 0 {
		errMsgs = append(errMsgs, "示例问法内容为空")
	} else {
		// 示例问法(可为空，同一机器人应用下不可重复)
		contentLen := config.GetMainConfig().ExampleCorpus.IntentExampleContentLen
		if intentExampleLen > contentLen {
			errMsgs = append(errMsgs, fmt.Sprintf("示例问法长度超过%d字符限制", contentLen))
		}
		if rowIndex, ok := uniqueKeys[intentExampleRow.Corpus]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("示例问法内容与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[intentExampleRow.Corpus] = i
		}

		if mapDBIntentExample != nil {
			if _, ok := mapDBIntentExample[intentExampleRow.Corpus]; ok {
				errMsgs = append(errMsgs, "示例问法与应用内其它画布示例问法重复")
			}
		}
	}

	return errMsgs
}

// parseCorpusXlsx
func parseCorpusXlsx(ctx context.Context, zipFile *zip.File, mapIntent map[string]entity.IntentRow) (
	[]*entity.CorpusRow, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.CorpusFileName
	minRow, maxRow := config.GetMainConfig().TaskFlow.MinRow, config.GetMainConfig().TaskFlow.MaxRow
	head := config.GetMainConfig().TaskFlow.CorpusHead
	// 检查数据
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		corpusRow := getCorpusFromRow(row)
		errMsgs := checkCorpusRow(corpusRow, i, uniqueKeys, mapIntent)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse corpus xlsx err:%+v", err)
		return nil, nil, false, err
	}
	corpusRows := make([]*entity.CorpusRow, 0)
	for _, row := range rows {
		corpusRows = append(corpusRows, getCorpusFromRow(row))
	}
	return corpusRows, bs, err == nil, nil
}

// getCorpusFromRow
func getCorpusFromRow(row []string) *entity.CorpusRow {
	corpusRow := &entity.CorpusRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelCorpusTplIntentID:
			corpusRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelCorpusTplContent:
			corpusRow.Content = strings.TrimSpace(cell)
		}
	}
	return corpusRow
}

// checkCorpusRow
func checkCorpusRow(corpusRow *entity.CorpusRow, i int, uniqueKeys map[string]int,
	mapIntent map[string]entity.IntentRow) []string {
	errMsgs := make([]string, 0)
	// 意图ID
	intentIDLen := utf8.RuneCountInString(corpusRow.IntentID)
	contentLen := utf8.RuneCountInString(corpusRow.Content)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if intent, ok := mapIntent[corpusRow.IntentID]; !ok {
			errMsgs = append(errMsgs, "意图ID在任务流程文件中不存在")
		} else if contentLen > 0 && corpusRow.Content != intent.IntentName {
			errMsgs = append(errMsgs, "语料内容和任务流程文件中对应的意图名称不匹配")
		}
	}
	// 语料内容
	if contentLen == 0 {
		errMsgs = append(errMsgs, "语料内容为空")
	} else {
		if rowIndex, ok := uniqueKeys[corpusRow.Content]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("语料内容与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[corpusRow.Content] = i
		}
	}
	return errMsgs
}

// getIntentAndCategoryID
func getIntentAndCategoryID(taskFlowRows []*entity.TaskFlowRow) (map[string]entity.IntentRow, map[string]struct{}) {
	mapIntent := make(map[string]entity.IntentRow)
	mapCategoryID := make(map[string]struct{})
	for _, v := range taskFlowRows {
		var intent entity.IntentRow
		intent.IntentName = v.IntentName
		intent.IntentDesc = v.IntentDesc
		mapIntent[v.IntentID] = intent
		mapCategoryID[v.CategoryID] = struct{}{}
	}
	return mapIntent, mapCategoryID
}

// parseSlotXlsx 解析槽位导入文件
func parseSlotXlsx(ctx context.Context, mapZipFile map[string]*zip.File, slotFileName string,
	mapIntent map[string]entity.IntentRow) (
	[]*entity.SlotRow, []byte, bool, error) {
	r, err := mapZipFile[slotFileName].Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().TaskFlow.SlotFileName
	// 槽位文件条数限制说明：
	// - 目前槽位只能通过询问节点和API节点入参关联
	// - 一个询问节点只挂一个槽位，一个API节点可挂多个槽位
	// - 一个任务流最多的槽位限制：maxRow = API节点最大数 * API节点最大入参数
	// - 一个任务流最少的槽位限制：minRow = 0
	minRow := 0
	maxRow := config.GetMainConfig().TaskFlow.MaxRow * config.GetMainConfig().VerifyTaskFlow.UiNodeTotal *
		config.GetMainConfig().VerifyTaskFlow.APINodeRequiredInfoMax
	head := config.GetMainConfig().TaskFlow.SlotHead
	// 检查数据
	mapSlotIDCheck := make(map[string]slotCheck)
	mapSlotNameCheck := make(map[string]slotCheck)
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		slotRow, ok := getSlotInfoFromRow(row, mapZipFile)
		if !ok {
			return fmt.Sprintf("槽位实体json文件:%s不存在或名称不合法；", slotRow.EntityInfo)
		}
		errMsgs := checkSlotRow(slotRow, i, uniqueKeys, mapIntent, mapSlotIDCheck, mapSlotNameCheck)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && err != errors.ErrExcelContent {
		log.Errorf("parse slot xlsx err:%+v", err)
		return nil, nil, false, err
	}
	slotRows := make([]*entity.SlotRow, 0)
	for _, row := range rows {
		slotRow, _ := getSlotInfoFromRow(row, mapZipFile)
		slotRows = append(slotRows, slotRow)
	}
	return slotRows, bs, err == nil, nil
}

// getSlotInfoFromRow 从导入行中获取槽位信息
func getSlotInfoFromRow(row []string, mapZipFile map[string]*zip.File) (*entity.SlotRow, bool) {
	slotRow := &entity.SlotRow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelSlotTplIntentID:
			slotRow.IntentID = strings.TrimSpace(cell)
		case entity.ExcelSlotTplID:
			slotRow.SlotID = strings.TrimSpace(cell)
		case entity.ExcelSlotTplName:
			slotRow.SlotName = strings.TrimSpace(cell)
		case entity.ExcelSlotTplDesc:
			slotRow.SlotDesc = strings.TrimSpace(cell)
		case entity.ExcelSlotTplExamples:
			slotRow.SlotExamples = strings.TrimSpace(cell)
		case entity.ExcelSlotTplEntityInfo:
			// 兼容历史数据

			// 旧的数据从excel中取
			slotRow.EntityInfo = strings.TrimSpace(cell)
			var entityInfos []entity.EntityMigrationInfo
			err := jsoniter.Unmarshal([]byte(slotRow.EntityInfo), &entityInfos)
			if err == nil {
				continue
			}
			// 新的数据从json文件中取
			if slotRow.EntityInfo != entity.GetSlotEntitiesFileKey(slotRow.SlotID) {
				return slotRow, false
			}
			entityFile, ok := mapZipFile[slotRow.EntityInfo]
			if !ok {
				return slotRow, false
			}
			r, err := entityFile.Open()
			if err != nil {
				return slotRow, false
			}
			body, err := io.ReadAll(r)
			if err != nil {
				return slotRow, false
			}
			slotRow.EntityInfo = string(body)
			err = r.Close()
			if err != nil {
				return slotRow, false
			}
		}
	}
	return slotRow, true
}

// slotCheck 槽位数据校验
type slotCheck struct {
	Index int      // Excel中Index
	Param []string // [槽位ID,槽位名称,槽位描述,槽位示例,实体信息]
}

// isLegal 校验槽位是否合法
func (s slotCheck) isLegal(slotID, slotName, slotDesc, slotExamples, entityInfo string) bool {
	if len(s.Param) != 5 {
		return false
	}
	return s.Param[0] == slotID && s.Param[1] == slotName && s.Param[2] == slotDesc &&
		s.Param[3] == slotExamples && s.Param[4] == entityInfo
}

// checkSlotRow 槽位信息导入行校验
func checkSlotRow(slotRow *entity.SlotRow, i int, uniquesKeys map[string]int, mapIntent map[string]entity.IntentRow,
	mapSlotIDCheck, mapSlotNameCheck map[string]slotCheck) []string {
	errMsgs := make([]string, 0)
	// 意图ID
	intentIDLen := utf8.RuneCountInString(slotRow.IntentID)
	if intentIDLen == 0 {
		errMsgs = append(errMsgs, "意图ID为空")
	} else {
		if _, ok := mapIntent[slotRow.IntentID]; !ok {
			errMsgs = append(errMsgs, "意图ID在任务流程文件中不存在")
		}
	}
	// 槽位ID
	slotIDLen := utf8.RuneCountInString(slotRow.SlotID)
	if slotIDLen == 0 {
		errMsgs = append(errMsgs, "槽位ID为空")
	} else {
		intentSlotKey := slotRow.IntentID + "_" + slotRow.SlotID
		if rowIndex, ok := uniquesKeys[intentSlotKey]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("意图ID和槽位ID与第%d行重复", rowIndex+1))
		} else {
			uniquesKeys[intentSlotKey] = i
		}
		if check, ok := mapSlotIDCheck[slotRow.SlotID]; ok &&
			!check.isLegal(slotRow.SlotID, slotRow.SlotName, slotRow.SlotDesc,
				slotRow.SlotExamples, slotRow.EntityInfo) {
			errMsgs = append(errMsgs, fmt.Sprintf("槽位数据与第%d行冲突", check.Index+1))
		} else {
			mapSlotIDCheck[slotRow.SlotID] = slotCheck{
				Index: i,
				Param: []string{slotRow.SlotID, slotRow.SlotName, slotRow.SlotDesc,
					slotRow.SlotExamples, slotRow.EntityInfo},
			}
		}
	}
	// 槽位名称
	slotNameLen := utf8.RuneCountInString(slotRow.SlotName)
	if slotNameLen == 0 {
		errMsgs = append(errMsgs, "槽位名称为空")
	} else {
		if slotNameLen > config.GetMainConfig().Entry.MaxEntityNameLen {
			errMsgs = append(errMsgs, "槽位名称长度超过字符限制")
		}
		if check, ok := mapSlotNameCheck[slotRow.SlotName]; ok &&
			!check.isLegal(slotRow.SlotID, slotRow.SlotName, slotRow.SlotDesc,
				slotRow.SlotExamples, slotRow.EntityInfo) {
			errMsgs = append(errMsgs, fmt.Sprintf("槽位数据与第%d行冲突", check.Index+1))
		} else {
			mapSlotNameCheck[slotRow.SlotName] = slotCheck{
				Index: i,
				Param: []string{slotRow.SlotID, slotRow.SlotName, slotRow.SlotDesc,
					slotRow.SlotExamples, slotRow.EntityInfo},
			}
		}
	}
	// 槽位描述｜示例｜实体信息校验
	errMsgs = append(errMsgs, checkSlotRowLegal(slotRow)...)
	return errMsgs
}

// checkSlotRowLegal 槽位描述｜示例｜实体信息导入行校验
func checkSlotRowLegal(slotRow *entity.SlotRow) []string {
	errMsgs := make([]string, 0)

	// 槽位描述
	slotDescLen := utf8.RuneCountInString(slotRow.SlotDesc)
	if slotDescLen == 0 {
		// 槽位描述可空
		// errMsgs = append(errMsgs, "槽位描述为空")
	} else {
		if slotDescLen > config.GetMainConfig().Entry.MaxEntityDescLen {
			errMsgs = append(errMsgs, "槽位描述长度超过字符限制")
		}
	}

	// 槽位示例
	var slotExamples []string
	slotExamplesLen := utf8.RuneCountInString(slotRow.SlotExamples)
	if slotExamplesLen == 0 {
		// 槽位示例可空
		// errMsgs = append(errMsgs, "槽位示例为空")
	} else {
		err := jsoniter.Unmarshal([]byte(slotRow.SlotExamples), &slotExamples)
		if err != nil {
			errMsgs = append(errMsgs, fmt.Sprintf("槽位示例非法，错误详情：%s", err.Error()))
		}
		if len(slotExamples) > config.GetMainConfig().Entry.MaxEntityExampleNum {
			errMsgs = append(errMsgs, "槽位示例个数超过系统限制")
		}
	}

	// 槽位实体
	var entityInfos []entity.EntityMigrationInfo
	entityInfoLen := utf8.RuneCountInString(slotRow.EntityInfo)
	if entityInfoLen == 0 {
		errMsgs = append(errMsgs, "实体信息为空")
	} else {
		err := jsoniter.Unmarshal([]byte(slotRow.EntityInfo), &entityInfos)
		if err != nil {
			errMsgs = append(errMsgs, fmt.Sprintf("实体信息非法，错误详情：%s", err.Error()))
		} else {
			if len(entityInfos) == 1 {
				if entityInfos[0].EntityName != slotRow.SlotName {
					errMsgs = append(errMsgs, "槽位名称和实体名称不一致")
				}
				if entityInfos[0].EntityDesc != slotRow.SlotDesc {
					errMsgs = append(errMsgs, "槽位描述和实体描述不一致")
				}
				if !types.EqualStringSlice(entityInfos[0].EntityExamples, slotExamples) {
					errMsgs = append(errMsgs, "槽位示例和实体示例不一致")
				}
				slotInfo := &entity.SlotMigrationInfo{
					SlotID:       slotRow.SlotID,
					SlotName:     slotRow.SlotName,
					SlotDesc:     slotRow.SlotDesc,
					SlotExamples: slotExamples,
					EntityInfo:   entityInfos,
				}
				if !slotInfo.IsLegal() {
					errMsgs = append(errMsgs, "槽位实体信息不合法或超限")
				}
			} else {
				errMsgs = append(errMsgs, "槽位必须且只能绑定一个具体实体")
			}
		}
	}
	return errMsgs
}

// parseImportTaskFlowFile
func parseImportTaskFlowFile(ctx context.Context, req *KEP.ImportTaskFlowReq, mapDBIntentName map[string]struct{},
	corpID uint64, robotId string) (*entity.TaskFlowImportData, *KEP.ImportTaskFlowRsp, error) {
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)
	// 解析zip文件
	mapZipFile, err := getImportTaskFlowFile(ctx, req)
	if err != nil {
		return nil, nil, err
	}
	isCheckSuccess := true
	// 解析任务流程文件
	taskFlowRows, isCheckSuccess, err, mapIntent, mapCategoryID := parseTaskFlow(ctx, mapZipFile, mapDBIntentName,
		isCheckSuccess, zipWriter, robotId)
	if err != nil {
		return nil, nil, err
	}
	// 解析分类文件
	categoryRows, isCheckSuccess, err := parseCategory(ctx, mapZipFile, mapCategoryID, isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析意图文件
	intentRows, isCheckSuccess, err := parseIntent(ctx, mapZipFile, mapIntent, isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析语料文件
	corpusRows, isCheckSuccess, err := parseCorpus(ctx, mapZipFile, mapIntent, isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析槽位文件
	slotRows, isCheckSuccess, err := parseSlot(ctx, mapZipFile, mapIntent, isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析示例问法文件
	intentCorpusRows, isCheckSuccess, err := parseIntentCorpusExample(ctx, mapZipFile, mapIntent, robotId,
		isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析自定义变量文件
	intentVarRows, isCheckSuccess, err := parseVar(ctx, mapZipFile, mapIntent, robotId, isCheckSuccess, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	zipWriter.Close()
	// 全部检查成功后才导入
	if isCheckSuccess {
		return &entity.TaskFlowImportData{
			CategoryData:     categoryRows,
			TaskFlowData:     taskFlowRows,
			IntentData:       intentRows,
			CorpusData:       corpusRows,
			SlotData:         slotRows,
			IntentCorpusData: intentCorpusRows,
			IntentVarData:    intentVarRows,
		}, nil, nil
	}
	checkZipFileName := fmt.Sprintf("错误_%s", req.GetFileName())
	cosPath := cos.GetCorpCOSFilePath(corpID, checkZipFileName)
	if err := cos.StorageCli.PutObject(ctx, zipBuffer.Bytes(), cosPath); err != nil {
		log.ErrorContextf(ctx, "putObject Failed! err:%s", err)
		return nil, nil, err
	}
	url, err := cos.StorageCli.GetPreSignedURL(ctx, cosPath)
	if err != nil {
		log.ErrorContextf(ctx, "getPreSingedURL Failed! err:%+v", err)
		return nil, nil, err
	}
	return nil, &KEP.ImportTaskFlowRsp{ErrorMsg: "文件数据存在错误，请下载并查看错误标注文件", ErrorLink: url,
		ErrorLinkText: "下载"}, nil
}

// parseTaskFlow 解析任务流程文件
func parseTaskFlow(ctx context.Context, mapZipFile map[string]*zip.File, mapDBIntentName map[string]struct{},
	isCheckSuccess bool, zipWriter *zip.Writer, robotId string) ([]*entity.TaskFlowRow, bool, error,
	map[string]entity.IntentRow, map[string]struct{}) {
	taskFlowFileName := config.GetMainConfig().TaskFlow.TaskFlowFileName
	checkTaskFlowFileName := taskFlowFileName
	taskFlowRows, taskFlowBuf, ok, err := parseTaskFlowXlsx(ctx, mapZipFile, taskFlowFileName, mapDBIntentName, robotId)
	if err != nil {
		return nil, false, err, nil, nil
	}
	if !ok {
		isCheckSuccess = false
		checkTaskFlowFileName = fmt.Sprintf("错误_%s", taskFlowFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkTaskFlowFileName, Data: taskFlowBuf})
	mapIntent, mapCategoryID := getIntentAndCategoryID(taskFlowRows)
	return taskFlowRows, isCheckSuccess, nil, mapIntent, mapCategoryID
}

// parseCategory 解析分类文件
func parseCategory(ctx context.Context, mapZipFile map[string]*zip.File, mapCategoryID map[string]struct{},
	isCheckSuccess bool, zipWriter *zip.Writer) ([]*entity.CategoryRow, bool, error) {
	categoryFileName := config.GetMainConfig().TaskFlow.CategoryFileName
	checkCategoryFileName := categoryFileName
	categoryRows, categoryBuf, ok, err := parseCategoryXlsx(ctx, mapZipFile[categoryFileName], mapCategoryID)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		isCheckSuccess = false
		checkCategoryFileName = fmt.Sprintf("错误_%s", categoryFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkCategoryFileName, Data: categoryBuf})
	return categoryRows, isCheckSuccess, nil
}

// parseIntent 解析意图文件
func parseIntent(ctx context.Context, mapZipFile map[string]*zip.File, mapIntent map[string]entity.IntentRow,
	isCheckSuccess bool, zipWriter *zip.Writer) ([]*entity.IntentRow, bool, error) {
	intentFileName := config.GetMainConfig().TaskFlow.IntentFileName
	checkIntentFileName := intentFileName
	intentRows, intentBuf, ok, err := parseIntentXlsx(ctx, mapZipFile[intentFileName], mapIntent)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		isCheckSuccess = false
		checkIntentFileName = fmt.Sprintf("错误_%s", intentFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkIntentFileName, Data: intentBuf})
	return intentRows, isCheckSuccess, nil
}

// parseCorpus 解析语料文件
func parseCorpus(ctx context.Context, mapZipFile map[string]*zip.File, mapIntent map[string]entity.IntentRow,
	isCheckSuccess bool, zipWriter *zip.Writer) ([]*entity.CorpusRow, bool, error) {
	corpusFileName := config.GetMainConfig().TaskFlow.CorpusFileName
	checkCorpusFileName := corpusFileName
	corpusRows, corpusBuf, ok, err := parseCorpusXlsx(ctx, mapZipFile[corpusFileName], mapIntent)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		isCheckSuccess = false
		checkCorpusFileName = fmt.Sprintf("错误_%s", corpusFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkCorpusFileName, Data: corpusBuf})
	return corpusRows, isCheckSuccess, nil
}

// parseSlot 解析槽位文件
func parseSlot(ctx context.Context, mapZipFile map[string]*zip.File, mapIntent map[string]entity.IntentRow,
	isCheckSuccess bool, zipWriter *zip.Writer) ([]*entity.SlotRow, bool, error) {
	slotFileName := config.GetMainConfig().TaskFlow.SlotFileName
	checkSlotFileName := slotFileName
	slotRows, slotBuf, ok, err := parseSlotXlsx(ctx, mapZipFile, slotFileName, mapIntent)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		isCheckSuccess = false
		checkSlotFileName = fmt.Sprintf("错误_%s", slotFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkSlotFileName, Data: slotBuf})
	return slotRows, isCheckSuccess, nil
}

// parseIntentCorpusExample 解析示例问法文件
func parseIntentCorpusExample(ctx context.Context, mapZipFile map[string]*zip.File,
	mapIntent map[string]entity.IntentRow, robotId string, isCheckSuccess bool, zipWriter *zip.Writer) (
	[]*entity.IntentExampleRow, bool, error) {
	intentExampleFileName := config.GetMainConfig().TaskFlow.IntentExampleFileName
	checkIntentExampleFileName := intentExampleFileName
	var intentCorpusRows []*entity.IntentExampleRow
	var intentCorpusBuf []byte
	var err error
	// 兼容示例问法文件不存在
	if _, ok := mapZipFile[intentExampleFileName]; ok {
		// 解析示例问法文件
		intentCorpusRows, intentCorpusBuf, ok, err = parseIntentCorpusExampleXlsx(ctx,
			mapZipFile[intentExampleFileName], mapIntent, robotId)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			isCheckSuccess = false
			checkIntentExampleFileName = fmt.Sprintf("错误_%s", intentExampleFileName)
		}
		_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkIntentExampleFileName, Data: intentCorpusBuf})
	}
	return intentCorpusRows, isCheckSuccess, nil
}

// parseVar 解析自定义变量文件
func parseVar(ctx context.Context, mapZipFile map[string]*zip.File, mapIntent map[string]entity.IntentRow,
	robotId string, isCheckSuccess bool, zipWriter *zip.Writer) ([]*entity.IntentVarRow, bool, error) {
	intentVarParamsFileName := config.GetMainConfig().TaskFlow.VarParamsFileName
	checkIntentVarParamsName := intentVarParamsFileName
	var intentVarRows []*entity.IntentVarRow
	var intentVarRowsBuf []byte
	var err error
	if _, ok := mapZipFile[intentVarParamsFileName]; ok {
		// 解析 变量文件
		intentVarRows, intentVarRowsBuf, ok, err = parseIntentVarXlsx(ctx, mapZipFile[intentVarParamsFileName],
			mapIntent, robotId)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			isCheckSuccess = false
			checkIntentVarParamsName = fmt.Sprintf("错误_%s", intentVarParamsFileName)
		}
		_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkIntentVarParamsName, Data: intentVarRowsBuf})
	}
	return intentVarRows, isCheckSuccess, nil
}
