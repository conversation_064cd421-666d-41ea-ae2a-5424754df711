package taskflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	llm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"github.com/PuerkitoBio/goquery"
)

// PreviewTaskFlowRequestNode 预览TaskFlow的询问节点
func PreviewTaskFlowRequestNode(ctx context.Context, req *KEP.PreviewTaskFlowRequestNodeReq) (
	rsp *KEP.PreviewTaskFlowRequestNodeRsp, err error) {
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode start|%s|requestNodeReq:%v", req)
	// 添加判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	contents, err := GetRequestNodeModePreview(ctx, req, appInfo)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode failed err:%+v", err)
		return nil, err
	}
	rsp = &KEP.PreviewTaskFlowRequestNodeRsp{
		ProbePreviews: contents,
	}
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode Success rsp:+%v", rsp)
	return rsp, nil

}

// PreviewTaskFlowAnswerNode 预览TaskFlow的答案节点
func PreviewTaskFlowAnswerNode(ctx context.Context, req *KEP.PreviewTaskFlowAnswerNodeReq) (
	rsp *KEP.PreviewTaskFlowAnswerNodeRsp, err error) {
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode start req:%v", req)
	// 添加判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	contents, err := GetAnswerNodeModePreview(ctx, req, appInfo)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode failed err:%+v", err)
		return nil, err
	}
	rsp = &KEP.PreviewTaskFlowAnswerNodeRsp{
		ProbePreviews: contents,
	}
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode Success rsp:+%v", rsp)
	return rsp, nil

}

// PreviewAnswerNodeDocument 预览答案节点的知识文档
func PreviewAnswerNodeDocument(ctx context.Context, req *KEP.PreviewAnswerNodeDocumentReq) (
	rsp *KEP.PreviewAnswerNodeDocumentRsp, err error) {
	log.InfoContextf(ctx, "PreviewAnswerNodeDocument start req:%v", req)
	// 添加判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	chatResponse, err := GetAnswerNodeDocumentPreview(ctx, req, appInfo)
	rsp = new(KEP.PreviewAnswerNodeDocumentRsp)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument failed|err:%+v", err)
		return nil, err
	}
	if chatResponse != nil {
		if chatResponse.GetMessage() != nil {
			rsp.Content = chatResponse.GetMessage().Content
		}
		if chatResponse.References != nil && len(chatResponse.References) > 0 {
			docs := make([]*KEP.Doc, 0)
			for _, reference := range chatResponse.References {
				Doc := &KEP.Doc{
					ReferId: strconv.FormatUint(reference.ID, 10),
					DocType: reference.Type,
					DocName: reference.Name,
				}
				docs = append(docs, Doc)
			}
			rsp.Docs = docs
		}
	}
	//Doc := &KEP.Doc{
	//	DocId:   "123123",
	//	DocType: 1,
	//	DocName: "测试预览文档.doc",
	//}
	//docs := make([]*KEP.Doc, 0)
	//docs = append(docs, Doc)
	//rsp = &KEP.PreviewAnswerNodeDocumentRsp{
	//	Content: "123",
	//	Docs:    docs,
	//}
	log.InfoContextf(ctx, "PreviewAnswerNodeDocument Success rsp:+%v", rsp)
	return rsp, nil

}

// GetRequestNodeModePreview 询问节点大模型效果预览
func GetRequestNodeModePreview(ctx context.Context, requestNodeReq *KEP.PreviewTaskFlowRequestNodeReq,
	appInfo *pb.GetAppInfoRsp) (
	[]string, error) {
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode prompt start")
	contents := make([]string, 0)
	botBizID := fmt.Sprintf("%d", requestNodeReq.GetBotBizId())
	req, err := checkRequestNodeModelPreviewReq(ctx, requestNodeReq)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode prompt failed|err:%+v", err)
		return contents, err
	}
	var query = config.GetMainConfig().LLM.RequestNodeQuery
	text, _ := json.Marshal(req)
	prompt := query + string(text)
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode prompt|%s",
		strings.Replace(prompt, "\n", "   ", -1))
	status := rpc.GetDescribeAccountStatus(ctx, appInfo)
	if status == 1 {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode status not used:%+v", err)
		return contents, errors.ErrTokenNotEnough
	}
	// 构建llm接口请求
	request := &llm.Request{
		RequestId:   util.RequestID(ctx),
		ModelName:   config.GetMainConfig().LLM.ModelName,
		AppKey:      botBizID,
		RequestType: llm.RequestType_ONLINE,
		PromptType:  llm.PromptType_TEXT,
	}
	promptMessage := llm.Message{
		Role:    llm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode chat request|%v", request)
	startTime := time.Now()
	resp, err := rpc.TaskFlowNodeModePreview(ctx, request)
	if err != nil || resp.GetCode() != 0 || resp.GetMessage() == nil {
		log.Errorf("PreviewTaskFlowRequestNode chat Failed|%s|resp:%+v|err:%+v", util.RequestID(ctx), resp, err)
		return contents, err
	}
	endTime := time.Now()
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode chat rsp|%v", resp)
	if config.GetMainConfig().ContentCheck {
		isEvil, err := rpc.Check(ctx, resp.GetMessage().GetContent(), appInfo.GetInfosecBizType())
		log.InfoContextf(ctx, "PreviewTaskFlowRequestNode check evil|%s| err|%v", isEvil, err)
		if isEvil {
			err1 := rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime,
				float64(resp.GetStatisticInfo().GetInputTokens()),
				float64(resp.GetStatisticInfo().GetOutputTokens()), 0)
			if err1 != nil {
				log.ErrorContextf(ctx, "PreviewAnswerNodeDocument ReportToken failed:%+v", err)
				return nil, errors.ErrTokenReportFailed
			}
			return contents, err
		}
	}
	err = rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime, float64(resp.GetStatisticInfo().GetInputTokens()),
		float64(resp.GetStatisticInfo().GetOutputTokens()), 1)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode ReportToken failed:%+v", err)
		return contents, errors.ErrTokenReportFailed
	}
	contents = append(contents, resp.GetMessage().GetContent())
	//}
	log.InfoContextf(ctx, "PreviewTaskFlowRequestNode chat Success resp:%+v", resp)
	return contents, nil
}

func checkRequestNodeModelPreviewReq(ctx context.Context, requestNodeReq *KEP.PreviewTaskFlowRequestNodeReq) (
	entity.RequestNodeReq, error) {
	var req entity.RequestNodeReq
	if requestNodeReq.RequestNodeReq == nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode check requestNodeReq is empty")
		return req, errors.ErrTaskFlowNodePreview
	}
	if len(requestNodeReq.RequestNodeReq.IntentName) <= 0 {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode check requestNodeReq.IntentName is empty")
		return req, errors.ErrTaskFlowNodePreview
	}
	req.IntentName = requestNodeReq.RequestNodeReq.IntentName
	if requestNodeReq.RequestNodeReq.SlotInfo == nil || len(requestNodeReq.RequestNodeReq.SlotInfo.SlotNames) <= 0 {
		log.ErrorContextf(ctx, "PreviewTaskFlowRequestNode check requestNodeReq.SlotInfo is empty")
		return req, errors.ErrTaskFlowNodePreview
	}
	slotNameList := make([]string, 0)
	for _, slotName := range requestNodeReq.RequestNodeReq.SlotInfo.SlotNames {
		exist := containsList(slotNameList, slotName)
		if !exist {
			slotNameList = append(slotNameList, slotName)
		}
	}
	req.SlotInfo.SlotNames = slotNameList
	entityNameList := make([]string, 0)
	for _, entityName := range requestNodeReq.RequestNodeReq.SlotInfo.EntityNames {
		exist := containsList(entityNameList, entityName)
		if !exist {
			entityNameList = append(entityNameList, entityName)
		}
	}
	req.SlotInfo.EntityNames = entityNameList
	return req, nil
}

// GetAnswerNodeModePreview 答案节点大模型效果预览
func GetAnswerNodeModePreview(ctx context.Context, answerNodeReq *KEP.PreviewTaskFlowAnswerNodeReq,
	appInfo *pb.GetAppInfoRsp) (
	[]string, error) {
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode prompt start")
	botBizID := fmt.Sprintf("%d", answerNodeReq.GetBotBizId())
	contents := make([]string, 0)
	// 校验并组装prompt
	prompt, err := checkAnswerNodeModelPreviewReq(ctx, answerNodeReq)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode prompt failed|err:%+v", err)
		return contents, err
	}
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode prompt|%s",
		strings.Replace(prompt, "\n", "   ", -1))
	status := rpc.GetDescribeAccountStatus(ctx, appInfo)
	if status == 1 {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode status not used:%+v", err)
		return contents, errors.ErrTokenNotEnough
	}
	// 构建llm接口请求
	request := &llm.Request{
		RequestId:   util.RequestID(ctx),
		ModelName:   config.GetMainConfig().LLM.ModelName,
		AppKey:      botBizID,
		RequestType: llm.RequestType_ONLINE,
		PromptType:  llm.PromptType_TEXT,
	}
	promptMessage := llm.Message{
		Role:    llm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode chat request|%v", request)
	startTime := time.Now()
	resp, err := rpc.TaskFlowNodeModePreview(ctx, request)
	if err != nil || resp.GetCode() != 0 || resp.GetMessage() == nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode chat Failed resp:%+v|err:%+v", resp, err)
		return contents, err
	}
	endTime := time.Now()
	if config.GetMainConfig().ContentCheck {
		isEvil, err := rpc.Check(ctx, resp.GetMessage().GetContent(), appInfo.GetInfosecBizType())
		log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode check evil|%s| err|%v", isEvil, err)
		if isEvil {
			err1 := rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime,
				float64(resp.GetStatisticInfo().GetInputTokens()),
				float64(resp.GetStatisticInfo().GetOutputTokens()), 0)
			if err1 != nil {
				log.ErrorContextf(ctx, "PreviewAnswerNodeDocument ReportToken failed:%+v", err)
				return nil, errors.ErrTokenReportFailed
			}
			return contents, err
		}
	}
	err = rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime, float64(resp.GetStatisticInfo().GetInputTokens()),
		float64(resp.GetStatisticInfo().GetOutputTokens()), 1)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode ReportToken failed:%+v", err)
		return contents, errors.ErrTokenReportFailed
	}
	contents = append(contents, resp.GetMessage().GetContent())
	log.InfoContextf(ctx, "PreviewTaskFlowAnswerNode chat Success resp.content:%s", contents)
	return contents, nil
}

func checkAnswerNodeModelPreviewReq(ctx context.Context, answerNodeReq *KEP.PreviewTaskFlowAnswerNodeReq) (
	string, error) {
	var prompt string
	if answerNodeReq.AnswerNodeReq == nil {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode check answerNodeReq is empty")
		return prompt, errors.ErrTaskFlowNodePreview
	}
	// 如果prompt有值，则将用户自定义的prompt富文本转换成字符串传给大模型
	if len(answerNodeReq.AnswerNodeReq.Prompt) > 0 {
		var err error
		prompt, err = convertText(answerNodeReq.AnswerNodeReq.Prompt, ctx)
		if err != nil {
			log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode convertText prompt error")
			return prompt, errors.PreviewError("自定义prompt解析错误")
		}
		return prompt, nil
	}
	if len(answerNodeReq.AnswerNodeReq.IntentName) <= 0 {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode check answerNodeReq.IntentName is empty")
		return prompt, errors.ErrTaskFlowNodePreview
	}
	if answerNodeReq.AnswerNodeReq.NodeType == KEP.NodeType_API.String() {
		prompt = apiPrompt(prompt, answerNodeReq)
	} else if answerNodeReq.AnswerNodeReq.NodeType == KEP.NodeType_REQUEST.String() {
		prompt = requestPrompt(prompt, answerNodeReq)
	} else {
		log.ErrorContextf(ctx, "PreviewTaskFlowAnswerNode check answerNodeReq.type is error")
		return prompt, errors.ErrTaskFlowNodePreview
	}
	return prompt, nil
}

func apiPrompt(prompt string, answerNodeReq *KEP.PreviewTaskFlowAnswerNodeReq) string {
	prompt += "以客服的语气，根据以下的信息用一句话向用户告知操作执行结果，需要包括完整的执行操作的名称和参数：\n"
	prompt += "【执行操作】\n" + answerNodeReq.AnswerNodeReq.IntentName + "\n"
	prompt += "【执行参数】\n"
	if len(answerNodeReq.AnswerNodeReq.SlotNames) > 0 {
		prompt += "{\n"
		for i, slotName := range answerNodeReq.AnswerNodeReq.SlotNames {
			if i == len(answerNodeReq.AnswerNodeReq.SlotNames)-1 {
				prompt += "\"" + slotName + "\":\"" + slotName + "\"\n"
			} else {
				prompt += "\"" + slotName + "\":\"" + slotName + "\",\n"
			}
		}
		prompt += "}\n"
	}
	prompt += "【执行结果】\n"
	promptConditions := make([]string, 0)
	// 处理一层逻辑
	if answerNodeReq.AnswerNodeReq.Condition != nil {
		promptConditions = easyConditionValue(answerNodeReq.AnswerNodeReq.Condition, promptConditions)
	}
	// 处理嵌套条件数据
	if answerNodeReq.AnswerNodeReq.Condition != nil && len(answerNodeReq.AnswerNodeReq.Condition.ConditionsLogic) > 0 &&
		len(answerNodeReq.AnswerNodeReq.Condition.Conditions) > 0 {
		promptConditions = apiPromptAnswerCondition(answerNodeReq, promptConditions)
	}
	if len(promptConditions) > 0 {
		if answerNodeReq.AnswerNodeReq.Condition.ConditionsLogic == KEP.ConditionInfo_AND.String() {
			for i, condition := range promptConditions {
				if i == len(promptConditions)-1 {
					prompt += condition
				} else {
					prompt += condition + ","
				}
			}
		} else {
			prompt += promptConditions[0]
		}
		prompt += "\n"
	}
	prompt += "将\"执行参数\", \"执行操作\"总结成一句完整通顺的话"
	return prompt
}

func easyConditionValue(condition *KEP.ComplexCondition, promptConditions []string) []string {
	if condition.Expression != nil && len(condition.Expression.Values) > 0 {
		if condition.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
			(condition.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
				condition.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
			for _, value := range condition.Expression.Values {
				exist := containsList(promptConditions, value)
				if !exist {
					promptConditions = append(promptConditions, value)
				}
			}
		}
	}
	return promptConditions
}

func easyCondition(condition *KEP.ComplexCondition, promptConditions []string) []string {
	if condition.Expression != nil && len(condition.Expression.Values) > 0 {
		if condition.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
			(condition.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
				condition.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
			var conditionValues string
			for i, value := range condition.Expression.Values {
				if i < len(condition.Expression.Values)-1 {
					conditionValues += value + ","
				} else {
					conditionValues += value
				}
			}
			promptConditions = append(promptConditions, condition.Expression.ConditionName+
				"为"+conditionValues)
		}
	}
	return promptConditions
}

func requestPrompt(prompt string, answerNodeReq *KEP.PreviewTaskFlowAnswerNodeReq) string {
	prompt += "请以客服的语气生成一句【回复】，告诉用户满足以下【条件】后可以触发【结果】。\n"
	prompt += "【条件】\n"
	promptConditions := make([]string, 0)
	// 处理一层逻辑
	if answerNodeReq.AnswerNodeReq.Condition != nil {
		promptConditions = easyCondition(answerNodeReq.AnswerNodeReq.Condition, promptConditions)
	}
	// 处理嵌套条件数据
	if answerNodeReq.AnswerNodeReq.Condition != nil && len(answerNodeReq.AnswerNodeReq.Condition.ConditionsLogic) > 0 &&
		len(answerNodeReq.AnswerNodeReq.Condition.Conditions) > 0 {
		for _, conditionFirst := range answerNodeReq.AnswerNodeReq.Condition.Conditions {
			if conditionFirst != nil && conditionFirst.Expression != nil && len(conditionFirst.Expression.Values) > 0 {
				if conditionFirst.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
					(conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
						conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
					var conditionValues string
					for i, value := range conditionFirst.Expression.Values {
						if i < len(conditionFirst.Expression.Values)-1 {
							conditionValues += value + ","
						} else {
							conditionValues += value
						}
					}
					promptConditions = append(promptConditions, conditionFirst.Expression.ConditionName+
						"为"+conditionValues)
				}
			}
			promptConditions = requestPromptAnswerCondition(conditionFirst, promptConditions)
		}
	}
	if len(promptConditions) > 0 {
		for i, value := range promptConditions {
			if i < len(promptConditions)-1 {
				if answerNodeReq.AnswerNodeReq.Condition.ConditionsLogic == KEP.ConditionInfo_AND.String() {
					prompt += value + " 且 "
				} else {
					prompt += value + " 或 "
				}
			} else {
				prompt += value
			}
		}
	}
	prompt += "\n"
	prompt += "【结果】\n" + answerNodeReq.AnswerNodeReq.IntentName + "\n" + "【回复】"
	return prompt
}

// GetAnswerNodeDocumentPreview 预览答案节点的知识文档
func GetAnswerNodeDocumentPreview(ctx context.Context, req *KEP.PreviewAnswerNodeDocumentReq,
	appInfo *pb.GetAppInfoRsp) (
	*chat.GetAnswerFromDocsReply, error) {
	log.InfoContextf(ctx, "PreviewAnswerNodeDocument prompt start req:%v", req)
	// 校验并组装prompt
	prompt, err := checkDocumentReq(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument prompt failed|err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "PreviewAnswerNodeDocument prompt|%s",
		strings.Replace(prompt, "\n", "   ", -1))
	status := rpc.GetDescribeAccountStatus(ctx, appInfo)
	if status == 1 {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument status not used:%+v", err)
		return nil, errors.ErrTokenNotEnough
	}
	// 构建llm接口请求
	request := new(chat.GetAnswerFromDocsRequest)
	request.Question = prompt
	request.RobotID = req.GetBotBizId()
	request.FilterKey = chat.Filter_FILTER_SANDBOX
	request.SessionID = util.RequestID(ctx)
	startTime := time.Now()
	resp, err := rpc.DocumentPreview(ctx, request)
	if err != nil || resp.GetCode() != 0 || resp.GetMessage() == nil {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument chat Failed resp:%+v|err:%+v", resp, err)
		return nil, err
	}
	endTime := time.Now()
	if config.GetMainConfig().ContentCheck {
		isEvil, err := rpc.Check(ctx, resp.GetMessage().GetContent(), appInfo.GetInfosecBizType())
		log.InfoContextf(ctx, "PreviewAnswerNodeDocument check evil|%s| err|%v", isEvil, err)
		if isEvil {
			err1 := rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime,
				float64(resp.GetStatisticInfo().GetInputTokens()),
				float64(resp.GetStatisticInfo().GetOutputTokens()), 0)
			if err1 != nil {
				log.ErrorContextf(ctx, "PreviewAnswerNodeDocument ReportToken failed:%+v", err)
				return nil, errors.ErrTokenReportFailed
			}
			return nil, err
		}
	}
	err = rpc.ReportTokenDosage(ctx, appInfo, startTime, endTime, float64(resp.GetStatisticInfo().GetInputTokens()),
		float64(resp.GetStatisticInfo().GetOutputTokens()), 1)
	if err != nil {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument ReportToken failed:%+v", err)
		return nil, errors.ErrTokenReportFailed
	}
	log.InfoContextf(ctx, "PreviewAnswerNodeDocument chat Success resp:%v", resp)
	return resp, nil
}

func checkDocumentReq(ctx context.Context, documentReq *KEP.PreviewAnswerNodeDocumentReq) (
	string, error) {
	var prompt string
	if documentReq.AnswerNodeDocumentReq == nil {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument checkDocumentReq answerNodeReq is empty")
		return prompt, errors.ErrTaskFlowNodePreview
	}
	if len(documentReq.AnswerNodeDocumentReq.IntentName) <= 0 {
		log.ErrorContextf(ctx, "PreviewAnswerNodeDocument checkDocumentReq answerNodeReq.IntentName is empty")
		return prompt, errors.ErrTaskFlowNodePreview
	}
	if len(documentReq.AnswerNodeDocumentReq.Conditions) > 0 {
		// 处理嵌套条件数据
		slotValues := make([]string, 0)
		for _, condition := range documentReq.AnswerNodeDocumentReq.Conditions {
			if condition != nil {
				slotValues = easyConditionValue(condition, slotValues)
			}
			if len(condition.ConditionsLogic) > 0 && len(condition.Conditions) > 0 {
				for _, conditionFirst := range condition.Conditions {
					if conditionFirst != nil && conditionFirst.Expression != nil &&
						len(conditionFirst.Expression.Values) > 0 {
						if conditionFirst.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
							(conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
								conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
							for _, value := range conditionFirst.Expression.Values {
								exist := containsList(slotValues, value)
								if !exist {
									slotValues = append(slotValues, value)
								}
							}
						}
					}
					slotValues = documentConditionTwo(conditionFirst, slotValues)
				}
			}
		}
		if len(slotValues) > 0 {
			for _, slotValue := range slotValues {
				prompt += "{" + slotValue + "} "
			}
		}
	}
	prompt += "\n{" + documentReq.AnswerNodeDocumentReq.IntentName + "}"
	return prompt, nil
}

func requestPromptAnswerCondition(conditionFirst *KEP.ComplexCondition, promptConditions []string) []string {
	if conditionFirst != nil && len(conditionFirst.ConditionsLogic) > 0 && len(conditionFirst.Conditions) > 0 {
		promptConditionTwos := make([]string, 0)
		for _, conditionTwo := range conditionFirst.Conditions {
			if conditionTwo != nil && conditionTwo.Expression != nil &&
				len(conditionTwo.Expression.Values) > 0 {
				if conditionTwo.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
					(conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
						conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
					var conditionValueTwos string
					for i, value := range conditionTwo.Expression.Values {
						if i < len(conditionTwo.Expression.Values)-1 {
							conditionValueTwos += value + ","
						} else {
							conditionValueTwos += value
						}
					}
					promptConditionTwos = append(promptConditionTwos, conditionTwo.Expression.ConditionName+
						"为"+conditionValueTwos)
				}
			}
		}
		if len(promptConditionTwos) > 0 {
			var conditionTwo string
			for i, value := range promptConditionTwos {
				if i < len(promptConditionTwos)-1 {
					if conditionFirst.ConditionsLogic == KEP.ConditionInfo_AND.String() {
						conditionTwo += value + "且"
					} else {
						conditionTwo += value + "或"
					}
				} else {
					conditionTwo += value
				}
			}
			promptConditions = append(promptConditions, conditionTwo)
		}
	}
	return promptConditions
}

func apiPromptAnswerCondition(answerNodeReq *KEP.PreviewTaskFlowAnswerNodeReq, promptConditions []string) []string {
	for _, conditionFirst := range answerNodeReq.AnswerNodeReq.Condition.Conditions {
		if conditionFirst != nil && conditionFirst.Expression != nil && len(conditionFirst.Expression.Values) > 0 {
			if conditionFirst.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
				(conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
					conditionFirst.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
				promptConditions = append(promptConditions, conditionFirst.Expression.Values...)
			}
		}
		if conditionFirst != nil && len(conditionFirst.ConditionsLogic) > 0 && len(conditionFirst.Conditions) > 0 {
			for _, conditionTwo := range conditionFirst.Conditions {
				if conditionTwo != nil && conditionTwo.Expression != nil &&
					len(conditionTwo.Expression.Values) > 0 {
					if conditionTwo.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
						(conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
							conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
						promptConditions = append(promptConditions, conditionTwo.Expression.Values...)
					}
				}
			}
		}
	}
	return promptConditions
}

func documentConditionTwo(conditionFirst *KEP.ComplexCondition, slotValues []string) []string {
	if conditionFirst != nil && len(conditionFirst.ConditionsLogic) > 0 &&
		len(conditionFirst.Conditions) > 0 {
		for _, conditionTwo := range conditionFirst.Conditions {
			if conditionTwo != nil && conditionTwo.Expression != nil &&
				len(conditionTwo.Expression.Values) > 0 {
				if conditionTwo.Expression.SourceType != KEP.ConditionInfo_BranchCondition_UNKNOWN.String() &&
					(conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_CONTAINS.String() ||
						conditionTwo.Expression.Operator == KEP.ConditionInfo_BranchCondition_EQ.String()) {
					for _, value := range conditionTwo.Expression.Values {
						exist := containsList(slotValues, value)
						if !exist {
							slotValues = append(slotValues, value)
						}
					}
				}
			}
		}
	}
	return slotValues
}

//// GetRequestNodeModePreviewTest 询问节点大模型效果预览
//func GetRequestNodeModePreviewTest(ctx context.Context, requestNodeReq *KEP.PreviewTaskFlowRequestNodeReq) (
//	[]string, error) {
//	log.Infof("GetRequestNodeModePreviewTest start|%s", util.RequestID(ctx))
//	contents := make([]string, 0)
//	botBizID := fmt.Sprintf("%d", requestNodeReq.GetBotBizId())
//	var err error
//	// 组装prompt
//	var query = config.GetMainConfig().LLM.RequestNodeQuery
//	prompt := query + "{\"intent_name\":\"开发票\",\"slot\":{\"slot_name\":\"纳税人身份\",\"entitys\":[\"个人\",\"企业\"]}}"
//	var resp *PB_SmartAssistant.Response
//	// 构建llm接口请求
//	request := &PB_SmartAssistant.Request{
//		RequestId:   "14212412474563456",
//		ModelName:   config.GetMainConfig().LLM.ModelName,
//		AppKey:      botBizID,
//		RequestType: PB_SmartAssistant.RequestType_ONLINE,
//		PromptType:  PB_SmartAssistant.PromptType_TEXT,
//	}
//	promptMessage := PB_SmartAssistant.Message{
//		Role:    PB_SmartAssistant.Role_USER,
//		Content: prompt,
//	}
//	request.Messages = append(request.Messages, &promptMessage)
//	log.Infof("GetRequestNodeModePreviewTest request|%v", request)
//	// 与算法商定，工程侧请求三次，拼装三次结果
//	for i := 0; i < 3; i++ {
//		resp, err = rpc.TaskFlowNodeModePreview(ctx, request)
//		if err != nil || resp.Code != 0 || resp.Message == nil {
//			log.Errorf("GetLLMReply LLMSimpleChat Failed|%s|resp:%+v|err:%+v|contents:%+v", util.RequestID(ctx),
//				resp, err, contents)
//			return contents, err
//		}
//		contents = append(contents, resp.Message.Content)
//	}
//	log.Infof("GetRequestNodeModePreviewTest Success|%s|resp.content:%s", util.RequestID(ctx), contents)
//	return contents, nil
//}
//
//// GetAnswerNodeModePreviewTest 询问节点大模型效果预览
//func GetAnswerNodeModePreviewTest(ctx context.Context, requestNodeReq *KEP.PreviewTaskFlowRequestNodeReq) (
//	[]string, error) {
//	log.Infof("GetAnswerNodeModePreviewTest start|%s", util.RequestID(ctx))
//	contents := make([]string, 0)
//	botBizID := fmt.Sprintf("%d", requestNodeReq.GetBotBizId())
//	var err error
//	// 组装prompt
//	var query = config.GetMainConfig().LLM.AnswerNodeQuery
//	prompt := query + "{\"APIName\": \"订机票\",\"conditions\":" +
//		"{\"预定状态\":\"=\":预定成功\"}\"slots\": [\"出发地\", \"目的地\",\"出发时间\"]}"
//	var resp *PB_SmartAssistant.Response
//	// 构建llm接口请求
//	request := &PB_SmartAssistant.Request{
//		RequestId:   "142124124124",
//		ModelName:   config.GetMainConfig().LLM.ModelName,
//		AppKey:      botBizID,
//		RequestType: PB_SmartAssistant.RequestType_ONLINE,
//		PromptType:  PB_SmartAssistant.PromptType_TEXT,
//	}
//	promptMessage := PB_SmartAssistant.Message{
//		Role:    PB_SmartAssistant.Role_USER,
//		Content: prompt,
//	}
//	request.Messages = append(request.Messages, &promptMessage)
//	log.Infof("GetAnswerNodeModePreviewTest request|%v", request)
//	resp, err = rpc.TaskFlowNodeModePreview(ctx, request)
//	if err != nil || resp.Code != 0 || resp.Message == nil {
//		log.ErrorContextf(ctx, "GetLLMReply LLMSimpleChat Failed resp:%+v|err:%+v", resp, err)
//		return contents, err
//	}
//	contents = append(contents, resp.Message.Content)
//	log.InfoContextf(ctx, "GetAnswerNodeModePreviewTest Success resp.content:%s", contents)
//	return contents, nil
//}

func containsList(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// convertText 使用goquery解析html获取字符串
func convertText(text string, ctx context.Context) (string, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(text))
	if err != nil {
		log.ErrorContextf(ctx, "ConvertText NewDocumentFromReader failed err:%v", err)
		return "", nil
	}
	doc.Find("span").Each(func(i int, s *goquery.Selection) {
		////dataType, _ := s.Attr("data-type")
		//dataInfo, _ := s.Attr("data-info")
		//dataInfoText, _ := url.QueryUnescape(dataInfo)
		//var spanInfo entity.SpanInfo
		//if err = json.Unmarshal([]byte(dataInfoText), &spanInfo); err != nil {
		//	log.ErrorContextf(ctx, "ConvertText dataInfoText json failed,err:%v", err)
		//}
		//s.ReplaceWithHtml(spanInfo.Name)
		s.ReplaceWithHtml(s.Text())
	})

	newHTML, err := doc.Find("body").First().Html()
	if err != nil {
		log.ErrorContextf(ctx, "ConvertText new html failed,err:%v", err)
		return "", err
	}
	log.InfoContextf(ctx, "ConvertText newHTML:%v", newHTML)
	return newHTML, err
}
