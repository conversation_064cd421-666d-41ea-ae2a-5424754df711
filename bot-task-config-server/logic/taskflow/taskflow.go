// Package taskflow bot-task-config-server
// @(#)taskflow.go  星期四, 十二月 21, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.
package taskflow

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/admin"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	dbUtil "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idget"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/go-comm/set"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"golang.org/x/exp/maps"
)

// RecoverHistoryTaskFlow 恢复历史任务流程
func RecoverHistoryTaskFlow(ctx context.Context,
	req *KEP.RecoverHistoryTaskFlowReq) (*KEP.RecoverHistoryTaskFlowRsp, error) {
	var tfph *entity.TaskFlowPublishHistory
	var msg string

	sid := util.RequestID(ctx)
	botBizId := req.GetBotBizId()
	flowId := req.GetFlowId()
	version := req.GetBizVersion()
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|RecoverHistoryTaskFlow|%s|%v", sid, err)
		return nil, err
	}

	// 获取当前任务流程
	taskFlow, err := db.GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|RecoverHistoryTaskFlow,GetTaskFlowDetail,flowId:%s,err:%+v",
			sid, flowId, err.Error())
		return nil, errors.ErrTaskFlowNotFound
	}

	tfName := taskFlow.IntentName

	// 获取最新的任务流名称，替换json里面任务流程名称 （移除）,恢复的都是历史发布版本
	tfph, err = db.GetDHTFNewNameJSON(ctx, flowId, botBizId, entity.FlowTypeICS,
		version, tfName, entity.SavePublished)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|flowId:%s, DescribeHistoryTaskFlow,err:%s", sid, flowId, err.Error())
		return nil, errors.OpDataFromDBError("获取历史的任务流程失败")
	}

	// 恢复历史状态时，恢复槽位、词条、变量
	tfph, msg, err = db.RecoverRelatedData(ctx, botBizId, taskFlow.Action, tfph)

	if err != nil {
		log.ErrorContextf(ctx, "R|sid:%s|RecoverHistoryTaskFlow,err:%s", sid, err.Error())
		return nil, errors.BadRequestError("恢复历史任务流的槽位或变量错误")
	}

	// 有重复，可以让正常恢复，只是弹出重复的提示
	return &KEP.RecoverHistoryTaskFlowRsp{
		FlowId:     tfph.FlowID,
		DialogJson: tfph.FlowJson,
		ErrorMsg:   msg,
		IsAllow:    true,
	}, nil
}

// ListHistoryTaskFlow 拉取历史版本
func ListHistoryTaskFlow(ctx context.Context,
	req *KEP.ListHistoryTaskFlowReq) (*KEP.ListHistoryTaskFlowRsp, error) {
	var tfph *[]entity.TaskFlowPublishHistory
	var data []*KEP.ListHistoryTaskFlowRsp_TaskFlow
	var total int64
	sid := util.RequestID(ctx)

	botBizId := req.GetBotBizId()
	flowId := req.GetFlowId()
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|ListHistoryTaskFlow|%s|%v", sid, err)
		return nil, err
	}

	tfph, total, err = db.GetPublishTaskFlowList(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|flowId:%s|botBizId:%s|err:%+v", sid, flowId, botBizId, err)
		return nil, errors.OpDataFromDBError("获取任务流程历史版本错误")
	}

	for _, item := range *tfph {
		data = append(data, &KEP.ListHistoryTaskFlowRsp_TaskFlow{
			// 由于通过Uin拉取腾讯云全量用户信息存在问题，暂时只展示账号ID
			UserName:    item.SubUin,
			SaveType:    item.SaveTypeStr(),
			BizVersion:  item.Version,
			PublishTime: uint32(item.PublishTime.Unix()),
		})
	}
	return &KEP.ListHistoryTaskFlowRsp{
		Total: uint32(total),
		List:  data,
	}, nil
}

// DescribeHistoryTaskFlow 拉取历史详情
func DescribeHistoryTaskFlow(ctx context.Context,
	req *KEP.DescribeHistoryTaskFlowReq) (*KEP.DescribeHistoryTaskFlowRsp, error) {
	var tfph *entity.TaskFlowPublishHistory
	sid := util.RequestID(ctx)
	botBizId := req.GetBotBizId()
	flowId := req.GetFlowId()
	version := req.GetBizVersion()
	saveType := req.GetSaveType()

	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, entity.SandboxEnvScene, botId)
	if err != nil {
		log.WarnContextf(ctx, "P|DescribeHistoryTaskFlow|%s|%v", sid, err)
		return nil, err
	}

	taskFlow, err := db.GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetDHTFNewNameJSON,flowId:%s,err:%s",
			sid, flowId, err.Error())
		return nil, errors.ErrTaskFlowNotFound
	}
	tfName := taskFlow.IntentName

	// 获取最新的任务流名称，替换json里面任务流程名称
	tfph, err = db.GetDHTFNewNameJSON(ctx, flowId, botBizId, entity.FlowTypeICS,
		version, tfName, saveType)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|flowId:%s, DescribeHistoryTaskFlow,err:%s", sid, flowId, err.Error())
		return nil, errors.OpDataFromDBError("获取历史任务流程版本详情失败")
	}
	return &KEP.DescribeHistoryTaskFlowRsp{
		FlowId:     tfph.FlowID,
		DialogJson: tfph.FlowJson,
	}, nil
}

// GetTaskFlowReleaseStatus 查询任务流程的发布状态
func GetTaskFlowReleaseStatus(ctx context.Context, req *KEP.GetTaskFlowReleaseStatusReq) (*KEP.GetTaskFlowReleaseStatusResp, error) {
	var sid = util.RequestID(ctx)
	var envType = req.GetEnvTag()
	botBizId := req.GetBotBizId()
	var scene uint32 = entity.SandboxEnvScene // 评测环境
	if envType == entity.EnvTypeProd {
		scene = entity.ProdEnvScene
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobotWithNoUserLogin(ctx, scene, botId)
	if errors.Is(err, errors.ErrRobotNotFound) {
		log.Warnf("P|GetTaskFlowReleaseStatus|%s|%v", sid, err)
		return &KEP.GetTaskFlowReleaseStatusResp{
			IsRelease: false,
		}, nil
	}
	if err != nil {
		log.Errorf("P|GetTaskFlowReleaseStatus|%s|%v", sid, err)
		return nil, err
	}

	p := publish.NewPublish()
	isRelease, err := p.ICSTaskFlowReleaseStatus(ctx, botBizId, KEP.GetTaskFlowReleaseStatusReq_EnvType(envType))
	rsp := KEP.GetTaskFlowReleaseStatusResp{
		IsRelease: isRelease,
	}
	log.Infof("O|GetTaskFlowReleaseStatus|%s|%s|ERR:%v", sid, botBizId, envType)
	if err != nil {
		log.Errorf("E|GetTaskFlowReleaseStatus|%s|%s", sid, err.Error())
		return &rsp, errors.OpDataFromDBError("获取发布状态失败")
	}
	return &rsp, nil
}

// GroupTaskFlow 分组移动任务流到不同分类
func GroupTaskFlow(ctx context.Context, req *KEP.GroupTaskFlowReq) (*KEP.GroupTaskFlowRsp, error) {
	var cateId = req.GetCateBizId()
	var flowIds = set.RemoveDuplicatesAndEmpty(req.GetFlowIds()) //id去重
	var botBizId = req.GetBotBizId()
	var sid = util.RequestID(ctx)
	rsp := &KEP.GroupTaskFlowRsp{}
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GroupTaskFlow,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	// 检查分类是否存在
	_, err = checkCateAndRetrieveID(ctx, cateId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GroupTaskFlow.checkCateAndRetrieveID:%s|%s", sid, err.Error())
		return rsp, errors.ErrCateNotFound
	}
	//log.Infof("GroupTaskFlow, childrenIds:%v", childrenIds)
	tfs, err := db.GetTaskFLowDetails(ctx, flowIds, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GroupTaskFlow.GetTaskFLowDetails:%s|%s", sid, err.Error())
		return rsp, errors.ErrTaskFlowNotFound
	}

	err = db.GroupTaskFlow(ctx, cateId, maps.Keys(tfs))
	if err != nil {
		log.ErrorContextf(ctx, "GroupTaskFlow db.GroupTaskFlow:%s|%s", sid, err.Error())
		return nil, errors.OpDataFromDBError("分组移动失败")
	}
	return rsp, nil
}

// CreateTaskFlow 创建任务流程
func CreateTaskFlow(ctx context.Context, req *KEP.CreateTaskFlowReq) (*KEP.CreateTaskFlowRsp, error) {
	robotId := req.GetBotBizId()
	taskFlowName := strings.TrimSpace(req.GetName())
	cateId := req.GetCateBizId()
	taskFlowJson := strings.TrimSpace(req.GetDialogJson())
	desc := req.GetDesc()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	sid := util.RequestID(ctx)

	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "CreateTaskFlow,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}
	// 任务流名称校验
	log.InfoContextf(ctx, "CreateTaskFlow,checkTaskFlowName start")
	err = checkTaskFlowName(ctx, "", robotId, taskFlowName)
	if err != nil {
		log.ErrorContextf(ctx, "CreateTaskFlow, checkTaskFlowName, err:%s|%s", sid, err.Error())
		return nil, err
	}
	// 检查分类是否存在
	log.InfoContextf(ctx, "CreateTaskFlow, checkCateAndRetrieveID, cateId:%s|robotId:%s", cateId, robotId)
	if _, err := checkCateAndRetrieveID(ctx, cateId, robotId); err != nil {
		log.ErrorContextf(ctx, "CreateTaskFlow, checkCateAndRetrieveID, err:%s|%s", sid, err.Error())
		return nil, errors.ErrCateNotFound
	}

	// 画布JSON正确性初步校验
	log.InfoContextf(ctx, "CreateTaskFlow, JsonToTaskFlow, taskFlowJson:%s", taskFlowJson)
	tree, err := protoutil.JsonToTaskFlowForPreCheck(taskFlowJson)
	log.InfoContextf(ctx, "CreateTaskFlow, UnmarshalStr, tree:%v", tree)
	if err != nil {
		log.ErrorContextf(ctx, "CreateTaskFlow, checkCateAndRetrieveID, err:%s|%s", sid, err.Error())
		return nil, errors.ErrTaskFlowUIJsonParams
	}

	taskFlowData := entity.CreateTaskFlowParams{
		FlowID:           idgenerator.NewUUID(),
		IntentID:         idgenerator.NewUUID(),
		CorpusID:         idgenerator.NewUUID(),
		IntentName:       taskFlowName,
		IntentDesc:       desc,
		FlowState:        entity.FlowStateDraft,
		FlowType:         entity.FlowTypeICS,
		IntentType:       entity.IntentTypeFlow,
		IntentSource:     entity.IntentSourceIcs,
		Version:          config.GetMainConfig().VerifyTaskFlow.Version,
		CategoryID:       cateId,
		DialogJsonDraft:  taskFlowJson,
		DialogJsonEnable: "", // 校验成功才入此库
		Uin:              uin,
		SubUin:           subUin,
		ReleaseStatus:    entity.ReleaseStatusUnPublished,
		Action:           entity.ActionInsert,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
		RobotId:          robotId,
	}
	log.InfoContextf(ctx, "CreateTaskFlow:%+v", taskFlowData)
	err = db.CreateTaskFlow(ctx, taskFlowData)
	if err != nil {
		log.ErrorContextf(ctx, "db.CreateTaskFlow, err:%s|%s", sid, err.Error())
		return nil, errors.OpDataFromDBError("创建任务流程失败")
	}
	return &KEP.CreateTaskFlowRsp{
		FlowId: taskFlowData.FlowID,
	}, nil
}

// SaveTaskFlow 编辑更新TaskFlow
func SaveTaskFlow(ctx context.Context, req *KEP.SaveTaskFlowReq) (*KEP.SaveTaskFlowRsp, error) {
	var err error
	var taskFlowDetails *entity.TaskFlow
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	robotId := req.GetBotBizId()
	taskFlowName := strings.TrimSpace(req.GetName())
	cateId := req.GetCateBizId()
	taskFlowJson := strings.TrimSpace(req.GetDialogJson())
	taskFlowId := strings.TrimSpace(req.GetFlowId())
	desc := strings.TrimSpace(req.GetDesc())
	flag := req.GetFlag()
	var safeUrls []string

	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "SaveTaskFlow|CheckRobot:%s|%+v", err)
		return nil, err
	}
	if _, err = checkCateAndRetrieveID(ctx, cateId, robotId); err != nil { // 检查分类
		log.ErrorContextf(ctx, "SaveTaskFlow,checkCateAndRetrieveID err:+%v", err)
		return nil, errors.ErrCateNotFound
	}
	// 检查任务流程是否存在
	if taskFlowDetails, err = db.GetTaskFlowDetail(ctx, taskFlowId, robotId); err != nil {
		log.WarnContextf(ctx, "SaveTaskFlow,GetTaskFlowDetail err:%+v", err)
		return nil, errors.ErrTaskFlowNotFound
	}
	if taskFlowDetails == nil || taskFlowDetails.FlowID == "" {
		return nil, errors.ErrTaskFlowNotFound
	}
	// 任务流名称校验
	if err = checkTaskFlowName(ctx, taskFlowId, robotId, taskFlowName); err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow,checkTaskFlowName err:%s", err.Error())
		return nil, errors.BadRequestError("参数校验失败")
	}
	// 意图描述(可为空及重复) --- 现在页面不可为空；此处先保持现状，防止其他调用受影响
	if utf8.RuneCountInString(desc) > config.GetMainConfig().VerifyTaskFlow.IntentDescLen {
		log.WarnContextf(ctx, "SaveTaskFlow err:%s|%s", desc, errors.ErrIntentNameExceed)
		return nil, errors.ErrIntentNameExceed
	}
	// 画布JSON正确性初步校验
	tree, err := protoutil.JsonToTaskFlowForPreCheck(taskFlowJson)
	if err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow,GetTaskFlowDetail err:%+v", err)
		return nil, errors.ErrTaskFlowUIJsonParams
	}
	// 保存时获取关联实体，自定义变量，词条，实体条件全选词条
	slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList := idget.GetAllRelatedFromTree(ctx, tree)
	entryIDList = db.GetRelatedEntryFromTreeSlot(ctx, slotEntryAllIDList, entryIDList)
	taskFlowData := entity.ModifyTaskFlowParams{
		FlowID: taskFlowId, IntentName: taskFlowName,
		IntentDesc: desc, CategoryID: cateId,
		DialogJsonDraft: taskFlowJson, Uin: uin, SubUin: subUin,
		UpdateTime: time.Now(),
		RobotId:    robotId, IsEnable: flag,
		SlotIDs:  slotIDList,
		EntryIDs: entryIDList,
		VarIDs:   varParamsIDList,
	}

	// 保存并生效，需要校验
	if flag == uint32(KEP.SaveTaskFlowReq_ENABLE) {
		if appInfo != nil && appInfo.KnowledgeQa != nil && appInfo.KnowledgeQa.Workflow != nil &&
			len(appInfo.KnowledgeQa.Workflow.SafeWhiteUrls) > 0 {
			safeUrls = appInfo.KnowledgeQa.Workflow.SafeWhiteUrls
		}
		// 校验任务流JSON， tree
		if errMsg := CheckTaskFlowJSON(ctx, robotId, tree, taskFlowId, taskFlowName, safeUrls); errMsg != nil {
			log.ErrorContextf(ctx, "SaveTaskFlow err:%+v", errMsg.Err)
			return &KEP.SaveTaskFlowRsp{
				FlowId: taskFlowId, ErrorMsg: errMsg.Err.Error(),
				ErrorNodeId: errMsg.NodeID, ErrorNodeName: errMsg.NodeName,
			}, nil
		}
		// 校验应用下对应词条是否存在
		entriesMap, _ := db.GetEntriesMapByAppIdAndIds(ctx, robotId, entryIDList)
		for _, i := range entryIDList {
			_, ok := entriesMap[i]
			if !ok {
				return &KEP.SaveTaskFlowRsp{
					FlowId: taskFlowId, ErrorMsg: fmt.Sprintf("该应用下不存在词条:%s", i),
				}, nil
			}
		}

		// 校验应用下的对应变量是否存在
		varsMap, _ := db.GetVarsMapByAppIdAndIds(ctx, robotId, varParamsIDList)
		for _, i := range varParamsIDList {
			_, ok := varsMap[i]
			if !ok {
				return &KEP.SaveTaskFlowRsp{
					FlowId: taskFlowId, ErrorMsg: fmt.Sprintf("该应用下不存在变量:%s", i),
				}, nil
			}
		}
	}

	if err = db.SaveTaskFlow(ctx, taskFlowData); err != nil {
		log.ErrorContextf(ctx, "SaveTaskFlow,GetTaskFlowDetail err:%+v", err)
		return &KEP.SaveTaskFlowRsp{
			ErrorMsg: err.Error(), FlowId: taskFlowId,
		}, errors.OpDataFromDBError("保存工作流失败")
	}

	return &KEP.SaveTaskFlowRsp{
		ErrorMsg: "", FlowId: taskFlowData.FlowID,
	}, nil
}

// DeleteTaskFlow 删除TaskFlow
func DeleteTaskFlow(ctx context.Context,
	req *KEP.DeleteTaskFlowReq) (*KEP.DeleteTaskFlowRsp, error) {
	robotId := req.GetBotBizId()
	flowIds := set.RemoveDuplicatesAndEmpty(req.GetFlowIds())
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "DeleteTaskFlow,params:%s|%s|%+v", sid, robotId, flowIds)
	botId, err := util.CheckReqBotBizIDUint64(ctx, robotId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteTaskFlow,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}
	tfs, err := db.GetTaskFLowDetails(ctx, flowIds, robotId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteTaskFlow,GetTaskFLowDetails err:%s|%+v", sid, err)
		return nil, errors.ErrTaskFlowNotFound
	}

	if err := db.DeleteTaskFlow(ctx, robotId, maps.Keys(tfs)); err != nil {
		log.ErrorContextf(ctx, "DeleteTaskFlow,GetTaskFLowDetails err:%s|%+v", sid, err)
		return nil, errors.OpDataFromDBError("删除任务流程失败")
	}

	return &KEP.DeleteTaskFlowRsp{}, nil
}

// ListTaskFlowPreview 获取发布列表
func ListTaskFlowPreview(ctx context.Context, req *KEP.ListTaskFlowPreviewReq) (*KEP.ListTaskFlowPreviewRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "ListTaskFlowPreview:req", req)

	sid := util.RequestID(ctx)
	clues.AddTrackData(ctx, "sid", sid)

	// 获取任务发布内容
	if req.GetReleaseBizId() > 0 {
		req0 := &KEP.GetDataSyncTaskReq{
			BotBizId:      req.GetBotBizId(),
			TaskID:        req.GetReleaseBizId(),
			ReleaseStatus: req.ReleaseStatus,
		}
		if len(req0.ReleaseStatus) == 0 {
			req0.ReleaseStatus = []uint32{4}
		}
		resp0, err := synctask.GetDataSyncTask(ctx, req0)
		log.Infof("R|ListTaskFlowPreview|GetDataSyncTask|%s|%+v|%+v|%+v", sid, req0, resp0, err)
		if err != nil {
			log.ErrorContextf(ctx, "ListTaskFlowPreview|err:%+v", err)
			return nil, errors.OpDataFromDBError("获取发布任务失败")
		}
		resp := &KEP.ListTaskFlowPreviewRsp{Total: uint32(len(resp0.GetTask().GetSyncItems()))}
		for _, t := range resp0.GetTask().GetSyncItems() {
			e := &KEP.ListTaskFlowPreviewRsp_TaskFlow{
				FlowId:     t.GetID(),
				Name:       t.GetTitle(),
				UpdateTime: t.GetUpdateTime() / 1000,
				Action:     uint32(entity.EnumToAction[t.Action]),
				ActionDesc: entity.EnumActionDescription[t.Action],
				Message:    t.GetMessage(),
			}
			resp.List = append(resp.List, e)
		}
		return resp, nil
	}

	// 如果有正在发布的任务, 返回空列表
	corpID := util.CorpID(ctx)
	releaseTaskID, releaseTaskStatus, releaseError := admin.DescribeLatestReleaseStatus(ctx, req.GetBotBizId(), corpID)
	log.Infof("DescribeLatestReleaseStatus|%s|BOT:%v|CORP:%v|TASK:%v|%v|ERR:%v", sid, req.GetBotBizId(), corpID, releaseTaskID, releaseTaskStatus, releaseError)
	// 正在发布中
	if releaseError == nil && releaseTaskStatus == 1 {
		return &KEP.ListTaskFlowPreviewRsp{}, nil
	}

	robotID := fmt.Sprintf("%d", req.GetBotBizId())
	params := entity.ListTaskFLowParams{
		Query:         strings.TrimSpace(req.GetQuery()),
		Page:          req.GetPageNumber(),
		PageSize:      req.GetPageSize(),
		BotBizId:      robotID,
		FlowState:     []string{entity.FlowStateEnable, entity.FlowStateChangeUnPublish, entity.FlowStatePublishChange},
		ReleaseStatus: []string{entity.ReleaseStatusUnPublished, entity.ReleaseStatusFail},
	}
	for _, val := range req.GetActions() {
		params.Actions = append(params.Actions, entity.PublishActionDesc[fmt.Sprintf("%d", val)])
	}
	if req.GetStartTime() != 0 {
		params.StartTime = time.Unix(int64(req.GetStartTime()), 0)
	}
	if req.GetEndTime() != 0 {
		params.EndTime = time.Unix(int64(req.GetEndTime()), 0)
	}
	t0 := time.Now()
	log.Infof("I|ListTaskFlow|%s|%+v", sid, params)
	total := synctask.CountUnpublishedTaskFlowWithQuery(ctx, robotID, params)
	taskFlows, err := synctask.ListUnpublishedTaskFlowWithQuery(ctx, robotID, params)
	log.Infof("O|ListTaskFlow|%s|%d|len:%d|%+v|ERR:%v|%s", sid, total, len(taskFlows), taskFlows, err, time.Since(t0))
	if err != nil {
		log.Errorf("E|ListTaskFlow|%s|%s", sid, err.Error())
		return nil, errors.ErrTaskFlowLoadFailed
	}
	resp := &KEP.ListTaskFlowPreviewRsp{Total: uint32(total)}
	for _, t := range taskFlows {
		e := &KEP.ListTaskFlowPreviewRsp_TaskFlow{
			FlowId:     t.FlowID,
			Name:       t.IntentName,
			UpdateTime: uint64(t.UpdateTime.Unix()),
			Action:     uint32(entity.EnumToAction[t.Action]),
			ActionDesc: entity.EnumActionDescription[t.Action],
		}
		resp.List = append(resp.List, e)
	}

	return resp, nil
}

// ListTaskFlow 任务流程列表(含筛选)
func ListTaskFlow(ctx context.Context,
	req *KEP.ListTaskFlowReq) (*KEP.ListTaskFlowRsp, error) {

	sid := util.RequestID(ctx)
	query := strings.TrimSpace(req.GetQuery())
	cateId := req.GetCateBizId()
	page := req.GetPage()
	pageSize := req.GetPageSize()
	botBizId := req.GetBotBizId()
	flowType := req.GetFlowType()
	statuses := set.RemoveDuplicatesAndEmpty(req.GetStatus())
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "sid:%s|ListTaskFlow|permission|err:%+v", sid, err)
		return nil, err
	}

	var cateIds []string
	var taskFlows []*entity.TaskFlow

	childrenIds, err := getCateChildrenIDs(ctx, cateId, botBizId)
	if err != nil {
		log.WarnContextf(ctx, "ListTaskFlow,checkCateAndRetrieveID err:%s|%+v", sid, err)
		return nil, errors.ErrCateNotFound
	}
	cateIds = append(cateIds, cateId)
	cateIds = append(cateIds, childrenIds...)

	params := &entity.ListTaskFLowParams{
		Query:         query,
		CateIds:       set.RemoveDuplicatesAndEmpty(cateIds),
		Page:          page,
		PageSize:      pageSize,
		BotBizId:      botBizId,
		FlowType:      flowType,
		ReleaseStatus: statuses,
	}

	taskFlows, total, err := db.ListTaskFlow(ctx, params)
	if err != nil {
		log.WarnContextf(ctx, "ListTaskFlow,ListTaskFlow err:%s|%s", sid, err.Error())
		return nil, errors.ErrTaskFlowNotFound
	}

	list := make([]*KEP.ListTaskFlowRsp_TaskFlow, 0, len(taskFlows))
	for _, item := range taskFlows {
		list = append(list, &KEP.ListTaskFlowRsp_TaskFlow{
			FlowId:         item.FlowID,
			Name:           item.IntentName,
			Desc:           item.IntentDesc,
			FlowState:      item.FlowState,
			FlowStateDesc:  item.FlowStateDesc(),
			CateBizId:      item.CategoryID,
			Status:         item.FrontReleaseStatus(),
			StatusDesc:     item.FrontReleaseStatusDesc(),
			UpdateTime:     uint32(item.UpdateTime.Unix()),
			CreateTime:     uint32(item.CreateTime.Unix()),
			IsAllowEdit:    item.IsAllowEdit(),
			IsAllowRelease: item.IsAllowRelease(),
			IsAllowDelete:  item.IsAllowDelete(),
		})
	}

	return &KEP.ListTaskFlowRsp{
		List:       list,
		PageNumber: page,
		Total:      uint32(total),
	}, nil
}

// GetTaskFlowDetail 获取任务流程详情
func GetTaskFlowDetail(ctx context.Context, req *KEP.GetTaskFlowDetailReq) (*KEP.GetTaskFlowDetailRsp, error) {
	var err error
	flowId := req.GetFlowId()
	botBizId := req.GetBotBizId()
	sid := util.RequestID(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GetTaskFlowDetail,permission.CheckRobot:%s|%s", sid, err.Error())
		return nil, err
	}

	tFlowDetail, err := db.GetTaskFlowDetail(ctx, flowId, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetTaskFlowDetail, error:%s|%s", sid, err.Error())
		return nil, errors.OpDataFromDBError("获取任务流程详情失败")
	}
	if tFlowDetail == nil || tFlowDetail.FlowID == "" {
		log.ErrorContextf(ctx, "GetTaskFlowDetail:sid:%s|botId:%s|:fid:%s,not found",
			sid, botBizId, flowId)
		return nil, errors.ErrTaskFlowNotFound
	}
	return &KEP.GetTaskFlowDetailRsp{
		FlowId:        tFlowDetail.FlowID,
		Name:          tFlowDetail.IntentName,
		Desc:          tFlowDetail.IntentDesc,
		FlowState:     tFlowDetail.FlowState,
		FlowStateDesc: tFlowDetail.FlowStateDesc(),
		//FlowType:       tFlowDetail.FlowType,
		//FlowTypeDesc:   tFlowDetail.FlowTypeDesc(),
		CateBizId:      tFlowDetail.CategoryID,
		Status:         tFlowDetail.FrontReleaseStatus(),
		StatusDesc:     tFlowDetail.FrontReleaseStatusDesc(),
		UpdateTime:     uint32(tFlowDetail.UpdateTime.Unix()),
		CreateTime:     uint32(tFlowDetail.CreateTime.Unix()),
		DialogJson:     tFlowDetail.DialogJsonDraft,
		IsAllowEdit:    tFlowDetail.IsAllowEdit(),
		IsAllowDelete:  tFlowDetail.IsAllowDelete(),
		IsAllowRelease: tFlowDetail.IsAllowRelease(),
	}, nil
}

// getCateChildrenIDs 获取分类下面的子分类
func getCateChildrenIDs(ctx context.Context, cateId, botBizId string) ([]string, error) {
	cates, err := db.GetCategorys(ctx, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|getCateChildrenIDs:%s,%v", util.RequestID(ctx),
			botBizId, err.Error())
		return nil, errors.ErrCateNotFound
	}
	tree := category.BuildCateTree(cates)
	checkNode := tree.FindNode(category.StrToUint64(cateId))
	return checkNode.ChildrenIDs(), nil
}

// checkCateAndRetrieveID 检查分类是否存在
func checkCateAndRetrieveID(ctx context.Context, cateId, botBizId string) (string, error) {
	cate, err := db.GetCategoryByCateId(ctx, cateId, botBizId)
	log.InfoContextf(ctx, "checkCateAndRetrieveID:%s,%v", cateId, cate)
	if err != nil {
		log.ErrorContextf(ctx, "checkCateAndRetrieveID:%s,%v", cateId, cate)
		return "", errors.ErrSystem
	}
	if cate == nil {
		log.ErrorContextf(ctx, "checkCateAndRetrieveID:%s,%v", cateId, cate)
		return "", errors.ErrCateNotFound
	}
	return cate.CategoryID, nil
}

// checkTaskFlowName 校验任务流的名称
func checkTaskFlowName(ctx context.Context, flowId, botBizId, taskFlowName string) error {
	name := strings.TrimSpace(taskFlowName)
	nameMaxLen := config.GetMainConfig().VerifyTaskFlow.IntentNameLen
	log.InfoContextf(ctx, "checkTaskFlowName,nameMaxLen:%d", nameMaxLen)
	if utf8.RuneCountInString(name) > nameMaxLen {
		return errs.Newf(errors.ErrIntentNameTooLong, "任务流程名大于%d个字符，请重新填写", nameMaxLen)
	}
	nameMaxMin := config.GetMainConfig().VerifyTaskFlow.MinIntentNameLen
	if utf8.RuneCountInString(name) < nameMaxMin {
		return errs.Newf(errors.ErrIntentNameTooShort, "任务流程名小于%d个字符，请重新填写", nameMaxMin)
	}
	tf, err := db.GetTaskFLowDetailsByName(ctx, flowId, botBizId, taskFlowName)
	if err != nil {
		return errors.ErrTaskFlowNotFound
	}
	if tf != nil {
		return errors.ErrTaskFlowNameDuplicated
	}
	return nil
}

// CheckTaskFlowJSON 校验画布JSON
func CheckTaskFlowJSON(ctx context.Context, botBizId string, tree *KEP.TaskFlow,
	taskFlowId, taskFlowName string, safeUrls []string) *NodeVerifyErrMsg {
	sid := util.RequestID(ctx)

	log.InfoContextf(ctx, "校验对话树中的FlowId是否与前端传的flowId一致")
	if taskFlowId != tree.GetTaskFlowID() {
		log.ErrorContextf(ctx, "%s|画布flowId与前端传参的flowId不一致", sid)
		return verityErr("画布flowId与前端传参的flowId不一致")
	}

	if strings.TrimSpace(taskFlowName) != strings.TrimSpace(tree.GetTaskFlowName()) {
		log.ErrorContextf(ctx, "%s|任务流名称修改需同步修改画布JSON中的任务流名称", sid)
		return verityErr("任务流名称修改需同步修改画布JSON中的任务流名称")
	}

	// 1. 校验节点的总节点数
	log.InfoContextf(ctx, "校验节点总数开始,画布JSON：%s", json0.Marshal2StringNoErr(tree))
	nodeTotal := len(tree.GetNodes())
	log.InfoContextf(ctx, "校验节点总数：%v", nodeTotal)
	if nodeTotal == 0 || nodeTotal > config.GetMainConfig().VerifyTaskFlow.UiNodeTotal {
		return verityErr("画布节点数不符合范围要求！")
	}

	//3. 获取画布json树中重复节点的名称及id
	_, duplicateIDs := getNodeNamesAndDuplicateIDs(tree)
	log.InfoContextf(ctx, "画布json树中重复节点：%v", json0.Marshal2StringNoErr(duplicateIDs))
	if len(duplicateIDs) > 0 {
		log.ErrorContextf(ctx, "%s|画布json树中重复节点：%v", sid, json0.Marshal2StringNoErr(duplicateIDs))
		return verityErr("画布内节点名称有重复" + "，" + json0.Marshal2StringNoErr(duplicateIDs))
	}

	tCtx := &TreeContext{BotBizId: botBizId, Tree: tree, SafeUrls: safeUrls}
	tCtx.initContext(ctx)
	return checkTree(ctx, tCtx)
}

// CopyTaskFlow 复制任务流
func CopyTaskFlow(ctx context.Context, req *KEP.CopyTaskFlowReq) (*KEP.CopyTaskFlowRsp, error) {
	log.InfoContextf(ctx, "CopyTaskFlow, BotBizId:%s, FlowId:%s, FlowVersion:%s",
		req.GetBotBizId(), req.GetFlowId(), req.GetFlowVersion())
	var err error
	appBizId := req.GetBotBizId()
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow CheckRobot Failed, err:%v", err)
		return nil, err
	}

	// 分布式锁
	key := entity.GetCopyTaskFlowLockKey(req.GetFlowId())
	locker := lock.NewDefaultLocker(key, req.GetFlowId(), database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow locker.Lock Failed, err:%v", lockErr)
		return nil, lockErr
	}
	if !ok {
		lockErr = errors.ErrTaskFlowCopying
		log.WarnContextf(ctx, "CopyTaskFlow locker.Lock Failed, err:%v", lockErr)
		return nil, lockErr
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "CopyTaskFlow locker.UnLock Fialed, err:%v", unLockErr)
		}
	}()

	// 开启事物
	tx := dbUtil.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
	defer func() {
		// 事物提交或者回滚
		txErr := dbUtil.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()

	tx = tx.WithContext(ctx)
	// 查询当前复制任务流基础信息 区分版本并进行槽位恢复逻辑
	taskFlow, errMsg, err := db.TxGetTaskFlowDetailCopyWithVersionRecover(ctx, tx,
		appBizId, req.GetFlowId(), req.GetFlowVersion())
	if err != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow TxGetTaskFlowDetailWithVersionRecover Failed, err:%v", err)
		return nil, err
	}
	// 组装复制信息
	copyCount, newTaskFlowParams, err := genCopyNewTaskFlow(ctx, appBizId, taskFlow)
	if err != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow genCopyNewTaskFlow Failed, err:%v", err)
		return nil, err
	}
	// 创建任务流程
	log.InfoContextf(ctx, "CopyTaskFlow newTaskFlowParams:%v", newTaskFlowParams)
	err = db.TxCreateTaskFlow(ctx, tx, newTaskFlowParams)
	if err != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow db.TxCreateTaskFlow Failed, err:%v", err)
		return nil, err
	}
	// 更新复制次数
	err = db.TxUpdateTaskFlowCopyCount(ctx, tx, taskFlow, copyCount)
	if err != nil {
		log.ErrorContextf(ctx, "CopyTaskFlow db.TxUpdateTaskFlowCopyCount Failed, err:%v", err)
		return nil, err
	}

	return &KEP.CopyTaskFlowRsp{
		CopiedFlowId: newTaskFlowParams.FlowID,
		ErrorMsg:     errMsg,
	}, nil
}

// genCopyNewTaskFlow 生成复制的新任务流
func genCopyNewTaskFlow(ctx context.Context, robotId string, oldTaskFlow *entity.TaskFlow) (
	int, entity.CreateTaskFlowParams, error) {
	// 生成任务流名称
	getNewTaskFlowName := func(oldTaskFlowName string, copyCount int) string {
		// 注意中文字符的处理
		nameSuffix := "-副本" + strconv.Itoa(copyCount)
		remainLength := config.GetMainConfig().VerifyTaskFlow.IntentNameLen - utf8.RuneCountInString(nameSuffix)

		if remainLength < utf8.RuneCountInString(oldTaskFlowName) {
			return string([]rune(oldTaskFlowName)[0:remainLength]) + nameSuffix
		} else {
			return oldTaskFlowName + nameSuffix
		}
	}

	var taskFlowName string
	copyCount := oldTaskFlow.CopyCount + 1
	for i := 0; i < config.GetMainConfig().VerifyTaskFlow.TaskFlowLimit; i++ {
		taskFlowName = getNewTaskFlowName(oldTaskFlow.IntentName, copyCount)
		err := checkTaskFlowName(ctx, "", robotId, taskFlowName)
		if err == nil {
			break
		}
		if errs.Code(err) != errs.Code(errors.ErrTaskFlowNameDuplicated) {
			return 0, entity.CreateTaskFlowParams{}, err
		}
		if i == config.GetMainConfig().VerifyTaskFlow.TaskFlowLimit-1 {
			return 0, entity.CreateTaskFlowParams{}, errors.ErrTaskFlowNameDuplicated
		}
		copyCount += 1
	}

	// 生成任务流ID
	taskFlowID := idgenerator.NewUUID()

	// 画布ID和名称替换
	dialogJson := ""
	if len(oldTaskFlow.DialogJsonDraft) > 0 {
		dialogTree, err := protoutil.JsonToTaskFlow(oldTaskFlow.DialogJsonDraft)
		if err != nil {
			return 0, entity.CreateTaskFlowParams{}, err
		}
		dialogTree.TaskFlowID = taskFlowID
		dialogTree.TaskFlowName = taskFlowName

		dialogJson, err = protoutil.TaskFlowToJson(dialogTree)
		if err != nil {
			return 0, entity.CreateTaskFlowParams{}, err
		}
	}

	createParams := entity.CreateTaskFlowParams{
		FlowID:           taskFlowID,
		IntentID:         idgenerator.NewUUID(),
		CorpusID:         idgenerator.NewUUID(),
		IntentName:       taskFlowName,
		IntentDesc:       oldTaskFlow.IntentDesc,
		FlowState:        entity.FlowStateDraft,
		FlowType:         entity.FlowTypeICS,
		IntentType:       entity.IntentTypeFlow,
		IntentSource:     entity.IntentSourceIcs,
		Version:          config.GetMainConfig().VerifyTaskFlow.Version,
		CategoryID:       oldTaskFlow.CategoryID,
		DialogJsonDraft:  dialogJson,
		DialogJsonEnable: "",
		Uin:              oldTaskFlow.Uin,
		SubUin:           oldTaskFlow.SubUin,
		ReleaseStatus:    entity.ReleaseStatusUnPublished,
		Action:           entity.ActionInsert,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
		RobotId:          robotId,
	}
	return copyCount, createParams, nil
}
