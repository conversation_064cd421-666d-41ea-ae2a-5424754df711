package taskflow

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/slot"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/PuerkitoBio/goquery"
)

// TreeContext 树的上下文信息
type TreeContext struct {
	BotBizId              string
	Tree                  *KEP.TaskFlow
	APIResponseParamTypes map[string]string // 这棵树所有的API节点的出参ID对应的参数类型(int,string...)
	SlotIDs               map[string]string // 这棵树引用的所有"槽位ID";  Key: SlotID, Value: 所在的NodeID
	EntryIDs              map[string]string // entryId:slotId
	VarIDs                map[string]string // 变量集合 Key:VarID, Value: 所在节点的NodeID
	// 维护应用下的安全白名单
	SafeUrls []string
}

func (t *TreeContext) initContext(ctx context.Context) {
	t.APIResponseParamTypes = make(map[string]string)
	t.SlotIDs = make(map[string]string)
	t.EntryIDs = make(map[string]string)
	t.VarIDs = make(map[string]string)

	for _, node := range t.Tree.GetNodes() {
		switch node.GetNodeType() {
		case KEP.NodeType_START:
			continue
		case KEP.NodeType_ANSWER:
			t.findVarParamsFromAnswer(ctx, node)
		case KEP.NodeType_API:
			t.findSlotFromApiNode(node)
			t.findParamFromApiNode(node)
			t.findVarParamsFromApiNode(node)
		case KEP.NodeType_REQUEST:
			t.findSlotFromRequestNode(node)
			t.findVarParamsFromRequestNode(ctx, node)
		}

		t.findVarCustomAndSlotEntryFromNodeBranch(ctx, node)
	}
}

func (t *TreeContext) findVarParmsFromApiNodeHeader(node *KEP.TaskFlowNode, headers []*KEP.APINodeData_Header) {
	if len(headers) > 0 {
		for _, h := range headers {
			if h.GetSourceType() == KEP.APINodeData_Header_CUSTOM_VAR {
				varId := h.GetCustomVarValueData().GetValue()
				t.VarIDs[varId] = node.NodeID
			}
			t.findVarParmsFromApiNodeHeader(node, h.GetSubHeader())
		}
	}
}

func (t *TreeContext) findVarParmsFromApiNodeReq(node *KEP.TaskFlowNode, reqs []*KEP.APINodeData_RequestParam) {
	if len(reqs) > 0 {
		for _, req := range reqs {
			if req.GetSourceType() == KEP.APINodeData_RequestParam_CUSTOM_VAR {
				varId := req.GetCustomVarValueData().GetValue()
				t.VarIDs[varId] = node.NodeID
			}
			t.findVarParmsFromApiNodeReq(node, req.GetSubRequest())
		}
	}
}

// findVarParamsFromApiNode API 节点的入参找 自定义变量
func (t *TreeContext) findVarParamsFromApiNode(node *KEP.TaskFlowNode) {
	// header SourceType：CUSTOM_VAR， CustomVarValueData下面的值 参数
	for _, r := range node.GetApiNodeData().GetHeaders() {
		if r.GetParamType() == entity.ParamTypeString &&
			r.GetSourceType() == KEP.APINodeData_Header_CUSTOM_VAR {
			t.VarIDs[r.GetCustomVarValueData().GetValue()] = node.NodeID
		}
		if r.GetParamType() == entity.ParamTypeObject {
			t.findVarParmsFromApiNodeHeader(node, r.SubHeader)
		}
	}

	// body SourceType：CUSTOM_VAR
	for _, r := range node.GetApiNodeData().GetRequest() {
		if r.GetParamType() == entity.ParamTypeString &&
			r.GetSourceType() == KEP.APINodeData_RequestParam_CUSTOM_VAR {
			t.VarIDs[r.GetCustomVarValueData().GetValue()] = node.NodeID
		}
		if r.GetParamType() == entity.ParamTypeObject {
			t.findVarParmsFromApiNodeReq(node, r.GetSubRequest())
		}
	}

}

func (t *TreeContext) findVarParamsFromRichText(ctx context.Context, source string, node *KEP.TaskFlowNode) {
	sid := util.RequestID(ctx)
	if len(source) == 0 {
		return
	}

	//log.InfoContextf(ctx, "sid:%s|findVarParamsFromRichText|source:%s|", sid, source)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|findVarParamsFromRichText|NewDocumentFromReader"+
			" failed,err:%v", sid, err)
		return
	}
	doc.Find("span").Each(func(i int, s *goquery.Selection) {
		dataValue, _ := s.Attr("data-value")
		dataType, _ := s.Attr("data-type")
		log.InfoContextf(ctx, "findVarParamsFromRichText|dataValue=%s｜dataType=%s", dataValue, dataType)
		if dataType == KEP.ConditionInfo_BranchCondition_CUSTOM_VAR.String() {
			t.VarIDs[dataValue] = node.NodeID
		}
	})
}

// 找信息澄清节点下的自定义参数
func (t *TreeContext) findVarParamsFromRequestNode(ctx context.Context, node *KEP.TaskFlowNode) {
	reqNodeData := node.GetRequestNodeData()
	if reqNodeData != nil && reqNodeData.GetRequest() != nil {
		if (reqNodeData.GetRequest().GetAskType() == KEP.RequestNodeData_RequestInfo_INPUT ||
			reqNodeData.GetRequest().GetRequestType() == KEP.RequestNodeData_RequestInfo_PURPOSE) &&
			reqNodeData.GetRequest().GetCustomAsk() != "" {
			t.findVarParamsFromRichText(ctx, reqNodeData.GetRequest().GetCustomAsk(), node)
		}
	}
}

// 找信息澄清节点下的自定义参数
func (t *TreeContext) findVarParamsFromAnswer(ctx context.Context, node *KEP.TaskFlowNode) {
	answerData := node.GetAnswerNodeData()
	if answerData.GetAnswerType() == KEP.AnswerNodeData_LLM &&
		answerData != nil && answerData.GetLLMAnswerData() != nil {
		if answerData.GetLLMAnswerData() != nil {
			t.findVarParamsFromRichText(ctx, answerData.GetLLMAnswerData().GetPrompt(), node)
		}
	}
	if answerData.GetAnswerType() == KEP.AnswerNodeData_INPUT &&
		answerData.GetInputAnswerData() != nil {
		if answerData.GetInputAnswerData() != nil {
			t.findVarParamsFromRichText(ctx, answerData.GetInputAnswerData().GetPreview(), node)
		}
	}
}

func (t *TreeContext) findVarCustomAndSlotEntryFromBranchCondition(ctx context.Context,
	node *KEP.TaskFlowNode, condInfo *KEP.ConditionInfo) {
	bc := condInfo.GetCondition()

	// 获取词条
	t.findEntryFromBranchValueAddition(ctx, condInfo.GetCondition())
	t.findEntryFromBranchComparisonSlotInfo(ctx, condInfo.GetCondition())

	if bc.GetSourceType() == KEP.ConditionInfo_BranchCondition_CUSTOM_VAR {
		// 变量ID
		varId := bc.GetCustomVarValueData().GetValue()
		t.VarIDs[varId] = node.NodeID
	}

	// 接口出参，引用实体 找对应实体
	if bc.GetSourceType() == KEP.ConditionInfo_BranchCondition_API_RESP {
		if bc.GetComparisonValueSourceType() == entity.ValueSourceEntity ||
			bc.GetComparisonValueSourceType() == entity.ValueSourceEntry {
			// 实体
			slotId := bc.GetComparisonSlotInfo().GetSlotID()
			t.SlotIDs[slotId] = node.NodeID
		}
	}
	if len(condInfo.GetConditionInfo()) > 0 {
		for _, itemCond := range condInfo.GetConditionInfo() {
			t.findVarCustomAndSlotEntryFromBranchCondition(ctx, node, itemCond)
		}
	}
}

// findVarCustomAndSlotFromNodeBranch 查找条件节点中的变量
func (t *TreeContext) findVarCustomAndSlotEntryFromNodeBranch(ctx context.Context, node *KEP.TaskFlowNode) {
	for _, b := range node.Branches {
		t.findVarCustomAndSlotEntryFromBranchCondition(ctx, node, b.GetConditionInfo())
	}

}

// API 节点的入参中使用的"槽位"
func (t *TreeContext) findSlotFromApiNode(node *KEP.TaskFlowNode) {
	for _, r := range node.GetApiNodeData().GetRequest() {
		if r.GetSourceType() == KEP.APINodeData_RequestParam_SLOT {
			t.SlotIDs[r.GetSlotValueData().SlotID] = node.NodeID
		}
	}
}

// findSubParamFromRspParam
func (t *TreeContext) findSubParamFromRspParam(rspParam *KEP.APINodeData_ResponseParam) {
	// 获取子参数的参数信息
	if rspParam.GetSubParams() != nil {
		for _, subParam := range rspParam.GetSubParams() {
			t.APIResponseParamTypes[subParam.GetParamID()] = subParam.GetParamType()
			t.findSubParamFromRspParam(subParam)
		}
	}
}

// API 节点的出参信息
func (t *TreeContext) findParamFromApiNode(node *KEP.TaskFlowNode) {
	for _, r := range node.GetApiNodeData().GetResponse() {
		t.APIResponseParamTypes[r.GetParamID()] = r.GetParamType()
		t.findSubParamFromRspParam(r)
	}
}

// 询问节点中使用的"槽位"
func (t *TreeContext) findSlotFromRequestNode(node *KEP.TaskFlowNode) {
	if node.GetRequestNodeData().GetRequest().GetRequestType() == KEP.RequestNodeData_RequestInfo_SLOT {
		t.SlotIDs[node.GetRequestNodeData().GetRequest().GetRequestValue()] = node.NodeID
	}
}

func (t *TreeContext) findEntryFromBranchComparisonSlotInfo(ctx context.Context,
	bc *KEP.ConditionInfo_BranchCondition) {
	if bc.GetSourceType() == KEP.ConditionInfo_BranchCondition_API_RESP &&
		bc.GetComparisonValueSourceType() == entity.ValueSourceEntity ||
		bc.GetComparisonValueSourceType() == entity.ValueSourceEntry {
		// 实体ID
		slotId := bc.GetComparisonSlotInfo().GetSlotID()
		// 通过slotId找到entityId
		slotEntity, _ := db.GetSlotEntityRelationBySlotID(ctx, slotId)
		if bc.GetComparisonSlotInfo().GetAllEntry() { //全部词条
			entryIds, _ := slot.GetEntryIdsByEntityId(ctx, slotEntity.EntityID)
			if len(entryIds) > 0 {
				for _, v := range entryIds {
					t.EntryIDs[v] = slotId
				}
			}
		} else {
			for _, v := range bc.GetComparisonSlotInfo().GetEntryIDs() {
				t.EntryIDs[v] = slotId
			}
		}
	}
}

// findEntryFromBranchValueAddition 查找条件节点中的词条
func (t *TreeContext) findEntryFromBranchValueAddition(ctx context.Context, bc *KEP.ConditionInfo_BranchCondition) {
	// 词条
	if bc.GetInInputValueAddition().GetValueSource() == entity.ValueSourceEntry ||
		(bc.GetComparisonValueSourceType() == entity.ValueSourceEntity ||
			bc.GetComparisonValueSourceType() == entity.ValueSourceEntry) {
		// 实体ID
		slotId := bc.GetSlotValueData().GetSlotID()
		// 通过slotId找到entityId
		slotEntity, _ := db.GetSlotEntityRelationBySlotID(ctx, slotId)
		if bc.GetInInputValueAddition().GetAllEntry() { //全部词条
			entryIds, _ := slot.GetEntryIdsByEntityId(ctx, slotEntity.EntityID)
			if len(entryIds) > 0 {
				for _, v := range entryIds {
					t.EntryIDs[v] = slotId
				}
			}
		} else {
			for _, v := range bc.GetInInputValueAddition().GetEntryIDs() {
				t.EntryIDs[v] = slotId
			}
		}
	}
}
