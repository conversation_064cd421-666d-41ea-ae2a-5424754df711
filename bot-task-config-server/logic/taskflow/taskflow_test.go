package taskflow

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/prashantv/gostub"
)

// 配置到run config的 go tool arguments里
// -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore"
func TestTreeToJson(t *testing.T) {
	tree := buildKEP()
	jsonStr, err := protoutil.TaskFlowToJson(tree)
	if err != nil {
		log.Fatalf("Failed to marshal <PERSON><PERSON><PERSON>: %v", err)
	}
	fmt.Println(jsonStr)

	//jsonStr := `{"TaskFlowID":"123","xxhh":"123","TaskFlowName":"国际邮寄","Nodes":[{"NodeID":"start","NodeName":"start-node-name","NodeType":"START","StartNodeData":{},"Branches":[{"BranchID":"branch-id-1","BranchType":"DIRECT","NextNodeID":"77220626-5adc-786a-e3f6-a991545728d3","PrevNodeID":"start"}]},{"NodeID":"77220626-5adc-786a-e3f6-a991545728d3 // 节点ID","NodeName":"查支持的国家 // 节点名称 ","NodeType":"API","ApiNodeData":{"API":{"URL":"https://www.5idhl.com/api/wiplus/base/onlineChat/getCountryGoodsList ","Method":"POST"},"Request":[{"ParamID":"api-1-input-1 // 入参的ID","ParamName":"car_model // 交互稿-API节点-参数名称","ParamType":"string","SourceType":"SLOT","SlotValueData":{"SlotID":"slot-id-1","AskType":"LLM"},"IsRequired":true},{"ParamID":"api-1-input-2","ParamName":"car_color // 对应到交互稿示例'收集实体' - 车辆颜色","ParamType":"string","SourceType":"SLOT","SlotValueData":{"SlotID":"slot-id-2  // 这个是通过单独的 slot（交互稿上是实体）相关的接口拿到的","AskType":"INPUT","CustomAsk":"请问您感兴趣的车身颜色是？我们目前提供红、蓝、黑、灰四种颜色。"},"IsRequired":true},{"ParamID":"api-1-input-3","ParamName":"resp-output-param-name-1 // 接口出参的 示例数据","ParamType":"string","SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"IsRequired":true},{"ParamID":"api-1-input-4","ParamName":"gudingzhi // 固定值的 示例数据","ParamType":"float","SourceType":"FIXED","FixedValueData":{"Value":"// 这里是固定值示例"},"IsRequired":true},{"ParamID":"api-1-input-5","ParamName":"system_user_id // 系统参数的 示例数据","ParamType":"string","SourceType":"SYSTEM","SystemValueData":{"Value":"USER_ID的值"},"IsRequired":true}],"LLMAskPreview":["请告诉我您感兴趣的车辆型号，方面为您报价"],"Response":[{"ParamID":"output-id-1","ParamName":"aaa","ParamType":"float","ParamTitle":"显示用的说明","JSONPath":"$.store.book[0].title"}]},"Branches":[{"BranchID":"branch-id-2 ","BranchType":"DIRECT","ConditionInfo":{"ConditionsLogic":"AND","ConditionInfo":[{"Condition":{"SourceType":"SLOT","SlotValueData":{"SlotID":"slot-1"},"Comparison":"NE","InputValues":["aabb"]}},{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"Comparison":"LT","InputValues":["10"]}},{"ConditionInfo":[{"ConditionsLogic":"OR","ConditionInfo":[{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-2"},"Comparison":"IS_SET"}},{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"Comparison":"CONTAINS","InputValues":["10"]}}]}]}]},"NextNodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f","PrevNodeID":"77220626-5adc-786a-e3f6-a991545728d3"}]},{"NodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f","NodeName":"物品选择","NodeType":"REQUEST","RequestNodeData":{"Request":{"ID":"req-id-1","RequestType":"SLOT","RequestValue":"slot_id","AskType":"LLM","LLMAskPreview":["大模型生成的问题"],"IsRequired":true},"EnableCard":true,"CardFrom":"INPUT","InputCard":{"InputCardContent":["用户输入的选项卡1","用户输入的选项卡2"]}},"Branches":[{"BranchID":"branch-id-4","BranchType":"DIRECT","NextNodeID":"f8549ed6-8638-b002-e427-0c5226e8eb45","PrevNodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f"}]},{"NodeID":"api-Request-node-2","NodeName":"带API选项卡的询问节点","NodeType":"REQUEST","RequestNodeData":{"Request":{"ID":"req-id-2","RequestType":"SLOT","RequestValue":"slot_id","AskType":"INPUT","CustomAsk":"用户输入的问题","IsRequired":true},"EnableCard":true,"CardFrom":"API","ApiCardRef":{"ParamID":"// 别的API节点的出参的ID"}}},{"NodeID":"f8549ed6-8638-b002-e427-0c5226e8eb45","NodeName":"查国家+物品能否邮寄 // 下面省略了","NodeType":"API","Branches":[{"BranchID":"branch-id-3","BranchType":"DIRECT","NextNodeID":"1122","PrevNodeID":"3344"}]},{"NodeID":"answer-node-id-1 // 前端生成","NodeName":"答案节点-大模型生成","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"LLM","LLMAnswerData":{"Preview":["大模型生成的答案1","大模型生成的答案2"]}}},{"NodeID":"answer-node-id-2 // 前端生成","NodeName":"答案节点-用户输入","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"INPUT","InputAnswerData":{"Preview":"// 跟以前的 AnswerCustom 的内容一致"}}},{"NodeID":"answer-node-id-3 // 前端生成","NodeName":"答案节点-引用知识文档","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"DOC","DocAnswerData":{"Preview":"// 引用知识文档也有\"回复预览\"","RefInfo":[{"DocType":"pdf","DocName":"基础设施网络安全.pdf","DocUrl":"https://cos.com/abc.pdf","Score":0.96}]}}}]}`
	//jsonStr := `{"TaskFlowID":"123","TaskFlowName":"国际邮寄","Nodes":[{"NodeID":"start","NodeName":"start-node-name","NodeType":"START","StartNodeData":{},"Branches":[{"BranchID":"branch-id-1","BranchType":"DIRECT","NextNodeID":"77220626-5adc-786a-e3f6-a991545728d3","PrevNodeID":"start"}]},{"NodeID":"77220626-5adc-786a-e3f6-a991545728d3 // 节点ID","NodeName":"查支持的国家 // 节点名称 ","NodeType":"API","ApiNodeData":{"API":{"URL":"https://www.5idhl.com/api/wiplus/base/onlineChat/getCountryGoodsList ","Method":"POST"},"Request":[{"ParamID":"api-1-input-1 // 入参的ID","ParamName":"car_model // 交互稿-API节点-参数名称","ParamType":"string","SourceType":"SLOT","SlotValueData":{"SlotID":"slot-id-1","AskType":"LLM"},"IsRequired":true},{"ParamID":"api-1-input-2","ParamName":"car_color // 对应到交互稿示例'收集实体' - 车辆颜色","ParamType":"string","SourceType":"SLOT","SlotValueData":{"SlotID":"slot-id-2  // 这个是通过单独的 slot（交互稿上是实体）相关的接口拿到的","AskType":"INPUT","CustomAsk":"请问您感兴趣的车身颜色是？我们目前提供红、蓝、黑、灰四种颜色。"},"IsRequired":true},{"ParamID":"api-1-input-3","ParamName":"resp-output-param-name-1 // 接口出参的 示例数据","ParamType":"string","SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"IsRequired":true},{"ParamID":"api-1-input-4","ParamName":"gudingzhi // 固定值的 示例数据","ParamType":"float","SourceType":"FIXED","FixedValueData":{"Value":"// 这里是固定值示例"},"IsRequired":true},{"ParamID":"api-1-input-5","ParamName":"system_user_id // 系统参数的 示例数据","ParamType":"string","SourceType":"SYSTEM","SystemValueData":{"Value":"USER_ID"},"IsRequired":true}],"LLMAskPreview":["请告诉我您感兴趣的车辆型号，方面为您报价"],"Response":[{"ParamID":"output-id-1","ParamName":"aaa","ParamType":"float","ParamTitle":"显示用的说明","JSONPath":"$.store.book[0].title"}]},"Branches":[{"BranchID":"branch-id-2 ","BranchType":"DIRECT","ConditionInfo":{"ConditionsLogic":"AND","ConditionInfo":[{"Condition":{"SourceType":"SLOT","SlotValueData":{"SlotID":"slot-1"},"Comparison":"NE","InputValues":["aabb"]}},{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"Comparison":"LT","InputValues":["10"]}},{"ConditionsLogic":"OR","ConditionInfo":[{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-2"},"Comparison":"IS_SET"}},{"Condition":{"SourceType":"API_RESP","APIRespValueData":{"ParamID":"output-1"},"Comparison":"CONTAINS","InputValues":["10"]}}]}]},"NextNodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f","PrevNodeID":"77220626-5adc-786a-e3f6-a991545728d3"}]},{"NodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f","NodeName":"物品选择","NodeType":"REQUEST","RequestNodeData":{"Request":{"ID":"req-id-1","RequestType":"SLOT","RequestValue":"slot_id","AskType":"LLM","LLMAskPreview":["大模型生成的问题"],"IsRequired":true},"EnableCard":true,"CardFrom":"INPUT","InputCard":{"InputCardContent":["用户输入的选项卡1","用户输入的选项卡2"]}},"Branches":[{"BranchID":"branch-id-4","BranchType":"DIRECT","NextNodeID":"f8549ed6-8638-b002-e427-0c5226e8eb45","PrevNodeID":"30d15c01-4a83-2cf4-7faa-85fceae8382f"}]},{"NodeID":"api-Request-node-2","NodeName":"带API选项卡的询问节点","NodeType":"REQUEST","RequestNodeData":{"Request":{"ID":"req-id-2","RequestType":"SLOT","RequestValue":"slot_id","AskType":"INPUT","CustomAsk":"用户输入的问题","IsRequired":true},"EnableCard":true,"CardFrom":"API","ApiCardRef":{"ParamID":"// 别的API节点的出参的ID"}}},{"NodeID":"f8549ed6-8638-b002-e427-0c5226e8eb45","NodeName":"查国家+物品能否邮寄 // 下面省略了","NodeType":"API","Branches":[{"BranchID":"branch-id-3","BranchType":"DIRECT","NextNodeID":"1122","PrevNodeID":"3344"}]},{"NodeID":"answer-node-id-1","NodeName":"答案节点-大模型生成","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"LLM","LLMAnswerData":{"Preview":["大模型生成的答案1","大模型生成的答案2"]},"DocAnswerData":{"Preview":"// 引用知识文档也有\"回复预览\"","RefInfo":[{"DocType":"pdf","DocName":"基础设施网络安全.pdf","DocUrl":"https://cos.com/abc.pdf","Score":0.96}]}}},{"NodeID":"answer-node-id-2 // 前端生成","NodeName":"答案节点-用户输入","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"INPUT","InputAnswerData":{"Preview":"// 跟以前的 AnswerCustom 的内容一致"}}},{"NodeID":"answer-node-id-3 // 前端生成","NodeName":"答案节点-引用知识文档","NodeType":"ANSWER","AnswerNodeData":{"AnswerType":"DOC","DocAnswerData":{"Preview":"// 引用知识文档也有\"回复预览\"","RefInfo":[{"DocType":"pdf","DocName":"基础设施网络安全.pdf","DocUrl":"https://cos.com/abc.pdf","Score":0.96}]}}}]}`

	fmt.Println("--------------- Unmarshal ---------------")
	tree2, err := protoutil.JsonToTaskFlow(jsonStr)
	if err != nil {
		log.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	for a, b := range tree2.GetSnapshot().GetSlotMap() {
		fmt.Printf("Snapshot-SlotMap: k:%s, v: %s\n", a, b)
	}

	// 打印反序列化后的结果
	fmt.Printf("TaskFlowID: %s\n", tree2.TaskFlowID)
	for i, node := range tree2.Nodes {
		fmt.Printf("[%d] NodeID: %s - %s:  [%T]", i, node.NodeID, node.NodeName, node.GetNodeData())

		// 方式一：
		//switch x := node.GetNodeData().(type) {
		//case *KEP.TaskFlowNode_StartNodeData:
		//	fmt.Printf("START")
		//case *KEP.TaskFlowNode_ApiNodeData:
		//	fmt.Printf("API节点 Request.len=%d", len(x.ApiNodeData.Request))
		//case *KEP.TaskFlowNode_RequestNodeData:
		//	fmt.Printf("询问节点 input.len=%+v", x.RequestNodeData.Request)
		//case *KEP.TaskFlowNode_AnswerNodeData:
		//	fmt.Printf("\t答案节点-大模型生成结果 %s", x.AnswerNodeData.GetLLMAnswerData())
		//}

		// 方式二：
		switch node.NodeType {
		case KEP.NodeType_START:
			node.GetStartNodeData()
			fmt.Printf("START")
		case KEP.NodeType_API:
			api := node.GetApiNodeData()
			if api != nil {
				fmt.Printf("API节点 input.len=%d", len(api.Request))
			}
		case KEP.NodeType_REQUEST:
			req := node.GetRequestNodeData()
			fmt.Printf("询问节点 input.len=%+v", req.Request)
		case KEP.NodeType_ANSWER:
			answer := node.GetAnswerNodeData()
			switch answer.AnswerType {
			case KEP.AnswerNodeData_LLM:
				fmt.Printf("\t答案节点-大模型生成结果 %s", answer.GetLLMAnswerData())
			case KEP.AnswerNodeData_INPUT:
				fmt.Printf("\t答案节点-用户输入的结果 %s", answer.GetInputAnswerData())
			case KEP.AnswerNodeData_DOC:
				ss := answer.GetDocAnswerData()
				fmt.Printf("\t答案节点-引用知识文档 %+v", ss)
				fmt.Printf("\t答案节点-引用知识文档 %+v", ss.Preview)

			}
		}
		fmt.Println()
	}

	fmt.Printf("TaskFlowName: %s\n", tree2.TaskFlowName)
}

func buildKEP() *KEP.TaskFlow {
	nodes := make([]*KEP.TaskFlowNode, 0)

	// 开始节点
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "start",
		NodeName: "start-node-name",
		NodeType: KEP.NodeType_START,
		NodeData: &KEP.TaskFlowNode_StartNodeData{
			StartNodeData: &KEP.StartNodeData{},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "branch-id-1",
				BranchType: KEP.Branch_DIRECT,
				NextNodeID: "77220626-5adc-786a-e3f6-a991545728d3",
				PrevNodeID: "start",
			},
		},
	})

	// API节点-1
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "77220626-5adc-786a-e3f6-a991545728d3 // 节点ID",
		NodeName: "查支持的国家 // 节点名称 ",
		NodeType: KEP.NodeType_API,
		NodeData: &KEP.TaskFlowNode_ApiNodeData{
			ApiNodeData: &KEP.APINodeData{
				API: &KEP.APINodeData_APIInfo{
					URL:    "https://www.5idhl.com/api/wiplus/base/onlineChat/getCountryGoodsList ",
					Method: "POST",
				},
				Request: []*KEP.APINodeData_RequestParam{
					{
						ParamID:    "api-1-input-1 // 入参的ID",
						ParamName:  "car_model // 交互稿-API节点-参数名称",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SLOT,
						SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
							SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
								SlotID:    "slot-id-1",
								AskType:   KEP.APINodeData_RequestParam_SlotValue_LLM,
								CustomAsk: "",
							},
						},
						IsRequired: true,
					},
					{
						ParamID:    "api-1-input-2",
						ParamName:  "car_color // 对应到交互稿示例'收集实体' - 车辆颜色",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SLOT,
						SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
							SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
								SlotID:    "slot-id-2  // 这个是通过单独的 slot（交互稿上是实体）相关的接口拿到的",
								AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
								CustomAsk: "请问您感兴趣的车身颜色是？我们目前提供红、蓝、黑、灰四种颜色。",
							},
						},
						IsRequired: true,
					},
					{
						ParamID:    "api-1-input-3",
						ParamName:  "resp-output-param-name-1 // 接口出参的 示例数据",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_API_RESP,
						SourceValue: &KEP.APINodeData_RequestParam_APIRespValueData{
							APIRespValueData: &KEP.APINodeData_RequestParam_APIRespValue{
								ParamID: "output-1",
							},
						},
						IsRequired: true,
					},
					{
						ParamID:    "api-1-input-4",
						ParamName:  "gudingzhi // 固定值的 示例数据",
						ParamType:  ParamTypeFloat,
						SourceType: KEP.APINodeData_RequestParam_FIXED,
						SourceValue: &KEP.APINodeData_RequestParam_FixedValueData{
							FixedValueData: &KEP.APINodeData_RequestParam_FixedValue{
								Value: "// 这里是固定值示例",
							},
						},
						IsRequired: true,
					},
					{
						ParamID:    "api-1-input-5",
						ParamName:  "system_user_id // 系统参数的 示例数据",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SYSTEM,
						SourceValue: &KEP.APINodeData_RequestParam_SystemValueData{
							SystemValueData: &KEP.APINodeData_RequestParam_SystemValue{
								Value: "USER_ID",
							},
						},
						IsRequired: true,
					},
				},
				LLMAskPreview: []string{
					"请告诉我您感兴趣的车辆型号，方面为您报价",
				},
				Response: []*KEP.APINodeData_ResponseParam{
					{
						ParamID:    "output-id-1",
						ParamName:  "aaa",
						ParamType:  ParamTypeFloat,
						ParamTitle: "显示用的说明",
						JSONPath:   "$.store.book[0].title",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "branch-id-2 ",
				BranchType: KEP.Branch_DIRECT,
				ConditionInfo: &KEP.ConditionInfo{
					ConditionsLogic: KEP.ConditionInfo_AND,
					ConditionInfo: []*KEP.ConditionInfo{
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
								SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
									SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
										SlotID: "slot-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_NE,
								InputValues: []string{"aabb"},
							},
						},
						{
							Condition: &KEP.ConditionInfo_BranchCondition{
								SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
								SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
									APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
										ParamID: "output-1",
									},
								},
								Comparison:  KEP.ConditionInfo_BranchCondition_LT,
								InputValues: []string{"10"},
							},
						},
						{
							ConditionsLogic: KEP.ConditionInfo_OR,
							ConditionInfo: []*KEP.ConditionInfo{
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-2",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
										InputValues: nil,
									},
								},
								{
									Condition: &KEP.ConditionInfo_BranchCondition{
										SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
										SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
											APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
												ParamID: "output-1",
											},
										},
										Comparison:  KEP.ConditionInfo_BranchCondition_CONTAINS,
										InputValues: []string{"10"},
									},
								},
							},
						},
					},
				},
				NextNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
				PrevNodeID: "77220626-5adc-786a-e3f6-a991545728d3",
			},
		},
	})

	// 询问节点 - 用户选项卡
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "30d15c01-4a83-2cf4-7faa-85fceae8382f",
		NodeName: "物品选择",
		NodeType: KEP.NodeType_REQUEST,
		NodeData: &KEP.TaskFlowNode_RequestNodeData{
			RequestNodeData: &KEP.RequestNodeData{
				Request: &KEP.RequestNodeData_RequestInfo{
					ID:           "req-id-1",
					RequestType:  KEP.RequestNodeData_RequestInfo_SLOT,
					RequestValue: "slot_id",
					AskType:      KEP.RequestNodeData_RequestInfo_LLM,
					CustomAsk:    "",
					LLMAskPreview: []string{
						"大模型生成的问题",
					},
					IsRequired: true,
				},
				EnableCard: true,
				CardFrom:   KEP.RequestNodeData_INPUT,
				InputCard: &KEP.RequestNodeData_InputCardContent{
					InputCardContent: []string{
						"用户输入的选项卡1",
						"用户输入的选项卡2",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "branch-id-4",
				BranchType: KEP.Branch_DIRECT,
				NextNodeID: "f8549ed6-8638-b002-e427-0c5226e8eb45",
				PrevNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
			},
		},
	})

	// 询问节点 - 意向判断 - 1
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "sssbbbbb",
		NodeName: "意向判断实例",
		NodeType: KEP.NodeType_REQUEST,
		NodeData: &KEP.TaskFlowNode_RequestNodeData{
			RequestNodeData: &KEP.RequestNodeData{
				Request: &KEP.RequestNodeData_RequestInfo{
					ID:          "req-id-purpos-1",
					RequestType: KEP.RequestNodeData_RequestInfo_PURPOSE,
					CustomAsk:   "意向判断的问题",
					IsRequired:  true,
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "branch-id-4",
				BranchType: KEP.Branch_DIRECT,
				NextNodeID: "f8549ed6-8638-b002-e427-0c5226e8eb45",
				PrevNodeID: "30d15c01-4a83-2cf4-7faa-85fceae8382f",
			},
		},
	})

	// 询问节点 - API选项卡
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "api-Request-node-2",
		NodeName: "带API选项卡的询问节点",
		NodeType: KEP.NodeType_REQUEST,
		NodeData: &KEP.TaskFlowNode_RequestNodeData{
			RequestNodeData: &KEP.RequestNodeData{
				Request: &KEP.RequestNodeData_RequestInfo{
					ID:           "req-id-2",
					RequestType:  KEP.RequestNodeData_RequestInfo_SLOT,
					RequestValue: "slot_id",
					AskType:      KEP.RequestNodeData_RequestInfo_INPUT,
					CustomAsk:    "用户输入的问题",
					IsRequired:   true,
				},
				EnableCard: true,
				CardFrom:   KEP.RequestNodeData_API,
				ApiCardRef: &KEP.RequestNodeData_ApiRespCardRefInfo{
					ParamID: "// 别的API节点的出参的ID",
				},
			},
		},
	})

	// API节点-2
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "f8549ed6-8638-b002-e427-0c5226e8eb45",
		NodeName: "查国家+物品能否邮寄",
		NodeType: KEP.NodeType_API,
		Branches: []*KEP.Branch{
			{
				BranchID:   "branch-id-3",
				BranchType: KEP.Branch_DIRECT,
				NextNodeID: "1122",
				PrevNodeID: "3344",
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "answer-node-id-1",
		NodeName: "答案节点-大模型生成",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_LLM,
				LLMAnswerData: &KEP.AnswerNodeData_LLMAnswer{
					Preview: []string{
						"大模型生成的答案1",
						"大模型生成的答案2",
					},
				},
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "answer-node-id-2",
		NodeName: "答案节点-用户输入",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_INPUT,
				InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
					Preview: "// 跟以前的 AnswerCustom 的内容一致",
				},
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "answer-node-id-3",
		NodeName: "答案节点-引用知识文档",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_DOC,
				DocAnswerData: &KEP.AnswerNodeData_DocAnswer{
					Preview: "// 引用知识文档也有\"回复预览\"",
					RefInfo: []*KEP.AnswerNodeData_DocAnswer_DocRefInfo{
						{
							DocId:   "125692",
							DocType: 2,
							DocName: "基础设施网络安全.pdf",
							//Content: "Score // 代表的是这批文档的相关性   Content是预览的文档内容",
						},
					},
				},
			},
		},
	})

	edges := make([]*KEP.Edge, 0)
	tf := &KEP.TaskFlow{
		TaskFlowID:   "123",
		TaskFlowName: "国际邮寄",
		Nodes:        nodes,
		Edges:        edges,
		Snapshot: &KEP.Snapshot{
			SlotMap: map[string]string{
				"id-1": "品牌",
				"id-2": "城市",
			},
		},
	}
	return tf
}

func TestCheckTaskFlowJSON(t *testing.T) {
	config.GetMainConfigForUnitTest().VerifyTaskFlow = config.VerifyTaskFlow{
		TaskFlowLimit:             100,
		Version:                   "",
		MinIntentNameLen:          100,
		IntentNameLen:             100,
		IntentDescLen:             100,
		UiNodeTotal:               100,
		UiNodeNameMax:             100,
		RequiredInfoNameMax:       100,
		RequiredProbeCustomMax:    100,
		RequiredProbePreviewMax:   100,
		AnswerNodeCustomMax:       100,
		OptionContentsTextMax:     100,
		APIPathTextMax:            100,
		APINodeRequiredInfoMax:    100,
		APIRspParamsMax:           100,
		OptionContentsMax:         100,
		NodeBranchCountMax:        100,
		ConditionCountMax:         100,
		ConditionContainsValueMax: 100,
		APINodeValidJSONFieldRex:  "^[a-zA-Z0-9_]+$",
		BranchConditionMaxDepth:   100,
		APINodeRequiredInfoMin:    0,
		UiNodeNameMin:             0,
		ConditionContainsValueMin: 0,
		APIRspParamsMin:           0,
		ConditionCountMin:         0,
		NodeBranchCountMin:        0,
		OptionContentsMin:         0,
		APINodeHeadersMaxCount:    10,
	}

	stubs1 := gostub.Stub(&getSlotsWithIDs,
		func(ctx context.Context, robotID string, slotIDs []string) ([]*entity.Slot, error) {
			var slotInfo []*entity.Slot
			slotInfo = append(slotInfo, &entity.Slot{SlotID: fromSlotID})
			slotInfo = append(slotInfo, &entity.Slot{SlotID: destinationSlotID})
			return slotInfo, nil
		})
	defer stubs1.Reset()

	stubs2 := gostub.Stub(&checkSecurityIfDenyAccess,
		func(urlStr string, whiteUrls []string) bool {
			return false
		})
	defer stubs2.Reset()

	type args struct {
		ctx          context.Context
		botBizId     string
		tree         *KEP.TaskFlow
		taskFlowId   string
		taskFlowName string
		safeUrls     []string
	}
	tests := []struct {
		name string
		args args
		want *NodeVerifyErrMsg
	}{
		{
			name: "标准",
			want: nil,
			args: args{
				ctx:          context.Background(),
				botBizId:     "aabb",
				taskFlowId:   "e846c17d-9dee-4ed4-b0d5-0617701ce778",
				taskFlowName: "自测1",
				tree:         buildTaskFlowJSON1(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			js, err := protoutil.TaskFlowToJson(tt.args.tree)
			if err != nil {
				t.Errorf("CheckTaskFlowJSON() err: %+v", err)
				return
			}
			t.Logf(js)

			if got := CheckTaskFlowJSON(tt.args.ctx, tt.args.botBizId, tt.args.tree, tt.args.taskFlowId, tt.args.taskFlowName, tt.args.safeUrls); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckTaskFlowJSON() = %v, want %v", got, tt.want)
			}
		})
	}
}
