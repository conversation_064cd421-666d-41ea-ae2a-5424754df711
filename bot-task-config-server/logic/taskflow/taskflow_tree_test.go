package taskflow

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"

var (

	// 出发地1 槽位ID
	//fromSlotID = "422210d0-9719-4ad8-bb53-fd798b9556bd"
	// 出发地2
	fromSlotID = "e942e724-f7a4-45c8-9dd4-a131b2dd5e42"
	// 目的地1
	//destinationSlotID = "acf4a117-34f0-4c85-8330-512cf7b1571b"
	//	 目的地2
	destinationSlotID = "aee7f2d1-ae72-48cd-bc3b-c2b5e8e4e21a"
)

func buildTaskFlowJSON1() *KEP.TaskFlow {

	taskFlowId := "e846c17d-9dee-4ed4-b0d5-0617701ce778"
	taskFlowName := "自测1"

	nodes := make([]*KEP.TaskFlowNode, 0)
	// 开始节点
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "start",
		NodeName: "开始节点",
		NodeType: KEP.NodeType_START,
		NodeUI: &KEP.UIParams{
			X: "70",
			Y: "60",
		},
		NodeData: &KEP.TaskFlowNode_StartNodeData{
			StartNodeData: &KEP.StartNodeData{},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "edge-0.86330292753169571709884888397",
				BranchType: KEP.Branch_DIRECT,
				NextNodeID: "48bf390b-2d8a-61e8-2b4d-58b10d5ac588",
				PrevNodeID: "start",
			},
		},
	})

	// 询问节点 - 用户选项卡
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "48bf390b-2d8a-61e8-2b4d-58b10d5ac588",
		NodeName: "信息澄清1",
		NodeType: KEP.NodeType_REQUEST,
		NodeData: &KEP.TaskFlowNode_RequestNodeData{
			RequestNodeData: &KEP.RequestNodeData{
				Request: &KEP.RequestNodeData_RequestInfo{
					ID:           "信息澄清1-req-1",
					RequestType:  KEP.RequestNodeData_RequestInfo_SLOT,
					RequestValue: fromSlotID,
					AskType:      KEP.RequestNodeData_RequestInfo_LLM,
					CustomAsk:    "",
					LLMAskPreview: []string{
						"您好，请问您需要订机票吗？如果是，请告诉我您的出发地是哪里？",
					},
					IsRequired: true,
				},
				EnableCard: true,
				CardFrom:   KEP.RequestNodeData_INPUT,
				InputCard: &KEP.RequestNodeData_InputCardContent{
					InputCardContent: []string{
						"深圳",
						"广州",
						"武汉",
						"北京",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "edge-0.148005622060666031705480171052",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
						SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
							SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
								SlotID: fromSlotID,
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_EQ,
						InputValues: []string{
							"广州",
						},
					},
				},
				NextNodeID: "caccf3a1-b525-772f-adcb-4e995f4b8431",
				PrevNodeID: "48bf390b-2d8a-61e8-2b4d-58b10d5ac588",
			},
			{
				BranchID:   "edge-0.88035900794620691705483231033",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
						SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
							SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
								SlotID: fromSlotID,
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_EQ,
						InputValues: []string{
							"广州",
						},
					},
				},
				NextNodeID: "5e559733-cd1b-0ee9-f4e1-0337fbdc94d1",
				PrevNodeID: "48bf390b-2d8a-61e8-2b4d-58b10d5ac588",
			},
			{
				BranchID:   "edge-0.65952654372232461705483870351",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_SLOT,
						SourceValue: &KEP.ConditionInfo_BranchCondition_SlotValueData{
							SlotValueData: &KEP.ConditionInfo_BranchCondition_SlotValue{
								SlotID: fromSlotID,
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_EQ,
						InputValues: []string{
							"武汉",
						},
					},
				},
				NextNodeID: "1da4b5f7-52c8-7849-dd2c-012f857a6452",
				PrevNodeID: "48bf390b-2d8a-61e8-2b4d-58b10d5ac588",
			},
		},
	})

	// API节点-1
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "caccf3a1-b525-772f-adcb-4e995f4b8431",
		NodeName: "智能接口1",
		NodeType: KEP.NodeType_API,
		NodeData: &KEP.TaskFlowNode_ApiNodeData{
			ApiNodeData: &KEP.APINodeData{
				API: &KEP.APINodeData_APIInfo{
					URL:    "http://9.137.220.143:18080",
					Method: "GET",
				},
				Request: []*KEP.APINodeData_RequestParam{
					{
						ParamID:    "api-1-input-1",
						ParamName:  "Destination",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SLOT,
						SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
							SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
								SlotID:    destinationSlotID,
								AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
								CustomAsk: "您好，请问您需要订机票的服务吗？如果是，请告诉我您的目的地是哪里。",
							},
						},
						IsRequired: true,
					},
				},
				Headers: []*KEP.APINodeData_Header{
					{
						ParamID:    "api-1-header-1",
						ParamName:  "auth",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_Header_SYSTEM,
						SourceValue: &KEP.APINodeData_Header_SystemValueData{
							SystemValueData: &KEP.APINodeData_Header_SystemValue{
								Value: "UserID",
							},
						},
					},
				},
				LLMAskPreview: []string{},
				Response: []*KEP.APINodeData_ResponseParam{
					{
						ParamID:    "ede15c57-2270-fd42-1086-5dff0d1b0dd7",
						ParamName:  "Destination",
						ParamType:  ParamTypeString,
						ParamTitle: "机票信息",
						JSONPath:   "Body.Destination",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "edge-0.91842845250785191705480231053",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
						SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
							APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
								ParamID: "ede15c57-2270-fd42-1086-5dff0d1b0dd7",
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_EQ,
						InputValues: []string{
							"杭州",
						},
					},
				},
				NextNodeID: "333c27d5-b525-b783-a4ba-d46c1f27cc00",
				PrevNodeID: "caccf3a1-b525-772f-adcb-4e995f4b8431",
			},
			{
				BranchID:   "edge-0.145473697562610751705482358077",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
						SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
							APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
								ParamID: "ede15c57-2270-fd42-1086-5dff0d1b0dd7",
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_IS_SET,
						InputValues: []string{
							"香港",
							"北京",
							"上海",
						},
					},
				},
				NextNodeID: "27c291a5-195e-adcb-3e95-29920a53d28c",
				PrevNodeID: "caccf3a1-b525-772f-adcb-4e995f4b8431",
			},
			{
				BranchID:   "edge-0.54356049345873351705482808712",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
						SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
							APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
								ParamID: "ede15c57-2270-fd42-1086-5dff0d1b0dd7",
							},
						},
						Comparison: KEP.ConditionInfo_BranchCondition_NOT_SET,
						InputValues: []string{
							"西安",
							"武汉",
							"成都",
						},
					},
				},
				NextNodeID: "fb4e6e18-f69b-42e8-d958-6fcf9a5b8414",
				PrevNodeID: "caccf3a1-b525-772f-adcb-4e995f4b8431",
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "333c27d5-b525-b783-a4ba-d46c1f27cc00",
		NodeName: "结束回复1",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_INPUT,
				InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
					Preview: "<p>api出参-string：等于，机票预订成功，机票信息：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"caccf3a1-b525-772f-adcb-4e995f4b8431_Destination\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A31-%E6%9C%BA%E7%A5%A8%E4%BF%A1%E6%81%AF%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%22caccf3a1-b525-772f-adcb-4e995f4b8431%22%7D\">$智能接口1-机票信息</span></p>",
				},
			},
		},
	})
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "27c291a5-195e-adcb-3e95-29920a53d28c",
		NodeName: "结束回复2",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_LLM,
				LLMAnswerData: &KEP.AnswerNodeData_LLMAnswer{
					Preview: []string{
						"您的订机票操作已成功，请查看您的机票信息。",
					},
				},
			},
		},
	})
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "fb4e6e18-f69b-42e8-d958-6fcf9a5b8414",
		NodeName: "结束回复3",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_INPUT,
				InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
					Preview: "<p>api出参-string：不包含，机票预订成功，机票信息：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"caccf3a1-b525-772f-adcb-4e995f4b8431_caccf3a1-b525-772f-adcb-4e995f4b8431_Destination\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A31-%E6%9C%BA%E7%A5%A8%E4%BF%A1%E6%81%AF%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%22caccf3a1-b525-772f-adcb-4e995f4b8431%22%7D\">$智能接口1-机票信息</span></p>",
				},
			},
		},
	})

	// API节点-2
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "5e559733-cd1b-0ee9-f4e1-0337fbdc94d1",
		NodeName: "智能接口2",
		NodeType: KEP.NodeType_API,
		NodeData: &KEP.TaskFlowNode_ApiNodeData{
			ApiNodeData: &KEP.APINodeData{
				API: &KEP.APINodeData_APIInfo{
					URL:    "http://9.137.220.143:18080",
					Method: "GET",
				},
				Request: []*KEP.APINodeData_RequestParam{
					{
						ParamID:    "api-2-input-1",
						ParamName:  "Destination",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SLOT,
						SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
							SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
								SlotID:    destinationSlotID,
								AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
								CustomAsk: "请问你的目的地是？",
							},
						},
						IsRequired: true,
					},
				},
				Headers: []*KEP.APINodeData_Header{
					{
						ParamID:    "api-2-header-1",
						ParamName:  "name",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_Header_API_RESP,
						SourceValue: &KEP.APINodeData_Header_APIRespValueData{
							APIRespValueData: &KEP.APINodeData_Header_APIRespValue{
								ParamID: "ede15c57-2270-fd42-1086-5dff0d1b0dd7",
							},
						},
					},
				},
				LLMAskPreview: []string{},
				Response: []*KEP.APINodeData_ResponseParam{
					{
						ParamID:    "b4e6bdf3-995c-0d7b-4a37-700aacd8a1cc",
						ParamName:  "City",
						ParamType:  ParamTypeString,
						ParamTitle: "城市信息",
						JSONPath:   "City",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "edge-0.33692839567550051705483292029",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
						SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
							APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
								ParamID: "b4e6bdf3-995c-0d7b-4a37-700aacd8a1cc",
							},
						},
						Comparison:  KEP.ConditionInfo_BranchCondition_IS_SET,
						InputValues: []string{},
					},
				},
				NextNodeID: "0fd7a940-725e-7b4e-2fdd-4fe381888abf",
				PrevNodeID: "5e559733-cd1b-0ee9-f4e1-0337fbdc94d1",
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "0fd7a940-725e-7b4e-2fdd-4fe381888abf",
		NodeName: "结束回复4",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_INPUT,
				InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
					Preview: "<p>api出参-string：已填充，机票预订成功，调用接口返回信息是：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"5e559733-cd1b-0ee9-f4e1-0337fbdc94d1_5e559733-cd1b-0ee9-f4e1-0337fbdc94d1_City\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A32-%E5%9F%8E%E5%B8%82%E4%BF%A1%E6%81%AF%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%225e559733-cd1b-0ee9-f4e1-0337fbdc94d1%22%7D\">$智能接口2-城市信息</span></p>",
				},
			},
		},
	})

	// API节点-3
	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "1da4b5f7-52c8-7849-dd2c-012f857a6452",
		NodeName: "智能接口3",
		NodeType: KEP.NodeType_API,
		NodeData: &KEP.TaskFlowNode_ApiNodeData{
			ApiNodeData: &KEP.APINodeData{
				API: &KEP.APINodeData_APIInfo{
					URL:    "http://9.137.220.143:18080",
					Method: "GET",
				},
				Request: []*KEP.APINodeData_RequestParam{
					{
						ParamID:    "api-3-input-1",
						ParamName:  "Destination",
						ParamType:  ParamTypeString,
						SourceType: KEP.APINodeData_RequestParam_SLOT,
						SourceValue: &KEP.APINodeData_RequestParam_SlotValueData{
							SlotValueData: &KEP.APINodeData_RequestParam_SlotValue{
								SlotID:    destinationSlotID,
								AskType:   KEP.APINodeData_RequestParam_SlotValue_INPUT,
								CustomAsk: "请问你的目的地是？",
							},
						},
						IsRequired: true,
					},
				},
				LLMAskPreview: []string{},
				Response: []*KEP.APINodeData_ResponseParam{
					{
						ParamID:    "c35392be-4d50-f127-dffb-11c77a527694",
						ParamName:  "City2",
						ParamType:  ParamTypeString,
						ParamTitle: "城市信息",
						JSONPath:   "City2",
					},
				},
			},
		},
		Branches: []*KEP.Branch{
			{
				BranchID:   "edge-0.289467015545394451705483977436",
				BranchType: KEP.Branch_CUSTOM,
				ConditionInfo: &KEP.ConditionInfo{
					//ConditionsLogic: KEP.ConditionInfo_AND,
					Condition: &KEP.ConditionInfo_BranchCondition{
						SourceType: KEP.ConditionInfo_BranchCondition_API_RESP,
						SourceValue: &KEP.ConditionInfo_BranchCondition_APIRespValueData{
							APIRespValueData: &KEP.ConditionInfo_BranchCondition_APIRespValue{
								ParamID: "c35392be-4d50-f127-dffb-11c77a527694",
							},
						},
						Comparison:  KEP.ConditionInfo_BranchCondition_NOT_SET,
						InputValues: []string{},
					},
				},
				NextNodeID: "faa34478-b4a2-668c-123f-94249a353c56",
				PrevNodeID: "1da4b5f7-52c8-7849-dd2c-012f857a6452",
			},
		},
	})

	nodes = append(nodes, &KEP.TaskFlowNode{
		NodeID:   "faa34478-b4a2-668c-123f-94249a353c56",
		NodeName: "结束回复5",
		NodeType: KEP.NodeType_ANSWER,
		NodeData: &KEP.TaskFlowNode_AnswerNodeData{
			AnswerNodeData: &KEP.AnswerNodeData{
				AnswerType: KEP.AnswerNodeData_INPUT,
				InputAnswerData: &KEP.AnswerNodeData_InputAnswer{
					Preview: "<p>api出参-string：未填充，机票预订成功，城市信息：<span data-w-e-type=\"slot\" data-w-e-is-void data-w-e-is-inline data-value=\"1da4b5f7-52c8-7849-dd2c-012f857a6452_City2\" data-info=\"%7B%22name%22%3A%22%E6%99%BA%E8%83%BD%E6%8E%A5%E5%8F%A33-%E5%9F%8E%E5%B8%82%E4%BF%A1%E6%81%AF%22%2C%22type%22%3A%22params%22%2C%22nodeId%22%3A%221da4b5f7-52c8-7849-dd2c-012f857a6452%22%7D\">$智能接口3-城市信息</span></p>",
				},
			},
		},
	})

	edges := make([]*KEP.Edge, 0)
	tf := &KEP.TaskFlow{
		TaskFlowID:   taskFlowId,
		TaskFlowName: taskFlowName,
		Nodes:        nodes,
		Edges:        edges,
	}
	return tf
}
