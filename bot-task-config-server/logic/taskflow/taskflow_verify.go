// bot-task-config-server
//
// @(#)taskflow_tree.go  星期二, 十二月 26, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package taskflow

import (
	"encoding/json"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

const (
	APIMethodGet  = "GET"
	APIMethodPost = "POST"

	APIProtocolHTTP  = "http"
	APIProtocolHTTPS = "https"

	ParamTypeString = "string" // string类型
	ParamTypeInt    = "int"    // int类型
	ParamTypeFloat  = "float"  // float类型
	ParamTypeBool   = "bool"   // bool类型
	//ParamTypeArray 为了兼容历史数据 原array类型描述变为 array<string>，实际存储的还是array
	// （v2.3）array类型描述变为 array<string>,新增 array<object>
	// https://tapd.woa.com/70080800/prong/stories/view/1070080800116471256
	ParamTypeArray       = "array"         // array类型
	ParamTypeArrayObject = "array<object>" // array<object>
	//ParamTypeObject （v2.3） https://tapd.woa.com/70080800/prong/stories/view/1070080800117214261
	ParamTypeObject = "object" // object

)

/*
1. 节点类型：
   节点类型(NodeType)：开始、判断询问、API、答案 "startNode"  "RequestNode"  "APINode"  "AnswerNode"
   节点名称在画布范围内不可重复
   整个画布的节点上限数为100个
   单一节点的分支上限数为20个
   分支内具体条件上限数为5

2. 节点跳转规则
   跳转类型BranchType：直接跳转(direct）,自定义:custom

3. 多个流转条件之间的符号链接(ConditionsLogic)：And(本期)、Or （最多5个条件组合，最少1个）
4. 跳转规则信息中的Operator（"==","!=","<",">","contains","not contains", "filled", "unfilled"）
	-  信息名称是前面已定义过的必要信息名称时，条件可选包含/不包含（包含内的多个条件是或的关系）、大于/等于/小于；
	-  信息名称是前面API出参的信息名称时，条件可选包含/不包含、大于/等于/小于、已填充/未填充；
	-  信息名称是用户ID时，条件可选填充/未填充；

  数组只能支持 包含、不包含、已填充、未填充


5. 询问节点：
	必要信息：RequiredInfo 目前UI及需求只要一个必要信息
    问法在画布范围内不可重复 .

6. 答案节点
	答案节点数据来源：大模型自动生成（auto）、自定义（custom）

7. API节点：
	必要信息：RequiredInfo： 可以是多个(最多20)，每个必要信息字数限制不超过50个字符
	参数数据类型 string, int, float, bool, array：
	请求API路径协议：仅限http、https,
    请求方法：GET / POST
	参数：APIResponseParams 最多20个，最少0个

	同一个节点的入参 参数名称(SlotVarName)、询问信息（SlotName）。 自定义话术(ProbeCustom) 不能重复
			出参 出参字段（ParamVarName），信息(参数)名称(SlotVarName), 解析路径(ParamPath) 不能重复


8. 所有分支 必需 以回复节点结束

		// TODO... 需要校验引用参数（ID/名称）里面的是否存在

*/

// NodeVerifyErrMsg 校验节点返回
type NodeVerifyErrMsg struct {
	Err      error
	NodeID   string
	NodeType string
	NodeName string
}

func verityErr(msg string) *NodeVerifyErrMsg {
	return &NodeVerifyErrMsg{Err: errors.TaskFLowVerifyError(msg)}
}
func verityErrNode(node *KEP.TaskFlowNode, err error) *NodeVerifyErrMsg {
	return &NodeVerifyErrMsg{
		Err:      err,
		NodeID:   node.NodeID,
		NodeType: node.NodeType.String(),
		NodeName: node.NodeName,
	}
}

// getNodeNamesAndDuplicateIDs 获取所有节点的名称及可能重复节点的名称及ID, 1
func getNodeNamesAndDuplicateIDs(t *KEP.TaskFlow) (map[string]string, map[string][]string) {
	nodeNames := make(map[string]string)      //所有节点的名称
	duplicateIDs := make(map[string][]string) // 重复节点名称的id集合

	for _, node := range t.Nodes {
		nodeName := node.NodeName
		nodeID := node.NodeID

		if existingID, ok := nodeNames[nodeName]; ok {
			// 检查是否有重复节点名称
			if _, ok := duplicateIDs[nodeName]; !ok {
				duplicateIDs[nodeName] = []string{existingID}
			}
			// 有重复的节点名称，它将这些重复的节点 ID 存储在一个 map 中
			duplicateIDs[nodeName] = append(duplicateIDs[nodeName], nodeID)
		} else {
			nodeNames[nodeName] = nodeID
		}
	}

	return nodeNames, duplicateIDs
}

// RemoveRichText 移除富文本
//
//	func RemoveRichText(prompt string) string {
//		const NoReadRegEx = `<[^>]+data-tel="no-read"[^>]*>(.*?)<\/(.*?)>` // 过滤带no-read属性值的标签
//		// 过滤带no-read属性值的标签和标签中的内容
//		dataFilter, _ := regexp.Compile(NoReadRegEx)
//		prompt = dataFilter.ReplaceAllString(prompt, "")
//
//		filter := `<p>|<\/p>|<span[^>]*>|<\/span>|<a [^>]*>|<\/a>|<div[^>]*>|<\/div>|<br[^>]*>|<\/br>|&nbsp;`
//		return regexp.MustCompile(filter).ReplaceAllString(prompt, "")
//	}
//
// RemoveRichText 移除富文本
func RemoveRichText(prompt string) string {
	//text := `<p>哈哈哈😃sssd &nbsp;<a href=\"http://www.baidu.com\"
	//target=\"_blank\">请求天气</a> <span data-w-e-type=\"slot\"
	//data-w-e-is-void data-w-e-is-inline data-value=\"api_weather\"
	//data-info="{\"name\":\"API节点-天气\",\"type\":\"params\",\"nodeId\":\"api\"}">
	//$API节点-天气</span>哈哈哈</p>`

	// 处理后： 哈哈哈😃sssd 请求天气 哈哈哈
	if prompt == "" {
		return ""
	}
	// Node ...
	type Node struct {
		Name   string `json:"name"`
		Type   string `json:"type"`
		NodeID string `json:"nodeId"`
	}
	var node Node

	const InfoRegEx = `data-info="({[^}]+})"`
	infoFilter, _ := regexp.Compile(InfoRegEx)
	infos := infoFilter.FindStringSubmatch(prompt)
	if len(infos) >= 2 {
		info := infos[1]
		info = strings.ReplaceAll(info, "\\", "")
		err := json.Unmarshal([]byte(info), &node)
		if err == nil {
			name := node.Name
			prompt = strings.Replace(prompt, "$"+name, "", -1)
		} else {
			log.Errorf("RemoveRichText:%s|%s\n", prompt, err.Error())
		}
	}

	filter := `<p>|<\/p>|<span[^>]*>|<\/span>|<a [^>]*>|<\/a>|<div[^>]*>|<\/div>|<br[^>]*>|<\/br>|&nbsp;`
	return regexp.MustCompile(filter).ReplaceAllString(prompt, "")
}
