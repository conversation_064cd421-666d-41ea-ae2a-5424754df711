/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"runtime/debug"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// 1. 识别环
// 2.游离的节点 (findSourceNodes)
// 3. 识别"参数提取节点"是否存在于“分支”中
// 4. 校验前序节点的依赖正确性；
//   a. 手写输入； b. 引用； c. 系统参数; d. 自定义参数；
// 5. errs 统一处理； 处理多个；
// 6. 流程必须以"结束节点"作为结束
//
// 前驱（predecessor）: 某个顶点的“父节点”或“上游节点”。
// 后继（successor）: 某个顶点的“子节点”或“下游节点”。

// WfContext Workflow 上下文，用于解析和树中取数据；
type WfContext struct {
	// 必要参数
	ctx      context.Context
	AppID    string
	Workflow *KEP_WF.Workflow
	SaveType uint32
	// 维护应用下的安全白名单
	SafeUrls []string
	// 维护Workflow和"自定义模型"的关联关系：大模型相关节点(大模型节点, 大模型知识问答节点,标签提取节点,意图识别节点)中引用了
	// v2.7.1&2.7.5 新增
	// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800122056509
	customModels []entity.WorkflowNodeCustomModel
	// 维护Workflow和"参数"的关联关系： 参数提取节点中使用的"参数"
	parameters []entity.WorkflowNodeParam
	// 维护Workflow和"工作流"的关联关系： 当前工作流引用的工作流的，工具节点中使用
	refWorkflows []entity.WorkflowReferenceParams
	// 维护Workflow和"自定义变量"的关联关系： 工作流与自定义变量（API参数）的引用关系， 所有带"input"的地方，目前有：工具、代码、逻辑判断节点
	customVarIDs []string
	// 维护Workflow和"知识型"的关联关系： 知识问答节点 或 知识检索节点中使用的知识类的引用关系
	knowledgeRef []entity.WorkflowRefKnowledgeParams
	// 维护Workflow和"插件"的关联关系：插件节点中使用的插件的引用关系
	pluginRef []entity.WorkflowRefPluginParams

	refWorkflowInputs    map[string][]*KEP_WF.InputParam  // 引用的工作流的输入参数。 key: 引用的工作流ID， value: 输入参数
	refWorkflowOutputs   map[string][]*KEP_WF.OutputParam // 引用的工作流的输出参数。 key: 引用的工作流ID， value: 输出参数
	refWorkflowIDNodeIDs map[string][]string              // 引用的工作流所归属的NodeID。 key: 引用的工作流ID， value: 归属的NodeIDs

	refToolIDToolNodeData map[string][]*KEP_WF.WorkflowNode // 引用的插件的工具信息

	// 内部用
	startNodeID      string
	nodeMap          map[string]*KEP_WF.WorkflowNode
	customVarIDCache map[string]*entity.VarParams               // 用于校验，"自定义变量"
	systemVarCache   map[string]*KEP.SystemVar                  // 用于校验，"系统变量"
	customModelCache map[string]*entity.WorkflowNodeCustomModel //用于去重
	nodeErrors       []*NodeError                               // "保存到测试环境"时检查到的错误

	htmlRegexp            *regexp.Regexp // 用于判断html标签的正则
	paramInRichTextRegexp *regexp.Regexp // 用于判断前端富文本中取参数名
	inputParamNameRegexp  *regexp.Regexp // 用于判断 输入参数名字要求正则表达式

	predecessors map[string][]string // 前驱节点IDs key: 当前节点ID, value: 前驱节点IDs
}

// New  创建一个新的 WorkFlow的上下文 WfContext 实例
func New(ctx context.Context, appID string, workflow *KEP_WF.Workflow, saveType uint32, safeUrls []string) *WfContext {
	return &WfContext{
		ctx:      ctx,
		AppID:    appID,
		Workflow: workflow,
		SaveType: saveType,
		SafeUrls: safeUrls,
		//parameters:   make([]entity.WorkflowNodeParam, 0),
		//customVarIDs: make([]string, 0),
		//knowledgeRef: make([]entity.WorkflowRefKnowledgeParams, 0),

		nodeMap:          make(map[string]*KEP_WF.WorkflowNode),
		customVarIDCache: make(map[string]*entity.VarParams),
		systemVarCache:   make(map[string]*KEP.SystemVar),
		customModelCache: make(map[string]*entity.WorkflowNodeCustomModel),
		nodeErrors:       make([]*NodeError, 0),

		predecessors: make(map[string][]string),
	}

}

// Parse 解析和检查
func (c *WfContext) Parse() error {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(c.ctx, "check.Parse|panic: %v, stack: \n%s", r, string(debug.Stack()))
		}
	}()
	if err := c.initNodeMap(); err != nil {
		return err
	}
	if err := c.initRegexp(); err != nil {
		return err
	}
	if err := c.loadCache(); err != nil {
		return err
	}

	//if c.SaveType != uint32(KEP_WF.SaveWorkflowReq_ENABLE) {
	//	return nil
	//}
	// 检查所有入度为0的节点（开始节点/游离节点）
	c.checkSourceNode()
	c.printWorkflow()
	c.checkEndWithAnswer()
	c.checkNodes()

	if err := c.checkRefPlugins(); err != nil {
		return err
	}

	startTime := time.Now()
	if !c.checkParallelNodesValid() {
		log.WarnContext(c.ctx, "check.Parse|checkParallelNodesValid|err: check failed")
	}
	log.InfoContextf(c.ctx, "check.Parse|checkParallelNodesValid|elapsed:%s", time.Since(startTime).String())

	if err := c.checkRefWorkflows(); err != nil {
		return err
	}

	return nil
}

func (c *WfContext) initNodeMap() error {
	if len(c.Workflow.GetNodes()) == 0 {
		return fmt.Errorf("请先配置节点")
	}
	if len(c.Workflow.GetNodes()) == 1 && c.Workflow.GetNodes()[0].GetNodeType() == KEP_WF.NodeType_START {
		c.appendNodeError(c.Workflow.GetNodes()[0].GetNodeID(), "请先配置流程节点")
	}
	if len(c.Workflow.GetNodes()) > config.GetMainConfig().VerifyWorkflow.UiNodeTotal {
		// 总结点数不能超过 xxx
		return fmt.Errorf("节点总数不能超过%d(现在是%d)",
			config.GetMainConfig().VerifyWorkflow.UiNodeTotal, len(c.Workflow.GetNodes()))
	}
	// 检查图中是否存在环，并输出形成环的路径
	if hasCycle, cyclePath := checkCycle(c.Workflow.GetNodes()); hasCycle {
		log.WarnContextf(c.ctx, "initNodeMap|checkCycle|hasCycle|cyclePath:%s", strings.Join(cyclePath, " -> "))
		return fmt.Errorf("存在环%s", strings.Join(cyclePath, " -> "))
	}
	startNodeCount := 0
	endNodeCount := 0
	for _, n := range c.Workflow.GetNodes() {
		if len(n.GetNodeID()) == 0 {
			return fmt.Errorf("NodeID不应为空")
		}
		if len(n.GetNodeName()) == 0 {
			return fmt.Errorf("节点名称不应为空")
		}
		if strings.Contains(n.GetNodeID(), "#") {
			return fmt.Errorf("NodeID不应包含#")
		}
		nameMaxLen := config.GetMainConfig().VerifyWorkflow.NodeNameMaxLen
		if utf8.RuneCountInString(n.GetNodeName()) > nameMaxLen {
			return fmt.Errorf("节点名称 %s 不应大于%d个字符，请重新填写", n.GetNodeName(), nameMaxLen)
		}
		c.nodeMap[n.GetNodeID()] = n
		switch n.GetNodeType() {
		case KEP_WF.NodeType_START:
			c.startNodeID = n.GetNodeID()
			startNodeCount++
		case KEP_WF.NodeType_END:
			endNodeCount++
		}
		nextIDs := getNextIDs(n)
		for _, nextID := range nextIDs {
			c.predecessors[nextID] = append(c.predecessors[nextID], n.GetNodeID())
		}
	}
	log.InfoContextf(c.ctx, "Parse|startNodeCount:%d|endNodeCount:%d", startNodeCount, endNodeCount)
	if startNodeCount != 1 {
		return fmt.Errorf("开始节点应该为一个，现在是%d个", startNodeCount)
	}
	if endNodeCount != 1 {
		return fmt.Errorf("结束节点应该为一个，现在是%d个", endNodeCount)
	}

	for _, node := range c.Workflow.GetNodes() {
		nextNodeIDs := getNextIDs(node)
		for _, nextNodeID := range nextNodeIDs {
			if _, exist := c.nodeMap[nextNodeID]; !exist {
				return fmt.Errorf("%s节点的下游节点ID非法(%s)", node.GetNodeName(), nextNodeID)
			}
		}
	}

	return nil
}

func (c *WfContext) initRegexp() error {
	var err error
	if c.htmlRegexp, err = regexp.Compile("<[^>]*>"); err != nil {
		log.Warnf("initRegexp|htmlRegexp|regexp.Compile:%+v", err)
		return err
	}

	if c.paramInRichTextRegexp, err = regexp.Compile(`{{(.*?)}}`); err != nil {
		log.Warnf("initRegexp|paramInRichTextRegexp|regexp.Compile:%+v", err)
		return err
	}

	pattern := config.GetMainConfig().VerifyWorkflow.NodeInputParamNamePattern
	if len(pattern) == 0 {
		pattern = `^[a-zA-Z_][a-zA-Z0-9_]*$`
	}
	if c.inputParamNameRegexp, err = regexp.Compile(pattern); err != nil {
		log.Warnf("initRegexp|inputParamNameRegexp|regexp.Compile:%+v", err)
		return err
	}
	return nil
}

// 源节点: 入度为0的节点
// 没有 前驱节点
func (c *WfContext) checkSourceNode() {
	sourceNodes := findSourceNodes(c.nodeMap)
	for _, node := range sourceNodes {
		if node.GetNodeType() == KEP_WF.NodeType_START {
			continue
		}
		log.InfoContextf(c.ctx, "checkSourceNode|findSourceNodes|node:%+v", node)
		c.appendNodeError(node.GetNodeID(), "未连接到流程中（请连接到流程中或删除）")
	}
}

func (c *WfContext) checkEndWithAnswer() {
	invalidNodes := getInvalidLeafNodes(c.nodeMap, c.startNodeID)
	for _, node := range invalidNodes {
		c.appendNodeError(node.GetNodeID(), "流程必须以\"结束节点\"作为结束")
	}
}

func (c *WfContext) checkNodes() {
	for _, node := range c.Workflow.GetNodes() {
		nodeTypeStr := node.GetNodeType().String()
		startTime := time.Now()
		c.checkInputParams(node)
		c.checkExceptionHandling(node)
		var err error
		switch node.GetNodeType() {
		case KEP_WF.NodeType_START:
			c.parseStartNode(node)
		case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
			c.parseParameterExtractorNode(node)
		case KEP_WF.NodeType_LLM:
			c.parseLLMNode(node)
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
			c.parseLLMKnowledgeQANode(node)
		case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
			c.parseKnowledgeRetrieverNode(node)
		case KEP_WF.NodeType_TAG_EXTRACTOR:
			c.parseTagExtractorNode(node)
		case KEP_WF.NodeType_CODE_EXECUTOR:
			c.parseCodeExecutorNode(node)
		case KEP_WF.NodeType_TOOL:
			c.parseToolNode(node)
		case KEP_WF.NodeType_LOGIC_EVALUATOR:
			c.parseLogicEvaluatorNode(node)
		case KEP_WF.NodeType_ANSWER:
			c.parseAnswerNode(node)
		case KEP_WF.NodeType_OPTION_CARD:
			c.parseOptionCardNode(node)
		case KEP_WF.NodeType_ITERATION:
			c.parseIteration(node)
		case KEP_WF.NodeType_INTENT_RECOGNITION:
			c.parseIntentRecognition(node)
		case KEP_WF.NodeType_WORKFLOW_REF:
			c.parseWorkflowRef(node)
		case KEP_WF.NodeType_PLUGIN:
			c.parsePlugin(node)
		case KEP_WF.NodeType_END:
			c.parseEnd(node)
		case KEP_WF.NodeType_VAR_AGGREGATION:
			c.parseVarAggregation(node)
		case KEP_WF.NodeType_BATCH:
			c.parseBatch(node)
		case KEP_WF.NodeType_MQ:
			c.parseMQNode(node)
		default:
			err = errors.TaskFLowVerifyError(fmt.Sprintf("%s节点类型错误", nodeTypeStr))
		}
		if err != nil {
			log.WarnContextf(c.ctx, "parse|nodeType:%s|node:%+v|err:%+v", nodeTypeStr, node, err)
		}
		// 收集节点引用的模型信息
		c.collectWorkflowCustomModel(node.NodeID, node.GetNodeType(), node)
		duration := time.Since(startTime)
		if duration > time.Millisecond*30 {
			log.WarnContextf(c.ctx, "checkNodes-elapsed|nodeType:%s|nodeID:%s|%s", nodeTypeStr,
				node.GetNodeID(), duration.String())
		} else {
			log.InfoContextf(c.ctx, "checkNodes-elapsed|nodeType:%s|nodeID:%s|%s", nodeTypeStr,
				node.GetNodeID(), duration.String())
		}
	}
}

func (c *WfContext) checkInputParams(wfn *KEP_WF.WorkflowNode) {
	if wfn.GetNodeType() == KEP_WF.NodeType_END {
		return // 结束节点没有输入变量
	}
	max := config.GetMainConfig().VerifyWorkflow.NodeInputParamMax
	// 代码节点的入参个数有单独的判断；
	if wfn.GetNodeType() != KEP_WF.NodeType_CODE_EXECUTOR && len(wfn.GetInputs()) > max {
		c.appendNodeError(wfn.GetNodeID(), fmt.Sprintf("输入参数个数不能超过%d", max))
	}

	nameLen := config.GetMainConfig().VerifyWorkflow.NodeInputParamNameLen
	defaultValueLen := config.GetMainConfig().VerifyWorkflow.NodeInputParamDefaultValueLen
	for _, inputParam := range wfn.GetInputs() {

		c.checkInputAndCollect(wfn.GetNodeID(), inputParam.GetInput())

		c.checkSubInputParams(wfn.GetNodeID(), nameLen, defaultValueLen, inputParam)
	}
}

func (c *WfContext) checkExceptionHandling(wfn *KEP_WF.WorkflowNode) {
	if wfn.GetExceptionHandling().GetSwitch() == KEP_WF.ExceptionHandling_OFF {
		return
	}
	retryMax := config.GetMainConfig().VerifyWorkflow.RetryMax
	retryIntervalMax := config.GetMainConfig().VerifyWorkflow.RetryIntervalMax
	abnormalOutputResultLen := config.GetMainConfig().VerifyWorkflow.AbnormalOutputResultLen
	if int(wfn.GetExceptionHandling().GetMaxRetries()) > retryMax {
		log.WarnContextf(c.ctx, "checkExceptionHandling retryMax nodeType:%s|nodeID:%s",
			wfn.GetNodeType().String(), wfn.GetNodeID())
		c.appendNodeError(wfn.GetNodeID(), fmt.Sprintf("最大重试次数不能超过%d", retryMax))
	}
	if int(wfn.GetExceptionHandling().GetRetryInterval()) > retryIntervalMax {
		log.WarnContextf(c.ctx, "checkExceptionHandling retryIntervalMax nodeType:%s|nodeID:%s",
			wfn.GetNodeType().String(), wfn.GetNodeID())
		c.appendNodeError(wfn.GetNodeID(), fmt.Sprintf("最大重试次数不能超过%d", retryIntervalMax))
	}
	if len(wfn.GetExceptionHandling().GetAbnormalOutputResult()) > 0 {
		if utf8.RuneCountInString(wfn.GetExceptionHandling().GetAbnormalOutputResult()) > abnormalOutputResultLen {
			log.WarnContextf(c.ctx, "checkExceptionHandling abnormalOutputResultLen nodeType:%s|nodeID:%s",
				wfn.GetNodeType().String(), wfn.GetNodeID())
			c.appendNodeError(wfn.GetNodeID(), fmt.Sprintf("异常输出变量长度不能超过%d", abnormalOutputResultLen))
		}
		var data interface{}
		err := json.Unmarshal([]byte(wfn.GetExceptionHandling().GetAbnormalOutputResult()), &data)
		if err != nil {
			log.WarnContextf(c.ctx, "checkExceptionHandling abnormalOutputResult json nodeType:%s|nodeID:%s",
				wfn.GetNodeType().String(), wfn.GetNodeID())
			c.appendNodeError(wfn.GetNodeID(), "异常情况的输出变量json格式不对")
		}
	}
}

func (c *WfContext) checkSubInputParams(nodeID string, nameLen, defaultValueLen int, inputParam *KEP_WF.InputParam) {
	if utf8.RuneCountInString(inputParam.GetName()) > nameLen {
		c.appendNodeError(nodeID, fmt.Sprintf("%s 参数名不能超过%d", inputParam.GetName(), nameLen))
	}
	if utf8.RuneCountInString(inputParam.GetDefaultValue()) > defaultValueLen {
		log.WarnContextf(c.ctx, "checkInputParams defaultValueLen is too long, value:%s",
			inputParam.GetDefaultValue())
		c.appendNodeError(nodeID, fmt.Sprintf("%s 参数默认值不能超过%d", inputParam.GetDefaultValue(),
			defaultValueLen))
	}

	if !c.inputParamNameRegexp.MatchString(inputParam.GetName()) {
		c.appendNodeError(nodeID, fmt.Sprintf("%s 参数名不合法", inputParam.GetName()))
	}
	// v2.7.5 新增 https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121980947
	// 目前前端只放开开始节点，但后端可以按照通用逻辑先进行限制处理
	// 获取object和array<object>类型的入参的 depth 和len
	inputType := inputParam.GetType()
	if inputType == KEP_WF.TypeEnum_OBJECT || inputType == KEP_WF.TypeEnum_ARRAY_OBJECT {
		subInputLen := config.GetMainConfig().VerifyWorkflow.NodeSubInputParamLen
		subInputDepth := config.GetMainConfig().VerifyWorkflow.NodeSubInputParamDepth
		subLen := len(inputParam.GetSubInputs())   // 子级的len
		depth := c.getInputParamsDepth(inputParam) // 深度
		if subLen > subInputLen {
			c.appendNodeError(nodeID, fmt.Sprintf("%s:子参数的长度length不能超过：%d",
				inputParam.GetName(), subInputLen))
		}

		if depth > subInputDepth {
			c.appendNodeError(nodeID, fmt.Sprintf("%s:子参数的深度depth不能超过：%d",
				inputParam.GetName(), subInputDepth))
		}
	}
	for _, item := range inputParam.GetSubInputs() {
		itemType := item.GetType()
		// 如果父类型为array<object>,子类型必须为object类型（改为为任何类型，可以理解其子级value值的类型）
		//if inputType == KEP_WF.TypeEnum_ARRAY_OBJECT {
		//	if itemType != KEP_WF.TypeEnum_OBJECT {
		//		c.appendNodeError(nodeID, fmt.Sprintf("%s:参数为:%s类型，子参数:%s的类型必须为:%s",
		//			inputParam.GetName(), inputType, item.GetName(), KEP_WF.TypeEnum_OBJECT.String()))
		//	}
		//}
		if itemType == KEP_WF.TypeEnum_OBJECT || itemType == KEP_WF.TypeEnum_ARRAY_OBJECT {
			c.checkSubInputParams(nodeID, nameLen, defaultValueLen, item)
		}
	}
}

func (c *WfContext) getInputParamsDepth(inputParam *KEP_WF.InputParam) int {
	var depth int
	if inputParam == nil {
		return 0
	}
	if inputParam.GetSubInputs() == nil {
		return 1
	}
	depth += 1
	var subDepth int
	for _, sub := range inputParam.GetSubInputs() {
		temDepth := c.getInputParamsDepth(sub)
		if temDepth > subDepth {
			subDepth = temDepth
		}
	}
	depth += subDepth
	return depth
}

func (c *WfContext) checkParallelNodesValid() bool {
	/*************************************************************************************************************
	 任务：检查并行节点是否合法：https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075492950
	 说明：与人交互的节点（目前为：引用工作流节点、循环节点、选项卡节点、参数提取节点）不能并行，例如：
	      a -> b -> c -> e  --
	           |               |
	             -> d -> f ->  h ------
	                |                   | -> k
	                  -> g ->  i -> j --
	 与人交互的节点不能是 c d e f g h i j
	 那么，对于存在多个后继节点的节点而言，它身后的并行的路径除非都合并到与人交互或其他的节点，否则不允许并行路径上出现与人交互的节点
	 因此，对于一个DAG中所有联通路径而言，每一个与人交互的节点都必须出现在路径中
	**************************************************************************************************************/
	// step 1. 构造set<与人交互的节点类型>，用于后续判断
	humanInteractNodeTypeSet := make(map[KEP_WF.NodeType]struct{})
	humanInteractNodeTypeSet[KEP_WF.NodeType_WORKFLOW_REF] = struct{}{}        // 引用工作流节点
	humanInteractNodeTypeSet[KEP_WF.NodeType_ITERATION] = struct{}{}           // 循环节点
	humanInteractNodeTypeSet[KEP_WF.NodeType_OPTION_CARD] = struct{}{}         // 选项卡节点
	humanInteractNodeTypeSet[KEP_WF.NodeType_PARAMETER_EXTRACTOR] = struct{}{} // 参数提取节点
	humanInteractNodeTypeSet[KEP_WF.NodeType_BATCH] = struct{}{}               // 批处理节点
	// step 2. 构造set<存在多分支的节点类型>，用于后续判断
	multiBranchNodeTypeSet := make(map[KEP_WF.NodeType]struct{})
	multiBranchNodeTypeSet[KEP_WF.NodeType_LOGIC_EVALUATOR] = struct{}{}
	multiBranchNodeTypeSet[KEP_WF.NodeType_OPTION_CARD] = struct{}{}
	multiBranchNodeTypeSet[KEP_WF.NodeType_INTENT_RECOGNITION] = struct{}{}
	// step 3. 构造map<节点ID,节点>、节点ID列表、路径查询起始节点列表，用于后续判断
	nodes := c.Workflow.GetNodes()
	nodeMap := make(map[string]*KEP_WF.WorkflowNode)
	nodeIDs := make([]string, 0)
	startNodeIDs := make([]string, 0)
	for i := 0; i < len(nodes); i++ {
		nodeMap[nodes[i].GetNodeID()] = nodes[i]
		nodeIDs = append(nodeIDs, nodes[i].GetNodeID())
		if nodes[i].GetNodeType() == KEP_WF.NodeType_START {
			startNodeIDs = append(startNodeIDs, nodes[i].GetNodeID())
		}
		if _, ok := multiBranchNodeTypeSet[nodes[i].GetNodeType()]; ok {
			startNodeIDs = append(startNodeIDs, nodes[i].GetNodeID())
		}
	}
	// step 4. 获取DAG中从多分支节点（包括根节点）开始到多分支节点（包括出度为0的节点）为止的路径
	allPass := true
	extractPathGroup := func(paths [][]string, nextNodeIDs []string) [][]string {
		nextNodeIDSet := make(map[string]struct{})
		for _, nextNodeID := range nextNodeIDs {
			nextNodeIDSet[nextNodeID] = struct{}{}
		}
		pathGroup := make([][]string, 0)
		for _, path := range paths {
			if len(path) < 2 {
				// 无需校验单节点的路径
				continue
			}
			if _, ok := nextNodeIDSet[path[1]]; ok {
				pathGroup = append(pathGroup, path)
			}
		}
		return pathGroup
	}
	for i := 0; i < len(startNodeIDs); i++ {
		startNode := nodeMap[startNodeIDs[i]]
		// 获取多分支节点开始的所有路径（不区分分支）
		paths := getAllPathsWithMemory(nodeMap, startNodeIDs[i], multiBranchNodeTypeSet)
		// 路径中有出现过与人交互的节点，需要校验路径中并行节点是否合法，多分支节点分支间需要单独处理
		switch startNode.GetNodeType() {
		case KEP_WF.NodeType_LOGIC_EVALUATOR:
			for _, group := range startNode.GetLogicEvaluatorNodeData().GetGroup() {
				pass := c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet, startNode,
					extractPathGroup(paths, group.GetNextNodeIDs()))
				if !pass {
					allPass = false
				}
			}
		case KEP_WF.NodeType_OPTION_CARD:
			cardNode := startNode.GetOptionCardNodeData()
			switch cardNode.GetCardFrom() {
			case KEP_WF.OptionCardNodeData_DYNAMIC:
				// 动态选项卡
				mainNextNodeIDs := cardNode.GetDynamicOptionsRefNextNodeIDs()
				pass := c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet,
					startNode, extractPathGroup(paths, mainNextNodeIDs))
				if !pass {
					allPass = false
				}
				// 动态选项卡，其它选项
				otherNextNodeIDs := cardNode.GetDynamicOptionsElseNextNodeIDs()
				pass = c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet,
					startNode, extractPathGroup(paths, otherNextNodeIDs))
				if !pass {
					allPass = false
				}
			default:
				// 默认（或用户输入）选项
				for _, option := range cardNode.GetOptions() {
					pass := c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet,
						startNode, extractPathGroup(paths, option.GetNextNodeIDs()))
					if !pass {
						allPass = false
					}
				}
			}

		case KEP_WF.NodeType_INTENT_RECOGNITION:
			for _, intent := range startNode.GetIntentRecognitionNodeData().GetIntents() {
				pass := c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet, startNode,
					extractPathGroup(paths, intent.GetNextNodeIDs()))
				if !pass {
					allPass = false
				}
			}
		default:
			pass := c.checkParallelNodesValidInPaths(nodeMap, humanInteractNodeTypeSet, startNode,
				extractPathGroup(paths, startNode.GetNextNodeIDs()))
			if !pass {
				allPass = false
			}
		}
	}
	return allPass
}

func (c *WfContext) checkParallelNodesValidInPaths(nodeMap map[string]*KEP_WF.WorkflowNode,
	humanInteractNodeTypeSet map[KEP_WF.NodeType]struct{}, startNode *KEP_WF.WorkflowNode, paths [][]string) bool {
	pass := true
	// 构造set<路径中出现的与人交互的节点ID>
	humanInteractNodeIDSet := make(map[string]bool)
	for _, path := range paths {
		for _, nodeID := range path {
			if _, ok := humanInteractNodeTypeSet[nodeMap[nodeID].GetNodeType()]; ok {
				humanInteractNodeIDSet[nodeID] = false
			}
		}
	}
	// 路径中没出现过与人交互的节点，检查通过，继续检查其他节点起始的路径
	if len(humanInteractNodeIDSet) == 0 {
		return true
	}
	// 初始化节点错误提示map，避免重复
	nodeErrorMap := make(map[string]string)
	// 检查所有与人交互节点是否都出现过
	for _, path := range paths {
		// map目前只能通过遍历的方式浅拷贝
		tempHumanInteractNodeIDSet := make(map[string]bool)
		for key, value := range humanInteractNodeIDSet {
			tempHumanInteractNodeIDSet[key] = value
		}
		for _, nodeID := range path {
			_, ok := tempHumanInteractNodeIDSet[nodeID]
			if !ok {
				continue
			}
			tempHumanInteractNodeIDSet[nodeID] = true
		}
		for humanInteractNodeID, visited := range tempHumanInteractNodeIDSet {
			if !visited {
				log.WarnContextf(c.ctx,
					"checkParallelNodesValid|checkParallelNodesValidInPaths|err: "+
						"human interact node %s exist in parallel", humanInteractNodeID)
				// 在paths中，humanInteractNodeID是唯一的，startNode也是固定的，因此这里的错误信息也是唯一的
				// 这里直接写到map中即可，允许覆盖
				nodeErrorMap[humanInteractNodeID] = fmt.Sprintf("%s节点%s不允许在节点%s后与其他节点并行",
					nodeMap[humanInteractNodeID].GetNodeType().String(), nodeMap[humanInteractNodeID].GetNodeName(),
					startNode.GetNodeName())
				pass = false
				break
			}
		}
	}
	for humanInteractNodeID, errorMessage := range nodeErrorMap {
		c.appendNodeError(humanInteractNodeID, errorMessage)
	}
	return pass
}

func getNextIDs(node *KEP_WF.WorkflowNode) []string {
	if node == nil {
		return []string{}
	}
	var nextNodeIDs []string
	switch node.GetNodeType() {
	case KEP_WF.NodeType_LOGIC_EVALUATOR:
		for _, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
			nextNodeIDs = append(nextNodeIDs, group.GetNextNodeIDs()...)
		}
	case KEP_WF.NodeType_OPTION_CARD:
		nodeCard := node.GetOptionCardNodeData()
		cardFrom := nodeCard.GetCardFrom()
		// 动态选项卡
		if cardFrom == KEP_WF.OptionCardNodeData_DYNAMIC {
			// 动态选项卡连线
			nextNodeIDs = append(nextNodeIDs, nodeCard.GetDynamicOptionsRefNextNodeIDs()...)
			// 动态选项卡其它连线
			nextNodeIDs = append(nextNodeIDs, nodeCard.GetDynamicOptionsElseNextNodeIDs()...)
		} else {
			// 用户输入
			for _, option := range node.GetOptionCardNodeData().GetOptions() {
				nextNodeIDs = append(nextNodeIDs, option.GetNextNodeIDs()...)
			}
		}

	case KEP_WF.NodeType_INTENT_RECOGNITION:
		for _, intents := range node.GetIntentRecognitionNodeData().GetIntents() {
			nextNodeIDs = append(nextNodeIDs, intents.GetNextNodeIDs()...)
		}
	default:
		return node.GetNextNodeIDs()
	}
	return nextNodeIDs
}

func (c *WfContext) getPredecessorNodeIDsMap(targetNodeID string) map[string]struct{} {
	//predecessors := buildPredecessorsMap(c.nodeMap)
	visited := make(map[string]struct{})
	queue := []string{targetNodeID}
	visited[targetNodeID] = struct{}{}

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		for _, pred := range c.predecessors[current] {
			if _, exists := visited[pred]; !exists {
				visited[pred] = struct{}{}
				queue = append(queue, pred)
			}
		}
	}

	return visited
}

//func buildPredecessorsMap(nodeMap map[string]*KEP_WF.WorkflowNode) map[string][]string {
//	predecessors := make(map[string][]string)
//	for id, node := range nodeMap {
//		nextIDs := getNextIDs(node)
//		for _, nextID := range nextIDs {
//			predecessors[nextID] = append(predecessors[nextID], id)
//		}
//	}
//	return predecessors
//}

// GetWorkCustomModel 获取节点中引用的参数
func (c *WfContext) GetWorkCustomModel() []entity.WorkflowNodeCustomModel {
	return c.customModels
}

// GetParameters 参数提取节点中使用的"参数"
func (c *WfContext) GetParameters() []entity.WorkflowNodeParam {
	return c.parameters
}

// GetRefWorkflows  当前工作流引用的工作流，工具节点中使用
func (c *WfContext) GetRefWorkflows() []entity.WorkflowReferenceParams {
	return c.refWorkflows
}

// GetCustomVarIDs 工作流与自定义变量（API参数）的引用关系
func (c *WfContext) GetCustomVarIDs() []string {
	return c.customVarIDs
}

// GetKnowledgeRef 知识问答节点 或 知识检索节点中使用的知识类的引用关系
func (c *WfContext) GetKnowledgeRef() []entity.WorkflowRefKnowledgeParams {
	return c.knowledgeRef
}

func (c *WfContext) GetRefPlugins() []entity.WorkflowRefPluginParams {
	return c.pluginRef
}

// GetNodeErrors "保存到测试环境"时检查到的错误
func (c *WfContext) GetNodeErrors() []*NodeError {
	return c.nodeErrors
}

// 1. 批量查db，t_workflow 表；
// 2. 不能引用自己
// 2. 检查被引用的工作流是否也有引用其他工作流（引用工作流节点、循环节点）；
// 3. 同时校验在引用该工作流中配置的 input 和 output 是否跟 被引用的 input 和 output 是否一致
func (c *WfContext) checkRefWorkflows() error {
	log.InfoContextf(c.ctx, "checkRefWorkflows|refWorkflowIDNodeIDs:%+v", c.refWorkflowIDNodeIDs)
	if len(c.refWorkflowIDNodeIDs) == 0 || len(c.Workflow.GetWorkflowID()) == 0 {
		return nil
	}
	workflowReferenceDepth := config.GetMainConfig().VerifyWorkflow.WorkflowReferenceDepth
	// 1-1. 获取所有工作流基本记录，构建引用深度
	workflows, workflowDeepMap, err := db.GetAllWorkflowBasics(c.ctx, c.AppID)
	if err != nil {
		return err
	}
	// 获取当前应用下所有工作流引用关系
	workflowRefs, err := db.GetAllWorkflowRefs(c.ctx, c.AppID)
	if err != nil {
		return err
	}
	// 构建工作流直接上下引用关系
	db.BuildWorkflowDirectReferences(workflows, workflowRefs)
	// 构建工作流所有上下引用关系和深度
	db.GetWorkflowsDeep(c.ctx, workflows, workflowDeepMap)

	refWorkflowIDs := make([]string, 0, len(c.refWorkflowIDNodeIDs))
	for refWorkflowID := range c.refWorkflowIDNodeIDs {
		refWorkflowIDs = append(refWorkflowIDs, refWorkflowID)
	}
	// 1-2. 批量查workflow数据---引用的工作流详情，解析校验
	refWorkflowMap, err := db.GetWorkflowDetails(c.ctx, refWorkflowIDs, c.AppID)
	if err != nil {
		return err
	}
	for refWorkflowID, belongNodeIDs := range c.refWorkflowIDNodeIDs {
		log.InfoContextf(c.ctx, "checkRefWorkflows|refWorkflowID:%+v|belongNodeIDs:%+v",
			refWorkflowID, belongNodeIDs)
		// 2-1. 校验： 不能引用自己
		if c.Workflow.GetWorkflowID() == refWorkflowID {
			c.appendMultiNodesError(belongNodeIDs, "不能引用自身工作流")
			continue
		}
		// 2-2. 校验： 是否在db存在
		refWorkflow, ok := refWorkflowMap[refWorkflowID]
		if !ok {
			c.appendMultiNodesError(belongNodeIDs, "引用的工作流不存在")
			continue
		}
		if len(refWorkflow.DialogJsonEnable) == 0 {
			c.appendMultiNodesError(belongNodeIDs, "引用的工作流未调试通过")
			continue
		}
		wf, err := protoutil.JsonToWorkflow(refWorkflow.DialogJsonEnable)
		if err != nil {
			log.WarnContextf(c.ctx, "checkRefWorkflows|JsonToWorkflow|workflowJson:%s|err:%+v",
				refWorkflow.DialogJsonEnable, err)
			return err
		}

		log.InfoContextf(c.ctx, "checkRefWorkflows|refWorkflowID:%+v|ReleaseStatus:%s|WorkflowState:%s",
			refWorkflowID, refWorkflow.ReleaseStatus, refWorkflow.WorkflowState)
		// 被引用工作流是草稿状态不能保存；
		// 对应 wfTransformState 下的 draftState 和  publishedDraftState
		if refWorkflow.ReleaseStatus == entity.WorkflowReleaseStatusUnPublished &&
			(refWorkflow.WorkflowState == entity.WorkflowStateDraft ||
				refWorkflow.WorkflowState == entity.WorkflowStatePublishedDraft) {
			c.appendMultiNodesError(belongNodeIDs, "请先调试该流程")
			continue
		}
		//refWorkflow.ReleaseStatus
		// 2-3. 校验： 引用的层级是否超过配置文件的5层以及是否循环引用了
		workflowRef, ok1 := workflowDeepMap[refWorkflowID]
		workflowSelf, ok2 := workflowDeepMap[c.Workflow.GetWorkflowID()]
		if !ok1 || !ok2 {
			c.appendMultiNodesError(belongNodeIDs, "工作流或者引用的工作流不存在")
			continue
		} else {
			if workflowSelf.UpwardDeep+workflowRef.DownwardDeep >= workflowReferenceDepth {
				c.appendMultiNodesError(belongNodeIDs, fmt.Sprintf("工作流被引用+引用的工作流总层级深度已经超过%d层了",
					workflowReferenceDepth))
				continue
			}
			if db.IsCycle(workflowSelf, workflowRef) {
				c.appendMultiNodesError(belongNodeIDs, "工作流循环引用了")
				continue
			}
		}

		// 2-4. 校验： 提取引用的工作流的输入输出参数，与节点中的输入输出做对比
		//inputs, outputs := extractInputAndOutput(wf)
		inputs, _ := extractInputAndOutput(wf)
		nodeInputs := c.refWorkflowInputs[wf.GetWorkflowID()]
		if !compareInputParams(c.ctx, nodeInputs, inputs) {
			c.appendMultiNodesError(belongNodeIDs, "输入参数不匹配")
		}

		log.InfoContextf(c.ctx, "checkRefWorkflows|refWorkflowID:%+v|inputs:%+v|nodeInputs:%+v|",
			refWorkflowID, inputs, nodeInputs)

		// 2025-01-03 应该不需要校验输出结果
		//nodeOutputs := c.refWorkflowOutputs[wf.GetWorkflowID()]
		//if !compareOutputParams(nodeOutputs, outputs) {
		//	c.appendMultiNodesError(belongNodeIDs, "输出参数不匹配")
		//}
		//log.InfoContextf(c.ctx, "checkRefWorkflows|refWorkflowID:%+v|"+
		//	"inputs:%+v|nodeInputs:%+v|outputs:%+v|nodeOutputs:%+v",
		//	refWorkflowID, inputs, nodeInputs, outputs, nodeOutputs)
	}
	return nil
}

// 收集节点引用的模型
// 大模型相关节点(大模型节点, 大模型知识问答节点,标签提取节点,意图识别节点, 参数提取节点)中引用了
// v2.7.1&2.7.5 新增
// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800122056509
func (c *WfContext) collectWorkflowCustomModel(nodeID string, nodeType KEP_WF.NodeType, node *KEP_WF.WorkflowNode) {
	// 模型名称
	mn := ""
	switch node.GetNodeType() {
	case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
		mn = node.GetParameterExtractorNodeData().GetModelName()
	case KEP_WF.NodeType_LLM:
		mn = node.GetLLMNodeData().GetModelName()
	case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
		mn = node.GetLLMKnowledgeQANodeData().GetModelName()
	case KEP_WF.NodeType_TAG_EXTRACTOR:
		mn = node.GetTagExtractorNodeData().GetModelName()
	case KEP_WF.NodeType_INTENT_RECOGNITION:
		mn = node.GetIntentRecognitionNodeData().GetModelName()
	}
	if len(mn) > 0 {
		var customModel entity.WorkflowNodeCustomModel
		// 判断该模型是否是自定义模型
		resp, err := rpc.GetModelInfo(c.ctx, util.CorpID(c.ctx), mn)
		if err != nil {
			log.ErrorContextf(c.ctx, "collectCustomModelByLLM|WorkflowID:%s|CorpId:%d|"+
				"ModelName:%s|error:%+v|resp|%v", c.Workflow.WorkflowID, util.CorpID(c.ctx), mn, err, resp)
			c.appendNodeError(nodeID, fmt.Sprintf("modelName:%s|GetModelInfo,err:%+v", mn, err))
			return
		}
		// 是自定义的模型
		if resp != nil && resp.GetIsCustomModel() {
			customModel.NodeID = nodeID
			customModel.NodeType = nodeType.String()
			customModel.ModelName = mn
			key := fmt.Sprintf("%s:%s", mn, nodeID)
			if _, ok := c.customModelCache[key]; !ok {
				c.customModels = append(c.customModels, customModel)
				c.customModelCache[key] = &customModel
			}
		}
	}
}
