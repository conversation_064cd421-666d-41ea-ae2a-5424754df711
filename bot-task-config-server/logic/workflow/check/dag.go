/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// 找到DAG的入度（inDegree）为0的
func findSourceNodes(nodeMap map[string]*KEP_WF.WorkflowNode) []*KEP_WF.WorkflowNode {
	inDegree := make(map[string]int)
	for _, node := range nodeMap {
		nextNodeIDs := getNextIDs(node)
		for _, nextID := range nextNodeIDs {
			if _, exists := nodeMap[nextID]; exists {
				inDegree[nextID]++
			}
		}
	}

	var sourceNodes []*KEP_WF.WorkflowNode
	for _, node := range nodeMap {
		if inDegree[node.GetNodeID()] == 0 {
			sourceNodes = append(sourceNodes, node)
		}
	}
	return sourceNodes
}

// 主函数，用来检查图中是否有环，并返回环的路径
func checkCycle(nodes []*KEP_WF.WorkflowNode) (bool, []string) {
	visited := make(map[string]bool)
	currentPath := make(map[string]bool)

	nodeMap := make(map[string]*KEP_WF.WorkflowNode, len(nodes))
	for _, node := range nodes {
		nodeMap[node.GetNodeID()] = node
	}

	for _, node := range nodes {
		if visited[node.GetNodeID()] {
			continue
		}
		if hasCycle, cyclePath := detectCycle(nodeMap, node.GetNodeID(), visited, currentPath, []string{}); hasCycle {
			return true, cyclePath
		}
	}
	return false, nil
}

// 环检测的递归函数，并记录形成环的路径
func detectCycle(nodes map[string]*KEP_WF.WorkflowNode, nodeID string, visited map[string]bool,
	currentPath map[string]bool, path []string) (bool, []string) {
	// 如果当前节点已经在路径中，说明存在环，返回整个形成环的路径
	if currentPath[nodeID] {
		// 在环中时，我们从路径的第一个出现环的位置开始重新构建环的路径
		cycleStartIndex := -1
		for i, n := range path {
			if n == nodeID {
				cycleStartIndex = i
				break
			}
		}
		return true, path[cycleStartIndex:]
	}

	// 如果节点已经访问过并且没有形成环，不再检查
	if visited[nodeID] {
		return false, nil
	}

	// 标记当前节点为已访问，并加入当前路径
	visited[nodeID] = true
	currentPath[nodeID] = true
	path = append(path, nodeID)

	node := nodes[nodeID]

	// 递归检测每个子节点
	for _, nextID := range getNextIDs(node) {
		if hasCycle, cyclePath := detectCycle(nodes, nextID, visited, currentPath, path); hasCycle {
			return true, cyclePath
		}
	}

	// 递归结束后，将节点从当前路径中移除
	delete(currentPath, nodeID)

	return false, nil
}

// 判定是否某个节点是必经之路
// 如：参数提取节点不能在”并行“中出现，相当于"参数提取节点"需要是必经之路
func isNodeEssential(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string, targetNodeID string) bool {
	paths := getAllPaths(nodeMap, rootNodeID)

	// 不打印日志；
	//log.Debugf("isNodeEssential|----%s----- getAllPaths: %+v\n", targetNodeID, paths)
	//log.Debugf("isNodeEssential|----%s----- getAllPaths: %+v\n", targetNodeID)
	for _, path := range paths {
		// 检查当前路径是否经过 targetNodeID
		found := false
		for _, id := range path {
			if id == targetNodeID {
				found = true
				break
			}
		}

		// 如果找到了一个路径不经过 targetNodeID，返回 false
		if !found {
			return false
		}
	}
	return true
}

func findPaths(nodeMap map[string]*KEP_WF.WorkflowNode, nodeID string, path []string, result *[][]string) {
	path = append(path, nodeID)

	nextNodeIDs := getNextIDs(nodeMap[nodeID])
	if len(nextNodeIDs) == 0 {
		*result = append(*result, append([]string(nil), path...))
		return
	}

	for _, nextID := range nextNodeIDs {
		findPaths(nodeMap, nextID, path, result)
	}

	path = path[:len(path)-1]
}

func getAllPaths(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string) [][]string {
	var result [][]string
	//  [["1", "2", "3", "5"] ["1", "2", "4", "5"]]
	findPaths(nodeMap, rootNodeID, []string{}, &result)
	return result
}

func findPathsWithMemory(nodeMap map[string]*KEP_WF.WorkflowNode, specialNodeTypeSet map[KEP_WF.NodeType]struct{},
	nodeID string, path []string, result *[][]string, memo map[string][][]string) {
	// 已经找过的路径直接从记忆中获取
	if cachedPaths, found := memo[nodeID]; found {
		for _, cachedPath := range cachedPaths {
			*result = append(*result, append(path, cachedPath...))
		}
		return
	}

	path = append(path, nodeID)

	nextNodeIDs := getNextIDs(nodeMap[nodeID])
	if len(nextNodeIDs) == 0 {
		*result = append(*result, append([]string(nil), path...))
		memo[nodeID] = [][]string{append([]string(nil), nodeID)}
		return
	}

	var currentPaths [][]string
	for _, nextNodeID := range nextNodeIDs {
		if specialNodeTypeSet != nil {
			nodeType := nodeMap[nextNodeID].GetNodeType()
			if _, ok := specialNodeTypeSet[nodeType]; ok {
				// 在特殊的节点类型中停止
				*result = append(*result, append(path, nextNodeID))
				continue
			}
		}
		findPathsWithMemory(nodeMap, specialNodeTypeSet, nextNodeID, path, result, memo)
		for _, resPath := range *result {
			if resPath[0] == nodeID {
				currentPaths = append(currentPaths, resPath[len(path)-1:])
			}
		}
	}
	// 新的路径需要加入记忆
	memo[nodeID] = currentPaths

	path = path[:len(path)-1]
}

func getAllPathsWithMemory(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string,
	specialNodeTypeSet map[KEP_WF.NodeType]struct{}) [][]string {
	var result [][]string
	memo := make(map[string][][]string)
	findPathsWithMemory(nodeMap, specialNodeTypeSet, rootNodeID, []string{}, &result, memo)
	return result
}

//func dfs(nodes map[string]*KEP_WF.WorkflowNode, currentID, targetID string, path []string, depth int) [][]string {
//	// 检查递归深度
//	if depth > config.GetMainConfig().VerifyWorkflow.UiNodeTotal {
//		return [][]string{}
//	}
//
//	path = append(path, currentID)
//
//	if currentID == targetID {
//		return [][]string{path}
//	}
//
//	var results [][]string
//	nextNodeIDs := getNextIDs(nodes[currentID])
//	for _, nextID := range nextNodeIDs {
//		newPaths := dfs(nodes, nextID, targetID, path, depth+1)
//		results = append(results, newPaths...)
//	}
//
//	return results
//}

// getInvalidLeafNodes 返回所有不是 ANSWER 类型的叶子节点
func getInvalidLeafNodes(nodes map[string]*KEP_WF.WorkflowNode, startNodeID string) []*KEP_WF.WorkflowNode {
	visited := make(map[string]bool)
	// 存储所有不合法的叶子节点
	invalidLeafNodes := make([]*KEP_WF.WorkflowNode, 0)

	var dfsFunc func(nodeID string)
	dfsFunc = func(nodeID string) {
		if visited[nodeID] {
			return
		}
		visited[nodeID] = true

		currentNode := nodes[nodeID]
		nextNodeIDs := getNextIDs(currentNode)
		// 如果是叶子节点（没有后继节点）并且不是 END 类型
		if len(nextNodeIDs) == 0 && currentNode.GetNodeType() != KEP_WF.NodeType_END {
			//if len(nextNodeIDs) == 0 && (currentNode.GetNodeType() != KEP_WF.NodeType_END &&
			//	currentNode.GetNodeType() != KEP_WF.NodeType_ANSWER) {
			invalidLeafNodes = append(invalidLeafNodes, currentNode)
		}

		// 递归检查所有后继节点
		for _, nextNodeID := range nextNodeIDs {
			dfsFunc(nextNodeID)
		}
		//visited[nodeID] = false
	}

	// 从起始节点开始遍历
	dfsFunc(startNodeID)

	return invalidLeafNodes
}
