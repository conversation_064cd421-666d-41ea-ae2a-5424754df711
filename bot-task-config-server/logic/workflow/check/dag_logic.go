/*
 * 2024-11-2
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// 用于判断包含逻辑判断节点+参数提取节点的逻辑；
// 具体case结合文档 https://iwiki.woa.com/p/4012502313#%E5%B9%B6%E8%A1%8C%E6%A0%A1%E9%AA%8C 和单测查看
func isNodeEssentialWithLogicEvaluator(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string,
	targetNodeID string) bool {
	notPassingPaths := getNotPassingPaths(nodeMap, rootNodeID, targetNodeID)
	if len(notPassingPaths) == 0 {
		// 如果每条路都经过了，那就是必经之路，所以OK
		return true
	}
	for _, path := range notPassingPaths {
		for _, nodeID := range path {
			if nodeMap[nodeID].GetNodeType() != KEP_WF.NodeType_LOGIC_EVALUATOR {
				continue
			}
			done := checkAllGroup(nodeMap, nodeID, targetNodeID)
			if done {
				return true
			}
		}

	}
	return false
}

func checkAllGroup(nodeMap map[string]*KEP_WF.WorkflowNode, nodeID string, targetNodeID string) bool {
	found := false
	for _, group := range nodeMap[nodeID].GetLogicEvaluatorNodeData().GetGroup() {
		// 先找到 后继节点是否包含"参数提取节点"
		foundParameterExtractorNode := false
		for _, nextNodeIDs := range group.GetNextNodeIDs() {
			ff := findOne(nodeMap, nextNodeIDs, targetNodeID)
			if ff {
				foundParameterExtractorNode = true
				break
			}
		}
		// 如果包含，那就从这个逻辑判断节点的group开始 每条路都需要时必经之路；
		if foundParameterExtractorNode {
			yes := isAllNodeEssentialFromGroup(nodeMap, group, targetNodeID)
			//log.Debugf("checkAllGroup|isAllNodeEssentialFromGroup|group:%+v, yes:%t\n", group, yes)
			if !yes {
				return false
			}
			found = true
		}
	}

	return found
}

func isAllNodeEssentialFromGroup(nodeMap map[string]*KEP_WF.WorkflowNode, group *KEP_WF.LogicalGroup,
	targetNodeID string) bool {
	for _, nextNodeIDs := range group.GetNextNodeIDs() {
		if !isNodeEssential(nodeMap, nextNodeIDs, targetNodeID) {
			return false
		}
	}
	return true
}

// 判定是否某个节点是必经之路
// 如：参数提取节点不能在”并行“中出现，相当于"参数提取节点"需要是必经之路
func findOne(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string, targetNodeID string) bool {
	paths := getAllPaths(nodeMap, rootNodeID)

	//log.Debugf("findOne|----%s----- getAllPaths: %+v\n", targetNodeID, paths)
	for _, path := range paths {
		for _, id := range path {
			if id == targetNodeID {
				return true
			}
		}
	}
	return false
}

func getNotPassingPaths(nodeMap map[string]*KEP_WF.WorkflowNode, rootNodeID string, targetNodeID string) [][]string {
	paths := getAllPaths(nodeMap, rootNodeID)

	//log.Debugf("getNotPassingPaths|----%s----- getAllPaths: %+v\n", targetNodeID, paths)
	var notPassingPaths [][]string

	for _, path := range paths {
		// 检查当前路径是否经过 targetNodeID
		found := false
		for _, id := range path {
			if id == targetNodeID {
				found = true
			}
		}

		// 如果找到了一个路径不经过 targetNodeID，返回 false
		if !found {
			notPassingPaths = append(notPassingPaths, path)
		}

	}

	//for i, path := range notPassingPaths {
	//log.Debugf("getNotPassingPaths|notPassingPaths: [%d]%s\n", i, path)
	//}
	return notPassingPaths
}
