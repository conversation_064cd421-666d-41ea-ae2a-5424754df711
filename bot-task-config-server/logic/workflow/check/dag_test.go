/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
)

func Test_checkCycle(t *testing.T) {

	tests := []struct {
		name  string
		nodes []*KEP_WF.WorkflowNode
		want  bool
		want1 []string
	}{
		{
			name: "标准的",
			nodes: []*KEP_WF.WorkflowNode{
				{NodeID: "A", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"B"}},
				{NodeID: "B", NodeType: KEP_WF.NodeType_LLM, NextNodeIDs: []string{"C"}},
				{NodeID: "C", NodeType: KEP_WF.NodeType_END, NextNodeIDs: []string{}},
			},
			want: false,
		},
		{
			name: "有环",
			nodes: []*KEP_WF.WorkflowNode{
				{NodeID: "A", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"B"}},
				{NodeID: "B", NodeType: KEP_WF.NodeType_LLM, NextNodeIDs: []string{"C"}},
				{NodeID: "C", NodeType: KEP_WF.NodeType_END, NextNodeIDs: []string{"A"}},
			},
			want:  true,
			want1: []string{"A", "B", "C"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := checkCycle(tt.nodes)
			assert.Equalf(t, tt.want, got, "checkCycle(%v)", tt.nodes)
			assert.Equalf(t, tt.want1, got1, "checkCycle(%v)", tt.nodes)
		})
	}
}

func Test_isNodeEssential(t *testing.T) {

	//path := [][]string{{"1", "2", "3"}, {"1", "2", "3"}}
	//path := []string{"1", "2", "3"}
	//path = path[:len(path)-1]

	tests := []struct {
		name         string
		nodeMap      map[string]*KEP_WF.WorkflowNode
		workflowJSON string
		rootNodeID   string
		targetNodeID string
		expected     bool
	}{
		{
			name: "线性路径-节点必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2"}},
				"2": {NodeID: "2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"3"}},
				"3": {NodeID: "3", NextNodeIDs: []string{"4"}},
				"4": {NodeID: "4", NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "2",
			expected:     true,
		},
		{
			name: "有分支路径-节点必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2"}},
				"2": {NodeID: "2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"3", "4"}},
				"3": {NodeID: "3", NextNodeIDs: []string{"5"}},
				"4": {NodeID: "4", NextNodeIDs: []string{"5"}},
				"5": {NodeID: "5", NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "2",
			expected:     true,
		},
		{
			name: "有分支路径-节点非必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2", "3"}},
				"2": {NodeID: "2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"4"}},
				"3": {NodeID: "3", NextNodeIDs: []string{"4"}},
				"4": {NodeID: "4", NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "2",
			expected:     false,
		},
		{
			name: "复杂分支-菱形结构-中间节点非必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2", "3"}},
				"2": {NodeID: "2", NextNodeIDs: []string{"4"}},
				"3": {NodeID: "3", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"4"}},
				"4": {NodeID: "4", NextNodeIDs: []string{"5"}},
				"5": {NodeID: "5", NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "3",
			expected:     false,
		},
		{
			name: "多重分支-末端节点必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2", "3"}},
				"2": {NodeID: "2", NextNodeIDs: []string{"4"}},
				"3": {NodeID: "3", NextNodeIDs: []string{"4"}},
				"4": {NodeID: "4", NextNodeIDs: []string{"5"}},
				"5": {NodeID: "5", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "5",
			expected:     true,
		},
		{
			name: "单节点-自身必经",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "1",
			expected:     true,
		},
		{
			name: "复杂多分支-交叉路径",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"1": {NodeID: "1", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"2", "3"}},
				"2": {NodeID: "2", NextNodeIDs: []string{"4", "5"}},
				"3": {NodeID: "3", NextNodeIDs: []string{"5", "6"}},
				"4": {NodeID: "4", NextNodeIDs: []string{"7"}},
				"5": {NodeID: "5", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"7"}},
				"6": {NodeID: "6", NextNodeIDs: []string{"7"}},
				"7": {NodeID: "7", NextNodeIDs: []string{}},
			},
			rootNodeID:   "1",
			targetNodeID: "5",
			expected:     false,
		},

		//{
		//	name: "复杂多分支-交叉路径2",
		//	nodeMap: map[string]*KEP_WF.WorkflowNode{
		//		"开始":    {NodeID: "开始", NextNodeIDs: []string{"逻辑判断1"}},
		//		"逻辑判断1": {NodeID: "逻辑判断1", NextNodeIDs: []string{"a", "b"}},
		//		"a":     {NodeID: "a", NextNodeIDs: []string{"i"}},
		//		"i":     {NodeID: "i", NextNodeIDs: []string{"e"}},
		//		"e":     {NodeID: "e", NextNodeIDs: []string{"d", "g"}},
		//		"d":     {NodeID: "d", NextNodeIDs: []string{"答案2"}},
		//
		//		"g": {NodeID: "7", NextNodeIDs: []string{}},
		//	},
		//	rootNodeID:   "开始",
		//	targetNodeID: "i",
		//	expected:     false,
		//},

		{
			name: "逻辑判断节点-1",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"参数提取节点1"},
								},
								{
									NextNodeIDs: []string{"b"},
								},
							},
						}}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"答案2"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     true,
		},
		{
			name: "逻辑判断节点-2",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"参数提取节点1"},
								},
								{
									NextNodeIDs: []string{"参数提取节点2"},
								},
							},
						}}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"参数提取节点2": {NodeID: "参数提取节点2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案2"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID: "开始",
			//targetNodeID: "参数提取节点1",
			targetNodeID: "参数提取节点2",
			expected:     true,
		},
		{
			name: "逻辑判断节点-3",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"b"},
								},
								{
									NextNodeIDs: []string{"c"},
								},
							},
						}}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"d"}},
				"c":       {NodeID: "c", NextNodeIDs: []string{"d"}},
				"d":       {NodeID: "d", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     true,
		},
		{
			name: "逻辑判断节点-4",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"参数提取节点1", "参数提取节点2"},
								},
							},
						}}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"参数提取节点2": {NodeID: "参数提取节点2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案2"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     false,
		},
		{
			name: "逻辑判断节点-5",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"e", "b", "c"},
								},
							},
						}}},
				"e":       {NodeID: "e", NextNodeIDs: []string{"答案2"}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"d"}},
				"c":       {NodeID: "c", NextNodeIDs: []string{"d"}},
				"d":       {NodeID: "d", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     false,
		},
		{
			name: "逻辑判断节点-6",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"b", "c"},
								},
							},
						}}},

				"b": {NodeID: "b", NextNodeIDs: []string{"d"}},
				"c": {NodeID: "c", NextNodeIDs: []string{"d"}},
				"d": {NodeID: "d", NextNodeIDs: []string{"逻辑判断2"}},
				"逻辑判断2": {NodeID: "逻辑判断2", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"f", "e"},
								},
							},
						}}},
				"f":       {NodeID: "f", NextNodeIDs: []string{"答案2"}},
				"e":       {NodeID: "e", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     false,
		},
		{
			name: "逻辑判断节点-7-1",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"b", "c"},
								},
							},
						}}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"参数提取节点1"}},
				"c":       {NodeID: "c", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"逻辑判断2"}},
				"逻辑判断2": {NodeID: "逻辑判断2", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"f", "e"},
								},
							},
						}}},
				"f":       {NodeID: "f", NextNodeIDs: []string{"答案2"}},
				"e":       {NodeID: "e", NextNodeIDs: []string{"参数提取节点2"}},
				"参数提取节点2": {NodeID: "参数提取节点2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     true,
		},
		{
			name: "逻辑判断节点-7-2",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"b", "c"},
								},
							},
						}}},

				"b":       {NodeID: "b", NextNodeIDs: []string{"参数提取节点1"}},
				"c":       {NodeID: "c", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"逻辑判断2"}},
				"逻辑判断2": {NodeID: "逻辑判断2", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"f", "e"},
								},
							},
						}}},
				"f":       {NodeID: "f", NextNodeIDs: []string{"答案2"}},
				"e":       {NodeID: "e", NextNodeIDs: []string{"参数提取节点2"}},
				"参数提取节点2": {NodeID: "参数提取节点2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点2",
			expected:     false,
		},
		{
			name: "逻辑判断节点-8",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"b", "c"},
								},
								{
									NextNodeIDs: []string{"g"},
								},
							},
						}}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"参数提取节点1"}},
				"c":       {NodeID: "c", NextNodeIDs: []string{"参数提取节点1"}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"逻辑判断2"}},
				"逻辑判断2": {NodeID: "逻辑判断2", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"f", "e"},
								},
							},
						}}},
				"f":       {NodeID: "f", NextNodeIDs: []string{"答案2"}},
				"e":       {NodeID: "e", NextNodeIDs: []string{"参数提取节点2"}},
				"参数提取节点2": {NodeID: "参数提取节点2", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"g":       {NodeID: "g", NextNodeIDs: []string{"答案3"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
				"答案3":     {NodeID: "答案3", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     true,
		},
		{
			name: "逻辑判断节点-9",
			nodeMap: map[string]*KEP_WF.WorkflowNode{
				"开始": {NodeID: "开始", NodeType: KEP_WF.NodeType_START, NextNodeIDs: []string{"逻辑判断1"}},
				"逻辑判断1": {NodeID: "逻辑判断1", NodeType: KEP_WF.NodeType_LOGIC_EVALUATOR,
					NodeData: &KEP_WF.WorkflowNode_LogicEvaluatorNodeData{
						LogicEvaluatorNodeData: &KEP_WF.LogicEvaluatorNodeData{
							Group: []*KEP_WF.LogicalGroup{
								{
									NextNodeIDs: []string{"参数提取节点1"},
								},
								{
									NextNodeIDs: []string{"参数提取节点1", "b"},
								},
							},
						}}},
				"参数提取节点1": {NodeID: "参数提取节点1", NodeType: KEP_WF.NodeType_PARAMETER_EXTRACTOR, NextNodeIDs: []string{"答案1"}},
				"b":       {NodeID: "b", NextNodeIDs: []string{"答案2"}},
				"答案1":     {NodeID: "答案1", NextNodeIDs: []string{}},
				"答案2":     {NodeID: "答案2", NextNodeIDs: []string{}},
			},
			rootNodeID:   "开始",
			targetNodeID: "参数提取节点1",
			expected:     false,
		},
		{
			name:         "复杂的",
			workflowJSON: "{\"WorkflowID\":\"54488412-35be-45d0-b537-6ed90ff46dfc\",\"WorkflowName\":\"快递费用和到达时间是多少\",\"WorkflowDesc\":\"处理用户询问寄件服务的具体数字的价格或时效，当用户未指明具体运输方式或明确指定空运时，优先命中该意图，包括：\\n\\n具体金额询问（如：寄到日本要多少钱、寄某物去某国家有没有附加费）\\n具体天数询问（如：到英国几天能到）\\n重量对应的费用（如：5公斤要多少钱）\\n不同服务的价格比较（如：空运和陆运价格对比）\\n关税等附加费用金额\\n各国具体的时效天数\\n到某个国外地址要多久\\n某国的清关政策是什么\",\"Nodes\":[{\"NodeID\":\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305\",\"NodeName\":\"开始\",\"NodeDesc\":\"\",\"NodeType\":\"START\",\"StartNodeData\":{},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"707beb65-4905-ac8d-e531-6484434cb81a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":false,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":73,\\\"y\\\":422},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":86,\\\"height\\\":44}}\"},{\"NodeID\":\"a936ccfc-717d-83ae-d5ef-16769b28c63a\",\"NodeName\":\"条件判断1\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"758f8029-6ac4-b480-93c7-59e844434b00\",\"758f8029-6ac4-b480-93c7-59e844434b00\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"俄罗斯,白俄罗斯\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"伊朗\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d044038e-9930-ace6-010e-2807efaa9742\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"苏丹\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"阿富汗,布基纳法索,以色列,马里,苏丹,乌克兰,委内瑞拉,海地,黎巴嫩\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"中非共和国,刚果民主共和国,刚果金,刚果民主共和国（刚果金）,伊朗,北朝鲜\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"e5441802-d004-ce93-0250-71f1f4ef72df\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"伊拉克,叙利亚,利比亚,索马里,也门\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"俄罗斯,白俄罗斯\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"39623e7f-0ae6-903a-3c03-c443d45939c3\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"伊朗\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"e033b6da-bafc-d684-b5ab-a3c976180ef3\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"苏丹\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":2,\\\"id\\\":\\\"4c262f22-a559-cef0-35dd-e67db4696752\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"阿富汗,布基纳法索,以色列,马里,苏丹,乌克兰,委内瑞拉,海地,黎巴嫩\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":3,\\\"id\\\":\\\"c2beb572-2b07-921b-429b-150a61dadf12\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"中非共和国,刚果民主共和国,刚果金,刚果民主共和国（刚果金）,伊朗,北朝鲜\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":4,\\\"id\\\":\\\"4143afed-b830-3f35-c37b-d4b38060e05f\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"伊拉克,叙利亚,利比亚,索马里,也门\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":5,\\\"id\\\":\\\"8a5f61da-eb24-ea88-95ae-95b61be1c406\\\"},{\\\"content\\\":[],\\\"index\\\":6,\\\"id\\\":\\\"12efc38d-7e33-ba1b-3337-13f9ec63ca9a\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":583.5,\\\"y\\\":422},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":444},\\\"dragging\\\":false}\"},{\"NodeID\":\"758f8029-6ac4-b480-93c7-59e844434b00\",\"NodeName\":\"俄罗斯白俄罗斯\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，DHL快递发往俄罗斯和白俄罗斯的服务临时暂停，恢复时间待另行通知。谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，DHL快递发往俄罗斯和白俄罗斯的服务临时暂停，恢复时间待另行通知。谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":422},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\",\"NodeName\":\"伊朗\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"出于商业决定，DHL快递单元即日起暂停了伊朗进出口快件的服务，直至进一步通知。\\n\\n仅接收有外交签封的外交邮袋及OFAC授权许可的货物发往伊朗。若有疑问，请提前咨询您的账号销售经理。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>出于商业决定，DHL快递单元即日起暂停了伊朗进出口快件的服务，直至进一步通知。</p><p>仅接收有外交签封的外交邮袋及OFAC授权许可的货物发往伊朗。若有疑问，请提前咨询您的账号销售经理。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":586},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"d044038e-9930-ace6-010e-2807efaa9742\",\"NodeName\":\"苏丹\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"DHL快递自2023年8月28日（星期一）起，恢复苏丹港市的进出口包裹/应税货物服务。\\n\\n但苏丹港市的进出口业务操作仍然有限。\\n\\n受航班的执飞班期影响，苏丹港市的进出口文件/包裹/应税货物将有 5天转运延迟影响。\\n\\n重量/尺寸限制如下：\\n\\n60公斤/件\\n\\n300公斤/票\\n\\n100cmx50cmx50cm最大尺寸\\n\\n苏丹其他地区（苏丹港市除外）的进出口文件/包裹/应税货物仍然暂停，恢复时间待定，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>DHL快递自2023年8月28日（星期一）起，恢复苏丹港市的进出口包裹/应税货物服务。</p><p>但苏丹港市的进出口业务操作仍然有限。</p><p>受航班的执飞班期影响，苏丹港市的进出口文件/包裹/应税货物将有 5天转运延迟影响。</p><p>重量/尺寸限制如下：</p><p>60公斤/件</p><p>300公斤/票</p><p>100cmx50cmx50cm最大尺寸</p><p>苏丹其他地区（苏丹港市除外）的进出口文件/包裹/应税货物仍然暂停，恢复时间待定，谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":750},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\",\"NodeName\":\"查验目的地清关政策\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/serviceTips\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"countryCode\",\"ParamDesc\":\"收件国家\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态码\"},{\"Title\":\"message\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"成功失败中文\"},{\"Title\":\"data\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"清关提示\"},{\"Title\":\"success\",\"Type\":\"BOOL\",\"Required\":[],\"Properties\":[],\"Desc\":\"成功失败\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.message\\\",\\\"Output.data\\\",\\\"Output.success\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"message\\\",\\\"label\\\":\\\"message\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"success\\\",\\\"label\\\":\\\"success\\\",\\\"type\\\":\\\"BOOL\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":1806,\\\"y\\\":669},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"455823e2-fe3a-269f-8fc5-f25981f6c659\",\"NodeName\":\"条件判断2\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\",\"JsonPath\":\"Output.继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"高风险国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"69c4688c-4d94-f912-6727-d0f64001b157\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"7ec7e47e-06a2-e22d-8131-d44e736bf6a1\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1168.5,\\\"y\\\":894},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\",\"NodeName\":\"条件判断3\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\",\"JsonPath\":\"Output.继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"限运目的地国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"33d4c2dd-22d0-431a-63ef-21d739e37fb7\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"9238690a-c3c4-eb38-1f11-301104b66242\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1168.5,\\\"y\\\":1078},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\",\"NodeName\":\"条件判断4\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e5441802-d004-ce93-0250-71f1f4ef72df\",\"JsonPath\":\"Output.继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"高风险限运目的地国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"a74a74c1-df68-0e89-75f1-d39ac20c8da1\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"072954d8-0c43-2276-9699-f5545e9a87a9\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1168.5,\\\"y\\\":1384},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\",\"NodeName\":\"条件判断5\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"ee51ba3a-0bc4-800f-90ea-a996509c3061\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"查验目的地清关政策.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"55389964-faf8-2c4c-f9e1-68b162a675f2\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6813bc02-9303-3b10-6c41-8b5c0bbc956b\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":2132.5,\\\"y\\\":594},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"NodeName\":\"寄送物品名称\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"4a9603a3-3afe-40b9-b7f6-73a6ebee26f7\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容”参数进行追问时，请严格按照以下内容进行追问：“请问您需要寄送什么物品？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"424a5065-681c-8334-4db9-65042b1fb56b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：包裹内容\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"4a9603a3-3afe-40b9-b7f6-73a6ebee26f7\\\",\\\"label\\\":\\\"包裹内容\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3214,\\\"y\\\":696},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"7089a95f-1133-cf2e-c6e8-0a90295566be\",\"NodeName\":\"条件判断6\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"ee51ba3a-0bc4-800f-90ea-a996509c3061\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\",\"JsonPath\":\"Output.是否目的地清关\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"目的地有清关政策提前鉴定物品.Output.是否目的地清关\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"e9e984ac-3012-3360-535b-5c5a17bf1816\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"0d60e64e-c5f0-4ca7-ad17-1749ebd221a4\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":2785.5,\\\"y\\\":747},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"100124ae-d6a4-d31a-dba7-37643315de0a\",\"NodeName\":\"取消邮寄\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"好的，请问还有什么可以帮助您的？\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"好的，请问还有什么可以帮助您的？\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3106,\\\"y\\\":1098},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"424a5065-681c-8334-4db9-65042b1fb56b\",\"NodeName\":\"条件判断7\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"40304fb1-5cc6-7489-2cfe-22e5975aab81\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"充电宝,移动电源,电动滑板车,自动平衡车,电动自行车,独轮电动车,背夹电源,无线充电器,高压充填之瓶罐类物品,摩丝,大漆,清漆,油漆,杀虫的烟雾剂,安全气囊,干冰,电动轮椅,平衡车,电动车\\t,电动车锂电池,车载香薰,香薰,水银,无线充,硫磺\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"6b8b0c18-674f-95c6-7c55-9b8d766df108\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"灭火器,镁棒,喷漆,手喷漆,细菌,病毒,害虫,菌种,动物血清,发泡剂,制冷剂,尿,精子,指甲油,汽油,柴油,煤油,精油,杀虫剂,固体酒精,人体组织,微生物,生物制品,血液,石棉,喷雾剂,甲烷,仿造货币,有价证券,铜币,打火机,火机,罐装瓦斯,丁烷,喷腊,喷式芳香剂,喷式刮胡膏,喷式防蚊液,防晒喷雾,喷雾类产品,礼炮,气体,医用酒精,麋鹿角,菌株,病理切片,植物\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"adb1474c-b932-17d7-5196-c6175d857b80\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"肉蛋奶蔬菜水果类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"爱马仕,香奈儿,耐克等品牌物品\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"83cd2742-eaf9-67b3-d782-70aed99e85bb\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"电子烟及其配件（包括烟油、雾化设备等）\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"04e09238-e9e5-5907-155b-f3c7af2ca95f\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"火锅底料,燕窝,肥皂,酒水,电话手表,电子产品,糖果,消毒水,海参,水彩颜料,墨盒,眼影,隐形眼镜护理液,零食,冬虫夏草,沐浴露,唇膏,味精,种子,胶水,一体机电脑,电风扇,AirPods,固体胶,点读笔,树脂,颜料\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"金银珠宝等首饰类物品\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"充电宝,移动电源,电动滑板车,自动平衡车,电动自行车,独轮电动车,背夹电源,无线充电器,高压充填之瓶罐类物品,摩丝,大漆,清漆,油漆,杀虫的烟雾剂,安全气囊,干冰,电动轮椅,平衡车,电动车\\\\t,电动车锂电池,车载香薰,香薰,水银,无线充,硫磺\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"db3b4556-e8bb-ed99-8ee3-5f2777ac9ab4\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"灭火器,镁棒,喷漆,手喷漆,细菌,病毒,害虫,菌种,动物血清,发泡剂,制冷剂,尿,精子,指甲油,汽油,柴油,煤油,精油,杀虫剂,固体酒精,人体组织,微生物,生物制品,血液,石棉,喷雾剂,甲烷,仿造货币,有价证券,铜币,打火机,火机,罐装瓦斯,丁烷,喷腊,喷式芳香剂,喷式刮胡膏,喷式防蚊液,防晒喷雾,喷雾类产品,礼炮,气体,医用酒精,麋鹿角,菌株,病理切片,植物\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"1f2744ff-68e6-e1a7-9a9d-232cbce7b07c\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"肉蛋奶蔬菜水果类\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":2,\\\"id\\\":\\\"2870ca5d-e77a-5f94-a849-03bd6b78a35a\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"爱马仕,香奈儿,耐克等品牌物品\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":3,\\\"id\\\":\\\"6d86f585-e85f-c14b-b1fc-7b24ed5517dc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"电子烟及其配件（包括烟油、雾化设备等）\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":4,\\\"id\\\":\\\"c5576792-5331-a1b4-00e9-d3ebae1254cc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"火锅底料,燕窝,肥皂,酒水,电话手表,电子产品,糖果,消毒水,海参,水彩颜料,墨盒,眼影,隐形眼镜护理液,零食,冬虫夏草,沐浴露,唇膏,味精,种子,胶水,一体机电脑,电风扇,AirPods,固体胶,点读笔,树脂,颜料\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":5,\\\"id\\\":\\\"1f02e4d6-0983-f69f-d452-6c58aa8f5230\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"金银珠宝等首饰类物品\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":6,\\\"id\\\":\\\"0b7e1786-2bbd-aa57-32fd-39f3443a5592\\\"},{\\\"content\\\":[],\\\"index\\\":7,\\\"id\\\":\\\"029ed8a7-f1ce-0579-f0b6-f3cba32900a7\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":3506.5,\\\"y\\\":709},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":504},\\\"dragging\\\":false}\"},{\"NodeID\":\"40304fb1-5cc6-7489-2cfe-22e5975aab81\",\"NodeName\":\"危险品\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您咨询的物品是危险品，DHL无法承运危险品，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您好，您咨询的物品是危险品，DHL无法承运危险品，谢谢。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3839,\\\"y\\\":960},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"6b8b0c18-674f-95c6-7c55-9b8d766df108\",\"NodeName\":\"禁运品\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您咨询的物品属于禁运品。禁运品为DHL不可运输商品。抱歉无法承运，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，您咨询的物品属于禁运品。禁运品为DHL不可运输商品。抱歉无法承运，谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3849,\\\"y\\\":1079},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"adb1474c-b932-17d7-5196-c6175d857b80\",\"NodeName\":\"肉蛋奶蔬菜水果类\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"非常抱歉！目前此类物品DHL无法承运。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>非常抱歉！目前此类物品DHL无法承运。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3869,\\\"y\\\":1261},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\",\"NodeName\":\"爱马仕,香奈儿,耐克等品牌物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"29549f7f-270c-4675-9616-0d82ab2b26bc\",\"Required\":true}],\"UserConstraint\":\"当需要对“个人还是公司”参数进行追问时，请严格按照以下内容进行追问：“【请问您是寄个人件还是公司件】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"a5f4be67-f76f-685f-492d-300c315b0a82\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"29549f7f-270c-4675-9616-0d82ab2b26bc\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3801,\\\"y\\\":1644},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"83cd2742-eaf9-67b3-d782-70aed99e85bb\",\"NodeName\":\"电子烟及其配件\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，从保护消费者身体健康和网络运输安全的角度出发，中国DHL停止电子烟及其配件（包括烟油、雾化设备等）的出口运输。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，从保护消费者身体健康和网络运输安全的角度出发，中国DHL停止电子烟及其配件（包括烟油、雾化设备等）的出口运输。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3856,\\\"y\\\":1382},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"04e09238-e9e5-5907-155b-f3c7af2ca95f\",\"NodeName\":\"鉴定类物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"97422915-75cb-440f-a001-1b97ebd2160f\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"97422915-75cb-440f-a001-1b97ebd2160f\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3801,\\\"y\\\":1950},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\",\"NodeName\":\"金银珠宝等首饰类物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"47669085-457b-42f7-bde5-28a09e3c7442\",\"Required\":true}],\"UserConstraint\":\"当需要对“个人还是公司”参数进行追问时，请严格按照以下内容进行追问：“【请问您是寄个人件还是公司件】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"47669085-457b-42f7-bde5-28a09e3c7442\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3801,\\\"y\\\":2278},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"a5f4be67-f76f-685f-492d-300c315b0a82\",\"NodeName\":\"条件判断8\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"b4293b2b-dbd1-c512-6b81-f71507fc8800\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"爱马仕,香奈儿,耐克等品牌物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ae5d3d55-c9e7-d886-89cd-a75b5876990f\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"d0d51c58-4069-4e2b-7abb-fb3a11a5e343\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4093.5,\\\"y\\\":1622},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"b4293b2b-dbd1-c512-6b81-f71507fc8800\",\"NodeName\":\"品牌物品个人件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"701c416f-a88c-490b-9d38-0d62ec764390\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容是否继续”参数进行追问时，请严格按照以下内容进行追问：“个人寄送品牌类商品，要求符合个人自用范围内，不涉及知识产权/仿制/冒牌等，海关查验需要提供购买发票等凭证。（使用过的物品一般不需购买凭证，但可能要求提供情况说明，如具体用途等）【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否包裹内容继续\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"701c416f-a88c-490b-9d38-0d62ec764390\\\",\\\"label\\\":\\\"是否包裹内容继续\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":1622},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\",\"NodeName\":\"品牌物品公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"531acfbc-bf21-44b5-9efa-d1be6e4d9285\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容是否继续”参数进行追问时，请严格按照以下内容进行追问：“公司出口品牌类商品，随货资料需提供有效品牌授权书或者有效合法使用人截图，对于有品牌的商品可以建议登陆备案系统查询品牌是否有备案，网址如下：http://202.127.48.145:8888/zscq/search/jsp/vBrandSearchIndex.jsp，无论大小品牌均需要客户如实申报，建议在运单和发票品名描述栏添加品牌信息。\\n\\n有效的品牌授权书/合法权利人使用备案截图资料请上传到5idhl在线申报平台的其他说明或报关单证处。同时请您一并随货提供，避免查验无此资料造成不必要的清关延误。\\n\\n若无有效授权资料，则海关查验将有可能查扣公共仓产生海关仓租费用，若权利人核实侵权则有可能面临海关罚款。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"1b70c439-2dd9-158b-d3a9-d8402c865467\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否包裹内容继续\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"531acfbc-bf21-44b5-9efa-d1be6e4d9285\\\",\\\"label\\\":\\\"是否包裹内容继续\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":1806},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\",\"NodeName\":\"条件判断9\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"04e09238-e9e5-5907-155b-f3c7af2ca95f\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"91ca5313-b00e-a168-a5b3-429ddf587e85\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"鉴定类物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"5fd859b0-c77c-f286-dd23-4759e548da65\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"e3980b89-cdd8-3954-087f-9a6842e3ac20\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4093.5,\\\"y\\\":1950},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\",\"NodeName\":\"个人件\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"很抱歉，根据航空公司及安检要求，我司无法承运含有液体和粉末、颗粒、膏状固体，以及含电池类物品的个人快件。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>很抱歉，根据航空公司及安检要求，我司无法承运含有液体和粉末、颗粒、膏状固体，以及含电池类物品的个人快件。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":1950},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"91ca5313-b00e-a168-a5b3-429ddf587e85\",\"NodeName\":\"公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"bc6ad377-be44-44e1-afc0-d8de63c12230\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容是否继续”参数进行追问时，请严格按照以下内容进行追问：“很高兴为您解答关于鉴定类物品的出运要求。为了确保航空运输的安全，以及结合航空公司的安检规定，涉及到液体、粉末、颗粒状固体、带电池类物品、磁性物品时，需要您参考如下基本出运要求：\\n\\n1\\\\.  持有DHL运费月结账号\\n\\n2 . 化工品MSDS报告\\n\\n3\\\\.  持有非危险品鉴定报告（报告为非危险品）。\\n\\n由于出口城市和目的地国家/地区均有不同的规定，您可 _点我转接人工客服_  获取更多您所在地区鉴定物品的寄送要求，同时您也可以 [【点击这里】](https://www.5idhl.com/#/createExpress/ServiceTip) 自助参考目的地服务提示。感谢您的理解和支持。”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"6dfae16a-b8c9-3360-d2e9-72864669a781\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否包裹内容继续\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"bc6ad377-be44-44e1-afc0-d8de63c12230\\\",\\\"label\\\":\\\"是否包裹内容继续\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":2094},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\",\"NodeName\":\"条件判断10\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"47d22243-cd15-0278-852e-b66b5d4bd040\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"073b3f06-2c79-2c49-c647-56146a7aae7a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"金银珠宝等首饰类物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee16cd19-d1b7-3bea-4e9a-59399c1a11fc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"983b342a-fed3-b29f-b404-092fc278d62d\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4093.5,\\\"y\\\":2278},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144}}\"},{\"NodeID\":\"47d22243-cd15-0278-852e-b66b5d4bd040\",\"NodeName\":\"首饰个人件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"815e2132-e1f9-412a-ab88-ef1bc0bf241a\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容是否继续”参数进行追问时，请严格按照以下内容进行追问：“个人物品申报寄送黄金首饰，需提供购买小票，数量需要在合理自用范围，申报金额港澳台地区不超过800人民币，其他国家不能超过1000人民币。\\n\\n请了解：包裹类快件都有可能会产生关税均由收件人支付。目的地进口要求还请收件人提前与当地DHL确认。\\n\\n温馨提示：\\n\\n个人发送快件或者接收快件，请在公司名称字段对应填写个人名字，不要在收发件人公司名称处写公司信息，否则会影响到个人物品的清关。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"6a425ade-6961-4bdf-344c-5c7191313a81\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否包裹内容继续\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"815e2132-e1f9-412a-ab88-ef1bc0bf241a\\\",\\\"label\\\":\\\"是否包裹内容继续\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":2278},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"073b3f06-2c79-2c49-c647-56146a7aae7a\",\"NodeName\":\"首饰公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"d6b0be71-2a4a-43be-8844-b61e70c55e4c\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容是否继续”参数进行追问时，请严格按照以下内容进行追问：“黄金首饰出口申报涉证，无论金额大小均需要公司正式报关并提供黄金进出口许可证。\\n\\n温馨提示：\\n\\n1.K金首饰，只要合金里面的黄金含量百分比大于等于2%的即视为黄金首饰。\\n\\n2.铂金、银首饰、其他珠宝首饰按正常B/C/D类申报\\n\\n3.银制品除银原材料(未锻造、半制成或粉末状)外，其他银制品不涉证\\n\\n4.翡翠，玉石等原始石材或者半切割石材的进口，应咨询进口直接口岸的海关。此类石材要求提供MSDS等安全性说明文档，避免引发辐射等安全性疑虑。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否包裹内容继续\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"d6b0be71-2a4a-43be-8844-b61e70c55e4c\\\",\\\"label\\\":\\\"是否包裹内容继续\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4386,\\\"y\\\":2462},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\",\"NodeName\":\"条件判断11\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b4293b2b-dbd1-c512-6b81-f71507fc8800\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"品牌物品个人件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"4808aee6-50c1-afe3-3664-5cf8d82da9fa\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"595631ae-989e-1b51-c18e-f05a2e04db11\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4678.5,\\\"y\\\":1622},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"664ef870-087d-799f-b066-913f8018f980\",\"NodeName\":\"取消邮寄2\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"好的，请问还有什么可以帮助您的？\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>好的，请问还有什么可以帮助您的？</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":5249,\\\"y\\\":1659},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"1b70c439-2dd9-158b-d3a9-d8402c865467\",\"NodeName\":\"条件判断12\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"品牌物品公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"bac0d4f4-e712-495a-8e27-b82bc99dd963\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"28b9f74f-8131-1ba6-69df-7a452b8819eb\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4678.5,\\\"y\\\":1806},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6dfae16a-b8c9-3360-d2e9-72864669a781\",\"NodeName\":\"条件判断13\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"91ca5313-b00e-a168-a5b3-429ddf587e85\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"4eb6e973-3708-27bc-8758-5c78122bc72e\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"653b73fa-1b60-365a-a7d1-09ea36e06c32\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4678.5,\\\"y\\\":2094},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6a425ade-6961-4bdf-344c-5c7191313a81\",\"NodeName\":\"条件判断14\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"47d22243-cd15-0278-852e-b66b5d4bd040\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"首饰个人件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"a6491ad1-1fa5-00a2-186d-52a57063c3ba\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"af9345f4-b25d-63b5-2b33-cf7570ba9678\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4678.5,\\\"y\\\":2278},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\",\"NodeName\":\"条件判断15\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"073b3f06-2c79-2c49-c647-56146a7aae7a\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"首饰公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"889a0c88-ce27-8e28-bd50-faf9321ff090\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"b6fd6386-1899-0224-05e0-1cc916f6afc9\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4678.5,\\\"y\\\":2462},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"NodeName\":\"收件国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"ff4533f1-a3ba-458d-920b-63af10fc9362\",\"Required\":true}],\"UserConstraint\":\"当需要对“收件国家”参数进行追问时，请严格按照以下内容进行追问：“请问您的快件寄往哪个国家/地区？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"a936ccfc-717d-83ae-d5ef-16769b28c63a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件国家\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"ff4533f1-a3ba-458d-920b-63af10fc9362\\\",\\\"label\\\":\\\"收件国家\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":291,\\\"y\\\":422},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\",\"NodeName\":\"高风险国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"d5aa3c12-4592-4922-854c-2be14423c52a\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家属于高风险国家。\\n\\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"455823e2-fe3a-269f-8fc5-f25981f6c659\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"d5aa3c12-4592-4922-854c-2be14423c52a\\\",\\\"label\\\":\\\"继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":894},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\",\"NodeName\":\"限运目的地国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"d38106c0-2a3c-4b1c-afe3-8476f8f3add2\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家属于限运目的地国家。\\n\\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"d38106c0-2a3c-4b1c-afe3-8476f8f3add2\\\",\\\"label\\\":\\\"继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":1078},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"e5441802-d004-ce93-0250-71f1f4ef72df\",\"NodeName\":\"高风险限运目的地国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"e139d2ca-080a-4f59-b5a4-f8c5e1385f4f\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家既是高风险国家也是限运目的地国家。\\n\\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"e139d2ca-080a-4f59-b5a4-f8c5e1385f4f\\\",\\\"label\\\":\\\"继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":876,\\\"y\\\":1384},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\",\"NodeName\":\"目的地有清关政策提前鉴定物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"9b51864c-e05b-470c-9c11-2805f0855233\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否目的地清关”参数进行追问时，请严格按照以下内容进行追问：“目的地清关提示如下\\n\\n输出 查验目的地清关政策\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"7089a95f-1133-cf2e-c6e8-0a90295566be\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否目的地清关\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"9b51864c-e05b-470c-9c11-2805f0855233\\\",\\\"label\\\":\\\"是否目的地清关\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":2485,\\\"y\\\":586},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"NodeName\":\"转换收件国家到简码\",\"NodeDesc\":\"\",\"NodeType\":\"LLM\",\"LLMNodeData\":{\"ModelName\":\"hunyuan\",\"Temperature\":0,\"TopP\":0,\"MaxTokens\":0,\"Prompt\":\"请将国家名称{{countryName}}  转换为对应的ISO 3166-1二位字母代码；如将\\\"中国\\\"转换为\\\"CN\\\"; \\\"美国\\\"转换为\\\"US\\\"; \\\"德国\\\"转换为\\\"DE\\\"。请直接返回结果\"},\"Inputs\":[{\"Name\":\"countryName\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"Desc\":\"国家名称\"}],\"Outputs\":[],\"NextNodeIDs\":[\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"输出：Output.Content\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1489,\\\"y\\\":617},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\",\"NodeName\":\"确认是否有账号\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"83a9f60a-fe27-4e4e-8fde-f41a807b0581\",\"Required\":true}],\"UserConstraint\":\"请提取“有无账号判断”参数，如果用户没有提供，请严格按照如下内容追问用户：“请问您是否在我公司有本地账号？（本地账号指的是贵公司和DHL 签订的付费账号)”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"cd1de672-84db-7bd8-bb9a-9921368bd421\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：有无账号判断\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"83a9f60a-fe27-4e4e-8fde-f41a807b0581\\\",\\\"label\\\":\\\"有无账号判断\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":5506,\\\"y\\\":2139},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"cd1de672-84db-7bd8-bb9a-9921368bd421\",\"NodeName\":\"判断有无账号\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\",\"JsonPath\":\"Output.有无账号判断\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"有\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"a324750d-2902-729a-63e9-8efdfb81da3c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"确认是否有账号.Output.有无账号判断\\\",\\\"rightStr\\\":\\\"有\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"1f826908-9bc2-58f4-967c-f5da772d97d2\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"29704682-dba7-e4a8-d4c6-abd03e12e8a0\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":5806,\\\"y\\\":2139},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144}}\"},{\"NodeID\":\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\",\"NodeName\":\"收集付款账号\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"175fbeab-f07c-43c3-b425-c293db0b0259\",\"Required\":true}],\"UserConstraint\":\"请提取“付款账号”参数，如果用户没有提供，请严格按照如下内容追问客户：“请问您的本地账号是？（本地账号指的是贵公司和DHL 签订的预付费账号）”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"9ad6056b-6a21-bd05-ed40-bb019e94f692\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：付款账号\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"175fbeab-f07c-43c3-b425-c293db0b0259\\\",\\\"label\\\":\\\"付款账号\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":6140,\\\"y\\\":2065},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"NodeName\":\"收集发件邮编及城市\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"312962cf-8cb7-4beb-97dd-d3bc7f757a3e\",\"Required\":true},{\"RefParameterID\":\"a50edd8d-aff3-46cb-b775-e5ca6c92beff\",\"Required\":true}],\"UserConstraint\":\"如果需要追问用户，请严格按照如下内容提问：“请提供您的发件地城市和邮编”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"9fe87046-03ab-4d7f-8205-1114cdecf54a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：发件城市,发件邮编\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"312962cf-8cb7-4beb-97dd-d3bc7f757a3e\\\",\\\"label\\\":\\\"发件城市\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"a50edd8d-aff3-46cb-b775-e5ca6c92beff\\\",\\\"label\\\":\\\"发件邮编\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":7071,\\\"y\\\":2324},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"NodeName\":\"销售信息查询\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/admin/onlineChat/getSalesInfo\",\"Method\":\"GET\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[{\"ParamName\":\"account\",\"ParamDesc\":\"账号\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\",\"JsonPath\":\"Output.付款账号\"}},\"IsRequired\":false,\"SubParams\":[]}],\"Body\":[]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"响应状态\"},{\"Title\":\"data\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"销售信息\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"account\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":6440,\\\"y\\\":2065},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124}}\"},{\"NodeID\":\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\",\"NodeName\":\"判断是否有销售信息\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"a324750d-2902-729a-63e9-8efdfb81da3c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"销售信息查询.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"405149e9-f39b-4d68-10bd-c8256d03aeab\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"3db30737-e90d-29f8-8342-2b59b1e40888\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":6740,\\\"y\\\":2065},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144}}\"},{\"NodeID\":\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\",\"NodeName\":\"回复销售信息\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"JsonPath\":\"Output.data\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":7040,\\\"y\\\":2065},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86}}\"},{\"NodeID\":\"9fe87046-03ab-4d7f-8205-1114cdecf54a\",\"NodeName\":\"检查发件邮编城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"postalCode\",\"ParamDesc\":\"发件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"city\",\"ParamDesc\":\"发件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityStartPostal\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"发件邮编\"},{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"发件城市\"}],\"Desc\":\"数据\"},{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"c69673a4-79a9-f533-9595-25d0faa85017\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"postalCode\\\",\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.data\\\",\\\"Output.data.cityStartPostal\\\",\\\"Output.data.cityName\\\",\\\"Output.status\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityStartPostal\\\",\\\"label\\\":\\\"cityStartPostal\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]},{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":7510,\\\"y\\\":2336},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"c69673a4-79a9-f533-9595-25d0faa85017\",\"NodeName\":\"条件判断18\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"bd4d7981-8565-0fb9-f523-4949c77f7879\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9fe87046-03ab-4d7f-8205-1114cdecf54a\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"03e6e522-486f-00a5-949d-eb4d7d83ce41\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查发件邮编城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":7816,\\\"y\\\":2335},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"03e6e522-486f-00a5-949d-eb4d7d83ce41\",\"NodeName\":\"发件地城市邮编错误\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您输入的国内邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您输入的国内邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":8130,\\\"y\\\":2529},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"bd4d7981-8565-0fb9-f523-4949c77f7879\",\"NodeName\":\"检查国家或地区是否有邮编\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkCountryHasPostalCode\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"countryCode\",\"ParamDesc\":\"国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"58df2fbc-077b-e64e-8d73-069adfea0dbe\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":8130,\\\"y\\\":2370},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"f207c545-794d-2111-e688-5962e6adfdf6\",\"NodeName\":\"条件判断19\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bd4d7981-8565-0fb9-f523-4949c77f7879\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"24d23dfa-06b9-0545-0b68-f2fe762f4086\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查国家或地区是否有邮编.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":8738,\\\"y\\\":2372},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\",\"NodeName\":\"检查收件邮编城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"GET\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[{\"ParamName\":\"postalCode\",\"ParamDesc\":\"收件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"city\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":false,\"SubParams\":[]}],\"Body\":[]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"},{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityStartPostal\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"},{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"},{\"Title\":\"countryCode\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"\"}],\"Desc\":\"\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"e5dacf97-8eda-141b-fce8-9425de15fd38\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"postalCode\\\",\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\",\\\"Output.data.cityStartPostal\\\",\\\"Output.data.cityName\\\",\\\"Output.data.countryCode\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityStartPostal\\\",\\\"label\\\":\\\"cityStartPostal\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"countryCode\\\",\\\"label\\\":\\\"countryCode\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]}]},\\\"position\\\":{\\\"x\\\":9470,\\\"y\\\":2279},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"NodeName\":\"收集收件邮编\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"69d5144f-91fc-4f82-88aa-9ed1ba1377ae\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件邮编\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"69d5144f-91fc-4f82-88aa-9ed1ba1377ae\\\",\\\"label\\\":\\\"收件邮编\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":9097,\\\"y\\\":2287},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"NodeName\":\"收集收件城市\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"19ce185e-a96c-4e69-8e69-b01950d40869\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"f207c545-794d-2111-e688-5962e6adfdf6\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件城市\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"19ce185e-a96c-4e69-8e69-b01950d40869\\\",\\\"label\\\":\\\"收件城市\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":8423,\\\"y\\\":2383},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"24d23dfa-06b9-0545-0b68-f2fe762f4086\",\"NodeName\":\"检查收件城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"city\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"收集实体\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件城市\"},{\"Title\":\"ccode\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件国家编码\"}],\"Desc\":\"数据\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"7470177f-16fb-15c2-517a-01cc324abf7b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\",\\\"Output.data.cityName\\\",\\\"Output.data.ccode\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"ccode\\\",\\\"label\\\":\\\"ccode\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]}]},\\\"position\\\":{\\\"x\\\":9348,\\\"y\\\":2534},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"e5dacf97-8eda-141b-fce8-9425de15fd38\",\"NodeName\":\"条件判断20\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"50a98cd2-65c9-156d-e805-24fb442254ed\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查收件邮编城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":9821,\\\"y\\\":2295},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"7470177f-16fb-15c2-517a-01cc324abf7b\",\"NodeName\":\"条件判断21\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"50a98cd2-65c9-156d-e805-24fb442254ed\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"24d23dfa-06b9-0545-0b68-f2fe762f4086\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查收件城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":9867,\\\"y\\\":2568},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\",\"NodeName\":\"检查邮编城市不通过\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您输入的国外邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您输入的国外邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10845,\\\"y\\\":2676},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"NodeName\":\"收集产品类型\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"928c14c2-6678-418a-afb0-0d96dafaf2c0\",\"Required\":true}],\"UserConstraint\":\"如果需要追问用户，请严格按照如下内容追问：“您好，我想了解一下您所查询的产品类型是文件类还是包裹类？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：产品类型\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"928c14c2-6678-418a-afb0-0d96dafaf2c0\\\",\\\"label\\\":\\\"产品类型\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":10848,\\\"y\\\":2339},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\",\"NodeName\":\"条件判断22\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"915297e6-14a1-d4ae-b857-5230424c8baf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"JsonPath\":\"Output.产品类型\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"包裹类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"f6e9c428-6316-facc-ad68-ba95b7705b01\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"JsonPath\":\"Output.产品类型\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"文件类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"收集产品类型.Output.产品类型\\\",\\\"rightStr\\\":\\\"包裹类\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"收集产品类型.Output.产品类型\\\",\\\"rightStr\\\":\\\"文件类\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"006e9c19-9354-485c-05ea-3871e0e48192\\\"},{\\\"content\\\":[],\\\"index\\\":2,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":11148,\\\"y\\\":2339},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":204}}\"},{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"NodeName\":\"运费时效包裹类\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/freightTime\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"fromCountryCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromPostalCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromCity\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCountryCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toPostalCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"toCity\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"productType\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"WPX\"]}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"declaredValue\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.申报价值\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"goodsWeight\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹重量\"}},\"IsRequired\":false,\"SubParams\":[]},{\"ParamName\":\"goodsLength\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹长\"}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"freightTime\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"运费时效\"},{\"Title\":\"goodsServiceTips\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"物品提示\"},{\"Title\":\"gems\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"重量提示\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"a639aa17-cc38-183c-fce9-dcfa4b42af29\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"fromCountryCode\\\",\\\"fromPostalCode\\\",\\\"fromCity\\\",\\\"toCountryCode\\\",\\\"toPostalCode\\\",\\\"toCity\\\",\\\"productType\\\",\\\"declaredValue\\\",\\\"goodsWeight\\\",\\\"goodsLength\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.freightTime\\\",\\\"Output.goodsServiceTips\\\",\\\"Output.gems\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"freightTime\\\",\\\"label\\\":\\\"freightTime\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"goodsServiceTips\\\",\\\"label\\\":\\\"goodsServiceTips\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"gems\\\",\\\"label\\\":\\\"gems\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":11775,\\\"y\\\":2204},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"NodeName\":\"收集包裹信息\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"0ae07523-f7c6-4710-98d2-55f3318e4f0a\",\"Required\":true},{\"RefParameterID\":\"39a5c37b-e894-4362-8877-ec3a700408f8\",\"Required\":true},{\"RefParameterID\":\"94f24b3c-bfb7-4d53-9577-12d1227ca62d\",\"Required\":true},{\"RefParameterID\":\"973cd1d1-6bc6-449f-b2a7-00ceb7cc1d3f\",\"Required\":true},{\"RefParameterID\":\"4a91c22a-1638-41ff-bf2a-08adcb4e226b\",\"Required\":true},{\"RefParameterID\":\"b91d72ed-a10c-475a-bdb7-c9133ed02d41\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：包裹重量,包裹长,包裹宽,包裹高,申报价值,运费时效时间\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"0ae07523-f7c6-4710-98d2-55f3318e4f0a\\\",\\\"label\\\":\\\"包裹重量\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"39a5c37b-e894-4362-8877-ec3a700408f8\\\",\\\"label\\\":\\\"包裹长\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"94f24b3c-bfb7-4d53-9577-12d1227ca62d\\\",\\\"label\\\":\\\"包裹宽\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"973cd1d1-6bc6-449f-b2a7-00ceb7cc1d3f\\\",\\\"label\\\":\\\"包裹高\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"4a91c22a-1638-41ff-bf2a-08adcb4e226b\\\",\\\"label\\\":\\\"申报价值\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"b91d72ed-a10c-475a-bdb7-c9133ed02d41\\\",\\\"label\\\":\\\"运费时效时间\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":11461,\\\"y\\\":2322},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"971e080a-4c04-0ca9-993e-89e0b2449bbf\",\"NodeName\":\"兜底回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您的产品类型目前没有相关信息，请重新提问\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您好，您的产品类型目前没有相关信息，请重新提问\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":12444,\\\"y\\\":2691},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"NodeName\":\"参数提取24\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"0b15d758-a1e3-4b4d-8a01-d77f4e6d7fc2\",\"Required\":true},{\"RefParameterID\":\"0dec91f2-e4ad-43d5-af17-071fa9849e36\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"b953174b-6407-27e2-9a5d-3c73aa507b78\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：文件重量,运费时效时间\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"0b15d758-a1e3-4b4d-8a01-d77f4e6d7fc2\\\",\\\"label\\\":\\\"文件重量\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"0dec91f2-e4ad-43d5-af17-071fa9849e36\\\",\\\"label\\\":\\\"运费时效时间\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":11458,\\\"y\\\":2454},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"NodeName\":\"运费时效文件类\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/freightTime\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"fromCountryCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromPostalCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromCity\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCountryCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toPostalCode\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCity\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"productType\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"DOX\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fileWeight\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"JsonPath\":\"Output.文件重量\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"sendTime\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"JsonPath\":\"Output.运费时效时间\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"freightTime\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"运费时效\"},{\"Title\":\"goodsServiceTips\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"物品提示\"},{\"Title\":\"gems\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"重量提示\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"fromCountryCode\\\",\\\"fromPostalCode\\\",\\\"fromCity\\\",\\\"toCountryCode\\\",\\\"toPostalCode\\\",\\\"toCity\\\",\\\"productType\\\",\\\"fileWeight\\\",\\\"sendTime\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.freightTime\\\",\\\"Output.goodsServiceTips\\\",\\\"Output.gems\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"freightTime\\\",\\\"label\\\":\\\"freightTime\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"goodsServiceTips\\\",\\\"label\\\":\\\"goodsServiceTips\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"gems\\\",\\\"label\\\":\\\"gems\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":11770,\\\"y\\\":2480},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"a639aa17-cc38-183c-fce9-dcfa4b42af29\",\"NodeName\":\"条件判断23\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"2681de46-a4fb-3a01-1108-7bdbba0364ac\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"运费时效包裹类.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":12206,\\\"y\\\":2274},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"2681de46-a4fb-3a01-1108-7bdbba0364ac\",\"NodeName\":\"运费时效包裹类回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"JsonPath\":\"Output.freightTime\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":12506,\\\"y\\\":2274},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86}}\"},{\"NodeID\":\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\",\"NodeName\":\"条件判断24\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"运费时效文件类.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":12070,\\\"y\\\":2480},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144}}\"},{\"NodeID\":\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\",\"NodeName\":\"运费时效文件类回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"JsonPath\":\"Output.freightTime\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":12370,\\\"y\\\":2480},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86}}\"}],\"Edge\":\"[{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.39623e7f-0ae6-903a-3c03-c443d45939c3-source\\\",\\\"target\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.0-source-758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.e033b6da-bafc-d684-b5ab-a3c976180ef3-source\\\",\\\"target\\\":\\\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.1-source-b046da4d-ce8c-c8bc-2448-6cc8e84167d1\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.4c262f22-a559-cef0-35dd-e67db4696752-source\\\",\\\"target\\\":\\\"d044038e-9930-ace6-010e-2807efaa9742\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.2-source-d044038e-9930-ace6-010e-2807efaa9742\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"sourceHandle\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca-source\\\",\\\"target\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f3d3235f-44a9-8d0a-1804-5cbce670eacaf3d3235f-44a9-8d0a-1804-5cbce670eaca-source-6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"sourceHandle\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca.6813bc02-9303-3b10-6c41-8b5c0bbc956b-source\\\",\\\"target\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a5ae515-5aa8-b334-8bd5-15069566c2ca6a5ae515-5aa8-b334-8bd5-15069566c2ca.1-source-ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"sourceHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be.e9e984ac-3012-3360-535b-5c5a17bf1816-source\\\",\\\"target\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"targetHandle\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be.0-source-ee51ba3a-0bc4-800f-90ea-a996509c3061ee51ba3a-0bc4-800f-90ea-a996509c3061-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"sourceHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be.0d60e64e-c5f0-4ca7-ad17-1749ebd221a4-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be.1-source-100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"sourceHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659.7ec7e47e-06a2-e22d-8131-d44e736bf6a1-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"sourceHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.9238690a-c3c4-eb38-1f11-301104b66242-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"sourceHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.072954d8-0c43-2276-9699-f5545e9a87a9-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"sourceHandle\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061-source\\\",\\\"target\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ee51ba3a-0bc4-800f-90ea-a996509c3061ee51ba3a-0bc4-800f-90ea-a996509c3061-source-424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.db3b4556-e8bb-ed99-8ee3-5f2777ac9ab4-source\\\",\\\"target\\\":\\\"40304fb1-5cc6-7489-2cfe-22e5975aab81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.0-source-40304fb1-5cc6-7489-2cfe-22e5975aab81\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.1f2744ff-68e6-e1a7-9a9d-232cbce7b07c-source\\\",\\\"target\\\":\\\"6b8b0c18-674f-95c6-7c55-9b8d766df108\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.1-source-6b8b0c18-674f-95c6-7c55-9b8d766df108\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.2870ca5d-e77a-5f94-a849-03bd6b78a35a-source\\\",\\\"target\\\":\\\"adb1474c-b932-17d7-5196-c6175d857b80\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.2-source-adb1474c-b932-17d7-5196-c6175d857b80\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.6d86f585-e85f-c14b-b1fc-7b24ed5517dc-source\\\",\\\"target\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.3-source-55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.c5576792-5331-a1b4-00e9-d3ebae1254cc-source\\\",\\\"target\\\":\\\"83cd2742-eaf9-67b3-d782-70aed99e85bb\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.4-source-83cd2742-eaf9-67b3-d782-70aed99e85bb\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.1f02e4d6-0983-f69f-d452-6c58aa8f5230-source\\\",\\\"target\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.5-source-04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.0b7e1786-2bbd-aa57-32fd-39f3443a5592-source\\\",\\\"target\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.6-source-d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"sourceHandle\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee-source\\\",\\\"target\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__55c590aa-8c75-5175-a7c0-8b7ce26442ee55c590aa-8c75-5175-a7c0-8b7ce26442ee-source-a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"sourceHandle\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82.ae5d3d55-c9e7-d886-89cd-a75b5876990f-source\\\",\\\"target\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a5f4be67-f76f-685f-492d-300c315b0a82a5f4be67-f76f-685f-492d-300c315b0a82.0-source-b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"sourceHandle\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82.d0d51c58-4069-4e2b-7abb-fb3a11a5e343-source\\\",\\\"target\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a5f4be67-f76f-685f-492d-300c315b0a82a5f4be67-f76f-685f-492d-300c315b0a82.1-source-d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"sourceHandle\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f-source\\\",\\\"target\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__04e09238-e9e5-5907-155b-f3c7af2ca95f04e09238-e9e5-5907-155b-f3c7af2ca95f-source-0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"sourceHandle\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.5fd859b0-c77c-f286-dd23-4759e548da65-source\\\",\\\"target\\\":\\\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b20fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.0-source-9c915fe9-1707-a452-4b0f-583eeb8af4c8\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"sourceHandle\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.e3980b89-cdd8-3954-087f-9a6842e3ac20-source\\\",\\\"target\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b20fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.1-source-91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"sourceHandle\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282-source\\\",\\\"target\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__d616bc15-1ebe-1fe1-20d7-12139a7c3282d616bc15-1ebe-1fe1-20d7-12139a7c3282-source-8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"sourceHandle\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b.ee16cd19-d1b7-3bea-4e9a-59399c1a11fc-source\\\",\\\"target\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__8dbd4ed5-311b-30cb-96e2-7c020b70b72b8dbd4ed5-311b-30cb-96e2-7c020b70b72b.0-source-47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"sourceHandle\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b.983b342a-fed3-b29f-b404-092fc278d62d-source\\\",\\\"target\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__8dbd4ed5-311b-30cb-96e2-7c020b70b72b8dbd4ed5-311b-30cb-96e2-7c020b70b72b.1-source-073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"sourceHandle\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800-source\\\",\\\"target\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__b4293b2b-dbd1-c512-6b81-f71507fc8800b4293b2b-dbd1-c512-6b81-f71507fc8800-source-0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"sourceHandle\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af.4808aee6-50c1-afe3-3664-5cf8d82da9fa-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0d34034b-4d38-db6a-40b4-2c40fef6e7af0d34034b-4d38-db6a-40b4-2c40fef6e7af.0-source-664ef870-087d-799f-b066-913f8018f980\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"sourceHandle\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8-source\\\",\\\"target\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__d9943e0b-9495-ade7-7191-9c0fbcb904f8d9943e0b-9495-ade7-7191-9c0fbcb904f8-source-1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"sourceHandle\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467.bac0d4f4-e712-495a-8e27-b82bc99dd963-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1b70c439-2dd9-158b-d3a9-d8402c8654671b70c439-2dd9-158b-d3a9-d8402c865467.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"sourceHandle\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85-source\\\",\\\"target\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__91ca5313-b00e-a168-a5b3-429ddf587e8591ca5313-b00e-a168-a5b3-429ddf587e85-source-6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"sourceHandle\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781.4eb6e973-3708-27bc-8758-5c78122bc72e-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6dfae16a-b8c9-3360-d2e9-72864669a7816dfae16a-b8c9-3360-d2e9-72864669a781.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"sourceHandle\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040-source\\\",\\\"target\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__47d22243-cd15-0278-852e-b66b5d4bd04047d22243-cd15-0278-852e-b66b5d4bd040-source-6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"sourceHandle\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81.a6491ad1-1fa5-00a2-186d-52a57063c3ba-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a425ade-6961-4bdf-344c-5c7191313a816a425ade-6961-4bdf-344c-5c7191313a81.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"sourceHandle\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a-source\\\",\\\"target\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__073b3f06-2c79-2c49-c647-56146a7aae7a073b3f06-2c79-2c49-c647-56146a7aae7a-source-facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"sourceHandle\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39.889a0c88-ce27-8e28-bd50-faf9321ff090-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__facd6bee-c5e7-b216-1d5c-20d0abf62c39facd6bee-c5e7-b216-1d5c-20d0abf62c39.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305\\\",\\\"sourceHandle\\\":\\\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305-source\\\",\\\"target\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c46ec3f9-8b9b-1301-0523-17e1dbdb8305c46ec3f9-8b9b-1301-0523-17e1dbdb8305-source-707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"sourceHandle\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a-source\\\",\\\"target\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"targetHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__707beb65-4905-ac8d-e531-6484434cb81a707beb65-4905-ac8d-e531-6484434cb81a-source-a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.c2beb572-2b07-921b-429b-150a61dadf12-source\\\",\\\"target\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.3-source-4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"sourceHandle\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72-source\\\",\\\"target\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"targetHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__4c09460c-761e-f2fe-4d11-d25ee56cfb724c09460c-761e-f2fe-4d11-d25ee56cfb72-source-455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.4143afed-b830-3f35-c37b-d4b38060e05f-source\\\",\\\"target\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.4-source-7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"sourceHandle\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb-source\\\",\\\"target\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"targetHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7eb041a5-4d1a-576a-a2ce-98929bd51ceb7eb041a5-4d1a-576a-a2ce-98929bd51ceb-source-ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.8a5f61da-eb24-ea88-95ae-95b61be1c406-source\\\",\\\"target\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.5-source-e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"sourceHandle\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df-source\\\",\\\"target\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"targetHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5441802-d004-ce93-0250-71f1f4ef72dfe5441802-d004-ce93-0250-71f1f4ef72df-source-1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\\\",\\\"sourceHandle\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-source\\\",\\\"target\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"targetHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__725e1eb4-4eee-ed7d-9b26-3a4c018bbfae725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-source-7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"sourceHandle\\\":\\\"\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"target\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0ebc2e94-a630-5591-0ea2-430f3097fe30-f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"sourceHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.33d4c2dd-22d0-431a-63ef-21d739e37fb7-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"sourceHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659.69c4688c-4d94-f912-6727-d0f64001b157-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.12efc38d-7e33-ba1b-3337-13f9ec63ca9a-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.6-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.39623e7f-0ae6-903a-3c03-c443d45939c3-source\\\",\\\"target\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"targetHandle\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.0-source-758f8029-6ac4-b480-93c7-59e844434b00758f8029-6ac4-b480-93c7-59e844434b00-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"sourceHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.a74a74c1-df68-0e89-75f1-d39ac20c8da1-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"sourceHandle\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca.55389964-faf8-2c4c-f9e1-68b162a675f2-source\\\",\\\"target\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\\\",\\\"targetHandle\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a5ae515-5aa8-b334-8bd5-15069566c2ca6a5ae515-5aa8-b334-8bd5-15069566c2ca.0-source-725e1eb4-4eee-ed7d-9b26-3a4c018bbfae725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"sourceHandle\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af.595631ae-989e-1b51-c18e-f05a2e04db11-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0d34034b-4d38-db6a-40b4-2c40fef6e7af0d34034b-4d38-db6a-40b4-2c40fef6e7af.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"sourceHandle\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467.28b9f74f-8131-1ba6-69df-7a452b8819eb-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1b70c439-2dd9-158b-d3a9-d8402c8654671b70c439-2dd9-158b-d3a9-d8402c865467.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"sourceHandle\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781.653b73fa-1b60-365a-a7d1-09ea36e06c32-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6dfae16a-b8c9-3360-d2e9-72864669a7816dfae16a-b8c9-3360-d2e9-72864669a781.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"sourceHandle\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81.af9345f4-b25d-63b5-2b33-cf7570ba9678-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a425ade-6961-4bdf-344c-5c7191313a816a425ade-6961-4bdf-344c-5c7191313a81.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"sourceHandle\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39.b6fd6386-1899-0224-05e0-1cc916f6afc9-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__facd6bee-c5e7-b216-1d5c-20d0abf62c39facd6bee-c5e7-b216-1d5c-20d0abf62c39.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.029ed8a7-f1ce-0579-f0b6-f3cba32900a7-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.7-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"sourceHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-source\\\",\\\"target\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-source-cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"sourceHandle\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421.1f826908-9bc2-58f4-967c-f5da772d97d2-source\\\",\\\"target\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__cd1de672-84db-7bd8-bb9a-9921368bd421cd1de672-84db-7bd8-bb9a-9921368bd421.0-source-9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"sourceHandle\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421.29704682-dba7-e4a8-d4c6-abd03e12e8a0-source\\\",\\\"target\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__cd1de672-84db-7bd8-bb9a-9921368bd421cd1de672-84db-7bd8-bb9a-9921368bd421.1-source-a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"sourceHandle\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3-source\\\",\\\"target\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9a7fd390-c81c-7bca-cd12-7b03606f14d39a7fd390-c81c-7bca-cd12-7b03606f14d3-source-9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"sourceHandle\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692-source\\\",\\\"target\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9ad6056b-6a21-bd05-ed40-bb019e94f6929ad6056b-6a21-bd05-ed40-bb019e94f692-source-5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"sourceHandle\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226.405149e9-f39b-4d68-10bd-c8256d03aeab-source\\\",\\\"target\\\":\\\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__5285d6b8-cc75-aee0-c3d9-3beb248e62265285d6b8-cc75-aee0-c3d9-3beb248e6226.0-source-6fb4f524-b70d-a837-9c45-0999dc9cb3cf\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"sourceHandle\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226.3db30737-e90d-29f8-8342-2b59b1e40888-source\\\",\\\"target\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"targetHandle\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__5285d6b8-cc75-aee0-c3d9-3beb248e62265285d6b8-cc75-aee0-c3d9-3beb248e6226.1-source-a324750d-2902-729a-63e9-8efdfb81da3ca324750d-2902-729a-63e9-8efdfb81da3c-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"sourceHandle\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c-source\\\",\\\"target\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a\\\",\\\"targetHandle\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a324750d-2902-729a-63e9-8efdfb81da3ca324750d-2902-729a-63e9-8efdfb81da3c-source-9fe87046-03ab-4d7f-8205-1114cdecf54a9fe87046-03ab-4d7f-8205-1114cdecf54a-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a\\\",\\\"sourceHandle\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a-source\\\",\\\"target\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9fe87046-03ab-4d7f-8205-1114cdecf54a9fe87046-03ab-4d7f-8205-1114cdecf54a-source-c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"sourceHandle\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"03e6e522-486f-00a5-949d-eb4d7d83ce41\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c69673a4-79a9-f533-9595-25d0faa85017c69673a4-79a9-f533-9595-25d0faa85017.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-03e6e522-486f-00a5-949d-eb4d7d83ce41\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"sourceHandle\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c69673a4-79a9-f533-9595-25d0faa85017c69673a4-79a9-f533-9595-25d0faa85017.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"sourceHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"sourceHandle\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81-source\\\",\\\"target\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\\\",\\\"targetHandle\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e8adb528-abbd-de1b-a6c8-a9dac40e5f81e8adb528-abbd-de1b-a6c8-a9dac40e5f81-source-12404459-bbee-4fc5-8d75-dadc6fbe6bf512404459-bbee-4fc5-8d75-dadc6fbe6bf5-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\\\",\\\"sourceHandle\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5-source\\\",\\\"target\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__12404459-bbee-4fc5-8d75-dadc6fbe6bf512404459-bbee-4fc5-8d75-dadc6fbe6bf5-source-e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086\\\",\\\"sourceHandle\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086-source\\\",\\\"target\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__24d23dfa-06b9-0545-0b68-f2fe762f408624d23dfa-06b9-0545-0b68-f2fe762f4086-source-7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"sourceHandle\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7470177f-16fb-15c2-517a-01cc324abf7b7470177f-16fb-15c2-517a-01cc324abf7b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"sourceHandle\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"targetHandle\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5dacf97-8eda-141b-fce8-9425de15fd38e5dacf97-8eda-141b-fce8-9425de15fd38.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-80961370-bdb9-98b2-1e4e-2d7bf9fcb18c80961370-bdb9-98b2-1e4e-2d7bf9fcb18c-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"sourceHandle\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5dacf97-8eda-141b-fce8-9425de15fd38e5dacf97-8eda-141b-fce8-9425de15fd38.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"sourceHandle\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"targetHandle\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7470177f-16fb-15c2-517a-01cc324abf7b7470177f-16fb-15c2-517a-01cc324abf7b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-50a98cd2-65c9-156d-e805-24fb442254ed50a98cd2-65c9-156d-e805-24fb442254ed-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"sourceHandle\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed-source\\\",\\\"target\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__50a98cd2-65c9-156d-e805-24fb442254ed50a98cd2-65c9-156d-e805-24fb442254ed-source-94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"sourceHandle\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf-source\\\",\\\"target\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\\\",\\\"targetHandle\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__915297e6-14a1-d4ae-b857-5230424c8baf915297e6-14a1-d4ae-b857-5230424c8baf-source-bb05eb54-1ad3-0f08-9069-9b337cdfbaa2bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.006e9c19-9354-485c-05ea-3871e0e48192-source\\\",\\\"target\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.006e9c19-9354-485c-05ea-3871e0e48192-source-f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"sourceHandle\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01-source\\\",\\\"target\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f6e9c428-6316-facc-ad68-ba95b7705b01f6e9c428-6316-facc-ad68-ba95b7705b01-source-b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"sourceHandle\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879-source\\\",\\\"target\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe\\\",\\\"targetHandle\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__bd4d7981-8565-0fb9-f523-4949c77f7879bd4d7981-8565-0fb9-f523-4949c77f7879-source-58df2fbc-077b-e64e-8d73-069adfea0dbe58df2fbc-077b-e64e-8d73-069adfea0dbe-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe\\\",\\\"sourceHandle\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe-source\\\",\\\"target\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"targetHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__58df2fbc-077b-e64e-8d73-069adfea0dbe58df2fbc-077b-e64e-8d73-069adfea0dbe-source-f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"sourceHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086\\\",\\\"targetHandle\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-24d23dfa-06b9-0545-0b68-f2fe762f408624d23dfa-06b9-0545-0b68-f2fe762f4086-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\\\",\\\"sourceHandle\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-source\\\",\\\"target\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__bb05eb54-1ad3-0f08-9069-9b337cdfbaa2bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-source-a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"sourceHandle\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"2681de46-a4fb-3a01-1108-7bdbba0364ac\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a639aa17-cc38-183c-fce9-dcfa4b42af29a639aa17-cc38-183c-fce9-dcfa4b42af29.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-2681de46-a4fb-3a01-1108-7bdbba0364ac\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"sourceHandle\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78-source\\\",\\\"target\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__b953174b-6407-27e2-9a5d-3c73aa507b78b953174b-6407-27e2-9a5d-3c73aa507b78-source-c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"sourceHandle\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c89734e2-3cd1-5cfd-12a3-7fb94e73c971c89734e2-3cd1-5cfd-12a3-7fb94e73c971.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"sourceHandle\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"targetHandle\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a639aa17-cc38-183c-fce9-dcfa4b42af29a639aa17-cc38-183c-fce9-dcfa4b42af29.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"selected\\\":false},{\\\"source\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"sourceHandle\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"targetHandle\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c89734e2-3cd1-5cfd-12a3-7fb94e73c971c89734e2-3cd1-5cfd-12a3-7fb94e73c971.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"selected\\\":false}]\"}",
			rootNodeID:   "c46ec3f9-8b9b-1301-0523-17e1dbdb8305",
			targetNodeID: "073b3f06-2c79-2c49-c647-56146a7aae7a", // 首饰公司件
			//targetNodeID: "ee51ba3a-0bc4-800f-90ea-a996509c3061", // 寄送物品名称
			//targetNodeID: "55c590aa-8c75-5175-a7c0-8b7ce26442ee", // 爱马仕,香奈儿,耐克等品牌物品
			expected: true,
		},
		{
			name:         "快递费用和到达时间是多少",
			workflowJSON: "{\"ProtoVersion\":\"V2_6\",\"WorkflowID\":\"f0522dc5-0034-4aab-9e8c-80a0d9978994\",\"WorkflowName\":\"快递费用和到达时间是多少\",\"WorkflowDesc\":\"处理用户询问寄件服务的具体数字的价格或时效，当用户未指明具体运输方式或明确指定空运时，优先命中该意图，包括：\\n\\n具体金额询问（如：寄到日本要多少钱、寄某物去某国家有没有附加费）\\n具体天数询问（如：到英国几天能到）\\n重量对应的费用（如：5公斤要多少钱）\\n不同服务的价格比较（如：空运和陆运价格对比）\\n关税等附加费用金额\\n各国具体的时效天数\\n到某个国外地址要多久\\n某国的清关政策是什么\",\"Nodes\":[{\"NodeID\":\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305\",\"NodeName\":\"开始\",\"NodeDesc\":\"\",\"NodeType\":\"START\",\"StartNodeData\":{},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"707beb65-4905-ac8d-e531-6484434cb81a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":false,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":73,\\\"y\\\":452},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":86,\\\"height\\\":44}}\"},{\"NodeID\":\"a936ccfc-717d-83ae-d5ef-16769b28c63a\",\"NodeName\":\"条件判断1\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"758f8029-6ac4-b480-93c7-59e844434b00\",\"758f8029-6ac4-b480-93c7-59e844434b00\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"俄罗斯,白俄罗斯\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"伊朗\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d044038e-9930-ace6-010e-2807efaa9742\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"苏丹\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"阿富汗,布基纳法索,以色列,马里,苏丹,乌克兰,委内瑞拉,海地,黎巴嫩\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"中非共和国,刚果,刚果民主共和国,刚果金,刚果民主共和国（刚果金）\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"e5441802-d004-ce93-0250-71f1f4ef72df\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"伊拉克,利比亚,索马里,也门\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"3e9a0fca-5acb-244b-4484-3d48c47d05b8\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"北朝鲜,叙利亚,克里米亚,古巴\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"俄罗斯,白俄罗斯\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"39623e7f-0ae6-903a-3c03-c443d45939c3\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"伊朗\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"e033b6da-bafc-d684-b5ab-a3c976180ef3\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"苏丹\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":2,\\\"id\\\":\\\"4c262f22-a559-cef0-35dd-e67db4696752\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"阿富汗,布基纳法索,以色列,马里,苏丹,乌克兰,委内瑞拉,海地,黎巴嫩\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":3,\\\"id\\\":\\\"c2beb572-2b07-921b-429b-150a61dadf12\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"中非共和国,刚果,刚果民主共和国,刚果金,刚果民主共和国（刚果金）\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":4,\\\"id\\\":\\\"4143afed-b830-3f35-c37b-d4b38060e05f\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取15.Output.收件国家\\\",\\\"rightStr\\\":\\\"伊拉克,利比亚,索马里,也门\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":5,\\\"id\\\":\\\"8a5f61da-eb24-ea88-95ae-95b61be1c406\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"收件国家.Output.收件国家\\\",\\\"rightStr\\\":\\\"北朝鲜,叙利亚,克里米亚,古巴\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":6,\\\"id\\\":\\\"96fc1a34-90b5-81ae-6f3b-f9f8fd5dcfe3\\\"},{\\\"content\\\":[],\\\"index\\\":7,\\\"id\\\":\\\"12efc38d-7e33-ba1b-3337-13f9ec63ca9a\\\"}],\\\"isHovering\\\":true,\\\"isParallel\\\":true,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":591,\\\"y\\\":452},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":504},\\\"dragging\\\":false}\"},{\"NodeID\":\"758f8029-6ac4-b480-93c7-59e844434b00\",\"NodeName\":\"俄罗斯白俄罗斯\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，DHL快递发往俄罗斯和白俄罗斯的服务临时暂停，恢复时间待另行通知。谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，DHL快递发往俄罗斯和白俄罗斯的服务临时暂停，恢复时间待另行通知。谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":989.9455362271665,\\\"y\\\":299.96173701679294},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":true,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\",\"NodeName\":\"伊朗\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"出于商业决定，DHL快递单元即日起暂停了伊朗进出口快件的服务，直至进一步通知。\\n  \\n仅接收有外交签封的外交邮袋及OFAC授权许可的货物发往伊朗。若有疑问，请提前咨询您的账号销售经理。\"},\"Inputs\":[{\"Name\":\"\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"\"]}},\"Desc\":\"\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"出于商业决定，DHL快递单元即日起暂停了伊朗进出口快件的服务，直至进一步通知。\\\\n  \\\\n仅接收有外交签封的外交邮袋及OFAC授权许可的货物发往伊朗。若有疑问，请提前咨询您的账号销售经理。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":621},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"d044038e-9930-ace6-010e-2807efaa9742\",\"NodeName\":\"苏丹\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"DHL快递自2023年8月28日（星期一）起，恢复苏丹港市的进出口包裹/应税货物服务。\\n\\n但苏丹港市的进出口业务操作仍然有限。\\n\\n受航班的执飞班期影响，苏丹港市的进出口文件/包裹/应税货物将有 5天转运延迟影响。\\n\\n重量/尺寸限制如下：\\n\\n60公斤/件\\n\\n300公斤/票\\n\\n100cmx50cmx50cm最大尺寸\\n\\n苏丹其他地区（苏丹港市除外）的进出口文件/包裹/应税货物仍然暂停，恢复时间待定，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>DHL快递自2023年8月28日（星期一）起，恢复苏丹港市的进出口包裹/应税货物服务。</p><p>但苏丹港市的进出口业务操作仍然有限。</p><p>受航班的执飞班期影响，苏丹港市的进出口文件/包裹/应税货物将有 5天转运延迟影响。</p><p>重量/尺寸限制如下：</p><p>60公斤/件</p><p>300公斤/票</p><p>100cmx50cmx50cm最大尺寸</p><p>苏丹其他地区（苏丹港市除外）的进出口文件/包裹/应税货物仍然暂停，恢复时间待定，谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":790},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\",\"NodeName\":\"查验目的地清关政策\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://leonxing.cn/robot/dhlTask?adapter=https%3A%2F%2Fwippe2-pc.cndhl.com%2Fapi%2Fwiplus%2Fbase%2Flarge-model%2FserviceTips\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"countryCode\",\"ParamDesc\":\"收件国家\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"key\",\"ParamDesc\":\"\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"CUSTOM_VARIABLE\",\"CustomVarID\":\"08be3749-0cb5-4845-9571-09596f1c95af\"},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态码\"},{\"Title\":\"message\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"成功失败中文\"},{\"Title\":\"data\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"清关提示\"},{\"Title\":\"success\",\"Type\":\"BOOL\",\"Required\":[],\"Properties\":[],\"Desc\":\"成功失败\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"countryCode\\\",\\\"key\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.message\\\",\\\"Output.data\\\",\\\"Output.success\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"id\\\":\\\"4af0e2da-a92d-f915-49ab-fa355bc2b906\\\",\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"id\\\":\\\"35cf0be0-2de0-389d-27fb-b171b1be6024\\\",\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]},{\\\"id\\\":\\\"a7f1cad1-6cff-ea48-cd6b-abc1d6c6f496\\\",\\\"value\\\":\\\"message\\\",\\\"label\\\":\\\"message\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"id\\\":\\\"f641cb36-dcf4-ba2f-9e80-4028008e4441\\\",\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"id\\\":\\\"f46a8a9b-5301-7db3-fb3f-c7855ddf1764\\\",\\\"value\\\":\\\"success\\\",\\\"label\\\":\\\"success\\\",\\\"type\\\":\\\"BOOL\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":1791,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"455823e2-fe3a-269f-8fc5-f25981f6c659\",\"NodeName\":\"条件判断2\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\",\"JsonPath\":\"Output.是否继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"高风险国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"69c4688c-4d94-f912-6727-d0f64001b157\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"7ec7e47e-06a2-e22d-8131-d44e736bf6a1\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1191,\\\"y\\\":936.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\",\"NodeName\":\"条件判断3\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\",\"JsonPath\":\"Output.是否继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"限运目的地国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"33d4c2dd-22d0-431a-63ef-21d739e37fb7\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"9238690a-c3c4-eb38-1f11-301104b66242\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1191,\\\"y\\\":1120.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\",\"NodeName\":\"条件判断4\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e5441802-d004-ce93-0250-71f1f4ef72df\",\"JsonPath\":\"Output.是否继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"高风险限运目的地国家.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"a74a74c1-df68-0e89-75f1-d39ac20c8da1\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"072954d8-0c43-2276-9699-f5545e9a87a9\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1191,\\\"y\\\":1304.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\",\"NodeName\":\"条件判断5\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"ee51ba3a-0bc4-800f-90ea-a996509c3061\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"查验目的地清关政策.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"55389964-faf8-2c4c-f9e1-68b162a675f2\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6813bc02-9303-3b10-6c41-8b5c0bbc956b\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":2091,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"NodeName\":\"寄送物品名称\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"eaf2457e-cb58-4fe3-99f4-04b3544eb698\",\"Required\":true}],\"UserConstraint\":\"当需要对“包裹内容”参数进行追问时，请严格按照以下内容进行追问：“请问您需要寄送什么物品？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"424a5065-681c-8334-4db9-65042b1fb56b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：包裹内容\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"eaf2457e-cb58-4fe3-99f4-04b3544eb698\\\",\\\"label\\\":\\\"包裹内容\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":2991,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"7089a95f-1133-cf2e-c6e8-0a90295566be\",\"NodeName\":\"条件判断6\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"ee51ba3a-0bc4-800f-90ea-a996509c3061\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\",\"JsonPath\":\"Output.是否目的地清关\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"目的地有清关政策提前鉴定物品.Output.是否目的地清关\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"e9e984ac-3012-3360-535b-5c5a17bf1816\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"0d60e64e-c5f0-4ca7-ad17-1749ebd221a4\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":2691,\\\"y\\\":1542.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"100124ae-d6a4-d31a-dba7-37643315de0a\",\"NodeName\":\"取消邮寄\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"好的，请问还有什么可以帮助您的？\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"好的，请问还有什么可以帮助您的？\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":2991,\\\"y\\\":1304.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":108},\\\"dragging\\\":false}\"},{\"NodeID\":\"424a5065-681c-8334-4db9-65042b1fb56b\",\"NodeName\":\"条件判断7\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"40304fb1-5cc6-7489-2cfe-22e5975aab81\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"充电宝,移动电源,电动滑板车,自动平衡车,电动自行车,独轮电动车,背夹电源,无线充电器,高压充填之瓶罐类物品,摩丝,大漆,清漆,油漆,杀虫的烟雾剂,安全气囊,干冰,电动轮椅,平衡车,电动车\\t,电动车锂电池,车载香薰,香薰,水银,无线充,硫磺\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"6b8b0c18-674f-95c6-7c55-9b8d766df108\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"灭火器,镁棒,喷漆,手喷漆,细菌,病毒,害虫,菌种,动物血清,发泡剂,制冷剂,尿,精子,指甲油,汽油,柴油,煤油,精油,杀虫剂,固体酒精,人体组织,微生物,生物制品,血液,石棉,喷雾剂,甲烷,仿造货币,有价证券,铜币,打火机,火机,罐装瓦斯,丁烷,喷腊,喷式芳香剂,喷式刮胡膏,喷式防蚊液,防晒喷雾,喷雾类产品,礼炮,气体,医用酒精,麋鹿角,菌株,病理切片,植物\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"adb1474c-b932-17d7-5196-c6175d857b80\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"肉蛋奶蔬菜水果类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"爱马仕,香奈儿,耐克等品牌物品\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"83cd2742-eaf9-67b3-d782-70aed99e85bb\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"电子烟及其配件（包括烟油、雾化设备等）\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"04e09238-e9e5-5907-155b-f3c7af2ca95f\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"火锅底料,燕窝,肥皂,酒水,电话手表,电子产品,糖果,消毒水,海参,水彩颜料,墨盒,眼影,隐形眼镜护理液,零食,冬虫夏草,沐浴露,唇膏,味精,种子,胶水,一体机电脑,电风扇,AirPods,固体胶,点读笔,树脂,颜料,电动机,调速器,洗面奶,废水,原料药,防晒乳,维生素,肥料,番茄酱,内电产品,塑料粒子,食物,石墨粉,石斛,添加剂,蓝牙耳机,卸妆膏,植物油,彩泥,固定胶,化肥,冻干粉,洗眼液,乒乓球,音箱,湿巾,变压器,吸尘器,化工原料,蛋白粉,猫砂,蚊香液,滴眼液,蓝牙音箱,车载空气净化器,清洁剂,吹风机,水,小苏打,石英砂,凝胶,护发素,传声器,巧克力,陈皮,洗衣液,散热膏,遥控器,锆英砂,液晶电视,印泥,内置锂电池,安神补脑液,干香料,香料,辣椒酱,天麻,涂料,蜡,热熔胶,洗头膏,瓶装饮料,碳酸氢钠,花露水,沙子,笔记本电脑,防晒霜,风油精,爽肤水,化妆水,粉底液,香水,粉,苹果手机,化学品,平板电脑,小米手环,马达,黄酒,陶瓷墨水,螺丝胶,护手霜,手霜,电动燃油泵,电磁,鱼油,试剂,无人机,印油,纽扣电池,饮用水,喜糖,化工品,油墨,脚气膏,蜂蜜,润滑油,大米,香烟,压缩机,面膜,化妆品,ipad,苹果电脑        ,油样,罐头,酱料,甲油胶,膏体,膏状,蜡烛,音响,额温枪,果酱,闪粉,手环,磁铁,电机,洗洁精,墨水,腮红,空调,润滑脂,果汁,摄像机,摄影器材,烫伤膏,提取物,液体香薰,液体,洗发水,颗粒,粉末,电池,干电池,内置电池,锂电池,小电池,吃的,食品,茶叶,酒,白酒,电子设备,牙膏,护肤品,面霜,精华液,干燥剂,隐形眼镜,电瓶,磁性,保健品,方便面,手机,沐浴液,手表,口红,咖啡\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"金银珠宝等首饰类物品\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"充电宝,移动电源,电动滑板车,自动平衡车,电动自行车,独轮电动车,背夹电源,无线充电器,高压充填之瓶罐类物品,摩丝,大漆,清漆,油漆,杀虫的烟雾剂,安全气囊,干冰,电动轮椅,平衡车,电动车\\\\t,电动车锂电池,车载香薰,香薰,水银,无线充,硫磺\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"db3b4556-e8bb-ed99-8ee3-5f2777ac9ab4\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"灭火器,镁棒,喷漆,手喷漆,细菌,病毒,害虫,菌种,动物血清,发泡剂,制冷剂,尿,精子,指甲油,汽油,柴油,煤油,精油,杀虫剂,固体酒精,人体组织,微生物,生物制品,血液,石棉,喷雾剂,甲烷,仿造货币,有价证券,铜币,打火机,火机,罐装瓦斯,丁烷,喷腊,喷式芳香剂,喷式刮胡膏,喷式防蚊液,防晒喷雾,喷雾类产品,礼炮,气体,医用酒精,麋鹿角,菌株,病理切片,植物\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"1f2744ff-68e6-e1a7-9a9d-232cbce7b07c\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"肉蛋奶蔬菜水果类\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":2,\\\"id\\\":\\\"2870ca5d-e77a-5f94-a849-03bd6b78a35a\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"爱马仕,香奈儿,耐克等品牌物品\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":3,\\\"id\\\":\\\"6d86f585-e85f-c14b-b1fc-7b24ed5517dc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"电子烟及其配件（包括烟油、雾化设备等）\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":4,\\\"id\\\":\\\"c5576792-5331-a1b4-00e9-d3ebae1254cc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"火锅底料,燕窝,肥皂,酒水,电话手表,电子产品,糖果,消毒水,海参,水彩颜料,墨盒,眼影,隐形眼镜护理液,零食,冬虫夏草,沐浴露,唇膏,味精,种子,胶水,一体机电脑,电风扇,AirPods,固体胶,点读笔,树脂,颜料,电动机,调速器,洗面奶,废水,原料药,防晒乳,维生素,肥料,番茄酱,内电产品,塑料粒子,食物,石墨粉,石斛,添加剂,蓝牙耳机,卸妆膏,植物油,彩泥,固定胶,化肥,冻干粉,洗眼液,乒乓球,音箱,湿巾,变压器,吸尘器,化工原料,蛋白粉,猫砂,蚊香液,滴眼液,蓝牙音箱,车载空气净化器,清洁剂,吹风机,水,小苏打,石英砂,凝胶,护发素,传声器,巧克力,陈皮,洗衣液,散热膏,遥控器,锆英砂,液晶电视,印泥,内置锂电池,安神补脑液,干香料,香料,辣椒酱,天麻,涂料,蜡,热熔胶,洗头膏,瓶装饮料,碳酸氢钠,花露水,沙子,笔记本电脑,防晒霜,风油精,爽肤水,化妆水,粉底液,香水,粉,苹果手机,化学品,平板电脑,小米手环,马达,黄酒,陶瓷墨水,螺丝胶,护手霜,手霜,电动燃油泵,电磁,鱼油,试剂,无人机,印油,纽扣电池,饮用水,喜糖,化工品,油墨,脚气膏,蜂蜜,润滑油,大米,香烟,压缩机,面膜,化妆品,ipad,苹果电脑        ,油样,罐头,酱料,甲油胶,膏体,膏状,蜡烛,音响,额温枪,果酱,闪粉,手环,磁铁,电机,洗洁精,墨水,腮红,空调,润滑脂,果汁,摄像机,摄影器材,烫伤膏,提取物,液体香薰,液体,洗发水,颗粒,粉末,电池,干电池,内置电池,锂电池,小电池,吃的,食品,茶叶,酒,白酒,电子设备,牙膏,护肤品,面霜,精华液,干燥剂,隐形眼镜,电瓶,磁性,保健品,方便面,手机,沐浴液,手表,口红,咖啡\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":5,\\\"id\\\":\\\"1f02e4d6-0983-f69f-d452-6c58aa8f5230\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"寄送物品名称.Output.包裹内容\\\",\\\"rightStr\\\":\\\"金银珠宝等首饰类物品\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":6,\\\"id\\\":\\\"0b7e1786-2bbd-aa57-32fd-39f3443a5592\\\"},{\\\"content\\\":[],\\\"index\\\":7,\\\"id\\\":\\\"029ed8a7-f1ce-0579-f0b6-f3cba32900a7\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":3291,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":504},\\\"dragging\\\":false}\"},{\"NodeID\":\"40304fb1-5cc6-7489-2cfe-22e5975aab81\",\"NodeName\":\"危险品\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您咨询的物品是危险品，DHL无法承运危险品，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您好，您咨询的物品是危险品，DHL无法承运危险品，谢谢。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":108},\\\"dragging\\\":false}\"},{\"NodeID\":\"6b8b0c18-674f-95c6-7c55-9b8d766df108\",\"NodeName\":\"禁运品\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您咨询的物品属于禁运品。禁运品为DHL不可运输商品。抱歉无法承运，谢谢。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，您咨询的物品属于禁运品。禁运品为DHL不可运输商品。抱歉无法承运，谢谢。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":1599},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"adb1474c-b932-17d7-5196-c6175d857b80\",\"NodeName\":\"肉蛋奶蔬菜水果类\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"非常抱歉！目前此类物品DHL无法承运。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>非常抱歉！目前此类物品DHL无法承运。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":1757.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":108},\\\"dragging\\\":false}\"},{\"NodeID\":\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\",\"NodeName\":\"爱马仕,香奈儿,耐克等品牌物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"11ffc9a9-0d5c-439e-8246-cbf6238b8fcf\",\"Required\":true}],\"UserConstraint\":\"当需要对“个人还是公司”参数进行追问时，请严格按照以下内容进行追问：“【请问您是寄个人件还是公司件】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"a5f4be67-f76f-685f-492d-300c315b0a82\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"11ffc9a9-0d5c-439e-8246-cbf6238b8fcf\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":1893.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"83cd2742-eaf9-67b3-d782-70aed99e85bb\",\"NodeName\":\"电子烟及其配件\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，从保护消费者身体健康和网络运输安全的角度出发，中国DHL停止电子烟及其配件（包括烟油、雾化设备等）的出口运输。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>您好，从保护消费者身体健康和网络运输安全的角度出发，中国DHL停止电子烟及其配件（包括烟油、雾化设备等）的出口运输。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":2087.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"04e09238-e9e5-5907-155b-f3c7af2ca95f\",\"NodeName\":\"鉴定类物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"427c452a-1b0f-4ab9-b147-93297d80fb19\",\"Required\":true}],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"427c452a-1b0f-4ab9-b147-93297d80fb19\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":2234},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\",\"NodeName\":\"金银珠宝等首饰类物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"c2c5781f-f878-4e7d-b1f5-7d532178a3d0\",\"Required\":true}],\"UserConstraint\":\"当需要对“个人还是公司”参数进行追问时，请严格按照以下内容进行追问：“【请问您是寄个人件还是公司件】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：个人还是公司\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"c2c5781f-f878-4e7d-b1f5-7d532178a3d0\\\",\\\"label\\\":\\\"个人还是公司\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":3591,\\\"y\\\":2574.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"a5f4be67-f76f-685f-492d-300c315b0a82\",\"NodeName\":\"条件判断8\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"b4293b2b-dbd1-c512-6b81-f71507fc8800\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"爱马仕,香奈儿,耐克等品牌物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ae5d3d55-c9e7-d886-89cd-a75b5876990f\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"d0d51c58-4069-4e2b-7abb-fb3a11a5e343\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":3891,\\\"y\\\":1893.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"b4293b2b-dbd1-c512-6b81-f71507fc8800\",\"NodeName\":\"品牌物品个人件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"cfdfef98-c73d-42c7-bc44-0a633f56084f\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否继续包裹内容”参数进行追问时，请严格按照以下内容进行追问：“个人寄送品牌类商品，要求符合个人自用范围内，不涉及知识产权/仿制/冒牌等，海关查验需要提供购买发票等凭证。（使用过的物品一般不需购买凭证，但可能要求提供情况说明，如具体用途等）【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否二次确认寄送品牌类商品\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"cfdfef98-c73d-42c7-bc44-0a633f56084f\\\",\\\"label\\\":\\\"是否二次确认寄送品牌类商品\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":1893.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\",\"NodeName\":\"品牌物品公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"8efa5e95-8d86-479c-bd33-561ea56ba460\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否继续包裹内容”参数进行追问时，请严格按照以下内容进行追问：“公司出口品牌类商品，随货资料需提供有效品牌授权书或者有效合法使用人截图，对于有品牌的商品可以建议登陆备案系统查询品牌是否有备案，网址如下：http://202.127.48.145:8888/zscq/search/jsp/vBrandSearchIndex.jsp，无论大小品牌均需要客户如实申报，建议在运单和发票品名描述栏添加品牌信息。\\n\\n有效的品牌授权书/合法权利人使用备案截图资料请上传到5idhl在线申报平台的其他说明或报关单证处。同时请您一并随货提供，避免查验无此资料造成不必要的清关延误。\\n\\n若无有效授权资料，则海关查验将有可能查扣公共仓产生海关仓租费用，若权利人核实侵权则有可能面临海关罚款。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"1b70c439-2dd9-158b-d3a9-d8402c865467\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否二次确认寄送品牌类商品\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"8efa5e95-8d86-479c-bd33-561ea56ba460\\\",\\\"label\\\":\\\"是否二次确认寄送品牌类商品\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":2077.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\",\"NodeName\":\"条件判断9\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"04e09238-e9e5-5907-155b-f3c7af2ca95f\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"91ca5313-b00e-a168-a5b3-429ddf587e85\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"鉴定类物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"5fd859b0-c77c-f286-dd23-4759e548da65\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"e3980b89-cdd8-3954-087f-9a6842e3ac20\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":3891,\\\"y\\\":2234},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\",\"NodeName\":\"个人件\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"很抱歉，根据航空公司及安检要求，我司无法承运含有液体和粉末、颗粒、膏状固体，以及含电池类物品的个人快件。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>很抱歉，根据航空公司及安检要求，我司无法承运含有液体和粉末、颗粒、膏状固体，以及含电池类物品的个人快件。</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":2234},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"91ca5313-b00e-a168-a5b3-429ddf587e85\",\"NodeName\":\"公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"160ca5ba-f6b2-4718-9a32-9a0a7402b3d7\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否包裹内容”参数进行追问时，请严格按照以下内容进行追问：“很高兴为您解答关于鉴定类物品的出运要求。为了确保航空运输的安全，以及结合航空公司的安检规定，涉及到液体、粉末、颗粒状固体、带电池类物品、磁性物品时，需要您参考如下基本出运要求：\\n\\n1\\\\.  持有DHL运费月结账号\\n\\n2 . 化工品MSDS报告\\n\\n3\\\\.  持有非危险品鉴定报告（报告为非危险品）。\\n\\n由于出口城市和目的地国家/地区均有不同的规定，您可 _点我转接人工客服_  获取更多您所在地区鉴定物品的寄送要求，同时您也可以 [【点击这里】](https://www.5idhl.com/#/createExpress/ServiceTip) 自助参考目的地服务提示。感谢您的理解和支持。【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"6dfae16a-b8c9-3360-d2e9-72864669a781\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否二次确认寄送鉴定类商品\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"160ca5ba-f6b2-4718-9a32-9a0a7402b3d7\\\",\\\"label\\\":\\\"是否二次确认寄送鉴定类商品\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":2390.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\",\"NodeName\":\"条件判断10\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"47d22243-cd15-0278-852e-b66b5d4bd040\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\",\"JsonPath\":\"Output.个人还是公司\"}},\"LeftType\":\"STRING\",\"Operator\":\"IN\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"个人件\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"073b3f06-2c79-2c49-c647-56146a7aae7a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"金银珠宝等首饰类物品.Output.个人还是公司\\\",\\\"rightStr\\\":\\\"个人件\\\",\\\"operatorStr\\\":\\\"属于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee16cd19-d1b7-3bea-4e9a-59399c1a11fc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"983b342a-fed3-b29f-b404-092fc278d62d\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":3891,\\\"y\\\":2574.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"47d22243-cd15-0278-852e-b66b5d4bd040\",\"NodeName\":\"首饰个人件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"e9bf40ee-1e83-4b36-8e38-137dba8d0ea8\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否继续包裹内容”参数进行追问时，请严格按照以下内容进行追问：“个人物品申报寄送黄金首饰，需提供购买小票，数量需要在合理自用范围，申报金额港澳台地区不超过800人民币，其他国家不能超过1000人民币。\\n\\n请了解：包裹类快件都有可能会产生关税均由收件人支付。目的地进口要求还请收件人提前与当地DHL确认。\\n\\n温馨提示：\\n\\n个人发送快件或者接收快件，请在公司名称字段对应填写个人名字，不要在收发件人公司名称处写公司信息，否则会影响到个人物品的清关。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"6a425ade-6961-4bdf-344c-5c7191313a81\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否二次确认寄送金银首饰珠宝类商品\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"e9bf40ee-1e83-4b36-8e38-137dba8d0ea8\\\",\\\"label\\\":\\\"是否二次确认寄送金银首饰珠宝类商品\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":2574.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"073b3f06-2c79-2c49-c647-56146a7aae7a\",\"NodeName\":\"首饰公司件\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"bd55f482-bf0b-4a6c-80e2-04e6617a22ad\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否继续包裹内容”参数进行追问时，请严格按照以下内容进行追问：“黄金首饰出口申报涉证，无论金额大小均需要公司正式报关并提供黄金进出口许可证。\\n\\n温馨提示：\\n\\n1.K金首饰，只要合金里面的黄金含量百分比大于等于2%的即视为黄金首饰。\\n\\n2.铂金、银首饰、其他珠宝首饰按正常B/C/D类申报\\n\\n3.银制品除银原材料(未锻造、半制成或粉末状)外，其他银制品不涉证\\n\\n4.翡翠，玉石等原始石材或者半切割石材的进口，应咨询进口直接口岸的海关。此类石材要求提供MSDS等安全性说明文档，避免引发辐射等安全性疑虑。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否二次确认寄送金银首饰珠宝类商品\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"bd55f482-bf0b-4a6c-80e2-04e6617a22ad\\\",\\\"label\\\":\\\"是否二次确认寄送金银首饰珠宝类商品\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4191,\\\"y\\\":2758.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\",\"NodeName\":\"条件判断11\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b4293b2b-dbd1-c512-6b81-f71507fc8800\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"品牌物品个人件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"4808aee6-50c1-afe3-3664-5cf8d82da9fa\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"595631ae-989e-1b51-c18e-f05a2e04db11\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4491,\\\"y\\\":1893.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"664ef870-087d-799f-b066-913f8018f980\",\"NodeName\":\"取消邮寄2\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"好的，请问还有什么可以帮助您的？\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"<p>好的，请问还有什么可以帮助您的？</p>\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4791,\\\"y\\\":2390.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":108},\\\"dragging\\\":false}\"},{\"NodeID\":\"1b70c439-2dd9-158b-d3a9-d8402c865467\",\"NodeName\":\"条件判断12\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"品牌物品公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"bac0d4f4-e712-495a-8e27-b82bc99dd963\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"28b9f74f-8131-1ba6-69df-7a452b8819eb\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4491,\\\"y\\\":2077.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6dfae16a-b8c9-3360-d2e9-72864669a781\",\"NodeName\":\"条件判断13\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"91ca5313-b00e-a168-a5b3-429ddf587e85\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"4eb6e973-3708-27bc-8758-5c78122bc72e\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"653b73fa-1b60-365a-a7d1-09ea36e06c32\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4491,\\\"y\\\":2390.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6a425ade-6961-4bdf-344c-5c7191313a81\",\"NodeName\":\"条件判断14\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"47d22243-cd15-0278-852e-b66b5d4bd040\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"首饰个人件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"a6491ad1-1fa5-00a2-186d-52a57063c3ba\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"af9345f4-b25d-63b5-2b33-cf7570ba9678\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4491,\\\"y\\\":2574.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\",\"NodeName\":\"条件判断15\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"664ef870-087d-799f-b066-913f8018f980\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"073b3f06-2c79-2c49-c647-56146a7aae7a\",\"JsonPath\":\"Output.是否包裹内容继续\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"否\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"首饰公司件.Output.是否包裹内容继续\\\",\\\"rightStr\\\":\\\"否\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"889a0c88-ce27-8e28-bd50-faf9321ff090\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"b6fd6386-1899-0224-05e0-1cc916f6afc9\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":4491,\\\"y\\\":2758.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"NodeName\":\"收件国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"886eb669-3cb8-4ad6-83a9-fe205d937a20\",\"Required\":true}],\"UserConstraint\":\"当需要对“收件国家”参数进行追问时，请严格按照以下内容进行追问：“请问您的快件寄往哪个国家/地区？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"a936ccfc-717d-83ae-d5ef-16769b28c63a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件国家\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"886eb669-3cb8-4ad6-83a9-fe205d937a20\\\",\\\"label\\\":\\\"收件国家\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":291,\\\"y\\\":452},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\",\"NodeName\":\"高风险国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"e0d57564-13aa-4841-a327-3032ed606ea1\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家属于高风险国家。\\n  \\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n  \\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"455823e2-fe3a-269f-8fc5-f25981f6c659\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"e0d57564-13aa-4841-a327-3032ed606ea1\\\",\\\"label\\\":\\\"是否继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":936.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\",\"NodeName\":\"限运目的地国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"b48c1761-18a1-4351-b5e2-9dbd43d51718\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家属于限运目的地国家。\\n\\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"b48c1761-18a1-4351-b5e2-9dbd43d51718\\\",\\\"label\\\":\\\"是否继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":1120.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"e5441802-d004-ce93-0250-71f1f4ef72df\",\"NodeName\":\"高风险限运目的地国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"34487ec2-4d5c-483e-a96a-f24c16c2e786\",\"Required\":true}],\"UserConstraint\":\"当需要对“继续邮寄”参数进行追问时，请严格按照以下内容进行追问：“您好，您咨询的国家既是高风险国家也是限运目的地国家。\\n\\n同时您也可以点击[【这里】](https://mydhlplus.dhl.com/cn/zh/ship/surcharges.html)查询高风险和限运目的地国家列表（会因形势变化而调整，以DHL官网公示为准）以及收费详情。\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"34487ec2-4d5c-483e-a96a-f24c16c2e786\\\",\\\"label\\\":\\\"是否继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":1304.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\",\"NodeName\":\"目的地有清关政策提前鉴定物品\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"4c130875-ac34-4347-9bab-9035610eb695\",\"Required\":true}],\"UserConstraint\":\"当需要对“是否目的地清关”参数进行追问时，请严格按照以下内容进行追问：“目的地清关提示如下[@@@data](https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/serviceTips)\\n\\n【请问您是否要继续邮寄呢？】”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"7089a95f-1133-cf2e-c6e8-0a90295566be\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否目的地清关\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"4c130875-ac34-4347-9bab-9035610eb695\\\",\\\"label\\\":\\\"是否目的地清关\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":2391,\\\"y\\\":1542.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"NodeName\":\"转换收件国家到简码\",\"NodeDesc\":\"\",\"NodeType\":\"LLM\",\"LLMNodeData\":{\"ModelName\":\"hunyuan\",\"Temperature\":0.4,\"TopP\":0.7,\"MaxTokens\":3000,\"Prompt\":\"请将国家名称{{countryName}}         转换为对应的ISO 3166-1二位字母代码；如将\\\"中国\\\"转换为\\\"CN\\\"; \\\"美国\\\"转换为\\\"US\\\"; \\\"德国\\\"转换为\\\"DE\\\"。请直接返回结果\"},\"Inputs\":[{\"Name\":\"countryName\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"707beb65-4905-ac8d-e531-6484434cb81a\",\"JsonPath\":\"Output.收件国家\"}},\"Desc\":\"国家名称\"}],\"Outputs\":[],\"NextNodeIDs\":[\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"输出：Output.Content\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":1491,\\\"y\\\":1440.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\",\"NodeName\":\"确认是否有账号\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"0fca53cb-a6a0-47aa-b914-8cf475a7eb55\",\"Required\":true}],\"UserConstraint\":\"请提取“有无账号”参数，如果用户没有提供，请严格按照如下内容追问用户：“请问您是否在我公司有本地账号？（本地账号指的是贵公司和DHL 签订的付费账号)”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"cd1de672-84db-7bd8-bb9a-9921368bd421\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：有无账号\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"0fca53cb-a6a0-47aa-b914-8cf475a7eb55\\\",\\\"label\\\":\\\"有无账号\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":4791,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"cd1de672-84db-7bd8-bb9a-9921368bd421\",\"NodeName\":\"判断有无账号\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\",\"JsonPath\":\"Output.有无账号\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"有\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"a324750d-2902-729a-63e9-8efdfb81da3c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"确认是否有账号.Output.有无账号\\\",\\\"rightStr\\\":\\\"有\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"1f826908-9bc2-58f4-967c-f5da772d97d2\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"29704682-dba7-e4a8-d4c6-abd03e12e8a0\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":5091,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\",\"NodeName\":\"收集付款账号\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"1ef1d005-2c89-42da-ae65-3e81928891f8\",\"Required\":true}],\"UserConstraint\":\"请提取“付款账号”参数，如果用户没有提供，请严格按照如下内容追问客户：“请问您的本地账号是？（本地账号指的是贵公司和DHL 签订的预付费账号）”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"9ad6056b-6a21-bd05-ed40-bb019e94f692\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：付款账号\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"1ef1d005-2c89-42da-ae65-3e81928891f8\\\",\\\"label\\\":\\\"付款账号\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":5391,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"NodeName\":\"收集发件邮编及城市\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"134c2a5d-f586-41b8-aa65-32f6aa897865\",\"Required\":true},{\"RefParameterID\":\"bc2e1fd1-be55-4160-829a-d5dff9e49fe6\",\"Required\":true}],\"UserConstraint\":\"如果需要追问用户，请严格按照如下内容提问：“请提供您的发件地城市和邮编”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"9fe87046-03ab-4d7f-8205-1114cdecf54a\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：发件城市,发件邮编\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"134c2a5d-f586-41b8-aa65-32f6aa897865\\\",\\\"label\\\":\\\"发件城市\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"bc2e1fd1-be55-4160-829a-d5dff9e49fe6\\\",\\\"label\\\":\\\"发件邮编\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":6291,\\\"y\\\":2651.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"NodeName\":\"销售信息查询\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/admin/onlineChat/getSalesInfo\",\"Method\":\"GET\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[{\"ParamName\":\"account\",\"ParamDesc\":\"账号\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\",\"JsonPath\":\"Output.付款账号\"}},\"IsRequired\":false,\"SubParams\":[]}],\"Body\":[]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"响应状态\"},{\"Title\":\"data\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"销售信息\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"account\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":5691,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\",\"NodeName\":\"判断是否有销售信息\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"a324750d-2902-729a-63e9-8efdfb81da3c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"销售信息查询.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"405149e9-f39b-4d68-10bd-c8256d03aeab\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"3db30737-e90d-29f8-8342-2b59b1e40888\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":5991,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\",\"NodeName\":\"回复销售信息\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9ad6056b-6a21-bd05-ed40-bb019e94f692\",\"JsonPath\":\"Output.data\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[]},\\\"position\\\":{\\\"x\\\":6291,\\\"y\\\":2526.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86},\\\"dragging\\\":false}\"},{\"NodeID\":\"9fe87046-03ab-4d7f-8205-1114cdecf54a\",\"NodeName\":\"检查发件邮编城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"postalCode\",\"ParamDesc\":\"发件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"city\",\"ParamDesc\":\"发件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"发件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityStartPostal\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"发件邮编\"},{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"发件城市\"}],\"Desc\":\"数据\"},{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"c69673a4-79a9-f533-9595-25d0faa85017\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"postalCode\\\",\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.data\\\",\\\"Output.data.cityStartPostal\\\",\\\"Output.data.cityName\\\",\\\"Output.status\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityStartPostal\\\",\\\"label\\\":\\\"cityStartPostal\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]},{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":6591,\\\"y\\\":2651.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"c69673a4-79a9-f533-9595-25d0faa85017\",\"NodeName\":\"条件判断18\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"bd4d7981-8565-0fb9-f523-4949c77f7879\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"9fe87046-03ab-4d7f-8205-1114cdecf54a\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"03e6e522-486f-00a5-949d-eb4d7d83ce41\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查发件邮编城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":6891,\\\"y\\\":2651.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"03e6e522-486f-00a5-949d-eb4d7d83ce41\",\"NodeName\":\"发件地城市邮编错误\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您输入的国内邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您输入的国内邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":7191,\\\"y\\\":2651.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"bd4d7981-8565-0fb9-f523-4949c77f7879\",\"NodeName\":\"检查国家或地区是否有邮编\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkCountryHasPostalCode\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"countryCode\",\"ParamDesc\":\"国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":false,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"INT\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"58df2fbc-077b-e64e-8d73-069adfea0dbe\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"INT\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":7191,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"f207c545-794d-2111-e688-5962e6adfdf6\",\"NodeName\":\"条件判断19\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bd4d7981-8565-0fb9-f523-4949c77f7879\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"INT\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"24d23dfa-06b9-0545-0b68-f2fe762f4086\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查国家或地区是否有邮编.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":7791,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\",\"NodeName\":\"检查收件邮编城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"postalCode\",\"ParamDesc\":\"收件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"city\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"收件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityStartPostal\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件邮编\"},{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件城市\"},{\"Title\":\"countryCode\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件国家编码\"}],\"Desc\":\"\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"e5dacf97-8eda-141b-fce8-9425de15fd38\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"postalCode\\\",\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\",\\\"Output.data.cityStartPostal\\\",\\\"Output.data.cityName\\\",\\\"Output.data.countryCode\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityStartPostal\\\",\\\"label\\\":\\\"cityStartPostal\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"countryCode\\\",\\\"label\\\":\\\"countryCode\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]}]},\\\"position\\\":{\\\"x\\\":8391,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"NodeName\":\"收集收件邮编\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"368f79f8-6dfb-46b9-a5b7-2b9c86636986\",\"Required\":true}],\"UserConstraint\":\"请提取“收件邮编”参数，如果用户没有提供， 请严格按照如下内容追问用户：“请提供一下您的收件城市的邮编”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件邮编\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"368f79f8-6dfb-46b9-a5b7-2b9c86636986\\\",\\\"label\\\":\\\"收件邮编\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":8091,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"NodeName\":\"收集收件城市\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"c79bedbb-132a-4c7b-b51c-d0a9be5c48dc\",\"Required\":true}],\"UserConstraint\":\"请提取“收件城市”参数，如果用户没有提供， 请严格按照如下内容追问用户：“请提供一下您的收件城市名称”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"f207c545-794d-2111-e688-5962e6adfdf6\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：收件城市\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"c79bedbb-132a-4c7b-b51c-d0a9be5c48dc\\\",\\\"label\\\":\\\"收件城市\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":7491,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"24d23dfa-06b9-0545-0b68-f2fe762f4086\",\"NodeName\":\"检查收件城市\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/checkPostalCodeCity\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"city\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"countryCode\",\"ParamDesc\":\"收集实体\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"data\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"cityName\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件城市\"},{\"Title\":\"ccode\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"收件国家编码\"}],\"Desc\":\"数据\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"7470177f-16fb-15c2-517a-01cc324abf7b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"city\\\",\\\"countryCode\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.data\\\",\\\"Output.data.cityName\\\",\\\"Output.data.ccode\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"data\\\",\\\"label\\\":\\\"data\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"cityName\\\",\\\"label\\\":\\\"cityName\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"ccode\\\",\\\"label\\\":\\\"ccode\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]}]},\\\"position\\\":{\\\"x\\\":8391,\\\"y\\\":2992},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":124},\\\"dragging\\\":false}\"},{\"NodeID\":\"e5dacf97-8eda-141b-fce8-9425de15fd38\",\"NodeName\":\"条件判断20\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"50a98cd2-65c9-156d-e805-24fb442254ed\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查收件邮编城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":8691,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"7470177f-16fb-15c2-517a-01cc324abf7b\",\"NodeName\":\"条件判断21\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"50a98cd2-65c9-156d-e805-24fb442254ed\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"24d23dfa-06b9-0545-0b68-f2fe762f4086\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"检查收件城市.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":8691,\\\"y\\\":2992},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\",\"NodeName\":\"检查邮编城市不通过\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您输入的国外邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您输入的国外邮编或城市信息不正确，或者超出服务范围。建议您可以通过PC端[【点击这里】](https://www.5idhl.com/#/createExpress/fee)，移动端[【点击这里】](https://weixin.5idhl.com/freightAgeing)自助获取更多服务信息。\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":8991,\\\"y\\\":2808},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":129},\\\"dragging\\\":false}\"},{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"NodeName\":\"收集产品类型\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"85a4abba-6566-488d-b795-3877fa0aea83\",\"Required\":true}],\"UserConstraint\":\"如果需要追问用户，请严格按照如下内容追问：“您好，我想了解一下您所查询的产品类型是文件类还是包裹类？”\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：产品类型\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"85a4abba-6566-488d-b795-3877fa0aea83\\\",\\\"label\\\":\\\"产品类型\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":8991,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\",\"NodeName\":\"条件判断22\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"915297e6-14a1-d4ae-b857-5230424c8baf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"JsonPath\":\"Output.产品类型\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"包裹类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"f6e9c428-6316-facc-ad68-ba95b7705b01\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"50a98cd2-65c9-156d-e805-24fb442254ed\",\"JsonPath\":\"Output.产品类型\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"文件类\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"收集产品类型.Output.产品类型\\\",\\\"rightStr\\\":\\\"包裹类\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[{\\\"leftStr\\\":\\\"收集产品类型.Output.产品类型\\\",\\\"rightStr\\\":\\\"文件类\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":1,\\\"id\\\":\\\"006e9c19-9354-485c-05ea-3871e0e48192\\\"},{\\\"content\\\":[],\\\"index\\\":2,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":9291,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":204},\\\"dragging\\\":false}\"},{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"NodeName\":\"运费时效包裹类\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/freightTime\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"fromCountryCode\",\"ParamDesc\":\"发件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromPostalCode\",\"ParamDesc\":\"发件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromCity\",\"ParamDesc\":\"发件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCountryCode\",\"ParamDesc\":\"收件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toPostalCode\",\"ParamDesc\":\"收件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCity\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"productType\",\"ParamDesc\":\"类型\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"WPX\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"declaredValue\",\"ParamDesc\":\"申报价值\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.申报价值\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"goodsWeight\",\"ParamDesc\":\"包裹重量\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹重量\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"goodsLength\",\"ParamDesc\":\"包裹长\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹长\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"goodsWidth\",\"ParamDesc\":\"包裹宽\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹宽\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"goodsHeight\",\"ParamDesc\":\"包裹高\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.包裹高\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"goodsName\",\"ParamDesc\":\"物品名称\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"ee51ba3a-0bc4-800f-90ea-a996509c3061\",\"JsonPath\":\"Output.包裹内容\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"sendTime\",\"ParamDesc\":\"发件日期\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"JsonPath\":\"Output.发件日期\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"freightTime\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"运费时效\"},{\"Title\":\"goodsServiceTips\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"物品提示\"},{\"Title\":\"gems\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"重量提示\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"a639aa17-cc38-183c-fce9-dcfa4b42af29\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"fromCountryCode\\\",\\\"fromPostalCode\\\",\\\"fromCity\\\",\\\"toCountryCode\\\",\\\"toPostalCode\\\",\\\"toCity\\\",\\\"productType\\\",\\\"declaredValue\\\",\\\"goodsWeight\\\",\\\"goodsLength\\\",\\\"goodsWidth\\\",\\\"goodsHeight\\\",\\\"goodsName\\\",\\\"sendTime\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.freightTime\\\",\\\"Output.goodsServiceTips\\\",\\\"Output.gems\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"freightTime\\\",\\\"label\\\":\\\"freightTime\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"goodsServiceTips\\\",\\\"label\\\":\\\"goodsServiceTips\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"gems\\\",\\\"label\\\":\\\"gems\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":9891,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"915297e6-14a1-d4ae-b857-5230424c8baf\",\"NodeName\":\"收集包裹信息\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"df079ef6-a6eb-4701-b7f1-c3668aaf77b1\",\"Required\":true},{\"RefParameterID\":\"447ea9be-296f-47c2-a1f5-68ac451dc0d7\",\"Required\":true},{\"RefParameterID\":\"fc0385d1-aa25-4e9d-b715-30e3242f3c11\",\"Required\":true},{\"RefParameterID\":\"f7c313f3-0be6-45c2-aee1-ebe3c9736d65\",\"Required\":true},{\"RefParameterID\":\"e6880b40-452b-4df3-bf85-cc6e79160ce9\",\"Required\":true},{\"RefParameterID\":\"fd5523f9-16bd-4f6f-8fc7-55493591e18b\",\"Required\":true}],\"UserConstraint\":\"请提取“包裹重量”、“包裹长”、“包裹宽”、“包裹高”、“申报价值”、“发件日期”参数，如果用户没有提供，请用友好专业的问法追问用户\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：包裹重量,包裹长,包裹宽,包裹高,申报价值,发件日期\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"df079ef6-a6eb-4701-b7f1-c3668aaf77b1\\\",\\\"label\\\":\\\"包裹重量\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"447ea9be-296f-47c2-a1f5-68ac451dc0d7\\\",\\\"label\\\":\\\"包裹长\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"fc0385d1-aa25-4e9d-b715-30e3242f3c11\\\",\\\"label\\\":\\\"包裹宽\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"f7c313f3-0be6-45c2-aee1-ebe3c9736d65\\\",\\\"label\\\":\\\"包裹高\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"e6880b40-452b-4df3-bf85-cc6e79160ce9\\\",\\\"label\\\":\\\"申报价值\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"fd5523f9-16bd-4f6f-8fc7-55493591e18b\\\",\\\"label\\\":\\\"发件日期\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":9591,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":104},\\\"dragging\\\":false}\"},{\"NodeID\":\"971e080a-4c04-0ca9-993e-89e0b2449bbf\",\"NodeName\":\"兜底回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"您好，您的产品类型目前没有相关信息，请重新提问\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"您好，您的产品类型目前没有相关信息，请重新提问\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10491,\\\"y\\\":3091.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":108},\\\"dragging\\\":false}\"},{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"NodeName\":\"收集文件信息\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"4b1035f1-4d13-4a3a-8771-dc7fad4eeb8f\",\"Required\":true},{\"RefParameterID\":\"8b6cd9e6-206a-4c76-a8cb-b2b147222975\",\"Required\":true}],\"UserConstraint\":\"请提取“文件重量”和“发件日期”参数，如果用户没有提供，请用友好专业的问法追问用户\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"b953174b-6407-27e2-9a5d-3c73aa507b78\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：文件重量,发件日期\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"4b1035f1-4d13-4a3a-8771-dc7fad4eeb8f\\\",\\\"label\\\":\\\"文件重量\\\",\\\"type\\\":\\\"STRING\\\"},{\\\"value\\\":\\\"8b6cd9e6-206a-4c76-a8cb-b2b147222975\\\",\\\"label\\\":\\\"发件日期\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":9591,\\\"y\\\":3228.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"NodeName\":\"运费时效文件类\",\"NodeDesc\":\"\",\"NodeType\":\"TOOL\",\"ToolNodeData\":{\"API\":{\"URL\":\"https://wippe2-pc.cndhl.com/api/wiplus/base/large-model/freightTime\",\"Method\":\"POST\",\"authType\":\"NONE\",\"KeyLocation\":\"HEADER\",\"KeyParamName\":\"\",\"KeyParamValue\":\"\"},\"Header\":[],\"Query\":[],\"Body\":[{\"ParamName\":\"fromCountryCode\",\"ParamDesc\":\"发件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"CN\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromPostalCode\",\"ParamDesc\":\"发件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fromCity\",\"ParamDesc\":\"发件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"a324750d-2902-729a-63e9-8efdfb81da3c\",\"JsonPath\":\"Output.发件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCountryCode\",\"ParamDesc\":\"收件国家编码\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"0ebc2e94-a630-5591-0ea2-430f3097fe30\",\"JsonPath\":\"Output.Content\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toPostalCode\",\"ParamDesc\":\"收件邮编\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\",\"JsonPath\":\"Output.收件邮编\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"toCity\",\"ParamDesc\":\"收件城市\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"58df2fbc-077b-e64e-8d73-069adfea0dbe\",\"JsonPath\":\"Output.收件城市\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"productType\",\"ParamDesc\":\"类型\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"DOX\"]}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"fileWeight\",\"ParamDesc\":\"文件重量\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"JsonPath\":\"Output.文件重量\"}},\"IsRequired\":true,\"SubParams\":[]},{\"ParamName\":\"sendTime\",\"ParamDesc\":\"发件日期\",\"ParamType\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"f6e9c428-6316-facc-ad68-ba95b7705b01\",\"JsonPath\":\"Output.发件日期\"}},\"IsRequired\":true,\"SubParams\":[]}]},\"Inputs\":[],\"Outputs\":[{\"Title\":\"Output\",\"Type\":\"OBJECT\",\"Required\":[],\"Properties\":[{\"Title\":\"status\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"状态\"},{\"Title\":\"freightTime\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"运费时效\"},{\"Title\":\"goodsServiceTips\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"物品提示\"},{\"Title\":\"gems\",\"Type\":\"STRING\",\"Required\":[],\"Properties\":[],\"Desc\":\"重量提示\"}],\"Desc\":\"输出内容\"}],\"NextNodeIDs\":[\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":{\\\"inputs\\\":[\\\"fromCountryCode\\\",\\\"fromPostalCode\\\",\\\"fromCity\\\",\\\"toCountryCode\\\",\\\"toPostalCode\\\",\\\"toCity\\\",\\\"productType\\\",\\\"fileWeight\\\",\\\"sendTime\\\"],\\\"outputs\\\":[\\\"Output\\\",\\\"Output.status\\\",\\\"Output.freightTime\\\",\\\"Output.goodsServiceTips\\\",\\\"Output.gems\\\"]},\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"value\\\":\\\"Output\\\",\\\"label\\\":\\\"Output\\\",\\\"type\\\":\\\"OBJECT\\\",\\\"children\\\":[{\\\"value\\\":\\\"status\\\",\\\"label\\\":\\\"status\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"freightTime\\\",\\\"label\\\":\\\"freightTime\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"goodsServiceTips\\\",\\\"label\\\":\\\"goodsServiceTips\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]},{\\\"value\\\":\\\"gems\\\",\\\"label\\\":\\\"gems\\\",\\\"type\\\":\\\"STRING\\\",\\\"children\\\":[]}]}]},\\\"position\\\":{\\\"x\\\":9891,\\\"y\\\":3228.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":140},\\\"dragging\\\":false}\"},{\"NodeID\":\"a639aa17-cc38-183c-fce9-dcfa4b42af29\",\"NodeName\":\"条件判断23\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"2681de46-a4fb-3a01-1108-7bdbba0364ac\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"运费时效包裹类.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10191,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"2681de46-a4fb-3a01-1108-7bdbba0364ac\",\"NodeName\":\"运费时效包裹类回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\",\"JsonPath\":\"Output.freightTime\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10491,\\\"y\\\":2954.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86},\\\"dragging\\\":false}\"},{\"NodeID\":\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\",\"NodeName\":\"条件判断24\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"JsonPath\":\"Output.status\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"200\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"971e080a-4c04-0ca9-993e-89e0b2449bbf\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"运费时效文件类.Output.status\\\",\\\"rightStr\\\":\\\"200\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"6f920ea7-4352-525e-bb0e-ea12e37acde8\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10191,\\\"y\\\":3228.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\",\"NodeName\":\"运费时效文件类回复\",\"NodeDesc\":\"\",\"NodeType\":\"ANSWER\",\"AnswerNodeData\":{\"Answer\":\"{{ans}}\"},\"Inputs\":[{\"Name\":\"ans\",\"Type\":\"STRING\",\"Input\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"b953174b-6407-27e2-9a5d-3c73aa507b78\",\"JsonPath\":\"Output.freightTime\"}},\"Desc\":\"答案\"}],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"{{ans}}\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":10491,\\\"y\\\":3228.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":86},\\\"dragging\\\":false}\"},{\"NodeID\":\"3e9a0fca-5acb-244b-4484-3d48c47d05b8\",\"NodeName\":\"保函国家\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[{\"RefParameterID\":\"0a8a8cc7-23a5-4a0f-8836-383e3167b39c\",\"Required\":true}],\"UserConstraint\":\"如果无法提取到\\\"继续邮寄\\\"的参数取值，请回复以下内容进行参数取值追问：\\\"您所要求的服务需要您填写相关的确认函以保证服务的安全性。请点击如下【[官网链接](https://mydhlplus.dhl.com/cn/en/help-and-support/shipping-advice/what-documents-do-i-need.html)】下载文件，详细阅读并填写相关信息。\\n\\n此保函需英文填写并签字盖章予以确认。每次发货时需提供英文版保函原件和复印件各一份。\\n\\n【请问您是否要继续邮寄呢？】\\\"。\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[\"e4b4a59b-da89-4214-171c-e5db34926312\"],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"参数：是否继续邮寄\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[{\\\"value\\\":\\\"0a8a8cc7-23a5-4a0f-8836-383e3167b39c\\\",\\\"label\\\":\\\"是否继续邮寄\\\",\\\"type\\\":\\\"STRING\\\"}]}]},\\\"position\\\":{\\\"x\\\":891,\\\"y\\\":1624.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84},\\\"dragging\\\":false}\"},{\"NodeID\":\"e4b4a59b-da89-4214-171c-e5db34926312\",\"NodeName\":\"条件判断25\",\"NodeDesc\":\"\",\"NodeType\":\"LOGIC_EVALUATOR\",\"LogicEvaluatorNodeData\":{\"Group\":[{\"NextNodeIDs\":[\"0ebc2e94-a630-5591-0ea2-430f3097fe30\"],\"Logical\":{\"LogicalOperator\":\"UNSPECIFIED\",\"Compound\":[],\"Comparison\":{\"Left\":{\"InputType\":\"REFERENCE_OUTPUT\",\"Reference\":{\"NodeID\":\"3e9a0fca-5acb-244b-4484-3d48c47d05b8\",\"JsonPath\":\"Output.是否继续邮寄\"}},\"LeftType\":\"STRING\",\"Operator\":\"EQ\",\"Right\":{\"InputType\":\"USER_INPUT\",\"UserInputValue\":{\"Values\":[\"是\"]}},\"MatchType\":\"SEMANTIC\"}}},{\"NextNodeIDs\":[\"100124ae-d6a4-d31a-dba7-37643315de0a\"]}]},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":[{\\\"content\\\":[{\\\"leftStr\\\":\\\"参数提取23.Output.继续邮寄\\\",\\\"rightStr\\\":\\\"是\\\",\\\"operatorStr\\\":\\\"等于\\\"}],\\\"index\\\":0,\\\"id\\\":\\\"425282bf-ecb4-57a9-ca44-44cbd11b6c38\\\"},{\\\"content\\\":[],\\\"index\\\":1,\\\"id\\\":\\\"0065f578-65dd-8c17-e756-2a348ca5a5db\\\"}],\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":true,\\\"target\\\":true,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":1191,\\\"y\\\":1624.5},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":144},\\\"dragging\\\":false}\"},{\"NodeID\":\"adcc0d64-812e-3c1b-8883-a82443561f42\",\"NodeName\":\"参数提取1\",\"NodeDesc\":\"\",\"NodeType\":\"PARAMETER_EXTRACTOR\",\"ParameterExtractorNodeData\":{\"Parameters\":[],\"UserConstraint\":\"\"},\"Inputs\":[],\"Outputs\":[],\"NextNodeIDs\":[],\"NodeUI\":\"{\\\"data\\\":{\\\"content\\\":\\\"\\\",\\\"isHovering\\\":false,\\\"isParallel\\\":false,\\\"source\\\":false,\\\"target\\\":false,\\\"debug\\\":null,\\\"error\\\":false,\\\"output\\\":[{\\\"label\\\":\\\"Output\\\",\\\"desc\\\":\\\"输出内容\\\",\\\"optionType\\\":\\\"REFERENCE_OUTPUT\\\",\\\"type\\\":\\\"object\\\",\\\"children\\\":[]}]},\\\"position\\\":{\\\"x\\\":1125,\\\"y\\\":495},\\\"targetPosition\\\":\\\"left\\\",\\\"sourcePosition\\\":\\\"right\\\",\\\"selected\\\":false,\\\"measured\\\":{\\\"width\\\":250,\\\"height\\\":84}}\"}],\"Edge\":\"[{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.39623e7f-0ae6-903a-3c03-c443d45939c3-source\\\",\\\"target\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.0-source-758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.e033b6da-bafc-d684-b5ab-a3c976180ef3-source\\\",\\\"target\\\":\\\"b046da4d-ce8c-c8bc-2448-6cc8e84167d1\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.1-source-b046da4d-ce8c-c8bc-2448-6cc8e84167d1\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.4c262f22-a559-cef0-35dd-e67db4696752-source\\\",\\\"target\\\":\\\"d044038e-9930-ace6-010e-2807efaa9742\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.2-source-d044038e-9930-ace6-010e-2807efaa9742\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"sourceHandle\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca-source\\\",\\\"target\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f3d3235f-44a9-8d0a-1804-5cbce670eacaf3d3235f-44a9-8d0a-1804-5cbce670eaca-source-6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"sourceHandle\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca.6813bc02-9303-3b10-6c41-8b5c0bbc956b-source\\\",\\\"target\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a5ae515-5aa8-b334-8bd5-15069566c2ca6a5ae515-5aa8-b334-8bd5-15069566c2ca.1-source-ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"sourceHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be.e9e984ac-3012-3360-535b-5c5a17bf1816-source\\\",\\\"target\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"targetHandle\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be.0-source-ee51ba3a-0bc4-800f-90ea-a996509c3061ee51ba3a-0bc4-800f-90ea-a996509c3061-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"sourceHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be.0d60e64e-c5f0-4ca7-ad17-1749ebd221a4-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be.1-source-100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"sourceHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659.7ec7e47e-06a2-e22d-8131-d44e736bf6a1-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"sourceHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.9238690a-c3c4-eb38-1f11-301104b66242-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"sourceHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.072954d8-0c43-2276-9699-f5545e9a87a9-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.1-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061\\\",\\\"sourceHandle\\\":\\\"ee51ba3a-0bc4-800f-90ea-a996509c3061-source\\\",\\\"target\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ee51ba3a-0bc4-800f-90ea-a996509c3061ee51ba3a-0bc4-800f-90ea-a996509c3061-source-424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.db3b4556-e8bb-ed99-8ee3-5f2777ac9ab4-source\\\",\\\"target\\\":\\\"40304fb1-5cc6-7489-2cfe-22e5975aab81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.0-source-40304fb1-5cc6-7489-2cfe-22e5975aab81\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.1f2744ff-68e6-e1a7-9a9d-232cbce7b07c-source\\\",\\\"target\\\":\\\"6b8b0c18-674f-95c6-7c55-9b8d766df108\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.1-source-6b8b0c18-674f-95c6-7c55-9b8d766df108\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.2870ca5d-e77a-5f94-a849-03bd6b78a35a-source\\\",\\\"target\\\":\\\"adb1474c-b932-17d7-5196-c6175d857b80\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.2-source-adb1474c-b932-17d7-5196-c6175d857b80\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.6d86f585-e85f-c14b-b1fc-7b24ed5517dc-source\\\",\\\"target\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.3-source-55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.c5576792-5331-a1b4-00e9-d3ebae1254cc-source\\\",\\\"target\\\":\\\"83cd2742-eaf9-67b3-d782-70aed99e85bb\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.4-source-83cd2742-eaf9-67b3-d782-70aed99e85bb\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.1f02e4d6-0983-f69f-d452-6c58aa8f5230-source\\\",\\\"target\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.5-source-04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.0b7e1786-2bbd-aa57-32fd-39f3443a5592-source\\\",\\\"target\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.6-source-d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee\\\",\\\"sourceHandle\\\":\\\"55c590aa-8c75-5175-a7c0-8b7ce26442ee-source\\\",\\\"target\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__55c590aa-8c75-5175-a7c0-8b7ce26442ee55c590aa-8c75-5175-a7c0-8b7ce26442ee-source-a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"sourceHandle\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82.ae5d3d55-c9e7-d886-89cd-a75b5876990f-source\\\",\\\"target\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a5f4be67-f76f-685f-492d-300c315b0a82a5f4be67-f76f-685f-492d-300c315b0a82.0-source-b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82\\\",\\\"sourceHandle\\\":\\\"a5f4be67-f76f-685f-492d-300c315b0a82.d0d51c58-4069-4e2b-7abb-fb3a11a5e343-source\\\",\\\"target\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a5f4be67-f76f-685f-492d-300c315b0a82a5f4be67-f76f-685f-492d-300c315b0a82.1-source-d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f\\\",\\\"sourceHandle\\\":\\\"04e09238-e9e5-5907-155b-f3c7af2ca95f-source\\\",\\\"target\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__04e09238-e9e5-5907-155b-f3c7af2ca95f04e09238-e9e5-5907-155b-f3c7af2ca95f-source-0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"sourceHandle\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.5fd859b0-c77c-f286-dd23-4759e548da65-source\\\",\\\"target\\\":\\\"9c915fe9-1707-a452-4b0f-583eeb8af4c8\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b20fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.0-source-9c915fe9-1707-a452-4b0f-583eeb8af4c8\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2\\\",\\\"sourceHandle\\\":\\\"0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.e3980b89-cdd8-3954-087f-9a6842e3ac20-source\\\",\\\"target\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b20fb50029-8fb9-7cf6-a0e8-7b1ebcbde4b2.1-source-91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282\\\",\\\"sourceHandle\\\":\\\"d616bc15-1ebe-1fe1-20d7-12139a7c3282-source\\\",\\\"target\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__d616bc15-1ebe-1fe1-20d7-12139a7c3282d616bc15-1ebe-1fe1-20d7-12139a7c3282-source-8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"sourceHandle\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b.ee16cd19-d1b7-3bea-4e9a-59399c1a11fc-source\\\",\\\"target\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__8dbd4ed5-311b-30cb-96e2-7c020b70b72b8dbd4ed5-311b-30cb-96e2-7c020b70b72b.0-source-47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b\\\",\\\"sourceHandle\\\":\\\"8dbd4ed5-311b-30cb-96e2-7c020b70b72b.983b342a-fed3-b29f-b404-092fc278d62d-source\\\",\\\"target\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__8dbd4ed5-311b-30cb-96e2-7c020b70b72b8dbd4ed5-311b-30cb-96e2-7c020b70b72b.1-source-073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800\\\",\\\"sourceHandle\\\":\\\"b4293b2b-dbd1-c512-6b81-f71507fc8800-source\\\",\\\"target\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__b4293b2b-dbd1-c512-6b81-f71507fc8800b4293b2b-dbd1-c512-6b81-f71507fc8800-source-0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"sourceHandle\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af.4808aee6-50c1-afe3-3664-5cf8d82da9fa-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0d34034b-4d38-db6a-40b4-2c40fef6e7af0d34034b-4d38-db6a-40b4-2c40fef6e7af.0-source-664ef870-087d-799f-b066-913f8018f980\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8\\\",\\\"sourceHandle\\\":\\\"d9943e0b-9495-ade7-7191-9c0fbcb904f8-source\\\",\\\"target\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__d9943e0b-9495-ade7-7191-9c0fbcb904f8d9943e0b-9495-ade7-7191-9c0fbcb904f8-source-1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"sourceHandle\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467.bac0d4f4-e712-495a-8e27-b82bc99dd963-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1b70c439-2dd9-158b-d3a9-d8402c8654671b70c439-2dd9-158b-d3a9-d8402c865467.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85\\\",\\\"sourceHandle\\\":\\\"91ca5313-b00e-a168-a5b3-429ddf587e85-source\\\",\\\"target\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__91ca5313-b00e-a168-a5b3-429ddf587e8591ca5313-b00e-a168-a5b3-429ddf587e85-source-6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"sourceHandle\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781.4eb6e973-3708-27bc-8758-5c78122bc72e-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6dfae16a-b8c9-3360-d2e9-72864669a7816dfae16a-b8c9-3360-d2e9-72864669a781.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040\\\",\\\"sourceHandle\\\":\\\"47d22243-cd15-0278-852e-b66b5d4bd040-source\\\",\\\"target\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__47d22243-cd15-0278-852e-b66b5d4bd04047d22243-cd15-0278-852e-b66b5d4bd040-source-6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"sourceHandle\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81.a6491ad1-1fa5-00a2-186d-52a57063c3ba-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a425ade-6961-4bdf-344c-5c7191313a816a425ade-6961-4bdf-344c-5c7191313a81.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a\\\",\\\"sourceHandle\\\":\\\"073b3f06-2c79-2c49-c647-56146a7aae7a-source\\\",\\\"target\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__073b3f06-2c79-2c49-c647-56146a7aae7a073b3f06-2c79-2c49-c647-56146a7aae7a-source-facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"sourceHandle\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39.889a0c88-ce27-8e28-bd50-faf9321ff090-source\\\",\\\"target\\\":\\\"664ef870-087d-799f-b066-913f8018f980\\\",\\\"targetHandle\\\":\\\"664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__facd6bee-c5e7-b216-1d5c-20d0abf62c39facd6bee-c5e7-b216-1d5c-20d0abf62c39.0-source-664ef870-087d-799f-b066-913f8018f980664ef870-087d-799f-b066-913f8018f980-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305\\\",\\\"sourceHandle\\\":\\\"c46ec3f9-8b9b-1301-0523-17e1dbdb8305-source\\\",\\\"target\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c46ec3f9-8b9b-1301-0523-17e1dbdb8305c46ec3f9-8b9b-1301-0523-17e1dbdb8305-source-707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a\\\",\\\"sourceHandle\\\":\\\"707beb65-4905-ac8d-e531-6484434cb81a-source\\\",\\\"target\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"targetHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__707beb65-4905-ac8d-e531-6484434cb81a707beb65-4905-ac8d-e531-6484434cb81a-source-a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.c2beb572-2b07-921b-429b-150a61dadf12-source\\\",\\\"target\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.3-source-4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72\\\",\\\"sourceHandle\\\":\\\"4c09460c-761e-f2fe-4d11-d25ee56cfb72-source\\\",\\\"target\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"targetHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__4c09460c-761e-f2fe-4d11-d25ee56cfb724c09460c-761e-f2fe-4d11-d25ee56cfb72-source-455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.4143afed-b830-3f35-c37b-d4b38060e05f-source\\\",\\\"target\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.4-source-7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb\\\",\\\"sourceHandle\\\":\\\"7eb041a5-4d1a-576a-a2ce-98929bd51ceb-source\\\",\\\"target\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"targetHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7eb041a5-4d1a-576a-a2ce-98929bd51ceb7eb041a5-4d1a-576a-a2ce-98929bd51ceb-source-ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.8a5f61da-eb24-ea88-95ae-95b61be1c406-source\\\",\\\"target\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.5-source-e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df\\\",\\\"sourceHandle\\\":\\\"e5441802-d004-ce93-0250-71f1f4ef72df-source\\\",\\\"target\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"targetHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5441802-d004-ce93-0250-71f1f4ef72dfe5441802-d004-ce93-0250-71f1f4ef72df-source-1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\\\",\\\"sourceHandle\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-source\\\",\\\"target\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be\\\",\\\"targetHandle\\\":\\\"7089a95f-1133-cf2e-c6e8-0a90295566be-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__725e1eb4-4eee-ed7d-9b26-3a4c018bbfae725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-source-7089a95f-1133-cf2e-c6e8-0a90295566be7089a95f-1133-cf2e-c6e8-0a90295566be-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"sourceHandle\\\":\\\"\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b-0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"target\\\":\\\"f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0ebc2e94-a630-5591-0ea2-430f3097fe30-f3d3235f-44a9-8d0a-1804-5cbce670eaca\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8\\\",\\\"sourceHandle\\\":\\\"ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.33d4c2dd-22d0-431a-63ef-21d739e37fb7-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__ebbbc0e6-870a-32ba-6824-7fc415a5e0f8ebbbc0e6-870a-32ba-6824-7fc415a5e0f8.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659\\\",\\\"sourceHandle\\\":\\\"455823e2-fe3a-269f-8fc5-f25981f6c659.69c4688c-4d94-f912-6727-d0f64001b157-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__455823e2-fe3a-269f-8fc5-f25981f6c659455823e2-fe3a-269f-8fc5-f25981f6c659.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.12efc38d-7e33-ba1b-3337-13f9ec63ca9a-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.6-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.39623e7f-0ae6-903a-3c03-c443d45939c3-source\\\",\\\"target\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00\\\",\\\"targetHandle\\\":\\\"758f8029-6ac4-b480-93c7-59e844434b00-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.0-source-758f8029-6ac4-b480-93c7-59e844434b00758f8029-6ac4-b480-93c7-59e844434b00-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b\\\",\\\"sourceHandle\\\":\\\"1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.a74a74c1-df68-0e89-75f1-d39ac20c8da1-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b1e4afc55-8a6e-8b28-5dd5-6d02f47bea7b.0-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca\\\",\\\"sourceHandle\\\":\\\"6a5ae515-5aa8-b334-8bd5-15069566c2ca.55389964-faf8-2c4c-f9e1-68b162a675f2-source\\\",\\\"target\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae\\\",\\\"targetHandle\\\":\\\"725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a5ae515-5aa8-b334-8bd5-15069566c2ca6a5ae515-5aa8-b334-8bd5-15069566c2ca.0-source-725e1eb4-4eee-ed7d-9b26-3a4c018bbfae725e1eb4-4eee-ed7d-9b26-3a4c018bbfae-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af\\\",\\\"sourceHandle\\\":\\\"0d34034b-4d38-db6a-40b4-2c40fef6e7af.595631ae-989e-1b51-c18e-f05a2e04db11-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__0d34034b-4d38-db6a-40b4-2c40fef6e7af0d34034b-4d38-db6a-40b4-2c40fef6e7af.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467\\\",\\\"sourceHandle\\\":\\\"1b70c439-2dd9-158b-d3a9-d8402c865467.28b9f74f-8131-1ba6-69df-7a452b8819eb-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__1b70c439-2dd9-158b-d3a9-d8402c8654671b70c439-2dd9-158b-d3a9-d8402c865467.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781\\\",\\\"sourceHandle\\\":\\\"6dfae16a-b8c9-3360-d2e9-72864669a781.653b73fa-1b60-365a-a7d1-09ea36e06c32-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6dfae16a-b8c9-3360-d2e9-72864669a7816dfae16a-b8c9-3360-d2e9-72864669a781.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81\\\",\\\"sourceHandle\\\":\\\"6a425ade-6961-4bdf-344c-5c7191313a81.af9345f4-b25d-63b5-2b33-cf7570ba9678-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__6a425ade-6961-4bdf-344c-5c7191313a816a425ade-6961-4bdf-344c-5c7191313a81.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39\\\",\\\"sourceHandle\\\":\\\"facd6bee-c5e7-b216-1d5c-20d0abf62c39.b6fd6386-1899-0224-05e0-1cc916f6afc9-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__facd6bee-c5e7-b216-1d5c-20d0abf62c39facd6bee-c5e7-b216-1d5c-20d0abf62c39.1-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b\\\",\\\"sourceHandle\\\":\\\"424a5065-681c-8334-4db9-65042b1fb56b.029ed8a7-f1ce-0579-f0b6-f3cba32900a7-source\\\",\\\"target\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"targetHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__424a5065-681c-8334-4db9-65042b1fb56b424a5065-681c-8334-4db9-65042b1fb56b.7-source-137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74\\\",\\\"sourceHandle\\\":\\\"137d3df6-ba00-ee4c-899b-e1f1e17cda74-source\\\",\\\"target\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__137d3df6-ba00-ee4c-899b-e1f1e17cda74137d3df6-ba00-ee4c-899b-e1f1e17cda74-source-cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"sourceHandle\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421.1f826908-9bc2-58f4-967c-f5da772d97d2-source\\\",\\\"target\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__cd1de672-84db-7bd8-bb9a-9921368bd421cd1de672-84db-7bd8-bb9a-9921368bd421.0-source-9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421\\\",\\\"sourceHandle\\\":\\\"cd1de672-84db-7bd8-bb9a-9921368bd421.29704682-dba7-e4a8-d4c6-abd03e12e8a0-source\\\",\\\"target\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__cd1de672-84db-7bd8-bb9a-9921368bd421cd1de672-84db-7bd8-bb9a-9921368bd421.1-source-a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3\\\",\\\"sourceHandle\\\":\\\"9a7fd390-c81c-7bca-cd12-7b03606f14d3-source\\\",\\\"target\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9a7fd390-c81c-7bca-cd12-7b03606f14d39a7fd390-c81c-7bca-cd12-7b03606f14d3-source-9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692\\\",\\\"sourceHandle\\\":\\\"9ad6056b-6a21-bd05-ed40-bb019e94f692-source\\\",\\\"target\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9ad6056b-6a21-bd05-ed40-bb019e94f6929ad6056b-6a21-bd05-ed40-bb019e94f692-source-5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"sourceHandle\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226.405149e9-f39b-4d68-10bd-c8256d03aeab-source\\\",\\\"target\\\":\\\"6fb4f524-b70d-a837-9c45-0999dc9cb3cf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__5285d6b8-cc75-aee0-c3d9-3beb248e62265285d6b8-cc75-aee0-c3d9-3beb248e6226.0-source-6fb4f524-b70d-a837-9c45-0999dc9cb3cf\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226\\\",\\\"sourceHandle\\\":\\\"5285d6b8-cc75-aee0-c3d9-3beb248e6226.3db30737-e90d-29f8-8342-2b59b1e40888-source\\\",\\\"target\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"targetHandle\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__5285d6b8-cc75-aee0-c3d9-3beb248e62265285d6b8-cc75-aee0-c3d9-3beb248e6226.1-source-a324750d-2902-729a-63e9-8efdfb81da3ca324750d-2902-729a-63e9-8efdfb81da3c-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c\\\",\\\"sourceHandle\\\":\\\"a324750d-2902-729a-63e9-8efdfb81da3c-source\\\",\\\"target\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a\\\",\\\"targetHandle\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a324750d-2902-729a-63e9-8efdfb81da3ca324750d-2902-729a-63e9-8efdfb81da3c-source-9fe87046-03ab-4d7f-8205-1114cdecf54a9fe87046-03ab-4d7f-8205-1114cdecf54a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a\\\",\\\"sourceHandle\\\":\\\"9fe87046-03ab-4d7f-8205-1114cdecf54a-source\\\",\\\"target\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__9fe87046-03ab-4d7f-8205-1114cdecf54a9fe87046-03ab-4d7f-8205-1114cdecf54a-source-c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"sourceHandle\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"03e6e522-486f-00a5-949d-eb4d7d83ce41\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c69673a4-79a9-f533-9595-25d0faa85017c69673a4-79a9-f533-9595-25d0faa85017.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-03e6e522-486f-00a5-949d-eb4d7d83ce41\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017\\\",\\\"sourceHandle\\\":\\\"c69673a4-79a9-f533-9595-25d0faa85017.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c69673a4-79a9-f533-9595-25d0faa85017c69673a4-79a9-f533-9595-25d0faa85017.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"sourceHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81\\\",\\\"sourceHandle\\\":\\\"e8adb528-abbd-de1b-a6c8-a9dac40e5f81-source\\\",\\\"target\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\\\",\\\"targetHandle\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e8adb528-abbd-de1b-a6c8-a9dac40e5f81e8adb528-abbd-de1b-a6c8-a9dac40e5f81-source-12404459-bbee-4fc5-8d75-dadc6fbe6bf512404459-bbee-4fc5-8d75-dadc6fbe6bf5-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5\\\",\\\"sourceHandle\\\":\\\"12404459-bbee-4fc5-8d75-dadc6fbe6bf5-source\\\",\\\"target\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__12404459-bbee-4fc5-8d75-dadc6fbe6bf512404459-bbee-4fc5-8d75-dadc6fbe6bf5-source-e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086\\\",\\\"sourceHandle\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086-source\\\",\\\"target\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__24d23dfa-06b9-0545-0b68-f2fe762f408624d23dfa-06b9-0545-0b68-f2fe762f4086-source-7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"sourceHandle\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7470177f-16fb-15c2-517a-01cc324abf7b7470177f-16fb-15c2-517a-01cc324abf7b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"sourceHandle\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c\\\",\\\"targetHandle\\\":\\\"80961370-bdb9-98b2-1e4e-2d7bf9fcb18c-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5dacf97-8eda-141b-fce8-9425de15fd38e5dacf97-8eda-141b-fce8-9425de15fd38.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-80961370-bdb9-98b2-1e4e-2d7bf9fcb18c80961370-bdb9-98b2-1e4e-2d7bf9fcb18c-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38\\\",\\\"sourceHandle\\\":\\\"e5dacf97-8eda-141b-fce8-9425de15fd38.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e5dacf97-8eda-141b-fce8-9425de15fd38e5dacf97-8eda-141b-fce8-9425de15fd38.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b\\\",\\\"sourceHandle\\\":\\\"7470177f-16fb-15c2-517a-01cc324abf7b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"targetHandle\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__7470177f-16fb-15c2-517a-01cc324abf7b7470177f-16fb-15c2-517a-01cc324abf7b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-50a98cd2-65c9-156d-e805-24fb442254ed50a98cd2-65c9-156d-e805-24fb442254ed-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed\\\",\\\"sourceHandle\\\":\\\"50a98cd2-65c9-156d-e805-24fb442254ed-source\\\",\\\"target\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__50a98cd2-65c9-156d-e805-24fb442254ed50a98cd2-65c9-156d-e805-24fb442254ed-source-94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf\\\",\\\"sourceHandle\\\":\\\"915297e6-14a1-d4ae-b857-5230424c8baf-source\\\",\\\"target\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\\\",\\\"targetHandle\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__915297e6-14a1-d4ae-b857-5230424c8baf915297e6-14a1-d4ae-b857-5230424c8baf-source-bb05eb54-1ad3-0f08-9069-9b337cdfbaa2bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b\\\",\\\"sourceHandle\\\":\\\"94b201c8-dd73-dec7-589c-4ae45a9ec43b.006e9c19-9354-485c-05ea-3871e0e48192-source\\\",\\\"target\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__94b201c8-dd73-dec7-589c-4ae45a9ec43b94b201c8-dd73-dec7-589c-4ae45a9ec43b.006e9c19-9354-485c-05ea-3871e0e48192-source-f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01\\\",\\\"sourceHandle\\\":\\\"f6e9c428-6316-facc-ad68-ba95b7705b01-source\\\",\\\"target\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f6e9c428-6316-facc-ad68-ba95b7705b01f6e9c428-6316-facc-ad68-ba95b7705b01-source-b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879\\\",\\\"sourceHandle\\\":\\\"bd4d7981-8565-0fb9-f523-4949c77f7879-source\\\",\\\"target\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe\\\",\\\"targetHandle\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__bd4d7981-8565-0fb9-f523-4949c77f7879bd4d7981-8565-0fb9-f523-4949c77f7879-source-58df2fbc-077b-e64e-8d73-069adfea0dbe58df2fbc-077b-e64e-8d73-069adfea0dbe-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe\\\",\\\"sourceHandle\\\":\\\"58df2fbc-077b-e64e-8d73-069adfea0dbe-source\\\",\\\"target\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"targetHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__58df2fbc-077b-e64e-8d73-069adfea0dbe58df2fbc-077b-e64e-8d73-069adfea0dbe-source-f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6\\\",\\\"sourceHandle\\\":\\\"f207c545-794d-2111-e688-5962e6adfdf6.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086\\\",\\\"targetHandle\\\":\\\"24d23dfa-06b9-0545-0b68-f2fe762f4086-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__f207c545-794d-2111-e688-5962e6adfdf6f207c545-794d-2111-e688-5962e6adfdf6.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-24d23dfa-06b9-0545-0b68-f2fe762f408624d23dfa-06b9-0545-0b68-f2fe762f4086-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2\\\",\\\"sourceHandle\\\":\\\"bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-source\\\",\\\"target\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__bb05eb54-1ad3-0f08-9069-9b337cdfbaa2bb05eb54-1ad3-0f08-9069-9b337cdfbaa2-source-a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"sourceHandle\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"2681de46-a4fb-3a01-1108-7bdbba0364ac\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a639aa17-cc38-183c-fce9-dcfa4b42af29a639aa17-cc38-183c-fce9-dcfa4b42af29.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-2681de46-a4fb-3a01-1108-7bdbba0364ac\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78\\\",\\\"sourceHandle\\\":\\\"b953174b-6407-27e2-9a5d-3c73aa507b78-source\\\",\\\"target\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__b953174b-6407-27e2-9a5d-3c73aa507b78b953174b-6407-27e2-9a5d-3c73aa507b78-source-c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"sourceHandle\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source\\\",\\\"target\\\":\\\"ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c89734e2-3cd1-5cfd-12a3-7fb94e73c971c89734e2-3cd1-5cfd-12a3-7fb94e73c971.ee5f9a27-97f2-cf7b-6ec1-c9643c020fcc-source-ecf7ee97-36e5-5871-7c2e-7e21cc26f4bf\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29\\\",\\\"sourceHandle\\\":\\\"a639aa17-cc38-183c-fce9-dcfa4b42af29.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"targetHandle\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a639aa17-cc38-183c-fce9-dcfa4b42af29a639aa17-cc38-183c-fce9-dcfa4b42af29.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971\\\",\\\"sourceHandle\\\":\\\"c89734e2-3cd1-5cfd-12a3-7fb94e73c971.6f920ea7-4352-525e-bb0e-ea12e37acde8-source\\\",\\\"target\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf\\\",\\\"targetHandle\\\":\\\"971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__c89734e2-3cd1-5cfd-12a3-7fb94e73c971c89734e2-3cd1-5cfd-12a3-7fb94e73c971.6f920ea7-4352-525e-bb0e-ea12e37acde8-source-971e080a-4c04-0ca9-993e-89e0b2449bbf971e080a-4c04-0ca9-993e-89e0b2449bbf-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a\\\",\\\"sourceHandle\\\":\\\"a936ccfc-717d-83ae-d5ef-16769b28c63a.96fc1a34-90b5-81ae-6f3b-f9f8fd5dcfe3-source\\\",\\\"target\\\":\\\"3e9a0fca-5acb-244b-4484-3d48c47d05b8\\\",\\\"targetHandle\\\":\\\"3e9a0fca-5acb-244b-4484-3d48c47d05b8-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":true,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__a936ccfc-717d-83ae-d5ef-16769b28c63aa936ccfc-717d-83ae-d5ef-16769b28c63a.96fc1a34-90b5-81ae-6f3b-f9f8fd5dcfe3-source-3e9a0fca-5acb-244b-4484-3d48c47d05b83e9a0fca-5acb-244b-4484-3d48c47d05b8-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"3e9a0fca-5acb-244b-4484-3d48c47d05b8\\\",\\\"sourceHandle\\\":\\\"3e9a0fca-5acb-244b-4484-3d48c47d05b8-source\\\",\\\"target\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312\\\",\\\"targetHandle\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__3e9a0fca-5acb-244b-4484-3d48c47d05b83e9a0fca-5acb-244b-4484-3d48c47d05b8-source-e4b4a59b-da89-4214-171c-e5db34926312e4b4a59b-da89-4214-171c-e5db34926312-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312\\\",\\\"sourceHandle\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312.425282bf-ecb4-57a9-ca44-44cbd11b6c38-source\\\",\\\"target\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30\\\",\\\"targetHandle\\\":\\\"0ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e4b4a59b-da89-4214-171c-e5db34926312e4b4a59b-da89-4214-171c-e5db34926312.425282bf-ecb4-57a9-ca44-44cbd11b6c38-source-0ebc2e94-a630-5591-0ea2-430f3097fe300ebc2e94-a630-5591-0ea2-430f3097fe30-target\\\",\\\"selected\\\":false,\\\"animated\\\":false},{\\\"source\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312\\\",\\\"sourceHandle\\\":\\\"e4b4a59b-da89-4214-171c-e5db34926312.0065f578-65dd-8c17-e756-2a348ca5a5db-source\\\",\\\"target\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a\\\",\\\"targetHandle\\\":\\\"100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"type\\\":\\\"custom\\\",\\\"data\\\":{\\\"connectedNodeIsHovering\\\":false,\\\"error\\\":false,\\\"isHovering\\\":false},\\\"id\\\":\\\"xy-edge__e4b4a59b-da89-4214-171c-e5db34926312e4b4a59b-da89-4214-171c-e5db34926312.0065f578-65dd-8c17-e756-2a348ca5a5db-source-100124ae-d6a4-d31a-dba7-37643315de0a100124ae-d6a4-d31a-dba7-37643315de0a-target\\\",\\\"selected\\\":false,\\\"animated\\\":false}]\"}",
			rootNodeID:   "c46ec3f9-8b9b-1301-0523-17e1dbdb8305",
			targetNodeID: "ee51ba3a-0bc4-800f-90ea-a996509c3061", // 首饰公司件
			//targetNodeID: "ee51ba3a-0bc4-800f-90ea-a996509c3061", // 寄送物品名称
			//targetNodeID: "55c590aa-8c75-5175-a7c0-8b7ce26442ee", // 爱马仕,香奈儿,耐克等品牌物品
			expected: true,
		},
	}
	for _, tt := range tests {
		//var durations []string
		t.Run(tt.name, func(t *testing.T) {
			nodeMap := make(map[string]*KEP_WF.WorkflowNode)

			if len(tt.workflowJSON) > 0 {
				wf, err := protoutil.JsonToWorkflowForPreCheck(tt.workflowJSON)
				if err != nil {
					return
				}
				for _, n := range wf.GetNodes() {
					nodeMap[n.GetNodeID()] = n
				}

				//for _, node := range wf.GetNodes() {
				//	if node.GetNodeType() != KEP_WF.NodeType_PARAMETER_EXTRACTOR {
				//		continue
				//	}
				//	startTime := time.Now()
				//	isNodeEssentialWithLogicEvaluator(nodeMap, tt.rootNodeID, node.GetNodeID())
				//	duration := time.Now().Sub(startTime)
				//	durations = append(durations, fmt.Sprintf("%s %s 耗时: %v\n", node.GetNodeID(), node.GetNodeName(), duration))
				//}
			} else {
				nodeMap = tt.nodeMap
			}
			t.Logf("%s: %t", tt.name, tt.expected)

			//rr := getAllPaths2(nodeMap, tt.rootNodeID)
			//t.Logf("%s: %+v", tt.name, rr)

			//assert.Equalf(t, tt.expected, isWorkflowValid(nodeMap), "isWorkflowValid(%v, %v, %v)", nil, tt.rootNodeID, tt.targetNodeID)

			//assert.Equalf(t, tt.expected, CheckWorkflowParallelLegal(nodeMap), "CheckWorkflowParallel(%v, %v, %v)", nil, tt.rootNodeID, tt.targetNodeID)

			//for _, duration := range durations {
			//	fmt.Println(duration)
			//}
			assert.Equalf(t, tt.expected, isNodeEssentialWithLogicEvaluator(nodeMap, tt.rootNodeID, tt.targetNodeID), "isNodeEssential(%v, %v, %v)", nil, tt.rootNodeID, tt.targetNodeID)
			//assert.Equalf(t, tt.expected, isNodeEssentialLogic(tt.nodeMap, tt.rootNodeID, tt.targetNodeID), "isNodeEssential(%v, %v, %v)", tt.nodeMap, tt.rootNodeID, tt.targetNodeID)
			//assert.Equalf(t, tt.expected, isNodeEssentialWithLogicEvaluator(tt.nodeMap, tt.rootNodeID, tt.targetNodeID), "isNodeEssential(%v, %v, %v)", tt.nodeMap, tt.rootNodeID, tt.targetNodeID)
		})
	}
}
