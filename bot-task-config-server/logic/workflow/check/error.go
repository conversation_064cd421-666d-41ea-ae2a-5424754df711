/*
 * 2024-10-16
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

// NodeError 节点的错误
type NodeError struct {
	NodeID string
	ErrMsg string
}

// WorkflowError 校验错误的信息
type WorkflowError struct {
	ErrorMsg   error
	NodeErrors []*NodeError
}

func (c *WfContext) appendNodeError(nodeID, errMsg string) {
	c.nodeErrors = append(c.nodeErrors, &NodeError{NodeID: nodeID, ErrMsg: errMsg})
}

func (c *WfContext) appendMultiNodesError(nodeIDs []string, msg string) {
	for _, nodeID := range nodeIDs {
		c.appendNodeError(nodeID, msg)
	}
}
