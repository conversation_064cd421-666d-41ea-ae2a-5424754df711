/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/ohler55/ojg/jp"
)

// 公共的输入参数判断
func (c *WfContext) checkInputAndCollect(currentNodeID string, input *KEP_WF.Input) {
	c.checkInputAndCollectBase(currentNodeID, nil, input)
}

// 只针对可以引用"当前节点内定义的输入参数"类型的 Input
// 如： 循环节点中的循环体等
//
//   - inputParamsDefines: 在当前节点定义的"输入参数"，用于校验在当前节点中引用的输入参数是否
//   - input: 要校验的Input
//
// 如果不确定是否需要用这个func，那就应该用上面的 checkInputAndCollect

func (c *WfContext) checkInputAndCollectForSupportInnerInputParams(currentNodeID string,
	nodeInputParamNames []string, input *KEP_WF.Input) {
	c.checkInputAndCollectBase(currentNodeID, nodeInputParamNames, input)
}

// 1. 校验 Input
// 2. 统一收集引用的"API参数（自定义变量）"
func (c *WfContext) checkInputAndCollectBase(currentNodeID string, nodeInputParamNames []string, input *KEP_WF.Input) {
	log.InfoContextf(c.ctx, "checkInputAndCollect|currentNodeID:%s|input:%+v", currentNodeID, input)
	if input == nil {
		return
	}

	switch input.GetInputType() {
	case KEP_WF.InputSourceEnum_USER_INPUT:
		if len(input.GetUserInputValue().GetValues()) == 0 {
			c.appendNodeError(currentNodeID, "输入内容为空")
		}
		for _, v := range input.GetUserInputValue().GetValues() {
			if len(v) == 0 {
				c.appendNodeError(currentNodeID, "输入内容为空")
			}
		}

		maxLen := config.GetMainConfig().VerifyWorkflow.UserInputMaxLen
		for i, v := range input.GetUserInputValue().GetValues() {
			if utf8.RuneCountInString(v) > maxLen {
				log.InfoContextf(c.ctx, "checkInputAndCollect|UserInput|[%d]|len:%d|v:%s", i, len(v), v)
				c.appendNodeError(currentNodeID, fmt.Sprintf("输入内容长度不能超过%d", maxLen))
			}
		}
	case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT:
		log.InfoContextf(c.ctx, "checkInputAndCollect|RefNodeID:%s", input.GetReference().GetNodeID())
		log.InfoContextf(c.ctx, "checkInputAndCollect|jsonPath:%s", input.GetReference().GetJsonPath())
		if len(input.GetReference().GetNodeID()) == 0 {
			c.appendNodeError(currentNodeID, "引用的内容为空")
			return
		}
		startTimePredecessor := time.Now()
		predecessorNodeIDsMap := c.getPredecessorNodeIDsMap(currentNodeID)
		log.InfoContextf(c.ctx, "checkInputAndCollectBase-elapsed|currentNodeID:%s|JsonPath:%s|%s",
			currentNodeID, input.GetReference().GetJsonPath(), time.Since(startTimePredecessor).String())

		if _, exist := predecessorNodeIDsMap[input.GetReference().GetNodeID()]; !exist {
			refNode, refExist := c.nodeMap[input.GetReference().GetNodeID()]
			if !refExist {
				// 引用Node的不存在
				c.appendNodeError(currentNodeID, fmt.Sprintf("引用的 (ID:%s) 节点不存在",
					input.GetReference().GetNodeID()))
			} else {
				// 引用Node的不是来自前驱节点节点
				c.appendNodeError(currentNodeID, fmt.Sprintf("引用的 %s(ID:%s) 节点不是前驱节点",
					refNode.GetNodeName(), refNode.GetNodeID()))
			}
		}
		c.checkReferenceOutput(currentNodeID, input.GetReference())

	case KEP_WF.InputSourceEnum_CUSTOM_VARIABLE:
		log.InfoContextf(c.ctx, "checkInputAndCollect|CustomVarID:%s", input.GetCustomVarID())
		if len(input.GetCustomVarID()) == 0 {
			c.appendNodeError(currentNodeID, "引用的自定义参数为空")
			return
		}
		if exist := c.checkCustomVarExist(input.GetCustomVarID()); !exist {
			// 引用的自定义参数不存在
			c.appendNodeError(currentNodeID, fmt.Sprintf("引用的%s自定义参数不存在", input.GetCustomVarID()))
			return
		}
		// 统一收集引用的"API参数（自定义变量）"
		c.collectCustomVarIDsFromInput(input)
	case KEP_WF.InputSourceEnum_SYSTEM_VARIABLE:
		// sys.user_query
		// sys.chat_history
		// sys.current_time
		systemVariable := input.GetSystemVariable()
		log.InfoContextf(c.ctx, "checkInputAndCollect|Name:%s", systemVariable.GetName())
		log.InfoContextf(c.ctx, "checkInputAndCollect|DialogHistoryLimit:%s", systemVariable.GetDialogHistoryLimit())
		if exist := c.checkSystemVarExist(systemVariable.GetName()); !exist {
			// 引用的系统参数不存在
			c.appendNodeError(currentNodeID, fmt.Sprintf("引用的%s系统参数不存在", systemVariable.GetName()))
		}
	case KEP_WF.InputSourceEnum_NODE_INPUT_PARAM:
		// 当前只有循环节点中使用： a) 引用的工作流的输入变量的右值；b)循环体内的左值；b)循环体内的右值
		// 校验"输入参数"名称 是否是"当前节点定义的输入参数"
		if len(input.GetNodeInputParamName()) == 0 {
			return
		}
		if len(nodeInputParamNames) == 0 {
			c.appendNodeError(currentNodeID, "未定义输入参数")
			return
		}
		// 这里现在没法拿到前序所有自层级，只能校验第一层
		hasPrefix := false
		for _, name := range nodeInputParamNames {
			if strings.HasPrefix(input.GetNodeInputParamName(), name) {
				hasPrefix = true
				break
			}
		}
		if !hasPrefix {
			c.appendNodeError(currentNodeID, fmt.Sprintf("%s 未在输入参数中定义", input.GetNodeInputParamName()))
		}

	default:
		// ignore
	}
}

func (c *WfContext) collectCustomVarIDsFromInput(input *KEP_WF.Input) {
	if input == nil {
		return
	}
	if input.GetInputType() != KEP_WF.InputSourceEnum_CUSTOM_VARIABLE {
		return
	}
	customVarID := input.GetCustomVarID()
	if len(customVarID) == 0 {
		return
	}
	for _, varID := range c.customVarIDs {
		if customVarID == varID {
			return
		}
	}
	c.customVarIDs = append(c.customVarIDs, customVarID)
}

// 引用其他节点的输出
// 具体说明以pb中的 KEP_WF.ReferenceFromNode 为准
func (c *WfContext) checkReferenceOutput(currentNodeID string, ref *KEP_WF.ReferenceFromNode) {
	_, exist := c.nodeMap[ref.GetNodeID()]
	if !exist {
		c.appendNodeError(currentNodeID, fmt.Sprintf("不存在的节点(ID:%s)", ref.GetNodeID()))
		return
	}
	jsonPath := ref.GetJsonPath()
	if ok := isValidJSONPath(jsonPath); !ok {
		c.appendNodeError(currentNodeID, fmt.Sprintf("引用内容不合法[%s]", jsonPath))
	}
	//var err error
	//nodeType := refNode.GetNodeType().String()
	//switch refNode.GetNodeType() {
	//// json path验证
	//case KEP_WF.NodeType_PARAMETER_EXTRACTOR: // ${RefParameterID}
	//	for _, parameter := range refNode.GetParameterExtractorNodeData().GetParameters() {
	//		if jsonPath == parameter.GetRefParameterID() {
	//			return // 找到了
	//		}
	//	}
	//	// 如果找不到这个参数
	//	c.appendNodeError(currentNodeID, fmt.Sprintf("引用的参数节点[%s]里不存在参数(ID:%s)",
	//		refNode.GetNodeName(), jsonPath))
	//case KEP_WF.NodeType_LLM, KEP_WF.NodeType_LLM_KNOWLEDGE_QA: // output
	//	if jsonPath != OUTPUT {
	//		c.appendNodeError(currentNodeID, fmt.Sprintf("引用内容不合法[%s]，应该是%s", jsonPath, OUTPUT))
	//	}
	//case KEP_WF.NodeType_TAG_EXTRACTOR: // output or output.${TagID}
	//	if jsonPath == OUTPUT {
	//		return
	//	}
	//	for _, tag := range refNode.GetTagExtractorNodeData().GetTags() {
	//		if jsonPath == fmt.Sprintf("%s.%s", OUTPUT, tag.GetID()) {
	//			return
	//		}
	//	}
	//	c.appendNodeError(currentNodeID, fmt.Sprintf("引用内容不合法[%s]", jsonPath))
	//	log.WarnContextf(c.ctx, "checkReferenceOutput|currentNodeID:%s|nodeType:%s|refNode:%+v|invalid",
	//		currentNodeID, nodeType, refNode)
	//case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER, KEP_WF.NodeType_CODE_EXECUTOR, KEP_WF.NodeType_TOOL: // JSONPATH
	//	if ok := isValidJSONPath(jsonPath); !ok {
	//		c.appendNodeError(currentNodeID, fmt.Sprintf("引用内容不合法[%s]", jsonPath))
	//	}
	//case KEP_WF.NodeType_START, KEP_WF.NodeType_LOGIC_EVALUATOR, KEP_WF.NodeType_ANSWER: // 不应该有
	//	log.InfoContextf(c.ctx, "checkReferenceOutput|currentNodeID:%s|nodeType:%s|refNode:%+v",
	//		currentNodeID, nodeType, refNode)
	//default:
	//	err = errors.TaskFLowVerifyError(fmt.Sprintf("%s节点类型错误", nodeType))
	//}
	//if err != nil {
	//	log.WarnContextf(c.ctx, "checkReferenceOutput|currentNodeID:%s|nodeType:%s|refNode:%+v|err:%+v",
	//		currentNodeID, nodeType, refNode, err)
	//}
}

func isValidJSONPath(path string) bool {
	// 校验json path的语法
	_, err := jp.ParseString(path)
	return err == nil
}
