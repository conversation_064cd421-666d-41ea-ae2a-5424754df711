/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_checkJSONPathSyntax(t *testing.T) {

	tests := []struct {
		name string
		path string
		want bool
	}{
		{path: "Output", want: true},
		{path: "$.store.book[*].author", want: true},
		{path: "$..author", want: true},
		{path: "$.store.*", want: true},
		{path: "$.store..price", want: true},
		{path: "$..book[2]", want: true},
		{path: "$..book[-2]", want: true},
		{path: "$..book[0,1]", want: true},
		{path: "$..book[:2]", want: true},
		{path: "$..book[?(@.isbn)]", want: true},
		{path: "$.store.book[?(@.price < 10)]", want: true},
		{path: "$..book[?(@.price <= $['expensive'])]", want: true},
		{path: "$..*", want: true},
		{path: "$invalid path", want: false},
		{path: "output.context[0].DocID", want: true},
		{path: "output.faq[0].ID", want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, isValidJSONPath(tt.path), "isValidJSONPath(%v)", tt.path)
		})
	}
}
