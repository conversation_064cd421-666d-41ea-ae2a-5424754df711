/*
 * 2024-12-13
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func compareInputParams(ctx context.Context, list1, list2 []*KEP_WF.InputParam) bool {
	if len(list1) != len(list2) {
		log.WarnContextf(ctx, "compareInputParams false list1 len:%d,list2 len:%d", len(list1), len(list2))
		return false
	}

	paramMap1 := make(map[string]*KEP_WF.InputParam, len(list1))
	paramMap2 := make(map[string]*KEP_WF.InputParam, len(list2))
	for _, param1 := range list1 {
		paramMap1[param1.GetName()] = param1
	}
	for _, param2 := range list2 {
		paramMap2[param2.GetName()] = param2
	}
	if len(paramMap1) != len(paramMap2) {
		log.WarnContextf(ctx, "compareInputParams false paramMap1 len:%d,paramMap2 len:%d",
			len(paramMap1), len(paramMap2))
		return false
	}
	for name1, param1 := range paramMap1 {
		param2, ok := paramMap2[name1]
		if !ok || param2.GetType() != param1.GetType() {
			log.WarnContextf(ctx, "compareInputParams false param name or type not match, name:%s", name1)
			return false
		}
		// 两者都没有子集的时候，不比较；反之则递归比较子集
		if len(param1.GetSubInputs()) > 0 || len(param2.GetSubInputs()) > 0 {
			if !compareInputParams(ctx, param1.GetSubInputs(), param2.GetSubInputs()) {
				return false
			}
		}
	}
	return true
}

// compareInputParams 检查两个 InputParam 列表的 Name 数量和值是否相同
//func compareInputParams(list1, list2 []*KEP_WF.InputParam) bool {
//	names1 := extractNames(list1)
//	names2 := extractNames(list2)
//
//	if len(names1) != len(names2) {
//		return false
//	}
//
//	sort.Strings(names1)
//	sort.Strings(names2)
//
//	return reflect.DeepEqual(names1, names2)
//}
//
//// extractNames 提取结构体列表中的 Name 字段值
//func extractNames(list []*KEP_WF.InputParam) []string {
//	names := make([]string, len(list))
//	for i, item := range list {
//		names[i] = item.GetName()
//	}
//	return names
//}
