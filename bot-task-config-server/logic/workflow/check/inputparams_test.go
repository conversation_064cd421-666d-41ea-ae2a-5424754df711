/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
)

func TestCompareInputParams(t *testing.T) {
	reqID := idgenerator.NewUUID()
	ctx := trpc.BackgroundContext()
	util.WithRequestID(ctx, reqID)
	ctx = log.WithContextFields(ctx, "RequestID", reqID)

	type args struct {
		list1 []*KEP_WF.InputParam
		list2 []*KEP_WF.InputParam
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "1. 相同的",
			args: args{
				list1: []*KEP_WF.InputParam{
					{
						Name: "aa",
					},
					{
						Name: "bb",
					},
				},
				list2: []*KEP_WF.InputParam{
					{
						Name: "bb",
					},
					{
						Name: "aa",
					},
				},
			},
			want: true,
		},
		{
			name: "2. 不同的",
			args: args{
				list1: []*KEP_WF.InputParam{
					{
						Name: "aa",
					},
					{
						Name: "bb",
					},
				},
				list2: []*KEP_WF.InputParam{
					{
						Name: "bb",
					},
					{
						Name: "aa111",
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, compareInputParams(ctx, tt.args.list1, tt.args.list2), "compareInputParams(%v, %v)", tt.args.list1, tt.args.list2)
		})
	}
}
