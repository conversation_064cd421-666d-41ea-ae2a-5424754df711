/*
 * 2024-12-18
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

//func getValidOperatorMap(
//	expr *KEP_WF.LogicalExpression) map[KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum]struct{} {
//	opMap := make(map[KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum]struct{})
//	opMap[KEP_WF.LogicalExpression_ComparisonExpression_IS_SET] = struct{}{}
//	opMap[KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET] = struct{}{}
//	opMap[KEP_WF.LogicalExpression_ComparisonExpression_IN] = struct{}{}
//	opMap[KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN] = struct{}{}
//
//	switch expr.GetComparison().GetLeftType() {
//	case KEP_WF.TypeEnum_STRING:
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_EQ] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_NE] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS] = struct{}{}
//
//	case KEP_WF.TypeEnum_INT, KEP_WF.TypeEnum_FLOAT:
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_LT] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_LE] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_GT] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_GE] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_EQ] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_NE] = struct{}{}
//	case KEP_WF.TypeEnum_BOOL:
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_EQ] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_NE] = struct{}{}
//	case KEP_WF.TypeEnum_OBJECT, KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_INT,
//		KEP_WF.TypeEnum_ARRAY_FLOAT, KEP_WF.TypeEnum_ARRAY_BOOL, KEP_WF.TypeEnum_ARRAY_OBJECT:
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS] = struct{}{}
//		opMap[KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS] = struct{}{}
//	}
//	return opMap
//}

//func checkOpExprValid(expr *KEP_WF.LogicalExpression) bool {
//	switch expr.GetComparison().GetOperator() {
//	case KEP_WF.LogicalExpression_ComparisonExpression_IS_SET,
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET:
//		return true
//	}
//
//	return true
//	//if leftTypes, ok := opExpr[expr.GetComparison().GetOperator()]; ok {
//	//	if rightTypes, ok := leftTypes[expr.GetComparison().GetLeftType()]; ok {
//	//		rightInputType := KEP_WF.TypeEnum_STRING
//	//		switch expr.GetComparison().GetRight().GetInputType() {
//	//		// 后台右值的类型，这里不做检查，依靠前端的校验
//	//		case KEP_WF.InputSourceEnum_USER_INPUT:
//	//			rightInputType = KEP_WF.TypeEnum_STRING
//	//		}
//	//		if _, ok := rightTypes[rightInputType]; ok {
//	//			return true
//	//		}
//	//	}
//	//}
//	//return false
//}

//	https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121107251
//
// 结构层次：运算符 -> 左值类型 -> 右值类型
//
// map的说明：
// - key: 运算符
// ---- key: 左值的类型
// -------- key: 支持的右值的类型
//var opExpr = map[KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum]map[KEP_WF.TypeEnum]map[KEP_WF.TypeEnum]struct{}{
//	// 等于操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_EQ: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_STRING: {},
//			KEP_WF.TypeEnum_INT:    {},
//			KEP_WF.TypeEnum_FLOAT:  {},
//			KEP_WF.TypeEnum_BOOL:   {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.TypeEnum_INT:    {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.TypeEnum_FLOAT:  {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.TypeEnum_BOOL:   {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//		},
//	},
//
//	// 不等于操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_NE: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_STRING: {},
//			KEP_WF.TypeEnum_INT:    {},
//			KEP_WF.TypeEnum_FLOAT:  {},
//			KEP_WF.TypeEnum_BOOL:   {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.TypeEnum_INT:    {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.TypeEnum_FLOAT:  {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.TypeEnum_BOOL:   {},
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//		},
//	},
//
//	// 属于操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_IN: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//		},
//	},
//
//	// 不属于操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//		},
//	},
//
//	// 包含操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//			KEP_WF.TypeEnum_STRING:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//			KEP_WF.TypeEnum_INT:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//			KEP_WF.TypeEnum_FLOAT:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//			KEP_WF.TypeEnum_BOOL:       {},
//		},
//	},
//
//	// 不包含操作符
//	KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.TypeEnum_STRING: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.TypeEnum_ARRAY_STRING: {},
//			KEP_WF.TypeEnum_STRING:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.TypeEnum_ARRAY_INT: {},
//			KEP_WF.TypeEnum_INT:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.TypeEnum_ARRAY_FLOAT: {},
//			KEP_WF.TypeEnum_FLOAT:       {},
//		},
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.TypeEnum_ARRAY_BOOL: {},
//			KEP_WF.TypeEnum_BOOL:       {},
//		},
//	},
//}

// 结构层次：左值类型 -> 右值类型 -> 运算符
//
// - key: 左值的类型
// ---- key: 右值的类型
// -------- key: 运算符
//var opMap1 = map[KEP_WF.TypeEnum]map[KEP_WF.TypeEnum]map[KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum]struct{}{
//	KEP_WF.TypeEnum_STRING: {
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_IN:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN: {},
//		},
//	},
//	KEP_WF.TypeEnum_INT: {
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_IN:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN: {},
//		},
//	},
//	KEP_WF.TypeEnum_FLOAT: {
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_IN:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN: {},
//		},
//	},
//	KEP_WF.TypeEnum_BOOL: {
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ: {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE: {},
//		},
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.LogicalExpression_ComparisonExpression_IN:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN: {},
//		},
//	},
//	KEP_WF.TypeEnum_ARRAY_STRING: {
//		KEP_WF.TypeEnum_ARRAY_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//		KEP_WF.TypeEnum_STRING: {
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//	},
//	KEP_WF.TypeEnum_ARRAY_INT: {
//		KEP_WF.TypeEnum_ARRAY_INT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//		KEP_WF.TypeEnum_INT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//	},
//	KEP_WF.TypeEnum_ARRAY_FLOAT: {
//		KEP_WF.TypeEnum_ARRAY_FLOAT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//		KEP_WF.TypeEnum_FLOAT: {
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//	},
//	KEP_WF.TypeEnum_ARRAY_BOOL: {
//		KEP_WF.TypeEnum_ARRAY_BOOL: {
//			KEP_WF.LogicalExpression_ComparisonExpression_EQ:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NE:           {},
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//		KEP_WF.TypeEnum_BOOL: {
//			KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:     {},
//			KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS: {},
//		},
//	},
//}

// 填充与否应该是任意类型都可以支持
//var setOperatorMap = map[KEP_WF.TypeEnum]map[KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum]struct{}{
//	KEP_WF.TypeEnum_STRING: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_INT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_FLOAT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_BOOL: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_OBJECT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_ARRAY_STRING: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_ARRAY_INT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_ARRAY_FLOAT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_ARRAY_BOOL: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//	KEP_WF.TypeEnum_ARRAY_OBJECT: {
//		KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:  {},
//		KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET: {},
//	},
//}
