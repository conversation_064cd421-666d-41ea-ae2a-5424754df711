/*
 * 2024-10-15
 * Copyright (c) 2024. x<PERSON>huiquan@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"unicode/utf8"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseAnswerNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetAnswerNodeData()
	// --story=122599567 回复节点支持配置输出变量，回复内容改为非必填，但需要做回复内容和新增输出变量不能同时为空的校验。
	if len(node.GetAnswer()) == 0 && len(wfn.GetOutputs()) == 0 {
		c.appendNodeError(nodeID, "回复节点内容和输出变量不能同时为空")
		return
	}
	if len(node.GetAnswer()) == 0 {
		// 仅配置输出变量无需对回复内容进行校验
		return
	}
	// 对回复内容进行校验
	max := config.GetMainConfig().VerifyWorkflow.AnswerMaxLen
	if utf8.RuneCountInString(c.removeHTMLTags(node.GetAnswer())) > max {
		c.appendNodeError(nodeID, fmt.Sprintf("回复节点内容不能超过%d", max))
	}

	c.parseFromString(wfn.GetInputs(), nodeID, node.GetAnswer())

	c.checkAnswerAndEndNodeOutputs(nodeID, wfn.GetOutputs(), wfn.GetNodeType())

}
