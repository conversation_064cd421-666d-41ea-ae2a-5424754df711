/*
 * 2024-10-15
 * Copyright (c) 2024. xinghuiquan@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"strings"
	"unicode/utf8"
)

// 检查代码节点输入参数，输出参数个数以及代码长度
func (c *WfContext) checkLenLimit(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetCodeExecutorNodeData()
	codeLen := config.GetMainConfig().VerifyWorkflow.CodeNodeCodeLen
	if len(strings.TrimSpace(node.GetCode())) == 0 {
		c.appendNodeError(nodeID, "代码为空")
	} else if utf8.RuneCountInString(node.GetCode()) > codeLen {
		c.appendNodeError(nodeID, fmt.Sprintf("代码字符数超出%d", codeLen))
		//} else {
		//	errMsg := checkCode(c.ctx, node.GetCode())
		//	if len(errMsg) > 0 {
		//		c.appendNodeError(nodeID, fmt.Sprintf("代码存在错误:%s", errMsg))
		//	}
	}

	inputLen := config.GetMainConfig().VerifyWorkflow.CodeNodeInputLen
	if len(wfn.GetInputs()) > inputLen {
		c.appendNodeError(nodeID, fmt.Sprintf("输入参数个数超出%d", inputLen))
	}
	outputLen := config.GetMainConfig().VerifyWorkflow.CodeNodeOutputLen
	if len(wfn.GetOutputs()) > outputLen {
		c.appendNodeError(nodeID, fmt.Sprintf("输出参数个数超出%d", outputLen))
	}

}

func (c *WfContext) parseCodeExecutorNode(wfn *KEP_WF.WorkflowNode) {
	c.checkLenLimit(wfn)
}

//func checkCode(ctx context.Context, code string) string {
//	checkRsp, err := rpc.CheckCode(ctx, &bot_exec_pycode_server.CheckCodeRequest{
//		FuncName: rpc.DefaultEntryFuncName,
//		FuncCode: code,
//	})
//	if err != nil { // 调用失败时，先放行
//		log.Errorf("CheckCode err:%v", err)
//		return ""
//	}
//	if checkRsp.Code != 0 {
//		return checkRsp.Message
//	}
//	return ""
//}
