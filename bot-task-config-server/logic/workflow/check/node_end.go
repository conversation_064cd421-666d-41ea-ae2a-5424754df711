/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseEnd(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	//node := wfn.GetEndNodeData()

	if len(wfn.GetInputs()) > 0 {
		c.appendNodeError(nodeID, "结束节点不能有输入变量")
	}

	c.checkAnswerAndEndNodeOutputs(nodeID, wfn.GetOutputs(), wfn.GetNodeType())

}

func (c *WfContext) checkAnswerAndEndNodeOutputs(nodeID string, outputs []*KEP_WF.OutputParam, nodeType KEP_WF.NodeType) {
	for _, output := range outputs {
		// output.GetValue() 允许为空, 如：
		//  - Output  <这里值是空的> Object
		//     - Name <张三> String
		//
		//if output.GetValue() == nil {
		//c.appendNodeError(nodeID, "数据来源为空")
		//}

		if len(output.GetTitle()) == 0 {
			c.appendNodeError(nodeID, "参数名为空")
			return
		}

		if len(output.GetProperties()) > 0 {
			c.checkAnswerAndEndNodeOutputs(nodeID, output.GetProperties(), nodeType)
		}

		c.checkInputAndCollect(nodeID, output.GetValue())
		if !c.inputParamNameRegexp.MatchString(output.GetTitle()) {
			c.appendNodeError(nodeID, fmt.Sprintf("%s 参数名不合法", output.GetTitle()))
		}
	}
}
