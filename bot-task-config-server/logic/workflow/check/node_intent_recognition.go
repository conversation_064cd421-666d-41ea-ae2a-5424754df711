/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"unicode/utf8"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseIntentRecognition(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetIntentRecognitionNodeData()
	if len(node.GetModelName()) == 0 {
		c.appendNodeError(nodeID, "模型为空")
	}
	temperatureMax := config.GetMainConfig().VerifyWorkflow.LLMNodeTemperatureMax
	if node.GetTemperature() < 0 || node.GetTemperature() > temperatureMax {
		c.appendNodeError(nodeID, fmt.Sprintf("温度范围为0~%f", temperatureMax))
	}
	topPMax := config.GetMainConfig().VerifyWorkflow.LLMNodeTopPMax
	if node.GetTopP() < 0 || node.GetTopP() > topPMax {
		c.appendNodeError(nodeID, fmt.Sprintf("TopP范围为0~%f", topPMax))
	}

	if len(node.GetPrompt()) == 0 {
		c.appendNodeError(nodeID, "待识别意图不应为空")
	}

	intentPrompt := config.GetMainConfig().VerifyWorkflow.IntentPrompt
	if len(node.GetPrompt()) > 0 &&
		utf8.RuneCountInString(c.removeHTMLTags(node.GetPrompt())) > intentPrompt {
		c.appendNodeError(nodeID, fmt.Sprintf("待识别意图的内容不能超过%d", intentPrompt))
	}

	c.parseFromString(wfn.GetInputs(), nodeID, node.GetPrompt())

	//intentMax := config.GetMainConfig().VerifyWorkflow.IntentMax
	//if len(node.GetIntents()) > intentMax {
	//	c.appendNodeError(nodeID, fmt.Sprintf("意图识别个数不能超过%d", intentMax))
	//}
	for i, intent := range node.GetIntents() {
		index := i + 1 // 给前端展示的错误提示
		if i == len(node.GetIntents())-1 {
			// 最后一个意图，即"其他意图"（没有命中没有任何一个填充意图），意图内容可为空
		} else {
			if len(intent.GetName()) == 0 {
				c.appendNodeError(nodeID, fmt.Sprintf("第%d个意图名称为空", index))
			}
		}
		if len(intent.GetNextNodeIDs()) == 0 {
			c.appendNodeError(nodeID, fmt.Sprintf("第%d个意图的连线不应为空", index))
		}
	}
}
