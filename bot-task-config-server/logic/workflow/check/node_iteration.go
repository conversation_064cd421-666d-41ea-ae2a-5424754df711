/*
 * 2024-12-13
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// DefaultInnerInputParamNames 内置的
var DefaultInnerInputParamNames = []string{
	"Loop",
	"Loop.Index",
	"Loop.Output",
	"Loop.ErrorCode",
	"Loop.Log",
}

func (c *WfContext) parseIteration(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetIterationNodeData()

	if len(node.GetWorkflowID()) == 0 {
		c.appendNodeError(nodeID, "引用的工作流为空")
		return
	}

	// 当前节点用到的"输入参数"的名字
	currentNodeInputParamNames := make([]string, 0)
	exists := make(map[string]bool)
	existArrays := make(map[string]bool)
	for _, param := range wfn.GetInputs() {
		// 目前除开始节点外，其它节点（循环节点）不支持输入参数（类型为array<object>及object）的子级，因此不需要处理
		currentNodeInputParamNames = append(currentNodeInputParamNames, param.GetName())
		exists[param.GetName()] = true
		switch param.GetType() {
		case KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_INT,
			KEP_WF.TypeEnum_ARRAY_FLOAT, KEP_WF.TypeEnum_ARRAY_BOOL, KEP_WF.TypeEnum_ARRAY_OBJECT:
			existArrays[param.GetName()] = true
			inputItemName := param.GetName() + ".Item"
			exists[inputItemName] = true
			currentNodeInputParamNames = append(currentNodeInputParamNames, inputItemName)
		}
	}

	innerParamNames := config.GetMainConfig().VerifyWorkflow.IterationInnerInputParamNames
	if len(innerParamNames) == 0 {
		innerParamNames = DefaultInnerInputParamNames
	}
	for _, name := range innerParamNames {
		if !exists[name] {
			// 在这里把几种内置的 变量名称也加进去用于校验
			currentNodeInputParamNames = append(currentNodeInputParamNames, name)
		}
	}

	// 1. 检查被引用工作流的输入参数：
	for _, input := range node.GetRefInputs() {
		if input.GetName() == "" {
			c.appendNodeError(nodeID, "变量名称不能为空")
		}
		// a. 校验 Input； b. 收集引用的"API参数（自定义变量）"
		c.checkInputAndCollectForSupportInnerInputParams(nodeID, currentNodeInputParamNames, input.GetInput())
	}

	// 2. 引用的工作流的关联关系
	c.collectWorkflowReference(nodeID, node.GetWorkflowID())
	// 3. 收集引用的工作流的输入，用于最后统一检查与引用的工作流中定义的输入是否一致
	c.collectItemToRefWorkflowInputs(node.GetWorkflowID(), node.GetRefInputs())
	// 4. 收集引用的工作流的输出，用于最后统一检查与引用的工作流中定义的输出是否一致
	c.collectItemToRefWorkflowOutputs(node.GetWorkflowID(), wfn.GetOutputs())

	// 5. 检查循环方式
	switch node.GetIterationMode() {
	case KEP_WF.IterationNodeData_ALL:
		// 校验SpecifiedTraversalVariable必填，且填写的变量必须是array类型的
		if node.GetSpecifiedTraversalVariable() == "" {
			c.appendNodeError(nodeID, "指定遍历的数组未选择")
		}
		if node.GetSpecifiedTraversalVariable() != "" && !existArrays[node.GetSpecifiedTraversalVariable()] {
			c.appendNodeError(nodeID, "指定遍历数组不存在")
		}
		return
	case KEP_WF.IterationNodeData_BY_CONDITION: // 按条件循环时的校验
		//c.checkIterationOperator(nodeID, 0, 0, node.GetCondition())
		c.checkIterationLogicInputs(nodeID, currentNodeInputParamNames, node.GetCondition(), wfn.GetNodeType())
	}
}

// 左值右值与 操作符支持与否的校验
//func (c *WfContext) checkIterationOperator(nodeID string, layer int, index int, expr *KEP_WF.LogicalExpression) {
//	if expr == nil {
//		return
//	}
//	if expr.GetLogicalOperator() == KEP_WF.LogicalExpression_OR ||
//		expr.GetLogicalOperator() == KEP_WF.LogicalExpression_AND {
//		for i, compoundExpr := range expr.GetCompound() {
//			c.checkIterationOperator(nodeID, layer+1, i, compoundExpr)
//		}
//		return
//	}
//
//	// 应该是每次都判断不然切换之后，在AI能用的，在精准方式就不能用了
//	//if expr.GetComparison().GetMatchType() != KEP_WF.LogicalExpression_ComparisonExpression_PRECISE {
//	//	return
//	//}
//	//opMap := getValidOperatorMap(expr)
//	//if _, exist := opMap[expr.GetComparison().GetOperator()]; !exist {
//	//	log.InfoContextf(c.ctx, "checkIterationOperator|layer:%d|index:%d|input:%+v", layer, index,
//	//		expr.GetComparison().GetLeft())
//	//	c.appendNodeError(nodeID, fmt.Sprintf("第%d个条件中的左值数据类型无法使用该运算符", index+1))
//	//}
//}

func (c *WfContext) checkIterationLogicInputs(nodeID string, currentNodeInputParamNames []string,
	expr *KEP_WF.LogicalExpression, nodeType KEP_WF.NodeType) {
	if expr == nil {
		return
	}

	switch expr.GetLogicalOperator() {
	case KEP_WF.LogicalExpression_OR, KEP_WF.LogicalExpression_AND:
		if expr.GetCompound() == nil {
			return
		}
		for _, compoundExpr := range expr.GetCompound() {
			c.checkIterationLogicInputs(nodeID, currentNodeInputParamNames, compoundExpr, nodeType)
		}
	case KEP_WF.LogicalExpression_UNSPECIFIED:
		leftInput := expr.GetComparison().GetLeft()
		if leftInput != nil {
			c.checkInputAndCollectForSupportInnerInputParams(nodeID, currentNodeInputParamNames, leftInput)
		}

		switch expr.GetComparison().GetOperator() {
		case KEP_WF.LogicalExpression_ComparisonExpression_IS_SET,
			KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET:
			// 如果是 "已填充"类型，那就允许为空，不检查右值
			return
		}
		rightInput := expr.GetComparison().GetRight()
		if rightInput != nil {
			c.checkInputAndCollectForSupportInnerInputParams(nodeID, currentNodeInputParamNames, rightInput)
		}
	}
}
