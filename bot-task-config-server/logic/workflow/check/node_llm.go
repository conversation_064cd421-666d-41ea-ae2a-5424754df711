/*
 * 2024-10-15
 * Copyright (c) 2024. x<PERSON>huiquan@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"unicode/utf8"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseLLMNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetLLMNodeData()
	if len(node.GetModelName()) == 0 {
		c.appendNodeError(nodeID, "模型为空")
	}
	temperatureMax := config.GetMainConfig().VerifyWorkflow.LLMNodeTemperatureMax
	if node.GetTemperature() < 0 || node.GetTemperature() > temperatureMax {
		c.appendNodeError(nodeID, fmt.Sprintf("温度范围为0~%f", temperatureMax))
	}
	topPMax := config.GetMainConfig().VerifyWorkflow.LLMNodeTopPMax
	if node.GetTopP() < 0 || node.GetTopP() > topPMax {
		c.appendNodeError(nodeID, fmt.Sprintf("TopP范围为0~%f", topPMax))
	}
	//maxTokensMax := config.GetMainConfig().VerifyWorkflow.LLMNodeMaxTokensMax
	//if node.GetMaxTokens() < 0 || node.GetMaxTokens() > maxTokensMax {
	//	c.appendNodeError(nodeID, fmt.Sprintf("最大回复token范围为0~%d", maxTokensMax))
	//}

	if len(node.GetPrompt()) == 0 {
		c.appendNodeError(nodeID, "提示词不应为空")
	}

	if len(node.GetModelName()) > 0 {
		c.checkNodePrompt(nodeID, node.GetPrompt())
	}
	c.parseFromString(wfn.GetInputs(), nodeID, node.GetPrompt())
}

func (c *WfContext) checkNodePrompt(nodeID, prompt string) {
	modelNameLimit := config.GetMainConfig().VerifyWorkflow.NodePromptMaxLen
	if utf8.RuneCountInString(c.removeHTMLTags(prompt)) > modelNameLimit {
		c.appendNodeError(nodeID, fmt.Sprintf("提示词不能超过%d", modelNameLimit))
	}
}

//func getPromptLimitWithModelName(ctx context.Context, modelName string) (int, error) {
//	limitNum, err := db.GetAppChatInputNum(ctx, modelName)
//	return int(limitNum), err
//}
