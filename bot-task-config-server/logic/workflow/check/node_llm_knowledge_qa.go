/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON>quan@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"strconv"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseLLMKnowledgeQANode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetLLMKnowledgeQANodeData()
	if len(node.GetModelName()) == 0 {
		c.appendNodeError(nodeID, "模型为空")
	}
	if len(node.GetQuery()) == 0 {
		c.appendNodeError(nodeID, "提示词不应为空")
	}
	if len(node.GetModelName()) > 0 {
		c.checkNodePrompt(nodeID, node.GetQuery())
	}
	c.parseFromString(wfn.GetInputs(), nodeID, node.GetQuery())
	if !node.GetAllKnowledge() && len(node.GetKnowledgeList()) == 0 {
		c.checkLLMKnowledgeQA(nodeID, node)
		c.collectKnowledgeRefFromQANode(nodeID, node)
	}
	for _, knowledge := range node.GetKnowledgeList() {
		if len(knowledge.GetKnowledgeBizID()) == 0 {
			c.appendNodeError(nodeID, "知识库ID不应为空")
		}
		if knowledge.GetKnowledgeType() != KEP_WF.Knowledge_DEFAULT &&
			knowledge.GetKnowledgeType() != KEP_WF.Knowledge_SHARED {
			c.appendNodeError(nodeID, "知识库类型不合法")
		}
		c.checkLLMKnowledgeQANew(nodeID, knowledge)
		c.collectKnowledgeRefFromQANodeNew(nodeID, knowledge)
	}
}

func (c *WfContext) checkLLMKnowledgeQA(nodeID string, node *KEP_WF.LLMKnowledgeQANodeData) {
	if node.GetFilter() != KEP_WF.KnowledgeFilter_DOC_AND_QA {
		return
	}
	verify := config.GetMainConfig().VerifyWorkflow
	nodeIdsLen := node.GetDocBizIDs()
	if len(nodeIdsLen) > 0 {
		docRecallCountMin := verify.DocRecallCountMin
		docRecallCountMax := verify.DocRecallCountMax
		bizIdsLen := verify.DocBizIdsMax
		if len(nodeIdsLen) > bizIdsLen {
			c.appendNodeError(nodeID, fmt.Sprintf("选择文档数量不应超过最大数量限制%d", bizIdsLen))
		}
		if node.GetDocRecallCount() < docRecallCountMin || node.GetDocRecallCount() > docRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档召回数量%d~%d", docRecallCountMin, docRecallCountMax))
		}
		docConfidenceMax := verify.DocConfidenceMax
		if node.GetDocConfidence() < 0 || node.GetDocConfidence() > docConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档检索匹配度0~%f", docConfidenceMax))
		}
	}
	if node.GetAllQA() {
		qaRecallCountMin := verify.QARecallCountMin
		qaRecallCountMax := verify.QARecallCountMax

		if node.GetQARecallCount() < qaRecallCountMin || node.GetQARecallCount() > qaRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答召回数量%d~%d", qaRecallCountMin, qaRecallCountMax))
		}
		qaConfidenceMax := verify.QAConfidenceMax
		if node.GetQAConfidence() < 0 || node.GetQAConfidence() > qaConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答检索匹配度0~%f", qaConfidenceMax))
		}
	}
}

func (c *WfContext) checkLLMKnowledgeQANew(nodeID string, knowledge *KEP_WF.Knowledge) {
	if knowledge.GetFilter() != KEP_WF.KnowledgeFilter_DOC_AND_QA {
		return
	}
	verify := config.GetMainConfig().VerifyWorkflow
	nodeIdsLen := knowledge.GetDocBizIDs()
	if len(nodeIdsLen) > 0 {
		docRecallCountMin := verify.DocRecallCountMin
		docRecallCountMax := verify.DocRecallCountMax
		bizIdsLen := verify.DocBizIdsMax
		if len(nodeIdsLen) > bizIdsLen {
			c.appendNodeError(nodeID, fmt.Sprintf("选择文档数量不应超过最大数量限制%d", bizIdsLen))
		}
		if knowledge.GetDocRecallCount() < docRecallCountMin || knowledge.GetDocRecallCount() > docRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档召回数量%d~%d", docRecallCountMin, docRecallCountMax))
		}
		docConfidenceMax := verify.DocConfidenceMax
		if knowledge.GetDocConfidence() < 0 || knowledge.GetDocConfidence() > docConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档检索匹配度0~%f", docConfidenceMax))
		}
	}
	if knowledge.GetAllQA() {
		qaRecallCountMin := verify.QARecallCountMin
		qaRecallCountMax := verify.QARecallCountMax

		if knowledge.GetQARecallCount() < qaRecallCountMin || knowledge.GetQARecallCount() > qaRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答召回数量%d~%d", qaRecallCountMin, qaRecallCountMax))
		}
		qaConfidenceMax := verify.QAConfidenceMax
		if knowledge.GetQAConfidence() < 0 || knowledge.GetQAConfidence() > qaConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答检索匹配度0~%f", qaConfidenceMax))
		}
	}
}

func (c *WfContext) collectKnowledgeRefFromQANode(nodeID string, node *KEP_WF.LLMKnowledgeQANodeData) {
	switch node.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		c.collectKnowledgeRefAll(nodeID)
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		c.collectKnowledgeRefDocAndQA(nodeID, node.GetDocBizIDs(), node.GetAllQA())
	case KEP_WF.KnowledgeFilter_TAG:
		if len(node.GetLabels().GetLabels()) == 0 {
			c.appendNodeError(nodeID, "未选择标签")
		}
		tagMax := config.GetMainConfig().VerifyWorkflow.KnowledgeTagMax
		if len(node.GetLabels().GetLabels()) > tagMax {
			c.appendNodeError(nodeID, fmt.Sprintf("标签最多可配置%d个", tagMax))
		}
		c.collectKnowledgeRefTag(nodeID, node.GetLabels().GetLabels())
	}
}

func (c *WfContext) collectKnowledgeRefFromQANodeNew(nodeID string, knowledge *KEP_WF.Knowledge) {
	switch knowledge.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		c.collectKnowledgeRefAll(nodeID)
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		c.collectKnowledgeRefDocAndQA(nodeID, knowledge.GetDocBizIDs(), knowledge.GetAllQA())
	case KEP_WF.KnowledgeFilter_TAG:
		if len(knowledge.GetLabels().GetLabels()) == 0 {
			c.appendNodeError(nodeID, "未选择标签")
		}
		tagMax := config.GetMainConfig().VerifyWorkflow.KnowledgeTagMax
		if len(knowledge.GetLabels().GetLabels()) > tagMax {
			c.appendNodeError(nodeID, fmt.Sprintf("标签最多可配置%d个", tagMax))
		}
		c.collectKnowledgeRefTag(nodeID, knowledge.GetLabels().GetLabels())
	}
}

func (c *WfContext) collectKnowledgeRefAll(nodeID string) {
	ref := entity.WorkflowRefKnowledgeParams{}
	ref.WorkflowID = c.Workflow.GetWorkflowID()
	ref.NodeID = nodeID
	ref.Type = entity.WorkflowRefKnowledgeTypeAll
	c.knowledgeRef = append(c.knowledgeRef, ref)
}

func (c *WfContext) collectKnowledgeRefDocAndQA(nodeID string, docIDs []string, allQA bool) {
	if len(docIDs) == 0 && !allQA {
		c.appendNodeError(nodeID, "未选择文档或问答")
	}
	for _, docID := range docIDs {
		ref := entity.WorkflowRefKnowledgeParams{}
		ref.WorkflowID = c.Workflow.GetWorkflowID()
		ref.NodeID = nodeID
		ref.Type = entity.WorkflowRefKnowledgeTypeDoc
		ref.BizId = docID
		c.knowledgeRef = append(c.knowledgeRef, ref)
	}
	if allQA {
		ref := entity.WorkflowRefKnowledgeParams{}
		ref.WorkflowID = c.Workflow.GetWorkflowID()
		ref.Type = entity.WorkflowRefKnowledgeTypeQa
		c.knowledgeRef = append(c.knowledgeRef, ref)
	}
}

func (c *WfContext) collectKnowledgeRefTag(nodeID string, labels []*KEP_WF.KnowledgeAttrLabelRefer) {
	buildRef := func(attributeBizID uint64) entity.WorkflowRefKnowledgeParams {
		ref := entity.WorkflowRefKnowledgeParams{}
		ref.WorkflowID = c.Workflow.GetWorkflowID()
		ref.NodeID = nodeID
		ref.Type = entity.WorkflowRefKnowledgeTypeTag
		ref.BizId = strconv.FormatUint(attributeBizID, 10)
		return ref
	}

	for _, label := range labels {
		if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_INPUT_PARAM {
			for _, inputParamName := range label.GetInputParamNames() {
				ref := buildRef(label.GetAttributeBizID())
				ref.InputParamName = inputParamName
				c.knowledgeRef = append(c.knowledgeRef, ref)
			}
		} else {
			for _, labelBizID := range label.GetLabelBizIDs() {
				ref := buildRef(label.GetAttributeBizID())
				ref.LabelId = strconv.FormatUint(labelBizID, 10)
				c.knowledgeRef = append(c.knowledgeRef, ref)
			}
		}
	}
}

//func (c *WfContext) collectKnowledgeRef(ref entity.WorkflowRefKnowledgeParams) {
//	c.knowledgeRef = append(c.knowledgeRef, ref)
//
//}
//func (c *WfContext) collectDocIDs(docIDs []string) {
//	if len(docIDs) == 0 {
//		return
//	}
//	c.docIDs = append(c.docIDs, docIDs...)
//}
//
//func (c *WfContext) collectKnowledgeAttrLabels(labels []*KEP_WF.KnowledgeAttrLabelRefer) {
//	c.knowledgeAttrLabels = append(c.knowledgeAttrLabels, labels...)
//}
