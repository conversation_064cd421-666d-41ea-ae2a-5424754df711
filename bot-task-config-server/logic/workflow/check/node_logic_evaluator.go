/*
 * 2024-10-15
 * Copyright (c) 2024. xinghuiquan@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseLogicEvaluatorNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetLogicEvaluatorNodeData()

	// 最少由 一个 if 和 一个 else组成； 所以至少要2个元素
	if len(node.GetGroup()) < 2 {
		log.WarnContext(c.ctx, "parseLogicEvaluatorNode|nodeID:%s|groups:%+v", nodeID, node.GetGroup())
		return
	}
	maxGroup := config.GetMainConfig().VerifyWorkflow.LogicGroupCountMax
	if len(node.GetGroup()) > maxGroup {
		log.WarnContext(c.ctx, "parseLogicEvaluatorNode|nodeID:%s|groups.len:%d", nodeID, len(node.GetGroup()))
		c.appendNodeError(nodeID, fmt.Sprintf("条件组最多%d个", maxGroup))
	}
	for i, group := range node.GetGroup() {
		//if i == len(node.GetGroup())-1 {
		//	continue // 最后一个条件，即else，可以为空的NextNodeIDs
		//} // 2024-11-07 注释，最后一个条件不允许为空
		if len(group.GetNextNodeIDs()) == 0 {
			c.appendNodeError(nodeID, fmt.Sprintf("第%d个条件的连线不应为空", i+1))
		}

		//c.checkPreciseOperator(nodeID, i, group.GetLogical())
		c.checkLogicInputs(nodeID, group.GetLogical(), wfn.GetNodeType())
	}

}

//func (c *WfContext) checkPreciseOperator(nodeID string, index int, expr *KEP_WF.LogicalExpression) {
//	if expr == nil {
//		return
//	}
//	if expr.GetLogicalOperator() == KEP_WF.LogicalExpression_OR ||
//		expr.GetLogicalOperator() == KEP_WF.LogicalExpression_AND {
//		for _, compoundExpr := range expr.GetCompound() {
//			c.checkPreciseOperator(nodeID, index, compoundExpr)
//		}
//		return
//	}
//
//	// 应该是每次都判断？不然切换之后，在AI能用的，在精准方式就不能用了
//	//if expr.GetComparison().GetMatchType() != KEP_WF.LogicalExpression_ComparisonExpression_PRECISE {
//	//	return
//	//}
//	//opMap := getValidOperatorMap(expr)
//	//if _, exist := opMap[expr.GetComparison().GetOperator()]; !exist {
//	//	c.appendNodeError(nodeID, fmt.Sprintf("第%d个条件中的左值数据类型无法使用该运算符", index+1))
//	//}
//}

func (c *WfContext) checkLogicInputs(nodeID string, expr *KEP_WF.LogicalExpression, nodeType KEP_WF.NodeType) {
	if expr == nil {
		return
	}

	switch expr.GetLogicalOperator() {
	case KEP_WF.LogicalExpression_OR, KEP_WF.LogicalExpression_AND:
		if expr.GetCompound() == nil {
			return
		}
		for _, compoundExpr := range expr.GetCompound() {
			c.checkLogicInputs(nodeID, compoundExpr, nodeType)
		}
	case KEP_WF.LogicalExpression_UNSPECIFIED:
		leftInput := expr.GetComparison().GetLeft()
		if leftInput != nil {
			c.checkInputAndCollect(nodeID, leftInput)
		}

		rightInput := expr.GetComparison().GetRight()
		if rightInput == nil {
			// 右值为空直接返回
			return
		}
		switch expr.GetComparison().GetOperator() {
		case KEP_WF.LogicalExpression_ComparisonExpression_IS_SET,
			KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET:
			// 如果是 "已填充/未填充"类型，那就允许为空，不检查右值
			return
		case KEP_WF.LogicalExpression_ComparisonExpression_GT,
			KEP_WF.LogicalExpression_ComparisonExpression_GE,
			KEP_WF.LogicalExpression_ComparisonExpression_LT,
			KEP_WF.LogicalExpression_ComparisonExpression_LE:
			// 如果是 "大于/大于等于/小于/小于等于"类型，左值只能为int或者float类型
			switch expr.GetComparison().GetLeftType() {
			case KEP_WF.TypeEnum_INT, KEP_WF.TypeEnum_FLOAT:
				// do nothing
			default:
				c.appendNodeError(nodeID, "比较运算符仅支持int类型或float类型")
			}
			// 不论左值的类型，都需要继续检查右值
			fallthrough
		default:
			c.checkInputAndCollect(nodeID, rightInput)
		}

		// 比较运算符需要二次检查右值
		switch expr.GetComparison().GetOperator() {
		case KEP_WF.LogicalExpression_ComparisonExpression_GT,
			KEP_WF.LogicalExpression_ComparisonExpression_GE,
			KEP_WF.LogicalExpression_ComparisonExpression_LT,
			KEP_WF.LogicalExpression_ComparisonExpression_LE:
			if rightInput.GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
				// 如果是 "大于/大于等于/小于/小于等于"类型 且 右值为用户输入类型，需要校验右值是否能够转化成左值对应的类型
				for _, v := range rightInput.GetUserInputValue().GetValues() {
					if len(v) == 0 {
						continue
					}
					switch expr.GetComparison().GetLeftType() {
					case KEP_WF.TypeEnum_INT:
						// 检查右值是否能转换为int64类型
						_, err := strconv.ParseInt(v, 10, 64)
						if err != nil {
							c.appendNodeError(nodeID, "比较运算符右值无法转换为64位int类型")
						}
					case KEP_WF.TypeEnum_FLOAT:
						// 检查右值是否能转换为float64类型
						_, err := strconv.ParseFloat(v, 64)
						if err != nil {
							c.appendNodeError(nodeID, "比较运算符右值无法转换为64位float类型")
						}
					case KEP_WF.TypeEnum_STRING:
						// 左值为字符串类型时，右值无需检查
						fallthrough
					default:
						// 其余类型跳过检查
						continue
					}
				}
			}
		}
	}
}
