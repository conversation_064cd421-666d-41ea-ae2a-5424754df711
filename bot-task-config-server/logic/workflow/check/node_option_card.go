/*
 * 2024-12-13
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"unicode/utf8"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// 1. 并行判断
// 2. 校验：
//		问题长度
//		问题中的变量
//		选项个数
//		选项长度
//		选项中的变量
//		NextNodeID判断
//		从inputs中收集使用到的自定义变量（API参数），从外层的公共inputs收集和校验

func (c *WfContext) parseOptionCardNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetOptionCardNodeData()

	c.parseFromString(wfn.GetInputs(), nodeID, node.GetQuestion())

	// 获取选项卡类型
	cardFrom := node.GetCardFrom()
	// 如果是动态选项卡
	if cardFrom == KEP_WF.OptionCardNodeData_DYNAMIC {
		dynamicRefInputName := node.GetDynamicOptionsRefInputName()
		// 判断选项卡变量类型
		inputs := wfn.GetInputs()
		for _, v := range inputs {
			if v.GetName() == dynamicRefInputName {
				if v.GetType() != KEP_WF.TypeEnum_ARRAY_STRING {
					c.appendNodeError(nodeID, fmt.Sprintf("动态选项卡输入参数：%s 应该为%s类型",
						dynamicRefInputName, KEP_WF.TypeEnum_ARRAY_STRING.String()))
				}
			}
		}
		// 动态选项卡连线不为空
		if len(node.GetDynamicOptionsRefNextNodeIDs()) == 0 {
			c.appendNodeError(nodeID, "动态选项卡选项连线不应为空")
		}
		//当为动态选项卡时，其它else选项连线不能为空
		if len(node.GetDynamicOptionsElseNextNodeIDs()) == 0 {
			c.appendNodeError(nodeID, "动态选项卡其它选项连线不应为空")
		}
	} else {
		// 用户输入
		maxItemCount := config.GetMainConfig().VerifyWorkflow.OptionMaxItemCount
		if len(node.GetOptions()) > maxItemCount {
			c.appendNodeError(nodeID, fmt.Sprintf("最多支持%d个选项", maxItemCount))
		}

		maxQuestionLen := config.GetMainConfig().VerifyWorkflow.OptionMaxQuestionLen
		if utf8.RuneCountInString(c.removeHTMLTags(node.GetQuestion())) > maxQuestionLen {
			c.appendNodeError(nodeID, fmt.Sprintf("问题或提示不能超过%d", maxQuestionLen))
		}
		for i, option := range node.GetOptions() {
			index := i + 1 // 给前端展示的错误提示
			if i == len(node.GetOptions())-1 {
				// 最后一个选项卡，即else，选项内容可为空
			} else {
				if len(option.GetContent()) == 0 {
					c.appendNodeError(nodeID, fmt.Sprintf("第%d个选项的内容为空", index))
				}
			}

			if len(option.GetNextNodeIDs()) == 0 {
				c.appendNodeError(nodeID, fmt.Sprintf("第%d个选项的连线不应为空", index))
			}
			c.parseFromString(wfn.GetInputs(), nodeID, option.GetContent())

			itemMaxLen := config.GetMainConfig().VerifyWorkflow.OptionItemMaxLen
			if utf8.RuneCountInString(c.removeHTMLTags(option.GetContent())) > itemMaxLen {
				c.appendNodeError(nodeID, fmt.Sprintf("选项内容%d不能超过%d", index, itemMaxLen))
			}
		}
	}
}
