/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseParameterExtractorNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	nodeName := wfn.GetNodeName()
	node := wfn.GetParameterExtractorNodeData()
	if len(node.GetParameters()) == 0 {
		c.appendNodeError(nodeID, "参数信息为空")
	}
	for _, input := range wfn.GetInputs() {
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE {
			c.appendNodeError(nodeID, "参数提取节点中不允许引用系统变量")
			break
		}
	}
	parameterMaxLen := config.GetMainConfig().VerifyWorkflow.NodeParameterMaxLen
	if len(node.GetParameters()) > parameterMaxLen {
		c.appendNodeError(nodeID, fmt.Sprintf("参数个数不能超过%d", parameterMaxLen))
	}
	//if c.SaveType == uint32(KEP_WF.SaveWorkflowReq_ENABLE) {
	//	//essential := isNodeEssential(c.nodeMap, c.startNodeID, nodeID)
	//	essential := isNodeEssentialWithLogicEvaluator(c.nodeMap, c.startNodeID, nodeID)
	//	if !essential {
	//		c.appendNodeError(nodeID, "并行分支中不允许有\"参数收集\"节点")
	//	}
	//}

	// 参数提取节点的 prompt 可以为空
	//promptLen := config.GetMainConfig().VerifyWorkflow.NodePromptMaxLen
	//if utf8.RuneCountInString(c.removeHTMLTags(node.GetUserConstraint())) > promptLen {
	//	c.appendNodeError(nodeID, fmt.Sprintf("提示词不能超过%d", promptLen))
	//}
	c.checkNodePrompt(nodeID, node.GetUserConstraint())

	c.parseFromString(wfn.GetInputs(), nodeID, node.GetUserConstraint())
	c.collectParameters(nodeID, nodeName, node)
}

func (c *WfContext) collectParameters(nodeID, nodeName string, node *KEP_WF.ParameterExtractorNodeData) {
	var parameter entity.WorkflowNodeParam
	parameter.NodeID = nodeID
	parameter.NodeName = nodeName
	parameter.ParameterIds = make([]string, 0, len(node.GetParameters()))
	for _, p := range node.GetParameters() {
		parameter.ParameterIds = append(parameter.ParameterIds, p.GetRefParameterID())
	}
	c.parameters = append(c.parameters, parameter)
}
