/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

func (c *WfContext) parsePlugin(wfn *KEP_WF.WorkflowNode) {
	log.InfoContextf(c.ctx, "parsePlugin|nodeID:%s", wfn.GetNodeID())
	nodeID := wfn.GetNodeID()
	c.collectPluginRef(nodeID, wfn)
	c.collectToolNodeDataToRefToolIDToolNodeData(wfn)
}

func (c *WfContext) collectPluginRef(nodeID string, wfn *KEP_WF.WorkflowNode) {
	if c.pluginRef == nil {
		c.pluginRef = make([]entity.WorkflowRefPluginParams, 0)
	}
	node := wfn.GetPluginNodeData()
	if node.GetPluginType().String() == "" || node.GetPluginID() == "" || node.GetToolID() == "" {
		c.appendNodeError(nodeID, "插件类型、插件ID或工具ID不能为空")
	} else {
		workflowRefPluginParams := entity.WorkflowRefPluginParams{
			WorkflowID: c.Workflow.GetWorkflowID(),
			NodeID:     nodeID,
			PluginType: node.GetPluginType().String(),
			PluginID:   node.GetPluginID(),
			ToolID:     node.GetToolID(),
		}
		c.pluginRef = append(c.pluginRef, workflowRefPluginParams)
	}

	// 收集Header、Query、Body中的输入参数
	toolInputs := node.GetToolInputs()
	maxCount := config.GetMainConfig().VerifyWorkflow.NodeToolMaxCount
	if len(toolInputs.GetHeader()) > maxCount {
		log.WarnContextf(c.ctx, "checkPluginNodeHeader len=%d|maxCount=%d", len(toolInputs.GetHeader()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("插件节点Header数量超过%d限制", maxCount))
	}
	if len(toolInputs.GetQuery()) > maxCount {
		log.WarnContextf(c.ctx, "checkPluginNodeHeader len=%d|maxCount=%d", len(toolInputs.GetQuery()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("插件节点Query数量超过%d限制", maxCount))
	}
	if len(toolInputs.GetBody()) > maxCount {
		log.WarnContextf(c.ctx, "checkPluginNodeHeader len=%d|maxCount=%d", len(toolInputs.GetBody()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("插件节点Body数量超过%d限制", maxCount))
	}
	for _, header := range toolInputs.GetHeader() {
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, header.GetIsRequired(), header, wfn.GetNodeType(), true)
	}
	for _, query := range toolInputs.GetQuery() {
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, query.GetIsRequired(), query, wfn.GetNodeType(), true)
	}
	for _, body := range toolInputs.GetBody() {
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, body.GetIsRequired(), body, wfn.GetNodeType(), true)
	}
}

func (c *WfContext) collectToolNodeDataToRefToolIDToolNodeData(wfn *KEP_WF.WorkflowNode) {
	toolID := wfn.GetPluginNodeData().GetToolID()
	if c.refToolIDToolNodeData == nil {
		c.refToolIDToolNodeData = make(map[string][]*KEP_WF.WorkflowNode)
	}
	c.refToolIDToolNodeData[toolID] = append(c.refToolIDToolNodeData[toolID], wfn)
}

// 1. 批量请求plugin-config-server获取插件和工具信息
// 2. 校验在引用该插件中配置的header、query、body是否跟被引用的一致
func (c *WfContext) checkRefPlugins() error {
	if len(c.refToolIDToolNodeData) == 0 || len(c.pluginRef) == 0 {
		return nil
	}
	// 1. 批量请求plugin-config-server接口获取插件和工具信息
	toolInfoMap, err := c.getPluginToolInfos(c.ctx)
	if err != nil {
		return err
	}
	// 2. 校验
	c.checkToolInfoExist(c.ctx, toolInfoMap)
	for toolID, refToolIDToolNodeData := range c.refToolIDToolNodeData {
		for _, wfn := range refToolIDToolNodeData {
			if _, ok := toolInfoMap[toolID]; !ok {
				continue
			}
			c.checkToolInfo(c.ctx, toolInfoMap[toolID], wfn)
		}
	}
	return nil
}

// 获取插件详细信息
func (c *WfContext) getPluginToolInfos(ctx context.Context) (map[string]*pb.ToolInfo, error) {
	// key: 插件id value: 引用了该插件的节点id列表
	pluginIdBelongNodeIdsMap := make(map[string][]string)
	// key: 工具id value: 工具信息
	toolInfoMap := make(map[string]*pb.ToolInfo)
	for _, ref := range c.pluginRef {
		if _, ok := pluginIdBelongNodeIdsMap[ref.PluginID]; !ok {
			pluginIdBelongNodeIdsMap[ref.PluginID] = make([]string, 0)
		}
		pluginIdBelongNodeIdsMap[ref.PluginID] = append(pluginIdBelongNodeIdsMap[ref.PluginID], ref.NodeID)
	}
	pluginIds := make([]string, 0)
	for pluginId := range pluginIdBelongNodeIdsMap {
		pluginIds = append(pluginIds, pluginId)
	}
	if len(pluginIds) == 0 {
		return toolInfoMap, nil
	}
	// 获取工作流相关的插件信息，包含官方插件以及当前uin下的自定义插件
	uin, _ := util.GetUinAndSubAccountUin(ctx)
	rsp, err := rpc.ListPlugins(ctx, &pb.ListPluginsReq{
		PageSize:   int32(len(pluginIds)),
		PageNumber: 0,
		QueryType:  pb.ListPluginsReq_ID,
		PluginType: pb.ListPluginsReq_ALL,
		PluginIds:  pluginIds,
		Uin:        uin,
	})
	if err != nil {
		return toolInfoMap, err
	}

	getIds := make([]string, 0)
	for _, plugin := range rsp.Plugins {
		getIds = append(getIds, plugin.GetPluginId())
		for _, tool := range plugin.GetTools() {
			toolInfoMap[tool.GetToolId()] = tool
		}
	}

	notExist := c.findIdsNotExist(getIds, pluginIds)
	for _, id := range notExist {
		belongNodeIDs := pluginIdBelongNodeIdsMap[id]
		c.appendMultiNodesError(belongNodeIDs, "插件不存在")
	}

	return toolInfoMap, nil
}

// 获取工具详细信息
func (c *WfContext) checkToolInfoExist(ctx context.Context, toolInfoMap map[string]*pb.ToolInfo) {
	// key: 工具id, value: 引用了该插件的节点id列表
	toolIdBelongNodeIdsMap := make(map[string][]string)
	for _, ref := range c.pluginRef {
		if _, ok := toolIdBelongNodeIdsMap[ref.ToolID]; !ok {
			toolIdBelongNodeIdsMap[ref.ToolID] = make([]string, 0)
		}
		toolIdBelongNodeIdsMap[ref.ToolID] = append(toolIdBelongNodeIdsMap[ref.ToolID], ref.NodeID)
	}
	toolIds := make([]string, 0)
	for toolId := range toolIdBelongNodeIdsMap {
		toolIds = append(toolIds, toolId)
	}
	if len(toolIds) == 0 {
		return
	}
	for _, toolId := range toolIds {
		if _, ok := toolInfoMap[toolId]; !ok {
			belongNodeIDs := toolIdBelongNodeIdsMap[toolId]
			c.appendMultiNodesError(belongNodeIDs, "工具不存在")
		}
	}
}

// 校验在引用该插件中配置的header、query、body是否跟被引用的一致
// outputs的校验走外部通用流程
func (c *WfContext) checkToolInfo(ctx context.Context, toolInfo *pb.ToolInfo, wfn *KEP_WF.WorkflowNode) {
	inputs := wfn.GetPluginNodeData().GetToolInputs()
	c.compareRequestParam(toolInfo.GetHeader(), inputs.GetHeader())
	c.compareRequestParam(toolInfo.GetQuery(), inputs.GetQuery())
	c.compareRequestParam(toolInfo.GetBody(), inputs.GetBody())
	outputs := wfn.GetOutputs()
	c.compareOutputParams(toolInfo.GetOutputs(), outputs, true)
}

// findIdsNotExist 返回ids中缺少的id
func (c *WfContext) findIdsNotExist(ids []string, allIds []string) []string {
	result := make([]string, 0)
	idsMap := make(map[string]struct{})
	for _, id := range ids {
		idsMap[id] = struct{}{}
	}
	for _, id := range allIds {
		if _, ok := idsMap[id]; !ok {
			result = append(result, id)
		}
	}
	return result
}

func (c *WfContext) compareRequestParam(toolInfoHeader []*pb.RequestParam, nodeHeader []*KEP_WF.ToolNodeData_RequestParam) bool {
	if len(toolInfoHeader) != len(nodeHeader) {
		return false
	}
	matched := make(map[int]bool)
	for _, o1 := range toolInfoHeader {
		found := false
		for i, o2 := range nodeHeader {
			if matched[i] {
				continue
			}
			if o1.GetName() == o2.GetParamName() && o1.GetDesc() == o2.GetParamDesc() &&
				o1.GetType().String() == o2.GetParamType().String() && o1.GetIsRequired() == o2.GetIsRequired() &&
				c.compareRequestParam(o1.GetSubParams(), o2.GetSubParams()) {
				matched[i] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

func (c *WfContext) compareOutputParams(toolInfoOutputs []*pb.ResponseParam, wfnOutput []*KEP_WF.OutputParam, isOutermost bool) bool {
	// 校验最外层主节点是否是Output
	if isOutermost && len(wfnOutput) == 0 {
		log.WarnContextf(c.ctx, "compareOutputParams|最外层主节点为空|toolInfoOuputs:%+v|wfnOutput:%+v", toolInfoOutputs, wfnOutput)
		return false
	}
	// 目前暂时忽略
	if isOutermost && (wfnOutput[0].GetType() != KEP_WF.TypeEnum_OBJECT || wfnOutput[0].GetTitle() != "Output") {
		log.WarnContextf(c.ctx, "compareOutputParams|最外层主节点不是Output|toolInfoOuputs:%+v|wfnOutput:%+v", toolInfoOutputs, wfnOutput)
		return false
	}
	wfnOutputProperties := wfnOutput
	if isOutermost {
		wfnOutputProperties = wfnOutput[0].GetProperties()
	}
	if len(toolInfoOutputs) != len(wfnOutputProperties) {
		log.WarnContextf(c.ctx, "compareOutputParams|参数个数不一致|toolInfoOuputs:%+v|wfnOutput:%+v", toolInfoOutputs, wfnOutputProperties)
		return false
	}

	matched := make(map[int]bool)

	for _, o1 := range toolInfoOutputs {
		found := false
		for i, o2 := range wfnOutputProperties {
			if matched[i] {
				continue
			}
			if o1.GetName() == o2.GetTitle() && o1.GetType().String() == o2.GetType().String() {
				if o2.GetType() == KEP_WF.TypeEnum_OBJECT || o2.GetType() == KEP_WF.TypeEnum_ARRAY_OBJECT {
					if !c.compareOutputParams(o1.GetSubParams(), o2.GetProperties(), false) {
						continue
					}
				}
				matched[i] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}
