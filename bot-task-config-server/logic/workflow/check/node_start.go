/*
 * 2024-10-22
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

func (c *WfContext) parseStartNode(node *KEP_WF.WorkflowNode) {
	for _, nextNodeID := range node.GetNextNodeIDs() {
		if nextNode, exist := c.nodeMap[nextNodeID]; exist && nextNode.GetNodeType() == KEP_WF.NodeType_END {
			c.appendNodeError(node.GetNodeID(), "开始节点不能直接连接结束节点")
		}
	}
}
