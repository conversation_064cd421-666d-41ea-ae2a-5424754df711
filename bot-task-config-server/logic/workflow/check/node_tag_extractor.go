/*
 * 2024-10-15
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseTagExtractorNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetTagExtractorNodeData()

	if len(node.GetModelName()) == 0 {
		c.appendNodeError(nodeID, "模型为空")
	}
	if len(node.GetTags()) == 0 {
		c.appendNodeError(nodeID, "标签为空")
	}

	tagMaxLen := config.GetMainConfig().VerifyWorkflow.NodeTagMaxLen
	if len(node.GetTags()) > tagMaxLen {
		c.appendNodeError(nodeID, fmt.Sprintf("标签最多为%d", tagMaxLen))
	}

	if len(node.GetQuery()) == 0 {
		c.appendNodeError(nodeID, "提示词不应为空")
	}

	if len(node.GetModelName()) > 0 {
		c.checkNodePrompt(nodeID, node.GetQuery())
	}

	c.parseFromString(wfn.GetInputs(), nodeID, node.GetQuery())

	for _, tag := range node.GetTags() {
		if len(tag.GetName()) == 0 {
			c.appendNodeError(nodeID, "标签名不应为空")
			break
		}
		tagExampleMaxLen := config.GetMainConfig().VerifyWorkflow.NodeTagExampleMaxLen
		if len(tag.GetValueExamples()) > tagExampleMaxLen {
			c.appendNodeError(nodeID, fmt.Sprintf("标签值示例最多为%d", tagExampleMaxLen))
			break
		}
	}
}
