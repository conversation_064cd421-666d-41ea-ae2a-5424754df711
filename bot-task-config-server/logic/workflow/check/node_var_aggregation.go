package check

import (
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseVarAggregation(node *KEP_WF.WorkflowNode) {

	log.InfoContextf(c.ctx, "parseVarAggregation|nodeID:%s", node.GetNodeID())
	va := node.GetVarAggregationNodeData()

	if len(node.GetInputs()) > 0 {
		c.appendNodeError(node.GetNodeID(), "聚合节点不能有输入变量")
	}

	if len(va.GetGroups()) == 0 {
		c.appendNodeError(node.GetNodeID(), "聚合组不能为空")
	}

	maxGroupCount := config.GetMainConfig().VerifyWorkflow.VarAggregationMaxGroupCount
	if len(va.GetGroups()) > maxGroupCount {
		c.appendNodeError(node.GetNodeID(), fmt.Sprintf("聚合组数不能超过%d", maxGroupCount))
	}

	maxItemCount := config.GetMainConfig().VerifyWorkflow.VarAggregationMaxItemCount
	for groupIndex, group := range va.GetGroups() {
		if group.GetName() == "" {
			c.appendNodeError(node.GetNodeID(), fmt.Sprintf("聚合组%d的名称不能为空", groupIndex+1))
		}
		if len(group.GetItems()) == 0 {
			c.appendNodeError(node.GetNodeID(), fmt.Sprintf("聚合组%d的项不能为空", groupIndex+1))
		}
		if len(group.GetItems()) > maxItemCount {
			c.appendNodeError(node.GetNodeID(), fmt.Sprintf("聚合组%d的项数不能超过%d", groupIndex+1, maxItemCount))
		}
		c.parseVarAggregationItems(node.GetNodeID(), groupIndex, group.GetType(), group.GetItems())
	}
}

func (c *WfContext) parseVarAggregationItems(nodeID string, groupIndex int,
	groupType KEP_WF.TypeEnum, items []*KEP_WF.VarParam) {
	log.InfoContextf(c.ctx, "parseVarAggregationItems|groupIndex:%d|items.len:%d", groupIndex, len(items))
	for i, item := range items {
		if item.GetType() != groupType {
			log.InfoContextf(c.ctx, "parseVarAggregationItems|groupIndex:%d|items.len:%d", groupIndex, len(items))
			c.appendNodeError(nodeID, fmt.Sprintf("聚合组%d的第%d项类型不一致, itemType:%s, groupType:%s",
				groupIndex+1, i+1, item.GetType().String(), groupType.String()))
		}
		if item.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			c.appendNodeError(nodeID, fmt.Sprintf("聚合组%d的第%d项不能使用用户输入", groupIndex+1, i+1))
		}
	}
}
