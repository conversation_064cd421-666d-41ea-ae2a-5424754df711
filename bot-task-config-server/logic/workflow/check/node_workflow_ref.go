/*
 * 2024-12-13
 * Copyright (c) 2024. xinghuiquan@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseWorkflowRef(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetWorkflowRefNodeData()

	if len(node.GetWorkflowID()) == 0 {
		c.appendNodeError(nodeID, "引用的工作流为空")
		return
	}

	// 1. 检查被引用工作流的输入参数：
	for _, input := range node.GetRefInputs() {
		c.checkInputParamsRecursively(nodeID, input, wfn.GetNodeType())
	}

	// 2. 引用的工作流的关联关系
	c.collectWorkflowReference(nodeID, node.GetWorkflowID())
	// 3. 收集引用的工作流的输入，用于最后统一检查与引用的工作流中定义的输入是否一致
	c.collectItemToRefWorkflowInputs(node.GetWorkflowID(), node.GetRefInputs())
	// 4. 收集引用的工作流的输出，用于最后统一检查与引用的工作流中定义的输出是否一致
	c.collectItemToRefWorkflowOutputs(node.GetWorkflowID(), wfn.GetOutputs())
}

// checkInputParamsRecursively 递归检查输入参数
func (c *WfContext) checkInputParamsRecursively(nodeID string, input *KEP_WF.InputParam, nodeType KEP_WF.NodeType) {
	if input == nil {
		return
	}
	// 检查当前层级的参数名称
	if input.GetName() == "" {
		c.appendNodeError(nodeID, "变量名称不能为空")
		return
	}
	// fixbug: https://tapd.woa.com/tapd_fe/worktable/search?queryToken=26b1f99e90f3cfcd82f47e60ede5d7a8&dialog_preview_id=bug_1070080800140159765
	// TypeEnum_ARRAY_OBJECT: {"Name":"arr_ob","Type":"ARRAY_OBJECT","Input":{"InputType":"REFERENCE_OUTPUT","Reference":{"NodeID":"3119d441-d24f-ced9-48f1-8a6dbb005d5f","JsonPath":"arr_ob"}},"Desc":"","IsRequired":false,"SubInputs":[{"Name":"f1","Type":"STRING","Input":{"InputType":"REFERENCE_OUTPUT","Reference":{"NodeID":"","JsonPath":""}},"Desc":"","IsRequired":false,"SubInputs":[],"closed":false}],"closed":true}
	// TypeEnum_OBJECT: {"Name":"aa","Type":"OBJECT","Input":{"InputType":"REFERENCE_OUTPUT","Reference":{"NodeID":"","JsonPath":""}},"Desc":"","IsRequired":false,"SubInputs":[{"Name":"aa_bb","Type":"OBJECT","Input":{"InputType":"USER_INPUT","UserInputValue":{"Values":["vvvv"]}},"Desc":"","IsRequired":false,"SubInputs":[{"Name":"aa_bb_cc","Type":"STRING","Input":{"InputType":"SYSTEM_VARIABLE","SystemVariable":{"Name":"SYS.CurrentTime","DialogHistoryLimit":15}},"Desc":"","IsRequired":false,"SubInputs":[],"closed":false}],"closed":false}],"closed":false}
	if input.GetType() != KEP_WF.TypeEnum_OBJECT {
		// a. 校验 Input； b. 收集引用的"API参数（自定义变量）"
		c.checkInputAndCollect(nodeID, input.GetInput())
	}

	if input.GetType() != KEP_WF.TypeEnum_ARRAY_OBJECT {
		// 递归处理子输入参数
		for _, subInput := range input.GetSubInputs() {
			c.checkInputParamsRecursively(nodeID, subInput, nodeType)
		}
	}
}

func (c *WfContext) collectItemToRefWorkflowInputs(refWorkflowID string, item []*KEP_WF.InputParam) {
	if len(refWorkflowID) == 0 || len(item) == 0 {
		return
	}
	if c.refWorkflowInputs == nil {
		c.refWorkflowInputs = make(map[string][]*KEP_WF.InputParam)
	}

	if _, ok := c.refWorkflowInputs[refWorkflowID]; ok {
		// 一个工作流内同时引用同一个工作流多次，那就会添加多次，这里去重
		return
	}

	c.refWorkflowInputs[refWorkflowID] = append(c.refWorkflowInputs[refWorkflowID], item...)
}

func (c *WfContext) collectItemToRefWorkflowOutputs(refWorkflowID string, item []*KEP_WF.OutputParam) {
	if len(refWorkflowID) == 0 || len(item) == 0 {
		return
	}
	if c.refWorkflowOutputs == nil {
		c.refWorkflowOutputs = make(map[string][]*KEP_WF.OutputParam)
	}

	c.refWorkflowOutputs[refWorkflowID] = append(c.refWorkflowOutputs[refWorkflowID], item...)
}

func (c *WfContext) collectWorkflowReference(currentNodeID string, refWorkflowID string) {
	if refWorkflowID == "" {
		return
	}
	// 用于校验
	if c.refWorkflowIDNodeIDs == nil {
		c.refWorkflowIDNodeIDs = make(map[string][]string)
	}
	c.refWorkflowIDNodeIDs[refWorkflowID] = append(c.refWorkflowIDNodeIDs[refWorkflowID], currentNodeID)

	// 用于保存到db时维护关联关系
	c.refWorkflows = append(c.refWorkflows, entity.WorkflowReferenceParams{
		WorkflowID:    c.Workflow.GetWorkflowID(),
		NodeID:        currentNodeID,
		WorkflowRefID: refWorkflowID,
	})
}
