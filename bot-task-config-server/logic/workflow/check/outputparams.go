/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

// compareOutputParams 对比两个 []OutputParam 中的 Title和Type
func compareOutputParams(outputs1, outputs2 []*KEP_WF.OutputParam) bool {
	if len(outputs1) != len(outputs2) {
		return false
	}

	matched := make(map[int]bool)

	for _, o1 := range outputs1 {
		found := false
		for i, o2 := range outputs2 {
			if matched[i] {
				continue
			}
			if o1.GetTitle() == o2.GetTitle() && o1.GetType() == o2.GetType() &&
				compareOutputParams(o1.GetProperties(), o2.GetProperties()) {
				matched[i] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}
