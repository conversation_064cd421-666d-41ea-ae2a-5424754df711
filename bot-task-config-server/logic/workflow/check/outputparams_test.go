/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCompareOutputParams(t *testing.T) {
	type args struct {
		outputs1 []*KEP_WF.OutputParam
		outputs2 []*KEP_WF.OutputParam
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "1. 相同的",
			args: args{
				outputs1: []*KEP_WF.OutputParam{
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle2", Type: KEP_WF.TypeEnum_INT},
						},
					},
					{
						Title: "Title2",
						Type:  KEP_WF.TypeEnum_STRING,
					},
				},
				outputs2: []*KEP_WF.OutputParam{
					{
						Title: "Title2",
						Type:  KEP_WF.TypeEnum_STRING,
					},
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle2", Type: KEP_WF.TypeEnum_INT},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "2. 最外层的Title不同",
			args: args{
				outputs1: []*KEP_WF.OutputParam{
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle2", Type: KEP_WF.TypeEnum_INT},
						},
					},
					{
						Title: "Title2",
						Type:  KEP_WF.TypeEnum_STRING,
					},
				},
				outputs2: []*KEP_WF.OutputParam{
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle2", Type: KEP_WF.TypeEnum_INT},
						},
					},
					{
						Title: "Title3", // <--- 区别
						Type:  KEP_WF.TypeEnum_STRING,
					},
				},
			},
			want: false,
		},
		{
			name: "3. 最外层的Title相同， 属性不同",
			args: args{
				outputs1: []*KEP_WF.OutputParam{
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle2", Type: KEP_WF.TypeEnum_INT},
						},
					},
					{
						Title: "Title2",
						Type:  KEP_WF.TypeEnum_STRING,
					},
				},
				outputs2: []*KEP_WF.OutputParam{
					{
						Title: "Title1",
						Type:  KEP_WF.TypeEnum_OBJECT,
						Properties: []*KEP_WF.OutputParam{
							{Title: "SubTitle1", Type: KEP_WF.TypeEnum_STRING},
							{Title: "SubTitle3", Type: KEP_WF.TypeEnum_INT}, // <--- 区别
						},
					},
					{
						Title: "Title2",
						Type:  KEP_WF.TypeEnum_STRING,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, compareOutputParams(tt.args.outputs1, tt.args.outputs2), "compareOutputParams(%v, %v)", tt.args.outputs1, tt.args.outputs2)
		})
	}
}
