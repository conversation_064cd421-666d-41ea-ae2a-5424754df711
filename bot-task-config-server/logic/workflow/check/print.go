/*
 * 2024-10-16
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"strings"
)

func (c *WfContext) printWorkflow() {
	str := c.printWorkflowNode(c.nodeMap[c.startNodeID], "", true, make(map[string]bool))
	log.InfoContextf(c.ctx, "printWorkflow:│-\n%s", str)
}

func (c *WfContext) printWorkflowNode(node *KEP_WF.WorkflowNode, indent string, isLast bool,
	visited map[string]bool) string {
	var sb strings.Builder

	nodeID := node.GetNodeID()
	nodeName := node.GetNodeName()
	if visited[nodeID] {
		sb.WriteString(fmt.Sprintf("%s└─*─ %s (%s)  (visited)\n", indent, nodeID, nodeName))
		return sb.String() // 避免循环引用
	}
	visited[nodeID] = true

	// 打印当前节点
	if isLast {
		sb.WriteString(fmt.Sprintf("%s└─── %s (%s)\n", indent, nodeID, nodeName))
		indent += "    "
	} else {
		sb.WriteString(fmt.Sprintf("%s├─── %s (%s)\n", indent, nodeID, nodeName))
		indent += "│   "
	}

	for i, nextID := range getNextIDs(node) {
		if nextNode, exists := c.nodeMap[nextID]; exists {
			sb.WriteString(c.printWorkflowNode(nextNode, indent, i == len(getNextIDs(node))-1, visited))
		}
	}

	return sb.String()
}
