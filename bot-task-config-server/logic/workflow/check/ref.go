/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

func (c *WfContext) loadCache() error {
	err := c.loadCustomVars()
	if err != nil {
		log.InfoContextf(c.ctx, "loadCache|loadCustomVars|err:%+v", err)
		return err
	}
	c.loadSystemVars()
	return nil
}

func (c *WfContext) loadCustomVars() error {
	vars, err := db.GetVarsByNameOrAppIdFunc(c.ctx, c.AppID, "", "", false)
	if err != nil {
		log.WarnContextf(c.ctx, "loadCustomVars|err:$+v", err)
		return err
	}
	c.customVarIDCache = make(map[string]*entity.VarParams)
	for _, params := range vars {
		c.customVarIDCache[params.VarID] = params
	}
	return nil
}

func (c *WfContext) checkCustomVarExist(id string) bool {
	_, exist := c.customVarIDCache[id]
	return exist
}

func (c *WfContext) loadSystemVars() {
	c.systemVarCache = make(map[string]*KEP.SystemVar, 0)
	var systemVars = config.GetMainConfig().SystemVarTemplates
	for _, v := range systemVars {
		systemVar := new(KEP.SystemVar)
		systemVar.Name = v.Name
		systemVar.Text = v.Text
		c.systemVarCache[v.Name] = systemVar
	}
}

func (c *WfContext) checkSystemVarExist(name string) bool {
	_, exist := c.systemVarCache[name]
	return exist
}
