/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"strings"
)

// {"InputType":"CUSTOM_VARIABLE","CustomVarID":"7a316f62-2c81-4a75-ba84-c6a1b494b0ef"}
//{"InputType":"SYSTEM_VARIABLE","SystemVariable":{"Name":"sys.chat_history","DialogHistoryLimit":15}}
//{"InputType":"REFERENCE_OUTPUT","Reference":{"NodeID":"de00a513-f6d9-058d-5387-a42959564537",
//  "ReferenceInfo":"74db95b3-4468-47c6-99db-1ddd33b523ca"}}

// DataExtra 解析富文本时 html标签中的 data-extra 的数据结构
//type DataExtra struct {
//	Reference struct {
//		NodeID        string `json:"NodeID"`
//		ReferenceInfo string `json:"ReferenceInfo"`
//	} `json:"Reference"`
//	SystemVariableName struct {
//		Name               string `json:"Name"`
//		DialogHistoryLimit int32  `json:"DialogHistoryLimit"`
//	} `json:"SystemVariable"`
//	CustomVarID string `json:"CustomVarID"`
//}

// 出发地：
//
//	<span
//		data-type="REFERENCE_OUTPUT/SYSTEM_VARIABLE/CUSTOM_VARIABLE"
//		data-extra="<Data_Value>"
//		data-info="xxxx">
//		@出发城市
//	</span> 需要一键优化的内容。
//
// 其中，<Data_Value>的内容格式为：
//
//	{
//		 "Reference":{
//		   "NodeID":"123",
//		   "ReferenceInfo":"output"
//		 },
//		 "SystemVariable":{
//		   "Name":"sys.chat_history",
//		   "DialogHistoryLimit":10
//		 },
//		 "CustomVarID":"123"
//		}
//
//		的 QueryEscape 编码

// 检查 text 中 {{输入参数}} 是否是 inputParams 里定义的
func (c *WfContext) parseFromString(inputParams []*KEP_WF.InputParam, currentNodeID string, text string) {
	inputParamMap := make(map[string]struct{})
	for _, inputParam := range inputParams {
		inputParamMap[inputParam.GetName()] = struct{}{}
	}

	paramInRichText := c.extractParams(text)
	for _, pr := range paramInRichText {
		if _, exist := inputParamMap[pr]; !exist {
			c.appendNodeError(currentNodeID, fmt.Sprintf("%s 未在输入参数中定义", pr))
		}
	}

	log.InfoContextf(c.ctx, "parseFromString|currentNodeID:%s|prompt:%s", currentNodeID, text)
	//doc, err := goquery.NewDocumentFromReader(strings.NewReader(text))
	//if err != nil {
	//	log.WarnContextf(c.ctx, "parseFromString|NewDocumentFromReader|err:%v", err)
	//	return fmt.Errorf("文本解析有误")
	//}
	//
	//doc.Find("span").Each(func(i int, s *goquery.Selection) {
	//	dataType, exists := s.Attr("data-type")
	//	if !exists {
	//		return
	//	}
	//
	//	dataExtra, exists := s.Attr("data-extra")
	//	if !exists {
	//		return
	//	}
	//	decodeDataValue, errInner := url.QueryUnescape(dataExtra)
	//	if errInner != nil || len(decodeDataValue) == 0 {
	//		log.WarnContextf(c.ctx, "parseFromString|QueryUnescape|errInner:%v", errInner)
	//		return
	//	}
	//
	//	// 确定富文本中引用"节点内参数"的规则
	//	input := &KEP_WF.Input{}
	//	//value := &DataExtra{}
	//	//if err1 := json.Unmarshal([]byte(decodeDataValue), input); err1 != nil {
	//	if err1 := protojson.Unmarshal([]byte(decodeDataValue), input); err1 != nil {
	//		log.WarnContextf(c.ctx, "parseFromString|Unmarshal|err: %v, decodeDataValue: %v", err1, decodeDataValue)
	//		return
	//	}
	//
	//	log.InfoContextf(c.ctx, "parseFromString|dataType:%s|decodeDataValue:%s", dataType, decodeDataValue)
	//	//input := &KEP_WF.Input{}
	//	//switch dataType {
	//	//case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT.String():
	//	//	input.InputType = KEP_WF.InputSourceEnum_REFERENCE_OUTPUT
	//	//	input.Source = &KEP_WF.Input_Reference{
	//	//		Reference: &KEP_WF.ReferenceFromNode{
	//	//			NodeID:        value.Reference.NodeID,
	//	//			ReferenceInfo: value.Reference.ReferenceInfo,
	//	//		},
	//	//	}
	//	//	log.InfoContextf(c.ctx, "parseFromString|Reference:%+v", value.Reference)
	//	//case KEP_WF.InputSourceEnum_SYSTEM_VARIABLE.String():
	//	//	input.InputType = KEP_WF.InputSourceEnum_SYSTEM_VARIABLE
	//	//	input.Source = &KEP_WF.Input_SystemVariable{
	//	//		SystemVariable: &KEP_WF.SystemVariable{
	//	//			Name:               value.SystemVariableName.Name,
	//	//			DialogHistoryLimit: value.SystemVariableName.DialogHistoryLimit,
	//	//		},
	//	//	}
	//	//	log.InfoContextf(c.ctx, "parseFromString|SystemVariableName:%+v", value.SystemVariableName)
	//	//case KEP_WF.InputSourceEnum_CUSTOM_VARIABLE.String():
	//	//	input.InputType = KEP_WF.InputSourceEnum_CUSTOM_VARIABLE
	//	//	input.Source = &KEP_WF.Input_CustomVarID{
	//	//		CustomVarID: value.CustomVarID,
	//	//	}
	//	//	log.InfoContextf(c.ctx, "parseFromString|CustomVarID:%+v", value.CustomVarID)
	//	//}
	//	//c.checkInputAndCollect(currentNodeID, input)
	//	//c.collectCustomVarIDsFromInput(input)
	//})
	//
}

func (c *WfContext) extractParams(text string) []string {
	// 正则表达式匹配 {{param}} 的内容

	matches := c.paramInRichTextRegexp.FindAllStringSubmatch(text, -1)
	var params []string
	for _, match := range matches {
		// match[1] 是括号内捕获的内容，即参数名称
		params = append(params, match[1])
	}

	return params
}

func (c *WfContext) removeHTMLTags(input string) string {
	noTags := c.htmlRegexp.ReplaceAllString(input, "")
	return strings.TrimSpace(noTags)
}
