/*
 * 2024-10-21
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/stretchr/testify/assert"
)

func Test_removeHTMLTags(t *testing.T) {
	var safeUrls []string
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "",
			input: "<p>这是一个<strong>段落</strong>，包含<em>HTML标签</em>。</p>",
			want:  "这是一个段落，包含HTML标签。",
		},
		{
			name:  "",
			input: "这是一个段落，包含HTML标签。",
			want:  "这是一个段落，包含HTML标签。",
		},
		{
			name:  "",
			input: "",
			want:  "",
		},
		{
			name: "",
			input: fmt.Sprintf("<span "+
				"data-type=\"%s\" "+
				"data-extra=\"%s\" data-info=\"xxxx\">@出发城市</span>",
				KEP_WF.InputSourceEnum_CUSTOM_VARIABLE.String(),
				"aaabb"),
			want: "@出发城市",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cc := New(trpc.BackgroundContext(), "", nil, uint32(KEP_WF.SaveWorkflowReq_ENABLE), safeUrls)
			_ = cc.initRegexp()
			assert.Equalf(t, tt.want, cc.removeHTMLTags(tt.input), "removeHTMLTags(%v)", tt.input)
		})
	}
}
