/*
 * 2024-12-13
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package check

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

func extractInputAndOutput(wf *KEP_WF.Workflow) ([]*KEP_WF.InputParam, []*KEP_WF.OutputParam) {
	inputs := make([]*KEP_WF.InputParam, 0)
	outputs := make([]*KEP_WF.OutputParam, 0)
	if wf == nil {
		return inputs, outputs
	}
	for _, node := range wf.GetNodes() {
		switch node.GetNodeType() {
		case KEP_WF.NodeType_START:
			inputs = append(inputs, node.GetInputs()...)
		case KEP_WF.NodeType_END:
			outputs = append(outputs, node.GetOutputs()...)
		}
	}
	return inputs, outputs
}
