package workflow

import (
	"context"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	dbUtil "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// CopyWorkflow 复制工作流 - 示例问法不复制
func CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq) (*KEP_WF.CopyWorkflowRsp, error) {
	log.InfoContextf(ctx, "CopyWorkflow, BotBizId:%s, FlowId:%s", req.GetAppBizId(), req.GetWorkflowId())
	var err error
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow CheckRobot Failed, err:%v", err)
		return nil, err
	}

	// 分布式锁
	key := entity.GetCopyWorkflowLockKey(req.GetWorkflowId())
	locker := lock.NewDefaultLocker(key, req.GetWorkflowId(), database.GetRedis())

	// 加锁
	ok, lockErr := locker.Lock(ctx, false)
	if lockErr != nil {
		log.ErrorContextf(ctx, "CopyWorkflow locker.Lock Failed, err:%v", lockErr)
		return nil, lockErr
	}
	if !ok {
		lockErr = errors.ErrWorkflowCopying
		log.WarnContextf(ctx, "CopyWorkflow locker.Lock Failed, err:%v", lockErr)
		return nil, lockErr
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.ErrorContextf(ctx, "CopyWorkflow locker.UnLock Fialed, err:%v", unLockErr)
		}
	}()
	// 查询当前复制工作流基础信息 区分版本并进行槽位恢复逻辑
	// v2.8.5 画布中节点里密钥需要加密存储 - 这里获取到的是加密状态下的画布信息，因此后续不做加解密处理
	workflow, err := db.GetWorkflowDetail(ctx, req.GetWorkflowId(), req.GetAppBizId())
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow GetWorkflowDetail Failed, err:%v", err)
		return nil, err
	}
	newWorkflowID := idgenerator.NewUUID()
	// 获取参数信息
	dbParams, dbWfParams, old2newParam, err :=
		getParamsToCopy(ctx, req, uin, subUin, newWorkflowID)
	if err != nil {
		return nil, err
	}
	workflowVar, err := getVarsToCopy(ctx, req, newWorkflowID)
	if err != nil {
		return nil, err
	}
	workflowRef, err := getWorkflowRefToCopy(ctx, req, newWorkflowID)
	if err != nil {
		return nil, err
	}
	pluginRef, err := getPluginRefToCopy(ctx, req, newWorkflowID)
	if err != nil {
		return nil, err
	}
	customModelsRef, err := getCustomModelsToCopy(ctx, req, newWorkflowID)
	if err != nil {
		return nil, err
	}
	//// 获取pdl
	//workflowPDL, err := getWorkflowPDLRefToCopy(ctx, req, newWorkflowID)
	//if err != nil {
	//	return nil, err
	//}
	// 组装复制信息
	newWorkflowParams, err := genCopyNewWorkflow(ctx, req.GetAppBizId(), newWorkflowID, workflow, old2newParam)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow genCopyNewWorkflow Failed, err:%v", err)
		return nil, err
	}
	err = copyWorkflow(ctx, req, newWorkflowParams, dbParams, dbWfParams, workflowVar, workflowRef,
		pluginRef, customModelsRef)
	if err != nil {
		return nil, err
	}
	return &KEP_WF.CopyWorkflowRsp{
		CopiedWorkflowId: newWorkflowID,
		ErrorMsg:         "",
	}, nil
}

func copyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowParams entity.CreateWorkflowParams,
	dbParams []*entity.Parameter, dbWfParams []*entity.WorkflowParameter, workflowVar []*entity.WorkflowVar,
	workflowRef []*entity.WorkflowReference, pluginRef []*entity.WorkflowRefPlugin,
	customModelsRef []*entity.WorkflowCustomModel) error {
	var err error
	tx := dbUtil.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	defer func() {
		// 事物提交或者回滚
		txErr := dbUtil.CommitOrRollbackTx(ctx, tx, err)
		if txErr != nil {
			err = txErr
		}
	}()
	tx = tx.WithContext(ctx)
	// 创建工作流程
	log.InfoContextf(ctx, "CopyWorkflow newWorkflowParams:%v", newWorkflowParams)
	err = db.TxCreateWorkflow(ctx, tx, newWorkflowParams)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow db.TxCreateWorkflow Failed, err:%v", err)
		return err
	}
	err = db.TxBatchCreateParams(ctx, tx, req.GetAppBizId(), dbParams, dbWfParams)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow db.TxBatchCreateParams Failed, err:%v", err)
		return err
	}
	err = db.TxInsertWorkflowVar(ctx, tx, workflowVar)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow db.TxInsertWorkflowVar Failed, err:%v", err)
		return err
	}
	err = db.TxInsertWorkflowRef(ctx, tx, workflowRef)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow db.TxInsertWorkflowRef Failed, err:%v", err)
		return err
	}
	err = db.TxInsertWorkflowRefPlugin(ctx, tx, pluginRef)
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow db.UpdateWorkflowRefPlugin Failed, err:%v", err)
		return err
	}

	if err = db.TxInsertWorkflowRefCustomModel(ctx, tx, customModelsRef); err != nil {
		log.WarnContextf(ctx, "CopyWorkflow|db.TxInsertWorkflowRefCustomModel|err:%+v", err)
		return err
	}

	return nil
}

func getVarsToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowID string) (
	[]*entity.WorkflowVar, error) {
	workflowVar, err := db.GetWorkflowVarByFlowId(ctx, req.GetAppBizId(), req.GetWorkflowId())
	if err != nil {
		log.ErrorContextf(ctx, "getVarsToCopy|err:%+v", err)
		return nil, err
	}
	for i := range workflowVar {
		workflowVar[i].ID = 0
		workflowVar[i].WorkflowID = newWorkflowID
		workflowVar[i].ReleaseStatus = entity.ReleaseStatusUnPublished
		workflowVar[i].Action = entity.ActionInsert
		workflowVar[i].CreateTime = time.Now()
		workflowVar[i].UpdateTime = time.Now()
	}
	return workflowVar, nil
}

func getWorkflowRefToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowID string) (
	[]*entity.WorkflowReference, error) {
	oldWorkflowRefs, err := db.GetWorkflowRefIdsByFlowIds(ctx, req.GetAppBizId(), []string{req.GetWorkflowId()})
	if err != nil {
		log.ErrorContextf(ctx, "getWorkflowRefToCopy|err:%+v", err)
		return nil, err
	}
	for i := range oldWorkflowRefs {
		oldWorkflowRefs[i].ID = 0
		oldWorkflowRefs[i].WorkflowID = newWorkflowID
		oldWorkflowRefs[i].CreateTime = time.Now()
		oldWorkflowRefs[i].UpdateTime = time.Now()
	}
	return oldWorkflowRefs, nil
}

//func getWorkflowPDLRefToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowID string) (
//	[]*entity.WorkflowReference, error) {
//	oldWorkflowPDLs, err := db.GetPDLByFlowIds(ctx, req.GetAppBizId(), []string{req.GetWorkflowId()})
//	if err != nil {
//		log.ErrorContextf(ctx, "getWorkflowRefToCopy|err:%+v", err)
//		return nil, err
//	}
//	for i := range oldWorkflowPDLs {
//		oldWorkflowPDLs[i].ID = 0
//		oldWorkflowPDLs[i].WorkflowID = newWorkflowID
//		oldWorkflowPDLs[i].CreateTime = time.Now()
//		oldWorkflowPDLs[i].UpdateTime = time.Now()
//	}
//	return oldWorkflowRefs, nil
//}

func getPluginRefToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowID string) (
	[]*entity.WorkflowRefPlugin, error) {
	oldPluginRefs, err := db.GetWorkflowRefPlugin(ctx, "", "", req.GetWorkflowId())
	if err != nil {
		log.ErrorContextf(ctx, "getPluginRefToCopy|err:%+v", err)
		return nil, err
	}
	for i := range oldPluginRefs {
		oldPluginRefs[i].ID = 0
		oldPluginRefs[i].WorkflowID = newWorkflowID
		oldPluginRefs[i].CreateTime = time.Now()
		oldPluginRefs[i].UpdateTime = time.Now()
	}
	return oldPluginRefs, nil
}

// getCustomModelsToCopy ...
func getCustomModelsToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, newWorkflowID string) (
	[]*entity.WorkflowCustomModel, error) {
	oldCustomModels, err := db.GetCustomModelByWfId(ctx, req.GetWorkflowId())
	if err != nil {
		log.ErrorContextf(ctx, "getPluginRefToCopy|err:%+v", err)
		return nil, err
	}
	for i := range oldCustomModels {
		oldCustomModels[i].ID = 0
		oldCustomModels[i].WorkflowID = newWorkflowID
		oldCustomModels[i].CreateTime = time.Now()
		oldCustomModels[i].UpdateTime = time.Now()
	}
	return oldCustomModels, nil
}

func getParamsToCopy(ctx context.Context, req *KEP_WF.CopyWorkflowReq, uin string, subUin string,
	newWorkflowID string) ([]*entity.Parameter, []*entity.WorkflowParameter, map[string]string, error) {
	mapParams, err := db.GetExportParams(ctx, req.GetAppBizId(), []string{req.GetWorkflowId()})
	if err != nil {
		log.ErrorContextf(ctx, "CopyWorkflow GetExportParams Failed, err:%v", err)
		return nil, nil, nil, err
	}
	dbParams := make([]*entity.Parameter, 0)
	dbWfParams := make([]*entity.WorkflowParameter, 0)
	for _, v := range mapParams[req.GetWorkflowId()] {
		dbParam, dbWfParam := v.ConvertToDBCreateInfo(req.GetAppBizId(), uin, subUin)
		dbParams = append(dbParams, dbParam)
		dbWfParams = append(dbWfParams, dbWfParam)
	}
	old2newParam := make(map[string]string)
	for i := range dbParams {
		nid := idgenerator.NewUUID()
		old2newParam[dbParams[i].ParameterID] = nid
		dbParams[i].ParameterID = nid
		dbParams[i].UIN = uin
		dbParams[i].SubUIN = subUin
		dbParams[i].ReleaseStatus = entity.ReleaseStatusUnPublished
		dbParams[i].Action = entity.ActionInsert
	}
	for i := range dbWfParams {
		dbWfParams[i].ParameterID = old2newParam[dbWfParams[i].ParameterID]
		dbWfParams[i].WorkFlowID = newWorkflowID
		dbWfParams[i].ReleaseStatus = entity.ReleaseStatusUnPublished
		dbWfParams[i].Action = entity.ActionInsert
	}

	return dbParams, dbWfParams, old2newParam, nil
}

var (
	copySuffix = regexp.MustCompile(`-副本(\d+)$`)
)

func getLastCopyIndex(s string) (string, int) {
	ns := s
	var index int
	var err error
	matched := copySuffix.FindAllStringSubmatch(s, -1)
	for i := len(matched) - 1; i >= 0; i-- {
		v := matched[i]
		if len(v) != 2 {
			continue
		}
		ns = strings.TrimSuffix(s, v[0])
		index, err = strconv.Atoi(v[1])
		if err != nil {
			continue
		}
		break
	}
	return ns, index
}

// genCopyNewWorkflow 生成复制的新工作流
func genCopyNewWorkflow(ctx context.Context, robotId, workflowId string, oldWorkflow *entity.Workflow,
	old2newParam map[string]string) (
	entity.CreateWorkflowParams, error) {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffID := util.StaffID(ctx)
	// 生成工作流名称
	getNewWorkflowName := func(oldWorkflowName string, copyCount int) string {

		// 注意中文字符的处理
		nameSuffix := "-副本" + strconv.Itoa(copyCount)
		remainLength := config.GetMainConfig().VerifyWorkflow.WorkflowNameLen - utf8.RuneCountInString(nameSuffix)

		if remainLength < utf8.RuneCountInString(oldWorkflowName) {
			return string([]rune(oldWorkflowName)[0:remainLength]) + nameSuffix
		} else {
			return oldWorkflowName + nameSuffix
		}
	}
	var workflowName string
	oldName, copyCount := getLastCopyIndex(oldWorkflow.WorkflowName)
	maxTotalWorkflow := config.GetMainConfig().VerifyWorkflow.WorkflowLimit
	if util.SID(ctx) == config.GetMainConfig().QidianSid {
		num, err := rpc.GetQdAccountWorkflowLimit(ctx, util.LoginUin(ctx), oldWorkflow.RobotId)
		if err == nil && num != -1 {
			maxTotalWorkflow = int(num)
		}
	}
	for i := 0; i < maxTotalWorkflow; i++ {
		copyCount += 1
		workflowName = getNewWorkflowName(oldName, copyCount)
		err := checkWorkflowName(ctx, "", robotId, workflowName)
		if err == nil {
			break
		}
		if errs.Code(err) != errs.Code(errors.ErrWorkflowNameDuplicated) {
			return entity.CreateWorkflowParams{}, err
		}
		if i == maxTotalWorkflow-1 {
			return entity.CreateWorkflowParams{}, errors.ErrWorkflowNameDuplicated
		}
	}

	// 画布ID和名称替换
	dialogJson := ""
	if len(oldWorkflow.DialogJsonDraft) > 0 {
		// 替换引用的参数id
		for k, v := range old2newParam {
			oldWorkflow.DialogJsonDraft = strings.ReplaceAll(oldWorkflow.DialogJsonDraft, k, v)
		}
		dialogTree, err := protoutil.JsonToWorkflow(oldWorkflow.DialogJsonDraft)
		if err != nil {
			return entity.CreateWorkflowParams{}, err
		}
		dialogTree.WorkflowID = workflowId
		dialogTree.WorkflowName = workflowName
		dialogJson, err = protoutil.WorkflowToJson(dialogTree)
		if err != nil {
			return entity.CreateWorkflowParams{}, err
		}
	}

	createParams := entity.CreateWorkflowParams{
		WorkflowID:       workflowId,
		WorkflowName:     workflowName,
		WorkflowDesc:     oldWorkflow.WorkflowDesc,
		WorkflowState:    entity.FlowStateDraft,
		Version:          1,
		DialogJsonDraft:  dialogJson,
		DialogJsonEnable: "",
		Uin:              uin,
		SubUin:           subUin,
		StaffID:          staffID,
		ReleaseStatus:    entity.ReleaseStatusUnPublished,
		Action:           entity.ActionInsert,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
		RobotId:          robotId,
	}
	return createParams, nil
}
