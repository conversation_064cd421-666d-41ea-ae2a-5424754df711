package workflow

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	secapi "git.woa.com/sec-api/go/scurl"
	jsoniter "github.com/json-iterator/go"
)

// DebugWorkflowNode 调试Workflow节点
func DebugWorkflowNode(ctx context.Context,
	req *KEP_WF.DebugWorkflowNodeReq) (*KEP_WF.DebugWorkflowNodeRsp, error) {
	var err error
	sid := util.RequestID(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	_, err = permission.CheckRobot(ctx, 1, appBizId)
	if err != nil {
		if errors.Is(err, errors.ErrLicenseInvalid) {
			return nil, errors.ErrLicenseInvalid
		}
		return nil, errors.ErrRobotNotFound
	}
	flowId := req.GetWorkflowId()

	workflow, err := db.GetWorkflowDetail(ctx, flowId, req.GetAppBizId())
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|DebugWorkflowNode,flowId:%s,err:%s",
			sid, flowId, err.Error())
		return nil, err
	}
	tree, err := protoutil.JsonToWorkflow(workflow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "DebugWorkflowNode, %s JsonToWorkflow err:%v", workflow.DialogJsonDraft, err)
		return nil, err
	}
	log.Infof("%s|get tree:%+v", sid, tree)
	return debugNode(ctx, req, tree)
}

func debugNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq,
	tree *KEP_WF.Workflow) (*KEP_WF.DebugWorkflowNodeRsp, error) {
	var err error
	var n *KEP_WF.WorkflowNode
	for _, node := range tree.Nodes {
		if node.GetNodeID() == req.GetNodeId() {
			n = node
			break
		}
	}
	if n == nil {
		return nil, errors.ErrWorkflowNodeNotFound
	}
	if n.NodeType != KEP_WF.NodeType_CODE_EXECUTOR && n.NodeType != KEP_WF.NodeType_TOOL {
		return nil, errors.ErrWorkflowNodeDebugEvil
	}
	if n.NodeData == nil {
		return nil, errors.ErrWorkflowNodeEvil
	}
	rsp := &KEP_WF.DebugWorkflowNodeRsp{}
	if n.NodeType == KEP_WF.NodeType_TOOL {
		data := n.GetToolNodeData()
		if data == nil || !isValidAPI(data) {
			return nil, errors.ErrWorkflowNodeEvil
		}
		rsp.Result, err = requestAPI(ctx, data, req.Header, req.Query, req.Param)
		if err == nil && len(n.GetOutputs()) > 0 {
			outputMap := make(map[string]any)
			err = jsoniter.UnmarshalFromString(rsp.Result, &outputMap)
			if err == nil {
				outputs := n.GetOutputs()
				if len(outputs) == 1 && outputs[0].Title == "Output" { // 输出参数会在最外层包一个Outputs
					outputs = outputs[0].Properties
				}
				result := parseAPIResponseOutputValue(outputMap, outputs)
				rsp.Output = util.Object2String(result)
			}
		}
	} else {
		data := n.GetCodeExecutorNodeData()
		if data == nil || !isValidCode(data) {
			return nil, errors.ErrWorkflowNodeEvil
		}
		rsp.Result, rsp.Logs, err = requestCode(ctx, req.GetAppBizId(), data, n.Inputs, req.Param)
		if err == nil && len(n.GetOutputs()) > 0 {
			outputMap := make(map[string]any)
			err = jsoniter.UnmarshalFromString(rsp.Result, &outputMap)
			if err == nil {
				outputs := n.GetOutputs()
				if len(outputs) == 1 && outputs[0].Title == "Output" { // 输出参数会在最外层包一个Outputs
					outputs = outputs[0].Properties
				}
				result, checkResult := parseCodeOutputValue(outputMap, outputs)
				rsp.Output = util.Object2String(result)
				rsp.OutputCheck = checkResult
			}
		}
	}
	if err != nil {
		rsp.Code = 1
		rsp.Message = err.Error()
	}
	return rsp, nil
}

func isValidAPI(api *KEP_WF.ToolNodeData) bool {
	return api != nil && api.GetAPI() != nil && len(api.GetAPI().GetURL()) > 0 && len(api.GetAPI().GetMethod()) > 0
}

func isValidCode(code *KEP_WF.CodeExecutorNodeData) bool {
	return code != nil && len(code.GetCode()) > 0
}

func requestAPI(ctx context.Context, apiInfo *KEP_WF.ToolNodeData, header, query, param string) (string, error) {
	var err error
	urlInfo, err := url.Parse(apiInfo.GetAPI().GetURL())
	if err != nil {
		return "", err
	}
	headerMap := mergeParams(apiInfo.GetHeader(), header)
	var queryMap map[string]any
	if len(apiInfo.GetQuery()) > 0 && len(query) > 0 {
		queryMap = mergeParams(apiInfo.GetQuery(), query)
		u := urlInfo.Query()
		setValues(u, queryMap)
		urlInfo.RawQuery = u.Encode()
	}

	var r *http.Request
	if apiInfo.GetAPI().GetMethod() == http.MethodPost {
		var paramMap map[string]any
		if len(apiInfo.GetBody()) > 0 && len(param) > 0 {
			paramMap = mergeParams(apiInfo.GetBody(), param)
		}
		r, err = http.NewRequest(apiInfo.GetAPI().GetMethod(), urlInfo.String(),
			strings.NewReader(util.Object2String(paramMap)))
	} else {
		r, err = http.NewRequest(apiInfo.GetAPI().GetMethod(), urlInfo.String(), nil)
	}
	if err != nil {
		return "", err
	}
	setValues(r.Header, headerMap)
	log.Infof("%s|debug api read:%v", util.RequestID(ctx), r)
	safeClient := secapi.NewSafeClient(
		secapi.WithConfTimeout(3 * time.Second))
	resp, err := safeClient.Do(r)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", errs.New(resp.StatusCode, resp.Status)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	return string(body), nil
}

func mergeParams(defined []*KEP_WF.ToolNodeData_RequestParam, input string) map[string]any {
	definedMap := make(map[string]any)
	defer func() {
		for k, v := range definedMap {
			if v == nil {
				delete(definedMap, k) //移除未填充的数据
			}
		}
	}()
	for _, v := range defined {
		definedMap[v.ParamName] = nil // 占位，等待用输入参数填充
		if v.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			data := v.Input.GetUserInputValue()
			if data != nil && len(data.Values) > 0 {
				switch v.ParamType {
				case KEP_WF.TypeEnum_STRING:
					definedMap[v.ParamName] = strings.Join(data.Values, ",")
				case KEP_WF.TypeEnum_INT:
					definedMap[v.ParamName], _ = strconv.ParseInt(data.Values[0], 10, 64)
				case KEP_WF.TypeEnum_FLOAT:
					definedMap[v.ParamName], _ = strconv.ParseFloat(data.Values[0], 64)
				case KEP_WF.TypeEnum_BOOL:
					definedMap[v.ParamName], _ = strconv.ParseBool(data.Values[0])
				case KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_OBJECT:
					definedMap[v.ParamName] = data.Values
				case KEP_WF.TypeEnum_ARRAY_INT:
					intV := make([]int64, len(data.Values))
					for i := 0; i < len(intV); i++ {
						intV[i], _ = strconv.ParseInt(data.Values[i], 10, 64)
					}
					definedMap[v.ParamName] = intV
				case KEP_WF.TypeEnum_ARRAY_FLOAT:
					floatV := make([]float64, len(data.Values))
					for i := 0; i < len(floatV); i++ {
						floatV[i], _ = strconv.ParseFloat(data.Values[i], 64)
					}
					definedMap[v.ParamName] = floatV
				case KEP_WF.TypeEnum_ARRAY_BOOL:
					boolV := make([]bool, len(data.Values))
					for i := 0; i < len(boolV); i++ {
						boolV[i], _ = strconv.ParseBool(data.Values[i])
					}
					definedMap[v.ParamName] = boolV
				}
			}
		}
	}
	inputMap := make(map[string]any)
	err := jsoniter.UnmarshalFromString(input, &inputMap)
	if err != nil {
		return definedMap
	}
	// 用传入的数据替换预置数据
	for k, v := range inputMap {
		if _, ok := definedMap[k]; ok {
			definedMap[k] = v
		}
	}
	return definedMap
}

func setValues(values map[string][]string, param map[string]any) {
	for k, v := range param {
		if v == nil {
			continue
		}
		rv := reflect.ValueOf(v)
		if rv.Kind() == reflect.Array || rv.Kind() == reflect.Slice {
			for i := 0; i < rv.Len(); i++ {
				values[k] = append(values[k], fmt.Sprintf("%v", rv.Index(i).Interface()))
			}
		} else {
			values[k] = []string{fmt.Sprintf("%v", v)}
		}
	}
}

func requestCode(ctx context.Context, appID string, code *KEP_WF.CodeExecutorNodeData,
	inputs []*KEP_WF.InputParam, param string) (string, string, error) {
	//checkRsp, err := rpc.CheckCode(ctx, &bot_exec_pycode_server.CheckCodeRequest{
	//	FuncName: rpc.DefaultEntryFuncName,
	//	FuncCode: code.Code,
	//})
	//if err != nil {
	//	return "", "", err
	//}
	//if checkRsp.Code != 0 {
	//	return "", "", errs.New(int(checkRsp.Code), checkRsp.Message)
	//}
	paramMap := mergeCodeParams(inputs, param)
	log.Infof("%s|input:%v,mergeCodeParams:%+v", util.RequestID(ctx), inputs, paramMap)
	result, err := rpc.RunCode(ctx, appID, code.Code, util.Object2String(paramMap))
	if err != nil {
		return "", "", err
	}
	return result, result, nil
}

func mergeCodeParams(defined []*KEP_WF.InputParam, input string) map[string]any {
	definedMap := make(map[string]any)
	defer func() {
		for k, v := range definedMap {
			if v == nil {
				delete(definedMap, k) //移除未填充的数据
			}
		}
	}()
	for _, v := range defined {
		if v.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			data, ok := v.Input.Source.(*KEP_WF.Input_UserInputValue)
			if ok && len(data.UserInputValue.Values) > 0 {
				definedMap[v.Name] = data.UserInputValue.Values[0]
			}
		} else {
			definedMap[v.Name] = nil
		}
	}
	inputMap := make(map[string]any)
	if len(input) > 0 {
		err := jsoniter.UnmarshalFromString(input, &inputMap)
		if err != nil {
			log.Warnf("mergeCodeParams|UnmarshalFromString input:%v err:%v", input, err)
			return definedMap
		}
	}

	// 用传入的数据替换预置数据
	for k, v := range inputMap {
		if _, ok := definedMap[k]; ok {
			definedMap[k] = v
		}
	}
	return definedMap
}

func parseAPIResponseOutputValue(data map[string]any, params []*KEP_WF.OutputParam) map[string]any {
	res := make(map[string]any)
	for _, param := range params {
		outputValue, ok := data[param.GetTitle()]
		if !ok {
			res[param.GetTitle()] = "参数未返回"
			continue
		}
		value := reflect.ValueOf(outputValue)
		if !value.IsValid() {
			res[param.GetTitle()] = nil
			continue
		}
		switch param.GetType() {
		case KEP_WF.TypeEnum_OBJECT:
			if subData, ok := data[param.GetTitle()].(map[string]any); ok {
				res[param.GetTitle()] = parseAPIResponseOutputValue(subData, param.GetProperties())
			}
		case KEP_WF.TypeEnum_ARRAY_OBJECT:
			if subData, ok := data[param.GetTitle()].([]map[string]any); ok {
				subRes := make([]map[string]any, 0)
				for _, subDataItem := range subData {
					subRes = append(subRes, parseAPIResponseOutputValue(subDataItem, param.GetProperties()))
				}
				res[param.GetTitle()] = subRes
			}
		case KEP_WF.TypeEnum_INT:
			if !value.CanInt() && !value.CanFloat() {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
			} else {
				res[param.GetTitle()] = value.Interface()
			}
		case KEP_WF.TypeEnum_FLOAT:
			if !value.CanInt() && !value.CanFloat() {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
			} else {
				res[param.GetTitle()] = value.Interface()
			}
		case KEP_WF.TypeEnum_BOOL:
			if value.Kind() != reflect.Bool {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
			} else {
				res[param.GetTitle()] = value.Bool()
			}
		case KEP_WF.TypeEnum_ARRAY_STRING:
			parseArrayString(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_INT:
			parseArrayInt(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_FLOAT:
			parseArrayFloat(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_BOOL:
			parseArrayBool(value, res, param.GetType(), param.GetTitle())
		default:
			res[param.GetTitle()] = value.Interface()
		}
	}
	return res
}

func parseCodeOutputValue(data map[string]any,
	params []*KEP_WF.OutputParam) (map[string]any, string) {
	res := make(map[string]any)
	var checkResult strings.Builder
	for _, param := range params {
		outputValue, ok := data[param.GetTitle()]
		if !ok {
			res[param.GetTitle()] = "参数未返回"
			checkResult.WriteString(fmt.Sprintf("%s参数未返回;", param.GetTitle()))
			continue
		}
		value := reflect.ValueOf(outputValue)
		if !value.IsValid() {
			res[param.GetTitle()] = nil
			continue
		}
		hasErr := false
		var tips string
		switch param.GetType() {
		case KEP_WF.TypeEnum_OBJECT:
			if subData, ok := data[param.GetTitle()].(map[string]any); ok {
				res[param.GetTitle()], tips = parseCodeOutputValue(subData, param.GetProperties())
				if len(tips) > 0 {
					checkResult.WriteString(fmt.Sprintf("%s.%s", param.GetTitle(), tips))
				}
			}
		case KEP_WF.TypeEnum_ARRAY_OBJECT:
			if subData, ok := data[param.GetTitle()].([]map[string]any); ok {
				subRes := make([]map[string]any, 0)
				for _, subDataItem := range subData {
					result, tips := parseCodeOutputValue(subDataItem, param.GetProperties())
					if len(tips) > 0 {
						checkResult.WriteString(fmt.Sprintf("%s.%s", param.GetTitle(), tips))
					}
					subRes = append(subRes, result)

				}
				res[param.GetTitle()] = subRes
			}
		case KEP_WF.TypeEnum_INT:
			if !value.CanInt() && !value.CanFloat() {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
				hasErr = true
			} else {
				res[param.GetTitle()] = value.Interface()
			}
		case KEP_WF.TypeEnum_FLOAT:
			if !value.CanInt() && !value.CanFloat() {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
			} else {
				res[param.GetTitle()] = value.Interface()
			}
		case KEP_WF.TypeEnum_BOOL:
			if value.Kind() != reflect.Bool {
				res[param.GetTitle()] = buildWrongType(value.Interface(), param.GetType())
			} else {
				res[param.GetTitle()] = value.Bool()
			}
		case KEP_WF.TypeEnum_ARRAY_STRING:
			parseArrayString(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_INT:
			parseArrayInt(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_FLOAT:
			parseArrayFloat(value, res, param.GetType(), param.GetTitle())
		case KEP_WF.TypeEnum_ARRAY_BOOL:
			parseArrayBool(value, res, param.GetType(), param.GetTitle())
		default:
			res[param.GetTitle()] = value.Interface()
		}
		if hasErr {
			checkResult.WriteString(fmt.Sprintf("%s:%s;", param.GetTitle(), res[param.GetTitle()]))
		}
	}
	return res, checkResult.String()
}

func parseArrayBool(value reflect.Value, res map[string]any, paramType KEP_WF.TypeEnum, paramName string) {
	if value.Kind() != reflect.Array && value.Kind() != reflect.Slice {
		res[paramName] = buildWrongType(value.Interface(), paramType)
	} else if value.Len() > 0 {
		if value.Index(0).Kind() != reflect.Bool {
			res[paramName] = buildWrongType(value.Interface(), paramType)
		} else {
			v := make([]bool, value.Len())
			for i := 0; i < value.Len(); i++ {
				v[i] = value.Index(i).Bool()
			}
			res[paramName] = v
		}
	} else {
		res[paramName] = make([]bool, 0)
	}
}

func parseArrayFloat(value reflect.Value, res map[string]any, paramType KEP_WF.TypeEnum, paramName string) {
	if value.Kind() != reflect.Array && value.Kind() != reflect.Slice {
		res[paramName] = buildWrongType(value.Interface(), paramType)
	} else if value.Len() > 0 {
		jsonStr, err := jsoniter.MarshalToString(value.Interface())
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		var values []float64
		err = jsoniter.UnmarshalFromString(jsonStr, &values)
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		res[paramName] = values
	} else {
		res[paramName] = make([]float64, 0)
	}
}

func parseArrayInt(value reflect.Value, res map[string]any, paramType KEP_WF.TypeEnum, paramName string) {
	if value.Kind() != reflect.Array && value.Kind() != reflect.Slice {
		res[paramName] = buildWrongType(value.Interface(), paramType)
	} else if value.Len() > 0 {
		jsonStr, err := jsoniter.MarshalToString(value.Interface())
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		var values []int64
		err = jsoniter.UnmarshalFromString(jsonStr, &values)
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		res[paramName] = values
	} else {
		res[paramName] = make([]int64, 0)
	}
}

func parseArrayString(value reflect.Value, res map[string]any, paramType KEP_WF.TypeEnum, paramName string) {
	if value.Kind() != reflect.Array && value.Kind() != reflect.Slice {
		res[paramName] = buildWrongType(value.Interface(), paramType)
	} else if value.Len() > 0 {
		jsonStr, err := jsoniter.MarshalToString(value.Interface())
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		var values []string
		err = jsoniter.UnmarshalFromString(jsonStr, &values)
		if err != nil {
			res[paramName] = buildWrongType(value.Interface(), paramType)
			return
		}
		res[paramName] = values
	} else {
		res[paramName] = make([]string, 0)
	}
}

func buildWrongType(v interface{}, t KEP_WF.TypeEnum) string {
	return fmt.Sprintf("定义类型为%v,但返回值为%v", t, v)
}
