package workflow

import (
	"context"
	"net/http"
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func Test_mergeCodeParams(t *testing.T) {
	type args struct {
		defined []*KEP_WF.InputParam
		input   string
	}
	tests := []struct {
		name string
		args args
		want map[string]any
	}{
		{
			name: "success",
			args: args{
				defined: []*KEP_WF.InputParam{
					{
						Name: "A",
						Type: 0,
						Input: &KEP_WF.Input{
							InputType: KEP_WF.InputSourceEnum_USER_INPUT,
							Source: &KEP_WF.Input_UserInputValue{
								UserInputValue: &KEP_WF.UserInputContent{
									Values: []string{"深圳天气"},
								},
							},
						},
					},
				},
				input: "",
			},
			want: map[string]any{
				"A": "深圳天气",
			},
		},
		{
			name: "success2",
			args: args{
				defined: []*KEP_WF.InputParam{
					{
						Name: "A",
						Type: 0,
						Input: &KEP_WF.Input{
							InputType: KEP_WF.InputSourceEnum_USER_INPUT,
							Source: &KEP_WF.Input_UserInputValue{
								UserInputValue: &KEP_WF.UserInputContent{
									Values: []string{"深圳天气"},
								},
							},
						},
					},
				},
				input: `{"B":123}`,
			},
			want: map[string]any{
				"A": "深圳天气",
			},
		},
		{
			name: "success3",
			args: args{
				defined: []*KEP_WF.InputParam{
					{
						Name: "A",
						Type: 0,
						Input: &KEP_WF.Input{
							InputType: KEP_WF.InputSourceEnum_USER_INPUT,
							Source: &KEP_WF.Input_UserInputValue{
								UserInputValue: &KEP_WF.UserInputContent{
									Values: []string{"深圳天气"},
								},
							},
						},
					},
					{
						Name:  "B",
						Type:  0,
						Input: &KEP_WF.Input{},
					},
				},
				input: `{"B":123}`,
			},
			want: map[string]any{
				"A": "深圳天气",
				"B": 123,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := mergeCodeParams(tt.args.defined, tt.args.input)
			assert.EqualValues(t, len(tt.want), len(got), "want len must equal get len")
			for k := range tt.want {
				assert.EqualValues(t, tt.want[k], got[k], "mergeCodeParams(%v, %v)", tt.args.defined, tt.args.input)
			}
		})
	}
}

// 测试debugNode函数
func TestDebugNode(t *testing.T) {
	ctx := context.Background()
	tree := &KEP_WF.Workflow{}
	req := &KEP_WF.DebugWorkflowNodeReq{}
	_, err := debugNode(ctx, req, tree)
	assert.Equal(t, err, errors.ErrWorkflowNodeNotFound)
	tree = &KEP_WF.Workflow{
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID: "1",
			},
		},
	}
	req = &KEP_WF.DebugWorkflowNodeReq{
		NodeId: "2",
	}
	_, err = debugNode(ctx, req, tree)
	assert.Equal(t, err, errors.ErrWorkflowNodeNotFound)

	tree = &KEP_WF.Workflow{
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID: "1",
			},
		},
	}
	req = &KEP_WF.DebugWorkflowNodeReq{
		NodeId: "1",
	}
	_, err = debugNode(ctx, req, tree)
	assert.Equal(t, err, errors.ErrWorkflowNodeDebugEvil)

	tree = &KEP_WF.Workflow{
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID:   "1",
				NodeType: KEP_WF.NodeType_CODE_EXECUTOR,
			},
		},
	}
	req = &KEP_WF.DebugWorkflowNodeReq{
		NodeId: "1",
	}
	_, err = debugNode(ctx, req, tree)
	assert.Equal(t, err, errors.ErrWorkflowNodeEvil)

	tree = &KEP_WF.Workflow{
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID:   "1",
				NodeType: KEP_WF.NodeType_CODE_EXECUTOR,
				NodeData: &KEP_WF.WorkflowNode_CodeExecutorNodeData{},
			},
		},
	}
	req = &KEP_WF.DebugWorkflowNodeReq{
		NodeId: "1",
	}
	_, err = debugNode(ctx, req, tree)
	assert.Equal(t, err, errors.ErrWorkflowNodeEvil)

	tree = &KEP_WF.Workflow{
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID:   "1",
				NodeType: KEP_WF.NodeType_TOOL,
				NodeData: &KEP_WF.WorkflowNode_ToolNodeData{
					ToolNodeData: &KEP_WF.ToolNodeData{
						API: &KEP_WF.ToolNodeData_APIInfo{
							URL:    "http://www.baidu.com",
							Method: http.MethodGet,
						},
					},
				},
			},
		},
	}
	req = &KEP_WF.DebugWorkflowNodeReq{
		NodeId: "1",
	}
	_, err = debugNode(ctx, req, tree)
	assert.Equal(t, err, nil)
}

// 测试isValidAPI函数
func TestIsValidAPI(t *testing.T) {
	// 准备测试数据
	emptyAPI := &KEP_WF.WorkflowNode_ToolNodeData{}
	emptyNodeAPI := &KEP_WF.WorkflowNode_ToolNodeData{
		ToolNodeData: &KEP_WF.ToolNodeData{},
	}
	wrongTypeAPI := &KEP_WF.WorkflowNode_ToolNodeData{
		ToolNodeData: &KEP_WF.ToolNodeData{
			API: &KEP_WF.ToolNodeData_APIInfo{
				URL:    "http://www.baidu.com",
				Method: http.MethodPost,
			},
		},
	}
	wrongURLAPI := &KEP_WF.WorkflowNode_ToolNodeData{
		ToolNodeData: &KEP_WF.ToolNodeData{
			API: &KEP_WF.ToolNodeData_APIInfo{
				Method: http.MethodPost,
			},
		},
	}
	wrongMethodAPI := &KEP_WF.WorkflowNode_ToolNodeData{
		ToolNodeData: &KEP_WF.ToolNodeData{
			API: &KEP_WF.ToolNodeData_APIInfo{
				URL: "http://www.baidu.com",
			},
		},
	}
	validAPI := &KEP_WF.WorkflowNode_ToolNodeData{
		ToolNodeData: &KEP_WF.ToolNodeData{
			API: &KEP_WF.ToolNodeData_APIInfo{
				URL:    "http://www.baidu.com",
				Method: http.MethodPost,
			},
		},
	}
	// 执行测试
	assert.False(t, isValidAPI(nil), "预期nil返回false")
	assert.False(t, isValidAPI(emptyAPI.ToolNodeData), "预期empty返回false")
	assert.False(t, isValidAPI(emptyNodeAPI.ToolNodeData), "预期emptyNode返回false")
	assert.False(t, isValidAPI(wrongTypeAPI.ToolNodeData), "预期wrongType返回false")
	assert.False(t, isValidAPI(wrongURLAPI.ToolNodeData), "预期emptyNode返回false")
	assert.False(t, isValidAPI(wrongMethodAPI.ToolNodeData), "预期emptyNode返回false")
	assert.True(t, isValidAPI(validAPI.ToolNodeData), "预期返回true")

}

// 测试isValidCode函数
func TestIsValidCode(t *testing.T) {
	// 准备测试数据
	emptyCode := &KEP_WF.WorkflowNode_CodeExecutorNodeData{}
	emptyNodeData := &KEP_WF.WorkflowNode_CodeExecutorNodeData{
		CodeExecutorNodeData: &KEP_WF.CodeExecutorNodeData{},
	}
	validCode := &KEP_WF.WorkflowNode_CodeExecutorNodeData{
		CodeExecutorNodeData: &KEP_WF.CodeExecutorNodeData{
			Code: "def main():pass",
		},
	}
	// 执行测试
	assert.False(t, isValidCode(nil), "预期nil返回false")
	assert.False(t, isValidCode(emptyCode.CodeExecutorNodeData), "预期empty返回false")
	assert.False(t, isValidCode(emptyNodeData.CodeExecutorNodeData), "预期emptyNode返回false")
	assert.True(t, isValidCode(validCode.CodeExecutorNodeData), "预期返回true")

}

// 测试checkCodeOutput函数
func TestCheckCodeOutput(t *testing.T) {
	// 准备测试数据
	output := []*KEP_WF.OutputParam{
		{Title: "age", Type: KEP_WF.TypeEnum_INT},
		{Title: "isStudent", Type: KEP_WF.TypeEnum_BOOL},
		{Title: "courses", Type: KEP_WF.TypeEnum_ARRAY_STRING, Properties: []*KEP_WF.OutputParam{}},
		{Title: "ob", Type: KEP_WF.TypeEnum_OBJECT, Properties: []*KEP_WF.OutputParam{
			{Title: "age", Type: KEP_WF.TypeEnum_INT},
			{Title: "isStudent", Type: KEP_WF.TypeEnum_BOOL},
		}},
	}
	result := `{"age": 20, "isStudent": true, "courses": ["Math", "English"],"ob":{"age": 20, "isStudent": true}}`
	outputMap := make(map[string]any)
	_ = jsoniter.UnmarshalFromString(result, &outputMap)
	// 执行测试
	resultMap, _ := parseCodeOutputValue(outputMap, output)

	// 验证结果
	assert.EqualValues(t, resultMap["age"], 20)
	assert.EqualValues(t, resultMap["isStudent"], true)
	assert.EqualValues(t, resultMap["courses"], []string{"Math", "English"})
	ob := resultMap["ob"].(map[string]any)
	assert.EqualValues(t, ob["age"], 20)
	assert.EqualValues(t, ob["isStudent"], true)
	// 更改result，添加一个类型不匹配的情况
	result = `{"age": "twenty", "isStudent": true, "courses": ["Math", "English"]}`
	outputMap = make(map[string]any)
	_ = jsoniter.UnmarshalFromString(result, &outputMap)
	// 执行测试
	resultMap, _ = parseCodeOutputValue(outputMap, output)

	// 验证结果
	assert.EqualValues(t, resultMap["age"], buildWrongType("twenty", KEP_WF.TypeEnum_INT), "预期提示age参数类型错误")
}
