package workflow

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ExportWorkflow 导出Workflow
func ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq) (*KEP_WF.ExportWorkflowRsp, error) {
	rsp := new(KEP_WF.ExportWorkflowRsp)
	staffID := util.StaffID(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	robotInfo, err := permission.CheckRobot(ctx, 1, appBizId)
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	count, err := db.GetWorkflowCountByFlowIds(ctx, req.GetAppBizId(), req.GetWorkflowIds())
	if err != nil {
		return rsp, err
	}
	if len(req.GetWorkflowIds()) != count {
		return rsp, errors.ErrWorkflowNotFound
	}
	refWorkflowIdMap, err := db.GetWorkflowRefsByFlowIds(ctx, req.GetAppBizId(), req.GetWorkflowIds())
	if err != nil {
		return rsp, err
	}
	idMap, ids := db.IDsWorkflowAndRef(refWorkflowIdMap, req.GetWorkflowIds())
	if len(idMap) > config.GetMainConfig().TaskFlow.ExportLimit {
		return nil, errors.ErrWorkflowExportLimit
	}
	if err := createExportWorkflow(ctx, robotInfo.GetCorpId(), staffID, req.GetAppBizId(),
		ids); err != nil {
		return rsp, errors.ErrCreateWorkflowExport
	}
	return rsp, nil
}

// createExportWorkflow 创建导出工作流程任务
func createExportWorkflow(ctx context.Context, corpID, staffID uint64, robotID string, flowIDs []string) error {
	// 添加任务到任务队列
	params := entity.WorkflowExportParams{
		RequestID: util.RequestID(ctx),
		CorpID:    corpID,
		StaffID:   staffID,
		RobotID:   robotID,
		ExportID:  uint64(idgenerator.NewInt64ID()),
		FlowIDs:   flowIDs,
	}
	if err := scheduler.NewExportWorkflowTask(ctx, robotID, params); err != nil {
		return err
	}
	createNoticeReq := &pb.CreateNoticeReq{
		BotBizId:     uint64(encode.StringToInt64(robotID)),
		PageId:       entity.NoticeWorkflowPageID,
		Type:         entity.NoticeTypeWorkflowExport,
		Level:        entity.LevelInfo,
		RelateId:     params.ExportID,
		Content:      entity.WorkflowExportNoticeContentIng,
		IsGlobal:     false,
		IsAllowClose: false,
		CorpId:       corpID,
		StaffId:      staffID,
	}
	if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
		return err
	}
	return nil
}
