// bot-task-config-server
//
// @(#)feedback.go  星期二, 十月 22, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package workflow

import (
	"context"
	"fmt"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/spf13/cast"
)

// DeleteWorkflowFeedback 删除工作流调试反馈
func DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq) (*KEP_WF.DeleteWorkflowFeedbackRsp, error) {
	// 参数校验
	feedbackBizIds := req.GetFeedbackBizIds()
	for _, v := range feedbackBizIds {
		_, err := util.CheckReqParamsIsUint64(ctx, v)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflowFeedback|err:%+v", err)
			return nil, errors.BadWorkflowReqError("传的参数异常")
		}
	}

	rsp := &KEP_WF.DeleteWorkflowFeedbackRsp{}
	if err := db.DeleteWorkflowFeedback(ctx, feedbackBizIds); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowFeedback|err:%+v", err)
		return nil, errors.OpDataFromDBError("删除工作流反馈失败")
	}
	return rsp, nil
}

// DescribeWorkflowFeedback 获取工作流反馈详情
func DescribeWorkflowFeedback(ctx context.Context,
	req *KEP_WF.DescribeWorkflowFeedReq) (*KEP_WF.DescribeWorkflowFeedRsp, error) {
	feedbackBizId := req.GetFeedbackBizId()
	flowName := ""
	appName := ""
	appType := ""
	_, err := util.CheckReqParamsIsUint64(ctx, feedbackBizId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowFeedback|err:%+v", err)
		return nil, errors.BadWorkflowReqError("传的业务Id参数异常")
	}
	// 获取反馈的原因
	feedbackIds := []string{feedbackBizId}
	feedReasonMap, err := db.GetFeedbackReasonByBizId(ctx, feedbackIds)
	if err != nil {
		log.ErrorContextf(ctx, "GetFeedbackReasonByBizId|err:%v", err)
		return nil, errors.OpDataFromDBError("获取详情反馈理由失败")
	}
	reasons := feedReasonMap[feedbackBizId]
	// 获取反馈记录
	feedRecord, err := db.GetWorkflowFeedBackRecordByBizId(ctx, feedbackBizId)
	if err != nil || feedRecord == nil {
		log.ErrorContextf(ctx, "GetFeedbackReasonByBizId|err:%v", err)
		return nil, errors.ErrWorkflowFeedbackNotFound
	}
	// 获取应用信息
	appIds := []uint64{cast.ToUint64(feedRecord.RobotId)}
	appRsp, err := rpc.GetAppInfosByAppIds(ctx, 1, appIds, rpc.DeleteFlagUnDelete)
	if err != nil {
		log.ErrorContextf(ctx, "getAppInfoMap|appIds:%+v|err:%+v", appIds, err)
		return nil, errors.OpDataFromDBError("获取应用信息失败")
	}
	if appRsp != nil {
		appName = appRsp.GetList()[0].BaseConfig.Name
		appType = appRsp.GetList()[0].AppType
	}

	// 获取正确工作流信息
	flowDetail, err := db.GetWorkflowDetail(ctx, feedRecord.WorkflowID, feedRecord.RobotId)
	if err != nil {
		return nil, errors.ErrWorkflowNotFound
	}

	rightNodeName := ""
	jsonStr := flowDetail.DialogJsonDraft
	flowName = flowDetail.WorkflowName
	if len(jsonStr) > 0 {
		workflow, err := protoutil.JsonToWorkflow(jsonStr)
		if err != nil {
			log.ErrorContextf(ctx, "get workflow json fail:%+v", err)
			//return nil, err
		}
		if workflow != nil && len(workflow.Nodes) > 0 {
			for _, v := range workflow.Nodes {
				if v.NodeID == feedRecord.NodeId {
					rightNodeName = v.NodeName
				}
			}
		}
	}

	// 拼接返回
	rsp := &KEP_WF.DescribeWorkflowFeedRsp{
		BusinessId:        feedRecord.BizId,
		Question:          feedRecord.Question,
		Answer:            feedRecord.Answer,
		Reasons:           reasons,
		Status:            uint32(feedRecord.Status),
		AppBizId:          feedRecord.RobotId,
		RejectReason:      feedRecord.RejectReason,
		OptimizedResult:   feedRecord.OptimizedResult,
		AppName:           appName,
		RightNodeId:       feedRecord.NodeId,
		RightNodeName:     rightNodeName,
		RightWorkflowId:   feedRecord.WorkflowID,
		RightWorkflowName: flowName,
		Desc:              feedRecord.Desc,
		FeedType:          entity.FeedBackTypeFlow,
		AppType:           appType,
	}
	return rsp, nil
}

// AddWorkflowFeedback 添加工作流程反馈
func AddWorkflowFeedback(ctx context.Context,
	req *KEP_WF.AddFlowFeedbackReq) (*KEP_WF.AddFlowFeedbackRsp, error) {
	appBizId := req.GetAppBizId()
	sid := util.RequestID(ctx)
	rsp := &KEP_WF.AddFlowFeedbackRsp{}
	rs := req.GetReasons()
	flowId := req.GetWorkflowId()
	nId := req.GetRightNodeId()
	desc := req.GetDesc()
	termsAccepted := req.GetTermsAccepted()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)

	if !termsAccepted {
		return nil, errors.ErrTermsUnaccepted
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	if len(rs) == 0 || len(nId) == 0 || len(flowId) == 0 {
		return nil, errors.BadRequestError("缺少必填参数")
	}

	maxLen := config.GetMainConfig().VerifyWorkflow.WorkflowFeedBackDescMaxLen
	if utf8.RuneCountInString(desc) > maxLen {
		log.InfoContextf(ctx, "AddWorkflowFeedback|desc|len:%d|v:%s", len(desc), desc)
		return nil, errors.BadRequestError(fmt.Sprintf("反馈描述超过最大限制：%d", maxLen))
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteWorkflow permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}

	// 判断工作流是否存在
	flow, err := db.GetWorkflowDetail(ctx, flowId, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "AddWorkflowFeedback|err:%+v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	// 校验nodeId
	nodeInfoMap, err := GetWorkflowNodeInfoMap(flow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "AddWorkflowFeedback|err:%+v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}
	if _, ok := nodeInfoMap[nId]; !ok {
		return nil, errors.BadRequestError("传入的画布节点Id不对")
	}
	createReq := &entity.WorkflowFeedbackRecord{
		WorkflowID: flowId,
		BizId:      fmt.Sprintf("%d", idgenerator.NewInt64ID()),
		CorpId:     util.CorpID(ctx),
		StaffId:    util.StaffID(ctx),
		RobotId:    req.GetAppBizId(),
		SessionId:  req.GetSessionId(),
		RecordId:   req.GetRecordId(),
		Question:   req.GetQuestion(),
		Answer:     req.GetAnswer(),
		NodeId:     req.GetRightNodeId(),
		Uin:        uin,
		SubUin:     subUin,
		Desc:       req.GetDesc(),
	}
	reasons := fillFeedbackReason(createReq, req.GetReasons())
	if err := db.AddWorkflowFeedback(ctx, createReq, reasons, botId); err != nil {
		log.ErrorContextf(ctx, "AddWorkflowFeedback fail, err:%+v", err)
		return nil, errors.OpDataFromDBError("添加反馈失败")
	}
	return rsp, nil
}

// UpdateWorkflowFeedback 更新工作流程反馈
func UpdateWorkflowFeedback(ctx context.Context,
	req *KEP_WF.UpdateFlowFeedbackReq) (*KEP_WF.UpdateFlowFeedbackRsp, error) {
	appBizId := req.GetAppBizId()
	feedBizId := req.GetBusinessId()
	sid := util.RequestID(ctx)
	flowId := req.GetWorkflowId()
	rs := req.GetReasons()
	desc := req.GetDesc()
	rsp := &KEP_WF.UpdateFlowFeedbackRsp{}
	nId := req.GetRightNodeId()
	uin, subUin := util.GetUinAndSubAccountUin(ctx)

	if len(rs) == 0 || len(nId) == 0 || len(flowId) == 0 {
		return nil, errors.BadRequestError("缺少必填参数")
	}
	maxLen := config.GetMainConfig().VerifyWorkflow.WorkflowFeedBackDescMaxLen
	if utf8.RuneCountInString(desc) > maxLen {
		log.InfoContextf(ctx, "AddWorkflowFeedback|desc|len:%d|v:%s", len(desc), desc)
		return nil, errors.BadRequestError(fmt.Sprintf("反馈描述超过最大限制：%d", maxLen))
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	_, err = util.CheckReqParamsIsUint64(ctx, feedBizId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowFeedback|err:%+v", err)
		return nil, errors.BadWorkflowReqError("传的业务Id参数异常")
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteWorkflow permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 判断记录是否存在
	record, err := db.GetWorkflowFeedBackRecordByBizId(ctx, feedBizId)
	if err != nil || record == nil {
		return nil, errors.ErrWorkflowFeedbackNotFound
	}

	// 判断工作流是否存在
	flow, err := db.GetWorkflowDetail(ctx, flowId, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "AddWorkflowFeedback|err:%+v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	// 校验nodeId
	nodeInfoMap, err := GetWorkflowNodeInfoMap(flow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "AddWorkflowFeedback|err:%+v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}
	if _, ok := nodeInfoMap[nId]; !ok {
		return nil, errors.BadRequestError("传入的画布节点Id不对")
	}

	updateReq := &entity.WorkflowFeedbackRecord{
		WorkflowID: flowId, BizId: feedBizId, RobotId: appBizId,
		SessionId: req.GetSessionId(),
		RecordId:  req.GetRecordId(), Question: req.GetQuestion(),
		Answer: req.GetAnswer(), NodeId: req.GetRightNodeId(),
		Uin: uin, SubUin: subUin, Desc: desc,
	}
	reasons := fillFeedbackReason(updateReq, rs)
	if err := db.UpdateWorkflowFeedback(ctx, updateReq, reasons); err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowFeedback fail, err:%+v", err)
		return nil, errors.OpDataFromDBError("更新反馈失败")
	}
	return rsp, nil
}

// UpdateWorkflowFeedbackStatus 更新工作流反馈状态（内部）
func UpdateWorkflowFeedbackStatus(ctx context.Context,
	req *KEP_WF.UpdateWorkflowFeedbackStatusReq) (*KEP_WF.UpdateWorkflowFeedbackStatusRsp, error) {
	rsp := new(KEP_WF.UpdateWorkflowFeedbackStatusRsp)
	botBizId := req.GetAppBizId()
	if len(botBizId) > 0 {
		botId, err := util.CheckReqBotBizIDUint64(ctx, botBizId)
		if err != nil {
			return nil, err
		}
		// 添加判断机器人是否存在(忽略登陆)
		_, err = permission.CheckRobotWithNoUserLogin(ctx, entity.SandboxEnvScene, botId)
		if err != nil {
			return rsp, err
		}
	}

	if err := db.UpdateWorkflowFeedbackStatus(ctx, req); err != nil {
		return rsp, err
	}
	return rsp, nil
}

// UpdateWorkflowFeedbackTapd 更新工作流反馈Tapd（内部）
func UpdateWorkflowFeedbackTapd(ctx context.Context,
	req *KEP_WF.UpdateWorkflowFeedbackTapdReq) (*KEP_WF.UpdateWorkflowFeedbackTapdRsp, error) {
	rsp := new(KEP_WF.UpdateWorkflowFeedbackTapdRsp)
	fBizId := req.GetFeedbackBizId()
	tapd := req.GetTapd()
	if err := db.UpdateWorkflowFeedbackTapd(ctx, fBizId, tapd); err != nil {
		return rsp, err
	}
	return rsp, nil
}

// fillFeedbackReason 获取反馈理由
func fillFeedbackReason(req *entity.WorkflowFeedbackRecord,
	reasons []string) []*entity.WorkflowFeedbackReason {
	fbReasons := make([]*entity.WorkflowFeedbackReason, 0, len(reasons))
	for _, reason := range reasons {
		if len(strings.TrimSpace(reason)) > 0 {
			fbReasons = append(fbReasons, &entity.WorkflowFeedbackReason{
				Reason:     reason,
				RobotId:    req.RobotId,
				Uin:        req.Uin,
				SubUin:     req.SubUin,
				FeedbackId: req.BizId,
			})
		}
	}
	return fbReasons
}

// ListWorkflowFeedback 获取工作流反馈列表
func ListWorkflowFeedback(ctx context.Context,
	req *KEP_WF.ListFlowFeedbackReq) (*KEP_WF.ListFlowFeedbackRsp, error) {
	sid := util.RequestID(ctx)
	rsp := &KEP_WF.ListFlowFeedbackRsp{}

	recordList, total, err := db.ListWorkflowFeedback(ctx, req)
	log.InfoContextf(ctx, "sid:%s|ListWorkflowFeedback|recordList|%+v", sid, recordList)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowFeedback sid:%s|err:%v", sid, err)
		return nil, errors.OpDataFromDBError("获取反馈记录失败")
	}
	if recordList == nil {
		return rsp, nil
	}
	appInfoMap, err := getAppInfoMap(ctx, recordList)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowFeedback｜getAppInfoMap|sid:%s|err:%v", sid, err)
		return nil, errors.OpDataFromDBError("获取应用信息失败")
	}
	feedbackIds := make([]string, 0)
	flowIds := make([]string, 0)
	flowNodeIds := make([]string, 0)

	mm := make(map[string]struct{}, 0)
	for _, v := range *recordList {
		if _, ok := mm[v.BizId]; !ok {
			feedbackIds = append(feedbackIds, v.BizId)
			flowIds = append(flowIds, v.WorkflowID)
			flowNodeIds = append(flowNodeIds, v.NodeId)
		}
	}
	feedReasonMap, err := db.GetFeedbackReasonByBizId(ctx, feedbackIds)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowFeedback sid:%s|err:%v", sid, err)
		return nil, errors.OpDataFromDBError("获取反馈理由失败")
	}
	// 通过 flowIds 获取对应工作流节点信息
	workflowNameMap, workflowNodeNameMap, err := getWorkflowInfoMapByIds(ctx, flowIds)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowFeedback sid:%s|err:%v", sid, err)
		return nil, errors.OpDataFromDBError("获取反馈所属工作流失败")
	}
	list := make([]*KEP_WF.ListFlowFeedbackRsp_Feedback, 0)
	for _, v := range *recordList {
		appName := ""
		appType := ""
		if appInfoMap != nil {
			appInfo := appInfoMap[cast.ToUint64(v.RobotId)]
			appName = appInfo.Name
			appType = appInfo.AppType
		}
		var reasons []string
		if feedReasonMap != nil {
			reasons = feedReasonMap[v.BizId]
		}
		list = append(list, &KEP_WF.ListFlowFeedbackRsp_Feedback{
			BusinessId:   cast.ToUint64(v.BizId),
			Question:     v.Question,
			Answer:       v.Answer,
			Reasons:      reasons,
			Status:       cast.ToUint32(v.Status),
			StatusDesc:   v.StatusDesc(),
			AppName:      appName,
			AppBizId:     v.RobotId,
			AppType:      appType,
			Desc:         v.Desc,
			WorkflowName: workflowNameMap[v.WorkflowID],
			NodeName:     workflowNodeNameMap[v.NodeId],
		})
	}
	rsp.List = list
	rsp.Total = uint32(total)
	return rsp, nil
}

func getWorkflowInfoMapByIds(ctx context.Context, flowIds []string) (map[string]string, map[string]string, error) {
	flowInfos, err := db.GetWorkflowsByFlowIdsWithNoAppId(ctx, flowIds)
	if err != nil {
		return nil, nil, err
	}
	workflowNameMap := make(map[string]string, 0)
	workflowNodeNameMap := make(map[string]string, 0)
	for _, flow := range flowInfos {
		workflowNameMap[flow.WorkflowID] = flow.WorkflowName

		jsonStr := flow.DialogJsonDraft
		workflow, err := protoutil.JsonToWorkflow(jsonStr)
		if err != nil {
			log.ErrorContextf(ctx, "getWorkflowInfoMapByIds|err:%+v", err)
			continue
		}
		for _, v := range workflow.Nodes {
			workflowNodeNameMap[v.NodeID] = v.NodeName
		}
	}
	return workflowNameMap, workflowNodeNameMap, nil
}

// getAppInfoMap 获取引用信息
func getAppInfoMap(ctx context.Context,
	recordList *[]entity.WorkflowFeedbackRecord) (map[uint64]entity.AppInfo, error) {
	mm := make(map[string]struct{}, 0)
	sid := util.RequestID(ctx)
	appIds := make([]uint64, 0)

	if recordList == nil {
		return nil, nil
	}
	// 获取记录中AppIds信息
	for _, v := range *recordList {
		if _, ok := mm[v.RobotId]; !ok {
			appIds = append(appIds, cast.ToUint64(v.RobotId))
		}
	}
	// 获取应用信息
	rsp, err := rpc.GetAppInfosByAppIds(ctx, 1, appIds, rpc.DeleteFlagUnDelete)
	log.InfoContextf(ctx, "sid:%s|getAppInfoMap|appIds:%+v|rsp:%+v", sid, appIds, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|getAppInfoMap|appIds:%+v|err:%+v", sid, appIds, err)
		return nil, err
	}

	// 获取应用的名称
	appMap := make(map[uint64]entity.AppInfo)
	for _, v := range rsp.GetList() {
		appMap[v.AppBizId] = entity.AppInfo{
			Name:    v.BaseConfig.Name,
			AppType: v.AppType,
		}
	}
	return appMap, nil
}
