package workflow

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// GetWorkflowGuideViewed 获取工作流引导展示信息
func GetWorkflowGuideViewed(ctx context.Context, _ *KEP_WF.GetWorkflowGuideViewedRequest) (
	*KEP_WF.GetWorkflowGuideViewedResponse, error) {
	resp := &KEP_WF.GetWorkflowGuideViewedResponse{ViewedKeys: []string{}}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	keys, err := db.GetWorkflowGuideViewedList(ctx, uin, subUin)
	if err != nil {
		log.WarnContextf(ctx, "GetWorkflowGuideViewed|uin:%s|subUin:%s|err:%+v", uin, subUin, err)
		return nil, errors.ErrSystem
	}
	log.InfoContextf(ctx, "GetWorkflowGuideViewed|uin:%s|subUin:%s|keys:%v", uin, subUin, keys)
	resp.ViewedKeys = keys
	return resp, nil
}

// MarkWorkflowGuideViewed 标记工作流引导页面已展示
func MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest) (
	*KEP_WF.MarkWorkflowGuideViewedResponse, error) {
	resp := &KEP_WF.MarkWorkflowGuideViewedResponse{}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	err := db.MarkWorkflowGuideViewed(ctx, uin, subUin, req.GetKey())
	if err != nil {
		log.WarnContextf(ctx, "MarkWorkflowGuideViewed|uin:%s|subUin:%s|err:%+v", uin, subUin, err)
		return nil, errors.ErrSystem
	}
	log.InfoContextf(ctx, "MarkWorkflowGuideViewed|uin:%s|subUin:%s|success", uin, subUin)
	return resp, nil
}
