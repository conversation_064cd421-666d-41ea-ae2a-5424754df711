package workflow

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"math"
	"path/filepath"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/pdlconvert"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"gopkg.in/yaml.v3"
)

// ImportWorkflow 导入工作流程
func ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq) (*KEP_WF.ImportWorkflowRsp, error) {
	log.InfoContextf(ctx, "ImportWorkflow parse start")
	rsp := new(KEP_WF.ImportWorkflowRsp)
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffID := util.StaffID(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	robotInfo, err := permission.CheckRobot(ctx, 1, appBizId)
	if err != nil {
		if errors.Is(err, errors.ErrLicenseInvalid) {
			return nil, errors.ErrLicenseInvalid
		}
		return nil, errors.ErrRobotNotFound
	}
	existWorkflow, err := db.GetExistWorkflowByRobotID(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	existExample, err := db.GetAllExample(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	// 1.自定义变量有一对多情况，所以解析时候不需要校验数据库中是否存在;后面process会替换数据库中的id
	// 2.下面的历史代码，因为查询条件矛盾，返回的existVar为空，恰好符合上面的第一点解析excel校验重名不生效
	//existVar, err := db.GetImportVarParamsInfos(ctx, req.GetAppBizId(), []string{})
	//if err != nil {
	//	return nil, err
	//}
	existVar := make(map[string]*entity.VarParams)
	// 解析校验导入zip数据
	importDataList, errRsp, err := parseImportWorkflowFile(ctx, req, existWorkflow, existExample, existVar,
		robotInfo.GetCorpId(), req.GetAppBizId())
	if err != nil || errRsp != nil {
		return errRsp, err
	}
	log.InfoContextf(ctx, "ImportWorkflow parse importDataList|%+v", importDataList)
	// 构造导入任务数据
	parentTask, tasks, err := fillWorkflowImport(ctx, req.GetAppBizId(), uin, subUin, importDataList)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "ImportWorkflow parse parentTask:%+v|tasks:%+v", parentTask, tasks)
	// 创建导入任务
	err = db.CreateWorkflowImport(ctx, robotInfo.GetCorpId(), staffID, req.GetAppBizId(), req.GetFileName(), parentTask,
		tasks)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "ImportWorkflow parse end")
	return rsp, nil
}

// fillWorkflowImport 组装工作流程数据
func fillWorkflowImport(ctx context.Context, robotID, uin, subUin string, importData []*entity.WorkflowImportData) (
	*entity.WorkflowImport, []*entity.WorkflowImport, error) {
	importID := fmt.Sprintf("%d", idgenerator.NewInt64ID())
	parentTask := &entity.WorkflowImport{
		ImportID: importID,
		RobotID:  robotID,
		Status:   entity.ImportWorkflowStatusProcessing,
		Uin:      uin,
		SubUin:   subUin,
	}
	tasks := make([]*entity.WorkflowImport, 0)
	for _, v := range importData {
		paramDataJson, _ := jsoniter.MarshalToString(v)
		tasks = append(tasks, &entity.WorkflowImport{
			ImportID:       fmt.Sprintf("%d", idgenerator.NewInt64ID()),
			RobotID:        robotID,
			ParentImportID: parentTask.ImportID,
			Params:         paramDataJson,
			Status:         entity.ImportWorkflowStatusWait,
			Message:        "",
			Uin:            uin,
			SubUin:         subUin,
		})
	}
	return parentTask, tasks, nil
}

// constructParamData 构建导入的工作流程数据
func constructParamData(ctx context.Context, workflows []*entity.ExportWorkflow,
	examples []*entity.ExportWorkflowExample, params []*entity.ParamMigrationInfo,
	vars []*entity.ExportWorkflowVar, workflowRefs []*entity.ExportWorkflowRef,
	WorkflowPDLs []*entity.ExportWorkflowPDL) []*entity.WorkflowImportData {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	//var workflowRefExportRows map[string]string
	//var workflowRefExistRow map[string]string
	paramDataList := make([]*entity.WorkflowImportData, 0)         // 工作流程相关数据
	mapExample := make(map[string][]*entity.ExportWorkflowExample) // 示例问法
	mapParam := make(map[string][]*entity.ParamMigrationInfo)      // 参数
	mapVar := make(map[string][]*entity.ExportWorkflowVar)         // 自定义变量
	mapWorkflowRef := make(map[string][]*entity.ExportWorkflowRef) // 工作流引用基本信息
	mapWorkflowPDL := make(map[string][]*entity.ExportWorkflowPDL) // 工作流pdl
	mapOld2New := make(map[string]string)
	for _, flow := range workflows {
		mapOld2New[flow.WorkflowID] = idgenerator.NewUUID() // 提取分配好工作流id，方便后续替换引用的工作流id
		// 提取将分配好工作流id，替换到引用的工作流ID
		//flow.DialogJson = strings.ReplaceAll(flow.DialogJson, item.VarID, newVarID)
	}
	for _, example := range examples {
		mapExample[example.FlowID] = append(mapExample[example.FlowID], example)
	}
	for _, param := range params {
		mapParam[param.FlowID] = append(mapParam[param.FlowID], param)
	}
	for _, varData := range vars {
		mapVar[varData.FlowID] = append(mapVar[varData.FlowID], varData)
	}
	for _, workflowRefData := range workflowRefs {
		mapWorkflowRef[workflowRefData.WorkflowID] = append(mapWorkflowRef[workflowRefData.WorkflowID], workflowRefData)
	}
	for _, workflowPDL := range WorkflowPDLs {
		mapWorkflowPDL[workflowPDL.WorkflowID] = append(mapWorkflowPDL[workflowPDL.WorkflowID], workflowPDL)
	}
	for _, workflow := range workflows {
		paramDataList = append(paramDataList, &entity.WorkflowImportData{
			WorkflowData: workflow,
			Examples:     mapExample[workflow.WorkflowID],
			Params:       mapParam[workflow.WorkflowID],
			Vars:         mapVar[workflow.WorkflowID],
			WorkflowRefs: mapWorkflowRef[workflow.WorkflowID],
			WorkflowPDLs: mapWorkflowPDL[workflow.WorkflowID],
			Old2New:      mapOld2New,
			Uin:          uin,
			SubUin:       subUin,
			//WorkflowName: "",
			//WorkflowDesc: "",
		})
	}
	return paramDataList
}

// getImportWorkflowFile
func getImportWorkflowFile(ctx context.Context, req *KEP_WF.ImportWorkflowReq) (map[string]*zip.File, error) {
	body, err := cos.StorageCli.GetObject(ctx, req.GetCosUrl())
	if err != nil {
		log.WarnContextf(ctx, "getImportWorkflowFile storage GetObject Failed! err:%+v", err)
		return nil, errors.ErrSystem
	}
	zipFileName := strings.TrimSuffix(req.GetFileName(), ".zip")
	if len(zipFileName) == 0 {
		return nil, errors.ErrInvalidFileName
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		return nil, errors.ErrSystem
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		if r.Flags == 0 { // 使用win rar压缩的文件名是gbk编码
			b, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(r.Name)),
				simplifiedchinese.GB18030.NewDecoder()))
			if err != nil {
				log.WarnContextf(ctx, "getImportWorkflowFile decode file name err:%+v", err)
				return nil, errors.ErrSystem
			}
			fileName = string(b)
		}
		mapZipFile[fileName] = r
	}
	// 检查文件是否存在
	if _, ok := mapZipFile[config.GetMainConfig().Workflow.WorkflowFileName]; !ok {
		return nil, errors.ErrImportWorkflowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().Workflow.ExampleFileName]; !ok {
		return nil, errors.ErrImportWorkflowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().Workflow.ParamFileName]; !ok {
		return nil, errors.ErrImportWorkflowFileNotFound
	}
	if _, ok := mapZipFile[config.GetMainConfig().Workflow.VarFileName]; !ok {
		return nil, errors.ErrImportWorkflowFileNotFound
	}
	// TODO(mikel) 兼容历史没有工作流引用和pdl的zip，这里就不加了
	return mapZipFile, nil
}

// parseWorkflowXlsx 解析工作流程导入xlsx
func parseWorkflowXlsx(ctx context.Context, mapZipFile map[string]*zip.File, workflowFileName string,
	mapExistWorkflow map[string]entity.Workflow, robotId string) (
	[]*entity.ExportWorkflow, []byte, bool, error) {
	r, err := mapZipFile[workflowFileName].Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	minRow, maxRow := config.GetMainConfig().Workflow.MinRow, config.GetMainConfig().Workflow.MaxRow
	// 这里还需要限制整体工作流数量的限制
	maxTotalWorkflow := config.GetMainConfig().VerifyWorkflow.WorkflowLimit
	if util.SID(ctx) == config.GetMainConfig().QidianSid {
		num, err := rpc.GetQdAccountWorkflowLimit(ctx, util.LoginUin(ctx), robotId)
		if err == nil && num != -1 {
			maxTotalWorkflow = int(num)
		}
	}
	maxRow = int(math.Min(
		float64(maxRow), float64(maxTotalWorkflow-len(mapExistWorkflow))))
	head := config.GetMainConfig().Workflow.WorkflowHead
	// 检查数据
	mapFlowID := make(map[string]int)
	mapNewName := make(map[string]int)
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		workflowRow, ok := getWorkflowFromRow(ctx, row, mapZipFile)
		if !ok {
			return fmt.Sprintf("工作流程json文件:%s不存在或名称不合法；", workflowRow)
		}
		errMsgs := checkWorkflowFromRow(ctx, workflowRow, i, uniqueKeys, mapFlowID, mapNewName,
			mapExistWorkflow)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	rows, bs, err := file.CheckXlsxContent(ctx, workflowFileName, minRow, maxRow, head, body, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseWorkflowXlsx CheckXlsxContent err:%+v", err)
		return nil, nil, false, err
	}
	workflowRows := make([]*entity.ExportWorkflow, 0)
	for _, row := range rows {
		workflowRow, _ := getWorkflowFromRow(ctx, row, mapZipFile)
		workflowRows = append(workflowRows, workflowRow)
	}
	return workflowRows, bs, err == nil, nil
}

// getWorkflowFromRow
func getWorkflowFromRow(ctx context.Context, row []string,
	mapZipFile map[string]*zip.File) (*entity.ExportWorkflow, bool) {
	sid := util.RequestID(ctx)
	workflowRow := &entity.ExportWorkflow{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelWorkflowTplID:
			workflowRow.WorkflowID = strings.TrimSpace(cell)
		case entity.ExcelWorkflowTpName:
			workflowRow.WorkflowName = strings.TrimSpace(cell)
		case entity.ExcelWorkflowTplDesc:
			workflowRow.WorkflowDesc = strings.TrimSpace(cell)
		//case entity.ExcelWorkflowTplRefWorkflow:
		//	if len(cell) > 0 {
		//		err := jsoniter.UnmarshalFromString(cell, &workflowRow.RefWorkflowIDs)
		//		if err != nil {
		//			return workflowRow, false
		//		}
		//	}
		case entity.ExcelWorkflowTplDialogJson:
			fileName := entity.GetWorkflowJsonFileKey(workflowRow.WorkflowID)
			jsonFile, ok := mapZipFile[fileName]
			if !ok {
				return workflowRow, false
			}
			r, err := jsonFile.Open()
			if err != nil {
				return workflowRow, false
			}
			body, err := io.ReadAll(r)
			if err != nil {
				return workflowRow, false
			}
			dialogJsonStr := string(body)
			_, err = protoutil.JsonToWorkflowForPreCheck(dialogJsonStr)
			if err != nil {
				log.WarnContextf(ctx, "getWorkflowFromRow,JsonToWorkflowForPreCheck err:%s|%+v", sid, err)
				return workflowRow, false
			}
			workflowRow.DialogJson = dialogJsonStr
			err = r.Close()
			if err != nil {
				return workflowRow, false
			}
		}
	}
	return workflowRow, true
}

// checkWorkflowFromRow
func checkWorkflowFromRow(ctx context.Context, workflowRow *entity.ExportWorkflow, i int, uniqueKeys, mapFlowID,
	mapNewWorkflowName map[string]int, mapExistWorkflow map[string]entity.Workflow) []string {
	errMsgs := make([]string, 0)
	// 工作流ID
	flowIDLen := utf8.RuneCountInString(workflowRow.WorkflowID)
	if flowIDLen == 0 {
		errMsgs = append(errMsgs, "工作流ID为空")
	} else {
		if rowIndex, ok := uniqueKeys[workflowRow.WorkflowID]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("工作流ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[workflowRow.WorkflowID] = i
		}
	}
	// 工作流名称
	nameLen := utf8.RuneCountInString(workflowRow.WorkflowName)
	if len(workflowRow.WorkflowName) == 0 {
		errMsgs = append(errMsgs, "工作流名称为空")
	} else {
		if nameLen > config.GetMainConfig().VerifyWorkflow.WorkflowNameLen {
			errMsgs = append(errMsgs, "工作流名称长度超过字符限制")
		}
		if rowIndex, ok := mapNewWorkflowName[workflowRow.WorkflowName]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("工作流名称与第%d行重复", rowIndex+1))
		} else {
			mapNewWorkflowName[workflowRow.WorkflowName] = i
		}
		if _, ok := mapExistWorkflow[workflowRow.WorkflowName]; ok {
			errMsgs = append(errMsgs, "工作流名称与已有工作流重复")
		}
	}

	// 工作流描述(可为空及重复)
	intentDescLen := utf8.RuneCountInString(workflowRow.WorkflowDesc)
	if intentDescLen > config.GetMainConfig().VerifyWorkflow.IntentDescLen {
		errMsgs = append(errMsgs, "工作流描述长度超过字符限制")
	}

	// 画布结构校验
	errMsgs = append(errMsgs, checkImportDialogJson(ctx, workflowRow.WorkflowID, workflowRow.WorkflowName,
		workflowRow.DialogJson)...)
	return errMsgs
}

// checkImportDialogJson
func checkImportDialogJson(ctx context.Context, flowID, flowName, dialogJson string) []string {
	errMsgs := make([]string, 0)
	if utf8.RuneCountInString(dialogJson) == 0 {
		errMsgs = append(errMsgs, "画布结构为空")
		return errMsgs
	}
	tree, err := protoutil.JsonToWorkflow(dialogJson)
	if err != nil {
		log.WarnContextf(ctx, "checkImportDialogJson JsonToWorkflow err:%v", err)
		errMsgs = append(errMsgs, "画布结构解析错误")
		return errMsgs
	}
	if flowID != tree.WorkflowID {
		errMsgs = append(errMsgs, "工作流程ID和画布结构中的流程ID不匹配")
	}
	if flowName != tree.WorkflowName {
		errMsgs = append(errMsgs, "工作流程名称和画布结构中的流程名称不匹配")
	}
	return errMsgs
}

// varParamCheck 变量数据校验
type varParamCheck struct {
	Index int      // Excel中Index
	Param []string // [变量ID,变量名称]
}

// isLegal 校验参数是否合法
func (s varParamCheck) isLegal(varID, varName string) bool {
	if len(s.Param) != 2 {
		return false
	}
	return s.Param[0] == varID && s.Param[1] == varName
}

// parseVarXlsx 解析变量文件
func parseVarXlsx(ctx context.Context, zipFile *zip.File,
	flowIds map[string]struct{}, existVar map[string]*entity.VarParams) (
	[]*entity.ExportWorkflowVar, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().Workflow.VarFileName
	minRow, maxRow := 0, config.GetMainConfig().Workflow.MaxRow
	head := config.GetMainConfig().Workflow.VarHead
	// 检查数据
	mapVarIDCheck := make(map[string]varParamCheck)
	mapVarNameCheck := make(map[string]varParamCheck)

	check := func(i int, row []string, uniqueKeys, totalMap map[string]int) (string, string) {
		varRow := getVarInfoFromRow(row)

		errMsgs, countErr := checkVarRow(varRow, i, uniqueKeys, totalMap, flowIds, mapVarIDCheck, mapVarNameCheck, existVar,
			maxRow)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；"), countErr
		}
		return "", countErr
	}

	rows, bs, err := file.CheckVarXlsxContentCompatibilityHistory(ctx, fileName, minRow, head, body, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseVarXlsx CheckVarXlsxContentCompatibilityHistory err:%+v", err)
		return nil, nil, false, err
	}
	varRows := make([]*entity.ExportWorkflowVar, 0)
	for _, row := range rows {
		varRow := getVarInfoFromRow(row)
		if len(varRow.VarDesc) == 0 {
			varRow.VarDesc = "-"
		}
		if len(varRow.VarType) == 0 {
			varRow.VarType = KEP_WF.TypeEnum_STRING.String()
		}
		varRows = append(varRows, varRow)
	}

	return varRows, bs, err == nil, nil
}

// checkVarRow 变量信息导入行 校验
func checkVarRow(varRow *entity.ExportWorkflowVar, i int, uniquesKeys, totalMap map[string]int,
	flowIds map[string]struct{}, mapVarIDCheck, mapVarNameCheck map[string]varParamCheck,
	mapDBIntentVars map[string]*entity.VarParams, maxRow int) ([]string, string) {
	errMsgs := make([]string, 0)
	if len(varRow.FlowID) == 0 {
		errMsgs = append(errMsgs, "工作流ID为空")
	} else {
		if _, ok := flowIds[varRow.FlowID]; !ok {
			errMsgs = append(errMsgs, "工作流ID在工作流程文件中不存在")
		}
		totalMap[varRow.FlowID] += 1
		//if totalMap[varRow.FlowID] > maxRow {
		//	return errMsgs, fmt.Sprintf("变量表格条数超过%d条，请修改后重新上传", maxRow)
		//}
	}

	// 变量ID
	varIDLen := utf8.RuneCountInString(varRow.VarID)
	if varIDLen == 0 {
		errMsgs = append(errMsgs, "变量ID为空")
	} else {
		intentParamKey := varRow.FlowID + "_" + varRow.VarID
		if rowIndex, ok := uniquesKeys[intentParamKey]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("工作流ID和变量ID与第%d行重复", rowIndex+1))
		} else {
			uniquesKeys[intentParamKey] = i
		}

		if mapDBIntentVars != nil {
			if _, ok := mapDBIntentVars[varRow.VarName]; ok {
				errMsgs = append(errMsgs, fmt.Sprintf("导入变量:%s,与应用内其它变量重复", varRow.VarName))
			}
		}

		if check, ok := mapVarIDCheck[varRow.VarID]; ok &&
			!check.isLegal(varRow.VarID, varRow.VarName) {
			errMsgs = append(errMsgs, fmt.Sprintf("变量数据与第%d行冲突", check.Index+1))
		} else {
			mapVarIDCheck[varRow.VarID] = varParamCheck{
				Index: i,
				Param: []string{varRow.VarID, varRow.VarName},
			}
		}
	}

	// 变量名称
	varNameLen := utf8.RuneCountInString(varRow.VarName)
	if varNameLen == 0 {
		errMsgs = append(errMsgs, "变量名称为空")
	} else {
		maxLimit := 100 // TODO: config.GetMainConfig().VerifyWorkflow.VarParamsTextMax
		if varNameLen > maxLimit {
			errMsgs = append(errMsgs, fmt.Sprintf("变量名称长度超过字符%d限制", maxLimit))
		}
		if check, ok := mapVarNameCheck[varRow.VarName]; ok &&
			!check.isLegal(varRow.VarID, varRow.VarName) {
			errMsgs = append(errMsgs, fmt.Sprintf("变量数据与第%d行冲突", check.Index+1))
		} else {
			mapVarNameCheck[varRow.VarName] = varParamCheck{
				Index: i,
				Param: []string{varRow.VarID, varRow.VarName},
			}
		}
	}
	// 变量类型
	if len(varRow.VarType) > 0 {
		switch varRow.VarType {
		case KEP_WF.TypeEnum_STRING.String(), KEP_WF.TypeEnum_INT.String(), KEP_WF.TypeEnum_FLOAT.String(),
			KEP_WF.TypeEnum_BOOL.String(), KEP_WF.TypeEnum_OBJECT.String(), KEP_WF.TypeEnum_ARRAY_STRING.String(),
			KEP_WF.TypeEnum_ARRAY_INT.String(), KEP_WF.TypeEnum_ARRAY_FLOAT.String(), KEP_WF.TypeEnum_ARRAY_BOOL.String(),
			KEP_WF.TypeEnum_ARRAY_OBJECT.String(),
			KEP_WF.TypeEnum_FILE.String(),
			KEP_WF.TypeEnum_DOCUMENT.String(), KEP_WF.TypeEnum_IMAGE.String(), KEP_WF.TypeEnum_AUDIO.String():
		default:
			errMsgs = append(errMsgs, "变量类型错误")
		}
	}
	return errMsgs, ""
}

// getVarInfoFromRow 从导入行中获取变量信息
func getVarInfoFromRow(row []string) *entity.ExportWorkflowVar {
	varRow := &entity.ExportWorkflowVar{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelVarFlowID:
			varRow.FlowID = strings.TrimSpace(cell)
		case entity.ExcelVarID:
			varRow.VarID = strings.TrimSpace(cell)
		case entity.ExcelVarName:
			varRow.VarName = strings.TrimSpace(cell)
		case entity.ExcelVarDesc:
			varRow.VarDesc = strings.TrimSpace(cell)
		case entity.ExcelVarType:
			varRow.VarType = strings.TrimSpace(cell)
		case entity.ExcelVarDefaultValue:
			varRow.VarDefaultValue = strings.TrimSpace(cell)
		case entity.ExcelVarDefaultFileName:
			varRow.VarDefaultFileName = strings.TrimSpace(cell)
		}
	}
	return varRow
}

// parseWorkflowRefXlsx
func parseWorkflowRefXlsx(ctx context.Context, zipFile *zip.File, importWorkflowMap map[string]struct{},
	existWorkflow map[string]entity.Workflow) (
	[]*entity.ExportWorkflowRef, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().Workflow.WorkflowRefFileName
	minRow, maxRow := 0, config.GetMainConfig().Workflow.MaxRow*100
	head := config.GetMainConfig().Workflow.WorkflowRefHead
	//workflowRefExportAdds := make(map[string]string)
	//workflowRefExistAdds := make(map[string]string)
	workflowRefRows := make([]*entity.ExportWorkflowRef, 0)
	// 检查数据
	check := func(i int, row []string, uniqueKeys map[string]int) string {
		workflowRefRow := getWorkflowRefFromRow(row)
		var errMsgs []string
		workflowRefRows, errMsgs = checkWorkflowRefRow(workflowRefRow, i, uniqueKeys, importWorkflowMap, existWorkflow,
			workflowRefRows)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；")
		}
		return ""
	}
	_, bs, err := file.CheckXlsxContent(ctx, fileName, minRow, maxRow, head, body, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseWorkflowRefXlsx CheckXlsxContent err:%+v", err)
		return nil, nil, false, err
	}
	//workflowRefRows := make([]*entity.ExportWorkflowRef, 0)
	//for _, row := range rows {
	//	workflowRefRows = append(workflowRefRows, getWorkflowRefFromRow(row))
	//}
	return workflowRefRows, bs, err == nil, nil
}

// parseExampleXlsx
func parseExampleXlsx(ctx context.Context, zipFile *zip.File, flowIdMap, existExample map[string]struct{}) (
	[]*entity.ExportWorkflowExample, []byte, bool, error) {
	r, err := zipFile.Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().Workflow.ExampleFileName
	minRow, maxRow := 0, config.GetMainConfig().Workflow.MaxRow
	head := config.GetMainConfig().Workflow.ExampleHead
	// 检查数据
	check := func(i int, row []string, uniqueKeys, totalMap map[string]int) (string, string) {
		exampleRow := getExampleFromRow(row)
		errMsgs, countErr := checkExampleRow(exampleRow, i, uniqueKeys, totalMap, flowIdMap, existExample, maxRow)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；"), countErr
		}
		return "", countErr

	}

	rows, bs, err := file.CheckWorkflowXlsxContent(ctx, fileName, minRow, head, body, check)

	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseExampleXlsx CheckWorkflowXlsxContent err:%+v", err)
		return nil, nil, false, err
	}
	exampleRows := make([]*entity.ExportWorkflowExample, 0)
	for _, row := range rows {
		exampleRows = append(exampleRows, getExampleFromRow(row))
	}
	return exampleRows, bs, err == nil, nil
}

// getWorkflowRefFromRow
func getWorkflowRefFromRow(row []string) *entity.ExportWorkflowRef {
	workflowRefRow := &entity.ExportWorkflowRef{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelWorkflowRefTplWorkflowID:
			workflowRefRow.WorkflowID = strings.TrimSpace(cell)
		case entity.ExcelWorkflowRefTplNodeID:
			workflowRefRow.NodeID = strings.TrimSpace(cell)
		case entity.ExcelWorkflowRefTplWorkflowRefID:
			workflowRefRow.WorkflowRefID = strings.TrimSpace(cell)
		case entity.ExcelWorkflowRefTplWorkflowRefName:
			workflowRefRow.WorkflowRefName = strings.TrimSpace(cell)
		}
	}
	return workflowRefRow
}

// getExampleFromRow
func getExampleFromRow(row []string) *entity.ExportWorkflowExample {
	exampleRow := &entity.ExportWorkflowExample{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelExampleFlowID:
			exampleRow.FlowID = strings.TrimSpace(cell)
		case entity.ExcelExampleID:
			exampleRow.ExampleID = strings.TrimSpace(cell)
		case entity.ExcelExample:
			exampleRow.Example = strings.TrimSpace(cell)
		}
	}
	return exampleRow
}

// checkWorkflowRefRow
func checkWorkflowRefRow(workflowRefRow *entity.ExportWorkflowRef, i int, uniqueKeys map[string]int,
	importWorkflowMap map[string]struct{}, existWorkflow map[string]entity.Workflow,
	workflowRefRows []*entity.ExportWorkflowRef) ([]*entity.ExportWorkflowRef, []string) {
	errMsgs := make([]string, 0)
	if len(workflowRefRow.WorkflowID) == 0 {
		errMsgs = append(errMsgs, "工作流ID为空")
	} else if _, ok := importWorkflowMap[workflowRefRow.WorkflowID]; !ok {
		errMsgs = append(errMsgs, "工作流ID不存在")
	}
	if len(workflowRefRow.NodeID) == 0 {
		errMsgs = append(errMsgs, "工作流引用节点ID为空")
	}
	if len(workflowRefRow.WorkflowRefID) == 0 {
		errMsgs = append(errMsgs, "工作流引用ID为空")
	}
	if len(workflowRefRow.WorkflowRefName) == 0 {
		errMsgs = append(errMsgs, "工作流引用名称为空")
	}
	if len(workflowRefRow.WorkflowRefID) != 0 && len(workflowRefRow.WorkflowRefName) != 0 {
		_, ok1 := importWorkflowMap[workflowRefRow.WorkflowRefID]
		_, ok2 := existWorkflow[workflowRefRow.WorkflowRefName]
		if !ok1 && !ok2 {
			errMsgs = append(errMsgs, "引用的工作流不存在")
		}
		// 优先以导入的为准；导入中存在引用ID，就用导入的；导入中不存在，则根据引用名称找数据库现有的
		var workflowRef entity.ExportWorkflowRef
		workflowRef.WorkflowID = workflowRefRow.WorkflowID
		workflowRef.NodeID = workflowRefRow.NodeID
		workflowRef.WorkflowRefID = workflowRefRow.WorkflowRefID
		if ok1 {
			workflowRef.WorkflowRefNewID = workflowRefRow.WorkflowRefID
			workflowRefRows = append(workflowRefRows, &workflowRef)
		} else if ok2 {
			workflowRef.WorkflowRefNewID = existWorkflow[workflowRefRow.WorkflowRefName].WorkflowID
			workflowRefRows = append(workflowRefRows, &workflowRef)
		}
	}
	if len(workflowRefRow.WorkflowID) != 0 && len(workflowRefRow.NodeID) != 0 &&
		len(workflowRefRow.WorkflowRefID) != 0 {
		workflowRefKey := workflowRefRow.WorkflowID + "_" + workflowRefRow.NodeID + "_" + workflowRefRow.WorkflowRefID
		if rowIndex, ok := uniqueKeys[workflowRefKey]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("工作流ID,节点ID,工作流引用ID与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[workflowRefKey] = i
		}
	}

	return workflowRefRows, errMsgs
}

// checkExampleRow
func checkExampleRow(exampleRow *entity.ExportWorkflowExample, i int, uniqueKeys, totalMap map[string]int,
	flowIdMap, existExample map[string]struct{}, maxRow int) ([]string, string) {
	errMsgs := make([]string, 0)
	if len(exampleRow.FlowID) == 0 {
		errMsgs = append(errMsgs, "工作流ID为空")
	} else {
		if _, ok := flowIdMap[exampleRow.FlowID]; !ok {
			errMsgs = append(errMsgs, "工作流ID不存在")
		}
		totalMap[exampleRow.FlowID] += 1
		//if totalMap[exampleRow.FlowID] > maxRow {
		//	return errMsgs, fmt.Sprintf("示例问法表格条数超过%d条，请修改后重新上传", maxRow)
		//}
	}
	// 语料内容
	if len(exampleRow.Example) == 0 {
		errMsgs = append(errMsgs, "示例问法内容为空")
	} else {
		if rowIndex, ok := uniqueKeys[exampleRow.Example]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("示例问法内容与第%d行重复", rowIndex+1))
		} else {
			uniqueKeys[exampleRow.Example] = i
		}
		if _, ok := existExample[exampleRow.Example]; ok {
			errMsgs = append(errMsgs, "示例问法内容与已有问法重复")
		}
	}
	return errMsgs, ""
}

// parseParamXlsx 解析参数导入文件
func parseParamXlsx(ctx context.Context, mapZipFile map[string]*zip.File,
	paramFileName string, flowIds map[string]struct{}) (
	[]*entity.ParamMigrationInfo, []byte, bool, error) {
	r, err := mapZipFile[paramFileName].Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	fileName := config.GetMainConfig().Workflow.ParamFileName
	minRow, maxRow := 0, config.GetMainConfig().VerifyParameter.ParamMaxNum
	head := config.GetMainConfig().Workflow.ParamHead
	// 检查数据
	mapParamIDCheck := make(map[string]paramCheck)
	mapParamNameCheck := make(map[string]paramCheck)
	check := func(i int, row []string, uniqueKeys, mapParamCount map[string]int) (string, string) {
		paramRow := getParamInfoFromRow(row)

		errMsgs, countErr := checkParamRow(paramRow, i, uniqueKeys, mapParamCount, mapParamIDCheck, mapParamNameCheck,
			flowIds, maxRow)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；"), countErr
		}
		return "", countErr
	}
	rows, bs, err := file.CheckWorkflowXlsxContent(ctx, fileName, minRow, head, body, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseParamXlsx CheckWorkflowXlsxContent err:%+v", err)
		return nil, nil, false, err
	}
	paramRows := make([]*entity.ParamMigrationInfo, 0)
	for _, row := range rows {
		paramRow := getParamInfoFromRow(row)
		paramRows = append(paramRows, paramRow)
	}
	return paramRows, bs, err == nil, nil
}

// getParamInfoFromRow 从导入行中获取参数信息
func getParamInfoFromRow(row []string) *entity.ParamMigrationInfo {
	paramRow := &entity.ParamMigrationInfo{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelParamTplFlowID:
			paramRow.FlowID = strings.TrimSpace(cell)
		case entity.ExcelParamTplNodeID:
			paramRow.NodeID = strings.TrimSpace(cell)
		case entity.ExcelParamTplNodeName:
			paramRow.NodeName = strings.TrimSpace(cell)
		case entity.ExcelParamTplID:
			paramRow.ParamID = strings.TrimSpace(cell)
		case entity.ExcelParamTplName:
			paramRow.ParamName = strings.TrimSpace(cell)
		case entity.ExcelParamTplDesc:
			paramRow.ParamDesc = strings.TrimSpace(cell)
		case entity.ExcelParamTplType:
			paramRow.ParamType = strings.TrimSpace(cell)
		case entity.ExcelParamTplExamples:
			paramRow.CorrectExample = strings.TrimSpace(cell)
		case entity.ExcelParamTplInvalidExamples:
			paramRow.IncorrectExample = strings.TrimSpace(cell)
		}
	}
	return paramRow
}

// paramCheck 参数数据校验
type paramCheck struct {
	Index int      // Excel中Index
	Param []string // [参数ID,参数名称,参数描述,参数示例,实体信息]
}

// isLegal 校验参数是否合法
func (s paramCheck) isLegal(paramID, paramName, paramDesc, paramExamples, entityInfo string) bool {
	if len(s.Param) != 5 {
		return false
	}
	return s.Param[0] == paramID && s.Param[1] == paramName && s.Param[2] == paramDesc &&
		s.Param[3] == paramExamples && s.Param[4] == entityInfo
}

// checkParamRow 参数信息导入行校验
func checkParamRow(paramRow *entity.ParamMigrationInfo, i int, uniquesKeys, mapParamCount map[string]int,
	mapParamIDCheck, mapParamNameCheck map[string]paramCheck, flowIds map[string]struct{}, maxRow int) (
	[]string, string) {
	errMsgs := make([]string, 0)
	if len(paramRow.FlowID) == 0 {
		errMsgs = append(errMsgs, "工作流ID为空")
	} else if _, ok := flowIds[paramRow.FlowID]; !ok {
		errMsgs = append(errMsgs, "工作流ID不存在")
	}
	if len(paramRow.NodeID) == 0 {
		errMsgs = append(errMsgs, "工作流节点ID为空")
	}
	mapParamCount[paramRow.ParamID+paramRow.NodeID] += 1
	//if mapParamCount[paramRow.ParamID+paramRow.NodeID] > maxRow {
	//	return errMsgs, fmt.Sprintf("同一个节点下最多允许参数个数为: %d", maxRow)
	//}
	// 参数ID
	paramIDLen := utf8.RuneCountInString(paramRow.ParamID)
	if paramIDLen == 0 {
		errMsgs = append(errMsgs, "参数ID为空")
	} else {
		intentParamKey := paramRow.FlowID + "_" + paramRow.ParamID
		if rowIndex, ok := uniquesKeys[intentParamKey]; ok {
			errMsgs = append(errMsgs, fmt.Sprintf("工作流ID和参数ID与第%d行重复", rowIndex+1))
		} else {
			uniquesKeys[intentParamKey] = i
		}
		if check, ok := mapParamIDCheck[paramRow.ParamID]; ok &&
			!check.isLegal(paramRow.ParamID, paramRow.ParamName, paramRow.ParamDesc,
				paramRow.ParamType, "") {
			errMsgs = append(errMsgs, fmt.Sprintf("参数数据与第%d行冲突", check.Index+1))
		} else {
			mapParamIDCheck[paramRow.ParamID] = paramCheck{
				Index: i,
				Param: []string{paramRow.ParamID, paramRow.ParamName, paramRow.ParamDesc,
					paramRow.ParamType, ""},
			}
		}
	}
	// 参数名称
	paramNameLen := utf8.RuneCountInString(paramRow.ParamName)
	if paramNameLen > 0 {
		if _, ok := mapParamNameCheck[paramRow.FlowID+paramRow.NodeID+paramRow.ParamName]; ok {
			errMsgs = append(errMsgs, "参数名称重复")
		} else {
			mapParamNameCheck[paramRow.FlowID+paramRow.NodeID+paramRow.ParamName] = paramCheck{}
		}

	}
	// 参数基本信息｜示例信息校验
	errMsgs = append(errMsgs, checkParamRowLegal(paramRow)...)
	return errMsgs, ""
}

// checkParamRowLegal 参数描述｜示例｜实体信息导入行校验
func checkParamRowLegal(paramRow *entity.ParamMigrationInfo) []string {
	errMsgs := make([]string, 0)
	errMsgs = checkImportParam(paramRow.ParamName, paramRow.ParamDesc, paramRow.ParamType, errMsgs)
	errMsgs = checkImportParamExample(paramRow.CorrectExample, errMsgs)
	errMsgs = checkImportParamExample(paramRow.IncorrectExample, errMsgs)
	return errMsgs
}

// checkImportParam 校验参数的基本信息
func checkImportParam(paramName, paramDesc, paramType string, errMsgs []string) []string {
	maxNameLen := config.GetMainConfig().VerifyParameter.ParamDescMaxLen
	maxDescLen := config.GetMainConfig().VerifyParameter.ParamDescMaxLen

	// 参数名长度校验
	if utf8.RuneCountInString(paramName) == 0 || utf8.RuneCountInString(paramName) > maxNameLen {
		errMsgs = append(errMsgs, fmt.Sprintf("参数名称应该在 1~%d个字符，请重新填写", maxNameLen))
	}

	// 参数描述长度校验
	if utf8.RuneCountInString(paramDesc) == 0 || utf8.RuneCountInString(paramDesc) > maxDescLen {
		errMsgs = append(errMsgs, fmt.Sprintf("参数描述应该在 1~%d个字符，请重新填写", maxDescLen))
	}

	// 参数类型校验
	if _, ok := KEP_WF.TypeEnum_value[paramType]; !ok {
		errMsgs = append(errMsgs, "参数类型不合法，请重新填写")
	}
	return errMsgs
}
func checkImportParamExample(list string, errMsgs []string) []string {
	//TODO: 补充检查
	return errMsgs
}

// parseImportWorkflowFile
func parseImportWorkflowFile(ctx context.Context, req *KEP_WF.ImportWorkflowReq,
	existWorkflow map[string]entity.Workflow, existExample map[string]struct{}, existVar map[string]*entity.VarParams,
	corpID uint64, robotId string) ([]*entity.WorkflowImportData, *KEP_WF.ImportWorkflowRsp, error) {
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)
	// 解析zip文件
	mapZipFile, err := getImportWorkflowFile(ctx, req)
	if err != nil {
		return nil, nil, err
	}
	// 解析工作流程文件
	workflow, isWFOk, err := parseWorkflow(ctx, mapZipFile, existWorkflow, zipWriter, robotId)
	if err != nil {
		return nil, nil, err
	}
	importWorkflowMap := make(map[string]struct{})
	for _, v := range workflow {
		importWorkflowMap[v.WorkflowID] = struct{}{}
	}
	// 解析语料文件
	examples, isExOk, err := parseExample(ctx, mapZipFile, importWorkflowMap, existExample, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析参数文件
	params, isParamOk, err := parseParam(ctx, mapZipFile, importWorkflowMap, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析自定义变量文件
	vars, isVarOk, err := parseVar(ctx, mapZipFile, importWorkflowMap, existVar, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析工作流引用文件
	workflowRefs, isWorkflowRefOk, err := parseWorkflowRef(ctx, mapZipFile, importWorkflowMap,
		existWorkflow, zipWriter)
	if err != nil {
		return nil, nil, err
	}
	// 解析工作流程PDL文件
	workflowPDL, isWorkflowPDLOk, err := parseWorkflowPDL(ctx, mapZipFile, importWorkflowMap, zipWriter, robotId)
	if err != nil {
		return nil, nil, err
	}
	zipWriter.Close()
	log.InfoContextf(ctx, "parseImportWorkflowFile parse file end isWFOk:%s,isExOk:%s,isParamOk:%s,isVarOk:%s,"+
		"isWorkflowRefOk:%s", isWFOk, isExOk, isParamOk, isVarOk, isWorkflowRefOk)
	// 全部检查成功后才导入
	if isWFOk && isExOk && isParamOk && isVarOk && isWorkflowRefOk && isWorkflowPDLOk {
		return constructParamData(ctx, workflow, examples, params, vars, workflowRefs, workflowPDL), nil, nil
	}
	checkZipFileName := fmt.Sprintf("错误_%s", req.GetFileName())
	cosPath := cos.GetCorpCOSFilePath(corpID, checkZipFileName)
	if err := cos.StorageCli.PutObject(ctx, zipBuffer.Bytes(), cosPath); err != nil {
		log.WarnContextf(ctx, "parseImportWorkflowFile putObject Failed! err:%s", err)
		return nil, nil, err
	}
	url, err := cos.StorageCli.GetPreSignedURL(ctx, cosPath)
	if err != nil {
		log.WarnContextf(ctx, "parseImportWorkflowFile getPreSingedURL Failed! err:%+v", err)
		return nil, nil, err
	}
	return nil, &KEP_WF.ImportWorkflowRsp{ErrorMsg: "文件数据存在错误，请下载并查看错误标注文件", ErrorLink: url,
		ErrorLinkText: "下载"}, nil
}

// parseWorkflow 解析工作流程文件
func parseWorkflow(ctx context.Context, mapZipFile map[string]*zip.File, mapExistName map[string]entity.Workflow,
	zipWriter *zip.Writer, robotId string) ([]*entity.ExportWorkflow, bool, error) {
	workflowFileName := config.GetMainConfig().Workflow.WorkflowFileName
	checkWorkflowFileName := workflowFileName
	workflowRows, workflowBuf, ok, err := parseWorkflowXlsx(ctx, mapZipFile, workflowFileName, mapExistName, robotId)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		checkWorkflowFileName = fmt.Sprintf("错误_%s", workflowFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkWorkflowFileName, Data: workflowBuf})
	log.InfoContextf(ctx, "ImportWorkflow parseWorkflow ok")
	return workflowRows, ok, nil
}

// parseWorkflowRef 解析工作流程引用文件
func parseWorkflowRef(ctx context.Context, mapZipFile map[string]*zip.File, importWorkflowMap map[string]struct{},
	existWorkflow map[string]entity.Workflow, zipWriter *zip.Writer) (
	[]*entity.ExportWorkflowRef, bool, error) {
	workflowRefFileName := config.GetMainConfig().Workflow.WorkflowRefFileName
	checkWorkflowRefFileName := workflowRefFileName

	var workflowRefRows []*entity.ExportWorkflowRef
	var workflowRefRowsBuf []byte

	var err error
	f, ok := mapZipFile[workflowRefFileName]
	if ok {
		// 解析 工作流引用文件
		workflowRefRows, workflowRefRowsBuf, ok, err = parseWorkflowRefXlsx(ctx, f,
			importWorkflowMap, existWorkflow)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			checkWorkflowRefFileName = fmt.Sprintf("错误_%s", workflowRefFileName)
		}
		_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkWorkflowRefFileName, Data: workflowRefRowsBuf})
	} else {
		ok = true
	}
	log.InfoContextf(ctx, "ImportWorkflow parseWorkflowRef ok")
	return workflowRefRows, ok, nil
}

// parseExample 解析语料文件
func parseExample(ctx context.Context, mapZipFile map[string]*zip.File, flowIdMap map[string]struct{},
	existExample map[string]struct{}, zipWriter *zip.Writer) ([]*entity.ExportWorkflowExample, bool, error) {
	exampleFileName := config.GetMainConfig().Workflow.ExampleFileName
	checkExampleFileName := exampleFileName
	exampleRows, exampleBuf, ok, err := parseExampleXlsx(ctx, mapZipFile[exampleFileName], flowIdMap, existExample)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		checkExampleFileName = fmt.Sprintf("错误_%s", exampleFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkExampleFileName, Data: exampleBuf})
	log.InfoContextf(ctx, "ImportWorkflow parseExample ok")
	return exampleRows, ok, nil
}

// parseParam 解析参数文件
func parseParam(ctx context.Context, mapZipFile map[string]*zip.File, flowIds map[string]struct{},
	zipWriter *zip.Writer) ([]*entity.ParamMigrationInfo, bool, error) {
	paramFileName := config.GetMainConfig().Workflow.ParamFileName
	checkParamFileName := paramFileName
	params, paramBuf, ok, err := parseParamXlsx(ctx, mapZipFile, paramFileName, flowIds)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		checkParamFileName = fmt.Sprintf("错误_%s", paramFileName)
	}
	_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkParamFileName, Data: paramBuf})
	log.InfoContextf(ctx, "ImportWorkflow parseParam ok")
	return params, ok, nil
}

// parseVar 解析自定义变量文件
func parseVar(ctx context.Context, mapZipFile map[string]*zip.File, flowIds map[string]struct{},
	existVar map[string]*entity.VarParams, zipWriter *zip.Writer) ([]*entity.ExportWorkflowVar, bool, error) {
	varParamsFileName := config.GetMainConfig().Workflow.VarFileName
	checkIntentVarParamsName := varParamsFileName
	var varRows []*entity.ExportWorkflowVar
	var varRowsBuf []byte
	var err error
	f, ok := mapZipFile[varParamsFileName]
	if ok {
		// 解析 变量文件
		varRows, varRowsBuf, ok, err = parseVarXlsx(ctx, f, flowIds, existVar)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			checkIntentVarParamsName = fmt.Sprintf("错误_%s", varParamsFileName)
		}
		_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkIntentVarParamsName, Data: varRowsBuf})
	}
	log.InfoContextf(ctx, "ImportWorkflow parseVar ok")
	return varRows, ok, nil
}

// parseWorkflowPDL 解析工作流程PDL文件
func parseWorkflowPDL(ctx context.Context, mapZipFile map[string]*zip.File, flowIds map[string]struct{},
	zipWriter *zip.Writer, robotId string) ([]*entity.ExportWorkflowPDL, bool, error) {
	workflowPDLFileName := config.GetMainConfig().Workflow.WorkflowPDLFileName
	checkWorkflowPDLFileName := workflowPDLFileName
	var workflowPDFRows []*entity.ExportWorkflowPDL
	var workflowPDFRowsBuf []byte
	var err error
	_, ok := mapZipFile[workflowPDLFileName]
	if ok {
		workflowPDFRows, workflowPDFRowsBuf, ok, err = parseWorkflowPDLXlsx(ctx, mapZipFile, flowIds, checkWorkflowPDLFileName, robotId)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			checkWorkflowPDLFileName = fmt.Sprintf("错误_%s", workflowPDLFileName)
		}
		_ = file.WriteZipData(zipWriter, file.FileInfo{FileName: checkWorkflowPDLFileName, Data: workflowPDFRowsBuf})
	} else {
		ok = true
	}
	log.InfoContextf(ctx, "ImportWorkflow parseWorkflowPDL ok")
	return workflowPDFRows, ok, nil
}

// parseWorkflowPDLXlsx 解析工作流程PDL导入xlsx
func parseWorkflowPDLXlsx(ctx context.Context, mapZipFile map[string]*zip.File, flowIds map[string]struct{},
	workflowPDLFileName string, robotId string) (
	[]*entity.ExportWorkflowPDL, []byte, bool, error) {
	r, err := mapZipFile[workflowPDLFileName].Open()
	if err != nil {
		return nil, nil, false, err
	}
	defer r.Close()
	body, err := io.ReadAll(r)
	if err != nil {
		return nil, nil, false, err
	}
	minRow, _ := 0, config.GetMainConfig().Workflow.MaxRow
	head := config.GetMainConfig().Workflow.WorkflowPDLHead
	workflowPDLRows := make([]*entity.ExportWorkflowPDL, 0)
	// 检查数据
	check := func(i int, row []string, uniqueKeys, mapParamCount map[string]int) (string, string) {
		workflowPDLRow, ok := getWorkflowPDLFromRow(ctx, row, mapZipFile)
		if !ok {
			return fmt.Sprintf("工作流程PDLjson文件:%s不存在或名称不合法；", workflowPDLRow), ""
		}
		errMsgs, countErr := checkWorkflowPDLRow(ctx, workflowPDLRow, flowIds)
		if len(errMsgs) > 0 {
			return strings.Join(errMsgs, "；"), countErr
		}
		workflowPDLRows = append(workflowPDLRows, workflowPDLRow)
		return "", ""
	}
	_, bs, err := file.CheckWorkflowXlsxContent(ctx, workflowPDLFileName, minRow, head, body, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseWorkflowPDLXlsx CheckWorkflowXlsxContent err:%+v", err)
		return nil, nil, false, err
	}
	//workflowPDLRows := make([]*entity.ExportWorkflowPDL, 0)
	//for _, row := range rows {
	//	workflowRow, _ := getWorkflowPDLFromRow(ctx, row, mapZipFile)
	//	workflowPDLRows = append(workflowPDLRows, workflowRow)
	//}
	return workflowPDLRows, bs, err == nil, nil
}

// getWorkflowPDLFromRow
func getWorkflowPDLFromRow(ctx context.Context, row []string,
	mapZipFile map[string]*zip.File) (*entity.ExportWorkflowPDL, bool) {
	workflowPDLRow := &entity.ExportWorkflowPDL{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelWorkflowPDLTplID:
			workflowPDLRow.WorkflowID = strings.TrimSpace(cell)
		case entity.ExcelWorkflowPDLTplContent:
			workflowPDLRow.Constraints = strings.TrimSpace(cell)
		//case entity.ExcelWorkflowPDLTplDialogJson:
		//	pdlFileName := entity.GetWorkflowPDLJsonFileKey(workflowPDLRow.WorkflowID)
		//	flag, jsonString := mapToJson(ctx, mapZipFile, pdlFileName)
		//	if !flag {
		//		return workflowPDLRow, false
		//	}
		//	workflowPDLRow.DialogJson = jsonString
		case entity.ExcelWorkflowPDLParamTplDialogJson:
			pdlParamFileName := entity.GetWorkflowPDLParamJsonFileKey(workflowPDLRow.WorkflowID)
			flag, jsonString := mapToJson(ctx, mapZipFile, pdlParamFileName)
			if !flag {
				return workflowPDLRow, false
			}
			workflowPDLRow.ParameterJson = jsonString
		case entity.ExcelWorkflowPDLContentTplDialogJson:
			pdlContentFileName := entity.GetWorkflowPDLContentJsonFileKey(workflowPDLRow.WorkflowID)
			flag, jsonString := mapToJson(ctx, mapZipFile, pdlContentFileName)
			if !flag {
				return workflowPDLRow, false
			}
			workflowPDLRow.ContentJson = jsonString
		case entity.ExcelWorkflowPDLToolTplDialogJson:
			pdlToolFileName := entity.GetWorkflowPDLToolJsonFileKey(workflowPDLRow.WorkflowID)
			flag, jsonString := mapToJson(ctx, mapZipFile, pdlToolFileName)
			if !flag {
				return workflowPDLRow, false
			}
			workflowPDLRow.ToolJson = jsonString
		}
	}
	return workflowPDLRow, true
}

func mapToJson(ctx context.Context, mapZipFile map[string]*zip.File, pdlToolFileName string) (bool, string) {
	log.InfoContextf(ctx, "mapToJson---|%s", pdlToolFileName)
	jsonFile, ok := mapZipFile[pdlToolFileName]
	if !ok {
		log.WarnContextf(ctx, "mapToJson mapZipFile fail")
		return false, ""
	}
	r, err := jsonFile.Open()
	if err != nil {
		log.WarnContextf(ctx, "mapToJson jsonFile open fail")
		return false, ""
	}
	body, err := io.ReadAll(r)
	if err != nil {
		log.WarnContextf(ctx, "mapToJson ReadAll fail")
		return false, ""
	}
	dialogJsonStr := string(body)
	err = r.Close()
	if err != nil {
		log.WarnContextf(ctx, "mapToJson close fail")
		return false, ""
	}
	return true, dialogJsonStr
}

// checkWorkflowPDLRow
func checkWorkflowPDLRow(ctx context.Context, workflowPDLRow *entity.ExportWorkflowPDL, flowIds map[string]struct{}) ([]string, string) {
	errMsgs := make([]string, 0)
	if len(workflowPDLRow.WorkflowID) == 0 {
		log.WarnContextf(ctx, "checkWorkflowPDLRow workflowPDLRow.WorkflowID is empty")
		errMsgs = append(errMsgs, "工作流ID为空")
	} else {
		if _, ok := flowIds[workflowPDLRow.WorkflowID]; !ok {
			log.WarnContextf(ctx, "checkWorkflowPDLRow workflowPDLRow.WorkflowID is not exist")
			errMsgs = append(errMsgs, "工作流ID不存在")
		}
	}
	// 画布结构校验
	errMsgs = append(errMsgs, checkImportPDLDialogJson(ctx, workflowPDLRow)...)
	return errMsgs, ""
}

// checkImportPDLDialogJson
func checkImportPDLDialogJson(ctx context.Context, workflowPDLRow *entity.ExportWorkflowPDL) []string {
	errMsgs := make([]string, 0)
	//if utf8.RuneCountInString(workflowPDLRow.DialogJson) == 0 {
	//	errMsgs = append(errMsgs, "pdl画布结构为空")
	//	log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow.DialogJson is empty")
	//	return errMsgs
	//}
	//tree, err := protoutil.JsonToWorkflow(workflowPDLRow.DialogJson)
	//if err != nil {
	//	log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow JsonToWorkflow err|%+v", err)
	//	errMsgs = append(errMsgs, "PDL画布结构解析错误")
	//	return errMsgs
	//}
	//if workflowPDLRow.WorkflowID != tree.WorkflowID {
	//	log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow.WorkflowID is different")
	//	errMsgs = append(errMsgs, "工作流程ID和PDL画布结构中的流程ID不匹配")
	//}
	_, err := protoutil.JsonToParameterInfoList(workflowPDLRow.ParameterJson)
	if err != nil {
		log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow JsonToParameterInfoList err|%+v", err)
		errMsgs = append(errMsgs, "PDL参数结构解析错误")
		return errMsgs
	}
	//log.Infof("workflowPDLRow.ToolJson--|%s", workflowPDLRow.ToolJson)
	_, err = protoutil.JsonToPDLToolsInfo(workflowPDLRow.ToolJson)
	if err != nil {
		log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow JsonToPDLToolsInfo err|%+v", err)
		errMsgs = append(errMsgs, "PDL工具信息结构解析错误")
		return errMsgs
	}
	// 检验PDL INFO内容
	pdlContent := pdlconvert.PDLContent{}
	err = yaml.Unmarshal([]byte(workflowPDLRow.ContentJson), &pdlContent)
	if err != nil {
		log.WarnContextf(ctx, "checkImportPDLDialogJson workflowPDLRow pdlContent yaml err|%+v", err)
		errMsgs = append(errMsgs, "PDL内容信息结构解析错误")
		return errMsgs
	}
	return errMsgs
}
