package workflow

import (
	"archive/zip"
	"bytes"
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func Test_parseWorkflow(t *testing.T) {
	type args struct {
		mapExistName map[string]entity.Workflow
	}
	tests := []struct {
		name    string
		args    args
		want    []*entity.ExportWorkflow
		want1   bool
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				mapExistName: nil,
			},
			want: []*entity.ExportWorkflow{
				{
					WorkflowID:   "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					WorkflowName: "病人信息查询",
					WorkflowDesc: "描述: 通过问题提取检查项，医学指标，药品名称以及药品种类，去做相应的检索\n示例: 谷氨酰转酞酶：114U/L ; 这个偏高是什么原因造成的,近一周T淋巴细胞绝计数趋势,皮质醇指标趋势,近一周酸碱值的趋势,近一周氧分压的趋势,近一周血氧含量的趋势,患者住院期间的心率，血压，呼吸率和血氧饱和度变化趋势,患者血气检查的异常指标,病人呼吸支持的变化,病人肝功能指标变化，病人的用药情况,低血压事件发生",
					DialogJson:   "{\"TaskFlowID\":\"3512dd22-550f-42a3-bfc8-7fe72ef24287\",\"TaskFlowName\":\"病人信息查询\",\"TaskFlowDesc\":\"描述: 通过问题提取检查项，医学指标，药品名称以及药品种类，去做相应的检索\\n示例: 谷氨酰转酞酶：114U/L ; 这个偏高是什么原因造成的,近一周T淋巴细胞绝计数趋势,皮质醇指标趋势,近一周酸碱值的趋势,近一周氧分压的趋势,近一周血氧含量的趋势,患者住院期间的心率，血压，呼吸率和血氧饱和度变化趋势,患者血气检查的异常指标,病人呼吸支持的变化,病人肝功能指标变化，病人的用药情况,低血压事件发生\",\"SessionMode\":1,\"Nodes\":[{\"NodeID\":\"start\",\"NodeType\":\"START\",\"NodeName\":\"开始\",\"NodeUI\":{\"X\":\"70\",\"Y\":\"60\"},\"Branches\":[{\"BranchID\":\"edge-0.79810973496941861719457836074\",\"BranchType\":\"DIRECT\",\"ConditionInfo\":{},\"NextNodeID\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\",\"PrevNodeID\":\"start\"}]},{\"NodeID\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\",\"NodeType\":\"API\",\"NodeName\":\"智能接口1\",\"NodeUI\":{\"X\":\"70\",\"Y\":\"220\"},\"Branches\":[{\"BranchID\":\"edge-0.070570893216546081719386410706\",\"BranchType\":\"DIRECT\",\"ConditionInfo\":{},\"NextNodeID\":\"b75c58db-815b-fd53-6e22-ca704c7ed15b\",\"PrevNodeID\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\"}],\"ApiNodeData\":{\"API\":{\"URL\":\"http://www.baidu.com/\",\"Method\":\"GET\"},\"Request\":[{\"ParamID\":\"1740d04a-a4cc-0b45-0819-782aa5a58a93\",\"ParamName\":\"cls\",\"ParamType\":\"string\",\"SourceType\":\"SLOT\",\"SlotValueData\":{\"SlotID\":\"96d1efb5-ea47-4e69-b86f-3facd11406c3\",\"AskType\":\"LLM\",\"CustomAsk\":\"\",\"FormatDesc\":\"\"},\"SubRequest\":[],\"IsRequired\":false},{\"ParamID\":\"6d63d6dd-87ce-79cc-d74d-2ad026e6d986\",\"ParamName\":\"sign\",\"ParamType\":\"string\",\"SourceType\":\"SLOT\",\"SlotValueData\":{\"SlotID\":\"a96b042b-021b-4934-b3db-bd1a5b7fe8f2\",\"AskType\":\"LLM\",\"CustomAsk\":\"\",\"FormatDesc\":\"\"},\"SubRequest\":[],\"IsRequired\":false},{\"ParamID\":\"03650386-0c08-629a-ed7c-364221cb9f33\",\"ParamName\":\"check\",\"ParamType\":\"string\",\"SourceType\":\"SLOT\",\"SlotValueData\":{\"SlotID\":\"f4949e3d-fad3-4338-b5dd-5a70e89670ec\",\"AskType\":\"LLM\",\"CustomAsk\":\"\",\"FormatDesc\":\"\"},\"SubRequest\":[],\"IsRequired\":false},{\"ParamID\":\"dd30eebf-8e2e-6309-bec3-c40f1ca365db\",\"ParamName\":\"drug\",\"ParamType\":\"string\",\"SourceType\":\"SLOT\",\"SlotValueData\":{\"SlotID\":\"0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf\",\"AskType\":\"LLM\",\"CustomAsk\":\"\",\"FormatDesc\":\"\"},\"SubRequest\":[],\"IsRequired\":false}],\"Headers\":[],\"LLMAskPreview\":[],\"Response\":[{\"ParamID\":\"e3c2e75f-1068-14f6-7f30-b608548706ff\",\"ParamName\":\"test\",\"ParamType\":\"string\",\"ParamTitle\":\"test\",\"JSONPath\":\"test\"}],\"DoubleCheck\":false}},{\"NodeID\":\"b75c58db-815b-fd53-6e22-ca704c7ed15b\",\"NodeType\":\"ANSWER\",\"NodeName\":\"结束回复1\",\"NodeUI\":{\"X\":\"70\",\"Y\":\"380\"},\"Branches\":[],\"AnswerNodeData\":{\"InputAnswerData\":{\"Preview\":\"指标:<span data-w-e-type=\\\"slot\\\" data-w-e-is-void data-w-e-is-inline data-value=\\\"a96b042b-021b-4934-b3db-bd1a5b7fe8f2\\\" data-type=\\\"SLOT\\\" data-extra=\\\"%7B%7D\\\" data-info=\\\"%7B%22name%22%3A%22%E6%8C%87%E6%A0%87%22%2C%22type%22%3A%22SLOT%22%2C%22text%22%3A%22%40%E6%8C%87%E6%A0%87%22%7D\\\">@指标</span>\\n\\n检查:123\\n\\n类别:\\n\\n药品:<span data-w-e-type=\\\"slot\\\" data-w-e-is-void data-w-e-is-inline data-value=\\\"0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf\\\" data-type=\\\"SLOT\\\" data-extra=\\\"%7B%7D\\\" data-info=\\\"%7B%22name%22%3A%22%E8%8D%AF%E5%93%81%E5%90%8D%E7%A7%B0%22%2C%22type%22%3A%22SLOT%22%2C%22text%22%3A%22%40%E8%8D%AF%E5%93%81%E5%90%8D%E7%A7%B0%22%7D\\\">@药品名称</span>\"},\"AnswerType\":\"INPUT\",\"LLMAnswerData\":{\"EnableCustomPromptWord\":false,\"Prompt\":\"\",\"Preview\":[]},\"DocAnswerData\":{\"Preview\":\"\",\"RefInfo\":[]}}}],\"Edges\":[{\"EdgeID\":\"edge-0.070570893216546081719386410706\",\"Source\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\",\"Target\":\"b75c58db-815b-fd53-6e22-ca704c7ed15b\",\"SourceAnchor\":1,\"TargetAnchor\":0},{\"EdgeID\":\"edge-0.79810973496941861719457836074\",\"Source\":\"start\",\"Target\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\",\"SourceAnchor\":0,\"TargetAnchor\":0}],\"Snapshot\":{\"SlotMap\":{\"f4949e3d-fad3-4338-b5dd-5a70e89670ec\":\"医学检查\",\"a96b042b-021b-4934-b3db-bd1a5b7fe8f2\":\"指标\",\"96d1efb5-ea47-4e69-b86f-3facd11406c3\":\"药品类别或治疗方式\",\"0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf\":\"药品名称\"},\"CustomVarMap\":{}},\"ProtoVersion\":\"V2_4\"}",
				},
			},
			want1:   false,
			wantErr: false,
		},
	}
	ctx := context.Background()
	body, err := os.ReadFile("../../testdata/workflow.zip")
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		mapZipFile[fileName] = r
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				MaxRow:           200,
				WorkflowFileName: "工作流程.xlsx",
				WorkflowHead: []string{
					"工作流ID",
					"工作流名称",
					"工作流描述",
					"引用工作流ID列表",
					"画布结构"},
			},
			VerifyWorkflow: config.VerifyWorkflow{
				WorkflowLimit:   200,
				WorkflowNameLen: 20,
				IntentDescLen:   500,
			},
		}
	})

	defer mockConfig.Reset()
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		robotId := "1831580433754619904"
		t.Run(tt.name, func(t *testing.T) {
			got, ok, err := parseWorkflow(ctx, mapZipFile, tt.args.mapExistName, zipWriter, robotId)
			if !tt.wantErr && err != nil {
				t.Errorf("parseWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "parseWorkflow() got = %v, want %v", got, tt.want)
			assert.Equalf(t, tt.want1, ok, "parseWorkflow() ok = %v, want %v", ok, tt.want1)
		})
	}
}

func Test_parseExample(t *testing.T) {
	type args struct {
		mapExistName map[string]struct{}
	}
	tests := []struct {
		name    string
		args    args
		want    []*entity.ExportWorkflowExample
		want1   bool
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				mapExistName: nil,
			},
			want: []*entity.ExportWorkflowExample{
				{
					FlowID:    "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					ExampleID: "1a66edb4-e577-4da0-80d7-be76f9da52dc",
					Example:   "动脉氧分压多少？",
				},
				{
					FlowID:    "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					ExampleID: "33ea5673-2f9e-44a9-9f3f-f28c6e75d0be",
					Example:   "病人的动脉氧分压",
				},
				{
					FlowID:    "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					ExampleID: "513ea6ae-6b00-4925-a53b-a685a55f69ff",
					Example:   "病人是否使用肾du药物",
				},
			},
			want1:   true,
			wantErr: false,
		},
	}
	ctx := context.Background()
	body, err := os.ReadFile("../../testdata/workflow.zip")
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		mapZipFile[fileName] = r
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				MaxRow:          200,
				ExampleFileName: "示例问法.xlsx",
				ExampleHead: []string{
					"工作流ID",
					"示例问法ID",
					"示例问法内容",
				},
			},
			VerifyWorkflow: config.VerifyWorkflow{
				WorkflowLimit:   200,
				WorkflowNameLen: 20,
				IntentDescLen:   500,
			},
		}
	})
	defer mockConfig.Reset()
	flowIdMap := make(map[string]struct{})
	flowIdMap["3512dd22-550f-42a3-bfc8-7fe72ef24287"] = struct{}{}
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			got, ok, err := parseExample(ctx, mapZipFile, flowIdMap, nil, zipWriter)
			if !tt.wantErr && err != nil {
				t.Errorf("parseWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "parseWorkflow() got = %v, want %v", got, tt.want)
			assert.Equalf(t, tt.want1, ok, "parseWorkflow() ok = %v, want %v", ok, tt.want1)
		})
	}
}

func Test_parseParam(t *testing.T) {
	type args struct {
		mapExistName map[string]struct{}
	}
	tests := []struct {
		name    string
		args    args
		want    []*entity.ParamMigrationInfo
		want1   bool
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				mapExistName: nil,
			},
			want: []*entity.ParamMigrationInfo{
				{
					FlowID:           "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					NodeID:           "node1",
					NodeName:         "node1",
					ParamID:          "0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf",
					ParamName:        "药品名称",
					ParamDesc:        "描述: 药品名称\n示例: 丙氨酰谷氨酰胺注射液，复方氨基酸注射液，氯化钠注射液，注射用醋酸卡泊芬净,去甲肾上腺素",
					ParamType:        "string",
					CorrectExample:   `["A,"B"]`,
					IncorrectExample: `["C","D"]`,
				},
				{
					FlowID:           "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					NodeID:           "node2",
					NodeName:         "node2",
					ParamID:          "96d1efb5-ea47-4e69-b86f-3facd11406c3",
					ParamName:        "药品类别或治疗方式",
					ParamDesc:        "描述: 药品类别或治疗方式\n示例: 抗心律失常药,利尿药,抗真菌药,麻醉药,降血糖药,肾脏系统,血管扩张药,血管活性药,呼吸兴奋剂,泌尿系统,激素类,免疫抑制剂,选择性钙通道阻滞剂,肾上腺素能,多巴胺能,肌肉松弛药,解毒药,抗肾上腺素能,抗感染药,抗癫痫药,麻醉剂,抗痛风药,抗贫血药,尿频尿失禁药,泻药,毛细血管稳定剂,抗精神病药,抗病毒药,抗血栓形成药,青霉素,抗生素,循环支持,机械通气",
					ParamType:        "string",
					CorrectExample:   `["A,"B"]`,
					IncorrectExample: `["C","D"]`,
				},
				{
					FlowID:         "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					NodeID:         "node3",
					NodeName:       "node3",
					ParamID:        "a96b042b-021b-4934-b3db-bd1a5b7fe8f2",
					ParamName:      "指标",
					ParamDesc:      "描述: 患者的生理指标\n示例: 血压,体温,心率,肝功能,血脂8项,钙测定,钠测定,血浆乳酸测定,氧合指数,心肌酶5项,肌酸激酶,肌酸激酶同工酶MB,乳酸脱氢酶,ECMO,呼吸率,血氧饱和度，细菌涂片,循环系统,感染,高铁血红蛋白,吸入氧浓度,心律失常,室性心动,动脉氧分压,心律,血肌酐",
					ParamType:      "string",
					CorrectExample: `["A,"B"]`,
				},
				{
					FlowID:    "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					NodeID:    "node4",
					NodeName:  "node4",
					ParamID:   "f4949e3d-fad3-4338-b5dd-5a70e89670ec",
					ParamName: "医学检查",
					ParamDesc: "描述: 患者的检查项目的名称, 但不包括药物或治疗方式\n示例: CT,心电图,X光,脑波检查、各种神经功能检查、肌电图、心电图、听力检查,细菌涂片,动脉采血",
					ParamType: "string",
				},
			},
			want1:   true,
			wantErr: false,
		},
	}
	ctx := context.Background()
	body, err := os.ReadFile("../../testdata/workflow.zip")
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		mapZipFile[fileName] = r
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				MaxRow:        200,
				ParamFileName: "参数.xlsx",
				ParamHead: []string{
					"工作流ID",
					"工作流节点ID",
					"工作流节点名称",
					"参数ID",
					"参数名称",
					"参数描述",
					"参数类型",
					"参数正确示例",
					"参数错误示例",
				},
			},
			VerifyWorkflow: config.VerifyWorkflow{
				WorkflowLimit:   200,
				WorkflowNameLen: 20,
				IntentDescLen:   500,
			},
			VerifyParameter: config.VerifyParameter{
				ParamTypeSli:               []string{"string"},
				ParamNameMaxLen:            100,
				ParamDescMaxLen:            1000,
				ParamMaxNum:                20,
				CorrectExampleNameMaxLen:   100,
				CorrectExampleMaxNum:       100,
				IncorrectExampleNameMaxLen: 100,
				IncorrectExampleMaxNum:     100,
			},
		}
	})
	defer mockConfig.Reset()
	flowIdMap := make(map[string]struct{})
	flowIdMap["3512dd22-550f-42a3-bfc8-7fe72ef24287"] = struct{}{}
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			got, ok, err := parseParam(ctx, mapZipFile, flowIdMap, zipWriter)
			if !tt.wantErr && err != nil {
				t.Errorf("parseWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, len(got), len(tt.want))
			assert.Equalf(t, tt.want1, ok, "parseWorkflow() ok = %v, want %v", ok, tt.want1)
			for i := range got {
				assert.Equalf(t, tt.want[i], got[i], "parseWorkflow() got = %v, want %v", got[i], tt.want[i])
			}
		})
	}
}

func Test_parseVar(t *testing.T) {
	type args struct {
		mapExistName map[string]struct{}
	}
	tests := []struct {
		name    string
		args    args
		want    []*entity.ExportWorkflowVar
		want1   bool
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				mapExistName: nil,
			},
			want: []*entity.ExportWorkflowVar{
				{
					FlowID:  "3512dd22-550f-42a3-bfc8-7fe72ef24287",
					VarID:   "11794ec1-f44a-4d83-93c6-28464db79266",
					VarName: "变量1",
				},
			},
			want1:   true,
			wantErr: false,
		},
	}
	ctx := context.Background()
	body, err := os.ReadFile("../../testdata/workflow.zip")
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	zipFiles, err := zip.NewReader(bytes.NewReader(body), int64(len(body)))
	if err != nil {
		t.Error("read testdata failed:", err.Error())
		return
	}
	mapZipFile := make(map[string]*zip.File)
	for _, r := range zipFiles.File {
		fileName := filepath.Base(r.Name)
		// macos 的压缩包会附带._XXX 的文件，需要过滤
		if strings.HasPrefix(fileName, "._") {
			continue
		}
		mapZipFile[fileName] = r
	}
	mockConfig := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
		return config.MainConfig{
			Workflow: config.Workflow{
				MaxRow:      200,
				VarFileName: "变量.xlsx",
				VarHead: []string{
					"工作流ID",
					"变量ID",
					"变量名称",
				},
			},
			VerifyWorkflow: config.VerifyWorkflow{
				WorkflowLimit:   200,
				WorkflowNameLen: 20,
				IntentDescLen:   500,
			},
		}
	})
	defer mockConfig.Reset()
	flowIdMap := make(map[string]struct{})
	flowIdMap["3512dd22-550f-42a3-bfc8-7fe72ef24287"] = struct{}{}
	for _, tt := range tests {
		zipBuffer := new(bytes.Buffer)
		zipWriter := zip.NewWriter(zipBuffer)
		t.Run(tt.name, func(t *testing.T) {
			got, ok, err := parseVar(ctx, mapZipFile, flowIdMap, nil, zipWriter)
			if !tt.wantErr && err != nil {
				t.Errorf("parseWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "parseWorkflow() got = %v, want %v", got, tt.want)
			assert.Equalf(t, tt.want1, ok, "parseWorkflow() ok = %v, want %v", ok, tt.want1)
		})
	}
}
