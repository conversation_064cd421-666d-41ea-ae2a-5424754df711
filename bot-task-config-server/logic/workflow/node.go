// bot-task-config-server
//
// @(#)node.go  星期四, 二月 27, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package workflow

import (
	"context"
	"strings"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// ListWorkflowNodeModel 获取节点模型列表
func ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq) (*KEP_WF.ListWorkflowNodeModelRsp, error) {
	rsp := new(KEP_WF.ListWorkflowNodeModelRsp)

	listJson, err := rpc.GetModelList(ctx, util.CorpID(ctx))
	if err != nil {
		return rsp, err
	}

	// 获取配置
	wfNodeModelSupport := config.GetMainConfig().WorkflowNodeLlmSupport
	//参数提取节点
	//KEP_WF.NodeType_PARAMETER_EXTRACTOR
	nodeType := strings.ToLower(KEP_WF.NodeType_PARAMETER_EXTRACTOR.String())
	rsp.ParameterExtractorModelList = getNodeModelByNodeType(ctx, nodeType, wfNodeModelSupport, listJson)
	//大模型节点
	nodeType = strings.ToLower(KEP_WF.NodeType_LLM.String())
	rsp.LlmModelList = getNodeModelByNodeType(ctx, nodeType, wfNodeModelSupport, listJson)
	//大模型知识问答节点
	nodeType = strings.ToLower(KEP_WF.NodeType_LLM_KNOWLEDGE_QA.String())
	rsp.LlmKnowledgeQaModelList = getNodeModelByNodeType(ctx, nodeType, wfNodeModelSupport, listJson)
	//标签提取节点
	nodeType = strings.ToLower(KEP_WF.NodeType_TAG_EXTRACTOR.String())
	rsp.TagExtractorModelList = getNodeModelByNodeType(ctx, nodeType, wfNodeModelSupport, listJson)
	//意图识别节点
	nodeType = strings.ToLower(KEP_WF.NodeType_INTENT_RECOGNITION.String())
	rsp.IntentRecognitionModelList = getNodeModelByNodeType(ctx, nodeType, wfNodeModelSupport, listJson)
	return rsp, nil
}

func getNodeModelByNodeType(ctx context.Context, nodeType string, wfNodeModelSupport map[string]config.NodeConfig,
	listJson []*pb.ModelInfo) []*KEP_WF.WorkflowNodeModelInfo {
	list := make([]*KEP_WF.WorkflowNodeModelInfo, 0, len(listJson))
	nodeTypeSupportModel, ok := wfNodeModelSupport[strings.ToLower(nodeType)]
	thoughtList := make([]*KEP_WF.WorkflowNodeModelInfo, 0)
	otherList := make([]*KEP_WF.WorkflowNodeModelInfo, 0)
	for _, v := range listJson {
		if v == nil {
			continue
		}
		tags := make([]string, 0)
		// 获取对应节点的配置
		mn := v.GetModelName()
		appendFlag := true
		if ok {
			if util.StrInArray(mn, nodeTypeSupportModel.HighPerformancePrice) {
				tags = append(tags, entity.HighPerformancePrice)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.Recommend) {
				tags = append(tags, entity.Recommend)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.DepthThink) {
				tags = append(tags, entity.DepthThink)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.MoreBalanced) {
				tags = append(tags, entity.MoreBalanced)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.DefaultSelected) {
				tags = append(tags, entity.DefaultSelected)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.LimitTimeFree) {
				tags = append(tags, entity.LimitTimeFree)
			}
			if util.StrInArray(mn, nodeTypeSupportModel.NotShow) {
				appendFlag = false
			}
		}
		if appendFlag {
			item := &KEP_WF.WorkflowNodeModelInfo{
				ModelName:        mn,
				ModelDesc:        v.GetModelDesc(),
				AliasName:        v.GetAliasName(),
				ResourceStatus:   v.GetResourceStatus(),
				PromptWordsLimit: v.GetPromptWordsLimit(),
				TopP: &KEP_WF.WorkflowNodeModelInfo_ParameterRule{
					Default: v.GetTopP().GetDefault(),
					Min:     v.GetTopP().GetMin(),
					Max:     v.GetTopP().GetMax(),
				},
				Temperature: &KEP_WF.WorkflowNodeModelInfo_ParameterRule{
					Default: v.GetTemperature().GetDefault(),
					Min:     v.GetTemperature().GetMin(),
					Max:     v.GetTemperature().GetMax(),
				},
				MaxTokens: &KEP_WF.WorkflowNodeModelInfo_ParameterRule{
					Default: v.GetMaxTokens().GetDefault(),
					Min:     v.GetMaxTokens().GetMin(),
					Max:     v.GetMaxTokens().GetMax(),
				},
				Source:        v.GetSource(),
				Icon:          v.GetIcon(),
				IsFree:        v.GetIsFree(),
				Tags:          tags,
				InputLenLimit: v.InputLenLimit,
				ModelCategory: v.ModelCategory,
			}
			if v.ModelCategory == entity.ThoughtModel {
				thoughtList = append(thoughtList, item)
			} else {
				otherList = append(otherList, item)
			}
		}
	}
	if len(thoughtList) > 0 {
		list = append(list, thoughtList...)
	}
	//把思考模型放在混元前面
	list = append(list, otherList...)
	return list
}

// ListWorkflowInfoByModelName 获取模型被哪些节点使用
func ListWorkflowInfoByModelName(ctx context.Context,
	req *KEP_WF.ListWorkflowInfoByModelNameReq) (*KEP_WF.ListWorkflowInfoByModelNameRsp, error) {
	rsp := new(KEP_WF.ListWorkflowInfoByModelNameRsp)
	cropId := req.GetCorpId()
	mn := req.GetModelName()
	if cropId == 0 || len(mn) == 0 {
		return rsp, errors.BadRequestError("参数错误")
	}
	// 查询模型引用画布节点
	// sandbox
	sandboxWorkflowsRef, err := db.GetWorkflowByModelName(ctx, cropId, mn, entity.SandboxEnv)
	if err != nil {
		return rsp, err
	}
	rsp.SandBoxList = sandboxWorkflowsRef
	// sandbox
	prodWorkflowsRef, err := db.GetWorkflowByModelName(ctx, cropId, mn, entity.ProductEnv)
	if err != nil {
		return rsp, err
	}
	rsp.ProdList = prodWorkflowsRef
	return rsp, nil
}
