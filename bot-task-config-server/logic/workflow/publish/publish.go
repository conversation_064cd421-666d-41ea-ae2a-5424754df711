package publish

import (
	"context"

	workflowPublishDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/workflow/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
)

// Publish 发布相关接口
type Publish interface {
	// WorkflowPublish 工作流发布
	WorkflowPublish(ctx context.Context, robotID string, taskID uint64, workflowIDs []string) error

	// VarParamsPublish API参数发布
	VarParamsPublish(ctx context.Context, robotID string, taskID uint64, varIDs []string) error

	// IsWorkflowPublished 判断机器人是否有工作流发布
	IsWorkflowPublished(ctx context.Context, robotID string, envType uint32) (bool, error)

	// IsWorkflowPublishedQueryById 判断应用下工作流是否发布
	IsWorkflowPublishedQueryById(ctx context.Context, robotID, workflowId string,
		envType uint32) (bool, error)

	// GetPublishedWorkflow 获取发布后的工作流
	GetPublishedWorkflow(ctx context.Context, robotID string, workflowIDs []string) (
		map[string]*entity.Workflow, error)
}

// publish ...
type publish struct {
	workflowDbDao workflowPublishDao.Dao
}

// NewPublish new publish logic
func NewPublish() Publish {
	return &publish{
		workflowDbDao: workflowPublishDao.NewDao(),
	}
}
