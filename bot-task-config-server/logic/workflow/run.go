package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"gorm.io/gorm"
)

// CreateWorkflowRun 创建工作流运行实例
func CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq) (*KEP_WF.CreateWorkflowRunRsp, error) {
	dmRunID := generateWorkflowRunID()
	workflowRun, startWorkflowRunReq, err := generateWorkflowRun(ctx, req, dmRunID)
	if err != nil {
		return nil, err
	}
	// 获取用户任务锁，阻塞，直至获取到锁
	lockKey := entity.GetWorkflowRunLockKey(workflowRun.UIN)
	locker := lock.NewDefaultLocker(lockKey, workflowRun.UIN, database.GetRedis())
	_, err = locker.Lock(ctx, true)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppID:%s|WorkflowRunId:%s|Lock|err:%v",
			workflowRun.AppID, workflowRun.WorkflowRunID, err)
		return nil, errors.ErrSystem
	}
	defer func() {
		if err := locker.UnLock(ctx); err != nil {
			log.WarnContextf(ctx, "CreateWorkflowRun|AppID:%s|WorkflowRunId:%s|UnLock|err:%v",
				workflowRun.AppID, workflowRun.WorkflowRunID, err)
		}
	}()
	err = db.SaveWorkflowRun(ctx, workflowRun)
	// WorkflowRunID为唯一键，重复需要重新构造ID重试
	retryTimes := 5
	for err != nil && errors.Is(err, gorm.ErrDuplicatedKey) {
		if retryTimes <= 0 {
			break
		}
		retryTimes -= 1
		dmRunID = generateWorkflowRunID()
		workflowRun.WorkflowRunID = dmRunID
		startWorkflowRunReq.WorkflowRunID = dmRunID
		err = db.SaveWorkflowRun(ctx, workflowRun)
	}
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|SaveWorkflowRun|AppID:%s|WorkflowRunId:%s|err:%v",
			workflowRun.AppID, workflowRun.WorkflowRunID, err)
		return nil, err
	}
	rsp := &KEP_WF.CreateWorkflowRunRsp{
		AppBizId:        workflowRun.AppID,
		WorkflowRunId:   workflowRun.WorkflowRunID,
		CreateTime:      uint64(time.Now().UnixMilli()),
		RunEnv:          req.GetRunEnv(),
		Query:           req.GetQuery(),
		CustomVariables: req.GetCustomVariables(),
	}
	// 异步处理逻辑
	go AsyncStartWorkflowRun(trpc.CloneContext(ctx), *workflowRun, startWorkflowRunReq, "CreateWorkflowRun")
	return rsp, nil
}

func generateWorkflowRunID() string {
	// 采用雪花算法生成唯一ID（加上固定前缀“wfr-”共16个字符）
	return fmt.Sprintf("wfr-%s", idgenerator.NewStringID())
}

func generateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq, dmRunID string) (*entity.WorkflowRun,
	*KEP_WF_DM.StartWorkflowRunRequest, error) {
	uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)
	appBizID := req.GetAppBizId()
	appBizIDInt, err := strconv.ParseUint(appBizID, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun appBizId ParseUint err:%v", err)
		return nil, nil, errors.ErrRobotNotFound
	}
	adminEnv := uint32(1)
	dmRunEnv := KEP_WF_DM.RunEnvType_SANDBOX
	runEnv := entity.RunEnvTest
	if req.GetRunEnv() == KEP_WF.EnvType_PROD {
		adminEnv = uint32(2)
		dmRunEnv = KEP_WF_DM.RunEnvType_PRODUCT
		runEnv = entity.RunEnvProduct
	}
	customVariables := make(map[string]string, len(req.GetCustomVariables()))
	for _, v := range req.GetCustomVariables() {
		customVariables[v.GetName()] = v.GetValue()
	}
	appInfo, err := rpc.GetAppInfo(ctx, adminEnv, appBizIDInt)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|GetAppInfo|AppBizId:%s|err:%+v", appBizID, err)
		return nil, nil, errors.ErrRobotNotFound
	}
	if appInfo.GetAppType() != entity.AppTypeKnowledgeQA {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|AppType:%s", appBizID, appInfo.GetAppType())
		return nil, nil, errors.ErrAsyncWorkflowNotEnabledInApp
	}
	if appInfo.GetKnowledgeQa() == nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|err:appInfo.KnowledgeQa is nil", appBizID)
		return nil, nil, errors.ErrAsyncWorkflowNotEnabledInApp
	}
	// 校验是否为单工作流模式
	if appInfo.GetKnowledgeQa().GetPattern() != entity.AppPatternSingleWorkflow {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|err:appInfo.KnowledgeQa.Pattern is not single workflow", appBizID)
		return nil, nil, errors.ErrAsyncWorkflowNotEnabledInApp
	}
	// 校验是否开启了异步任务
	if !appInfo.GetKnowledgeQa().GetAsyncWorkflow() {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|err:async task not enable", appBizID)
		return nil, nil, errors.ErrAsyncWorkflowNotEnabledInApp
	}
	dmRunWorkflowID := appInfo.GetKnowledgeQa().GetWorkflowId()
	workflow, err := db.GetWorkflowDetail(ctx, dmRunWorkflowID, appBizID)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|GetWorkflowDetail|WorkflowID:%s|err:%+v", dmRunWorkflowID, err)
		return nil, nil, errors.ErrWorkflowNotFound
	}
	dmRunWorkflowJSON := workflow.DialogJsonDraft
	if dmRunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
		dmRunWorkflowJSON = workflow.DialogJsonEnable
	} else {
		// 草稿态需要判断工作流画布状态，只有已发布或调试成功的才能够被允许发起异步任务
		if workflow.WorkflowState != entity.FlowStateEnable && workflow.WorkflowState != entity.FlowStatePublishChange {
			log.WarnContextf(ctx, "CreateWorkflowRun|GetWorkflowDetail|WorkflowID:%s|err:workflow state is not enable", dmRunWorkflowID)
			return nil, nil, errors.ErrAsyncWorkflowDialogJsonChanged
		}
	}
	// 检查工作流是否满足异步任务条件
	can, err := canAsync(ctx, appBizID, dmRunWorkflowID)
	if err != nil {
		return nil, nil, err
	}
	if !can {
		log.WarnContextf(ctx, "CreateWorkflowRunAppBizId:%s|err:not able to create workflow run", appBizID)
		return nil, nil, errors.ErrAsyncWorkflowInvalid
	}
	// 检查工作流使用的模型资源是否耗尽
	err = checkModelResource(ctx, appBizID, appInfo.GetCorpId(), dmRunWorkflowJSON)
	if err != nil {
		return nil, nil, err
	}
	dmRunMainModel := entity.GetAppModelName(appInfo)
	// 构造WorkflowRun
	workflowRun := &entity.WorkflowRun{
		WorkflowRunID:   dmRunID,
		UIN:             uin,
		SubUIN:          subAccountUin,
		AppID:           appBizID,
		Name:            workflow.WorkflowName,
		WorkflowID:      dmRunWorkflowID,
		WorkflowJSON:    dmRunWorkflowJSON,
		ProtoVersion:    uint8(workflow.ProtoVersion),
		RunEnv:          runEnv,
		Query:           req.GetQuery(),
		MainModelName:   dmRunMainModel,
		CustomVariables: utils.ToJsonString(req.GetCustomVariables()),
		State:           entity.WorkflowRunStatePending,
	}
	// 构造异步工作流启动请求， IsDebug 字段暂不启用
	startWorkflowRunReq := &KEP_WF_DM.StartWorkflowRunRequest{
		Uin:             uin,
		WorkflowRunID:   workflowRun.WorkflowRunID,
		RunEnv:          dmRunEnv,
		WorkflowID:      workflowRun.WorkflowID,
		AppID:           workflowRun.AppID,
		Query:           req.GetQuery(),
		MainModelName:   workflowRun.MainModelName,
		CustomVariables: customVariables,
	}
	return workflowRun, startWorkflowRunReq, nil
}

func checkModelResource(ctx context.Context, appBizID string, corpId uint64, workflowJson string) error {
	models, err := rpc.GetModelList(ctx, corpId)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|CorpId:%d|GetModelList|err:%+v",
			appBizID, corpId, err)
		return errors.ErrRobotNotFound
	}
	modelResourceStatus := make(map[string]bool)
	for _, model := range models {
		resourceStatus := model.GetResourceStatus()
		if resourceStatus == 2 {
			modelResourceStatus[model.GetModelName()] = false
		} else {
			modelResourceStatus[model.GetModelName()] = true
		}
	}
	wf, err := protoutil.JsonToWorkflowForPreCheck(workflowJson)
	if wf == nil || err != nil {
		log.WarnContextf(ctx, "CreateWorkflowRun|AppBizId:%s|JsonToWorkflowForPreCheck|wf:%+v|err:%+v",
			appBizID, wf, err)
		return errors.ErrWorkflowNotFound
	}
	for _, node := range wf.GetNodes() {
		modelName := ""
		switch node.GetNodeType() {
		case KEP_WF.NodeType_LLM:
			modelName = node.GetLLMNodeData().GetModelName()
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
			modelName = node.GetLLMKnowledgeQANodeData().GetModelName()
		case KEP_WF.NodeType_INTENT_RECOGNITION:
			modelName = node.GetIntentRecognitionNodeData().GetModelName()
		case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
			// 参数提取节点不允许出现在开启异步调用的工作流里，跳过检查
		case KEP_WF.NodeType_TAG_EXTRACTOR:
			modelName = node.GetTagExtractorNodeData().GetModelName()
		}
		if modelName != "" {
			if !modelResourceStatus[modelName] {
				return errors.ErrAsyncWorkflowTokenNotEnough
			}
		}
	}
	return nil
}

func AsyncStartWorkflowRun(ctx context.Context, workflowRun entity.WorkflowRun,
	startWorkflowRunReq *KEP_WF_DM.StartWorkflowRunRequest, logPrefix string) {
	// 获取用户任务锁，阻塞，直至获取到锁
	lockKey := entity.GetWorkflowRunLockKey(workflowRun.UIN)
	locker := lock.NewDefaultLocker(lockKey, workflowRun.UIN, database.GetRedis())
	_, err := locker.Lock(ctx, true)
	if err != nil {
		log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|Lock|err:%v",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, err)
		return
	}
	defer func() {
		if err := locker.UnLock(ctx); err != nil {
			log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|UnLock|err:%v",
				logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, err)
		}
	}()
	// 重试兜底
	fallback := func(ctx context.Context, uin string, workflowRunID string) {
		fallbackInterval := time.Duration(config.GetMainConfig().AsyncWorkflowConfig.RetryTimerInterval) * time.Second
		if err := db.SaveWorkflowRunRetryUIN(ctx, uin, time.Now().Add(fallbackInterval)); err != nil {
			log.WarnContextf(ctx, "%s|SaveWorkflowRunRetryUIN|UIN:%s|WorkflowRunID:%s|err:%+v",
				logPrefix, uin, workflowRunID, err)
			return
		}
		log.InfoContextf(ctx, "%s|fallback|UIN:%s|workflowRunId:%s", logPrefix, uin, workflowRunID)
	}
	// 获取总体并发，最好由DM提供接口，避免频繁读DB扫表
	overallCount, err := db.GetWorkflowRunCounts(ctx)
	if err != nil {
		log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|GetWorkflowRunCounts|err:%v",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, err)
		fallback(ctx, workflowRun.UIN, workflowRun.WorkflowRunID)
		return
	}
	// 当前总体并发超限，直接返回，由定时任务拉起
	if overallCount >= int64(config.GetMainConfig().AsyncWorkflowConfig.WorkflowRunOverallLimit) {
		log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|GetWorkflowRunCounts|count:%d",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, overallCount)
		fallback(ctx, workflowRun.UIN, workflowRun.WorkflowRunID)
		return
	}
	// 获取主账号ID对应并发，最好由DM提供接口，避免频繁读DB扫表
	count, err := db.GetWorkflowRunCountByUIN(ctx, workflowRun.UIN)
	if err != nil {
		log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|GetWorkflowRunCountByUIN|err:%v",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, err)
		fallback(ctx, workflowRun.UIN, workflowRun.WorkflowRunID)
		return
	}
	// 当前主账号ID对应并发超限，直接返回，由定时任务拉起
	if count >= int64(config.GetMainConfig().AsyncWorkflowConfig.WorkflowRunLimit) {
		log.WarnContextf(ctx, "%s|AppID:%s|WorkflowRunId:%s|GetWorkflowRunCountByUIN|count:%d",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, count)
		fallback(ctx, workflowRun.UIN, workflowRun.WorkflowRunID)
		return
	}
	// 调用DM
	startWorkflowRunRsp, err := rpc.StartWorkflowRun(ctx, startWorkflowRunReq)
	if err != nil {
		log.WarnContextf(ctx, "%s|invoke.StartWorkflowRun|AppID:%s|WorkflowRunId:%s|req:%+v|err:%v",
			logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, startWorkflowRunReq, err)
		fallback(ctx, workflowRun.UIN, workflowRun.WorkflowRunID)
		return
	}
	log.InfoContextf(ctx, "%s|invoke.StartWorkflowRun|AppID:%s|WorkflowRunId:%s|IsAlreadyRunning:%v",
		logPrefix, workflowRun.AppID, workflowRun.WorkflowRunID, startWorkflowRunRsp.GetIsAlreadyRunning())
}

// CanCreateWorkflowRun 判断是否能创建工作流运行实例
func CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq) (*KEP_WF.CanCreateWorkflowRunRsp, error) {
	if len(req.GetWorkflowId()) == 0 {
		log.WarnContextf(ctx, "CanCreateWorkflowRun workflowId is empty")
		return nil, errors.BadRequestError("工作流ID不能为空")
	}
	appBizID := req.GetAppBizId()
	_, err := strconv.ParseUint(appBizID, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "CanCreateWorkflowRun appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}

	var can bool
	if can, err = canAsync(ctx, appBizID, req.GetWorkflowId()); err != nil {
		return nil, err
	}

	return &KEP_WF.CanCreateWorkflowRunRsp{Result: can}, nil
}

// 异步调用的开启条件是：不包含参数提取节点、选项卡节点和回复节点的单工作流应用。如果引用了子工作流，子工作流中也不能包含上述节点。
func canAsync(ctx context.Context, appBizID string, workflowIDs ...string) (bool, error) {
	workflows, err := db.GetWorkflowsByFlowIds(ctx, workflowIDs, appBizID)
	if err != nil {
		log.WarnContextf(ctx, "canAsync|GetWorkflowsByFlowIds|err:%+v", err)
		return false, errors.ErrWorkflowNotFound
	}
	var refWorkflowIDs []string
	for _, workflow := range workflows {
		if workflow == nil || workflow.DialogJsonEnable == "" {
			log.WarnContextf(ctx, "canAsync|workflow is empty|%+v", workflow)
			return false, errors.ErrWorkflowNotFound
		}
		wf, err := protoutil.JsonToWorkflowForPreCheck(workflow.DialogJsonEnable)
		if wf == nil || err != nil {
			log.WarnContextf(ctx, "canAsync|JsonToWorkflowForPreCheck|wf:%+v|err:%+v", wf, err)
			return false, errors.ErrWorkflowNotFound
		}
		for _, node := range wf.GetNodes() {
			switch node.GetNodeType() {
			case KEP_WF.NodeType_PARAMETER_EXTRACTOR, KEP_WF.NodeType_OPTION_CARD, KEP_WF.NodeType_ANSWER:
				return false, nil
			case KEP_WF.NodeType_BATCH:
				refWorkflowIDs = append(refWorkflowIDs, node.GetBatchNodeData().GetWorkflowID())
			case KEP_WF.NodeType_ITERATION:
				refWorkflowIDs = append(refWorkflowIDs, node.GetIterationNodeData().GetWorkflowID())
			case KEP_WF.NodeType_WORKFLOW_REF:
				refWorkflowIDs = append(refWorkflowIDs, node.GetWorkflowRefNodeData().GetWorkflowID())
			}
		}
	}
	if len(refWorkflowIDs) == 0 {
		return true, nil
	}

	return canAsync(ctx, appBizID, refWorkflowIDs...)
}

// ListWorkflowRuns 查询工作流运行实例列表
func ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq) (*KEP_WF.ListWorkflowRunsRsp, error) {
	appBizID := req.GetAppBizId()
	_, err := strconv.ParseUint(appBizID, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "ListWorkflowRuns appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	pageSize := uint32(15)
	pageNum := uint32(1)
	if req.GetPageSize() > 0 {
		pageSize = req.GetPageSize()
	}
	if req.GetPage() > 0 {
		pageNum = req.GetPage()
	}
	workflowRuns, total, err := db.GetWorkflowRunsByAppIDAndEnvWithPagination(ctx, req.GetAppBizId(),
		req.GetRunEnv().String(), pageNum, pageSize)
	if err != nil {
		log.WarnContextf(ctx, "ListWorkflowRuns|GetWorkflowRunsByEnvWithPagination|err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "ListWorkflowRuns|GetWorkflowRunsByEnvWithPagination|total:%d", total)

	runs := make([]*KEP_WF.WorkflowRunBase, 0, len(workflowRuns))
	for _, run := range workflowRuns {
		runBase := &KEP_WF.WorkflowRunBase{
			RunEnv:        req.GetRunEnv(),
			AppBizId:      run.AppID,
			WorkflowRunId: run.WorkflowRunID,
			WorkflowId:    run.WorkflowID,
			Name:          run.Name,
			State:         run.GetWorkflowRunState(),
			FailMessage:   run.FailMessage,
			TotalTokens:   run.TotalToken,
		}
		if run.StartTime != nil {
			runBase.StartTime = uint64(run.StartTime.UnixMilli())
		}
		if run.EndTime != nil {
			runBase.EndTime = uint64(run.EndTime.UnixMilli())
		}
		if run.CreateTime != nil {
			runBase.CreateTime = uint64(run.CreateTime.UnixMilli())
		}
		runs = append(runs, runBase)
	}

	return &KEP_WF.ListWorkflowRunsRsp{
		Total: uint32(total),
		//PageNumber:   req.GetPage(),
		WorkflowRuns: runs,
	}, nil
}

// DescribeWorkflowRun 查询工作流运行实例详情
func DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq) (*KEP_WF.DescribeWorkflowRunRsp, error) {

	workflowRun, nodeRuns, err := db.GetWorkflowRunAndNodesByID(ctx, req.GetWorkflowRunId())
	if err != nil {
		log.WarnContextf(ctx, "DescribeWorkflowRun|GetWorkflowRunAndNodesByID|err:%+v", err)
		return nil, err
	}

	if workflowRun == nil {
		log.WarnContextf(ctx, "DescribeWorkflowRun|workflowRun is nil")
		return nil, errors.ErrWorkflowRunNotFound
	}

	nrs := make([]*KEP_WF.NodeRunBase, 0, len(nodeRuns))
	for _, nodeRun := range nodeRuns {
		totalTokens, err := nodeRun.GetTotalTokens()
		if err != nil {
			log.WarnContextf(ctx, "DescribeWorkflowRun|GetNodeRunTotalTokens|NodeRunId:%s|StatisticInfos:%s|err:%v",
				nodeRun.NodeID, nodeRun.StatisticInfos, err)
			return nil, err
		}
		nr := &KEP_WF.NodeRunBase{
			NodeRunId:        nodeRun.NodeRunID,
			NodeId:           nodeRun.NodeID,
			WorkflowRunId:    nodeRun.WorkflowRunID,
			NodeName:         nodeRun.NodeName,
			NodeType:         nodeRun.GetNodeType(),
			State:            nodeRun.GetNodeRunState(),
			FailMessage:      nodeRun.FailMessage,
			CostMilliseconds: nodeRun.CostMilliseconds,
			TotalTokens:      totalTokens,
		}
		nrs = append(nrs, nr)
	}

	customVariables, err := workflowRun.GetCustomVariables()
	if err != nil {
		log.WarnContextf(ctx, "DescribeWorkflowRun|GetCustomVariables|err:%+v", err)
		return nil, err
	}

	wr := &KEP_WF.WorkflowRunDetail{
		RunEnv:          workflowRun.GetRunEnv(),
		AppBizId:        workflowRun.AppID,
		WorkflowRunId:   workflowRun.WorkflowRunID,
		WorkflowId:      workflowRun.WorkflowID,
		Name:            workflowRun.Name,
		State:           workflowRun.GetWorkflowRunState(),
		FailMessage:     workflowRun.FailMessage,
		TotalTokens:     workflowRun.TotalToken,
		DialogJson:      workflowRun.WorkflowJSON,
		Query:           workflowRun.Query,
		MainModelName:   workflowRun.MainModelName,
		CustomVariables: customVariables,
	}
	if workflowRun.StartTime != nil {
		wr.StartTime = uint64(workflowRun.StartTime.UnixMilli())
	}
	if workflowRun.EndTime != nil {
		wr.EndTime = uint64(workflowRun.EndTime.UnixMilli())
	}
	if workflowRun.CreateTime != nil {
		wr.CreateTime = uint64(workflowRun.CreateTime.UnixMilli())
	}

	return &KEP_WF.DescribeWorkflowRunRsp{
		WorkflowRun: wr,
		NodeRuns:    nrs,
	}, nil
}

// DescribeNodeRun 查看工作流节点运行详情
func DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq) (*KEP_WF.DescribeNodeRunRsp, error) {
	if len(req.GetNodeRunId()) == 0 {
		log.WarnContextf(ctx, "DescribeNodeRun NodeRunId is empty")
		return nil, errors.BadRequestError("节点运行实例ID不能为空")
	}
	nodeRun, err := db.GetNodeRunByID(ctx, req.GetNodeRunId())
	if err != nil {
		log.WarnContextf(ctx, "DescribeNodeRun|GetNodeRunByID|NodeRunId:%s|err:%v", req.GetNodeRunId(), err)
		return nil, err
	}
	if nodeRun == nil {
		log.WarnContextf(ctx, "DescribeNodeRun|NodeRunId:%s|nodeRun is nil", req.GetNodeRunId())
		return nil, errors.ErrNodeRunNotFound
	}
	statisticInfos, err := nodeRun.GetStatisticInfos()
	if err != nil {
		log.WarnContextf(ctx, "DescribeNodeRun|GetNodeRunByID|NodeRunId:%s|StatisticInfos:%s|err:%v",
			req.GetNodeRunId(), nodeRun.StatisticInfos, err)
		return nil, err
	}

	log.DebugContextf(ctx, "DescribeNodeRun|GetNodeRunByID|NodeRunId:%s|StatisticInfos:%s",
		req.GetNodeRunId(), nodeRun.StatisticInfos)

	var totalTokens uint32 // kinvo: db里没有统计token数量,需要从 StatisticInfos 取合
	for _, info := range statisticInfos {
		totalTokens += info.GetTotalTokens()
	}
	nr := &KEP_WF.NodeRunDetail{
		NodeRunId:        nodeRun.NodeRunID,
		NodeId:           nodeRun.NodeID,
		WorkflowRunId:    nodeRun.WorkflowRunID,
		NodeName:         nodeRun.NodeName,
		NodeType:         nodeRun.GetNodeType(),
		State:            nodeRun.GetNodeRunState(),
		FailMessage:      nodeRun.FailMessage,
		CostMilliseconds: nodeRun.CostMilliseconds,
		TotalTokens:      totalTokens,
		Input:            nodeRun.Input,
		Output:           nodeRun.Output,
		TaskOutput:       nodeRun.TaskOutput,
		StatisticInfos:   statisticInfos,
	}
	if nodeRun.StartTime != nil {
		nr.StartTime = uint64(nodeRun.StartTime.UnixMilli())
	}
	if nodeRun.EndTime != nil {
		nr.EndTime = uint64(nodeRun.EndTime.UnixMilli())
	}
	return &KEP_WF.DescribeNodeRunRsp{
		NodeRun: nr,
	}, nil
}

// StopWorkflowRun 停止工作流运行实例
func StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq) (*KEP_WF.StopWorkflowRunRsp, error) {
	_, err := rpc.StopWorkflowRun(ctx, &KEP_WF_DM.StopWorkflowRunRequest{WorkflowRunID: req.GetWorkflowRunId()})
	if err != nil {
		log.WarnContextf(ctx, "StopWorkflowRun|invoke.StopWorkflowRun|WorkflowRunId:%s|err:%v", req.GetWorkflowRunId(), err)
		return nil, err
	}
	log.InfoContextf(ctx, "StopWorkflowRun|invoke.StopWorkflowRun|WorkflowRunId:%s", req.GetWorkflowRunId())
	return &KEP_WF.StopWorkflowRunRsp{}, nil
}

// SaveAppDebugMode 保存应用的调试配置的调试模式
func SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq) (
	*KEP_WF.SaveAppDebugModeRsp, error) {
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugMode appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(req.GetAppBizId())))
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}

	// 验证前端的枚举值是否有效
	if req.GetDebugMode() < KEP_WF.DebugModeType(0) || req.GetDebugMode() > KEP_WF.DebugModeType(1) {
		log.WarnContextf(ctx, "SaveAppDebugMode debugMode:%s, err:%v", req.GetDebugMode(), err)
		return nil, errors.BadRequestError("调试模式类型不对")
	}
	err = db.SaveAppDebugMode(ctx, req.GetAppBizId(), req.GetDebugMode().String())
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugMode db.SaveAppDebugConfig err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "SaveAppDebugMode rsp ok")
	return &KEP_WF.SaveAppDebugModeRsp{}, nil
}

// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
func SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq) (
	*KEP_WF.SaveAppDebugCustomVariablesRsp, error) {
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugCustomVariables appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(req.GetAppBizId())))
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}

	//// API参数转json
	//if len(req.GetCustomVariables()) == 0 {
	//	log.WarnContextf(ctx, "SaveAppDebugConfig customVariables:%s, err:%v", req.GetDebugMode(), err)
	//	return nil, errors.BadRequestError("调试模式类型不对")
	//}
	// 将结构体切片转换为 JSON 字符串
	customJson, err := json.Marshal(req.GetCustomVariables())
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugCustomVariables customVariables to json err:%+v", err)
		return nil, err
	}
	err = db.SaveAppDebugCustomVariables(ctx, req.GetAppBizId(), string(customJson))
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugCustomVariables db.SaveAppDebugConfig err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "SaveAppDebugCustomVariables rsp ok")
	return &KEP_WF.SaveAppDebugCustomVariablesRsp{}, nil
}

// DescribeAppDebugConfig 查看应用的调试配置
func DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq) (
	*KEP_WF.DescribeAppDebugConfigRsp, error) {
	rsp := &KEP_WF.DescribeAppDebugConfigRsp{}
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "DescribeAppDebugConfig appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(req.GetAppBizId())))
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	workflowCustomConfig, err := db.DescribeAppDebugConfig(ctx, req.GetAppBizId())
	if err != nil {
		log.WarnContextf(ctx, "DescribeAppDebugConfig db.DescribeAppDebugConfig err:%+v", err)
		return rsp, err
	}
	customVariables := make([]*KEP_WF.CustomVariableConfig, 0)
	if workflowCustomConfig != nil {

		// 将json转换为结构体切片
		err = json.Unmarshal([]byte(workflowCustomConfig.CustomVariables), &customVariables)
		if err != nil {
			return nil, fmt.Errorf("DescribeAppDebugConfig customVariables json unmarshal error: %w", err)
		}
		rsp.DebugMode = KEP_WF.DebugModeType(KEP_WF.DebugModeType_value[workflowCustomConfig.DebugMode])
	}
	rsp.CustomVariables = customVariables
	log.InfoContextf(ctx, "DescribeAppDebugConfig rsp ok")
	return rsp, nil
}
