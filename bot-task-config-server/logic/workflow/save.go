/*
 * 2024-10-8
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package workflow

import (
	"context"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	workflowPublishDao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/workflow/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/check"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gorm.io/gorm"
)

// SaveWorkflow 编辑更新Workflow
func SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq) (*KEP_WF.SaveWorkflowRsp, error) {
	var err error
	var workflow *entity.Workflow
	appBizID := req.GetAppBizId()
	workflowName := strings.TrimSpace(req.GetName())
	workflowJson := strings.TrimSpace(req.GetDialogJson())
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	desc := strings.TrimSpace(req.GetDesc())
	saveType := req.GetSaveType()
	// 尝试将字符串转换为 uint64
	_, err = strconv.ParseUint(appBizID, 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|CheckRobot:%s|%+v", err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 检查工作流是否存在
	if workflow, err = db.GetWorkflowDetail(ctx, workflowID, appBizID); err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.ErrWorkflowNotFound
		}
		log.ErrorContextf(ctx, "SaveWorkflow|GetWorkflowDetail err:%+v", err)
		return nil, err
	}
	if workflow == nil || len(workflow.WorkflowID) == 0 {
		return nil, errors.ErrWorkflowNotFound
	}
	if req.GetWorkflowVersion() != workflow.Version {
		log.InfoContextf(ctx, "SaveWorkflow|Req.version:%d|db.version:%d",
			req.GetWorkflowVersion(), workflow.Version)
		return nil, errors.ErrWorkflowWrongVersion
	}

	// 工作流名称校验
	if err = checkWorkflowName(ctx, workflowID, appBizID, workflowName); err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|checkWorkflowName err:%s", err.Error())
		return nil, err
	}

	// 意图描述
	if utf8.RuneCountInString(desc) > config.GetMainConfig().VerifyWorkflow.IntentDescLen {
		log.WarnContextf(ctx, "SaveWorkflow|err:%s|%s", desc, errors.ErrIntentNameExceed)
		return nil, errors.ErrIntentNameExceed
	}

	log.InfoContextf(ctx, "SaveWorkflow|saveType:%d", saveType)
	if saveType == uint32(KEP_WF.SaveWorkflowReq_ENABLE) {
		_, err := protoutil.JsonToWorkflow(workflowJson) // 强制校验只是用于告警和log
		if err != nil {
			log.ErrorContextf(ctx, "SaveWorkflow|JsonToWorkflow|workflowJson:%s|err:%+v", workflowJson, err)
			// 这里没有return，只是用于打印日志
		}
	}
	// 保存草稿的时候有些枚举值可以填空；比如逻辑判断节点中的 不选"等于"等操作符，所以不能用 JsonToWorkflow
	tree, err := protoutil.JsonToWorkflowForPreCheck(workflowJson)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|JsonToWorkflowForPreCheck|saveType:%d|workflowJson:%s|err:%+v",
			saveType, workflowJson, err)
		return nil, errs.New(500301, fmt.Sprintf("工作流画布JSON参数错误 %s", err))
	}
	if tree == nil {
		log.ErrorContextf(ctx, "SaveWorkflow|JsonToWorkflowForPreCheck|workflowJson is empty")
		return nil, errors.ErrWorkflowUIJsonParams
	}
	tree = decodeBase64Code(tree)
	tree.ProtoVersion = KEP_WF.WorkflowProtoVersion_V2_6
	workflowJson, err = protoutil.WorkflowToJson(tree)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|WorkflowToJson|workflowJson:%s|err:%+v",
			workflowJson, err)
		return nil, errors.ErrWorkflowUIJsonParams
	}
	// v2.8.5 画布中节点里的密钥需要加密存储
	workflowJson, err = protoutil.EncryptWorkflowJson(workflowJson)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|EncryptWorkflowJson|workflowJson:%s|err:%+v",
			workflowJson, err)
		return nil, errors.ErrWorkflowUIJsonParams
	}

	if err := checkWorkflowBasic(ctx, tree, workflowID, workflowName); err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|checkWorkflowBasic|err:%+v", err)
		return nil, errors.BadWorkflowReqError(err.Error())
	}
	var safeUrls []string
	if appInfo != nil && appInfo.KnowledgeQa != nil &&
		appInfo.KnowledgeQa.Workflow != nil && len(appInfo.KnowledgeQa.Workflow.SafeWhiteUrls) > 0 {
		safeUrls = appInfo.KnowledgeQa.Workflow.SafeWhiteUrls
	}
	wf := check.New(ctx, appBizID, tree, saveType, safeUrls)
	if err := wf.Parse(); err != nil { // 致命错误，不能保存草稿
		log.ErrorContextf(ctx, "SaveWorkflow|Parse|err:%+v", err)
		return nil, errors.BadWorkflowReqError(err.Error())
	}

	if saveType == uint32(KEP_WF.SaveWorkflowReq_ENABLE) && len(wf.GetNodeErrors()) > 0 {
		resp := &KEP_WF.SaveWorkflowRsp{WorkflowId: workflowID, WorkflowVersion: req.GetWorkflowVersion()}
		resp.NodeErrors = make([]*KEP_WF.SaveWorkflowRsp_NodeError, 0, len(wf.GetNodeErrors()))
		for _, nodeError := range wf.GetNodeErrors() {
			resp.NodeErrors = append(resp.NodeErrors, &KEP_WF.SaveWorkflowRsp_NodeError{
				NodeId: nodeError.NodeID, ErrMsg: nodeError.ErrMsg,
			})
		}
		return resp, nil
	}

	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	staffID := util.StaffID(ctx)
	modifyWorkflowParams := entity.ModifyWorkflowParams{
		WorkflowID:      workflowID,
		WorkflowName:    workflowName,
		WorkflowDesc:    desc,
		Version:         req.GetWorkflowVersion(),
		DialogJsonDraft: workflowJson,
		Uin:             uin,
		SubUin:          subUin,
		StaffID:         staffID,
		UpdateTime:      time.Now(),
		AppBizID:        appBizID,
		IsDebug:         saveType,
		RefWorkflows:    wf.GetRefWorkflows(),
		CustomVarIDs:    wf.GetCustomVarIDs(),
		KnowledgeRef:    wf.GetKnowledgeRef(),
		Parameters:      wf.GetParameters(),
		RefPlugins:      wf.GetRefPlugins(),
		CustomModels:    wf.GetWorkCustomModel(),
	}

	if saveType == uint32(KEP_WF.SaveWorkflowReq_ENABLE) {
		modifyWorkflowParams.HasUnPublishData = getWorkflowHasUnPublishData(ctx, appBizID, []string{workflowID})
	}
	newWorkflow, err := db.SaveWorkflow(ctx, modifyWorkflowParams)
	if err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|db.SaveWorkflow|err:%+v", err)
		return nil, errors.OpDataFromDBError(err.Error())
	}

	return &KEP_WF.SaveWorkflowRsp{
		WorkflowId:      workflowID,
		WorkflowVersion: newWorkflow.Version,
	}, nil
}

// checkWorkflowBasic 校验画布JSON
func checkWorkflowBasic(ctx context.Context, tree *KEP_WF.Workflow, workflowID, workflowName string) error {
	if workflowID != tree.GetWorkflowID() {
		log.ErrorContextf(ctx, "checkWorkflowBasic|workflowID:%s|json-workflowID:%s",
			workflowID, tree.GetWorkflowID())
		return fmt.Errorf("画布workflowID(%s)与接口传参的workflowID(%s)不一致", tree.GetWorkflowID(), workflowID)
	}

	if strings.TrimSpace(workflowName) != strings.TrimSpace(tree.GetWorkflowName()) {
		log.ErrorContextf(ctx, "checkWorkflowBasic|workflowName:%s|json-workflowName:%s",
			workflowName, tree.GetWorkflowName())
		return fmt.Errorf("工作流名称修改需同步修改画布JSON中的工作流名称")
	}

	_, duplicateIDs := getNodeNamesAndDuplicateIDs(tree)
	log.InfoContextf(ctx, "checkWorkflowBasic|getNodeNamesAndDuplicateIDs:%v",
		json0.Marshal2StringNoErr(duplicateIDs))
	if len(duplicateIDs) > 0 {
		log.ErrorContextf(ctx, "checkWorkflowBasic|duplicateIDs：%v", json0.Marshal2StringNoErr(duplicateIDs))
		var builder strings.Builder
		first := true
		for key := range duplicateIDs {
			if !first {
				builder.WriteString(",")
			}
			first = false
			builder.WriteString(key)
		}
		return fmt.Errorf("画布内节点名称有重复, %s", builder.String())
		//errMsg := fmt.Sprintf("画布内节点名称有重复 %s", builder.String())
		//return errs.New(errors.ErrWorkflowDuplicateNodeName, errMsg)
	}

	return nil
}

// getNodeNamesAndDuplicateIDs 获取所有节点的名称及可能重复节点的名称及ID, 1
func getNodeNamesAndDuplicateIDs(t *KEP_WF.Workflow) (map[string]string, map[string][]string) {
	nodeNames := make(map[string]string)      //所有节点的名称
	duplicateIDs := make(map[string][]string) // 重复节点名称的id集合

	for _, node := range t.GetNodes() {
		nodeName := node.GetNodeName()
		nodeID := node.GetNodeID()

		if seenNodeID, ok := nodeNames[nodeName]; ok {
			// 检查是否有重复节点名称
			if _, ok := duplicateIDs[nodeName]; !ok {
				duplicateIDs[nodeName] = []string{seenNodeID}
			}
			// 有重复的节点名称，它将这些重复的节点 ID 存储在一个 map 中
			duplicateIDs[nodeName] = append(duplicateIDs[nodeName], nodeID)
		} else {
			nodeNames[nodeName] = nodeID
		}
	}

	return nodeNames, duplicateIDs
}

// 前端要绕过WAF门神扫描，把代码base64后传入
func decodeBase64Code(tree *KEP_WF.Workflow) *KEP_WF.Workflow {
	for i, n := range tree.GetNodes() {
		if n.GetNodeType() == KEP_WF.NodeType_CODE_EXECUTOR {
			nodeData := n.GetCodeExecutorNodeData()
			code, err := base64.StdEncoding.DecodeString(nodeData.GetCode())
			if err != nil { // 兼容不是通过base64上传的旧版本
				continue
			}
			nodeData.Code = string(code)
			n.NodeData = &KEP_WF.WorkflowNode_CodeExecutorNodeData{CodeExecutorNodeData: nodeData}
			tree.Nodes[i] = n
		}
	}
	return tree
}

// getWorkflowHasUnPublishData 获取工作流关联未发布的数据
// 返回值:
//   - true: 存在未发布数据或发生错误
//   - false: 不存在未发布数据
func getWorkflowHasUnPublishData(ctx context.Context, robotID string, workflowIDs []string) bool {
	pDao := workflowPublishDao.NewDao()

	// 统一的错误处理函数
	handleError := func(operation string, err error) bool {
		log.ErrorContextf(ctx, "getWorkflowHasUnPublishData|%s|err:%+v", operation, err)
		return true
	}

	// 1. 检查工作流本身是否有未发布数据
	workflows, err := pDao.GetUnPublishWorkflow(ctx, robotID, workflowIDs)
	if err != nil {
		return handleError("GetUnPublishWorkflow", err)
	}
	if len(workflows) > 0 {
		return true
	}

	// 2. 检查机器人工作流关联
	robotWorkflows, err := pDao.GetUnPublishRobotWorkflow(ctx, workflowIDs)
	if err != nil {
		return handleError("GetUnPublishRobotWorkflow", err)
	}
	if len(robotWorkflows) > 0 {
		return true
	}

	// 3. 检查示例问法/特殊问法
	workflowExamples, err := db.GetUnPublishWorkflowExample(ctx, workflowIDs, robotID)
	if err != nil {
		return handleError("GetUnPublishWorkflowExample", err)
	}
	if len(workflowExamples) > 0 {
		return true
	}

	// 4. 检查工作流和API参数引用关系
	workflowVars, err := pDao.GetUnPublishWorkflowVar(ctx, workflowIDs)
	if err != nil {
		return handleError("GetUnPublishWorkflowVar", err)
	}
	if len(workflowVars) > 0 {
		return true
	}

	// 提取变量ID
	varParamIds := make([]string, 0, len(workflowVars))
	for _, workflowVar := range workflowVars {
		varParamIds = append(varParamIds, workflowVar.VarID)
	}

	// 5. 检查变量表
	vars, err := pDao.GetUnPublishVarParams(ctx, varParamIds)
	if err != nil {
		return handleError("GetUnPublishVarParams", err)
	}
	if len(vars) > 0 {
		return true
	}

	// 6. 检查工作流和参数引用关系
	workflowParameters, err := pDao.GetUnPublishWorkflowParameter(ctx, workflowIDs)
	if err != nil {
		return handleError("GetUnPublishWorkflowParameter", err)
	}
	if len(workflowParameters) > 0 {
		return true
	}

	// 提取参数ID
	parameterIDs := make([]string, 0, len(workflowParameters))
	for _, param := range workflowParameters {
		parameterIDs = append(parameterIDs, param.ParameterID)
	}

	// 7. 检查参数表
	parameters, err := pDao.GetUnPublishParameter(ctx, parameterIDs)
	if err != nil {
		return handleError("GetUnPublishParameter", err)
	}
	if len(parameters) > 0 {
		return true
	}

	// 记录调试信息
	log.DebugContextf(ctx, "getWorkflowHasUnPublishData: workflows=%d, robotWorkflows=%d, "+
		"workflowExamples=%d, workflowVars=%d, vars=%d, workflowParameters=%d, parameters=%d",
		len(workflows), len(robotWorkflows), len(workflowExamples), len(workflowVars),
		len(vars), len(workflowParameters), len(parameters))

	return false
}
