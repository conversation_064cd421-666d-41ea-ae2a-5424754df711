package synctask

import (
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/admin"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/workflow/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/trpc0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"strings"
	"time"
)

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
func SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (
	*KEP.SendDataSyncTaskEventRsp, error) {
	sid := util.RequestID(ctx)
	if len(codec.Message(ctx).ServerMetaData()["request_id"]) == 0 {
		util.WithRequestID(ctx, sid)
	}
	robotID := toRobotID(req.GetBotBizId())
	RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeEvent, req.GetEvent(), nil)
	log.InfoContextf(ctx, "SendDataSyncTaskEvent|%s|%s|%s|%v", req.GetEvent(), sid, robotID, time.Now())

	rsp := &KEP.SendDataSyncTaskEventRsp{TaskID: req.TaskID}
	switch req.GetEvent() {
	case entity.TaskEventCollect:
		if err := eventCollect(ctx, sid, robotID, req); err != nil {
			return rsp, err
		}
		return rsp, nil
	case entity.TaskEventRelease:
		if err := eventRelease(ctx, sid, robotID, req); err != nil {
			return rsp, err
		}
		return rsp, nil
	case entity.TaskEventRetry:
		if err := eventRetry(ctx, sid, robotID, req); err != nil {
			return rsp, err
		}
		return rsp, nil
	case entity.TaskEventPause:
		// 暂停不用处理
		return rsp, nil
	default:
		return nil, errors.BadRequestError("任务事件类型不存在")
	}
}

// eventCollect 采集事件
func eventCollect(ctx context.Context, sid string, robotID string, req *KEP.SendDataSyncTaskEventReq) error {
	// 检查任务是否存在, 存在则返回错误
	st0, err := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
	if err != nil {
		log.ErrorContextf(ctx, "eventCollect|GetDataSyncTask|%s|%v", sid, err)
		return errors.ErrSyncTaskLoadFailed
	}
	// 查到任务
	if st0.TaskID == req.GetTaskID() {
		return errors.BadRequestError("发布任务已经存在, 请勿重新创建")
	}

	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	st := entity.SyncTask{
		UserID:    subUin,
		UserName:  uin,
		Scene:     req.GetBusinessName(),
		RobotID:   robotID,
		TaskID:    req.GetTaskID(),
		Status:    int(entity.TaskStatusCollect),
		SessionID: sid,
		Server:    trpc0.AppServer(),
		WorkIP:    trpc0.LocalIP(),
		Note:      fmt.Sprintf(`{"corp-id":"%d","staff-id":"%d"}`, req.GetCorpID(), req.GetStaffID()),
	}
	err = synctask.CreateSyncTask(ctx, st)
	content, errM := json.Marshal(st)
	if errM != nil {
		content = []byte(errM.Error())
		log.WarnContextf(ctx, "eventCollect|json.Marshal errM:%+v", errM)
	}
	RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask, string(content), err)
	log.InfoContextf(ctx, "eventCollect|st:%+v|err:%+v", st, err)
	if err != nil {
		return err
	}

	// 将本次要发布的任务, 更新为发布中状态
	err = updateNeedPublishStatus(ctx, req.GetEvent(), robotID, req.GetTaskID(),
		[]string{entity.ReleaseStatusUnPublished})
	if err != nil {
		return err
	}
	return nil
}

// eventRelease 发布事件
func eventRelease(ctx context.Context, sid string, robotID string, req *KEP.SendDataSyncTaskEventReq) error {
	// 检查任务是否存在, 不存在则返回错误
	st, err := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
	if err != nil {
		log.ErrorContextf(ctx, "eventRelease|GetDataSyncTask|%s|%v", sid, err)
		return errors.ErrSyncTaskLoadFailed
	}
	// 没有查到任务或任务状态不是待发布
	if st.TaskID == 0 || st.Status != int(entity.TaskStatusCollect) {
		return errors.BadRequestError("发布任务不存在或不能发布")
	}
	// 检查任务是否可以马上执行
	ctx2 := clues.NewTrackContext(trpc.CloneContext(ctx))
	go saveTaskForSync(ctx2, req)
	return nil
}

// eventRetry 重试事件
func eventRetry(ctx context.Context, sid string, robotID string, req *KEP.SendDataSyncTaskEventReq) error {
	// 检查任务是否存在, 不存在则返回错误
	st, err := synctask.GetSyncTask(ctx, robotID, req.GetTaskID())
	if err != nil {
		log.ErrorContextf(ctx, "eventRetry|GetDataSyncTask|%s|%v", sid, err)
		return errors.ErrSyncTaskLoadFailed
	}
	// 没有查到任务
	if st.TaskID == 0 {
		return errors.BadRequestError("发布任务不存在")
	}
	if st.Status != int(entity.TaskStatusSyncFailed) {
		err = fmt.Errorf("task not allow retry")
		log.ErrorContextf(ctx, "eventRetry|st:%+v|err:%+v", st, err)
		return nil // TODO(halelv): 目前admin服务处理不了这里的错误，先返回nil，通过错误日志告警，后续优化
	}
	// 将本次要发布的任务, 更新为发布中状态
	err = updateNeedPublishStatus(ctx, req.GetEvent(), robotID, req.GetTaskID(),
		[]string{entity.ReleaseStatusUnPublished, entity.ReleaseStatusFail})
	if err != nil {
		return err
	}
	// 检查任务是否可以马上执行
	ctx2 := clues.NewTrackContext(trpc.CloneContext(ctx))
	go saveTaskForSync(ctx2, req)
	return nil
}

// updateNeedPublishStatus 更新需要发布的数据发布状态 需要指定event该event对应的可发布的releaseStatus列表
func updateNeedPublishStatus(ctx context.Context, event string, robotID string, taskID uint64,
	releaseStatus []string) error {
	// 将本次要发布的流程, 更新为发布中状态
	items, err := synctask.ListUnpublishedTaskFlow(ctx, robotID, releaseStatus)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask,
		fmt.Sprintf("%s|ListUnpublishedTaskFlow|len:%d", event, len(items)), err)
	if err != nil {
		return err
	}

	if len(items) == 0 {
		return nil
	}

	// 更新任务流状态为发布中
	flowIDs := make([]string, len(items))
	for i := 0; i < len(items); i++ {
		flowIDs[i] = items[i].WorkflowID
	}
	err = synctask.UpdateTaskFlowReleaseStatus(ctx, robotID, flowIDs, entity.ReleaseStatusPublishing)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask,
		fmt.Sprintf(`%s|UpdateTaskFlowReleaseStatus|len:%d|["%s"]`,
			event, len(items), strings.Join(flowIDs, `","`)), err)
	if err != nil {
		return err
	}

	// 记录本次要发布的任务流程
	recordSyncItems(ctx, robotID, taskID, items, event)
	return nil
}

// recordSyncItems 发步结果记录
func recordSyncItems(ctx context.Context, robotID string, taskID uint64, items0 []entity.Workflow, message string) {
	// 发步结果记录
	releaseItems := make([]KEP.SyncItem, len(items0))
	for j := 0; j < len(items0); j++ {
		releaseItems[j] = KEP.SyncItem{
			ID:         items0[j].WorkflowID,
			Title:      items0[j].WorkflowName,
			Type:       "WORKFLOW",
			Action:     items0[j].Action,
			UpdateTime: uint64(time.Now().UnixMilli()),
			Message:    message,
		}
	}
	rj, erj := json.Marshal(releaseItems)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeResult, string(rj), erj)
}

// saveTaskForSync 同步发布任务
func saveTaskForSync(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) {
	// 系统可以同时运行的任务数, 如果超出, 则需要等待
	c := synctask.CountSyncTaskByStatus(ctx, entity.TaskStatusProcessing)
	if c < config.GetMainConfig().DataSyncTask.ReleaseQueueLength {
		releaseTasks(ctx, req.GetBotBizId(), req.GetTaskID(), req.GetEvent())
		return
	}
	queueTasks(ctx, req)
}

// releaseTasks 执行发布任务
func releaseTasks(ctx context.Context, botBizID, taskID uint64, event string) {
	sid := util.RequestID(ctx)
	robotID := toRobotID(botBizID)
	err := synctask.UpdateSyncTaskStatus(ctx, robotID, taskID, entity.TaskStatusProcessing)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, `{"status":2,"desc":"ing"}`, err)
	// 执行发布任务
	items, err1 := listCollectedTaskFlow(ctx, robotID, event)
	if err1 != nil {
		RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, "查询任务流失败", err1)
		// 回调错误通知
		e0 := releaseNotify(ctx, botBizID, taskID, false, err1.Error())
		log.ErrorContextf(ctx, "ReleaseNotify|%s|%v", sid, e0)

		// 失败记录
		e1 := synctask.UpdateSyncTaskStatusAndCount(ctx, robotID, taskID, entity.TaskStatusSyncFailed,
			0, len(items))
		log.InfoContextf(ctx, "UpdateSyncTaskStatusAndCount|%s|%v", sid, e1)
		return
	}

	// 发布记数
	var successCount int

	// 批次处理
	batch := len(items) / int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch)
	log.InfoContextf(ctx, "releaseTasks|%s|TOTAL:%d|BATCH:%d", sid, len(items), batch)
	clues.AddTrackData(ctx, "CONFIG:ReleaseItemsPerBatch", config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch)
	for i := 0; i <= batch; i++ {
		begin := int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch) * i
		end := int(config.GetMainConfig().DataSyncTask.ReleaseItemsPerBatch) * (i + 1)
		if end > len(items) {
			end = len(items)
		}
		clues.AddTrackData(ctx, "BATCH", map[string]any{"batch": batch, "i": i, "begin": begin, "end": end})
		t1 := time.Now()
		items0 := items[begin:end]
		itemIDs := make([]string, len(items0))
		for j := 0; j < len(items0); j++ {
			itemIDs[j] = items0[j].WorkflowID
		}

		var insertIDs, updateIDs, deleteIDs []string
		var insertItems, updateItems, deleteItems []entity.Workflow
		for _, item := range items0 {
			switch item.Action {
			case entity.ActionInsert:
				insertIDs = append(insertIDs, item.WorkflowID)
				insertItems = append(insertItems, item)
			case entity.ActionUpdate:
				updateIDs = append(updateIDs, item.WorkflowID)
				updateItems = append(updateItems, item)
			case entity.ActionDelete:
				deleteIDs = append(deleteIDs, item.WorkflowID)
				deleteItems = append(deleteItems, item)
			}
		}

		err2 := publish.NewPublish().WorkflowPublish(ctx, robotID, taskID, itemIDs)
		log.InfoContextf(ctx, "%s|%s|releaseTasks()|batch:%d/%d [%d,%d)|%s|%v",
			robotID, sid, i, batch, begin, end, time.Since(t1), "")
		if err2 != nil { // 任务流程发布错误
			publishMsg := fmt.Sprintf("INSERT:[%s],UPDATE:[%s],DELETE:[%s]",
				strings.Join(insertIDs, ","),
				strings.Join(updateIDs, ","),
				strings.Join(deleteIDs, ","),
			)
			RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, publishMsg, err2)
			log.ErrorContextf(ctx, "ICSTaskFlowPublish|%s|%v|%v|%v|%v",
				sid, robotID, batch, clues.GetTrackDataJSON(ctx), err2.Error())
			recordSyncItems(ctx, robotID, taskID, items0, errs.Msg(err2))
			continue
		}

		recordDiff(ctx, robotID, taskID, batch, insertIDs, updateIDs, deleteIDs, insertItems, updateItems)
		recordRelease(ctx, robotID, taskID, batch, insertItems, updateItems, deleteItems)

		// 记数统计
		successCount += len(items0)
		recordSyncItems(ctx, robotID, taskID, items0, "")
	}
	// 发布状态
	var syncStatus entity.TaskStatus
	failedCount := len(items) - successCount
	if failedCount == 0 {
		syncStatus = entity.TaskStatusSyncSuccess
	} else {
		syncStatus = entity.TaskStatusSyncFailed
	}
	e1 := synctask.UpdateSyncTaskStatusAndCount(ctx, robotID, taskID, syncStatus, successCount, failedCount)
	msg0 := fmt.Sprintf(`{"status";"success","count":%d,"failed":%d}`, successCount, failedCount)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeTask, msg0, e1)
	log.InfoContextf(ctx, "UpdateSyncTaskStatusAndCount|%s|%v", sid, e1)

	// 回调发布成功通知
	e0 := releaseNotify(ctx, botBizID, taskID, syncStatus == entity.TaskStatusSyncSuccess, msg0)
	log.InfoContextf(ctx, "ReleaseNotify|%s|%v", sid, e0)
}

// queueTasks 任务等待队列
func queueTasks(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) {
	sid := util.RequestID(ctx)
	robotID := toRobotID(req.GetBotBizId())
	err := synctask.UpdateSyncTaskStatus(ctx, robotID, req.GetTaskID(), entity.TaskStatusQueuing)
	log.InfoContextf(ctx, "ENTER-QUEUE|%s|%v|%v", sid, robotID, req.GetTaskID())
	RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeTask,
		`{"status":55,"desc":"queuing"}`, err)
}

// listCollectedTaskFlow 查询准备发布的任务
func listCollectedTaskFlow(ctx context.Context, robotID, event string) ([]entity.Workflow, error) {
	log.InfoContextf(ctx, "%s|listCollectedTaskFlow|robotID:%s", event, robotID)
	// 重试的任务 前置也需要将流程的状态置未发布中 这里查询发布中的任务
	return synctask.ListUnpublishedTaskFlow(ctx, robotID, []string{entity.ReleaseStatusPublishing})
}

// recordDiff 记录发布对比
func recordDiff(ctx context.Context, robotID string, taskID uint64, batch int,
	insertIDs, updateIDs, deleteIDs []string,
	insertItems, updateItems []entity.Workflow) {
	sid := util.RequestID(ctx)
	m0 := map[string]any{"batch": batch, "INSERT-IDs": insertIDs, "UPDATE-IDs": updateIDs, "DELETE-IDs": deleteIDs}
	clues.AddTrackData(ctx, "BATCH:DIFF", m0)

	updateProdItems, err1 := listProdTaskFlowByIDs(ctx, robotID, updateIDs)
	log.InfoContextf(ctx, "recordDiff|update|%s|%v|%v", sid, len(updateProdItems), err1)
	deleteProdItems, err2 := listProdTaskFlowByIDs(ctx, robotID, deleteIDs)
	log.InfoContextf(ctx, "recordDiff|delete|%s|%v|%v", sid, len(deleteProdItems), err2)

	m1 := map[string]any{
		"batch":      batch,
		"INSERT-IDs": insertIDs, "UPDATE-IDs": updateIDs, "DELETE-IDs": deleteIDs,
		"INSERT-ITEMs": insertItems, "UPDATE-ITEMs": updateItems,
		"UPDATE-PROD-ITEMs": updateProdItems, "DELETE-PROD-ITEMs": deleteProdItems,
	}
	if err1 != nil {
		m1["list-update-prod-items-error"] = err1.Error()
	}
	if err2 != nil {
		m1["list-delete-prod-items-error"] = err2.Error()
	}

	m1j, m1err := json.Marshal(m1)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeDiff, string(m1j), m1err)
}

// listProdTaskFlowByIDs 按 id 查对应的任务流程
func listProdTaskFlowByIDs(ctx context.Context, robotID string, ids []string) ([]entity.Workflow, error) {
	return synctask.ListTaskFlowByIDs(ctx, database.GetLLMRobotWorkflowGORM(), robotID, ids)
}

// recordRelease 记录发布详情
func recordRelease(ctx context.Context, robotID string, taskID uint64, batch int,
	insertItems, updateItems, deleteItems []entity.Workflow) {
	m1 := map[string]any{"batch": batch, "INSERT": insertItems, "UPDATE": updateItems, "DELETE": deleteItems}
	m1j, m1err := json.Marshal(m1)
	RecordSyncTaskLog(ctx, robotID, taskID, entity.LogTypeRelease, string(m1j), m1err)
}

// releaseNotify 通知发布状态
func releaseNotify(ctx context.Context, botBizID, taskID uint64, success bool, message string) error {
	sid := util.RequestID(ctx)
	err := admin.ReleaseNotify(ctx, botBizID, taskID, success, message)
	log.InfoContextf(ctx, "R|ReleaseNotify|%s|%v|%v|%v|%v|%v", sid, botBizID, taskID, success, message, err)
	var msg string
	if err == nil {
		msg = "OK"
	} else {
		msg = "FAILED"
	}
	msg0 := fmt.Sprintf(`{"success":%t,"message":"%s","invoke":"%s"}`, success, message, msg)
	RecordSyncTaskLog(ctx, toRobotID(botBizID), taskID, entity.LogTypeNotify, msg0, err)
	return err
}

// ReleaseAPIVarParams 通知API参数发布
func ReleaseAPIVarParams(ctx context.Context, req *KEP.ReleaseAPIVarParamsReq) (*KEP.ReleaseAPIVarParamsRsp, error) {
	log.InfoContextf(ctx, "ReleaseAPIVarParams req:%+v", req)
	robotID := toRobotID(req.GetBotBizId())
	RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeReleaseAPIVar,
		fmt.Sprintf("%+v", req), nil)

	err := publish.NewPublish().VarParamsPublish(ctx, robotID, req.GetTaskID(), req.GetAPIVarIDs())
	if err != nil {
		log.ErrorContextf(ctx, "ReleaseAPIVarParams VarParamsPublish failed, err:%+v", err)
		RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeReleaseAPIVar,
			"VarParamsPublish failed", err)
		return nil, err
	} else {
		log.InfoContextf(ctx, "ReleaseAPIVarParams VarParamsPublish success")
		RecordSyncTaskLog(ctx, robotID, req.GetTaskID(), entity.LogTypeReleaseAPIVar,
			"VarParamsPublish success", nil)
	}
	return &KEP.ReleaseAPIVarParamsRsp{TaskID: req.TaskID}, nil
}
