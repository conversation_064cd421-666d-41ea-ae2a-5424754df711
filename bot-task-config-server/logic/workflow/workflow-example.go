// bot-task-config-server
//
// @(#)workflow-example.go  星期三, 十月 09, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package workflow

import (
	"context"
	"fmt"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	utilsErrors "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/file"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

// ImportWorkflowExample 导入工作流示例问法
func ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq) (*KEP_WF.ImportWfExampleRsp, error) {
	rsp := new(KEP_WF.ImportWfExampleRsp)

	sid := util.RequestID(ctx)

	staffID := util.StaffID(ctx)
	wfID := req.GetWorkflowId()
	appBizID := req.GetAppBizId()
	fileName := req.GetFileName()

	botID, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botID)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowExample permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	corpID := appInfo.CorpId

	// 获取工作流下示例问法个数
	//maxExampleContent := config.GetMainConfig().ExampleCorpus.IntentExampleContentLen
	maxExampleCount := config.GetMainConfig().ExampleCorpus.IntentExampleMax

	// 获取该工作流下的示例用法总数
	count, err := db.GetFlowExampleForRobotIntentCount(ctx, appBizID, wfID)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|ImportWorkflowExample|robotID:%s|wfID:%s|err:%+v",
			sid, appBizID, wfID, err)
		rsp.ErrorMsg = err.Error()
		return rsp, err
	}
	if int(count) >= maxExampleCount {
		errMsg := errs.Newf(errors.ErrExampleCountTooLong, "该工作流下示例问法总数不能超过%d个", maxExampleCount)
		rsp.ErrorMsg = errMsg.Error()
		return rsp, nil
	}

	// 检查数据的有效性
	errRsp, examsInfo, err := parseWfExampleXlsx(ctx, req, fileName, corpID)
	if err != nil || errRsp != nil {
		return errRsp, err
	}

	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	importExamsParams := entity.WfExampleImportData{
		Examples:   examsInfo,
		WorkflowId: wfID,
		AppId:      appBizID,
		Uin:        uin,
		SubUin:     subUin,
		StaffId:    staffID,
	}

	paramStr, err := jsoniter.MarshalToString(importExamsParams)
	if err != nil {
		log.ErrorContextf(ctx, "ImportWfExams marshalExamsParams Failed! params:%+v,err:%v",
			importExamsParams, err)
		return nil, errors.BadWorkflowReqError(err.Error())
	}

	// 创建导入任务
	importID := fmt.Sprintf("%d", idgenerator.NewInt64ID())

	task := &entity.WorkflowImport{
		ImportID: importID,
		RobotID:  appBizID,
		Params:   paramStr,
		Uin:      uin,
		SubUin:   subUin,
	}

	err = db.CreateWfExampleImportTask(ctx, corpID, staffID, importID, appBizID, fileName, task)
	if err != nil {
		log.ErrorContextf(ctx, "CreateWfExampleImportTask Failed!｜robotID:%s|sid:%s,err:%v", appBizID, sid, err)
		return nil, err
	}
	return rsp, nil
}

func parseWfExampleXlsx(ctx context.Context, req *KEP_WF.ImportWfExampleReq, fileName string, corpID uint64) (
	*KEP_WF.ImportWfExampleRsp, []*entity.ExportWorkflowExample, error) {

	sid := util.RequestID(ctx)

	// 下载文件，校验文件格式为xlsx
	excelContent, err := cos.StorageCli.GetObject(ctx, req.GetCosUrl())
	if err != nil {
		log.ErrorContextf(ctx, "parseWfExampleXlsx downloadFile Failed! sid:%s,err:%v", sid, err)
		return nil, nil, errors.BadWorkflowReqError("解析excel文件错误")
	}

	minRow := 0
	maxRow := config.GetMainConfig().Workflow.ExampleMaxRow

	// 检查数据
	check := func(rowIndex int, row []string, uniqueMap map[string]int) string {
		examRow := getWfExampleInfoFromRow(row)
		errMessages := checkWfExamRow(examRow, rowIndex, uniqueMap)
		if len(errMessages) > 0 {
			return strings.Join(errMessages, "；")
		}
		return ""
	}

	rows, checkErrContent, err := file.CheckExampleXlsxContent(ctx, fileName, minRow, maxRow, []string{}, excelContent, check)
	if err != nil && !errors.Is(err, errors.ErrExcelContent) {
		log.WarnContextf(ctx, "parseWfExampleXlsx Failed! sid:%s,err:%+v", sid, err)
		return nil, nil, err
	}
	log.InfoContextf(ctx, "parseWfExampleXlsx excelContents,sid:%s,rows:%+v", sid, rows)

	// excel内容检测有问题
	if errors.Is(err, errors.ErrExcelContent) {
		checkFileName := fmt.Sprintf("错误_%s", req.GetFileName())
		cosPath := cos.GetCorpCOSFilePath(corpID, checkFileName)
		if err := cos.StorageCli.PutObject(ctx, checkErrContent, cosPath); err != nil {
			log.ErrorContextf(ctx, "putObject Failed! err:%+v", err)
			return nil, nil, err
		}
		url, err := cos.StorageCli.GetPreSignedURL(ctx, cosPath)
		if err != nil {
			return nil, nil, err
		}

		return &KEP_WF.ImportWfExampleRsp{ErrorMsg: "文件数据存在错误，请下载并查看错误标注文件", ErrorLink: url,
			ErrorLinkText: "下载"}, nil, nil
	}

	examRows := make([]*entity.ExportWorkflowExample, 0)
	for _, row := range rows {
		examRows = append(examRows, getWfExampleInfoFromRow(row))
	}
	log.InfoContextf(ctx, "parseWfExampleXlsx ,sid:%s,examRows:%+v", sid, examRows)

	return nil, examRows, nil

}

// / checkWfExamRow 导入示例问法校验
func checkWfExamRow(examRow *entity.ExportWorkflowExample, rowIndex int, uniqueName map[string]int) []string {
	errMessages := make([]string, 0)
	maxExampleContent := config.GetMainConfig().ExampleCorpus.IntentExampleContentLen
	examImportLimit := config.GetMainConfig().Workflow.ExampleMaxRow
	examCotentLen := utf8.RuneCountInString(strings.TrimSpace(examRow.Example))
	if examCotentLen == 0 {
		errMessages = append(errMessages, "示例问法为空")
	}
	if examCotentLen > maxExampleContent {
		errMessages = append(errMessages, fmt.Sprintf("%s: 示例问法内容超过最大长度%d", examRow.Example, maxExampleContent))
	}
	if rowIndex, ok := uniqueName[examRow.Example]; ok {
		errMessages = append(errMessages, fmt.Sprintf("%s: 存在重复", examRow.Example))
	} else {
		uniqueName[examRow.Example] = rowIndex
	}

	if rowIndex > examImportLimit {
		errMessages = append(errMessages, fmt.Sprintf("最多导入%d条", examImportLimit))
	}

	return errMessages
}

// getWfExampleInfoFromRow 获取导入示例问法的内容
func getWfExampleInfoFromRow(row []string) *entity.ExportWorkflowExample {
	examRow := &entity.ExportWorkflowExample{}
	for cellIndex, cell := range row {
		switch cellIndex {
		case entity.ExcelImportExample:
			examRow.Example = strings.TrimSpace(cell)
		}
	}
	return examRow
}

// CreateWorkflowExample 创建工作流示例问法
func CreateWorkflowExample(ctx context.Context,
	req *KEP_WF.CreateWorkflowExampleReq) (*KEP_WF.CreateWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	appBizID := req.GetAppBizId()
	workflowId := req.GetWorkflowId()
	example := strings.TrimSpace(req.GetExample())
	rsp := &KEP_WF.CreateWorkflowExampleRsp{}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	botID, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botID)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflowExample permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 判断工作流是否存在
	workflow, err := db.GetWorkflowDetail(ctx, workflowId, appBizID)
	if err != nil || workflow == nil {
		log.ErrorContextf(ctx, "CreateWorkflowExample|%s|%v", sid, err)
		return nil, errors.ErrWorkflowNotFound
	}

	// 判断示例问法的名称长度及是否在机器人唯独重复
	err = checkWorkflowExample(ctx, appBizID, workflowId, example)
	if err != nil {
		log.WarnContextf(ctx, "sid:%s|checkWorkflowExample|flowId:%s|%s|err:%+v",
			sid, workflowId, example, err)
		return nil, err
	}

	exampleId := idgenerator.NewUUID()
	key := "KEP_bot-task-config-server:CreateWorkflowExample-" + appBizID
	locker := lock.NewDefaultLocker(key, exampleId, database.GetRedis())
	// 加锁
	ok, lockErr := locker.Lock(ctx, true)

	if lockErr != nil {
		log.ErrorContextf(ctx, "CreateWorkflowExample locker.Lock Failed, err:%v", lockErr)
		return rsp, nil
	}
	if !ok {
		log.WarnContextf(ctx, "CreateWorkflowExample locker.Lock Failed, err:%v", lockErr)
		return rsp, nil
	}
	// 解锁
	defer func() {
		unLockErr := locker.UnLock(ctx)
		if unLockErr != nil {
			log.WarnContextf(ctx, "CreateExample locker.UnLock Fialed, err:%v", unLockErr)
		}
		log.InfoContextf(ctx, "sid:%s|unlock success", sid)
	}()

	// 创建示例问法
	flowExample := &entity.WorkflowExample{
		FlowID:        workflowId,
		ExampleID:     exampleId,
		Example:       example,
		RobotId:       appBizID,
		Uin:           uin,
		SubUin:        subUin,
		ReleaseStatus: entity.WorkflowReleaseStatusUnPublished,
		Action:        entity.ActionInsert,
	}
	if err := db.CreateWorkflowExample(ctx, flowExample, appBizID); err != nil {
		log.ErrorContextf(ctx, "sid:%s|CreateWorkflowExample|flowId:%s|"+
			"example:%+v|err:%s", sid, workflowId, example, err)
		return nil, errors.OpDataFromDBError("创建工作流示例问法失败")
	}
	return &KEP_WF.CreateWorkflowExampleRsp{
		ExampleId: flowExample.ExampleID,
	}, nil
}

// UpdateWorkflowExample 更新工作流示例问法
func UpdateWorkflowExample(ctx context.Context,
	req *KEP_WF.UpdateWorkflowExampleReq) (*KEP_WF.UpdateWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	appBizID := req.GetAppBizId()
	exampleId := strings.TrimSpace(req.GetExampleId())
	exam := strings.TrimSpace(req.GetExample())

	botID, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botID)
	if err != nil {
		log.WarnContextf(ctx, "UpdateWorkflowExample permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	example, err := db.GetFlowExampleByNameId(ctx, botID, exampleId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetFlowExampleByNameId|exampleId:%s|"+
			"example:%s|err:%+v", sid, exampleId, exam, err)
		return nil, err
	}
	if example == nil {
		return nil, errors.ErrRobotExampleNotFound
	}
	//判断示例问法的名称长度及是否在机器人唯独重复
	err = checkWorkflowExample(ctx, appBizID, example.FlowID, exam)
	if err != nil {
		log.WarnContextf(ctx, "sid:%s|UpdateExample|checkExampleNameAndLength|exampleId:%s|"+
			"example:%s|err:%+v", sid, exampleId, exam, err)
		return nil, err
	}

	if err := db.UpdateWorkflowExample(ctx, appBizID, exampleId, exam); err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateWorkflowExample|err:%+v",
			sid, err)
		return nil, errors.OpDataFromDBError("更新工作流示例问法失败")
	}

	return &KEP_WF.UpdateWorkflowExampleRsp{}, nil
}

// DeleteWorkflowExample 获取工作流示例问法
func DeleteWorkflowExample(ctx context.Context,
	req *KEP_WF.DeleteWorkflowExampleReq) (*KEP_WF.DeleteWorkflowExampleRsp, error) {
	d := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	appBizID := req.GetAppBizId()
	exampleId := strings.TrimSpace(req.GetExampleId())
	botID, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botID)
	if err != nil {
		log.WarnContextf(ctx, "UpdateWorkflowExample permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	if err := d.Transaction(func(tx *gorm.DB) error {
		if err := db.DeleteWorkflowExample(ctx, tx, appBizID, []string{exampleId}); err != nil {
			log.ErrorContextf(ctx, "sid:%s|DeleteWorkflowExample|err:%+v", sid, err)
			return errors.OpDataFromDBError("删除工作流示例问法失败")
		}
		return nil
	}); err != nil {
		return &KEP_WF.DeleteWorkflowExampleRsp{}, err
	}

	return &KEP_WF.DeleteWorkflowExampleRsp{}, nil
}

// ListWorkflowExample 获取工作流示例问法
func ListWorkflowExample(ctx context.Context,
	req *KEP_WF.ListWorkflowExampleReq) (*KEP_WF.ListWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	appBizID := req.GetAppBizId()
	flowId := req.GetWorkflowId()
	keyword := req.GetKeyword()
	rsp := &KEP_WF.ListWorkflowExampleRsp{}

	botID, err := util.CheckReqBotBizIDUint64(ctx, appBizID)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botID)
	if err != nil {
		log.WarnContextf(ctx, "ListWorkflowExample permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}
	// 判断当前工作流是否存在
	workflow, err := db.GetWorkflowDetail(ctx, flowId, appBizID)
	if err != nil || workflow == nil {
		return rsp, utilsErrors.ErrTaskFlowNotFound
	}
	// 当前示例问法
	examples, total, err := db.ListFlowExamByBotAndIntentId(ctx, keyword, flowId, appBizID)
	if err != nil {
		log.ErrorContextf(ctx, "ListFlowExamByBotAndIntentId|%+v", err)
		return rsp, errors.OpDataFromDBError("获取工作流示例问法列表失败")
	}
	for _, v := range examples {
		rsp.List = append(rsp.List, &KEP_WF.ListWorkflowExampleRsp_ExampleData{
			ExampleId: v.ExampleID,
			Example:   v.Example,
		})
	}
	rsp.Total = uint32(total)
	return rsp, nil
}

// checkWorkflowExample 校验问法示例的长度及是否重复
func checkWorkflowExample(ctx context.Context, robotID string, intentId, example string) error {
	maxExampleContent := config.GetMainConfig().ExampleCorpus.IntentExampleContentLen
	maxExampleCount := config.GetMainConfig().ExampleCorpus.IntentExampleMax
	sid := util.RequestID(ctx)

	if len([]rune(example)) > maxExampleContent {
		return errs.Newf(errors.ErrExampleContentTooLong, "示例问法超过%d个字符，请重新填写", maxExampleContent)
	}
	// 获取该工作流下的示例用法总数
	count, err := db.GetFlowExampleForRobotIntentCount(ctx, robotID, intentId)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|robotID:%s|intentId:%s|err:%+v",
			sid, robotID, intentId, err)
		return err
	}
	if int(count) >= maxExampleCount {
		return errs.Newf(errors.ErrExampleCountTooLong, "该工作流下示例问法总数不能超过%d个", maxExampleCount)
	}

	// 检查是否重复
	intentExample, err := db.GetFlowExampleByNameInRobot(ctx, robotID, example)
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|GetExampleByNameInRobot|robotID:%s|intentId:%s｜err:%+v",
			sid, robotID, intentId, err)
		return errors.OpDataFromDBError("获取示例问法失败")
	}
	log.InfoContextf(ctx, "sid:%s|GetExampleByNameInRobot:%+v", sid, intentExample)
	if len(intentExample) > 0 {
		log.WarnContextf(ctx, "sid:%s|checkExampleLengthAndRepeat|"+
			"robotID:%s|example:%s|%+v", sid, robotID, example, intentExample)
		return errors.ErrRobotExampleDuplicated
	}

	return nil
}
