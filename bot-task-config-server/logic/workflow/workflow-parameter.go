// bot-task-config-server
//
// @(#)parameter.go  星期日, 九月 29, 2024
// Copyright(c) 2024, reinhold@Tencent. All rights reserved.

package workflow

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

func getParameterExamplesJsonStr(ctx context.Context, examples []string) string {
	if len(examples) == 0 {
		return "[]"
	}
	aliasStr, err := jsoniter.MarshalToString(examples)
	if err != nil {
		log.ErrorContextf(ctx, "getParameterExamplesJsonStr Failed!  data:%s,err:%v", examples, err)
		return "[]"
	}
	return aliasStr
}

// checkParam 校验参数的基本信息
func checkParam(ctx context.Context, params *entity.ParameterChecks) error {
	maxNameLen := config.GetMainConfig().VerifyParameter.ParamNameMaxLen
	maxDescLen := config.GetMainConfig().VerifyParameter.ParamDescMaxLen
	// 参数名长度校验
	if utf8.RuneCountInString(params.ParamName) == 0 ||
		utf8.RuneCountInString(params.ParamName) > maxNameLen {
		return errs.Newf(errors.ErrParameterNameTooLong, "参数名称应该在 1~%d个字符，请重新填写", maxNameLen)
	}

	// 参数描述长度校验
	if utf8.RuneCountInString(params.ParamDesc) == 0 || utf8.RuneCountInString(params.ParamDesc) > maxDescLen {
		return errs.Newf(errors.ErrParameterDescTooLong, "参数描述应该在 1~%d个字符，请重新填写", maxDescLen)
	}

	if utf8.RuneCountInString(params.CustomAsk) > 0 {
		htmlRegexp, err := regexp.Compile("<[^>]*>")
		if err != nil {
			log.Warnf("checkParam|htmlRegexp|regexp.Compile:%+v", err)
			return err
		}
		customAskLenMax := config.GetMainConfig().VerifyParameter.CustomAskMaxLen
		noTagsCustomAsk := htmlRegexp.ReplaceAllString(params.CustomAsk, "")
		len := utf8.RuneCountInString(strings.TrimSpace(noTagsCustomAsk))
		if len > customAskLenMax {
			return errs.Newf(errors.ErrParameterCustomAskTooLong, "固定话术询问应该在1~%d个字符，请重新填写", maxDescLen)
		}
	}
	// 参数类型校验
	_, ok := KEP_WF.TypeEnum_value[params.ParamType]
	if !ok {
		return errs.Newf(errors.ErrParameterType, "参数类型不合法，请重新填写")
	}

	// 查数据库，看下参数名是否有重复的
	paramInfo, err := db.CheckParamNameExist(ctx, params.NodeParamIds, params.UnExceptParamID, params.ParamName)
	if err != nil {
		return errors.ErrSystem
	}
	if len(paramInfo) > 0 {
		return errors.ErrParamNameDuplicated
	}

	// 参数下正确示例，错误示例是否重复
	return checkCorrectIncorrectExamples(ctx, params.CorrectExamples, params.IncorrectExamples)
}

// checkCorrectIncorrectExamples 检查参数下正确示例，错误示例是否符合要求
func checkCorrectIncorrectExamples(ctx context.Context, correctExamples, incorrectExamples []string) error {
	log.InfoContextf(ctx, "checkCorrectIncorrectExamples, correct:%+v,incorrect:%+v", correctExamples, incorrectExamples)
	correctExampleNameLen := config.GetMainConfig().VerifyParameter.CorrectExampleNameMaxLen
	correctExampleMaxNum := config.GetMainConfig().VerifyParameter.CorrectExampleMaxNum
	incorrectExampleNameMaxLen := config.GetMainConfig().VerifyParameter.IncorrectExampleNameMaxLen
	incorrectExampleMaxNum := config.GetMainConfig().VerifyParameter.IncorrectExampleMaxNum

	// 正确示例长度校验
	correctExampleMap := make(map[string]int)
	for _, v := range correctExamples {
		if utf8.RuneCountInString(v) == 0 || utf8.RuneCountInString(v) > correctExampleNameLen {
			return errs.Newf(errors.ErrParameterExampleNameTooLong, "正确示例名称应该在 1~%d个字符，请重新填写", correctExampleNameLen)
		}
		if _, ok := correctExampleMap[v]; ok {
			return errs.Newf(errors.ErrParameterExampleDuplicated, "正确示例重复: %s", v)
		}
		correctExampleMap[v] = 1
	}

	// 正确示例个数限制
	if len(correctExamples) > correctExampleMaxNum {
		return errs.Newf(errors.ErrParameterExampleNumExceed, "正确示例同义词个数最大为: %d个", correctExampleMaxNum)
	}

	// 错误示例长度校验
	incorrectExampleMap := make(map[string]int)
	for _, v := range incorrectExamples {
		if utf8.RuneCountInString(v) == 0 || utf8.RuneCountInString(v) > incorrectExampleNameMaxLen {
			return errs.Newf(errors.ErrParameterExampleNameTooLong, "错误示例名称应该在 1~%d个字符，请重新填写", incorrectExampleNameMaxLen)
		}
		if _, ok := incorrectExampleMap[v]; ok {
			return errs.Newf(errors.ErrParameterExampleDuplicated, "错误示例重复: %s", v)
		}
		incorrectExampleMap[v] = 1
	}

	// 错误示例个数限制
	if len(incorrectExamples) > incorrectExampleMaxNum {
		return errs.Newf(errors.ErrParameterExampleNumExceed, "错误示例个数最大为: %d个", incorrectExampleMaxNum)
	}

	// 正确示例和错误示例不允许相同检查
	conflictExamples := util.IntersectArray(correctExamples, incorrectExamples)
	if len(conflictExamples) > 0 {
		return errs.Newf(errors.ErrParameterExampleDuplicated, "正确示例与错误示例重复: %s", conflictExamples)
	}

	return nil
}

// checkParamInfoExist 检查参数信息是否存在
func checkParamInfoExist(ctx context.Context, appBizID, paramID string) (workflowParam entity.WorkflowParameter, err error) {
	// 判断参数ID是否存在
	if len(paramID) == 0 {
		return workflowParam, errors.ErrParams
	}
	if len(paramID) > 0 {
		workflowParam, err = db.GetWorkflowParamInfoByAppParamID(ctx, appBizID, paramID)
		if err != nil {
			return workflowParam, errors.ErrSystem
		}
		if workflowParam.ID == 0 {
			return workflowParam, errors.ErrParamNotExist
		}
	}

	return workflowParam, nil
}

// CreateParameter 参数创建操作
func CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq) (*KEP_WF.CreateParameterResp, error) {

	appBizID := req.GetAppBizId()
	paramName := strings.TrimSpace(req.GetName())
	paramDesc := strings.TrimSpace(req.GetDesc())
	paramType := strings.TrimSpace(cast.ToString(req.GetType()))
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	nodeID := strings.TrimSpace(req.GetNodeId())
	nodeName := strings.TrimSpace(req.GetNodeName())

	correctExamples := req.GetCorrectExamples()
	inCorrectExamples := req.GetIncorrectExamples()

	if len(nodeID) == 0 {
		return nil, errors.ErrParams
	}

	// 需要校验一下节点名成是否超长
	nodeNameMaxLen := config.GetMainConfig().VerifyWorkflow.NodeNameMaxLen
	if utf8.RuneCountInString(nodeName) > nodeNameMaxLen {
		return nil, fmt.Errorf("节点名称 %s 不应大于%d个字符，请重新填写", nodeName, nodeNameMaxLen)
	}

	// 添加判断机器人是否存在
	_, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "CreateParameter,permission.CheckRobot:%s", err.Error())
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 检查工作流信息
	workflowInfo, err := db.GetWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.ErrWorkflowNotFound
		}
		return nil, err
	}

	// 检查node下参数是否超限
	nodeParamIDs, err := db.GetParamIDByNodeID(ctx, workflowID, nodeID)
	if err != nil {
		return nil, err
	}
	maxParamNum := config.GetMainConfig().VerifyParameter.ParamMaxNum
	if len(nodeParamIDs) >= maxParamNum {
		return nil, errs.Newf(errors.ErrParameterNumExceed, "同一个节点下最多允许参数个数为: %d", maxParamNum)
	}
	// 获取自定义话术
	customAsk := req.GetCustomAsk()
	customAskEnable := req.GetCustomAskEnable()
	paramChecks := &entity.ParameterChecks{
		NodeParamIds:      nodeParamIDs,
		CorrectExamples:   correctExamples,
		IncorrectExamples: inCorrectExamples,
		UnExceptParamID:   "",
		ParamName:         paramName,
		ParamDesc:         paramDesc,
		ParamType:         paramType,
		CustomAsk:         customAsk,
	}
	// 基本参数校验【名字，描述，长度等】
	err = checkParam(ctx, paramChecks)
	if err != nil {
		return nil, err
	}

	uin, subAccountUin := util.GetUinAndSubAccountUin(ctx)
	paramInfo := &entity.Parameter{
		ParameterID:       idgenerator.NewUUID(),
		ParameterName:     paramName,
		ParameterDesc:     paramDesc,
		ParameterType:     paramType,
		CorrectExamples:   getParameterExamplesJsonStr(ctx, correctExamples),
		IncorrectExamples: getParameterExamplesJsonStr(ctx, inCorrectExamples),
		CustomAsk:         customAsk,
		CustomAskEnable:   customAskEnable,
		AppID:             appBizID,
		UIN:               uin,
		SubUIN:            subAccountUin,
		ReleaseStatus:     entity.ReleaseStatusUnPublished,
		IsDeleted:         0,
		Action:            entity.ActionInsert,
	}

	// 写入数据库
	err = db.CreateParam(ctx, nodeID, nodeName, paramInfo, workflowInfo)
	if err != nil {
		return nil, errors.ErrSystem
	}
	resp := KEP_WF.CreateParameterResp{ParameterId: paramInfo.ParameterID}
	return &resp, nil
}

// UpdateParameter 参数更新操作
func UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq) (*KEP_WF.UpdateParameterResp, error) {
	resp := &KEP_WF.UpdateParameterResp{}

	appBizID := req.GetAppBizId()
	paramID := strings.TrimSpace(req.GetParameterId())
	paramName := strings.TrimSpace(req.GetName())
	paramDesc := strings.TrimSpace(req.GetDesc())
	paramType := strings.TrimSpace(cast.ToString(req.GetType()))
	correctExamples := req.GetCorrectExamples()
	inCorrectExamples := req.GetIncorrectExamples()

	// 添加判断机器人是否存在
	_, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "UpdateParameter,permission.CheckRobot:%s", err.Error())
		return resp, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 参数是否存在检查
	workflowParam, err := checkParamInfoExist(ctx, appBizID, paramID)
	if err != nil {
		return nil, err
	}

	nodeParamIDs, err := db.GetParamIDByNodeID(ctx, workflowParam.WorkFlowID, workflowParam.NodeID)
	if err != nil {
		return nil, err
	}

	// 获取自定义话术及开关
	customAsk := req.GetCustomAsk()
	customAskEnable := req.GetCustomAskEnable()
	paramChecks := &entity.ParameterChecks{
		NodeParamIds:      nodeParamIDs,
		CorrectExamples:   correctExamples,
		IncorrectExamples: inCorrectExamples,
		UnExceptParamID:   paramID,
		ParamName:         paramName,
		ParamDesc:         paramDesc,
		ParamType:         paramType,
		CustomAsk:         customAsk,
	}
	// 【名字，描述，长度等】
	err = checkParam(ctx, paramChecks)
	if err != nil {
		return resp, err
	}

	// 检查工作流的状态，是否处于发布状态中，发布状态不允许编辑
	workflowInfo, err := db.CheckWorkflowStatusByParamID(ctx, appBizID, paramID)
	if err != nil {
		return resp, err
	}

	err = db.UpdateParam(ctx, appBizID, paramID, paramName, paramDesc, paramType,
		getParameterExamplesJsonStr(ctx, correctExamples), getParameterExamplesJsonStr(ctx, inCorrectExamples),
		customAsk, customAskEnable, workflowInfo)
	if err != nil {
		return resp, err
	}

	resp.ParameterId = paramID
	return resp, nil
}

// DeleteParameter 参数的删除操作
func DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq) (*KEP_WF.DeleteParameterResp, error) {
	resp := &KEP_WF.DeleteParameterResp{}

	appBizID := req.GetAppBizId()
	paramID := strings.TrimSpace(req.GetParameterId())

	// 添加判断机器人是否存在
	_, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "DeleteParameter,permission.CheckRobot:%s", err.Error())
		return resp, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 参数是否存在检查
	_, err = checkParamInfoExist(ctx, appBizID, paramID)
	if err != nil {
		return resp, err
	}

	// 检查工作流的状态，是否处于发布状态中，发布状态不允许编辑
	workflow, err := db.CheckWorkflowStatusByParamID(ctx, appBizID, paramID)
	if err != nil {
		return resp, err
	}

	err = db.DeleteParam(ctx, appBizID, paramID, workflow)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

// GetParameterList 获取参数列表
func GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq) (*KEP_WF.GetParameterListResp, error) {
	resp := &KEP_WF.GetParameterListResp{}
	appBizID := req.GetAppBizId()
	paramIDs := req.GetParameterId()
	//workflowID := strings.TrimSpace(req.GetWorkflowId())
	pageSize := req.GetPageSize()
	pageNum := req.GetPageNumber()

	if pageNum == 0 {
		pageNum = 1
	}
	if pageSize == 0 || pageSize > 1000 {
		pageSize = 1000
	}
	// 添加判断机器人是否存在
	_, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "GetParameterList,permission.CheckRobot:%s", err.Error())
		return resp, err
	}
	// 根据节点id获取参数ID
	if len(paramIDs) == 0 {
		return resp, nil
	}
	total, paramInfos, err := db.GetParamInfosByParamIDs(ctx, paramIDs, int(pageSize), int(pageNum))
	if err != nil {
		return resp, err
	}

	for _, v := range paramInfos {
		paramInfo := &KEP_WF.ParameterInfo{
			ParameterId:       v.ParameterID,
			Name:              v.ParameterName,
			Desc:              v.ParameterDesc,
			Type:              v.ParameterType,
			CorrectExamples:   util.GetParameterExamplesArr(ctx, v.CorrectExamples),
			IncorrectExamples: util.GetParameterExamplesArr(ctx, v.IncorrectExamples),
			CustomAsk:         v.CustomAsk,
			CustomAskEnable:   v.CustomAskEnable,
		}
		resp.List = append(resp.List, paramInfo)
	}
	resp.Total = uint32(total)
	resp.PageSize = pageSize
	resp.PageNumber = pageNum
	return resp, nil

}

// GetBotNodeParameterList 获取当前应用下所有的参数信息
func GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq) (*KEP_WF.GetBotNodeParameterListResp, error) {
	resp := new(KEP_WF.GetBotNodeParameterListResp)

	appBizID := req.GetAppBizId()
	keyword := strings.TrimSpace(req.GetKeyword())
	pageLimit := req.GetPageLimit()
	pageOffset := req.GetPageOffset()
	workflowID := strings.TrimSpace(req.GetWorkflowId())

	if pageLimit == 0 || pageLimit > 2000 {
		pageLimit = 2000
	}

	// 添加判断机器人是否存在
	_, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "GetBotNodeParameterList,permission.CheckRobot:%s", err.Error())
		return resp, err
	}

	// 检查workflowID是否存在
	workflow, err := db.GetWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.ErrWorkflowNotFound
		}
		return nil, err
	}

	total, nodeParamInfo, err := db.GetNodeParamInfoByAppID(ctx, appBizID, workflow.WorkflowID,
		keyword, int(pageOffset), int(pageLimit))

	workflowIDs := make([]string, 0)
	for _, v := range nodeParamInfo {
		workflowIDs = append(workflowIDs, v.WorkFlowID)
	}

	workflowsMap, err := db.GetWorkflowDetails(ctx, workflowIDs, appBizID)
	if err != nil {
		return resp, err
	}

	// 组装list返回
	for _, v := range nodeParamInfo {
		workflowName, ok := workflowsMap[v.WorkFlowID]
		if !ok {
			continue
		}

		tmpData := &KEP_WF.GetBotNodeParameterListResp_WorkflowNodeParameterInfo{
			WorkflowName:      workflowName.WorkflowName,
			WorkflowId:        v.WorkFlowID,
			NodeName:          v.NodeName,
			ParameterId:       v.ParameterID,
			ParameterName:     v.ParameterName,
			ParameterDesc:     v.ParameterDesc,
			ParameterType:     v.ParameterType,
			CorrectExamples:   util.GetParameterExamplesArr(ctx, v.CorrectExamples),
			IncorrectExamples: util.GetParameterExamplesArr(ctx, v.IncorrectExamples),
			CustomAsk:         v.CustomAsk,
			CustomAskEnable:   v.CustomAskEnable,
		}
		resp.List = append(resp.List, tmpData)
	}
	resp.Total = uint32(total)
	resp.PageOffset = pageOffset
	resp.PageLimit = pageLimit
	return resp, nil
}
