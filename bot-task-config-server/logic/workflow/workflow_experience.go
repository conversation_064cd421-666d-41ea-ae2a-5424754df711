// bot-task-config-server
//
// @(#)workflow_experience.go  星期一, 二月 24, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package workflow

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// CreateExperienceWorkflow 创建体验中心指定应用下的工作流
func CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq) (
	*KEP_WF.CreateExperienceWorkflowRsp, error) {
	rsp := &KEP_WF.CreateExperienceWorkflowRsp{}
	staffID := util.StaffID(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	robotInfo, err := rpc.GetAppInfo(ctx, 1, appBizId)
	if err != nil {
		log.WarnContext(ctx, "CreateExperienceWorkflow|GetAppInfo|appBizId:%d|err=%+v|", appBizId, err)
		return nil, errors.ErrRobotNotFound
	}
	if len(req.GetExperienceAppBizId()) == 0 {
		log.WarnContextf(ctx, "CreateExperienceWorkflow req.GetExperienceAppBizId is empty")
		return nil, errors.BadWorkflowReqError("请输入体验中心应用ID")
	}
	// 提前判断是否有工作流需要复制，不需要就直接返回；需要就创建异步任务
	workflowList, err := db.GetExperienceWorkflowIds(ctx, []string{req.GetExperienceAppBizId()}, nil)
	if err != nil {
		return nil, err
	}
	if len(workflowList) == 0 {
		log.InfoContextf(ctx, "CreateExperienceWorkflow|workflowList is empty")
		return rsp, nil
	}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	var newWorkflowId string
	// 创建复制指定体验中心应用下的工作流到当前应用下的任务
	taskParams := entity.TaskCopyExperienceWorkflowParams{
		RequestID:         util.RequestID(ctx),
		CorpID:            robotInfo.GetCorpId(),
		StaffID:           staffID,
		RobotID:           req.GetAppBizId(),
		ExperienceRobotID: req.GetExperienceAppBizId(),
		Uin:               uin,
		SubUin:            subUin,
		TaskID:            uint64(idgenerator.NewInt64ID()),
	}
	// 根据admin请求参数，判断是否需要指定单工作流模式下替换后的工作流ID
	if len(req.GetWorkflowId()) != 0 {
		newWorkflowId = idgenerator.NewUUID()
		taskParams.WorkflowID = req.GetWorkflowId()
		taskParams.NewWorkflowID = newWorkflowId
	}
	// 创建任务调度
	if err := scheduler.NewImportExperienceWorkflowTask(ctx, req.GetAppBizId(), taskParams); err != nil {
		return rsp, err
	}
	rsp.WorkflowId = newWorkflowId
	return rsp, nil
}

// GetExperienceWorkflowList 获取体验中心下所有工作流模版列表
func GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq) (
	*KEP_WF.GetExperienceWorkflowListRsp, error) {
	rsp := &KEP_WF.GetExperienceWorkflowListRsp{}
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	_, err = permission.CheckRobot(ctx, 1, appBizId)
	if err != nil {
		if errors.Is(err, errors.ErrLicenseInvalid) {
			return nil, errors.ErrLicenseInvalid
		}
		return nil, errors.ErrRobotNotFound
	}
	request := &admin.GetValidExperienceAppsReq{
		PageSize:   1000,
		PageNumber: 1,
	}
	// 拉取体验中心下的所有应用
	getValidExperienceAppResp, err := rpc.GetValidExperienceApps(ctx, request)
	if err != nil {
		log.ErrorContextf(ctx, "GetExperienceWorkflowList GetValidExperienceApps Failed, err:%+v", err)
		return rsp, err
	}
	list := make([]*KEP_WF.WorkflowRef, 0)
	appIdList := make([]string, 0)
	if len(getValidExperienceAppResp.GetList()) > 0 {
		for _, apps := range getValidExperienceAppResp.GetList() {
			appId := strconv.FormatUint(apps.GetAppBizId(), 10)
			appIdList = append(appIdList, appId)
		}
	}
	log.InfoContextf(ctx, "GetExperienceWorkflowList appIdList|%+v", appIdList)
	if len(appIdList) > 0 {
		// 获取所有体验中心下已发布的工作流
		workflowList, err := db.GetExperienceWorkflowIds(ctx, appIdList, nil)
		log.InfoContextf(ctx, "GetExperienceWorkflowList workflowList|%+v", workflowList)
		if err != nil {
			log.ErrorContextf(ctx, "GetExperienceWorkflowList GetExperienceWorkflowIds Failed, err:%v", err)
			return nil, err
		}
		if len(workflowList) > 0 {
			for _, workflow := range workflowList {
				list = append(list, &KEP_WF.WorkflowRef{
					WorkflowId:   workflow.WorkflowID,
					WorkflowName: workflow.WorkflowName,
					WorkflowDesc: workflow.WorkflowDesc,
				})
			}
		}
	}
	rsp.List = list
	return rsp, nil
}
