-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_bot_task`
--

DROP TABLE IF EXISTS `t_bot_task`;


CREATE TABLE `t_bot_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '使用者ID, 业务按实际情况关联',
  `task_type` tinyint NOT NULL COMMENT '任务类型',
  `task_mutex` tinyint NOT NULL COMMENT '任务互斥',
  `params` longtext NOT NULL COMMENT '参数',
  `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_times` int NOT NULL DEFAULT '0' COMMENT '最大重试次数',
  `timeout` int NOT NULL DEFAULT '0' COMMENT '超时时间(s)',
  `runner` varchar(64) NOT NULL COMMENT '执行器',
  `runner_instance` bigint NOT NULL COMMENT '执行器实例',
  `result` text NOT NULL COMMENT '本次结果',
  `trace_id` varchar(64) NOT NULL COMMENT 'trace_id',
  `start_time` datetime NOT NULL COMMENT '任务开始执行时间',
  `end_time` datetime NOT NULL COMMENT '任务完成时间',
  `next_start_time` datetime NOT NULL COMMENT '下次任务开始执行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id_task_mutex` (`user_id`,`task_mutex`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务';











-- Dump completed on 2024-01-25  9:03:15
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_bot_task_history`
--

DROP TABLE IF EXISTS `t_bot_task_history`;


CREATE TABLE `t_bot_task_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '使用者ID, 业务按实际情况关联',
  `task_type` tinyint NOT NULL COMMENT '任务类型',
  `task_mutex` tinyint NOT NULL COMMENT '任务互斥',
  `params` longtext NOT NULL COMMENT '参数',
  `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_times` int NOT NULL DEFAULT '0' COMMENT '最大重试次数',
  `timeout` int NOT NULL DEFAULT '0' COMMENT '超时时间(s)',
  `runner` varchar(64) NOT NULL COMMENT '执行器',
  `runner_instance` bigint NOT NULL COMMENT '执行器实例',
  `result` text NOT NULL COMMENT '本次结果',
  `is_success` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态: 0: 失败; 1 成功;',
  `trace_id` varchar(64) NOT NULL COMMENT 'trace_id',
  `start_time` datetime NOT NULL COMMENT '任务开始执行时间',
  `end_time` datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '任务完成时间',
  `next_start_time` datetime NOT NULL COMMENT '下次任务开始执行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id_task_mutex` (`user_id`,`task_mutex`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务历史';











-- Dump completed on 2024-01-25  9:03:15
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_category`
--

DROP TABLE IF EXISTS `t_category`;


CREATE TABLE `t_category` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_category_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '类别id',
  `f_category_name` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '类别名字',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_level` tinyint NOT NULL DEFAULT '1' COMMENT '类别等级',
  `f_parent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '类别父级id',
  `f_feature` varchar(128) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '功能模块：文档库or任务流',
  `f_order_number` int NOT NULL DEFAULT '1' COMMENT '同层级title的排序编号',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_is_deleted` bigint NOT NULL DEFAULT '0' COMMENT '0未删除 1已删除',
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `idx_category_id` (`f_category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='对话任务流分类';











-- Dump completed on 2024-01-25  9:03:15
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_corpus`
--

DROP TABLE IF EXISTS `t_corpus`;


CREATE TABLE `t_corpus` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_corpus_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '语料ID',
  `f_content` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '语料内容',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_corpus_id` (`f_corpus_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='语料库';











-- Dump completed on 2024-01-25  9:03:15
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_dict`
--

DROP TABLE IF EXISTS `t_dict`;


CREATE TABLE `t_dict` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_dict_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_dict_name` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '词典名称',
  `f_dict_type` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '词典类型：系统or用户',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_dict_id` (`f_dict_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='词典';











-- Dump completed on 2024-01-25  9:03:16
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_entity`
--

DROP TABLE IF EXISTS `t_entity`;


CREATE TABLE `t_entity` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_entity_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '实体id',
  `f_entity` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '实体',
  `f_entity_alias` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '实体别名',
  `f_dict_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_entity_id` (`f_dict_id`,`f_entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='实体表';











-- Dump completed on 2024-01-25  9:03:16
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_intent`
--

DROP TABLE IF EXISTS `t_intent`;


CREATE TABLE `t_intent` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图id',
  `f_intent_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '意图名称',
  `f_intent_type` enum('FLOW','') COLLATE utf8mb4_bin NOT NULL DEFAULT 'FLOW' COMMENT '任务类型：FLOW对话树意图 or 非对话树意图',
  `f_source` enum('SYS','GLOBAL','ICS','ASSIS') COLLATE utf8mb4_bin NOT NULL DEFAULT 'ICS' COMMENT '业务来源：系统全局-SYS、用户全局-GLOBAL、智能客服-ICS、助手-ASSIS',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID，source为SYS时，该项为空',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_id` (`f_intent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='意图表';











-- Dump completed on 2024-01-25  9:03:16
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_intent_flow`
--

DROP TABLE IF EXISTS `t_intent_flow`;


CREATE TABLE `t_intent_flow` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_flow_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '引用ID',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_flow_id` (`f_intent_id`,`f_flow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='意图任务流关联表';











-- Dump completed on 2024-01-25  9:03:16
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_robot_configs`
--

DROP TABLE IF EXISTS `t_robot_configs`;


CREATE TABLE `t_robot_configs` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '机器人ID',
  `f_robot_version` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '版本信息',
  `f_robot_configs` text COLLATE utf8mb4_bin NOT NULL COMMENT '记录不同版本关联的信息',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_robot_id` (`f_robot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='机器人版本配置';











-- Dump completed on 2024-01-25  9:03:17
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_robot_intent`
--

DROP TABLE IF EXISTS `t_robot_intent`;


CREATE TABLE `t_robot_intent` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人ID',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_robot_intent_id` (`f_robot_id`,`f_intent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='机器人意图关联表';











-- Dump completed on 2024-01-25  9:03:17
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_skill`
--

DROP TABLE IF EXISTS `t_skill`;


CREATE TABLE `t_skill` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_skill_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '技能ID',
  `f_skill_name` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '技能名称',
  `f_skill_desc` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '技能描述',
  `f_skill_icon` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '技能图标',
  `f_language` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '支持的语言',
  `f_is_internal` int NOT NULL DEFAULT '0' COMMENT '是否内部调用',
  `f_unuse_reply` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '未启用回复',
  `f_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '系统全局、用户全局、机器人级',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='技能表';











-- Dump completed on 2024-01-25  9:03:17
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_skill_intent`
--

DROP TABLE IF EXISTS `t_skill_intent`;


CREATE TABLE `t_skill_intent` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_skill_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '技能ID',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图名称',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='技能意图关联表';











-- Dump completed on 2024-01-25  9:03:18
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_slot`
--

DROP TABLE IF EXISTS `t_slot`;


CREATE TABLE `t_slot` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_slot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '槽位id',
  `f_slot_name` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '槽位中文名称',
  `f_slot_var` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '槽位变量名称',
  `f_slot_type` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '槽位类型',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图id',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_slot_id` (`f_intent_id`,`f_slot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='槽位表';











-- Dump completed on 2024-01-25  9:03:18
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_slot_dict`
--

DROP TABLE IF EXISTS `t_slot_dict`;


CREATE TABLE `t_slot_dict` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_slot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '槽位ID',
  `f_dict_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_slot_dict_id` (`f_slot_id`,`f_dict_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='槽位词典表';











-- Dump completed on 2024-01-25  9:03:18
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_sync_task`
--

DROP TABLE IF EXISTS `t_sync_task`;


CREATE TABLE `t_sync_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `user_id` varchar(80) NOT NULL DEFAULT '' COMMENT '用户ID, 登录用户唯一标识',
  `user_name` varchar(255) NOT NULL COMMENT '操作用户名称, 显示使用',
  `scene` varchar(80) NOT NULL COMMENT '使用场景, 电话客服, 文本客服',
  `robot_id` varchar(100) NOT NULL COMMENT '机器人 id',
  `task_id` varchar(100) NOT NULL COMMENT '任务 id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` int NOT NULL COMMENT '状态, 新任务, 同步中, 同步成功, 同步失败',
  `success_count` int NOT NULL DEFAULT '0',
  `failed_count` int NOT NULL DEFAULT '0',
  `done_time` datetime DEFAULT NULL COMMENT '结束时间',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '此次记录产生的 session id',
  `server` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作 server, 显示使用',
  `work_ip` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作 ip, 显示使用',
  `note` varchar(1000) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务记录';











-- Dump completed on 2024-01-25  9:03:18
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_sync_task_log`
--

DROP TABLE IF EXISTS `t_sync_task_log`;


CREATE TABLE `t_sync_task_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `robot_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '机器人',
  `sync_task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对应的任务记录 主键 ID',
  `session_id` varchar(100) NOT NULL DEFAULT '',
  `type` varchar(30) NOT NULL DEFAULT '' COMMENT '任务: TASK, 对比 DIFF',
  `log_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志内容',
  `error_message` varchar(1000) DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `server` varchar(100) NOT NULL DEFAULT '',
  `work_ip` varchar(60) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志';











-- Dump completed on 2024-01-25  9:03:19
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_task_flow`
--

DROP TABLE IF EXISTS `t_task_flow`;


CREATE TABLE `t_task_flow` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_flow_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '任务流id',
  `f_intent_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '意图名称',
  `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_flow_state` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'DRAFT' COMMENT '任务流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改',
  `f_flow_type` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '任务流类型：ICS：客服or ASSIS：助手',
  `f_version` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '版本',
  `f_category_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '类别ID',
  `f_dialog_json_draft` mediumtext COLLATE utf8mb4_bin NOT NULL COMMENT '对话树json字段草稿',
  `f_dialog_json_enable` mediumtext COLLATE utf8mb4_bin NOT NULL COMMENT '启用状态下的节点信息',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除',
  `f_action` enum('INSERT','UPDATE','DELETE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_flow_id` (`f_flow_id`),
  KEY `idx_category_id` (`f_category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='对话任务流配置';











-- Dump completed on 2024-01-25  9:03:19
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_task_flow_history`
--

DROP TABLE IF EXISTS `t_task_flow_history`;


CREATE TABLE `t_task_flow_history` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_flow_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '任务流ID',
  `f_flow_json` mediumtext COLLATE utf8mb4_bin NOT NULL COMMENT '对话树Json',
  `f_version` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '版本信息',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='任务流历史记录';











-- Dump completed on 2024-01-25  9:03:19
-- ************************************************************
--
-- close fk
--
-- skip


-- MySQL dump 10.13  Distrib 8.0.25, for Linux (x86_64)
--
-- Host: ************    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql












--
-- Table structure for table `t_task_flow_import`
--

DROP TABLE IF EXISTS `t_task_flow_import`;


CREATE TABLE `t_task_flow_import` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_import_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '导入id',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_parent_import_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '父导入id',
  `f_params` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '参数',
  `f_status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0:待处理，1：处理中，2：已处理，3：处理失败',
  `f_message` varchar(5120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_final_params` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '最终参数',
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `idx_import_id` (`f_import_id`),
  KEY `idx_robot_parent_importid` (`f_robot_id`,`f_parent_import_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='对话任务流导入任务';











-- Dump completed on 2024-01-25  9:03:19
