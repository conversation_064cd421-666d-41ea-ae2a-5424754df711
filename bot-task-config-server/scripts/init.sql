CREATE TABLE `t_category` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_category_id` varchar(64) NOT NULL DEFAULT '' COMMENT '类别id',
  `f_category_name` varchar(32) NOT NULL DEFAULT '' COMMENT '类别名字',
  `f_robot_id` varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_level` tinyint NOT NULL DEFAULT 1 COMMENT '类别等级',
  `f_parent_id` varchar(64) NOT NULL DEFAULT '' COMMENT '类别父级id',
  `f_feature` varchar(128) NOT NULL DEFAULT '' COMMENT '功能模块：文档库or任务流',
  `f_order_number` int NOT NULL DEFAULT 1 COMMENT '同层级title的排序编号',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_is_deleted`    tinyint      NOT NULL DEFAULT 0 COMMENT '0未删除 1已删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `idx_category_id` (`f_category_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '对话任务流分类';

CREATE TABLE t_robot_intent (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_robot_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '机器人ID',
  `f_intent_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id),
  KEY `idx_robot_intent_id` (`f_robot_id`, `f_intent_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '机器人意图关联表';

CREATE TABLE `t_intent` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_intent_id` varchar(64) NOT NULL DEFAULT '' COMMENT '意图id',
  `f_intent_name` varchar(32) NOT NULL DEFAULT '' COMMENT '意图名称',
  `f_intent_type` enum ('FLOW', '') NOT NULL DEFAULT 'FLOW' COMMENT '任务类型：FLOW对话树意图 or 非对话树意图',
  `f_source` enum ('SYS', 'GLOBAL', 'ICS', 'ASSIS') NOT NULL DEFAULT 'ICS' COMMENT '业务来源：系统全局-SYS、用户全局-GLOBAL、智能客服-ICS、助手-ASSIS',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID，source为SYS时，该项为空',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_id` (`f_intent_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图表';

CREATE TABLE t_intent_flow (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_flow_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '引用ID',
  `f_intent_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_flow_id` (`f_intent_id`, `f_flow_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图任务流关联表';

CREATE TABLE t_slot (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_slot_id` varchar(64) NOT NULL DEFAULT '' COMMENT '槽位id',
  `f_slot_name` varchar(32) NOT NULL DEFAULT '' COMMENT '槽位中文名称',
  `f_slot_var` varchar(32) NOT NULL DEFAULT '' COMMENT '槽位变量名称',
  `f_slot_type` varchar(32) NOT NULL DEFAULT '' COMMENT '槽位类型',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_intent_id` varchar(64) NOT NULL DEFAULT '' COMMENT '意图id',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_intent_slot_id` (`f_intent_id`, `f_slot_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '槽位表';

CREATE TABLE t_slot_dict (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_slot_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '槽位ID',
  `f_dict_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id),
  KEY `idx_slot_dict_id` (`f_slot_id`, `f_dict_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '槽位词典表';

CREATE TABLE t_dict (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_dict_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_dict_name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '词典名称',
  `f_dict_type` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '词典类型：系统or用户',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id),
  KEY `idx_dict_id` (`f_dict_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '词典';

CREATE TABLE `t_entity` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_entity_id` varchar(64) NOT NULL DEFAULT '' COMMENT '实体id',
  `f_entity` varchar(32) NOT NULL DEFAULT '' COMMENT '实体',
  `f_entity_alias` varchar(32) NOT NULL DEFAULT '' COMMENT '实体别名',
  `f_dict_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '词典ID',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_entity_id` (`f_dict_id`, `f_entity_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '实体表';

CREATE TABLE t_task_flow (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_flow_id` varchar(64) NOT NULL DEFAULT '' COMMENT '任务流id',
  `f_intent_name` varchar(32) NOT NULL DEFAULT '' COMMENT '意图名称',
  `f_intent_id` varchar(64) NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_flow_state` varchar(64)  NOT NULL DEFAULT 'DRAFT' COMMENT '任务流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改;',
  `f_flow_type` varchar(64) NOT NULL DEFAULT 'ICS' COMMENT '任务流类型：ICS：客服 or ASSIS：助手',
  `f_version` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '版本',
  `f_category_id` varchar(64) NOT NULL DEFAULT '' COMMENT '类别ID',
  `f_dialog_json_draft` mediumtext NOT NULL COMMENT '对话树json字段草稿',
  `f_dialog_json_enable` mediumtext NOT NULL COMMENT '启用状态下的节点信息',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_flow_id` (`f_flow_id`),
  KEY `idx_category_id` (`f_category_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '对话任务流配置';

CREATE TABLE t_robot_configs (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_robot_id` VARCHAR(64) NOT NULL COMMENT '机器人ID',
  `f_robot_version` VARCHAR(32) NOT NULL COMMENT '版本信息',
  `f_robot_configs` text NOT NULL COMMENT '记录不同版本关联的信息',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id),
  KEY `idx_robot_id` (`f_robot_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '机器人版本配置';

CREATE TABLE t_corpus (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_corpus_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '语料ID',
  `f_content` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '语料内容',
  `f_intent_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id),
  KEY `idx_corpus_id` (`f_corpus_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '语料库';

CREATE TABLE t_skill (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_skill_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '技能ID',
  `f_skill_name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '技能名称',
  `f_skill_desc` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '技能描述',
  `f_skill_icon` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '技能图标',
  `f_language` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '支持的语言',
  `f_is_internal` int NOT NULL DEFAULT 0 COMMENT '是否内部调用',
  `f_unuse_reply` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '未启用回复',
  `f_type` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '系统全局、用户全局、机器人级',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '技能表';

CREATE TABLE t_skill_intent (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_skill_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '技能ID',
  `f_intent_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图名称',
  `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
  `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
  `f_action` enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '技能意图关联表';

CREATE TABLE t_task_flow_history (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_flow_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '任务流ID',
  `f_flow_json` mediumtext NOT NULL COMMENT '对话树Json',
  `f_version` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '版本信息',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (f_id)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '任务流历史记录';

CREATE TABLE `t_task_flow_import` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_import_id` varchar(64) NOT NULL DEFAULT '' COMMENT '导入id',
  `f_robot_id` varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_parent_import_id` varchar(64) NOT NULL DEFAULT '' COMMENT '父导入id',
  `f_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数',
  `f_final_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最终参数',
  `f_status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0:待处理，1：处理中，2：已处理，3：处理失败',
  `f_message` varchar(5120) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `idx_import_id` (`f_import_id`),
  KEY `idx_robot_parent_importid` (`f_robot_id`, `f_parent_import_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '对话任务流导入任务';

CREATE TABLE `t_bot_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '使用者ID, 业务按实际情况关联',
  `task_type` tinyint NOT NULL COMMENT '任务类型',
  `task_mutex` tinyint NOT NULL COMMENT '任务互斥',
  `params` longtext NOT NULL COMMENT '参数',
  `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_times` int NOT NULL DEFAULT '0' COMMENT '最大重试次数',
  `timeout` int NOT NULL DEFAULT '0' COMMENT '超时时间(s)',
  `runner` varchar(64) NOT NULL COMMENT '执行器',
  `runner_instance` bigint NOT NULL COMMENT '执行器实例',
  `result` text NOT NULL COMMENT '本次结果',
  `trace_id` varchar(64) NOT NULL COMMENT 'trace_id',
  `start_time` datetime NOT NULL COMMENT '任务开始执行时间',
  `end_time` datetime NOT NULL COMMENT '任务完成时间',
  `next_start_time` datetime NOT NULL COMMENT '下次任务开始执行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id_task_mutex` (`user_id`, `task_mutex`),
  KEY `create_time` (`create_time`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务';

CREATE TABLE `t_bot_task_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '使用者ID, 业务按实际情况关联',
  `task_type` tinyint NOT NULL COMMENT '任务类型',
  `task_mutex` tinyint NOT NULL COMMENT '任务互斥',
  `params` longtext NOT NULL COMMENT '参数',
  `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_times` int NOT NULL DEFAULT '0' COMMENT '最大重试次数',
  `timeout` int NOT NULL DEFAULT '0' COMMENT '超时时间(s)',
  `runner` varchar(64) NOT NULL COMMENT '执行器',
  `runner_instance` bigint NOT NULL COMMENT '执行器实例',
  `result` text NOT NULL COMMENT '本次结果',
  `is_success` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态: 0: 失败; 1 成功;',
  `trace_id` varchar(64) NOT NULL COMMENT 'trace_id',
  `start_time` datetime NOT NULL COMMENT '任务开始执行时间',
  `end_time` datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '任务完成时间',
  `next_start_time` datetime NOT NULL COMMENT '下次任务开始执行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id_task_mutex` (`user_id`, `task_mutex`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务历史';