-- MySQL dump 10.14  Distrib 5.5.65-MariaDB, for Linux (x86_64)
--
-- Host: ***********    Database: db_llm_robot_task_dev
-- ------------------------------------------------------
-- Server version	8.0.22-txsql

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_app_shared`
--

DROP TABLE IF EXISTS `t_app_shared`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_shared` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
  `f_app_id` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '机器人/应用ID',
  `f_shared_code` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '分享码',
  `f_create_user` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '创建用户ID',
  `f_update_user` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '更新用户ID',
  `f_expire_time` datetime NOT NULL COMMENT '过期时间',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_is_deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`f_id`),
  KEY `f_shared_code` (`f_shared_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='应用分享码ID';
/*!40101 SET character_set_client = @saved_cs_client */;
