-------------- v2.1变更： 删除v1.7的无用表 --------------
DROP TABLE IF EXISTS `t_slot`;
DROP TABLE IF EXISTS `t_slot_dict`;
DROP TABLE IF EXISTS `t_dict`;
DROP TABLE IF EXISTS `t_entity`;
DROP TABLE IF EXISTS `t_task_flow_publish_history`;



-- v2.1新增: 任务流程历史发布记录表
CREATE TABLE t_task_flow_publish_history (
     `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
     `f_flow_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '任务流ID',
     `f_intent_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
     `f_flow_json` mediumtext NOT NULL COMMENT '对话树Json',
     `f_version` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '版本信息',
     `f_proto_version` tinyint(1) NOT NULL DEFAULT 0 COMMENT '对话树的协议版本号',
     `f_save_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '保存的历史类型：1:发布版本、0:草稿版本',
     `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
     `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
     `f_user_name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '发布/创建用户',
     `f_is_deleted`     bigint         NOT NULL DEFAULT 0 COMMENT '是否删除',
     `f_publish_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
     `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY   `idx_flow_id` (`f_flow_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '任务流程历史发布记录';



-- v2.1新增: 意图和槽位的引用关系维护
CREATE TABLE t_intent_slot
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_intent_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
    `f_slot_id`        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '槽位ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_intent_slot_id` (`f_intent_id`, `f_slot_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图槽位的关联表';


--  v2.1新增: 槽位表
-- 槽位没有"系统级"的概念，只有"机器人级"的概念，所以是有"机器人ID"的
-- 从使用场景上，只有某个机器人下的的槽位，引用了"系统级"实体 或 "机器人级"的实体
CREATE TABLE t_slot
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_slot_id`        varchar(64) NOT NULL DEFAULT '' COMMENT '槽位id',
    `f_slot_name`      varchar(32) NOT NULL DEFAULT '' COMMENT '槽位中文名称',
    `f_slot_examples`  varchar(256) NOT NULL DEFAULT  '' COMMENT '槽位示例',
    `f_slot_desc`      varchar(2000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述信息',
    `f_robot_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_uin`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_slot_id` (`f_slot_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '槽位表';


--  v2.1新增: 槽位关联的实体表
CREATE TABLE t_slot_entity
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_slot_id`        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '槽位ID',
    `f_entity_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '词典ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY                `idx_slot_entity_id` (`f_slot_id`, `f_entity_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '槽位实体关联表';



-- v2.1新增: 实体表
-- 级别类型表示：
--   1. 系统级：   f_level_type 为 SYS； 并且 f_robot_id 为空
--   2. 机器人级： f_level_type 为 BOT； 并且填充 f_robot_id
--   3. 用户级：   未来扩充，增加 type的枚举值 USR
CREATE TABLE t_entity
(
    `f_id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_entity_id`       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '实体ID',
    `f_entity_name`     VARCHAR(32) NOT NULL DEFAULT '' COMMENT '实体名称',
    `f_entity_examples` VARCHAR(256) NOT NULL DEFAULT  '' COMMENT '实体示例',
    `f_entity_desc`     varchar(2000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述信息',
    `f_level_type`      enum ('SYS', 'BOT') NOT NULL DEFAULT 'BOT' COMMENT '词典的数据级别类型：SYS or BOT',
    `f_robot_id`        varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id; f_level_type是 SYS时空； BOT时不为空',
    `f_uin`             VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`         VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status`  enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`      int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`          enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY                `idx_entity_id` (`f_entity_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '实体表';

-- v2.1新增: 词条表（某个实体下的词条）
CREATE TABLE `t_entry`
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_entry_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '词条id',
    `f_entity_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '实体ID',
    `f_entry_value`    varchar(32) NOT NULL DEFAULT '' COMMENT '词条值',
    `f_entry_alias`    varchar(2048) NOT NULL DEFAULT '' COMMENT '词条别名(同义词)',
    `f_uin`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_entity_entry_id` (`f_entity_id`, `f_entry_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '词条表';


-- v2.1新增 词条向量组与机器人关系表
CREATE TABLE `t_entry_vector_group` (
    `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_vector_group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '向量组group',
    `f_vector_group_type` enum('sandbox', 'prod') NOT NULL DEFAULT 'sandbox' COMMENT '环境区分：sandbox-沙箱环境， prod-正式环境',
    `f_robot_id` varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_embedding_mode_name` varchar(128) NOT NULL DEFAULT '' COMMENT '模型名称',
    `f_uin` varchar(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin` varchar(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '词条向量组与机器人关系表';



-- halelv
-- 增加对话树的复制次数
ALTER TABLE `t_task_flow`
    ADD COLUMN `f_copy_count` int(11) NOT NULL DEFAULT 0 COMMENT '对话树的复制次数' AFTER `f_version`;

-- leo
-- 增加意图描述
ALTER TABLE `t_task_flow`
    ADD COLUMN `f_intent_desc` varchar(2000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述信息' AFTER `f_intent_name`;

ALTER TABLE `t_intent`
    ADD COLUMN `f_intent_desc` varchar(2000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述信息' AFTER `f_intent_name`;

-- 在删除任务流的同时，标记删除 历史记录，方便后续表数据清理
ALTER TABLE `t_task_flow_history`
    ADD COLUMN `f_is_deleted`     bigint         NOT NULL DEFAULT 0 COMMENT '是否删除' AFTER `f_sub_uin`;

-- t_task_flow_history 检索引，方便查询更新
CREATE INDEX idx_flow_id ON t_task_flow_history (f_flow_id);

-- xinghui
-- 增加对话树的协议版本号，用于兼容和未来扩展，也可以用于兼容现在线上的版本；
-- 具体版本号的映射关系在 TaskFlowProtoVersion
-- https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/tree.proto#L19
ALTER TABLE `t_task_flow`
    ADD COLUMN `f_proto_version` tinyint(1) NOT NULL DEFAULT 0 COMMENT '对话树的协议版本号' AFTER `f_dialog_json_enable`;

-- xinghui
-- 为了未来“从历史版本恢复/复制”时也能知道历史版本是哪个协议版本号，也要在t_task_flow_history 里加上这个字段；
ALTER TABLE `t_task_flow_history`
    ADD COLUMN `f_proto_version` tinyint(1) NOT NULL DEFAULT 0 COMMENT '对话树的协议版本号' AFTER `f_flow_json`;
