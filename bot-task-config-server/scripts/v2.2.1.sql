CREATE TABLE t_intent_corpus
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_intent_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
    `f_corpus_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '预料id',
    `f_corpus`         varchar(255) NOT NULL DEFAULT '' COMMENT '示例预料',
    `f_robot_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_uin`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `idx_corpus_id` (`f_corpus_id`) USING BTREE,
    KEY                `idx_intent_corpus_id` (`f_intent_id`,`f_corpus_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图预料(示例问法)表';


-- 发布历史任务流程关联的示例问法
CREATE TABLE t_intent_corpus_publish_history
(
    `f_id`           bigint                          NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_flow_id`      VARCHAR(64)                     NOT NULL DEFAULT '' COMMENT '任务流ID',
    `f_intent_id`    varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '意图ID',
    `f_corpus_id`    varchar(64)                     NOT NULL DEFAULT '' COMMENT '预料id',
    `f_corpus`       varchar(255) NOT NULL DEFAULT '' COMMENT '内容',
    `f_version`      VARCHAR(32)                     NOT NULL DEFAULT '' COMMENT '版本信息',
    `f_save_type`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '保存的历史类型：1:发布版本、0:草稿版本',
    `f_uin`          VARCHAR(32)                     NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`      VARCHAR(32)                     NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_is_deleted`   bigint                          NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_publish_time` datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    `f_create_time`  datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`  datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY              `idx_flow_id` (`f_flow_id`),
    KEY              `idx_intent_corpus_id` (`f_intent_id`, `f_corpus_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '任务流程与语料问法关联历史发布记录';

--- 更改 t_entry_vector_group
-- 历史数据为 entry，在添加字段之间需要把历史数据的 f_save_type 保存 为 entry 类型

-- 第一步：将表 t_entry_vector_group 改为 t_vector_group
RENAME TABLE t_entry_vector_group TO t_vector_group;

-- 第二步： 添加 f_save_type 字段
ALTER TABLE `t_vector_group`
    ADD COLUMN `f_save_type` enum('entry', 'intent') NOT NULL COMMENT 'group类型' AFTER `f_robot_id`;

-- 第s步：历史数据为 entry， 将历史数据的 f_save_type 设置为 entry
UPDATE t_vector_group SET f_save_type = 'entry' WHERE f_save_type='';