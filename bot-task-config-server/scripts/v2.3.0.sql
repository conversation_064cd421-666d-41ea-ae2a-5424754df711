-- v2.3新增: 意图和词条的引用关系维护
CREATE TABLE t_intent_entry
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_intent_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
    `f_entry_id`        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '词条ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_intent_entry_id` (`f_intent_id`, `f_entry_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图词条的关联表';
