--  v2.4.0 新增: 变量表
CREATE TABLE t_var
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_var_id`        varchar(64) NOT NULL DEFAULT '' COMMENT '变量id',
    `f_var_name`      varchar(128) NOT NULL DEFAULT '' COMMENT '变量名称',
    `f_app_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '应用id',
    `f_uin`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_var_id` (`f_var_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '变量表';

-- v2.4.0 新增: 意图和变量的引用关系维护
CREATE TABLE t_intent_var
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_intent_id`      VARCHAR(64) NOT NULL DEFAULT '' COMMENT '意图ID',
    `f_var_id`        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '变量ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_intent_var_id` (`f_intent_id`, `f_var_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '意图变量的关联表';
