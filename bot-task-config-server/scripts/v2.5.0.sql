-- V2.5.0 新增，保存导出记录，方便OP平台查询
CREATE TABLE `t_export_file` (
    `f_id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `f_export_id` bigint unsigned NOT NULL COMMENT '导出业务ID',
    `f_app_id` varchar(64) NOT NULL DEFAULT '' COMMENT '应用ID',
    `f_params` varchar(2048) NOT NULL DEFAULT '' COMMENT '下载传参数',
    `f_status` tinyint NOT NULL COMMENT '状态  0 导出中 1 导出成功 2 导出失败',
    `f_cos_url` varchar(1024) NOT NULL DEFAULT '' COMMENT 'cos文件地址',
    `f_file_size` bigint NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
    `f_type` varchar(32) NOT NULL DEFAULT '' COMMENT '下载类型：flow：任务流程',
    `f_platform` varchar(32) NOT NULL DEFAULT '' COMMENT '导出平台：op：op平台导出',
    `f_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `f_is_deleted`  tinyint NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`f_id`),
    KEY `idx_export_id` (`f_export_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '文件下载记录';
