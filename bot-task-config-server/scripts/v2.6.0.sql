CREATE TABLE t_workflow
(
    `f_id`                 bigint        NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_workflow_id`        varchar(64)   NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_workflow_name`      varchar(255)   NOT NULL DEFAULT '' COMMENT '工作流名称',
    `f_desc`               varchar(2000) NOT NULL DEFAULT '' COMMENT '描述信息',
    `f_flow_state`         varchar(64)   NOT NULL DEFAULT 'DRAFT' COMMENT '工作流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改',
    `f_version`            BIGINT UNSIGNED    DEFAULT 0 COMMENT '版本(用于乐观锁)',
    `f_robot_id`           varchar(64)   NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_dialog_json_draft`  mediumtext    NOT NULL COMMENT '对话树json字段草稿',
    `f_dialog_json_enable` mediumtext    NOT NULL COMMENT '启用状态下的节点信息',
    `f_proto_version`      tinyint(1) NOT NULL DEFAULT 0 COMMENT '对话树的协议版本号',
    `f_uin`                VARCHAR(32)   NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`            VARCHAR(32)   NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_staff_id`           bigint unsigned DEFAULT 0 COMMENT '员工ID',
    `f_release_status`     enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_enable`          tinyint       NOT NULL DEFAULT '0' COMMENT '是否启用, 1: 启用，0：禁用',
    `f_is_deleted`         int           NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`             enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                    `idx_flow_id` (`f_workflow_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流';

--  机器人工作流关联表
CREATE TABLE t_robot_workflow
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_robot_id`       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_workflow_id`    VARCHAR(64) NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY                `idx_robot_workflow_id` (`f_robot_id`, `f_workflow_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '机器人工作流关联表';

-- 示例问法/特殊问法(入口：编辑工作流时的底部展开）
CREATE TABLE t_workflow_example
(
    `f_id`             bigint       NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_workflow_id`    VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_example_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '示例问法ID',
    `f_example`        varchar(255) NOT NULL DEFAULT '' COMMENT '示例问法',
    `f_robot_id`       varchar(64)  NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_uin`            VARCHAR(32)  NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32)  NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int          NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `idx_example_id` (`f_example_id`) USING BTREE,
    KEY                `idx_workflow_example_id` (`f_workflow_id`,`f_example_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流-示例问法';


-- 工作流和API参数的引用关系维护
CREATE TABLE t_workflow_var
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_workflow_id`    VARCHAR(64) NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_var_id`         VARCHAR(64) NOT NULL DEFAULT '' COMMENT '变量ID',
    `f_robot_id`           varchar(64)   NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_workflow_var_id` (`f_workflow_id`, `f_var_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流和API参数的关联表';


-- 工作流和工作流的引用关系维护
CREATE TABLE t_workflow_reference
(
    `f_id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_workflow_id`     VARCHAR(64) NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_workflow_ref_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '被引用工作流ID',
    `f_robot_id`           varchar(64)   NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_is_deleted`      int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                 `idx_robot_id` (`f_robot_id`),
    KEY                 `idx_workflow_id` (`f_workflow_id`),
    KEY                 `idx_workflow_reference_id` (`f_workflow_ref_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流引用工作流的关联表';


-- 工作流和知识型的引用关系维护
CREATE TABLE `t_workflow_ref_knowledge`
(
    `f_id`          bigint                          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_workflow_id` VARCHAR(64)                     NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_node_id`     VARCHAR(64)                     NOT NULL DEFAULT '' COMMENT '工作流中的NodeID',
    `f_biz_id`      VARCHAR(64)                     NOT NULL DEFAULT '' COMMENT '数据业务ID', -- f_doc_id / f_qa_id / f_tag_id，当为TAG时标签Id（label_biz_id）
    `f_label_id`    VARCHAR(64)                     NOT NULL DEFAULT '' COMMENT '标签Id,应用TAG有效',
    `f_robot_id`    VARCHAR(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_type`        enum ('ALL', 'DOC', 'QA', 'TAG') NOT NULL DEFAULT 'ALL' COMMENT '类型 ALL: 全部知识|DOC:文档｜QA：问答（默认是全部）｜TAG：标签',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time` datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY `idx_ref_id` ( `f_robot_id`,`f_workflow_id`, `f_node_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作流关联知识型文档/问答/标签表';


-- 工作流和参数的引用关系维护
CREATE TABLE t_workflow_parameter
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_robot_id`       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_workflow_id`    VARCHAR(64) NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_node_id`        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '工作流中的NodeID',
    `f_node_name`      varchar(64) NOT NULL DEFAULT '' COMMENT '工作流中的Node名称',
    `f_parameter_id`   VARCHAR(64) NOT NULL DEFAULT '' COMMENT '参数ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int         NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                `idx_workflow_parameter_id` (`f_workflow_id`, `f_parameter_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流参数的关联表';


-- 参数表
CREATE TABLE t_parameter
(
    `f_id`             bigint                                   NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_parameter_id`   VARCHAR(64)                              NOT NULL DEFAULT '' COMMENT '参数ID',
    `f_parameter_name` VARCHAR(64)                              NOT NULL DEFAULT '' COMMENT '参数名称',
    `f_parameter_desc` VARCHAR(2000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述信息',
    `f_parameter_type` VARCHAR(32)                              NOT NULL DEFAULT '' COMMENT '参数类型，string等',
    `f_correct_examples` VARCHAR(10240)                          NOT NULL DEFAULT  '' COMMENT '正确示例',
    `f_incorrect_examples` VARCHAR(2048)                        NOT NULL DEFAULT  '' COMMENT '错误示例',
    `f_robot_id`       VARCHAR(64)                              NOT NULL DEFAULT '' COMMENT '应用id; ',
    `f_uin`            VARCHAR(32)                              NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32)                              NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_release_status` enum ('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_deleted`     int                                      NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_create_time`    datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (f_id),
    KEY                `idx_parameter_id` (`f_parameter_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '参数表';

CREATE TABLE `t_vector_group`
(
    `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_vector_group_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '向量组group',
    `f_vector_group_type` enum('sandbox', 'prod') COLLATE utf8mb4_bin NOT NULL DEFAULT 'sandbox' COMMENT '环境区分：sandbox-沙箱环境， prod-正式环境',
    `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_save_type` enum('workflow','entry') COLLATE utf8mb4_bin NOT NULL COMMENT 'group类型',
    `f_embedding_mode_name` varchar(128) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '模型名称',
    `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '词条向量组与机器人关系表';

CREATE TABLE `t_vector_store`
(
    `f_id`                   bigint                                                          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_biz_id`               varchar(64)                                                     NOT NULL DEFAULT '' COMMENT '数据业务ID', -- f_workflow_id / f_example_id
    `f_robot_id`             varchar(64) COLLATE utf8mb4_bin                                 NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_type`                 enum ('FLOW_NAME', 'EXAMPLE') NOT NULL DEFAULT 'FLOW_NAME' COMMENT '类型',
    `f_content`              varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
    `f_embedding_model`      varchar(64) NOT NULL DEFAULT '' COMMENT 'embedding 模型名称',
    `f_vector`               blob                                                            NOT NULL COMMENT '特征向量',
    `f_create_time`          datetime                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`          datetime                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `f_is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `idx_vector_id` (`f_biz_id`, `f_robot_id`, `f_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='特征向量';


CREATE TABLE `t_feedback_reason`
(
    `f_id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `f_feedback_id` bigint unsigned DEFAULT NULL COMMENT '反馈原因ID',
    `f_reason`      VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '反馈原因',
    `f_robot_id`    VARCHAR(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_is_deleted`  tinyint                                                       NOT NULL DEFAULT '0' COMMENT '软删除标记',
    `f_uin`         VARCHAR(32)                                                   NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`     VARCHAR(32)                                                   NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_create_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY             `idx_reason` (`f_reason`),
    KEY             `idx_bot_reason` (`f_robot_id`,`f_reason`),
    KEY             `idx_feedback_id` (`f_feedback_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工作流反馈原因';

CREATE TABLE `t_feedback_record`
(
    `f_id`               bigint unsigned NOT NULL AUTO_INCREMENT,
    `f_workflow_id`      VARCHAR(64) COLLATE utf8mb4_bin          NOT NULL DEFAULT '' COMMENT '正确任务流程的意图ID',
    `f_biz_id`           bigint unsigned DEFAULT '0' COMMENT '反馈信息业务ID',
    `f_corp_id`          bigint DEFAULT '0' COMMENT '企业ID',
    `f_staff_id`         bigint DEFAULT '0' COMMENT '员工ID',
    `f_robot_id`         VARCHAR(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
    `f_session_id`       VARCHAR(64) COLLATE utf8mb4_general_ci   NOT NULL DEFAULT '' COMMENT '会话ID',
    `f_record_id`        VARCHAR(64) COLLATE utf8mb4_general_ci   NOT NULL DEFAULT '' COMMENT '会话消息记录ID',
    `f_question`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题',
    `f_desc`             longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细描述',
    `f_answer`           longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复答案',
    `f_node_id`          VARCHAR(64) COLLATE utf8mb4_bin          NOT NULL DEFAULT '' COMMENT '正确任务流程的节点ID',
    `f_tapd`             VARCHAR(1024) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'tapd链接',
    `f_status`           tinyint                                  NOT NULL DEFAULT '0' COMMENT '处理状态,0:已提交，1：定位问题中，2：处理中，3：排期优化中，9:拒绝，10-已处理完成',
    `f_reject_reason` VARCHAR(4096) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝理由',
    `f_optimized_result` VARCHAR(1024) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优化结果',
    `f_create_time`      datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`      datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `f_is_deleted`       tinyint                                  NOT NULL DEFAULT '0' COMMENT '软删除标记',
    `f_uin`              VARCHAR(32)                              NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`          VARCHAR(32)                              NOT NULL DEFAULT '' COMMENT '子用户ID',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `uk_business_id` (`f_biz_id`) USING BTREE,
    KEY                  `idx_uin_id_bot_biz_id` (`f_sub_uin`,`f_robot_id`,`f_create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工作流程调试反馈记录';

-- 发布相关 halelv begin ---

CREATE TABLE `t_sync_task`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `user_id`       varchar(80)                                                   NOT NULL DEFAULT '' COMMENT '用户ID, 登录用户唯一标识',
    `user_name`     varchar(255)                                                  NOT NULL COMMENT '操作用户名称, 显示使用',
    `scene`         varchar(80)                                                   NOT NULL COMMENT '使用场景, 电话客服, 文本客服',
    `robot_id`      varchar(100)                                                  NOT NULL COMMENT '机器人 id',
    `task_id`       varchar(100)                                                  NOT NULL COMMENT '任务 id',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`        int                                                           NOT NULL COMMENT '状态, 新任务, 同步中, 同步成功, 同步失败',
    `success_count` int                                                           NOT NULL DEFAULT '0',
    `failed_count`  int                                                           NOT NULL DEFAULT '0',
    `done_time`     datetime                                                               DEFAULT NULL COMMENT '结束时间',
    `session_id`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '此次记录产生的 session id',
    `server`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作 server, 显示使用',
    `work_ip`       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '操作 ip, 显示使用',
    `note`          varchar(1000)                                                          DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY             `task_id` (`task_id`),
    KEY             `create_time` (`create_time`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发布任务记录';

CREATE TABLE `t_sync_task_log`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `robot_id`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '机器人',
    `sync_task_id`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对应的任务记录 主键 ID',
    `session_id`    varchar(100)                                                  NOT NULL DEFAULT '',
    `type`          varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '任务: TASK, 对比 DIFF',
    `log_content`   longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志内容',
    `error_message` varchar(1000)                                                          DEFAULT '',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `server`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
    `work_ip`       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    KEY `idx_robot_id_sync_task_id` (`robot_id`, `sync_task_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发布操作日志';

-- 发布相关 halelv done ---

-- 导入/导出 start ---
CREATE TABLE `t_export_file` (
  `f_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `f_export_id` bigint NOT NULL COMMENT '导出业务ID',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_status` tinyint NOT NULL COMMENT '状态 0 导出中 1 导出成功 2 导出失败',
  `f_cos_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'cos文件地址',
  `f_file_size` bigint NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `f_type` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '下载类型：flow：任务流程',
  `f_platform` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '导出平台：op：op平台导出',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_params` varchar(2048) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '下载传参',
  `f_is_deleted` tinyint NOT NULL COMMENT '是否删除',
  PRIMARY KEY (`f_id`),
  KEY `idx_export_id` (`f_export_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '文件下载记录';

CREATE TABLE `t_workflow_import` (
  `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `f_import_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '导入id',
  `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人id',
  `f_parent_import_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '父导入id',
  `f_params` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '参数',
  `f_status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0:待处理，1：处理中，2：已处理，3：处理失败',
  `f_message` varchar(5120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `f_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主用户ID',
  `f_sub_uin` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '子用户ID',
  `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_final_params` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '最终参数',
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `idx_import_id` (`f_import_id`),
  KEY `idx_robot_parent_importid` (`f_robot_id`, `f_parent_import_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流导入任务';
-- 导入/导出 end ---