-- 工作流引用插件工具关系维护
CREATE TABLE t_workflow_plugin
(
    `f_id`              bigint          NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_workflow_id`     VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_node_id`         VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '工作流中的NodeID',
    `f_plugin_type`     VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '插件类型',
    `f_plugin_id`       VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '插件ID',
    `f_tool_id`         VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '工具ID',
    `f_robot_id`        VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_is_deleted`      int             NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                 `idx_robot_id` (`f_robot_id`),
    KEY                 `idx_plugin_id` (`f_plugin_id`),
    KEY                 `idx_tool_id` (`f_tool_id`),
    KEY                 `idx_plugin_type_id` (`f_plugin_type`),
    KEY                 `idx_workflow_node_id` (`f_workflow_id`,`f_node_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流引用插件工具的关联表';

-- 增加一个字段，表示引用的是输入参数的名称
ALTER TABLE `t_workflow_ref_knowledge`
    ADD COLUMN `f_reference_variable_name` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '标签引用变量名称' AFTER `f_label_id`;

-- 增加变量描述和变量类型两个字段
ALTER TABLE `t_var`
    ADD COLUMN `f_var_desc` varchar(2000) COLLATE utf8mb4_bin NOT NULL DEFAULT '-' COMMENT '变量描述' AFTER `f_var_name`;

ALTER TABLE `t_var`
    ADD COLUMN `f_var_type` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT 'STRING' COMMENT '变量类型' AFTER `f_var_name`;

-- 工作流和工作流的引用关系维护
DROP TABLE IF EXISTS t_workflow_reference;
CREATE TABLE t_workflow_reference
(
    `f_id`              bigint          NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_workflow_id`     VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_node_id`         VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '工作流中的NodeID',
    `f_workflow_ref_id` VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '被引用工作流ID',
    `f_robot_id`        varchar(64)     NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_is_deleted`      int             NOT NULL DEFAULT 0 COMMENT '是否删除',
    `f_create_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    KEY                 `idx_robot_id` (`f_robot_id`),
    KEY                 `idx_workflow_node_id` (`f_workflow_id`,`f_node_id`),
    KEY                 `idx_workflow_reference_id` (`f_workflow_ref_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流引用工作流的关联表';

-- halelv PDL发布

CREATE TABLE `t_pdl_sync_task`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `user_id`       varchar(80)                                                   NOT NULL DEFAULT '' COMMENT '用户ID, 登录用户唯一标识',
    `user_name`     varchar(255)                                                  NOT NULL COMMENT '操作用户名称, 显示使用',
    `scene`         varchar(80)                                                   NOT NULL COMMENT '使用场景, 电话客服, 文本客服',
    `robot_id`      varchar(100)                                                  NOT NULL COMMENT '机器人 id',
    `task_id`       varchar(100)                                                  NOT NULL COMMENT '任务 id',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`        int                                                           NOT NULL COMMENT '状态, 新任务, 同步中, 同步成功, 同步失败',
    `success_count` int                                                           NOT NULL DEFAULT '0',
    `failed_count`  int                                                           NOT NULL DEFAULT '0',
    `done_time`     datetime                                                               DEFAULT NULL COMMENT '结束时间',
    `session_id`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '此次记录产生的 session id',
    `server`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作 server, 显示使用',
    `work_ip`       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '操作 ip, 显示使用',
    `note`          varchar(1000)                                                          DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY             `task_id` (`task_id`),
    KEY             `create_time` (`create_time`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发布任务记录';

CREATE TABLE `t_pdl_sync_task_log`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `robot_id`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '机器人',
    `sync_task_id`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对应的任务记录 主键 ID',
    `session_id`    varchar(100)                                                  NOT NULL DEFAULT '',
    `type`          varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '任务: TASK, 对比 DIFF',
    `log_content`   longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志内容',
    `error_message` varchar(1000)                                                          DEFAULT '',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `server`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
    `work_ip`       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    KEY `idx_robot_id_sync_task_id` (`robot_id`, `sync_task_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发布操作日志';

CREATE TABLE `t_workflow_pdl`
(
    `f_id`                 bigint                                               NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_pdl_id`      	   varchar(64)                                          NOT NULL DEFAULT '' COMMENT 'PDL唯一ID',
    `f_workflow_id`        varchar(64)                                          NOT NULL DEFAULT '' COMMENT '工作流ID',
    `f_workflow_name`      varchar(255)                                         NOT NULL DEFAULT '' COMMENT '工作流名称',
    `f_robot_id`           varchar(64)                                          NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_flow_state`         varchar(64)                                          NOT NULL DEFAULT 'DRAFT' COMMENT '工作流PDL状态：转换中、转换失败、待调试',
    `f_version`            bigint unsigned                                      NOT NULL DEFAULT '0' COMMENT '版本(用于乐观锁)',
    `f_dialog_json_enable` mediumtext                                           NOT NULL COMMENT '启用状态下的节点信息',
    `f_parameter` 		   text                                           		NOT NULL COMMENT '参数',
    `f_pdl_content`        mediumtext                                           NOT NULL COMMENT 'PDL 字段内容',
    `f_tools_info`         mediumtext                                           NOT NULL COMMENT 'PDL 对应的APIInfos',
    `f_user_constraints`   text                                                 NOT NULL COMMENT 'PDL 的约束',
    `f_staff_id`           bigint unsigned                                      NOT NULL DEFAULT '0' COMMENT '员工ID',
    `f_release_status`     enum ('UNPUBLISHED','PUBLISHING','PUBLISHED','FAIL') NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
    `f_is_enable`          tinyint                                              NOT NULL DEFAULT '0' COMMENT '是否启用, 1: 启用，0：禁用',
    `f_is_deleted`         int                                                  NOT NULL DEFAULT '0' COMMENT '是否删除',
    `f_action`             enum ('INSERT','UPDATE','DELETE')                    NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
    `f_fail_reason`        varchar(150)                                         NOT NULL DEFAULT '' COMMENT '转换失败的原因',
    `f_pdl_time`           datetime                                             NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'PDL转换时间',
    `f_create_time`        datetime                                             NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`        datetime                                             NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `idx_pdl_id` (`f_pdl_id`),
    KEY `idx_workflow_id` (`f_workflow_id`),
    KEY `idx_robot_workflow_id` (`f_robot_id`,`f_workflow_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT ='工作流PDL';

CREATE TABLE `t_pdl_history` (
    `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_pdl_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'PDL唯一ID',
    `f_pdl_version` varchar(100) NOT NULL DEFAULT '' COMMENT '版本',
    `f_robot_id` varchar(100) NOT NULL DEFAULT '' COMMENT '机器人ID',
    `f_dialog_json_enable` mediumtext COMMENT '启用状态下的节点信息',
    `f_parameter` text COMMENT '参数',
    `f_pdl_content` mediumtext COMMENT 'PDL 字段内容',
    `f_tools_info` mediumtext COMMENT 'PDL 对应的APIInfos',
    `f_user_constraints` text COMMENT 'PDL 的约束',
    `f_is_deleted` bigint NOT NULL DEFAULT 0 COMMENT '0未删除 1已删除',
    `f_publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
    `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PDL发布记录表';


-- leo 添加索引
CREATE INDEX idx_robot_save_type ON t_vector_group(f_robot_id, f_save_type);

CREATE INDEX idx_robot_id_workflow_id ON t_workflow_example(f_robot_id, f_workflow_id);

CREATE INDEX idx_parameter_id ON t_workflow_parameter(f_parameter_id);

CREATE INDEX idx_robot_workflow_id ON t_workflow (f_robot_id, f_workflow_id);




