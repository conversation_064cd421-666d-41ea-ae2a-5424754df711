-- 存自定义模型与工作流关联关系
CREATE TABLE `t_workflow_custom_model` (
        `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
        `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '应用ID',
        `f_workflow_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '工作流ID',
        `f_node_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '工作流中的NodeID',
        `f_node_type` varchar(64) COLLATE utf8mb4_bin NOT NULL  DEFAULT '' COMMENT '工作流中的节点类型',
        `f_model_name` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '模型名称',
        `f_staff_id` bigint NOT NULL COMMENT '员工ID',
        `f_corp_id` bigint NOT NULL COMMENT '企业ID',
        `f_release_status` enum('UNPUBLISHED', 'PUBLISHING', 'PUBLISHED', 'FAIL') COLLATE utf8mb4_bin NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '状态：未发布、发布中、已发布、发布失败',
        `f_action`         enum ('INSERT', 'UPDATE', 'DELETE') NOT NULL DEFAULT 'INSERT' COMMENT '执行的动作：新增、更新、删除',
        `f_is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
        `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`f_id`),
        KEY `idx_corp_id` (`f_corp_id`),
        KEY `idx_robot_workflow` (`f_robot_id`, `f_workflow_id`),
        KEY `idx_model_name` (`f_model_name`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流与自定义模型关联表';

-- PDL快照版本表
CREATE TABLE `t_pdl_version`
(
     `f_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
     `f_pdl_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'PDL唯一ID',
     `f_pdl_snapshot_version` bigint unsigned NOT NULL DEFAULT '0' COMMENT 'pdl快照版本',
     `f_workflow_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '工作流ID',
     `f_robot_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '机器人ID',
     `f_workflow_name` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '工作流名称',
     `f_dialog_json_enable` mediumtext COMMENT '画布快照',
     `f_parameter` text COMMENT '参数',
     `f_pdl_content` mediumtext COMMENT '画布转换PDL的结果（修改前）',
     `f_tools_info` mediumtext COMMENT 'PDL对应的APIInfos（修改前）',
     `f_user_constraints` text COMMENT 'PDL的约束（修改前）',
     `f_pdl_content_modified` mediumtext COMMENT '修改后的pdl内容',
     `f_tools_info_modified` mediumtext COMMENT '修改后的APIInfos',
     `f_user_constraints_modified` text COMMENT '修改后的约束',
     `f_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `f_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`f_id`),
     UNIQUE KEY `idx_pdl_id_version` (`f_pdl_id`, `f_pdl_snapshot_version`),
     KEY `idx_robot_id` (`f_robot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PDL快照版本表';

-- pdl表添加当前pdl快照版本
ALTER TABLE t_workflow_pdl
    ADD COLUMN `f_pdl_snapshot_version`  bigint unsigned NOT NULL DEFAULT '0' COMMENT '当前pdl快照版本' AFTER `f_pdl_id`;

-- 处理慢查询
alter table t_workflow_import add index index_pi_id(`f_parent_import_id`);