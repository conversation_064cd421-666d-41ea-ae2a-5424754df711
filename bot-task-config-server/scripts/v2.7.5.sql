-- sandbox 和 prod都要更新
-- tapd：https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121661417
ALTER TABLE t_parameter ADD COLUMN f_custom_ask TEXT COLLATE utf8mb4_bin COMMENT '自定义回复话术';
ALTER TABLE t_parameter ADD COLUMN f_custom_ask_enable TINYINT(1) DEFAULT 0 COMMENT '是否启用自定义话术（1=启用，0=禁用）';

-- 工作流-新手引导展示状态 注： 只需要在sandbox中创建， prod 不需要创建
-- 开发环境： db_llm_workflow_dev
-- 测试环境： db_llm_workflow_test
-- 预发布环境： db_llm_workflow_pre_release
-- 生产环境： db_llm_workflow_formal
CREATE TABLE t_workflow_guide
(
    `f_id`             bigint      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_uin`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`        VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_key`            VARCHAR(32) NOT NULL DEFAULT '' COMMENT '新手引导类型Key，如 guide, tip, tipKnown',
    `f_viewed`         tinyint     NOT NULL DEFAULT 0 COMMENT '是否看过新手引导; 0: 没看过; 1: 已看过',
    `f_is_deleted`     tinyint     NOT NULL DEFAULT 0 COMMENT '是否删除; 0: 未删除; 1: 已删除',
    `f_create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `uniq_uin_sub_uin_key` (`f_uin`, `f_sub_uin`, `f_key`) COMMENT '确保用户+引导类型唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流-新手引导展示状态';


ALTER TABLE `t_workflow_pdl`
MODIFY COLUMN `f_fail_reason` varchar(1024) NOT NULL DEFAULT '' COMMENT '转换失败的原因';

-- 接入安灯，修改相关字段 workflow库
ALTER TABLE t_feedback_record
    ADD COLUMN f_andon_ticket VARCHAR(64) NOT NULL DEFAULT '' COMMENT '安灯工单ID',
ADD COLUMN f_andon_url VARCHAR(256) NOT NULL DEFAULT '' COMMENT '安灯URL';