// bot-task-config-server
//
// @(#)app-imp.go  星期五, 五月 31, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/app"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// GetAppShareURL 获取分享链接地址
func (imp TaskConfigImp) GetAppShareURL(ctx context.Context, req *KEP.GetAppShareURLReq) (
	*KEP.GetAppShareURLResp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "GetAppShareURLReq", req)

	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetAppShareURL|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := app.GetAppShareURL(ctx, req)
		clues.AddTrackData(ctx, "GetAppShareURLResp", req)
		log.Infof("RESP|GetAppShareURL|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err,
			time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetAppShareURL|%s|%v", sid, err0)
	return nil, err0
}

// GetAppChatInputNum 通过应用模型获取输入字符限制
func (imp TaskConfigImp) GetAppChatInputNum(ctx context.Context, req *KEP.GetAppChatInputNumReq) (
	*KEP.GetAppChatInputNumRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "GetAppChatInputNum", req)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetAppChatInputNum|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		rsp, err := app.GetAppChatInputNum(ctx, req)
		clues.AddTrackData(ctx, "GetAppChatInputNumRsp", rsp)
		log.Infof("RESP|GetAppChatInputNum|%s|%v|%s|%v|%s", sid, rsp, clues.GetTrackDataJSON(ctx), err,
			time.Since(t0))
		return rsp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetAppChatInputNum|%s|%v", sid, err0)
	return nil, err0
}

// ClearAppFlowResource 清理删除的应用资源
func (imp TaskConfigImp) ClearAppFlowResource(ctx context.Context,
	req *KEP_WF.ClearAppFlowResourceReq) (*KEP_WF.ClearAppFlowResourceRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ClearAppFlowResource|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := app.ClearAppFlowResource(ctx, req)
		log.Infof("ClearAppFlowResource|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ClearAppFlowResource|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteAppResource 删除应用的时候，删除应用下数据
func (imp TaskConfigImp) DeleteAppResource(ctx context.Context,
	req *KEP_WF.DeleteAppResourceReq) (*KEP_WF.DeleteAppResourceRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteAppResource|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := app.DeleteAppResource(ctx, req)
		log.Infof("DeleteAppResource|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DeleteAppResource|RESP|%s|%v", sid, err0)
	return nil, err0
}
