// bot-task-config-server
//
// @(#)category-service.go  星期五, 十二月 15, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/category"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"github.com/asaskevich/govalidator"
)

// CreateCategory 创建分类
func (imp TaskConfigImp) CreateCategory(ctx context.Context, req *KEP.CreateCategoryReq) (*KEP.CreateCategoryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CreateCategory|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := category.CreateCategory(ctx, req)
		log.Infof("CreateCategory|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CreateCategory|RESP|%s|%v", sid, err0)
	return nil, err0
}

// UpdateCategory 更新分类
func (imp TaskConfigImp) UpdateCategory(ctx context.Context, req *KEP.UpdateCategoryReq) (*KEP.UpdateCategoryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateCategory|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := category.UpdateCategory(ctx, req)
		log.Infof("UpdateCategory|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CreateCategory|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteCategory 删除分类
func (imp TaskConfigImp) DeleteCategory(ctx context.Context, req *KEP.DeleteCategoryReq) (*KEP.DeleteCategoryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteCategory|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := category.DeleteCategory(ctx, req)
		log.Infof("DeleteCategory|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DeleteCategory|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListCategory 分类列表&过滤
func (imp TaskConfigImp) ListCategory(ctx context.Context, req *KEP.ListCategoryReq) (*KEP.ListCategoryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListCategory|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := category.ListCategory(ctx, req)
		log.Infof("ListCategory|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListCategory|RESP|%s|%v", sid, err0)
	return nil, err0
}
