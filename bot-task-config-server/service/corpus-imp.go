// bot-task-config-server
//
// @(#)corpus-imp.go  星期一, 四月 08, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/corpus"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/asaskevich/govalidator"
)

// CreateExample 创建示例问法
func (imp TaskConfigImp) CreateExample(ctx context.Context, req *KEP.CreateExampleReq) (*KEP.CreateExampleRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|CreateExample|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := corpus.CreateExample(ctx, req)
		log.Infof("RESP|CreateExample|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|CreateExample|%s|%v", sid, err0)
	return nil, err0
}

// GetExampleList 获取示例问法列表
func (imp TaskConfigImp) GetExampleList(ctx context.Context, req *KEP.GetExampleListReq) (*KEP.GetExampleListRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetExampleList|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := corpus.GetExampleList(ctx, req)
		log.Infof("RESP|GetExampleList|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetExampleList|%s|%v", sid, err0)
	return nil, err0
}

// UpdateExample 更新示例问法
func (imp TaskConfigImp) UpdateExample(ctx context.Context, req *KEP.UpdateExampleReq) (*KEP.UpdateExampleRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|UpdateExample|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := corpus.UpdateExample(ctx, req)
		log.Infof("RESP|UpdateExample|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|UpdateExample|%s|%v", sid, err0)
	return nil, err0
}

// DeleteExample 删除示例问法
func (imp TaskConfigImp) DeleteExample(ctx context.Context, req *KEP.DeleteExampleReq) (*KEP.DeleteExampleRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|DeleteExample|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := corpus.DeleteExample(ctx, req)
		log.Infof("RESP|DeleteExample|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|DeleteExample|%s|%v", sid, err0)
	return nil, err0
}
