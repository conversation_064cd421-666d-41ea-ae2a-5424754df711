/*
 * 2024-12-25
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package migrator

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// SimpleWorkflow ...
type SimpleWorkflow struct {
	WorkflowID    string `gorm:"column:f_workflow_id"`    // 工作流ID
	WorkflowName  string `gorm:"column:f_workflow_name"`  // 工作流名称
	WorkflowState string `gorm:"column:f_flow_state"`     // 工作流状态： 工作流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改;
	RobotId       string `gorm:"column:f_robot_id"`       // 机器人ID
	ReleaseStatus string `gorm:"column:f_release_status"` // 状态：未发布、发布中、已发布、发布失败
	IsEnable      bool   `gorm:"column:f_is_enable"`      // 生产环境是否启用
}

// GetSimpleWorkflowList ...
func GetSimpleWorkflowList(ctx context.Context, robotId, env string, currentPage,
	pageSize int) ([]*SimpleWorkflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var simpleWfs []*SimpleWorkflow
	offset := (currentPage - 1) * pageSize
	if err := db.Table("t_workflow").Select("f_workflow_id, "+
		"f_workflow_name,f_flow_state,f_robot_id,f_release_status,f_is_enable").
		Where("f_robot_id=? AND f_is_deleted=0", robotId).
		Limit(pageSize).Offset(offset).Find(&simpleWfs).Error; err != nil {
		return simpleWfs, err
	}
	return simpleWfs, nil
}

// GetWorkflowExamRobotIDList 待刷示例问法应用ID列表
func GetWorkflowExamRobotIDList(ctx context.Context, env string) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var robotIDs []string
	err := db.
		Model(&entity.WorkflowExample{}).
		Where("f_is_deleted = 0").
		Select("DISTINCT f_robot_id").
		Find(&robotIDs).Error
	if err != nil {
		return robotIDs, err
	}

	return robotIDs, nil
}

// GetWFIdsByAppId ...
func GetWFIdsByAppId(ctx context.Context, appId, env string) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var wfIds []string
	err := db.
		Model(&entity.Workflow{}).
		Where("f_is_deleted = 0 AND f_robot_id=?", appId).
		Select("f_workflow_id").
		Find(&wfIds).Error
	if err != nil {
		return wfIds, err
	}

	return wfIds, nil
}

// GetFlowExampleByEnvWfId 获取应用工作流下面示例问法
func GetFlowExampleByEnvWfId(ctx context.Context, robotId,
	workflowId, env string) ([]*entity.WorkflowExample, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var examples []*entity.WorkflowExample

	if err := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?",
			robotId, workflowId).Scan(&examples).Error; err != nil {
		return examples, err
	}

	return examples, nil
}

// GetWorkflowDetailByEnv ...
func GetWorkflowDetailByEnv(ctx context.Context, workflowID, botBizId, env string) (*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var workflow entity.Workflow
	// 查询符合条件的记录
	err := db.Table("t_workflow").
		Where("f_robot_id = ? AND f_workflow_id = ? AND f_is_deleted = 0", botBizId, workflowID).
		Take(&workflow).Error // 使用 Take 只获取一个记录
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetailByEnv|botId:%s|:workflowID:%s|err:%+v",
			botBizId, workflowID, err)
		return nil, err
	}
	return &workflow, nil
}

// GetWorkflowRobotIDList 待刷数据的机器人的ID列表
func GetWorkflowRobotIDList(ctx context.Context, env string) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var robotIDs []string
	err := db.
		Model(&entity.RobotWorkflow{}).
		Where("f_is_deleted = 0").
		Select("DISTINCT f_robot_id").
		Find(&robotIDs).Error
	if err != nil {
		return robotIDs, err
	}

	return robotIDs, nil
}

// GetWorkflowCount .
//
// SELECT w.*
// FROM t_workflow w
// JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id
// WHERE
//
//	r.f_robot_id = 1849805468254076928
//	AND r.f_is_deleted = 0
//	ORDER BY w.f_update_time DESC
//	LIMIT 1 OFFSET 0
func GetWorkflowCount(ctx context.Context, botBizId, env string) (int, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// Prod环境
	if entity.ProductEnv == env {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var total int64
	// 查询符合条件的记录
	err := db.Table("t_workflow w").
		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
		Where("r.f_robot_id = ? AND r.f_is_deleted = 0", botBizId).
		Count(&total).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowList|botId:%s||err:%+v", botBizId, err)
		return 0, err
	}
	return int(total), nil
}

// GetWorkflowList .
//
// SELECT w.*
// FROM t_workflow w
// JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id
// WHERE
//
//	r.f_robot_id = 1849805468254076928
//	AND r.f_is_deleted = 0
//	ORDER BY w.f_update_time DESC
//	LIMIT 1 OFFSET 0
func GetWorkflowList(ctx context.Context, botBizId, env string, currentPage,
	pageSize int) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// Prod环境
	if entity.ProductEnv == env {
		db = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	offset := (currentPage - 1) * pageSize
	var workflows []*entity.Workflow
	err := db.Table("t_workflow w").
		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
		Where("r.f_robot_id = ? AND r.f_is_deleted = 0", botBizId).
		Order("w.f_update_time DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&workflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowList|botId:%s||err:%+v", botBizId, err)
		return nil, err
	}
	return workflows, nil
}

// GetWorkflowDetail 获取工作流详情
// SELECT w.*
// FROM t_workflow w
// JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id
// WHERE r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0;
//func GetWorkflowDetail(ctx context.Context, workflowID, botBizId string) (*entity.Workflow, error) {
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	var workflow entity.Workflow
//	// 查询符合条件的记录
//	err := db.Table("t_workflow w").
//		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
//		Where("r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0", botBizId, workflowID).
//		Take(&workflow).Error // 使用 Take 只获取一个记录
//	if err != nil {
//		log.ErrorContextf(ctx, "GetWorkflowDetail|botId:%s|:workflowID:%s|err:%+v",
//			botBizId, workflowID, err)
//		return nil, err
//	}
//	return &workflow, nil
//}

// SaveWorkflow 刷数据用的保存工作流（评测库）
func SaveWorkflow(ctx context.Context, workflow *entity.Workflow) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
		Updates(map[string]interface{}{
			"f_dialog_json_draft": workflow.DialogJsonDraft,
			"f_update_time":       workflow.UpdateTime,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveWorkflow|err:%s", err)
		return err
	}
	return nil
}

// SaveWorkflowProd 刷数据用的保存工作流（正式库）
func SaveWorkflowProd(ctx context.Context, workflow *entity.Workflow) error {
	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
		Updates(map[string]interface{}{
			"f_dialog_json_draft": workflow.DialogJsonDraft,
			"f_update_time":       workflow.UpdateTime,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveWorkflowProd|err:%s", err)
		return err
	}
	return nil
}

// SaveWorkflowEnable 刷数据用的保存工作流（评测库）
func SaveWorkflowEnable(ctx context.Context, workflow *entity.Workflow) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
		Updates(map[string]interface{}{
			"f_dialog_json_enable": workflow.DialogJsonEnable,
			"f_update_time":        workflow.UpdateTime,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveWorkflowEnable|err:%s", err)
		return err
	}
	return nil
}

// SaveWorkflowEnableProd 刷数据用的保存工作流（正式库）
func SaveWorkflowEnableProd(ctx context.Context, workflow *entity.Workflow) error {
	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
		Updates(map[string]interface{}{
			"f_dialog_json_enable": workflow.DialogJsonEnable,
			"f_update_time":        workflow.UpdateTime,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "SaveWorkflowEnableProd|err:%s", err)
		return err
	}
	return nil
}

//// SaveWorkflowDraftAndEnable 刷数据用的保存工作流（评测库）
//func SaveWorkflowDraftAndEnable(ctx context.Context, workflow *entity.Workflow) error {
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
//		Updates(map[string]interface{}{
//			"f_dialog_json_draft":  workflow.DialogJsonDraft,
//			"f_dialog_json_enable": workflow.DialogJsonEnable,
//			"f_update_time":        workflow.UpdateTime,
//		}).Error; err != nil {
//		log.ErrorContextf(ctx, "SaveWorkflowDraftAndEnable|err:%s", err)
//		return err
//	}
//	return nil
//}
//
//// SaveWorkflowDraftAndEnableProd 刷数据用的保存工作流（正式库）
//func SaveWorkflowDraftAndEnableProd(ctx context.Context, workflow *entity.Workflow) error {
//	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
//	if err := db.Model(&entity.Workflow{}).Where("f_workflow_id = ?", workflow.WorkflowID).
//		Updates(map[string]interface{}{
//			"f_dialog_json_draft":  workflow.DialogJsonDraft,
//			"f_dialog_json_enable": workflow.DialogJsonEnable,
//			"f_update_time":        workflow.UpdateTime,
//		}).Error; err != nil {
//		log.ErrorContextf(ctx, "SaveWorkflowDraftAndEnableProd|err:%s", err)
//		return err
//	}
//	return nil
//}
