package embedding

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
)

// 实现embedding特征升级灰度主逻辑，以应用为维度
func Migrate(ctx context.Context, appID string, modelName string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	vdb := vdao.NewDao()
	// 1. 标记工作流embedding向量正在升级中
	err := vdb.MarkWorkflowVectorUpgrade(ctx, db, appID, entity.SaveWorkflowType)
	if err != nil {
		return err
	}
	// 2. 获取模型名对应的配置项
	embeddingModelConfig := getUsingVectorModelInfo(ctx, modelName)
	if embeddingModelConfig == nil {
		log.WarnContextf(ctx, "embedding|Migrate|getUsingVectorModelInfo|%s not found", modelName)
		return fmt.Errorf("model %s is not found in config", modelName)
	}
	// 3. 获取embedding向量库分组
	oldVectorGroupInfos, err := getWorkflowVectorGroups(ctx, db, appID)
	if err != nil {
		log.WarnContextf(ctx, "embedding|Migrate|get workflow vector group err:%v", err)
		return err
	}
	// 4. 构造新的embedding向量库分组
	sandboxGroupID, prodGroupID, newVectorGroupInfos := getNewWorkflowVectorGroups(ctx, appID,
		oldVectorGroupInfos, embeddingModelConfig)
	// 5. 调用向量库接口存储新的向量分组
	_, _, err = vdb.CreateBotWorkflowGroup(ctx, appID, newVectorGroupInfos)
	if err != nil {
		log.WarnContextf(ctx, "embedding|Migrate|CreateBotWorkflowGroup Failed err:%v", err)
		return err
	}
	// 6. 处理评测库与正式库
	envs := []string{entity.SandboxEnv, entity.ProductEnv}
	for _, env := range envs {
		groupID := sandboxGroupID
		if env == entity.ProductEnv {
			groupID = prodGroupID
		}
		totalCount, err := migrator.GetWorkflowCount(ctx, appID, entity.SandboxEnv)
		if err != nil {
			log.WarnContextf(ctx, "embedding|Migrate|GetWorkflowCount|err:%+v", err)
			return err
		}
		pageSize := 5
		for currentPage := 1; ; currentPage++ {
			wfs, err := migrator.GetWorkflowList(ctx, appID, env, currentPage, pageSize)
			if err != nil {
				log.WarnContextf(ctx, "embedding|Migrate|GetWorkflowList|err:%+v", err)
				return err
			}
			for _, wf := range wfs {
				wfExams, err := migrator.GetFlowExampleByEnvWfId(ctx, appID, wf.WorkflowID, env)
				if err != nil {
					log.WarnContextf(ctx, "embedding|Migrate|GetFlowExampleByEnvWfId|err:%+v", err)
					return err
				}
				err = saveWorkflowToVector(ctx, db, vdb, appID, groupID, wf, wfExams, embeddingModelConfig)
				if err != nil {
					log.WarnContextf(ctx, "embedding|Migrate|SaveWorkflowToVector|err:%+v", err)
					return err
				}
			}
			if currentPage*pageSize >= totalCount {
				break
			}
		}
		// 7. 更新向量库分组信息
		if err = db.Transaction(func(tx *gorm.DB) error {
			// 更新向量库分组ID
			if env == entity.SandboxEnv {
				err = vdb.UpdateWorkflowVectorGroupID(ctx, tx, appID, entity.SaveWorkflowType,
					vdao.WorkflowSandboxGroupInfix, groupID)
			} else {
				err = vdb.UpdateWorkflowVectorGroupID(ctx, tx, appID, entity.SaveWorkflowType,
					vdao.WorkflowProdGroupInfix, groupID)
			}
			if err != nil {
				log.ErrorContextf(ctx, "embedding|Migrate|UpdateWorkflowVectorGroupID failed, err:%v", err)
				return err
			}
			// 通知DM
			var dmErr error
			startInvoke := time.Now()
			for {
				if env == entity.SandboxEnv {
					req := &KEP_WF_DM.UpsertAppToSandboxRequest{
						AppID:                    appID,
						RetrievalWorkflowGroupID: groupID,
						RetrievalWorkflowModel:   embeddingModelConfig.LatestEmbeddingModelName,
					}
					if _, dmErr = proxy.GetWfDmProxy().UpsertAppToSandbox(ctx, req); err != nil {
						log.ErrorContextf(ctx, "embedding|Migrate|UpsertAppToSandboxRequest failed, err:%v", err)
						return err
					}
				} else {
					req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
						AppID:                    appID,
						RetrievalWorkflowGroupID: groupID,
						RetrievalWorkflowModel:   embeddingModelConfig.LatestEmbeddingModelName,
					}
					if _, dmErr = proxy.GetWfDmProxy().ReleaseWorkflowApp(ctx, req); err != nil {
						log.ErrorContextf(ctx, "embedding|Migrate|ReleaseWorkflowAppRequest failed, err:%v", err)
						return err
					}
				}
				if dmErr == nil {
					break
				}
				// 如果调用失败，且已经调用超过半小时，则不再重试，返回报错
				if time.Since(startInvoke) < 30*time.Minute {
					time.Sleep(20 * time.Second)
					continue
				}
				return dmErr
			}
			return nil
		}); err != nil {
			log.WarnContextf(ctx, "embedding|Migrate|MarkWorkflowVectorUpgradeDone|err:%+v", err)
			return err
		}
	}
	// 8. 标记工作流embedding向量升级完成
	err = vdb.MarkWorkflowVectorUpgradeDone(ctx, db, appID, entity.SaveWorkflowType)
	if err != nil {
		return err
	}
	return nil
}

func getUsingVectorModelInfo(ctx context.Context, modelName string) *config.UsingVectorModelInfo {
	vectorGroup := config.GetMainConfig().WorkflowVectorGroup
	info := &config.UsingVectorModelInfo{
		Biz:             vectorGroup.Biz,
		Secret:          vectorGroup.Secret,
		OperationMaxIDs: vectorGroup.OperationMaxIDs,
		WorkflowUseVdb:  vectorGroup.WorkflowUseVdb,

		EmbeddingModelName:       vectorGroup.EmbeddingModelName,
		LatestEmbeddingModelName: modelName,
	}
	eVersionConfig, ok := vectorGroup.EmbeddingVersionControl[modelName]
	if !ok {
		log.ErrorContextf(ctx, "embedding|Migrate|getUsingVectorModelInfo failed, "+
			"modelName:%s not found in config", modelName)
		return nil
	}
	info.GroupSuffix = eVersionConfig.GroupSuffix
	info.WorkflowNameTemplate = eVersionConfig.WorkflowNameTemplate
	info.WorkflowExampleTemplate = eVersionConfig.WorkflowExampleTemplate
	info.WorkflowCombinationTemplate = eVersionConfig.WorkflowCombinationTemplate
	info.WorkflowCombinationExampleNum = eVersionConfig.WorkflowCombinationExampleNum
	return info
}

func getWorkflowVectorGroups(ctx context.Context, db *gorm.DB, appID string) ([]entity.WorkflowVectorGroup, error) {
	// 升级需要的APPID在库中必须有
	var vectorGroupInfos []entity.WorkflowVectorGroup
	err := db.Model(&entity.WorkflowVectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", appID, entity.SaveWorkflowType).
		Find(&vectorGroupInfos).Error
	if err != nil {
		log.WarnContextf(ctx, "get vector group from db by appid %s failed, err:%+v", appID, err)
		return nil, err
	}
	if len(vectorGroupInfos) != 2 {
		log.WarnContextf(ctx, "get vector group from db by appid %s failed, expect 2 groups, but got %d",
			appID, len(vectorGroupInfos))
		return nil, fmt.Errorf("get vector group from db by appid %s failed, expect 2 groups, but got %d",
			appID, len(vectorGroupInfos))
	}
	return vectorGroupInfos, nil
}

func getNewWorkflowVectorGroups(ctx context.Context, appID string, oldWorkflowVectorGroups []entity.WorkflowVectorGroup,
	embeddingModelConfig *config.UsingVectorModelInfo) (sandboxGroupID string, prodGroupID string,
	newWorkflowVectorGroups []entity.WorkflowVectorGroup) {
	newWorkflowVectorGroups = make([]entity.WorkflowVectorGroup, 0)
	sandboxGroupID, prodGroupID = vdao.GetWorkFlowVectorGroupIDStr(ctx, appID, entity.SaveWorkflowType,
		embeddingModelConfig.Biz, embeddingModelConfig.GroupSuffix)
	for _, vectorGroupInfo := range oldWorkflowVectorGroups {
		if vectorGroupInfo.VectorGroupType == vdao.WorkflowSandboxGroupInfix {
			newWorkflowVectorGroups = append(newWorkflowVectorGroups, entity.WorkflowVectorGroup{
				VectorGroupID:     sandboxGroupID,
				VectorGroupType:   vdao.WorkflowSandboxGroupInfix,
				RobotID:           appID,
				SaveType:          entity.SaveWorkflowType,
				EmbeddingModeName: embeddingModelConfig.LatestEmbeddingModelName,
				UIN:               vectorGroupInfo.UIN,
				SubUIN:            vectorGroupInfo.SubUIN,
			})
		}
		if vectorGroupInfo.VectorGroupType == vdao.WorkflowProdGroupInfix {
			newWorkflowVectorGroups = append(newWorkflowVectorGroups, entity.WorkflowVectorGroup{
				VectorGroupID:     prodGroupID,
				VectorGroupType:   vdao.WorkflowSandboxGroupInfix,
				RobotID:           appID,
				SaveType:          entity.SaveWorkflowType,
				EmbeddingModeName: embeddingModelConfig.LatestEmbeddingModelName,
				UIN:               vectorGroupInfo.UIN,
				SubUIN:            vectorGroupInfo.SubUIN,
			})
		}
	}
	return
}

func saveWorkflowToVector(ctx context.Context, db *gorm.DB, vdb vdao.Dao, appID string, embeddingGroupID string,
	wf *entity.Workflow, wfExams []*entity.WorkflowExample, info *config.UsingVectorModelInfo) error {
	// 渲染embedding内容模版
	name := strings.TrimSpace(wf.WorkflowName)
	desc := strings.TrimSpace(wf.WorkflowDesc)
	examStrs := make([]string, 0, info.WorkflowCombinationExampleNum)
	if len(wfExams) > 0 {
		for _, v := range wfExams[0:info.WorkflowCombinationExampleNum] {
			if v != nil {
				examStrs = append(examStrs, v.Example)
			}
		}
	}
	embeddingRenderWorkflow := &entity.EmbeddingRenderWorkflow{
		WorkflowName:        name,
		WorkflowDescription: desc,
		WorkflowExampleList: examStrs,
	}
	wfNameTemplateStr, err := util.Render(ctx, info.WorkflowNameTemplate, embeddingRenderWorkflow)
	if err != nil {
		log.DebugContextf(ctx, "get WorkflowNameTemplate err :%+v", err)
		return err
	}
	wfExamplesDescTemplateStr := ""
	if len(strings.TrimSpace(info.WorkflowCombinationTemplate)) > 0 {
		wfExamplesDescTemplateStr, err = util.Render(ctx, info.WorkflowCombinationTemplate, embeddingRenderWorkflow)
		if err != nil {
			log.DebugContextf(ctx, "get WorkflowCombinationTemplate err :%+v", err)
			return err
		}
	}
	// 构造embedding提取相关配置
	workflowVectorOrg := &entity.WorkflowVectorOrg{
		WorkflowID:                 wf.WorkflowID,
		WorkflowName:               wf.WorkflowName,
		FlowNameExamsDescID:        fmt.Sprintf(entity.FlowNameExamsDescID, wf.WorkflowID),
		WorkflowNameVectorOrg:      wfNameTemplateStr,
		WorkflowDesc:               wf.WorkflowDesc,
		WorkflowState:              wf.WorkflowState,
		RobotId:                    wf.RobotId,
		ReleaseStatus:              wf.ReleaseStatus,
		IsEnable:                   wf.IsEnable,
		FlowNameExamsDescVectorOrg: wfExamplesDescTemplateStr,
		Action:                     entity.ActionInsert,
	}
	appInfo := &vector_db_manager.AppInfo{
		Biz:    info.Biz,
		AppKey: appID,
		Secret: info.Secret,
	}
	dataList := make([]*vector_db_manager.AddVectorReq_Index, 0)
	// 工作流名称embedding
	workflowEmbed, err := vdb.GetWorkflowEmbedding(ctx, db, workflowVectorOrg, appInfo, info.LatestEmbeddingModelName)
	if err != nil {
		return err
	}
	fieldValueWorkflow := &vector_db_manager.FieldValue{
		FieldName:        vdao.FieldWorkflowId,
		FieldType:        vector_db_manager.FieldValue_STRING,
		FieldValueString: wf.WorkflowID,
	}
	fieldValueEnable := &vector_db_manager.FieldValue{
		FieldName:        vdao.FieldWorkflowEnable,
		FieldType:        vector_db_manager.FieldValue_UINT64,
		FieldValueUint64: vdao.GetWfVectorEnableByFlowState(wf.WorkflowState, wf.IsEnable), // 1:启用，0：禁用
	}
	dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
		Id:        wf.WorkflowID,
		Embedding: workflowEmbed[wf.WorkflowID],
		AttributeFields: &vector_db_manager.AttributeFields{
			Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
		},
		EntityId: wf.WorkflowID,
	})
	// 工作流名称、示例问法、工作流描述组合embedding
	if workflowVectorOrg.FlowNameExamsDescVectorOrg != "" {
		workflowNameExamsDescEmbed, err := vdb.GetWorkflowExamDescEmbedding(ctx, db, workflowVectorOrg,
			appInfo, info.LatestEmbeddingModelName)
		if err != nil {
			return err
		}
		if workflowNameExamsDescEmbed != nil {
			dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
				Id:        workflowVectorOrg.FlowNameExamsDescID,
				Embedding: workflowNameExamsDescEmbed[workflowVectorOrg.FlowNameExamsDescID],
				AttributeFields: &vector_db_manager.AttributeFields{
					Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
				},
				EntityId: workflowVectorOrg.FlowNameExamsDescID, // 私有化弃用
			})
		}
	}
	// 示例问法embedding
	if len(wfExams) > 0 {
		wfExamIDs := make([]string, 0)
		wfExamMap := make(map[string]string)
		examVectorOrgs := make([]*entity.WorkflowExampleVectorOrg, 0)
		for _, exam := range wfExams {
			wfExamIDs = append(wfExamIDs, exam.ExampleID)
			wfExampleTemplateStr, err := util.Render(ctx, info.WorkflowExampleTemplate, exam)
			if err != nil {
				log.DebugContextf(ctx, "get WorkflowCombinationTemplate err :%+v", err)
				return err
			}
			wfExamMap[exam.ExampleID] = wfExampleTemplateStr
			examVectorOrgs = append(examVectorOrgs, &entity.WorkflowExampleVectorOrg{
				FlowID:           exam.FlowID,
				ExampleID:        exam.ExampleID,
				Example:          exam.Example,
				ExampleVectorOrg: wfExampleTemplateStr,
				RobotId:          exam.RobotId,
			})
		}
		examplesEmbed, err := vdb.GetWorkflowExampleEmbedding(ctx, db, examVectorOrgs,
			appInfo, info.LatestEmbeddingModelName)
		if err != nil {
			return err
		}
		for _, examIDs := range types.SplitStringSlice(wfExamIDs, info.OperationMaxIDs-2) {
			for _, wfExamID := range examIDs {
				fieldValueExample := &vector_db_manager.FieldValue{
					FieldName:        vdao.FieldExampleId,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: wfExamID,
				}
				fieldValueExampleOrg := &vector_db_manager.FieldValue{
					FieldName:        vdao.FieldExampleValue,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: wfExamMap[wfExamID], // 示例问法原文
				}
				dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
					Id:        wfExamID,                // 示例问法ID
					Embedding: examplesEmbed[wfExamID], // 示例问法向量化数据
					AttributeFields: &vector_db_manager.AttributeFields{
						Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow,
							fieldValueEnable, fieldValueExample, fieldValueExampleOrg},
					},
					EntityId: wf.WorkflowID,
				})
			}
		}
	}
	reqID := fmt.Sprintf("wf:embed:upgrade:%s", appID)
	if err = vdb.AddVectorByBatch(ctx, dataList, appInfo, reqID, embeddingGroupID); err != nil {
		log.ErrorContextf(ctx, "SaveCorpusVector AddVector, err:%v", err)
		return err
	}
	return nil
}
