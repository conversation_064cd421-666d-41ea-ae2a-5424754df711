// bot-task-config-server
//
// @(#)workflow-example.go  星期五, 一月 10, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package v262

import (
	"context"
	"net/http"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/go-comm/encode"
	jsoniter "github.com/json-iterator/go"
)

const yes = "1"

// SyncResult ...
type SyncResult struct {
	Msg      string
	AppBizID string
}

// SyncWorkflowExampleToRdsRsp ...
type SyncWorkflowExampleToRdsRsp struct {
	TraceID    string
	SyncResult *SyncResult
	ErrMsg     string
}

// SyncWorkflowExampleToRds 同步exam到redis
func SyncWorkflowExampleToRds(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	resp := &SyncWorkflowExampleToRdsRsp{}
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|ParseForm|err:%+v", err)
		return
	}
	testStr := r.FormValue("test")
	env := r.FormValue("env")
	if env != entity.SandboxEnv && env != entity.ProductEnv {
		rspErr(w, resp, "env is Sandbox or Product")
		return
	}
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|testStr:%s", testStr)
	test := false
	if testStr == yes {
		test = true
	}
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|r:%+v", r)
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|Form:%+v", r.Form)

	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)

	resp.TraceID = traceID
	appIDs := getExampleRobotIds(ctx, w, r, resp)
	if len(appIDs) == 0 {
		rspErr(w, resp, "appIDs is EMPTY")
		return
	}
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|appIDs:%+v", appIDs)
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		res, err := syncWorkflowExamRdsByAppID(newCtx, appID, test, env)
		if err != nil {
			rspErr(w, resp, err.Error())
			return
		}
		resp.SyncResult = res
	}

	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|resp:%s", string(respStr))
	_, _ = w.Write(respStr)
}

// syncWorkflowExamRdsByAppID ...
func syncWorkflowExamRdsByAppID(ctx context.Context, appId string,
	test bool, env string) (*SyncResult, error) {
	var result = &SyncResult{}
	// 获取exam
	exams, err := getWorkflowExamsByAppId(ctx, env, appId)
	// 获取对应的id
	log.DebugContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|examsLen:%d", len(exams))
	if err != nil {
		log.WarnContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|err:%+v", err)
		result.AppBizID = appId
		result.Msg = err.Error()
		return result, err
	}

	//组装存redis的示例问法
	exampleRedisInfo, err := db.GetBatchWfExamRedisInfo(ctx, exams)
	if err != nil {
		log.WarnContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|err:%+v", err)
		result.AppBizID = appId
		result.Msg = err.Error()
		return result, err
	}
	log.DebugContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|appId:%s|test:%+v|env:%s"+
		"|exampleRedisInfo:%+v", appId, test, env, exampleRedisInfo)
	if !test {
		key := db.GetFlowExampleKey(env, appId)
		// 获取应用下所有的画布Id
		flowIds, err := getFlowIdsByAppId(ctx, appId, env)
		if err != nil {
			log.WarnContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|err:%+v", err)
			result.AppBizID = appId
			result.Msg = err.Error()
			return result, err
		}
		_ = database.GetRedis().HDel(ctx, key, flowIds...).Err()
		err = db.SyncExampleDataToRedis(ctx, env, appId, exampleRedisInfo, []string{})
		if err != nil {
			log.WarnContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|err:%+v", err)
			result.AppBizID = appId
			result.Msg = err.Error()
			return result, err
		}
		result.AppBizID = appId
		result.Msg = "Success"
	} else {
		result.AppBizID = appId
		result.Msg = "test"
	}
	return result, nil
}

// getExampleRobotIds ...
func getExampleRobotIds(ctx context.Context, w http.ResponseWriter, r *http.Request,
	resp *SyncWorkflowExampleToRdsRsp) []string {
	all := r.FormValue("all")
	env := r.FormValue("env")
	log.InfoContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|all:%s", all)
	if all == yes {
		appIDsFromDb, err := getAppIdsByWorkflowExam(ctx, env)
		if err != nil {
			log.ErrorContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|err:%+v", err)
			rspErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}

	appidStr := r.FormValue("appid")
	log.InfoContextf(ctx, "sync-workflow-example| SyncWorkflowExampleToRds|appidStr:%s", appidStr)
	_, err := strconv.ParseUint(appidStr, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "sync-workflow-example|SyncWorkflowExampleToRds|ParseUint|req:%+v, err:%+v", r, err)
		rspErr(w, resp, err.Error())
		return nil
	}
	return []string{appidStr}
}

// getWorkflowExamsByAppId ...
func getWorkflowExamsByAppId(ctx context.Context, env, appId string) ([]*entity.WorkflowExample, error) {
	dbQuery := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		dbQuery = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var exams []*entity.WorkflowExample
	if err := dbQuery.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=?", appId).Scan(&exams).Error; err != nil {
		log.ErrorContextf(ctx, "getAppIdsByWorkflowExam|err:%+v", err)
		return exams, err
	}
	return exams, nil
}

// getFlowIdsByAppId 获取应用下所有flowId
func getFlowIdsByAppId(ctx context.Context, appId, env string) ([]string, error) {
	dbQuery := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		dbQuery = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var flowIds []string
	if err := dbQuery.Table(entity.Workflow{}.TableName()).Select("f_workflow_id").
		Where("f_is_deleted=0 AND f_robot_id=?", appId).Scan(&flowIds).Error; err != nil {
		log.ErrorContextf(ctx, "getFlowIdsByAppId|err:%+v", err)
		return flowIds, err
	}
	return flowIds, nil
}

// getAppIdsByWorkflowExam ...
func getAppIdsByWorkflowExam(ctx context.Context, env string) ([]string, error) {
	dbQuery := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if env == entity.ProductEnv {
		dbQuery = database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	}
	var robotIds []string
	if err := dbQuery.Table(entity.WorkflowExample{}.TableName()).Select("DISTINCT f_robot_id").
		Where("f_is_deleted=0").Scan(&robotIds).Error; err != nil {
		log.ErrorContextf(ctx, "getAppIdsByWorkflowExam|err:%+v", err)
		return robotIds, err
	}
	return robotIds, nil
}

// rspErr ...
func rspErr(w http.ResponseWriter, resp *SyncWorkflowExampleToRdsRsp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}
