/*
 * 2024-12-25
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package v270

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
)

const (
	yes = "1"
)

var step = 1000

// AddEndNode v2.7.0 刷数据： 增加结束节点
// ------------------------------------------------------------------
// 使用说明： 打开trpc-go的admin，增加自定义指令
//
// 1. 在trpc-go.yaml中 开启admin
//  1. 参考资料： https://iwiki.woa.com/p/99485663
//  2. `curl "http://ip:port/cmds"`
//  3. `curl "http://ip:port/version"`
//
// 2. 在服务中注册：
//  1. `import "git.code.oa.com/trpc-go/trpc-go/admin"`
//  2. `admin.HandleFunc("/test", r.s.Test)`
//  3. 七彩石 - trpc-go.yaml 里的 admin里配置端口号
//
// 3. （可选）在节点中：`ss -tunlp`   查看端口和ip 情况
//
// 4. 在节点: `curl -X POST -d "uin=aaa&robot_id=bbb" http://************:8081/test -v`
// ------------------------------------------------------------------
// 具体接口使用说明:
//
//	key : value
//	- all: 1/0; 1(全量)
//	- appid: 机器人ID （对应到t_robot 表中的 business_id）
//
// 如：
// - 刷全量： curl -X POST -d "all=1" http://{ip}:{port}/v270/add-end-node
// - 刷单应用：curl -X POST -d "appid=123456" http://{ip}:{port}/v270/add-end-node
// - 测试(最后不保存，只是跑逻辑）：curl -X POST -d "appid=123456&test=1" http://{ip}:{port}/v270/add-end-node
func AddEndNode(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	//ctx = clues.NewTrackContext(ctx)
	//defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "add-end-node|AddEndNode|ParseForm|err:%+v", err)
		return
	}
	testStr := r.FormValue("test")
	log.InfoContextf(ctx, "add-end-node|AddEndNode|testStr:%s", testStr)
	test := false
	if testStr == yes {
		test = true
	}
	log.InfoContextf(ctx, "add-end-node|AddEndNode|r:%+v", r)
	log.InfoContextf(ctx, "add-end-node|AddEndNode|Form:%+v", r.Form)

	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)
	resp := &AddEndNodeResp{}
	resp.TraceID = traceID
	appIDs := getAppIDs(ctx, w, r, resp)
	if len(appIDs) == 0 {
		respErr(w, resp, "appIDs is EMPTY")
		return
	}

	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		result, err := upgrade(newCtx, appID, test)
		if err != nil {
			log.WarnContextf(ctx, "add-end-node|AddEndNode|upgrade|err:%+v", err)
			respErr(w, resp, err.Error())
			return
		}
		resp.AppBizIDResults = append(resp.AppBizIDResults, result)
	}

	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "add-end-node|AddEndNode|resp:%s", string(respStr))
	_, _ = w.Write(respStr)
}

func getAppIDs(ctx context.Context, w http.ResponseWriter, r *http.Request, resp *AddEndNodeResp) []string {
	all := r.FormValue("all")
	log.InfoContextf(ctx, "add-end-node|getAppIDs|all:%s", all)
	if all == yes {
		appIDsFromDb, err := migrator.GetWorkflowRobotIDList(ctx, entity.SandboxEnv)
		if err != nil {
			log.WarnContextf(ctx, "add-end-node|AddEndNode|GetAppList|err:%+v", err)
			respErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}

	appidStr := r.FormValue("appid")
	log.InfoContextf(ctx, "add-end-node|AddEndNode|appidStr:%s", appidStr)
	_, err := strconv.ParseUint(appidStr, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "add-end-node|AddEndNode|ParseUint|req:%+v, err:%+v", r, err)
		respErr(w, resp, err.Error())
		return nil
	}
	return []string{appidStr}
}

func respErr(w http.ResponseWriter, resp *AddEndNodeResp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}

func upgrade(ctx context.Context, appid string, test bool) (*AppBizIDResult, error) {
	result := &AppBizIDResult{}
	result.AppBizID = appid
	totalCount, err := migrator.GetWorkflowCount(ctx, appid, entity.SandboxEnv)
	if err != nil {
		log.WarnContextf(ctx, "add-end-node|upgrade|GetWorkflowCount|err:%+v", err)
		return result, err
	}
	result.Total = totalCount
	ignoreUpgrade := func(workflow *KEP_WF.Workflow) bool {
		for _, node := range workflow.GetNodes() {
			if node.GetNodeType() == KEP_WF.NodeType_END {
				return true
			}
		}
		return false
	}
	step = 5
	pageSize := step
	for currentPage := 1; ; currentPage++ {
		wfs, err := migrator.GetWorkflowList(ctx, appid, entity.SandboxEnv, currentPage, pageSize)
		if err != nil {
			log.WarnContextf(ctx, "add-end-node|upgrade|GetWorkflowList|err:%+v", err)
			return result, err
		}
		for _, wf := range wfs {
			wfr := WorkflowResult{}
			wfr.WorkflowID = wf.WorkflowID
			wfr.Name = wf.WorkflowName
			startTime := time.Now()
			if len(wf.DialogJsonDraft) == 0 {
				wfr.Msg = "DialogJsonDraft is empty"
				result.WorkflowResults = append(result.WorkflowResults, wfr)
				continue
			}
			workflow, err := protoutil.JsonToWorkflow(wf.DialogJsonDraft)
			if err != nil {
				log.WarnContextf(ctx, "add-end-node|upgrade|JsonToWorkflow|WorkflowID:%s|err:%+v",
					wf.WorkflowID, err)
				//return result, err
				wfr.Msg = fmt.Sprintf("ignore, wrong json|err:%+v", err)
				result.WorkflowResults = append(result.WorkflowResults, wfr)
				continue
			}
			if workflow == nil {
				wfr.Msg = "workflow == nil"
				result.WorkflowResults = append(result.WorkflowResults, wfr)
				continue
			}

			if ignoreUpgrade(workflow) {
				wfr.Msg = "ignore, already has END node"
				result.WorkflowResults = append(result.WorkflowResults, wfr)
				continue
			}
			if len(workflow.GetEdge()) == 0 {
				// 以前开发环境调试初期的一些脏数据，忽略掉
				wfr.Msg = "ignore, Edge is EMPTY"
				result.WorkflowResults = append(result.WorkflowResults, wfr)
				continue
			}
			newJson, err := migratorJson(ctx, workflow)
			if err != nil {
				log.WarnContextf(ctx, "add-end-node|upgrade|migratorJson|WorkflowID:%s|err:%+v",
					wf.WorkflowID, err)
				return result, err
			}
			log.InfoContextf(ctx, "add-end-node|AddEndNode|appid:%s|WorkflowID:%s|oldJson:%s|newJson:%s",
				appid, wf.WorkflowID, wf.DialogJsonDraft, newJson)
			wf.DialogJsonDraft = newJson

			if !test {
				err := migrator.SaveWorkflow(ctx, wf)
				if err != nil {
					log.WarnContextf(ctx, "add-end-node|upgrade|SaveWorkflow|WorkflowID:%s|err:%+v",
						wf.WorkflowID, err)
					return result, err
				}
			}
			elapsed := time.Since(startTime).String()
			wfr.Elapsed = elapsed
			result.WorkflowResults = append(result.WorkflowResults, wfr)
		}
		if currentPage*pageSize >= totalCount {
			break
		}
	}
	return result, nil
}

func migratorJson(ctx context.Context, workflow *KEP_WF.Workflow) (string, error) {
	edges, err := stringToEdgeSlice(workflow.GetEdge())
	if err != nil {
		log.WarnContextf(ctx, "add-end-node|migratorJson|stringToEdgeSlice|WorkflowID:%s|err:%+v",
			workflow.WorkflowID, err)
		return "", err
	}
	endNodeName := "结束节点"
	hasSameNodeName := false
	for _, node := range workflow.GetNodes() {
		if node.GetNodeName() == endNodeName {
			hasSameNodeName = true
		}
		if node.GetNodeType() != KEP_WF.NodeType_ANSWER {
			continue
		}
		node.NextNodeIDs = append(node.NextNodeIDs, EndNodeID)
		edges = append(edges, buildEdge(node.GetNodeID()))
	}
	if hasSameNodeName {
		endNodeName = fmt.Sprintf("%s%s", endNodeName, generateRandomAlpha())
	}
	// 增加一个结束节点
	workflow.Nodes = append(workflow.Nodes, buildEndNode(endNodeName))

	edgeStr, err := edgeSliceToString(edges)
	if err != nil {
		log.WarnContextf(ctx, "add-end-node|migratorJson|edgeSliceToString|WorkflowID:%s|err:%+v",
			workflow.WorkflowID, err)
		return "", err
	}
	workflow.Edge = edgeStr
	return protoutil.WorkflowToJson(workflow)
}

func generateRandomAlpha() string {
	rand.Seed(time.Now().UnixNano())
	// 生成两个随机的 ASCII 码值 (97-122对应a-z)
	firstLetter := rand.Intn(26) + 97
	secondLetter := rand.Intn(26) + 97
	// 将 ASCII 码转换为字符
	return string(rune(firstLetter)) + string(rune(secondLetter))
}

func buildEdge(sourceID string) Edge {
	return Edge{
		Source:       sourceID,
		SourceHandle: buildSourceHandle(sourceID),
		Target:       EndNodeID,
		TargetHandle: buildTargetHandle(EndNodeID),
		Type:         "custom",
		Data: EdgeData{
			ConnectedNodeIsHovering: false,
			Error:                   false,
			IsHovering:              false,
		},
		Id:       buildEdgeID(sourceID),
		Selected: false,
		Animated: false,
	}
}

func stringToEdgeSlice(str string) ([]Edge, error) {
	var edges []Edge
	err := json.Unmarshal([]byte(str), &edges)
	if err != nil {
		return nil, err
	}

	return edges, nil
}

func edgeSliceToString(edges []Edge) (string, error) {
	data, err := json.Marshal(edges)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

func buildEndNode(endNodeName string) *KEP_WF.WorkflowNode {
	return &KEP_WF.WorkflowNode{
		NodeID:   EndNodeID,
		NodeName: endNodeName,
		NodeType: KEP_WF.NodeType_END,
		NodeData: &KEP_WF.WorkflowNode_EndNodeData{
			EndNodeData: &KEP_WF.EndNodeData{},
		},
		NodeUI: NodeUI,
	}
}

// WorkflowResult .
type WorkflowResult struct {
	WorkflowID string `json:"workflow_id"`
	Name       string `json:"name"`
	Msg        string `json:"msg"` //
	Elapsed    string `json:"elapsed"`
}

// AppBizIDResult .
type AppBizIDResult struct {
	AppBizID        string           `json:"app_biz_id"`
	Total           int              `json:"total"`
	WorkflowResults []WorkflowResult `json:"workflow_results"`
}

// AddEndNodeResp .
type AddEndNodeResp struct {
	TraceID         string            `json:"trace_id"`
	AppBizIDResults []*AppBizIDResult `json:"results"`
	ErrMsg          string            `json:"err_msg"`
}
