// bot-task-config-server
//
// @(#)check-workflow-enable.go  星期四, 二月 13, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package v270

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	checkAll  = "all"
	checkExam = "example"
)

// checkRes ...
type checkRes struct {
	Msg          []string
	SandboxTotal int
	ProdTotal    int
	AppBizID     string
	ErrWfIds     []string
}

// CheckWorkflowEnableRsp ...
type CheckWorkflowEnableRsp struct {
	TraceID  string
	CheckRes *checkRes
	ErrMsg   string
}

// 使用方法：
// - 查全量应用工作流状态： curl -X POST -d "env=Sandbox&type=all" http://{ip}:{port}/v270/check-workflow-enable
// - 查单应用：curl -X POST -d "appid=123456&env=Sandbox" http://{ip}:{port}/v270/check-workflow-enable
// - 查单应用工作流及示例问法：curl -X POST -d "appid=123456&env=Sandbox&type=example" http://{ip}:{port}/v270/check-workflow-enable
// curl -X POST -d "appid=1872275726255521792&env=Sandbox" http://xxxx:8081/v270/check-workflow-enable -v

// CheckWorkflowEnable ...
func CheckWorkflowEnable(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	resp := &CheckWorkflowEnableRsp{}
	err := r.ParseForm()
	if err != nil {
		log.WarnContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|ParseForm|err:%+v", err)
		rspCheckErr(w, resp, "ParseForm error")
		return
	}

	log.InfoContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|r:%+v", r)
	log.InfoContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|Form:%+v", r.Form)
	env := r.FormValue("env")
	if env != entity.SandboxEnv && env != entity.ProductEnv {
		rspCheckErr(w, resp, "env is Sandbox or Product")
		return
	}
	checkType := r.FormValue("type")
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)
	resp.TraceID = traceID
	appIDs := getCheckAppIDs(ctx, env, w, r, resp)
	if len(appIDs) == 0 {
		rspCheckErr(w, resp, "appIDs is EMPTY")
		return
	}
	log.InfoContextf(ctx, "check-workflow-enable|env:%s|appIDs:%+v", env, appIDs)
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		res, err := checkEnableByAppId(newCtx, appID, env, checkType)
		if err != nil {
			rspCheckErr(w, resp, err.Error())
			return
		}
		resp.CheckRes = res
	}

	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|resp:%s", string(respStr))
	_, _ = w.Write(respStr)
}

func checkEnableByAppId(ctx context.Context, appID, env, checkType string) (*checkRes, error) {
	var result = &checkRes{}
	// 获取应用下所有工作流的个数
	totalCount, err := migrator.GetWorkflowCount(ctx, appID, env)
	result.AppBizID = appID
	if err != nil {
		log.WarnContextf(ctx, "check-workflow-enable|checkEnableByAppId|err:%+v", err)
		result.Msg = append(result.Msg, err.Error())
		return result, err
	}
	step = 50
	pageSize := step
	rdsWfsEnableMap, err := getWfsEnableFromRdsByAppId(ctx, appID, env)
	if err != nil {
		log.WarnContextf(ctx, "check-workflow-enable|getWfsEnableFromRdsByAppId|err:%+v", err)
		result.Msg = append(result.Msg, err.Error())
		return result, err
	}
	for currentPage := 1; ; currentPage++ {
		batchWfIds := make([]string, 0)
		wfs, err := migrator.GetSimpleWorkflowList(ctx, appID, env, currentPage, pageSize)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|GetSimpleWorkflowList|err:%+v", err)
			result.Msg = append(result.Msg, err.Error())
			return result, err
		}
		mm := make(map[string]struct{}, 0)
		for _, wf := range wfs {
			if _, ok := mm[wf.WorkflowID]; !ok {
				batchWfIds = append(batchWfIds, wf.WorkflowID)
				mm[wf.WorkflowID] = struct{}{}
			}
		}

		// 获取vector中的可用状态
		vectorWfsEnableMap, err := getEntitiesEnableVectorByAppId(ctx, appID, env, batchWfIds)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|getWfsEnableVectorByAppId|err:%+v", err)
			result.Msg = append(result.Msg, err.Error())
			result.ErrWfIds = append(result.ErrWfIds, batchWfIds...)
			continue
		}
		log.DebugContextf(ctx, "check-workflow-enable|checkEnableByAppId|appId:%s|env:%s|rdsWfsEnableMap:%+v"+
			"|vectorWfsEnableMap:%+v", appID, env, rdsWfsEnableMap, vectorWfsEnableMap)
		for _, wf := range wfs {
			if err := checkWfEnableBySingle(ctx, wf, env, rdsWfsEnableMap, vectorWfsEnableMap, checkType); err != nil {
				log.WarnContextf(ctx, "check-workflow-enable|checkWfEnableBySingle|err:%+v", err)
				result.Msg = append(result.Msg, err.Error())
				result.ErrWfIds = append(result.ErrWfIds, wf.WorkflowID)
				continue
			}
		}
		if currentPage*pageSize >= totalCount {
			break
		}
	}
	return result, nil
}

// checkWfEnableBySingle ...
func checkWfEnableBySingle(ctx context.Context, wf *migrator.SimpleWorkflow, env string,
	rdsWfsEnableMap map[string]string, vectorWfsEnableMap map[string]uint64, checkType string) error {
	// 通过wf计算是否可用（db获取）
	dbEnable := vdao.GetWfVectorEnableByFlowState(wf.WorkflowState, wf.IsEnable)

	rdsEnable := uint64(0)
	// 获取redis是否可用
	if value, ok := rdsWfsEnableMap[wf.WorkflowID]; ok {
		rdsEnable = cast.ToUint64(value)
	}

	vectorEnable := uint64(0)
	if value, ok := vectorWfsEnableMap[wf.WorkflowID]; ok {
		vectorEnable = value
	}
	msg := fmt.Sprintf("check-workflow-enable|env:%s|robotId:%s|wfId:%s|"+
		"dbEnable:%d|rdsEnable:%d|vectorEnable:%d",
		env, wf.RobotId, wf.WorkflowID, dbEnable, rdsEnable, vectorEnable)
	log.DebugContextf(ctx, msg)
	if !(dbEnable == rdsEnable && rdsEnable == vectorEnable) {
		errMsg := msg + "|enable not equal"
		return errors.New(errMsg)
	}

	if checkType == checkExam {
		// 获取工作流下的示例问法
		exams, err := migrator.GetFlowExampleByEnvWfId(ctx, wf.RobotId, wf.WorkflowID, env)
		if err != nil {
			return err
		}
		mm := make(map[string]struct{}, 0)
		batchExamIds := make([]string, 0)
		for _, v := range exams {
			if _, ok := mm[v.ExampleID]; !ok {
				batchExamIds = append(batchExamIds, v.ExampleID)
				mm[v.ExampleID] = struct{}{}
			}
		}
		// 获取示例问法
		vectorExamsEnableMap, err := getEntitiesEnableVectorByAppId(ctx, wf.RobotId, env, batchExamIds)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|getEntitiesEnableVectorByAppId|err:%+v", err)
			return err
		}
		for _, eId := range batchExamIds {
			examVectorEnable := uint64(0)
			if value, ok := vectorExamsEnableMap[eId]; ok {
				examVectorEnable = value
			}
			msg := fmt.Sprintf("check-workflow-enable|env:%s|robotId:%s|wfId:%s|examId:%s|"+
				"dbEnable:%d|rdsEnable:%d|vectorEnable:%d|examVectorEnable:%d",
				env, wf.RobotId, wf.WorkflowID, eId, dbEnable, rdsEnable, vectorEnable, examVectorEnable)
			log.DebugContextf(ctx, msg)
			if examVectorEnable != dbEnable {
				errMsg := msg + "|enable not equal"
				return errors.New(errMsg)
			}
		}
	}
	return nil
}

// getEntitiesEnableVectorByAppId ...
func getEntitiesEnableVectorByAppId(ctx context.Context, appID, env string,
	ids []string) (map[string]uint64, error) {
	useModelInfo := config.GetUsingVectorModelInfo(ctx)
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: appID,
		Secret: useModelInfo.Secret,
	}
	groupEnv := "sandbox"
	if env == entity.ProductEnv {
		groupEnv = "prod"
	}
	vectorEntitiesEnableMap := make(map[string]uint64, len(ids))
	groupID := fmt.Sprintf("%s-%s-%s-%s",
		useModelInfo.Biz, "workflow", groupEnv, appID)
	suffix := useModelInfo.GroupSuffix
	if len(suffix) > 0 {
		groupID = fmt.Sprintf("%s-%s", groupID, suffix)
	}
	vdb := vdao.NewDao()

	for _, batchIds := range types.SplitStringSlice(ids, useModelInfo.OperationMaxIDs) {
		wfVectors, err := vdb.GetVectors(ctx, util.RequestID(ctx), groupID, appInfo, batchIds)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|getWfsEnableVectorByAppId|GetVectors|fail|err:%+v", err)
			return nil, err
		}
		if len(wfVectors) > 0 {
			for _, v := range wfVectors {
				for _, f := range v.GetAttributeFields().GetFields() {
					if f.FieldName == vdao.FieldWorkflowEnable {
						vectorEntitiesEnableMap[v.Id] = f.FieldValueUint64
					}
				}
			}
		}
		log.DebugContextf(ctx, "check-workflow-enable|appID:%s|ids:%+v|vectorEntitiesEnableMap:%+v",
			appID, batchIds, vectorEntitiesEnableMap)
	}

	return vectorEntitiesEnableMap, nil
}

// getWfsEnableFromRdsByAppId 获取存在redis中的可用标识
func getWfsEnableFromRdsByAppId(ctx context.Context, appId, env string) (map[string]string, error) {
	key := fmt.Sprintf(entity.WfEnableRedisKey, env, appId)
	return database.GetRedis().HGetAll(ctx, key).Result()
}

func getCheckAppIDs(ctx context.Context, env string, w http.ResponseWriter, r *http.Request,
	resp *CheckWorkflowEnableRsp) []string {
	checkType := r.FormValue("type")
	log.InfoContextf(ctx, "check-workflow-enable| getAppBizIDs|checkType:%s", checkType)
	appidStr := r.FormValue("appid")
	if len(appidStr) > 0 {
		log.InfoContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|appidStr:%s", appidStr)
		_, err := strconv.ParseUint(appidStr, 10, 64)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|ParseUint|req:%+v, err:%+v", r, err)
			rspCheckErr(w, resp, err.Error())
			return nil
		}
		return []string{appidStr}
	}

	if checkType == checkExam {
		// 获取关联示例问法的应用Id
		appIDsFromDb, err := migrator.GetWorkflowExamRobotIDList(ctx, env)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|GetAppList|err:%+v", err)
			rspCheckErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}

	if checkType == checkAll {
		appIDsFromDb, err := migrator.GetWorkflowRobotIDList(ctx, env)
		if err != nil {
			log.WarnContextf(ctx, "check-workflow-enable|CheckWorkflowEnable|GetAppList|err:%+v", err)
			rspCheckErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}
	return nil
}

func rspCheckErr(w http.ResponseWriter, resp *CheckWorkflowEnableRsp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	log.Errorf("check-workflow-enable|err:%s", errMsg)
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}
