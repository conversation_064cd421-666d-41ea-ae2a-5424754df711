/*
 * 2024-12-25
 * Copyright (c) 2024. xinghuiquan@Tencent. All rights reserved.
 *
 */

package v270

import "fmt"

const (
	// EdgePrefix 前端数据Edge的ID的固定前缀
	EdgePrefix = "xy-edge__"
	// EndNodeID v2.7刷数据时，固定生成的结束节点的NodeID
	EndNodeID = "end"

	// NodeUI 前端在结束节点用的数据
	NodeUI = "{\"data\":{\"content\":\"\",\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":false,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"object\",\"children\":[]}]},\"position\":{\"x\":-1,\"y\":-1},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84}}"
)

// Edge 前端的数据 - 边
type Edge struct {
	Source       string   `json:"source"`       // 回复节点的NodeID
	SourceHandle string   `json:"sourceHandle"` // <回复节点的NodeID> + [-source]
	Target       string   `json:"target"`       // 结束节点的NodeID， 所有END节点  ID就叫： END
	TargetHandle string   `json:"targetHandle"` // <结束节点的NodeID> + [-target]
	Type         string   `json:"type"`         // custom
	Data         EdgeData `json:"data"`
	Id           string   `json:"id"`       // xy-edge__${source}${sourceHandler}-${target}${targetHandle}
	Selected     bool     `json:"selected"` // false
	Animated     bool     `json:"animated"` // false
}

// EdgeData 边里的Data数据
type EdgeData struct {
	ConnectedNodeIsHovering bool `json:"connectedNodeIsHovering"` // false
	Error                   bool `json:"error"`                   // false
	IsHovering              bool `json:"isHovering"`              // false
}

// source 是原回复节点的ID（背景： v2.7的时候把回复节点一拆二，所有回复节点后面都加一个END节点）
func buildEdgeID(source string) string {
	target := EndNodeID
	return fmt.Sprintf("%s%s%s-%s%s",
		EdgePrefix,
		source, buildSourceHandle(source),
		target, buildTargetHandle(target),
	)
}

func buildSourceHandle(source string) string {
	return fmt.Sprintf("%s-source", source)
}

func buildTargetHandle(target string) string {
	return fmt.Sprintf("%s-target", target)
}
