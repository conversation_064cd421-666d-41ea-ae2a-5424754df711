// bot-task-config-server
//
// @(#)sync-workflow-enable.go  星期四, 一月 02, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package v270

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/go-comm/encode"
	jsoniter "github.com/json-iterator/go"
)

// SyncResult ...
type SyncResult struct {
	SandboxTotal int
	ProdTotal    int
	AppBizID     string
}

// SyncEnableToRedisRsp ...
type SyncEnableToRedisRsp struct {
	TraceID    string
	SyncResult *SyncResult
	ErrMsg     string
}

// 使用方法：
// - 刷全量： curl -X POST -d "all=1" http://{ip}:{port}/v270/sync-workflow-enable
// - 刷单应用：curl -X POST -d "appid=123456" http://{ip}:{port}/v270/sync-workflow-enable
// - 测试(最后不保存，只是跑逻辑）：curl -X POST -d "appid=123456&test=1" http://{ip}:{port}/v270/sync-workflow-enable
// curl -X POST -d "appid=1872275726255521792&test=0" http://**************:8081/v270/sync-workflow-enable -v

// SyncWorkflowEnableToRedis ...
func SyncWorkflowEnableToRedis(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "sync-workflow-enable|SyncWorkflowEnableToRedis|ParseForm|err:%+v", err)
		return
	}
	testStr := r.FormValue("test")
	log.InfoContextf(ctx, "sync-workflow-enable|SyncWorkflowEnableToRedis|testStr:%s", testStr)
	test := false
	if testStr == yes {
		test = true
	}
	log.InfoContextf(ctx, "sync-workflow-enable|SyncWorkflowEnableToRedis|r:%+v", r)
	log.InfoContextf(ctx, "sync-workflow-enable|SyncWorkflowEnableToRedis|Form:%+v", r.Form)

	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)
	resp := &SyncEnableToRedisRsp{}
	resp.TraceID = traceID
	appIDs := getAppBizIDs(ctx, w, r, resp)
	if len(appIDs) == 0 {
		rspErr(w, resp, "appIDs is EMPTY")
		return
	}
	log.InfoContextf(ctx, "sync-workflow-enable|appIDs:%+v", appIDs)
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		res, err := syncWorkflowByAppID(newCtx, appID, test)
		if err != nil {
			rspErr(w, resp, err.Error())
			return
		}
		resp.SyncResult = res
	}

	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "sync-workflow-enable|SyncWorkflowEnableToRedis|resp:%s", string(respStr))
	_, _ = w.Write(respStr)
}

func syncWorkflowByAppID(ctx context.Context, appID string, test bool) (*SyncResult, error) {
	var result = &SyncResult{}

	result, err := syncWorkflowEnvByAppID(ctx, appID, entity.SandboxEnv, test, result)
	if err != nil {
		return result, err
	}

	result, err = syncWorkflowEnvByAppID(ctx, appID, entity.ProductEnv, test, result)
	if err != nil {
		return result, err
	}

	return result, nil
}

// syncEnableToRedis 同步到redis
func syncEnableToRedis(ctx context.Context, appID, env string,
	upsertWfEnableInfos map[string]string) error {
	key := fmt.Sprintf(entity.WfEnableRedisKey, env, appID)
	if len(upsertWfEnableInfos) > 0 {
		err := database.GetRedis().HMSet(ctx, key, upsertWfEnableInfos).Err()
		if err != nil {
			log.ErrorContextf(ctx, "sync-workflow-enable|env:%s|key:%s|val:%+v|err:%+v",
				env, key, upsertWfEnableInfos, err)
			return err
		}
	}
	return nil
}

func syncWorkflowEnvByAppID(ctx context.Context, appID, env string, test bool, result *SyncResult) (*SyncResult, error) {
	upsertRedisInfo := make(map[string]string)
	if result == nil {
		return result, nil
	}
	result.AppBizID = appID
	pageSize := 100

	// 根据环境获取工作流
	totalCount, err := migrator.GetWorkflowCount(ctx, appID, env)
	if err != nil {
		log.ErrorContextf(ctx, "sync-workflow-enable|GetWorkflowCount|err:%+v", err)
		return result, err
	}
	if entity.ProductEnv == env {
		result.ProdTotal = totalCount
	} else {
		result.SandboxTotal = totalCount
	}

	for currentPage := 1; ; currentPage++ {
		wfs, err := migrator.GetWorkflowList(ctx, appID, env, currentPage, pageSize)
		if err != nil {
			log.ErrorContextf(ctx, "sync-workflow-enable|GetWorkflowList|err:%+v", err)
			return result, err
		}
		for _, wf := range wfs {
			// 判断是否可用
			isEnable := vdao.GetWfVectorEnableByFlowState(wf.WorkflowState, wf.IsEnable)
			upsertRedisInfo[wf.WorkflowID] = fmt.Sprintf("%d", isEnable)
			log.InfoContextf(ctx, "sync-workflow-enable|env:%s|flowID:%s|flowState:%s|IsEnable:%+v|redisEnable:%+v",
				env, wf.WorkflowID, wf.WorkflowState, wf.IsEnable, isEnable)
		}
		if currentPage*pageSize >= totalCount {
			break
		}
	}
	// 是否入redis库
	if !test {
		err := syncEnableToRedis(ctx, appID, env, upsertRedisInfo)
		if err != nil {
			return result, err
		}
	}
	return result, nil
}

func getAppBizIDs(ctx context.Context, w http.ResponseWriter, r *http.Request,
	resp *SyncEnableToRedisRsp) []string {
	all := r.FormValue("all")
	log.InfoContextf(ctx, "sync-workflow-enable| getAppBizIDs|all:%s", all)
	if all == yes {
		appIDsFromDb, err := migrator.GetWorkflowRobotIDList(ctx, entity.SandboxEnv)
		if err != nil {
			log.WarnContextf(ctx, "sync-workflow-enable| SyncWorkflowEnableToRedis|GetAppList|err:%+v", err)
			rspErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}

	appidStr := r.FormValue("appid")
	log.InfoContextf(ctx, "sync-workflow-enable| SyncWorkflowEnableToRedis|appidStr:%s", appidStr)
	_, err := strconv.ParseUint(appidStr, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "sync-workflow-enable| SyncWorkflowEnableToRedis|ParseUint|req:%+v, err:%+v", r, err)
		rspErr(w, resp, err.Error())
		return nil
	}
	return []string{appidStr}
}

func rspErr(w http.ResponseWriter, resp *SyncEnableToRedisRsp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}
