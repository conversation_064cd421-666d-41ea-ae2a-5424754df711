/*
 * 2025-03-04
 * Copyright (c) 2025. hanlynn<PERSON>@Tencent. All rights reserved.
 *
 */

package v275

import (
	"context"
	"fmt"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"net/http"
	"strconv"
	"time"
)

const logPrefix = "populate-node-data"

const (
	TestScene = uint32(1) // 测试场景，即调试状态
	ProdScene = uint32(2) // 正式场景，即发布状态
)

// WorkflowResult .
type WorkflowResult struct {
	WorkflowID string `json:"workflow_id"`
	Name       string `json:"name"`
	Msg        string `json:"msg"`
	Elapsed    string `json:"elapsed"`
}

// AppBizIDResult .
type AppBizIDResult struct {
	AppBizID        string           `json:"app_biz_id"`
	Total           int              `json:"total"`
	WorkflowResults []WorkflowResult `json:"workflow_results"`
	Msg             string           `json:"msg"`
}

// PopulateNodeDataResp .
type PopulateNodeDataResp struct {
	TraceID         string            `json:"trace_id"`
	AppBizIDResults []*AppBizIDResult `json:"results"`
	ErrMsg          string            `json:"err_msg"`
}

// PopulateNodeData v2.7.5 刷数据： 填充 参数提取节点模型的模型名称 以及 知识检索和大模型知识问答节点的检索策略
//
// ------------------------------------------------------------------
//
// 使用说明： 打开trpc-go的admin，增加自定义指令
//
// 1. 在trpc-go.yaml中 开启admin
//  1. 参考资料： https://iwiki.woa.com/p/99485663
//  2. `curl "http://ip:port/cmds"`
//  3. `curl "http://ip:port/version"`
//
// 2. 在服务中注册：
//  1. `import "git.code.oa.com/trpc-go/trpc-go/admin"`
//  2. `admin.HandleFunc("/test", r.s.Test)`
//  3. 七彩石 - trpc-go.yaml 里的admin里配置端口号
//
// 3. （可选）在节点中：`ss -tunlp` 查看端口和ip情况
//
// 4. 在节点: `curl -X POST -d "uin=aaa&robot_id=bbb" http://{ip}:{port}/test -v`
//
// ------------------------------------------------------------------
//
// 具体接口使用说明（key-value对）：
//   - all: 全量，取值范围：0 或者 1，可不填，默认为 0，避免误操作对线上全量数据造成影响
//   - appid: 机器人ID （对应到t_robot表中的business_id），当 all 设置为 1 时可不填，否则必填
//   - scene: 场景（对应工作流在调试还是已发布），取值范围：1（对应测试场景）或者 2（对应正式场景），必填
//   - test: 测试（不写库，仅跑逻辑），取值范围：0 或者 1，可不填，默认为 1，避免误操作对线上数据造成影响
//
// 如：
//   - 刷全量： curl -X POST -d "all=1&scene=1&test=0" http://{ip}:{port}/v275/populate-node-data
//   - 刷单应用：curl -X POST -d "appid=123456&scene=1&test=0" http://{ip}:{port}/v275/populate-node-data
//   - 测试：curl -X POST -d "appid=123456&scene=2" http://{ip}:{port}/v275/populate-node-data
func PopulateNodeData(w http.ResponseWriter, r *http.Request) {
	// 1. 前置处理
	ctx := trpc.CloneContext(r.Context())
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)
	logFunctionPrefix := logPrefix + "|PopulateNodeData"
	resp := &PopulateNodeDataResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s|ParseForm|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	log.InfoContextf(ctx, "%s|r:%+v", logFunctionPrefix, r)
	log.InfoContextf(ctx, "%s|Form:%+v", logFunctionPrefix, r.Form)
	// 2.2. 提取参数
	all := false
	allStr := r.FormValue("all")
	if allStr != "" {
		all, err = strconv.ParseBool(allStr)
		if err != nil {
			log.WarnContextf(ctx, "%s|ParseBool|req:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	}
	appID := r.FormValue("appid")
	if appID != "" {
		// 校验appID是否为无符号整数格式的字符串
		_, err := strconv.ParseUint(appID, 10, 64)
		if err != nil {
			log.WarnContextf(ctx, "%s|ParseUint|req:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	} else if !all {
		// 非全量刷数据时appID禁止为空
		respErr(w, resp, "appid is EMPTY")
		return
	}
	sceneStr := r.FormValue("scene")
	var scene uint32
	if sceneStr == "" {
		respErr(w, resp, "scene is EMPTY")
		return
	} else {
		// 校验scene是否有效
		sceneUint64, err := strconv.ParseUint(sceneStr, 10, 32)
		if err != nil {
			log.WarnContextf(ctx, "%s|ParseUint|req:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
		scene = cast.ToUint32(sceneUint64)
		if scene != TestScene && scene != ProdScene {
			respErr(w, resp, "scene only support 1 for test or 2 for prod")
			return
		}
	}
	test := true
	testStr := r.FormValue("test")
	if testStr != "" {
		test, err = strconv.ParseBool(testStr)
		if err != nil {
			log.WarnContextf(ctx, "%s|ParseBool|req:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	}
	// 3. 刷数据
	// 3.1. 获取刷数据涉及的appID列表
	var appIDs []string
	if all {
		appIDs, err = migrator.GetWorkflowRobotIDList(ctx, entity.SandboxEnv)
		if err != nil {
			log.WarnContextf(ctx, "%s|GetAppIDs|err:%+v", logFunctionPrefix, err)
			respErr(w, resp, err.Error())
			return
		}
	} else {
		appIDs = append(appIDs, appID)
	}
	// 3.2. 按照appID的维度进行刷数据
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		// 需要根据场景（评测/正式）把应用的默认配置中刷到节点数据中
		appIds := []uint64{cast.ToUint64(appID)}
		rsp, err := rpc.GetAppInfosByAppIds(ctx, scene, appIds, rpc.DeleteFlagNoFilter)
		if err != nil {
			log.WarnContextf(ctx, "%s|AppID:%s|GetAppInfo|err:%+v", logFunctionPrefix, appID, err)
			resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
				AppBizID: appID,
				Msg:      err.Error(),
			})
			continue
		}
		if len(rsp.GetList()) == 0 {
			log.WarnContextf(ctx, "%s|AppID:%s|GetAppInfo|empty app info", logFunctionPrefix, appID)
			resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
				AppBizID: appID,
				Msg:      "empty app info",
			})
			continue
		}
		app := rsp.GetList()[0]
		// *****************************************************************
		// --story=121947222 【模型】参数提取节点的模型选择、条件判断/选项卡的模型
		// *****************************************************************
		modelMap := app.GetKnowledgeQa().GetModel()
		// 注意：normal_message是应用配置中的生成模型在map中对应的key，固化处理，不做成变量
		modelInfo, ok := modelMap["normal_message"]
		if !ok {
			log.WarnContextf(ctx, "%s|AppID:%s|normal_message model not found, map:%v",
				logFunctionPrefix, appID, modelMap)
			continue
		}
		modelName := modelInfo.GetModelName()
		// 高级版模型:"cs-normal-70b","hunyuan","sn-edu-70b" 需要替换为 "workflow-pro"
		if modelName == "cs-normal-70b" || modelName == "hunyuan" || modelName == "sn-edu-70b" {
			modelName = "workflow-pro"
		}
		// *****************************************************************
		// --story=121754038 【工作流节点】知识检索/大模型问答节点支持检索策略配置
		// *****************************************************************
		searchStrategy := &KEP_WF.SearchStrategy{}
		switch app.GetKnowledgeQa().GetSearchStrategy().GetStrategyType() {
		case bot_admin_config_server.SearchStrategyTypeEnum_Mixing:
			searchStrategy.StrategyType = KEP_WF.SearchStrategyTypeEnum_MIXING
		case bot_admin_config_server.SearchStrategyTypeEnum_Semantics:
			searchStrategy.StrategyType = KEP_WF.SearchStrategyTypeEnum_SEMANTICS
		}
		searchStrategy.TableEnhancement = app.GetKnowledgeQa().GetSearchStrategy().GetTableEnhancement()
		result, err := populateNodeData(newCtx, appID, scene, modelName, searchStrategy, test)
		if err != nil {
			log.WarnContextf(ctx, "%s|AppID:%s|populateNodeData|err:%+v", logFunctionPrefix, appID, err)
			resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
				AppBizID: appID,
				Msg:      err.Error(),
			})
			continue
		}
		resp.AppBizIDResults = append(resp.AppBizIDResults, result)
	}
	// 4. 正常返回
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "%s|resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}

func respErr(w http.ResponseWriter, resp *PopulateNodeDataResp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}

func populateNodeData(ctx context.Context, appID string, scene uint32, modelName string,
	searchStrategy *KEP_WF.SearchStrategy, test bool) (*AppBizIDResult, error) {
	// 前置处理
	logFunctionPrefix := logPrefix + "|populateNodeData"
	env := entity.SandboxEnv
	if scene == ProdScene {
		env = entity.ProductEnv
	}
	logAppPrefix := fmt.Sprintf("AppID:%s|Scene:%d", appID, scene)
	result := &AppBizIDResult{}
	result.AppBizID = appID
	// 从数据库中获取工作流数据（评测库或正式库）
	totalCount, err := migrator.GetWorkflowCount(ctx, appID, env)
	if err != nil {
		log.WarnContextf(ctx, "%s|%s|GetWorkflowCount|err:%+v", logFunctionPrefix, logAppPrefix, err)
		return result, err
	}
	result.Total = totalCount
	pageSize := 5
	for currentPage := 1; ; currentPage++ {
		wfList, err := migrator.GetWorkflowList(ctx, appID, env, currentPage, pageSize)
		if err != nil {
			log.WarnContextf(ctx, "%s|%s|GetWorkflowList|err:%+v", logFunctionPrefix, logAppPrefix, err)
			return result, err
		}
		for _, wf := range wfList {
			wfResult := WorkflowResult{}
			wfResult.WorkflowID = wf.WorkflowID
			wfResult.Name = wf.WorkflowName
			startTime := time.Now()
			// 修改草稿态数据
			log.InfoContextf(ctx, "%s|%s|process DialogJsonDraft start", logFunctionPrefix, logAppPrefix)
			migratorWorkflowDraft(ctx, appID, scene, test, wf, modelName, searchStrategy, result, &wfResult)
			log.InfoContextf(ctx, "%s|%s|process DialogJsonDraft done", logFunctionPrefix, logAppPrefix)
			// 修改待发布态数据
			log.InfoContextf(ctx, "%s|%s|process DialogJsonEnable start", logFunctionPrefix, logAppPrefix)
			migratorWorkflowEnable(ctx, appID, scene, test, wf, modelName, searchStrategy, result, &wfResult)
			log.InfoContextf(ctx, "%s|%s|process DialogJsonEnable done", logFunctionPrefix, logAppPrefix)
			elapsed := time.Since(startTime).String()
			wfResult.Elapsed = elapsed
			result.WorkflowResults = append(result.WorkflowResults, wfResult)
		}
		if currentPage*pageSize >= totalCount {
			break
		}
	}
	return result, nil
}

func migratorWorkflowDraft(ctx context.Context, appID string, scene uint32, test bool, wf *entity.Workflow,
	modelName string, searchStrategy *KEP_WF.SearchStrategy, result *AppBizIDResult, wfResult *WorkflowResult) {
	// 前置处理
	logFunctionPrefix := logPrefix + "|migratorWorkflowDraft"
	logAppPrefix := fmt.Sprintf("AppID:%s|Scene:%d", appID, scene)
	// 处理草稿态数据
	previousMsg := wfResult.Msg
	newDraftJson, needPopulate := migratorJson(ctx, appID, scene, wf.WorkflowID, wf.DialogJsonDraft,
		modelName, searchStrategy, wfResult)
	if wfResult.Msg != "" {
		wfResult.Msg = fmt.Sprintf("%s[wfDraftJsonErr:%s]", previousMsg, wfResult.Msg)
		return
	}
	wf.DialogJsonDraft = newDraftJson
	if !test && needPopulate {
		var err error
		// 入库
		switch scene {
		case TestScene:
			err = migrator.SaveWorkflow(ctx, wf)
		case ProdScene:
			err = migrator.SaveWorkflowProd(ctx, wf)
		}
		if err != nil {
			log.WarnContextf(ctx, "%s|SaveWorkflow|%s|WorkflowID:%s|err:%+v", logFunctionPrefix,
				logAppPrefix, wf.WorkflowID, err)
			wfResult.Msg = fmt.Sprintf("%s[dbErr:%+v]", wfResult.Msg, err.Error())
			result.WorkflowResults = append(result.WorkflowResults, *wfResult)
			return
		}
	}
}

func migratorWorkflowEnable(ctx context.Context, appID string, scene uint32, test bool, wf *entity.Workflow,
	modelName string, searchStrategy *KEP_WF.SearchStrategy, result *AppBizIDResult, wfResult *WorkflowResult) {
	// 前置处理
	logFunctionPrefix := logPrefix + "|migratorWorkflowEnable"
	logAppPrefix := fmt.Sprintf("AppID:%s|Scene:%d", appID, scene)
	// 处理待发布态数据
	previousMsg := wfResult.Msg
	newEnableJson, needPopulate := migratorJson(ctx, appID, scene, wf.WorkflowID, wf.DialogJsonEnable,
		modelName, searchStrategy, wfResult)
	if wfResult.Msg != "" {
		wfResult.Msg = fmt.Sprintf("%s[wfEnableJsonErr:%s]", previousMsg, wfResult.Msg)
		return
	}
	wf.DialogJsonEnable = newEnableJson
	if !test && needPopulate {
		var err error
		// 入库
		switch scene {
		case TestScene:
			err = migrator.SaveWorkflowEnable(ctx, wf)
		case ProdScene:
			err = migrator.SaveWorkflowEnableProd(ctx, wf)
		}
		if err != nil {
			log.WarnContextf(ctx, "%s|SaveWorkflowEnable|%s|WorkflowID:%s|err:%+v", logFunctionPrefix,
				logAppPrefix, wf.WorkflowID, err)
			wfResult.Msg = fmt.Sprintf("%s[dbErr:%+v]", wfResult.Msg, err.Error())
			return
		}
		// 通知DM
		workflowEnable, _ := protoutil.JsonToWorkflow(wf.DialogJsonEnable) // 无需判断error，必然成功
		request := new(KEP_WF_DM.UpsertWorkflowToSandboxRequest)
		request.AppID = appID
		request.Workflow = workflowEnable
		log.InfoContextf(ctx, "%s|NotifyDM|%s|WorkflowID:%s|req:%+v", logFunctionPrefix, logAppPrefix,
			wf.WorkflowID, request)
		response, err := rpc.UpsertWorkflowToSandbox(ctx, request)
		if err != nil {
			log.WarnContextf(ctx, "%s|NotifyDM|%s|WorkflowID:%s|err:%v", logFunctionPrefix, logAppPrefix,
				wf.WorkflowID, err.Error())
			wfResult.Msg = fmt.Sprintf("%s[dmErr:%+v]", wfResult.Msg, err.Error())
			return
		}
		log.InfoContextf(ctx, "%s|NotifyDM|%s|WorkflowID:%s|resp:%+v", logFunctionPrefix, logAppPrefix,
			wf.WorkflowID, response)
	}
}

func migratorJson(ctx context.Context, appID string, scene uint32, wfID string, wfJson string, modelName string,
	searchStrategy *KEP_WF.SearchStrategy, wfResult *WorkflowResult) (string, bool) {
	// 前置处理
	logFunctionPrefix := logPrefix + "|migratorJson"
	logAppPrefix := fmt.Sprintf("AppID:%s|Scene:%d", appID, scene)
	// 解析并处理JSON数据
	if len(wfJson) == 0 {
		wfResult.Msg = "ignore, workflow json is empty"
		return "", false
	}
	workflow, err := protoutil.JsonToWorkflow(wfJson)
	if err != nil {
		log.WarnContextf(ctx, "%s|JsonToWorkflow|%s|WorkflowID:%s|json:%s|err:%+v", logFunctionPrefix,
			logAppPrefix, wfID, wfJson, err)
		wfResult.Msg = fmt.Sprintf("ignore, wrong json|err:%+v", err)
		return "", false
	}
	if workflow == nil {
		wfResult.Msg = "ignore, workflow is nil"
		return "", false
	}
	needPopulate := migratorJsonInner(ctx, appID, modelName, searchStrategy, workflow)
	if !needPopulate {
		return "", false
	}
	newWfJson, err := protoutil.WorkflowToJson(workflow)
	if err != nil {
		log.WarnContextf(ctx, "%s|WorkflowToJson|%s|WorkflowID:%s|json:%s|err:%+v", logFunctionPrefix,
			logAppPrefix, wfID, wfJson, err)
		wfResult.Msg = fmt.Sprintf("convert to json failed|err:%+v", err)
		return "", false
	}
	log.InfoContextf(ctx, "%s|migratorJsonInner|%s|WorkflowID:%s|oldJson:%s|newJson:%s", logFunctionPrefix,
		logAppPrefix, wfID, wfJson, newWfJson)
	return newWfJson, needPopulate
}

func migratorJsonInner(ctx context.Context, appID string, modelName string,
	searchStrategy *KEP_WF.SearchStrategy, workflow *KEP_WF.Workflow) bool {
	// 前置处理
	logFunctionPrefix := logPrefix + "|migratorJsonInner"
	logAppPrefix := fmt.Sprintf("AppID:%s|WorkflowID:%s", appID, workflow.GetWorkflowID())
	needPopulate := false
	// 查找需要刷数据的节点并更新入参中的workflow
	for _, node := range workflow.GetNodes() {
		switch node.GetNodeType() {
		case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
			currentModelName := node.GetParameterExtractorNodeData().GetModelName()
			log.InfoContextf(ctx, "%s|%s|ParameterExtratorNode|populate ModelName, before:%s, after:%s",
				logFunctionPrefix, logAppPrefix, currentModelName, modelName)
			if currentModelName == "" {
				node.GetParameterExtractorNodeData().ModelName = modelName
				needPopulate = true
			}
		case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
			currentSearchStrategy := node.GetKnowledgeRetrieverNodeData().GetSearchStrategy()
			log.InfoContextf(ctx, "%s|%s|KnowledgeRetrieverNode|populate SearchStrategy, before:%s, after:%s",
				logFunctionPrefix, logAppPrefix,
				currentSearchStrategy, searchStrategy.String())
			if currentSearchStrategy == nil {
				node.GetKnowledgeRetrieverNodeData().SearchStrategy = &KEP_WF.SearchStrategy{
					StrategyType:     searchStrategy.GetStrategyType(),
					TableEnhancement: searchStrategy.GetTableEnhancement(),
				}
				needPopulate = true
			}
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
			currentSearchStrategy := node.GetLLMKnowledgeQANodeData().GetSearchStrategy()
			log.InfoContextf(ctx, "%s|%s|LLMKnowledgeQANode|populate SearchStrategy, before:%s, after:%s",
				logFunctionPrefix, logAppPrefix,
				currentSearchStrategy.String(), searchStrategy.String())
			if currentSearchStrategy == nil {
				node.GetLLMKnowledgeQANodeData().SearchStrategy = &KEP_WF.SearchStrategy{
					StrategyType:     searchStrategy.GetStrategyType(),
					TableEnhancement: searchStrategy.GetTableEnhancement(),
				}
				needPopulate = true
			}
		default:
			continue
		}
	}
	return needPopulate
}
