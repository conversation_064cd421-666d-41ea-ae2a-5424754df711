/*
 * 2025-05-23
 * Copyright (c) 2025. hanlynnke@Tencent. All rights reserved.
 *
 */

package v290

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const logPrefix = "knowledge-node-data"

const (
	TestScene = uint32(1) // 测试场景，即调试状态
	ProdScene = uint32(2) // 正式场景，即发布状态
)

// WorkflowResult .
type WorkflowResult struct {
	WorkflowID         string `json:"workflow_id"`
	Name               string `json:"name"`
	Msg                string `json:"msg"`
	Elapsed            string `json:"elapsed"`
	IsDraftNeedUpdate  bool   `json:"is_draft_need_update"`
	IsEnableNeedUpdate bool   `json:"is_enable_need_update"`
}

// AppBizIDResult .
type AppBizIDResult struct {
	AppBizID        string           `json:"app_biz_id"`
	Total           int              `json:"total"`
	NeedCount       int              `json:"need_count"`
	WorkflowResults []WorkflowResult `json:"workflow_results"`
	Msg             string           `json:"msg"`
}

// KnowledgeNodeDataResp .
type KnowledgeNodeDataResp struct {
	TraceID         string            `json:"trace_id"`
	AppBizIDResults []*AppBizIDResult `json:"results"`
	ErrMsg          string            `json:"err_msg"`
}

// KnowledgeNodeData v2.9.0 刷数据：将工作流画布中的 知识检索节点和大模型知识问答节点的数据刷到默认知识库里
// ------------------------------------------------------------------
//
// 使用说明： 打开trpc-go的admin，增加自定义指令
//
// 1. 在trpc-go.yaml中 开启admin
//  1. 参考资料： https://iwiki.woa.com/p/99485663
//  2. `curl "http://ip:port/cmds"`
//  3. `curl "http://ip:port/version"`
//
// 2. 在服务中注册：
//  1. `import "git.code.oa.com/trpc-go/trpc-go/admin"`
//  2. `admin.HandleFunc("/test", r.s.Test)`
//  3. 七彩石 - trpc-go.yaml 里的admin里配置端口号
//
// 3. （可选）在节点中：`ss -tunlp` 查看端口和ip情况
//
// 4. 在节点: `curl -X POST -d "uin=aaa&robot_id=bbb" http://{ip}:{port}/test -v`
//
// ------------------------------------------------------------------
//
// 具体接口使用说明（key-value对）：
//   - all: 全量，取值范围：0 或者 1，可不填，默认为 0，避免误操作对线上全量数据造成影响
//     all = 0 指定应用，appid必填
//     all = 1 全部应用，appid可不填
//   - appid: 机器人ID （对应到t_robot表中的business_id），当 all 设置为 1 时可不填，否则必填
//   - scene: 场景（对应工作流在调试还是已发布），取值范围：1（对应测试场景）或者 2（对应正式场景），必填
//     scene = 1 沙箱环境
//     scene = 2 线上环境
//   - test: 测试（不写库，仅跑逻辑），取值范围：0 或者 1，可不填，默认为 1，避免误操作对线上数据造成影响
//     test = 0 读数据并更新
//     test = 1 测试，只读数据
//
// 如：
//   - 刷全量： curl -X POST -d "all=1&scene=1&test=0" http://{ip}:{port}/v285/iteration-node-data
//   - 刷单应用：curl -X POST -d "appid=123456&scene=1&test=0" http://{ip}:{port}/v285/iteration-node-data
//   - 测试：curl -X POST -d "appid=123456&scene=2" http://{ip}:{port}/v285/iteration-node-data
func KnowledgeNodeData(w http.ResponseWriter, r *http.Request) {
	// 1. 前置处理
	ctx := trpc.CloneContext(r.Context())
	//ctx := trpc.BackgroundContext()
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	logFunctionPrefix := logPrefix + " start"
	resp := &KnowledgeNodeDataResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s ParseForm err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	log.InfoContextf(ctx, "%s request:%+v", logFunctionPrefix, r)
	log.InfoContextf(ctx, "%s form:%+v", logFunctionPrefix, r.Form)
	// 2.2. 提取参数
	all := false
	allStr := r.FormValue("all")
	if allStr != "" {
		all, err = strconv.ParseBool(allStr)
		if err != nil {
			log.WarnContextf(ctx, "%s all parseBool request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	}
	appID := r.FormValue("appid")
	if appID != "" {
		// 校验appID是否为无符号整数格式的字符串
		_, err := strconv.ParseUint(appID, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "%s appid parseUint request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	} else if !all {
		// 非全量刷数据时appID禁止为空
		log.ErrorContextf(ctx, "%s all and appid is EMPTY", logFunctionPrefix)
		respErr(w, resp, "appid is EMPTY")
		return
	}
	sceneStr := r.FormValue("scene")
	var scene uint32
	if sceneStr == "" {
		log.ErrorContextf(ctx, "%s scene is EMPTY", logFunctionPrefix)
		respErr(w, resp, "scene is EMPTY")
		return
	} else {
		// 校验scene是否有效
		sceneUint64, err := strconv.ParseUint(sceneStr, 10, 32)
		if err != nil {
			log.ErrorContextf(ctx, "%s scene ParseUint request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
		scene = cast.ToUint32(sceneUint64)
		if scene != TestScene && scene != ProdScene {
			log.ErrorContextf(ctx, "%s scene only support 1 for test or 2 for prod", logFunctionPrefix)
			respErr(w, resp, "scene only support 1 for test or 2 for prod")
			return
		}
	}
	test := true
	testStr := r.FormValue("test")
	if testStr != "" {
		test, err = strconv.ParseBool(testStr)
		if err != nil {
			log.ErrorContextf(ctx, "%s test ParseBool request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	}
	// 3. 刷数据
	// 3.1. 获取刷数据涉及的appID列表
	var appIDs []string
	if all {
		appIDs, err = migrator.GetWorkflowRobotIDList(ctx, entity.SandboxEnv)
		if err != nil {
			log.ErrorContextf(ctx, "%s GetAppIDs err:%+v", logFunctionPrefix, err)
			respErr(w, resp, err.Error())
			return
		}
	} else {
		appIDs = append(appIDs, appID)
	}
	// 3.2. 按照appID的维度进行刷数据
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		result, err := knowledgeNodeData(newCtx, appID, scene, test)
		if err != nil {
			log.ErrorContextf(ctx, "%s AppID:%s, knowledgeNodeData err:%+v", logFunctionPrefix, appID, err)
			resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
				AppBizID: appID,
				Msg:      err.Error(),
			})
			continue
		}
		resp.AppBizIDResults = append(resp.AppBizIDResults, result)
	}
	logFunctionPrefix = logPrefix + " end"
	// knowledge-node-data end
	log.InfoContextf(ctx, "%s", logFunctionPrefix)
	// 4. 正常返回
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)

	log.InfoContextf(ctx, "%s resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}

func respErr(w http.ResponseWriter, resp *KnowledgeNodeDataResp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}

func knowledgeNodeData(ctx context.Context, appID string, scene uint32, test bool) (*AppBizIDResult, error) {
	// 前置处理
	env := entity.SandboxEnv
	if scene == ProdScene {
		env = entity.ProductEnv
	}
	logAppPrefix := fmt.Sprintf("AppID:%s,Scene:%d", appID, scene)
	result := &AppBizIDResult{}
	result.AppBizID = appID
	// 从数据库中获取工作流数据（评测库或正式库）
	totalCount, err := migrator.GetWorkflowCount(ctx, appID, env)
	if err != nil {
		log.ErrorContextf(ctx, "%s,%s GetWorkflowCount err:%+v", logPrefix, logAppPrefix, err)
		return result, err
	}
	result.Total = totalCount
	pageSize := 10
	needCount := 0
	for currentPage := 1; ; currentPage++ {
		wfList, err := migrator.GetWorkflowList(ctx, appID, env, currentPage, pageSize)
		if err != nil {
			log.ErrorContextf(ctx, "%s,%s GetWorkflowList err:%+v", logPrefix, logAppPrefix, err)
			return result, err
		}
		log.InfoContextf(ctx, "%s,%s process workflow start", logPrefix, logAppPrefix)
		for _, wf := range wfList {
			wfResult := WorkflowResult{}
			wfResult.WorkflowID = wf.WorkflowID
			wfResult.Name = wf.WorkflowName
			startTime := time.Now()
			// 修改草稿态数据
			migratorWorkflowDraft(ctx, appID, scene, test, wf, result, &wfResult)
			// 修改待发布态数据
			migratorWorkflowEnable(ctx, appID, scene, test, wf, result, &wfResult)
			elapsed := time.Since(startTime).String()
			wfResult.Elapsed = elapsed
			if wfResult.IsEnableNeedUpdate || wfResult.IsDraftNeedUpdate {
				needCount += 1
			}
			result.WorkflowResults = append(result.WorkflowResults, wfResult)
		}
		log.InfoContextf(ctx, "%s,%s process workflow done", logPrefix, logAppPrefix)
		if currentPage*pageSize >= totalCount {
			break
		}
	}
	result.NeedCount = needCount
	return result, nil
}

func migratorWorkflowDraft(ctx context.Context, appID string, scene uint32, test bool, wf *entity.Workflow,
	result *AppBizIDResult, wfResult *WorkflowResult) {
	// 前置处理
	logFunctionPrefix := logPrefix + " migratorWorkflowDraft"
	logAppPrefix := fmt.Sprintf("AppID:%s,Scene:%d", appID, scene)
	// 处理草稿态数据
	previousMsg := wfResult.Msg
	newDraftJson, needPopulate := migratorJson(ctx, appID, scene, wf.WorkflowID, wf.DialogJsonDraft, wfResult)
	if wfResult.Msg != "" {
		wfResult.Msg = fmt.Sprintf("%s[wfDraftJsonErr:%s]", previousMsg, wfResult.Msg)
		return
	}
	//log.InfoContextf(ctx, "%s,%s migratorWorkflowDraft WorkflowID:%s,oldJson:%s,newJson:%s", logFunctionPrefix,
	//	logAppPrefix, wf.WorkflowID, wf.DialogJsonDraft, newDraftJson)
	wf.DialogJsonDraft = newDraftJson
	wfResult.IsDraftNeedUpdate = needPopulate
	if !test && needPopulate {
		var err error
		// 入库
		switch scene {
		case TestScene:
			err = migrator.SaveWorkflow(ctx, wf)
		case ProdScene:
			err = migrator.SaveWorkflowProd(ctx, wf)
		}
		if err != nil {
			log.ErrorContextf(ctx, "%s,%s SaveWorkflowDraft WorkflowID:%s,err:%+v", logFunctionPrefix,
				logAppPrefix, wf.WorkflowID, err)
			wfResult.Msg = fmt.Sprintf("%s[dbErr:%+v]", wfResult.Msg, err.Error())
			result.WorkflowResults = append(result.WorkflowResults, *wfResult)
			return
		}
	}
}

func migratorWorkflowEnable(ctx context.Context, appID string, scene uint32, test bool, wf *entity.Workflow,
	result *AppBizIDResult, wfResult *WorkflowResult) {
	// 前置处理
	logFunctionPrefix := logPrefix + " migratorWorkflowEnable"
	logAppPrefix := fmt.Sprintf("AppID:%s,Scene:%d", appID, scene)
	// 处理待发布态数据
	previousMsg := wfResult.Msg
	newEnableJson, needPopulate := migratorJson(ctx, appID, scene, wf.WorkflowID, wf.DialogJsonEnable, wfResult)
	wfResult.IsEnableNeedUpdate = needPopulate
	if wfResult.Msg != "" {
		wfResult.Msg = fmt.Sprintf("%s[wfEnableJsonErr:%s]", previousMsg, wfResult.Msg)
		return
	}
	//log.InfoContextf(ctx, "%s,%s migratorWorkflowEnable WorkflowID:%s,oldJson:%s,newJson:%s", logFunctionPrefix,
	//	logAppPrefix, wf.WorkflowID, wf.DialogJsonDraft, newEnableJson)
	wf.DialogJsonEnable = newEnableJson
	if !test && needPopulate {
		var err error
		// 入库
		switch scene {
		case TestScene:
			err = migrator.SaveWorkflowEnable(ctx, wf)
		case ProdScene:
			err = migrator.SaveWorkflowEnableProd(ctx, wf)
		}
		if err != nil {
			log.ErrorContextf(ctx, "%s,%s SaveWorkflowEnable WorkflowID:%s,err:%+v", logFunctionPrefix,
				logAppPrefix, wf.WorkflowID, err)
			wfResult.Msg = fmt.Sprintf("%s[dbErr:%+v]", wfResult.Msg, err.Error())
			result.WorkflowResults = append(result.WorkflowResults, *wfResult)
			return
		}
	}
}

func migratorJson(ctx context.Context, appID string, scene uint32, wfID string, wfJson string,
	wfResult *WorkflowResult) (string, bool) {
	// 前置处理
	logFunctionPrefix := logPrefix + " migratorJson"
	logAppPrefix := fmt.Sprintf("AppID:%s,Scene:%d,workflowID:%s", appID, scene, wfID)
	// 解析并处理JSON数据
	if len(wfJson) == 0 {
		log.WarnContextf(ctx, "%s,%s wfJson is empty", logFunctionPrefix, logAppPrefix)
		wfResult.Msg = "ignore, workflow json is empty"
		return "", false
	}
	workflow, err := protoutil.JsonToWorkflow(wfJson)
	if err != nil {
		log.ErrorContextf(ctx, "%s,%s JsonToWorkflow json:%s,err:%+v", logFunctionPrefix,
			logAppPrefix, wfJson, err)
		wfResult.Msg = fmt.Sprintf("ignore, wrong json|err:%+v", err)
		return "", false
	}
	if workflow == nil {
		log.WarnContextf(ctx, "%%s,%s wfJson JsonToWorkflow is nil", logFunctionPrefix, logAppPrefix)
		wfResult.Msg = "ignore, workflow is nil"
		return "", false
	}
	needPopulate := migratorJsonInner(ctx, appID, workflow)
	if !needPopulate {
		return "", false
	}
	newWfJson, err := protoutil.WorkflowToJson(workflow)
	if err != nil {
		log.ErrorContextf(ctx, "%s,%s WorkflowToJson json:%s,err:%+v", logFunctionPrefix,
			logAppPrefix, wfID, wfJson, err)
		wfResult.Msg = fmt.Sprintf("convert to json failed|err:%+v", err)
		return "", false
	}
	return newWfJson, needPopulate
}

func migratorJsonInner(ctx context.Context, appID string, workflow *KEP_WF.Workflow) bool {
	// 前置处理
	logFunctionPrefix := logPrefix + " migratorJsonInner"
	logAppPrefix := fmt.Sprintf("AppID:%s,WorkflowID:%s", appID, workflow.GetWorkflowID())
	needPopulate := false
	// 查找需要刷数据的节点并更新入参中的workflow
	for _, node := range workflow.GetNodes() {
		switch node.GetNodeType() {
		case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
			needPopulate = replaceKnowledgeRetrieverNodeData(appID, node)
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
			needPopulate = replaceLLMKnowledgeQANodeData(appID, node)
		default:
			continue
		}
	}
	log.InfoContextf(ctx, "%s,%s is need refresh data:%s",
		logFunctionPrefix, logAppPrefix, needPopulate)
	return needPopulate
}

func replaceKnowledgeRetrieverNodeData(appID string, node *KEP_WF.WorkflowNode) bool {
	nodeData := node.GetKnowledgeRetrieverNodeData()
	if nodeData.GetAllKnowledge() || len(nodeData.GetKnowledgeList()) > 0 {
		return false
	}
	knowledge := &KEP_WF.Knowledge{
		KnowledgeType:  KEP_WF.Knowledge_DEFAULT,
		KnowledgeBizID: appID,
		DocBizIDs:      nodeData.GetDocBizIDs(),
		CateBizIDs:     nil,
		Labels:         nodeData.GetLabels(),
		Filter:         nodeData.GetFilter(),
		AllQA:          nodeData.GetAllQA(),
		DocRecallCount: nodeData.GetDocRecallCount(),
		DocConfidence:  nodeData.GetDocConfidence(),
		QARecallCount:  nodeData.GetQARecallCount(),
		QAConfidence:   nodeData.GetQAConfidence(),
		SearchStrategy: nodeData.GetSearchStrategy(),
	}
	nodeData.KnowledgeList = append(nodeData.KnowledgeList, knowledge)
	return true
}

func replaceLLMKnowledgeQANodeData(appID string, node *KEP_WF.WorkflowNode) bool {
	nodeData := node.GetLLMKnowledgeQANodeData()
	if nodeData.GetAllKnowledge() || len(nodeData.GetKnowledgeList()) > 0 {
		return false
	}
	knowledge := &KEP_WF.Knowledge{
		KnowledgeType:  KEP_WF.Knowledge_DEFAULT,
		KnowledgeBizID: appID,
		DocBizIDs:      nodeData.GetDocBizIDs(),
		CateBizIDs:     nil,
		Labels:         nodeData.GetLabels(),
		Filter:         nodeData.GetFilter(),
		AllQA:          nodeData.GetAllQA(),
		DocRecallCount: nodeData.GetDocRecallCount(),
		DocConfidence:  nodeData.GetDocConfidence(),
		QARecallCount:  nodeData.GetQARecallCount(),
		QAConfidence:   nodeData.GetQAConfidence(),
		SearchStrategy: nodeData.GetSearchStrategy(),
	}
	nodeData.KnowledgeList = append(nodeData.KnowledgeList, knowledge)
	return true
}
