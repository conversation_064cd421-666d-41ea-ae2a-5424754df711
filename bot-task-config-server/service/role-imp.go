package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/role"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/asaskevich/govalidator"
)

// RoleDefaultTemplateList 角色预设模版获取
func (imp TaskConfigImp) RoleDefaultTemplateList(ctx context.Context, req *KEP.RoleDefaultTemplateListReq) (
	*KEP.RoleDefaultTemplateListRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("RoleDefaultTemplateList|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		rsp, err := role.GetRoleDefaultTemplateList(ctx, req)
		log.Infof("RoleDefaultTemplateList|RSP|%s|%v|%v", sid, rsp, err)
		return rsp, err
	}

	err := errors.BadRequestError(gve.Error())
	log.Warnf("RoleDefaultTemplateList|RSP|%s|%v", sid, err)
	return nil, err
}

// GetPromptWordTemplateList 获取提示词模版
func (imp TaskConfigImp) GetPromptWordTemplateList(ctx context.Context, req *KEP.GetPromptWordTemplateListReq) (
	*KEP.GetPromptWordTemplateListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetPromptWordTemplateList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		rsp, err := role.GetPromptWordTemplateList(ctx, req)
		log.Infof("GetPromptWordTemplateList|RSP|%s|%v|%v", sid, rsp, err)
		return rsp, err
	}

	err := errors.BadRequestError(gve.Error())
	log.Warnf("GetPromptWordTemplateList|RSP|%s|%v", sid, err)
	return nil, err
}
