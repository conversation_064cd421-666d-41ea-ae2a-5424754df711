package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/slot"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/asaskevich/govalidator"
)

// GetSlotList 槽位列表
func (imp TaskConfigImp) GetSlotList(ctx context.Context, req *KEP.GetSlotListReq) (*KEP.GetSlotListRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GetSlotList|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.GetSlotList(ctx, req)
		log.Infof("GetSlotList|RESP|%s|%+v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetSlotList|%s|%v", sid, err0)
	return nil, err0
}

// CreateSlot 新建槽位
func (imp TaskConfigImp) CreateSlot(ctx context.Context, req *KEP.CreateSlotReq) (*KEP.CreateSlotRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CreateSlot|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		resp, err := slot.CreateSlot(ctx, req)
		log.Infof("CreateSlot|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|CreateSlot|%s|%v", sid, err0)
	return nil, err0
}

// UpdateSlot 更新槽位
func (imp TaskConfigImp) UpdateSlot(ctx context.Context, req *KEP.UpdateSlotReq) (*KEP.UpdateSlotRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateSlot|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.UpdateSlot(ctx, req)
		log.Infof("UpdateSlot|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|UpdateSlot|%s|%v", sid, err0)
	return nil, err0
}

// DeleteSlot 删除槽位
func (imp TaskConfigImp) DeleteSlot(ctx context.Context, req *KEP.DeleteSlotReq) (*KEP.DeleteSlotRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteSlot|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.DeleteSlot(ctx, req)
		log.Infof("DeleteSlot|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|DeleteSlot|%s|%v", sid, err0)
	return nil, err0
}

// GetEntryList 词条列表
func (imp TaskConfigImp) GetEntryList(ctx context.Context, req *KEP.GetEntryListReq) (*KEP.GetEntryListRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GetEntryList|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.GetEntryList(ctx, req)
		log.Infof("GetEntryList|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetEntryList|%s|%v", sid, err0)
	return nil, err0
}

// CreateEntry 新建词条
func (imp TaskConfigImp) CreateEntry(ctx context.Context, req *KEP.CreateEntryReq) (*KEP.CreateEntryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CreateEntry|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.CreateEntry(ctx, req)
		log.Infof("CreateEntry|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|CreateEntry|%s|%v", sid, err0)
	return nil, err0
}

// UpdateEntry 更新词条
func (imp TaskConfigImp) UpdateEntry(ctx context.Context, req *KEP.UpdateEntryReq) (*KEP.UpdateEntryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateEntry|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.UpdateEntry(ctx, req)
		log.Infof("UpdateEntry|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|UpdateEntry|%s|%v", sid, err0)
	return nil, err0
}

// DeleteEntry 删除词条
func (imp TaskConfigImp) DeleteEntry(ctx context.Context, req *KEP.DeleteEntryReq) (*KEP.DeleteEntryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteEntry|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := slot.DeleteEntry(sid, ctx, req)
		log.Infof("DeleteEntry|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|DeleteEntry|%s|%v", sid, err0)
	return nil, err0
}

// ImportEntry 导入词条流程
func (imp TaskConfigImp) ImportEntry(ctx context.Context, req *KEP.ImportEntryReq) (*KEP.ImportEntryRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ImportEntry|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		resp, err := slot.ImportEntry(ctx, req)
		log.Infof("ImportEntry|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ImportEntry|RESP|%s|%v", sid, err0)
	return nil, err0
}
