// bot-task-config-server
//
// @(#)sync-task.go  Wednesday, December 20, 2023
// Copyright(c) 2023, leyton@Tencent. All rights reserved.

package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	pdlSyncTask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/synctask"
	taskFlowSyncTask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/synctask"
	workflowSyncTask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	adminPb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"github.com/asaskevich/govalidator"
)

// GetUnreleasedCount 未发布数量
func (imp TaskConfigImp) GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (
	*KEP.GetUnreleasedCountRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetUnreleasedCount|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		if req.GetCorpID() > 0 {
			ctx = util.WithCorpID(ctx, req.GetCorpID())
		}
		if req.GetStaffID() > 0 {
			ctx = util.WithStaffID(ctx, req.GetStaffID())
		}
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|GetUnreleasedCount|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.GetUnreleasedCountRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetUnreleasedCount(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskFlowSyncTask.GetUnreleasedCount(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdlSyncTask.GetUnreleasedCount(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetUnreleasedCount(ctx, req)
			}
		}

		log.Infof("RESP|GetUnreleasedCount|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetUnreleasedCount|%s|%v", sid, err0)
	return nil, err0
}

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
func (imp TaskConfigImp) SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|SendDataSyncTaskEvent|%s|%v", sid, req)

	if req.GetTaskID() == 0 {
		err0 := errors.BadRequestError("请传入正确的 [TaskID]")
		log.Warnf("E|RESP|SendDataSyncTaskEvent|%s|%v", sid, err0)
		return nil, err0
	}

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		if req.GetCorpID() > 0 {
			ctx = util.WithCorpID(ctx, req.GetCorpID())
		}
		if req.GetStaffID() > 0 {
			ctx = util.WithStaffID(ctx, req.GetStaffID())
		}
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|SendDataSyncTaskEvent|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.SendDataSyncTaskEventRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.SendDataSyncTaskEvent(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskFlowSyncTask.SendDataSyncTaskEvent(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdlSyncTask.SendDataSyncTaskEvent(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.SendDataSyncTaskEvent(ctx, req)
			}
		}

		log.Infof("RESP|SendDataSyncTaskEvent|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|SendDataSyncTaskEvent|%s|%v", sid, err0)
	return nil, err0
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
func (imp TaskConfigImp) GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetDataSyncTask|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		if req.GetCorpID() > 0 {
			ctx = util.WithCorpID(ctx, req.GetCorpID())
		}
		if req.GetStaffID() > 0 {
			ctx = util.WithStaffID(ctx, req.GetStaffID())
		}
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|GetDataSyncTask|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.GetDataSyncTaskRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetDataSyncTask(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskFlowSyncTask.GetDataSyncTask(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdlSyncTask.GetDataSyncTask(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetDataSyncTask(ctx, req)
			}
		}

		log.Infof("RESP|GetDataSyncTask|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetDataSyncTask|%s|%v", sid, err0)
	return nil, err0
}

// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
func (imp TaskConfigImp) GetDataSyncTasks(ctx context.Context, req *KEP.GetDataSyncTasksReq) (*KEP.GetDataSyncTasksRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetDataSyncTasks|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|GetDataSyncTasks|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.GetDataSyncTasksRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetDataSyncTasks(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskFlowSyncTask.GetDataSyncTasks(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdlSyncTask.GetDataSyncTasks(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.GetDataSyncTasks(ctx, req)
			}
		}

		log.Infof("RESP|GetDataSyncTasks|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetDataSyncTasks|%s|%v", sid, err0)
	return nil, err0
}

// CheckRobotReady 检查机器人, 是否准备好
func (imp TaskConfigImp) CheckRobotReady(ctx context.Context, req *KEP.CheckRobotReadyReq) (*KEP.CheckRobotReadyRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|CheckRobotReady|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|CheckRobotReady|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.CheckRobotReadyRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.CheckRobotReady(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskFlowSyncTask.CheckRobotReady(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdlSyncTask.CheckRobotReady(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflowSyncTask.CheckRobotReady(ctx, req)
			}
		}

		log.Infof("RESP|CheckRobotReady|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Errorf("E|RESP|CheckRobotReady|%s|%v", sid, err0)
	return nil, err0
}

// ReleaseAPIVarParams 通知API参数发布
func (imp TaskConfigImp) ReleaseAPIVarParams(ctx context.Context, req *KEP.ReleaseAPIVarParamsReq) (
	*KEP.ReleaseAPIVarParamsRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|ReleaseAPIVarParams|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		if req.GetCorpID() > 0 {
			ctx = util.WithCorpID(ctx, req.GetCorpID())
		}
		if req.GetStaffID() > 0 {
			ctx = util.WithStaffID(ctx, req.GetStaffID())
		}

		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		resp, err := workflowSyncTask.ReleaseAPIVarParams(ctx, req)
		log.Infof("RESP|ReleaseAPIVarParams|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|ReleaseAPIVarParams|%s|%v", sid, err0)
	return nil, err0
}

// getAppPatternAndWorkflowInfo 获取应用模式和工作流配置信息
func (imp TaskConfigImp) getAppPatternAndWorkflowInfo(ctx context.Context, robotID interface{}) (
	string, *adminPb.AppWorkflow, error) {
	log.InfoContextf(ctx, "getAppPatternAndWorkflowInfo robotID: %+v", robotID)
	var appBizID uint64
	switch t := robotID.(type) {
	case uint64:
		appBizID = t
	case string:
		appBizID, _ = strconv.ParseUint(t, 10, 64)
	default:
		log.WarnContextf(ctx, "getAppPatternAndWorkflowInfo robotID: %+v illegal", robotID)
		return "", nil, fmt.Errorf("robotID: %+v illegal", robotID)
	}
	if appBizID == 0 {
		log.WarnContextf(ctx, "getAppPatternAndWorkflowInfo robotID: %+v illegal", robotID)
		return "", nil, fmt.Errorf("robotID: %+v illegal", robotID)
	}
	req := &adminPb.GetAppInfoReq{
		AppBizId: appBizID,
		Scenes:   1, // 这里指定场景 1是评测
	}
	log.InfoContextf(ctx, "getAppPatternAndWorkflowInfo GetAppInfo, req: %+v", req)
	rsp, err := proxy.GetAdminAPIProxy().GetAppInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "getAppPatternAndWorkflowInfo GetAppInfo failed, err: %+v", err)
		return "", nil, err
	}
	log.InfoContextf(ctx, "getAppPatternAndWorkflowInfo GetAppInfo, rsp.Pattern: %s, rsp.Workflow: %+v",
		rsp.GetKnowledgeQa().GetPattern(), rsp.GetKnowledgeQa().GetWorkflow())
	return rsp.GetKnowledgeQa().GetPattern(), rsp.GetKnowledgeQa().GetWorkflow(), nil
}
