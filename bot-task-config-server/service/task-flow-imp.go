// bot-task-config-server
//
// @(#)task-flow-service.go  星期五, 十二月 15, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/app"
	pdl "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/taskflow"
	workflow "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/asaskevich/govalidator"
)

// RecoverHistoryTaskFlow 恢复历史版本接口
func (imp TaskConfigImp) RecoverHistoryTaskFlow(ctx context.Context, req *KEP.RecoverHistoryTaskFlowReq) (
	*KEP.RecoverHistoryTaskFlowRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|RecoverHistoryTaskFlow|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := taskflow.RecoverHistoryTaskFlow(ctx, req)
		log.Infof("RESP|RecoverHistoryTaskFlow|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err,
			time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|RecoverHistoryTaskFlow|%s|%v", sid, err0)
	return nil, err0
}

// ListHistoryTaskFlow 历史版本列表
func (imp TaskConfigImp) ListHistoryTaskFlow(ctx context.Context, req *KEP.ListHistoryTaskFlowReq) (
	*KEP.ListHistoryTaskFlowRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|ListHistoryTaskFlow|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := taskflow.ListHistoryTaskFlow(ctx, req)
		log.Infof("RESP|ListHistoryTaskFlow|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err,
			time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|ListHistoryTaskFlow|%s|%v", sid, err0)
	return nil, err0
}

// DescribeHistoryTaskFlow 历史版本详情列表
func (imp TaskConfigImp) DescribeHistoryTaskFlow(ctx context.Context, req *KEP.DescribeHistoryTaskFlowReq) (*KEP.DescribeHistoryTaskFlowRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|DescribeHistoryTaskFlow|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := taskflow.DescribeHistoryTaskFlow(ctx, req)
		log.Infof("RESP|DescribeHistoryTaskFlow|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|DescribeHistoryTaskFlow|%s|%v", sid, err0)
	return nil, err0
}

// GetRobotIdByShareCode 通过分享码获取机器人ID
func (imp TaskConfigImp) GetRobotIdByShareCode(ctx context.Context, req *KEP.GetRobotIdByShareCodeReq) (*KEP.GetRobotIdByShareCodeResp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetRobotIdByShareCode|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := app.GetRobotIdByShareCode(ctx, req)
		log.Infof("RESP|GetAppShareURL|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetAppShareURL|%s|%v", sid, err0)
	return nil, err0
}

// GetTaskFlowReleaseStatus 查询任务流的发布状态
func (imp TaskConfigImp) GetTaskFlowReleaseStatus(ctx context.Context, req *KEP.GetTaskFlowReleaseStatusReq) (*KEP.GetTaskFlowReleaseStatusResp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetTaskFlowReleaseStatus|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|GetTaskFlowReleaseStatus|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.GetTaskFlowReleaseStatusResp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflow.GetWorkflowReleaseStatus(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskflow.GetTaskFlowReleaseStatus(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdl.GetPDLReleaseStatus(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflow.GetWorkflowReleaseStatus(ctx, req)
			}
		}

		log.Infof("RESP|GetTaskFlowReleaseStatus|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetTaskFlowReleaseStatus|%s|%v", sid, err0)
	return nil, err0
}

// ListTaskFlow 任务流程列表
func (imp TaskConfigImp) ListTaskFlow(ctx context.Context,
	req *KEP.ListTaskFlowReq) (*KEP.ListTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.ListTaskFlow(ctx, req)
		log.Infof("ListTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetTaskFlowDetail 获取任务流程详情
func (imp TaskConfigImp) GetTaskFlowDetail(ctx context.Context,
	req *KEP.GetTaskFlowDetailReq) (*KEP.GetTaskFlowDetailRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GetTaskFlowDetail|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.GetTaskFlowDetail(ctx, req)
		log.Infof("GetTaskFlowDetail|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GetTaskFlowDetail|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GroupTaskFlow 分组移动任务流到不同分类
func (imp TaskConfigImp) GroupTaskFlow(ctx context.Context,
	req *KEP.GroupTaskFlowReq) (*KEP.GroupTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GroupTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.GroupTaskFlow(ctx, req)
		log.Infof("GroupTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GroupTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// CreateTaskFlow 创建任务流程
func (imp TaskConfigImp) CreateTaskFlow(ctx context.Context,
	req *KEP.CreateTaskFlowReq) (*KEP.CreateTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CreateTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.CreateTaskFlow(ctx, req)
		log.Infof("CreateTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CreateTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// SaveTaskFlow 编辑更新TaskFlow
func (imp TaskConfigImp) SaveTaskFlow(ctx context.Context,
	req *KEP.SaveTaskFlowReq) (*KEP.SaveTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("SaveTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.SaveTaskFlow(ctx, req)
		log.Infof("SaveTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("SaveTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteTaskFlow 删除TaskFlow
func (imp TaskConfigImp) DeleteTaskFlow(ctx context.Context,
	req *KEP.DeleteTaskFlowReq) (*KEP.DeleteTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := taskflow.DeleteTaskFlow(ctx, req)
		log.Infof("DeleteTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DeleteTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListTaskFlowPreview 获取发布列表
func (imp TaskConfigImp) ListTaskFlowPreview(ctx context.Context,
	req *KEP.ListTaskFlowPreviewReq) (*KEP.ListTaskFlowPreviewRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|ListTaskFlowPreview|%s|%v", sid, req)

	// 添加判断机器人是否存在
	_, err := permission.CheckRobot(ctx, 1, req.GetBotBizId())
	if err != nil {
		log.Errorf("P|ListTaskFlowPreview|%s|%v", sid, err)
		return nil, err
	}

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)

		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetBotBizId())
		if err != nil {
			log.Errorf("P|GetTaskFlowReleaseStatus|%s|%v", sid, err)
			return nil, err
		}

		resp := &KEP.ListTaskFlowPreviewRsp{}

		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflow.ListWorkflowPreview(ctx, req)
			} else {
				// 旧工作流
				resp, err = taskflow.ListTaskFlowPreview(ctx, req)
			}
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				resp, err = pdl.ListPDLPreview(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				resp, err = workflow.ListWorkflowPreview(ctx, req)
			}
		}

		log.Infof("RESP|ListTaskFlowPreview|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|ListTaskFlowPreview|%s|%v", sid, err0)
	return nil, err0
}

// ImportTaskFlow 导入任务流程
func (imp TaskConfigImp) ImportTaskFlow(ctx context.Context, req *KEP.ImportTaskFlowReq) (*KEP.ImportTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ImportTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		resp, err := taskflow.ImportTaskFlow(ctx, req)
		log.Infof("ImportTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ImportTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ExportTaskFlow 导出任务流程
func (imp TaskConfigImp) ExportTaskFlow(ctx context.Context, req *KEP.ExportTaskFlowReq) (*KEP.ExportTaskFlowRsp,
	error) {
	sid := util.RequestID(ctx)
	log.Infof("ExportTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		resp, err := taskflow.ExportTaskFlow(ctx, req)
		log.Infof("ExportTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ExportTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// InnerExportTaskFlow 导出任务流程
func (imp TaskConfigImp) InnerExportTaskFlow(ctx context.Context,
	req *KEP.InnerExportTaskFlowReq) (*KEP.InnerExportTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("InnerExportTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		resp, err := taskflow.InnerExportTaskFlow(ctx, req)
		log.Infof("InnerExportTaskFlow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("InnerExportTaskFlow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// PreviewTaskFlowRequestNode 预览TaskFlow的询问节点
func (imp TaskConfigImp) PreviewTaskFlowRequestNode(ctx context.Context, req *KEP.PreviewTaskFlowRequestNodeReq) (
	*KEP.PreviewTaskFlowRequestNodeRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("PreviewTaskFlowRequestNode|REQ|%s|%v", sid, req)
	resp, err := taskflow.PreviewTaskFlowRequestNode(ctx, req)
	log.Infof("PreviewTaskFlowRequestNode|RESP|%s|%v", sid, err)
	return resp, err
}

// PreviewTaskFlowAnswerNode 预览TaskFlow的答案节点
func (imp TaskConfigImp) PreviewTaskFlowAnswerNode(ctx context.Context, req *KEP.PreviewTaskFlowAnswerNodeReq) (
	*KEP.PreviewTaskFlowAnswerNodeRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("PreviewTaskFlowAnswerNode|REQ|%s|%v", sid, req)
	resp, err := taskflow.PreviewTaskFlowAnswerNode(ctx, req)
	log.Infof("PreviewTaskFlowAnswerNode|RESP|%s|%v", sid, err)
	return resp, err
}

// PreviewAnswerNodeDocument 预览答案节点的知识文档
func (imp TaskConfigImp) PreviewAnswerNodeDocument(ctx context.Context, req *KEP.PreviewAnswerNodeDocumentReq) (
	*KEP.PreviewAnswerNodeDocumentRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("PreviewAnswerNodeDocument|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		rsp, err := taskflow.PreviewAnswerNodeDocument(ctx, req)
		log.Infof("PreviewAnswerNodeDocument|RSP|%s|%v|%v", sid, rsp, err)
		return rsp, err
	}

	err := errors.BadRequestError(gve.Error())
	log.Warnf("PreviewAnswerNodeDocument|RSP|%s|%v", sid, err)
	return nil, err
}

// CopyTaskFlow 复制任务流
func (imp TaskConfigImp) CopyTaskFlow(ctx context.Context, req *KEP.CopyTaskFlowReq) (*KEP.CopyTaskFlowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CopyTaskFlow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		rsp, err := taskflow.CopyTaskFlow(ctx, req)
		log.Infof("CopyTaskFlow|RSP|%s|%v|%v", sid, rsp, err)
		return rsp, err
	}

	err := errors.BadRequestError(gve.Error())
	log.Warnf("CopyTaskFlow|RSP|%s|%v", sid, err)
	return nil, err
}
