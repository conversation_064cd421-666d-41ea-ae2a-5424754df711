// bot-task-config-server
//
// @(#)var-imp.go  星期二, 六月 25, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/varparam"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/asaskevich/govalidator"
)

// CreateVar 创建变量
func (imp TaskConfigImp) CreateVar(ctx context.Context, req *KEP.CreateVarReq) (*KEP.CreateVarRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.InfoContextf(ctx, "CreateVar start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := varparam.CreateVar(ctx, req)
		log.InfoContextf(ctx, "RESP|CreateVar|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx),
			err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "E|RESP|CreateVar|%s|%v", sid, err0)
	return nil, err0
}

// UpdateVar 更新变量
func (imp TaskConfigImp) UpdateVar(ctx context.Context, req *KEP.UpdateVarReq) (*KEP.UpdateVarRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.InfoContextf(ctx, "UpdateVar start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := varparam.UpdateVar(ctx, req)
		log.InfoContextf(ctx, "RESP|UpdateVar|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx),
			err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "E|RESP|UpdateVar|%s|%v", sid, err0)
	return nil, err0
}

// DeleteVar 删除变量
func (imp TaskConfigImp) DeleteVar(ctx context.Context, req *KEP.DeleteVarReq) (*KEP.DeleteVarRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.InfoContextf(ctx, "DeleteVar start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := varparam.DeleteVar(ctx, req)
		log.InfoContextf(ctx, "RESP|DeleteVar|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx),
			err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "E|RESP|DeleteVar|%s|%v", sid, err0)
	return nil, err0
}

// DescribeVar 获取变量详情
func (imp TaskConfigImp) DescribeVar(ctx context.Context, req *KEP.DescribeVarReq) (*KEP.DescribeVarRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.InfoContextf(ctx, "DescribeVar start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := varparam.DescribeVar(ctx, req)
		log.InfoContextf(ctx, "RESP|DescribeVar|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx),
			err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "E|RESP|DescribeVar|%s|%v", sid, err0)
	return nil, err0
}

// GetVarList 获取变量列表
func (imp TaskConfigImp) GetVarList(ctx context.Context, req *KEP.GetVarListReq) (*KEP.GetVarListRsp, error) {
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.InfoContextf(ctx, "GetVarList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		resp, err := varparam.GetVarList(ctx, req)
		log.InfoContextf(ctx, "RESP|GetVarList|%s|%v|%s|%v|%s", sid, resp, clues.GetTrackDataJSON(ctx),
			err, time.Since(t0))
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "E|RESP|GetVarList|%s|%v", sid, err0)
	return nil, err0
}

// GetSystemVarList 获取系统变量
func (imp TaskConfigImp) GetSystemVarList(ctx context.Context, req *KEP.GetSystemVarListReq) (
	*KEP.GetSystemVarListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetSystemVarList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
		// 业务处理
		rsp, err := varparam.GetSystemVarList(ctx, req)
		log.InfoContextf(ctx, "GetSystemVarList|RSP|%s|%v|%v", sid, rsp, err)
		return rsp, err
	}

	err := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetSystemVarList|RSP|%s|%v", sid, err)
	return nil, err
}
