package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// SaveAgentWorkflow 保存AgentWorkflow
func (imp TaskConfigImp) SaveAgentWorkflow(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq) (*KEP_WF.SaveAgentWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "SaveAgentWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := pdl.SaveWorkflowPDL(ctx, req)
		log.InfoContextf(ctx, "SaveAgentWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("SaveAgentWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListAgentWorkflow Agent工作流列表
func (imp TaskConfigImp) ListAgentWorkflow(ctx context.Context, req *KEP_WF.ListAgentWorkflowReq) (*KEP_WF.ListAgentWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListAgentWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := pdl.ListWorkflowPDL(ctx, req)
		log.Infof("ListAgentWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListAgentWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetAgentWorkflowInfo 内部服务调用该接口，目前主会话会调用
func (imp TaskConfigImp) GetAgentWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq) (*KEP_WF.GetAgentWorkflowInfoRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("REQ|GetAgentWorkflowInfo|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		rsp, err := pdl.GetWorkflowInfo(ctx, req)
		log.Infof("GetAgentWorkflowInfo|RESP|%s|%v|%v", sid, rsp, err)

		return rsp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetAgentWorkflowInfo|%s|%v", sid, err0)
	return nil, err0
}

// GetAgentWorkflowDetail 获取某个Agent工作流的详细信息
func (imp TaskConfigImp) GetAgentWorkflowDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq) (*KEP_WF.GetAgentWorkflowDetailRsp, error) {
	log.InfoContextf(ctx, "GetAgentWorkflowDetail|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := pdl.GetWorkflowPDLDetail(ctx, req)
		log.InfoContextf(ctx, "GetAgentWorkflowDetail|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetAgentWorkflowDetail|BadRequest|%v", err0)
	return nil, err0
}

// GetAgentWorkflowState 获取某个Agent工作流状态
func (imp TaskConfigImp) GetAgentWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq) (*KEP_WF.GetAgentWorkflowStateRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GetAgentWorkflowState|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := pdl.GetWorkflowState(ctx, req)
		log.Infof("GetAgentWorkflowState|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GetAgentWorkflowState|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ConvertToAgentWorkflow 工作流画布转换成PDL
func (imp TaskConfigImp) ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq) (*KEP_WF.ConvertToAgentWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ConvertToAgentWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := pdl.ConvertToAgentWorkflow(ctx, req)
		log.Infof("ConvertToAgentWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ConvertToAgentWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// SwitchAgentWorkflowState 是否开启Agent工作流
func (imp TaskConfigImp) SwitchAgentWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq) (*KEP_WF.SwitchAgentWorkflowStateRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("SwitchAgentWorkflowState|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := pdl.SwitchWorkflowState(ctx, req)
		log.Infof("SwitchAgentWorkflowState|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("SwitchAgentWorkflowState|RESP|%s|%v", sid, err0)
	return nil, err0
}

func (imp TaskConfigImp) ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq) (*KEP_WF.ListPDLVersionRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListPDLVersion|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := pdl.ListPDLVersion(ctx, req)
		log.Infof("ListPDLVersion|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListPDLVersion|RESP|%s|%v", sid, err0)
	return nil, err0
}

func (imp TaskConfigImp) GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq) (*KEP_WF.GetPDLVersionDetailRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("GetPDLVersionDetail|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := pdl.GetPDLVersionDetail(ctx, req)
		log.Infof("GetPDLVersionDetail|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GetPDLVersionDetail|RESP|%s|%v", sid, err0)
	return nil, err0
}
