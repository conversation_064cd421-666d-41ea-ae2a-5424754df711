// bot-task-config-server
//
// @(#)workflow-example.go  星期五, 九月 27, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// ImportWorkflowExample 工作流示例问法导入
func (imp TaskConfigImp) ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq) (*KEP_WF.ImportWfExampleRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ImportWorkflowExample|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 日志信息填充
		ctx = log.WithContextFields(ctx, "RequestID", sid)
		// 业务处理
		resp, err := workflow.ImportWorkflowExample(ctx, req)
		log.InfoContextf(ctx, "ImportWorkflowExample|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "ImportWorkflowExample|RESP|%s|%v", sid, err0)
	return nil, err0
}

// CreateWorkflowExample 创建工作流示例问法
func (imp TaskConfigImp) CreateWorkflowExample(ctx context.Context,
	req *KEP_WF.CreateWorkflowExampleReq) (*KEP_WF.CreateWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("CreateWorkflowExample|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.CreateWorkflowExample(ctx, req)
		log.Infof("CreateWorkflowExample|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CreateWorkflowExample|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowExample 获取工作流示例列表
func (imp TaskConfigImp) ListWorkflowExample(ctx context.Context,
	req *KEP_WF.ListWorkflowExampleReq) (*KEP_WF.ListWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListWorkflowExample|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflowExample(ctx, req)
		log.Infof("ListWorkflowExample|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowExample|RESP|%s|%v", sid, err0)
	return nil, err0
}

// UpdateWorkflowExample 更新工作流示例问法
func (imp TaskConfigImp) UpdateWorkflowExample(ctx context.Context,
	req *KEP_WF.UpdateWorkflowExampleReq) (*KEP_WF.UpdateWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateWorkflowExample|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.UpdateWorkflowExample(ctx, req)
		log.Infof("UpdateWorkflowExample|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("UpdateWorkflowExample|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteWorkflowExample 删除工作流示例问法
func (imp TaskConfigImp) DeleteWorkflowExample(ctx context.Context,
	req *KEP_WF.DeleteWorkflowExampleReq) (*KEP_WF.DeleteWorkflowExampleRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteWorkflowExample|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DeleteWorkflowExample(ctx, req)
		log.Infof("DeleteWorkflowExample|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DeleteWorkflowExample|RESP|%s|%v", sid, err0)
	return nil, err0
}
