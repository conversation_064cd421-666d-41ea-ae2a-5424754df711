// bot-task-config-server
//
// @(#)workflow-example.go  星期五, 九月 27, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package service

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// GetParameterList  获取参数列表
func (imp TaskConfigImp) GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq) (*KEP_WF.GetParameterListResp, error) {
	log.InfoContextf(ctx, "GetParameterList|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetParameterList(ctx, req)
		log.InfoContextf(ctx, "GetParameterList|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetParameterList|BadRequest|%v", err0)
	return nil, err0
}

// CreateParameter 新建参数
func (imp TaskConfigImp) CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq) (*KEP_WF.CreateParameterResp, error) {
	log.InfoContextf(ctx, "CreateParameter|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.CreateParameter(ctx, req)
		log.InfoContextf(ctx, "CreateParameter|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "CreateParameter|BadRequest|%v", err0)
	return nil, err0
}

// UpdateParameter 更新参数信息
func (imp TaskConfigImp) UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq) (*KEP_WF.UpdateParameterResp, error) {
	log.InfoContextf(ctx, "UpdateParameter|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.UpdateParameter(ctx, req)
		log.InfoContextf(ctx, "UpdateParameter|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "UpdateParameter|BadRequest|%v", err0)
	return nil, err0
}

// DeleteParameter 删除参数信息
func (imp TaskConfigImp) DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq) (*KEP_WF.DeleteParameterResp, error) {
	log.InfoContextf(ctx, "DeleteParameter|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DeleteParameter(ctx, req)
		log.InfoContextf(ctx, "DeleteParameter|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "DeleteParameter|BadRequest|%v", err0)
	return nil, err0
}

// GetBotNodeParameterList 获取应用下所有参数列表
func (imp TaskConfigImp) GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq) (*KEP_WF.GetBotNodeParameterListResp, error) {
	log.InfoContextf(ctx, "GetBotNodeParameterList|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetBotNodeParameterList(ctx, req)
		log.InfoContextf(ctx, "GetBotNodeParameterList|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetBotNodeParameterList|BadRequest|%v", err0)
	return nil, err0
}
