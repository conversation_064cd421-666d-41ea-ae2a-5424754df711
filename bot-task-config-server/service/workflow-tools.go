/*
 * 2024-11-19
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON><PERSON>@Tencent. All rights reserved.
 *
 */

package service

import (
	"context"
	"net/http"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	v262 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator/v262"
	v270 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator/v270"
	v275 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator/v275"
	v285 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator/v285"
	v290 "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator/v290"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	jsoniter "github.com/json-iterator/go"
)

const (
	// Step 捞数据步长
	Step = 100
)

// KnowledgeNodeData [v2.9.0] 刷数据： 知识检索节点&大模型知识问答节点 适配共享知识库
func (imp TaskConfigImp) KnowledgeNodeData(w http.ResponseWriter, r *http.Request) {
	v290.KnowledgeNodeData(w, r)
}

// IterationNodeData [v2.8.5] 刷数据：将工作流画布中的 循环节点新定义的的全部遍历参数设成Input和Input.Index处理
func (imp TaskConfigImp) IterationNodeData(w http.ResponseWriter, r *http.Request) {
	v285.IterationNodeData(w, r)
}

// PopulateNodeData [v2.7.5] 刷数据：将应用配置中的 生成模型刷到参数提取节点、搜索策略刷到知识检索节点和大模型问答节点
func (imp TaskConfigImp) PopulateNodeData(w http.ResponseWriter, r *http.Request) {
	v275.PopulateNodeData(w, r)
}

// CheckWorkflowEnable [v2.7.1] 检查-workflow可用状态在db、redis、vector是否一致
func (imp TaskConfigImp) CheckWorkflowEnable(w http.ResponseWriter, r *http.Request) {
	v270.CheckWorkflowEnable(w, r)
}

// SyncWorkflowExamToVector [v2.7.0] 将示例问法数据同步Vector
func (imp TaskConfigImp) SyncWorkflowExamToVector(w http.ResponseWriter, r *http.Request) {
	v270.SyncWorkflowExamToVector(w, r)
}

// SyncWorkflowEnableToRedis [v2.7.0] 刷数据： 将数据的可用性加到redis中
func (imp TaskConfigImp) SyncWorkflowEnableToRedis(w http.ResponseWriter, r *http.Request) {
	v270.SyncWorkflowEnableToRedis(w, r)
}

// AddEndNode [v2.7.0] 刷数据： 增加结束节点
func (imp TaskConfigImp) AddEndNode(w http.ResponseWriter, r *http.Request) {
	v270.AddEndNode(w, r)
}

// SyncWorkflowExampleToRds 刷示例问法数据：
// 修复导入的工作流示例问法没有同步到redis sandbox
func (imp TaskConfigImp) SyncWorkflowExampleToRds(w http.ResponseWriter, r *http.Request) {
	v262.SyncWorkflowExampleToRds(w, r)
}

// CheckWorkflowJson 检查工作流JSON
func (imp TaskConfigImp) CheckWorkflowJson(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	//ctx = clues.NewTrackContext(ctx)
	//defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	_ = r.ParseForm()
	log.InfoContextf(ctx, "CheckWorkflowJson|r:%+v", r)
	log.InfoContextf(ctx, "CheckWorkflowJson|Form:%+v", r.Form)
	traceID := encode.GenerateSessionID()
	//pkg.WithTraceID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)

	totalCount, err := getWorkflowCount(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "CheckWorkflowJson|getWorkflowCount|err:%+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		respStr, _ := jsoniter.Marshal(err)
		_, _ = w.Write(respStr)
		return
	}

	pageSize := Step

	for currentPage := 1; ; currentPage++ {
		workflows, err := getWorkflows(ctx, currentPage, pageSize)
		if err != nil {
			log.ErrorContextf(ctx, "CheckWorkflowJson|getWorkflows|err:%+v", err)
			w.WriteHeader(http.StatusInternalServerError)
			respStr, _ := jsoniter.Marshal(err)
			_, _ = w.Write(respStr)
			return
		}
		for _, workflow := range workflows {
			_, err := protoutil.JsonToWorkflow(workflow.DialogJsonDraft)
			if err != nil {
				log.ErrorContextf(ctx, "CheckWorkflowJson|[%s]|err:%+v", workflow.WorkflowID, err)
			}
		}
		if currentPage*pageSize >= totalCount {
			break
		}
	}

	w.WriteHeader(http.StatusOK)

}

func getWorkflowCount(ctx context.Context) (int, error) {
	var total int64
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	res := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0").
		Order("f_update_time DESC").
		Count(&total)
	if res.Error != nil {
		log.InfoContextf(ctx, "getWorkflowCount|err:%+v", res.Error)
		return 0, res.Error
	}
	log.InfoContextf(ctx, "getWorkflowCount|total:%d", total)
	return int(total), nil
}

func getWorkflows(ctx context.Context, currentPage, pageSize int) ([]*entity.Workflow, error) {
	workflows := make([]*entity.Workflow, 0)
	offset := (currentPage - 1) * pageSize
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	res := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0").
		Order("f_update_time DESC").
		Offset(offset).
		Limit(pageSize).
		Scan(&workflows)
	if res.Error != nil {
		log.InfoContextf(ctx, "getWorkflows|err:%+v", res.Error)
		return nil, res.Error
	}
	log.InfoContextf(ctx, "getWorkflows|workflows:%d", len(workflows))
	return workflows, nil
}
