// ############################################################################
//
// PB 来源 -> https://git.woa.com/dialogue-platform/bot-config/bot-task-config-server.git
//
// ############################################################################

// KEP.bot-task-config-server
//
// @(#)task-config.proto  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.


// ####################################################################################################### //
//                                                                                                         //
//     PB 文件 [task-config.proto] 请移步到                                                                //
//                                                                                                         //
//     https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/task-config.proto    //
//                                                                                                         //
// ####################################################################################################### //


syntax = "proto3";

package trpc.KEP.bot_task_config_server;

option go_package="git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP/bot_task_config_server";

// 服务全名: KEP.bot-task-config-server.task-config
//
// 北极星传送门 | Development
// http://polaris.oa.com/#/services/info/instance/Development/trpc.KEP.bot-task-config-server.task-config
//
// 北极星传送门 | Test
// http://polaris.oa.com/#/services/info/instance/Test/trpc.KEP.bot-task-config-server.task-config
//
// 北极星传送门 | Pre-release
// http://polaris.oa.com/#/services/info/instance/Pre-release/trpc.KEP.bot-task-config-server.task-config
//
// 北极星传送门 | Production
// http://polaris.oa.com/#/services/info/instance/Production/trpc.KEP.bot-task-config-server.task-config
service TaskConfig {

    // Echo 方法用于测试服务是否部署成功
    // rpc Echo (EchoReq) returns (EchoResp) {}

	// ############################################################################################
	//
	// 请在本区域增加新的接口实现
	//
	// ############################################################################################
}

