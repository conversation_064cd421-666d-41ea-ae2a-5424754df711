// Package binding 校验
package binding

import (
	"git.woa.com/dialogue-platform/go-comm/json0"
	"github.com/go-playground/validator/v10"
)

// BindJSON 解json为interface，并做相关校验
func BindJSON(s string, obj interface{}) error {
	if err := json0.UnmarshalStr(s, obj); err != nil {
		return err
	}

	return New().Struct(obj)
}

// New 新建一个校验器
func New() *validator.Validate {
	v := validator.New()

	v.SetTagName("binding")
	_ = v.RegisterValidation("key", isKey)
	return v
}
