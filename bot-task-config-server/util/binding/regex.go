// Package binding 规则
package binding

import (
	"regexp"

	"github.com/go-playground/validator/v10"
)

const (
	keyRegexString = "^[a-zA-Z_][a-zA-Z0-9_]*$"
)

var (
	keyRegex = regexp.MustCompile(keyRegexString)
)

// isKey is the validation function for validating if the current field's value is a valid identifier key.
func isKey(fl validator.FieldLevel) bool {
	return keyRegex.MatchString(fl.Field().String())
}
