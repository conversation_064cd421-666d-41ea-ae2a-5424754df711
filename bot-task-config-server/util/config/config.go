// KEP.bot-task-config-server
//
// @(#)config.go  December 07, 2023
// Copyright(c) 2023, boy<PERSON><PERSON>@Tencent. All rights reserved.

// Package config TODO
package config

import (
	"context"
	"fmt"
	"time"

	tconfig "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/config"
	"git.woa.com/dialogue-platform/go-comm/json0"
)

// MainConfig 定义 main.yaml 的结构
type MainConfig struct {
	Cos                    Cos                   `yaml:"cos"`       // cos配置
	Storage                Storage               `yaml:"storage"`   // 存储
	Category               Category              `yaml:"category"`  // 分类配置
	TaskFlow               TaskFlow              `yaml:"task_flow"` // 任务流配置
	Workflow               Workflow              `yaml:"workflow"`  // 工作流配置
	RefreshWorkflow        RefreshWorkflow       `yaml:"refresh_workflow"`
	VerifyTaskFlow         VerifyTaskFlow        `yaml:"verify_task_flow"` // 校验配置
	VerifyWorkflow         VerifyWorkflow        `yaml:"verify_workflow"`  // 工作流校验配置
	Tasks                  map[string]Task       `yaml:"tasks"`
	TaskPrefix             string                `yaml:"task_prefix"`
	TaskFetchNum           uint32                `yaml:"task_fetch_num"`
	TaskFetchTimeout       time.Duration         `yaml:"task_fetch_timeout"`
	TaskFetchPeriod        time.Duration         `yaml:"task_fetch_period"`
	TaskMaxTimeout         time.Duration         `yaml:"task_max_timeout"` // 任务最大超时时间，超过告警
	DataSyncTask           DataSyncTask          `yaml:"data_sync_task"`
	VectorGroup            VectorGroup           `yaml:"vector_group"`
	WorkflowVectorGroup    WorkflowVectorGroup   `yaml:"workflow_vector_group"`
	LlmSupportWorkflow     map[string][]string   `yaml:"llm_support_workflow"` // 模型支持工作流情况， V2.7新加
	WorkflowNodeLlmSupport map[string]NodeConfig `yaml:"workflow_node_llm_support"`
	LLM                    LLMConf               `yaml:"llm"` // 加载llm配置
	ApiPathSafety          ApiPathSafety         `yaml:"api_path_safety"`
	ToolNodePathSafety     ToolNodePathSafety    `yaml:"tool_node_path_safety"`
	AppShare               AppShare              `yaml:"app_share"`      // 生成分享链接的基础信息
	Entry                  Entry                 `yaml:"entry"`          // 实体词条配置
	ExampleCorpus          ExampleCorpus         `yaml:"example_corpus"` // 示例用法
	CloudAPIs              CloudAPIs             `yaml:"cloud_apis"`     // 云API

	TaskFlowConfig           TaskFlowConfig           `yaml:"task_flow_config"`             // 对话树协议版本升级配置
	RoleDefaultTemplates     []RoleDefaultTemplate    `yaml:"roleDefaultTemplate"`          // 角色预设模板
	PromptWordTemplates      []PromptWordTemplate     `yaml:"promptWordTemplate"`           // 提示词模板
	SystemVarTemplates       []SystemVarTemplate      `yaml:"systemVarTemplate"`            // 系统变量
	LlmInputNum              []LlmInputNum            `yaml:"llmInputNum"`                  // 模型长度对应字符输入限制
	SyncEntityEntriesToRedis SyncEntityEntriesToRedis `yaml:"sync_entity_entries_to_redis"` // 属性实体词条数据到redis

	ContentCheck bool        `yaml:"content_check"` // 安全审核开关
	Finance      FinanceConf `yaml:"finance"`       // 加载finance配置

	VerifyParameter VerifyParameter `yaml:"verify_parameter"` // 参数节点相关配置

	FlowDeleteConfig FlowDeleteConfig `yaml:"flow_delete_config"` // 工作流/任务删除配置

	PrivacyEnv bool `yaml:"privacy_env"` // 是否为私有化环境部署
	// # 有些项目部署工作流，但会依赖task-config服务，如果有工作流则需要通知dm，该参数为true，否则为false
	// 目前 API参数 t_var 被doc模块调用，所以CreateVar相关的接口，需要通过该标识区分是否通知DM
	WorkflowDmNotify bool `yaml:"workflow_dm_notify"`
	QidianSid        int  `yaml:"qidian_sid"` // 企点sid

	QidianClient string `yaml:"qidian_client"` // 企点client

	PDLConfig        PDLConfig        `yaml:"pdl_config"`         // pdl配置
	PDLConvertConfig PDLConvertConfig `yaml:"pdl_convert_config"` // 转换pdl配置

	AsyncWorkflowConfig AsyncWorkflowConfig `yaml:"async_workflow_config"` // 异步工作流配置
}

// FlowDeleteConfig 知识删除配置
type FlowDeleteConfig struct {
	// NeedDeleteTables DB需要删除的表名列表
	// map:[string]bool, key:表名 value:表删除handler name
	// 	t_doc: COMMON
	// 	t_doc_segment: CORP_ROBOT_ID
	// 	...
	NeedDeleteTables map[string]string `yaml:"need_delete_tables"`
	DeleteBatchSize  int               `yaml:"delete_batch_size"` // 批次删除最大数量
	QueryBatchSize   int               `yaml:"query_batch_size"`  // 批次查询最大数量
}

// Task 任务配置
type Task struct {
	Runners           int           `yaml:"runners"`             // 执行协程数
	RetryWaitTime     time.Duration `yaml:"retry_wait_time"`     // 重试等待时间
	MaxRetry          uint          `yaml:"max_retry"`           // 最大重试次数
	Timeout           time.Duration `yaml:"timeout"`             // 超时时间
	FailTimeout       time.Duration `yaml:"fail_timeout"`        // 失败回调超时时间
	Delay             time.Duration `yaml:"delay"`               // 启动延迟
	Batch             uint          `yaml:"batch"`               // 并发批次数, 比如 Batch: 3, BatchSize: 10, 则并发处理 3 个批次, 每个批次内有 10 条待处理记录
	BatchSize         uint          `yaml:"batch_size"`          // 每批次大小, 默认为 0, 按 Batch 计算每批次大小
	StoppedResumeTime time.Duration `yaml:"stopped_resume_time"` // 任务停止后可以恢复的时效
}

// Cos cos配置
//type Cos struct {
//	BucketUrl  string        `yaml:"bucket_url"` // cos地址
//	SecretID   string        `yaml:"secret_id"`  // 秘钥ID
//	SecretKey  string        `yaml:"secret_key"` // 秘钥key
//	ExpireTime time.Duration `yaml:"expire_time"`
//}

// Storage 对象存储配置
type Storage struct {
	Type  string `yaml:"type"`
	Cos   Cos    `yaml:"cos"`
	MinIO MinIO  `yaml:"minio"`
}

// Cos cos存储
type Cos struct {
	SecretID   string        `yaml:"secret_id"`
	SecretKey  string        `yaml:"secret_key"`
	Region     string        `yaml:"region"`
	AppID      string        `yaml:"app_id"`
	Bucket     string        `yaml:"bucket"`
	Domain     string        `yaml:"domain"`
	ExpireTime time.Duration `yaml:"expire_time"`
}

// MinIO minio存储
type MinIO struct {
	SecretID    string        `yaml:"secret_id"`
	SecretKey   string        `yaml:"secret_key"`
	Region      string        `yaml:"region"`
	Bucket      string        `yaml:"bucket"`
	ExpireTime  time.Duration `yaml:"expire_time"`
	STSEndpoint string        `yaml:"sts_endpoint"` // sts地址:用于分配临时密钥
	EndPoint    string        `yaml:"end_point"`    // minio服务地址
	UseHTTPS    bool          `yaml:"use_https"`    // 是否使用https
}

// LLMConf LLM configuration
type LLMConf struct {
	ModelName        string        `yaml:"model_name"`
	RequestNodeQuery string        `yaml:"request_node_query"`
	AnswerNodeQuery  string        `yaml:"answer_node_query"`
	DocTimeout       time.Duration `yaml:"doc_timeout"`
}

// FinanceConf configuration
type FinanceConf struct {
	Disabled bool `yaml:"disabled"`
}

// Category 分类配置
type Category struct {
	CategoryNameLen    int `yaml:"category_name_len"`     // 分类名称长度
	CategoryNameMinLen int `yaml:"category_name_min_len"` // 分类名称长度
	CategoryNodeLimit  int `yaml:"category_node_limit"`   //分类总节点数
}

// VerifyTaskFlow 对话流程校验配置
type VerifyTaskFlow struct {
	TaskFlowLimit               int    `yaml:"task_flow_limit"`              // 任务流个数限制
	Version                     string `yaml:"version"`                      // 任务流画布版本
	MinIntentNameLen            int    `yaml:"min_intent_name_len"`          // 任务流程名称最小长度
	IntentNameLen               int    `yaml:"intent_name_len"`              // 任务流程名称长度
	IntentDescLen               int    `yaml:"intent_desc_len"`              // 意图描述
	UiNodeTotal                 int    `yaml:"ui_node_total"`                // 任务流程画布JSON节点总数
	UiNodeNameMax               int    `yaml:"ui_node_name_max"`             // 节点名称的最大长度
	UiNodeNameMin               int    `yaml:"ui_node_name_min"`             // 节点名称的最小长度
	VarParamsCountMax           int    `yaml:"var_params_count_max"`         // 变量数量不超过1000
	VarParamsTextMax            int    `yaml:"var_params_text_max"`          // 自定义参数字符限制不超过50
	VarParamsDescTextMax        int    `yaml:"var_params_desc_text_max"`     // 自定义参数描述字符限制不超过1000
	VarParamsDefaultValueMax    int    `yaml:"var_params_default_value_max"` // 自定义参数默认值字符限制不超过1000
	RequiredInfoNameMax         int    `yaml:"required_info_name_max"`       // 必要信息名称最大长度
	RequiredProbeCustomMax      int    `yaml:"required_probe_custom_max"`    // 自定义话术/必要信息询问语
	RequiredProbePreviewMax     int    `yaml:"required_probe_preview_max"`   // 追问话术最大长度
	AnswerNodeCustomMax         int    `yaml:"answer_node_custom_max"`       // 自定义答案节点长度
	AnswerNodePromptMax         int    `yaml:"answer_node_prompt_max"`       // 答案节点智能回复自定义提示词长度
	OptionContentsTextMax       int    `yaml:"option_contents_text_max"`     // 选项卡内容长度限制
	APIPathTextMax              int    `yaml:"api_path_text_max"`            // API节点解析路径长度限制
	APINodeRequiredInfoMax      int    `yaml:"api_node_required_info_max"`   // API节点必要信息(入参)长度
	APINodeRequiredInfoMin      int    `yaml:"api_node_required_info_min"`
	APIRequestParamSubMaxDepth  int    `yaml:"api_request_param_sub_max_depth"` // API节点 入参的最大深度
	APIReqSlotFormatDescMax     int    `yaml:"api_req_slot_format_desc_max"`    // 实体来源格式描述最大长度
	APIRspParamsMax             int    `yaml:"api_rsp_params_max"`              // API节点出参最大数量
	APIRspParamsMin             int    `yaml:"api_rsp_params_min"`
	APIRspParamMaxDepth         int    `yaml:"api_rsp_param_max_depth"`     // API节点出参的层级限制
	APIRspSubParamMaxCount      int    `yaml:"api_rsp_sub_param_max_count"` // API节点子出参的最大限制
	OptionContentsMax           int    `yaml:"option_contents_max"`         // 选项最大数量限制
	OptionContentsMin           int    `yaml:"option_contents_min"`
	NodeBranchCountMax          int    `yaml:"node_branch_count_max"` // 单个节点条件分支数最大数
	NodeBranchCountMin          int    `yaml:"node_branch_count_min"`
	ConditionCountMax           int    `yaml:"condition_count_max"` // 组合条件数量, 10
	ConditionCountMin           int    `yaml:"condition_count_min"`
	ConditionContainsValueMax   int    `yaml:"condition_contains_value_max"` // 组合条件分支为"包含"，Value实体的个数
	ConditionContainsValueMin   int    `yaml:"condition_contains_value_min"`
	ConditionInValueMax         int    `yaml:"condition_in_value_max"`           // 组合条件分支为"属于"，Value实体的个数
	APINodeValidJSONFieldRex    string `yaml:"api_node_valid_json_field_rex"`    // API节点出入参中 JSON字段合法性的正则 ^[a-zA-Z0-9\_\-\:]+$
	BranchConditionMaxDepth     int    `yaml:"branch_condition_max_depth"`       // 分支中条件深度的最大值, 2
	APINodeHeadersMaxCount      int    `yaml:"api_node_headers_max_count"`       // API节点Header的个数，10
	APINodeHeadersParamMaxDepth int    `yaml:"api_node_headers_param_max_depth"` // API节点Header的Param深度
	APINodeFixedValueMaxCount   int    `yaml:"api_node_fixed_value_max_count"`   // API节点固定值的最大长度，2000
	APIParamValidJSONFieldRex   string `yaml:"api_param_valid_json_field_rex"`   // API变量合法性的正则 ^[a-zA-Z\_\-]+$
}

// VerifyWorkflow 工作流程校验配置
type VerifyWorkflow struct {
	Version                       string  `yaml:"version"`                            // 工作流画布版本
	WorkflowNameLen               int     `yaml:"workflow_name_len"`                  // 工作流名称长度
	MinWorkflowLen                int     `yaml:"min_workflow_len"`                   // 工作流名称最小长度
	WorkflowLimit                 int     `yaml:"workflow_limit"`                     // 工作流个数限制
	NodeNameMaxLen                int     `yaml:"node_name_max_len"`                  // 节点名称长度限制， 20
	NodeInputParamMax             int     `yaml:"node_input_param_max"`               // 每个节点的输入参数个数限制， 50个
	NodeInputParamNameLen         int     `yaml:"node_input_param_name_len"`          // 输入参数名字长度限制， 30个
	NodeSubInputParamLen          int     `yaml:"node_sub_input_param_len"`           // 输入子入参数的数组长度 10 个
	NodeSubInputParamDepth        int     `yaml:"node_sub_input_param_depth"`         // 输入子入参数的深度 10
	NodeInputParamNamePattern     string  `yaml:"node_input_param_name_pattern"`      // 输入参数名字要求正则表达式
	NodeInputParamDefaultValueLen int     `yaml:"node_input_param_default_value_len"` // 输入参数默认值限制，1000
	IntentDescLen                 int     `yaml:"intent_desc_len"`                    // 意图描述长度, 200
	UiNodeTotal                   int     `yaml:"ui_node_total"`                      // 工作流画布JSON节点总数 500个
	NodePromptMaxLen              int     `yaml:"node_prompt_max_len"`                // 节点中prompt的长度， 1000个
	NodeParameterMaxLen           int     `yaml:"node_parameter_max_len"`             // 每个节点最多支持配置10个参数 10个
	LLMNodeTemperatureMax         float32 `yaml:"llm_node_temperature_max"`           // 0~1
	LLMNodeTopPMax                float32 `yaml:"llm_node_top_p_max"`                 // 0~1
	DocBizIdsMax                  int     `yaml:"doc_biz_ids_max"`                    // 知识检索及大模型知识问答两节点文档数量
	DocRecallCountMin             int32   `yaml:"doc_recall_count_min"`               // 文档召回数量,  最小3, 最大10
	DocRecallCountMax             int32   `yaml:"doc_recall_count_max"`               // 文档召回数量Max,  最小3, 最大10
	DocConfidenceMax              float32 `yaml:"doc_confidence_max"`                 // 文档检索匹配度Max, 0.01 ~ 0.99
	QARecallCountMin              int32   `yaml:"qa_recall_count_min"`                // 问答召回数量， 最小2, 最大5
	QARecallCountMax              int32   `yaml:"qa_recall_count_max"`                // 问答召回数量Max， 最小2, 最大5
	QAConfidenceMax               float32 `yaml:"qa_confidence_max"`                  // 问答检索匹配度Max, 0.01 ~ 0.99
	KnowledgeTagMax               int     `yaml:"knowledge_tag_max"`                  // 知识检索和知识Max 10
	NodeTagMaxLen                 int     `yaml:"node_tag_max_len"`                   // 标签节点最多支持配置标签的个数， 10个
	NodeTagExampleMaxLen          int     `yaml:"node_tag_example_max_len"`           // 支持输入标签的取值示例，最多支持50个
	NodeToolRespMaxDepth          int     `yaml:"node_tool_resp_max_depth"`           // 工具节点resp的最大深度 11 ，由于多了一个output
	NodeToolMaxCount              int     `yaml:"node_tool_max_count"`                // 工具节点入参的个数 50
	NodeToolSubMaxDepth           int     `yaml:"node_tool_sub_max_depth"`            // 工具节点入参的深度 5
	NodeToolValidJSONFieldRex     string  `yaml:"node_tool_valid_json_field_rex"`     // 工具节点出入参中 JSON字段合法性的正则 ^[a-zA-Z0-9_]+$
	NodeToolParamNameMax          int     `yaml:"node_tool_name_max"`                 // 工具节点参数名称最大长度 40
	UserInputMaxLen               int     `yaml:"user_input_max_len"`                 // 用户输入的"固定值"的长度， 120
	LogicGroupCountMax            int     `yaml:"logic_group_count_max"`              // 条件组最多个数限制，50个
	AnswerMaxLen                  int     `yaml:"answer_max_len"`                     // 回复节点内容最大, 500
	CodeNodeInputLen              int     `yaml:"code_node_input_len"`                // 代码节点输入参数个数上限, 暂定99
	CodeNodeOutputLen             int     `yaml:"code_node_output_len"`               // 代码节点输输出参数个数上限, 暂定99
	CodeNodeCodeLen               int     `yaml:"code_node_code_len"`                 // 代码节点代码文本长度上限, 暂定4096
	//LLMNodeMaxTokensMax       int32   `yaml:"llm_node_max_tokens_max"`        // 0~4000

	WorkflowFeedBackDescMaxLen int `yaml:"feedback_desc_max_len"` // 代码节点代码文本长度上限, 暂定4096

	OptionMaxItemCount            int      `yaml:"option_max_item_count"`             // ←---[v2.7.0]新增  选项卡节点中选项的最大个数 21个(20 + else 占一个)
	OptionMaxQuestionLen          int      `yaml:"option_max_question_len"`           // 选项卡问题最大长度  1000
	OptionItemMaxLen              int      `yaml:"option_item_max_len"`               // 选项卡选项最大长度  200
	IntentPrompt                  int      `yaml:"intent_prompt"`                     // 意图识别模型 - 待识别意图的内容  4000
	IterationInnerInputParamNames []string `yaml:"iteration_inner_input_param_names"` // 循环节点内置的"输入变量"

	VarAggregationMaxGroupCount int `yaml:"var_aggregation_max_group_count"` //  ←---[v2.7.5]新增 变量聚合节点中 聚合变量组 的最大个数  // 10
	VarAggregationMaxItemCount  int `yaml:"var_aggregation_max_item_count"`  // 变量聚合节点中 聚合变量组 中 聚合变量的最大个数 // 50

	RetryMax                int `yaml:"retry_max"`                  // 异常处理机制，最大重试次数	5
	RetryIntervalMax        int `yaml:"retry_interval_max"`         // 异常处理机制，最大重试时间间隔	60
	AbnormalOutputResultLen int `yaml:"abnormal_output_result_len"` // 异常处理机制，异常输出变量最大长度	5000

	WorkflowReferenceDepth int `yaml:"workflow_reference_depth"` // 工作流引用最大深度	5

	IntentMax int `yaml:"intent_max"` // 意图识别个数，21个(20 + else 占一个)

	DebuggedOrPublishedEnable bool `yaml:"debugged_or_published_enable"` // 是否开启真只针对调试通过和发布的工作流开关
}

// VerifyParameter 参数词条配置校验
type VerifyParameter struct {
	// 2.7.5 添加 https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121661417
	CustomAskEnableSid []string `yaml:"custom_ask_enable_sid"` // 第三方集成商支持自定义话术白名单
	CustomAskMaxLen    int      `yaml:"custom_ask_len"`        //  支持自定义话术长度
	ParamNameMaxLen    int      `yaml:"param_name_len"`        // 参数名长度
	ParamDescMaxLen    int      `yaml:"param_desc_len"`        // 参数描述长度
	ParamTypeSli       []string `yaml:"param_type_sli"`        // 参数类型枚举值
	ParamMaxNum        int      `yaml:"param_max_num"`         // 参数最大个数

	CorrectExampleNameMaxLen int `yaml:"correct_example_name_max_len"` // 正确示例名字最大长度
	CorrectExampleMaxNum     int `yaml:"correct_example_max_num"`      // 正确示例个数最大限制

	IncorrectExampleNameMaxLen int `yaml:"incorrect_example_name_max_len"` // 错误示例名最大长度
	IncorrectExampleMaxNum     int `yaml:"incorrect_example_max_num"`      // 错误示例个数最大限制
}

// TaskFlow 任务流配置
type TaskFlow struct {
	CategoryFileName      string   `yaml:"category_file_name"`       // 分类文件名
	TaskFlowFileName      string   `yaml:"task_flow_file_name"`      // 任务流程文件名
	IntentFileName        string   `yaml:"intent_file_name"`         // 意图文件名
	CorpusFileName        string   `yaml:"corpus_file_name"`         // 语料文件名
	SlotFileName          string   `yaml:"slot_file_name"`           // 槽位文件名
	IntentExampleFileName string   `yaml:"intent_example_file_name"` // 示例问法文件名
	VarParamsFileName     string   `yaml:"var_params_file_name"`     // 自定义变量文件名
	MaxRow                int      `yaml:"max_row"`                  // 导入最大行数
	MinRow                int      `yaml:"min_row"`                  // 导入最小行数
	ExportLimit           int      `yaml:"export_limit"`             // 导出限制
	HistoryLen            int      `yaml:"history_len"`              // 任务流程
	CategoryHead          []string `yaml:"category_head"`            // 类别文件头
	TaskFlowHead          []string `yaml:"task_flow_head"`           // 任务流文件头
	IntentHead            []string `yaml:"intent_head"`              // 意图流文件头
	CorpusHead            []string `yaml:"corpus_head"`              // 语料文件头
	SlotHead              []string `yaml:"slot_head"`                // 槽位文件头
	IntentExampleHead     []string `yaml:"intent_example_head"`      // 示例用法文件头
	VarParamsHead         []string `yaml:"var_params_head"`          // 自定义变量文件头
}

// DataSyncTask 数据同步任务
type DataSyncTask struct {
	ReleaseQueueLength   uint32 `yaml:"release_queue_length"`    // 发布队列长度
	ReleaseItemsPerBatch uint32 `yaml:"release_items_per_batch"` // 每批发布任务流数
}

// VectorGroup 向量库配置
type VectorGroup struct {
	Biz                string `yaml:"Biz"`                // 调用方系统标识
	Secret             string `yaml:"Secret"`             // 调用方分配的Secret
	OperationMaxIDs    int    `yaml:"OperationMaxIDs"`    // 向量接口单次批量操作最大ID数量
	EmbeddingModelName string `yaml:"EmbeddingModelName"` // Embedding模型名称
	TaskFlowUseVdb     bool   `yaml:"TaskFlowUseVdb"`     // 是否启用Vdb
	//WorkflowUseVdb     bool   `yaml:"WorkflowUseVdb"`     // 是否启用Vdb
}

// ApiPathSafety 画布ApiPath IP白名单
type ApiPathSafety struct {
	WhiteIp []string `yaml:"white_ip"`
}

// ToolNodePathSafety 工具节点 IP白名单
type ToolNodePathSafety struct {
	WhiteIp []string `yaml:"white_ip"`
}

// AppShare 应用分享地址
type AppShare struct {
	BaseUrl            string   `yaml:"base_url"`             // 分享的基础地址
	ImmediatelyBaseUrl string   `yaml:"immediately_base_url"` // 立即体验的地址
	ExpiredTime        uint32   `yaml:"expired_time"`         // 分享的过期时间，如果设置0标识不过期,按秒来算
	RedisPrefixKey     string   `yaml:"redis_prefix_key"`     // redis存储分享Key前缀
	ShareCodeLen       uint     `yaml:"share_code_len"`       // 分享码长度
	WhiteAppId         []string `yaml:"white_app_id"`         // 应用白名单，不重新生成分享码
}

// Entry 词条配置
type Entry struct {
	MaxEntityNameLen    int      `yaml:"MaxEntityNameLen"`    // 实体名最大长度
	MaxEntityExampleLen int      `yaml:"MaxEntityExampleLen"` // 实体示例最大长度
	MaxEntityExampleNum int      `yaml:"MaxEntityExampleNum"` // 实体示例最多允许的数量
	MaxEntityDescLen    int      `yaml:"MaxEntityDescLen"`    // 实体描述最大长度
	MaxEntryNameLen     int      `yaml:"MaxEntryNameLen"`     // 词条名最大长度
	MaxEntryNum         int      `yaml:"MaxEntryNum"`         // 词条最大数量限制
	MaxEntryAliasLen    int      `yaml:"MaxEntryAliasLen"`    // 词条别名最大长度限制
	MaxEntryAliasNum    int      `yaml:"MaxEntryAliasNum"`    // 词条别名最大数量限制
	EntryHead           []string `yaml:"EntryHead"`           // 词汇文件头
}

// ExampleCorpus 示例问法
type ExampleCorpus struct {
	IntentExampleContentLen   int `yaml:"intent_example_content_len"`   // 示例问法的长度 50
	IntentExampleMax          int `yaml:"intent_example_max"`           // 机器人下示例问法的个数限制 200
	ExampleEmbeddingBatchSize int `yaml:"example_embedding_batch_size"` // 每次批处理数
}

// CloudAPIs 云API
type CloudAPIs struct {
	SecretID    string `yaml:"secret_id"`
	SecretKey   string `yaml:"secret_key"`
	AccountHost string `yaml:"account_host"`
	Region      string `yaml:"region"`
	Version     string `yaml:"version"`
}

// TaskFlowConfig 对话树相关配置
type TaskFlowConfig struct {
	// TaskFlowProtoVersion_V1_7 升级为 KEP.TaskFlowProtoVersion_V2_1
	// [升级v1.7对话树的协议到 v2.1]
	UpgradeProtoVersionFrom17To21 From17To21 `yaml:"upgrade_proto_version_from_17_to_21"`
	// v2.4 刷数据 https://iwiki.woa.com/p/**********
	UpgradeProtoVersionFrom21To24 From21To24 `yaml:"upgrade_proto_version_from_21_to_24"`
	// 刷示例用法
	RefreshExampleToVector RefreshToVector `yaml:"refresh_example_to_vector"`
	// 处理评测环境与发布环境向量不一致
	RefreshDelExampleToVector RefreshDelExampleToVector `yaml:"refresh_del_example_to_vector"`
	// 处理删除的向量
	DelVectorFromDelExample DelVectorFromDelExample `yaml:"del_vector_from_del_example"`
}

// RoleDefaultTemplate 角色描述
type RoleDefaultTemplate struct {
	Name string `yaml:"name"` // 角色名称
	Text string `yaml:"text"` // 角色描述内容
}

// PromptWordTemplate 提示词
type PromptWordTemplate struct {
	Name string `yaml:"name"` // 场景名称
	Text string `yaml:"text"` // 场景内容
}

// SystemVarTemplate 系统变量
type SystemVarTemplate struct {
	Name string `yaml:"name"` // 变量名称
	Text string `yaml:"text"` // 变量描述
	//Type string              `yaml:"type"` // 变量类型
	//sub  []SystemVarTemplate `yaml:"sub"`  // 子集变量描述
}

// LlmInputNum ...
type LlmInputNum struct {
	Length string `yaml:"length"`
	Limit  int32  `yaml:"limit"`
}

// From17To21 从v1.7版本升级为v2.1的对话树
// TaskFlowProtoVersion_V1_7 升级为 KEP.TaskFlowProtoVersion_V2_1
// v1.7的协议： entity/ui/TaskFlow
// v2.1的协议： pb中的 tree.pb 文件
// 这部分为一次性逻辑，刷完线上数据后可以删掉；
type From17To21 struct {
	Enable        bool     `yaml:"enable"`          // 总开关
	Spec          string   `yaml:"spec"`            // cron
	UpgradeAllBot bool     `yaml:"upgrade_all_bot"` // 是否刷全量机器人
	WhiteBotList  []string `yaml:"white_bot_list"`  // upgrade_all_bot 是false的情况下，只刷白名单
}

// From21To24 从v2.1版本升级为v2.4的对话树
// TaskFlowProtoVersion_V2.1 升级为 KEP.TaskFlowProtoVersion_V2_4
// v1.7的协议： entity/ui/TaskFlow
// v2.1的协议： pb中的 tree.pb 文件
// V2.4 改动地方
// 这部分为一次性逻辑，刷完线上数据后可以删掉；
type From21To24 struct {
	Enable        bool     `yaml:"enable"`          // 总开关
	Spec          string   `yaml:"spec"`            // cron
	UpgradeAllBot bool     `yaml:"upgrade_all_bot"` // 是否刷全量机器人
	WhiteBotList  []string `yaml:"white_bot_list"`  // upgrade_all_bot 是false的情况下，只刷白名单
}

// RefreshDelExampleToVector ...
type RefreshDelExampleToVector struct {
	Enable             bool     `yaml:"enable"`                // 总开关
	Spec               string   `yaml:"spec"`                  // cron
	RefreshAll         bool     `yaml:"refresh_all"`           // 是否刷全量机器人
	WhiteIntentIdsList []string `yaml:"white_intent_ids_list"` // upgrade_all 是false的情况下，只刷白名单
}

// DelVectorFromDelExample ...
type DelVectorFromDelExample struct {
	Enable            bool     `yaml:"enable"`               // 总开关
	Spec              string   `yaml:"spec"`                 // cron
	RefreshAll        bool     `yaml:"refresh_all"`          // 是否刷全量机器人
	WhiteRobotIdsList []string `yaml:"white_robot_ids_list"` // upgrade_all 是false的情况下，只刷白名单
}

// RefreshToVector ...
type RefreshToVector struct {
	Enable        bool     `yaml:"enable"`          // 总开关
	Spec          string   `yaml:"spec"`            // cron
	Attempts      uint     `yaml:"attempts"`        // 重试次数
	Delay         uint     `yaml:"delay"`           // 重试时间
	MaxDelay      uint     `yaml:"maxDelay"`        // 两次之间最大重试时间
	RefreshAllBot bool     `yaml:"refresh_all_bot"` // 是否刷全量机器人
	WhiteBotList  []string `yaml:"white_bot_list"`  // upgrade_all_bot 是false的情况下，只刷白名单
	RetryAll      bool     `yaml:"retry_all"`       // 全部重试， 一般情况下不需要全部重试
}

// SyncEntityEntriesToRedis ...
type SyncEntityEntriesToRedis struct {
	Enable       bool     `yaml:"enable"`         // 总开关
	Spec         string   `yaml:"spec"`           // cron
	SyncAllBot   bool     `yaml:"sync_all_bot"`   // 是否刷全量机器人
	WhiteBotList []string `yaml:"white_bot_list"` // upgrade_all_bot 是false的情况下，只刷白名单
}

// PDLConfig pdl相关配置
type PDLConfig struct {
	VersionNumLimit int               `yaml:"version_num_limit"` // PDL版本数量限制，默认最多展示最近的20个版本
	ReasonLenLimit  int               `yaml:"reason_len_limit"`  // 原因字符长度限制
	ReasonDesc      map[string]string `yaml:"reason_desc"`       // pdl不可转换和转换失败等原因枚举值对应的描述
}

// PDLConvertConfig 画布转pdl的相关配置
type PDLConvertConfig struct {
	ConvertMethod       string               `yaml:"convert_method"`         // 转换方式 rule-工程规则转换 llm-大模型转换
	PDLTokenLenLimit    int                  `yaml:"pdl_token_len"`          // pdl token 长度限制 默认限制5k
	CondExpressLenLimit int                  `yaml:"cond_expression_len"`    // 条件表达式字符长度限制
	PluginToolBlackList []string             `yaml:"plugin_tool_black_list"` // 插件黑名单，包含这些插件的不允许转换PDL
	RuleConvert         PDLRuleConvertConfig `yaml:"rule_convert"`           // 工程规则转换配置
	LLMConvert          PDLLLMConvertConfig  `yaml:"llm_convert"`            // 大模型转换配置
}

type PDLRuleConvertConfig struct {
	ExtractFuncEnable bool `yaml:"extract_func_enable"` // 是否开启提取函数处理 开启后默认入度>=2 && 后继节点数量>=2的节点抽成函数
	FuncInDegreeNum   int  `yaml:"func_in_degree_num"`  // 封装函数的节点入度数量限制
	FuncSuccessorNum  int  `yaml:"func_successor_num"`  // 封装函数的节点后继节点数量限制

}

type PDLLLMConvertConfig struct {
	ProxyEnable        bool   `yaml:"proxy_enable"`         // 是否开启代理
	ProxyURL           string `yaml:"proxy_url"`            // 代理地址
	LLMApiKey          string `yaml:"llm_api_key"`          // 调用openai的api key
	LLMApiURL          string `yaml:"llm_api_url"`          // 调用openai的url
	ContentTruncateLen int    `yaml:"content_truncate_len"` // 内容截断长度
	ProcedurePromptTpl string `yaml:"procedure_prompt_tpl"` // prompt模板
}

// AsyncWorkflowConfig 异步工作流配置
type AsyncWorkflowConfig struct {
	WorkflowRunLimit        int `yaml:"workflow_run_limit"`         // 工作流运行实例个数用户级限制，默认10
	WorkflowRunOverallLimit int `yaml:"workflow_run_overall_limit"` // 工作流运行实例个数总体限制，默认100
	FallbackTimerInterval   int `yaml:"fallback_timer_interval"`    // 定时兜底间隔时间，单位秒，默认300
	RetryTimerInterval      int `yaml:"retry_timer_interval"`       // 定时重试UIN间隔时间，单位秒，默认10
	RunningRetryInterval    int `yaml:"running_retry_interval"`     // 运行中重试间隔时间，单位秒，默认600
}

var mainConfig MainConfig

// Init 初始化配置
func Init() (err error) {
	configCh, err := tconfig.Get("rainbow").Watch(context.Background(), "main.yaml")
	if err != nil {
		panic(err)
	}

	go func() {
		for range configCh {
			mainConfigTmp := MainConfig{}
			err := initMainConfig(&mainConfigTmp)
			if err != nil {
				continue
			}
			mainConfig = mainConfigTmp
		}
	}()

	err = initMainConfig(&mainConfig)
	if err != nil {
		return err
	}
	return nil
}

func initMainConfig(mconfig *MainConfig) (err error) {

	// main config
	err = config.UnmarshalYAMLFromRainbowWithPlaceholder("main.yaml", &mconfig)
	if err != nil {
		log.Infof("init main config err:%v", err)
		return err
	}

	log.Infof("\n\n--------------------------------------------------------------------------------\n" +
		fmt.Sprintf("mainConfig: %+v\n", mconfig) +
		"================================================================================")
	return nil
}

// GetMainConfig 获取 main.yaml 配置文件内容
func GetMainConfig() MainConfig {
	return mainConfig
}

// GetMainConfigForUnitTest 用户单测中设置默认的配置值
func GetMainConfigForUnitTest() *MainConfig {
	return &mainConfig
}

func getVectorEmbeddingConfig(ctx context.Context, eVersionControl map[string]VectorEmbeddingConfig,
	eVersionName string) *VectorEmbeddingConfig {
	log.Debugf("GetUsingVectorModelInfo|eVersionControl:%s", json0.Marshal2StringNoErr(eVersionControl))
	wfVectorGroup, ok := eVersionControl[eVersionName]
	if !ok {
		log.ErrorContextf(ctx, "getVectorEmbeddingConfig fail")
	}
	return &wfVectorGroup
}

func GetUsingVectorModelInfo(ctx context.Context) *UsingVectorModelInfo {
	vectorGroup := GetMainConfig().WorkflowVectorGroup
	info := &UsingVectorModelInfo{
		Biz:             vectorGroup.Biz,
		Secret:          vectorGroup.Secret,
		OperationMaxIDs: vectorGroup.OperationMaxIDs,
		WorkflowUseVdb:  vectorGroup.WorkflowUseVdb,

		EmbeddingModelName:       vectorGroup.EmbeddingModelName,
		LatestEmbeddingModelName: vectorGroup.LatestEmbeddingModelName,
	}

	eVersionName := info.LatestEmbeddingModelName
	eVersionControl := vectorGroup.EmbeddingVersionControl
	eVersionConfig := getVectorEmbeddingConfig(ctx, eVersionControl, eVersionName)
	log.DebugContextf(ctx, "GetUsingVectorModelInfo|eVersionConfig:%s", json0.Marshal2StringNoErr(eVersionConfig))

	if eVersionConfig != nil {
		info.GroupSuffix = eVersionConfig.GroupSuffix
		info.WorkflowNameTemplate = eVersionConfig.WorkflowNameTemplate
		info.WorkflowExampleTemplate = eVersionConfig.WorkflowExampleTemplate
		info.WorkflowCombinationTemplate = eVersionConfig.WorkflowCombinationTemplate
		info.WorkflowCombinationExampleNum = eVersionConfig.WorkflowCombinationExampleNum
	}

	log.DebugContextf(ctx, "GetUsingVectorModelInfo:%s", json0.Marshal2StringNoErr(info))
	return info
}
