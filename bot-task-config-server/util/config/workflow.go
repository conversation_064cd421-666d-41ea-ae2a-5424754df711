package config

// Workflow 工作流配置
type Workflow struct {
	WorkflowFileName    string        `yaml:"workflow_file_name"`     // 工作流程文件名
	ParamFileName       string        `yaml:"param_file_name"`        // 槽位文件名
	ExampleFileName     string        `yaml:"example_file_name"`      // 示例问法文件名
	VarFileName         string        `yaml:"var_file_name"`          // 自定义变量文件名
	WorkflowRefFileName string        `yaml:"workflow_ref_file_name"` // 工作流引用工作流关系文件名
	WorkflowPDLFileName string        `yaml:"workflow_pdl_file_name"` // 工作流程PDL文件名
	MaxRow              int           `yaml:"max_row"`                // 导入最大行数
	MinRow              int           `yaml:"min_row"`                // 导入最小行数
	ExportLimit         int           `yaml:"export_limit"`           // 导出限制
	ExampleMaxRow       int           `yaml:"example_max_row"`        // 导入示例问法的最大行数长度
	WorkflowHead        []string      `yaml:"workflow_head"`          // 任务流文件头
	ParamHead           []string      `yaml:"param_head"`             // 参数文件头
	ExampleHead         []string      `yaml:"example_head"`           // 示例用法文件头
	VarHead             []string      `yaml:"var_head"`               // 自定义变量文件头
	WorkflowRefHead     []string      `yaml:"workflow_ref_head"`      // 工作流引用工作流关系文件头
	WorkflowPDLHead     []string      `yaml:"workflow_pdl_head"`      // 工作流程PDL文件头
	RunCode             RunCodeConfig `yaml:"run_code"`               // 代码运行相关的配置
}

// RefreshWorkflow 工作流刷数据配置
type RefreshWorkflow struct {
	Vector RefreshVector `yaml:"vector"`
}

// RefreshVector ...
type RefreshVector struct {
	Enable            bool     `yaml:"enable"`               // 总开关
	Spec              string   `yaml:"spec"`                 // cron
	RefreshAll        bool     `yaml:"refresh_all"`          // 是否刷全量机器人
	WhiteRobotIdsList []string `yaml:"white_robot_ids_list"` // upgrade_all 是false的情况下，只刷白名单
}

// RunCodeConfig 代码运行相关的配置
type RunCodeConfig struct {
	CodeTemplate    string `yaml:"code_template"`     // 代码模板
	ResultSeparator string `yaml:"result_separator"`  // 结果分隔符
	MaxResultLength int64  `yaml:"max_result_length"` // 返回的最大长度
}

/**
workflow_node_llm_support:
  # 标签提取节点
  parameter_extractor:
    high_performance_price: ["lke-deepseek-v3", "cs-normal-70b", "hunyuan"]
    recommend: ["lke-deepseek-v3"]
    depth_think: ["lke-deepseek-r1"]
    more_balanced: ["hunyuan"]
    not_show: []

  # 大模型节点
  llm:
    high_performance_price: ["lke-deepseek-v3", "hunyuan"]
    recommend: ["lke-deepseek-v3"]
    depth_think: ["lke-deepseek-r1"]
    more_balanced: ["hunyuan"]
    not_show: []

  # 大模型知识问答节点
  llm_knowledge_qa:
    high_performance_price: ["lke-deepseek-v3", "hunyuan"]
    recommend: ["lke-deepseek-v3"]
    depth_think: ["lke-deepseek-r1"]
    more_balanced: ["hunyuan"]
    not_show: []
*/

// NodeConfig 公共节点配置结构
type NodeConfig struct {
	HighPerformancePrice []string `yaml:"high_performance_price"`
	Recommend            []string `yaml:"recommend"`
	DepthThink           []string `yaml:"depth_think"`
	MoreBalanced         []string `yaml:"more_balanced"`
	DefaultSelected      []string `yaml:"default_selected"`
	LimitTimeFree        []string `yaml:"limit_time_free"`
	NotShow              []string `yaml:"not_show"`
}

// VectorEmbeddingConfig 向量embedding版本配置，包括向量库ID后缀、各种embedding内容模版等
type VectorEmbeddingConfig struct {
	GroupSuffix                   string `yaml:"GroupSuffix"`                   // 向量库ID后缀标识
	WorkflowNameTemplate          string `yaml:"WorkflowNameTemplate"`          // 工作流名称embedding内容模板（go template语法）
	WorkflowExampleTemplate       string `yaml:"WorkflowExampleTemplate"`       // 工作流相似问embedding内容模板（go template语法）
	WorkflowCombinationTemplate   string `yaml:"WorkflowCombinationTemplate"`   // 工作流组合embedding内容模板（go template语法）
	WorkflowCombinationExampleNum int    `yaml:"WorkflowCombinationExampleNum"` // 工作流组合embedding内容模板中相似问的数量
}

// WorkflowVectorGroup 工作流embedding向量配置及版本控制
type WorkflowVectorGroup struct {
	EmbeddingModelName       string `yaml:"EmbeddingModelName"`       // 兼容镜像更新前的老版本(新版本废弃)
	LatestEmbeddingModelName string `yaml:"LatestEmbeddingModelName"` // 使用的最新模型名
	Biz                      string `yaml:"Biz"`                      // 调用方系统标识
	Secret                   string `yaml:"Secret"`                   // 调用方分配的Secret
	OperationMaxIDs          int    `yaml:"OperationMaxIDs"`          // 向量接口单次批量操作最大ID数量
	WorkflowUseVdb           bool   `yaml:"WorkflowUseVdb"`           // 是否启用Vdb

	EmbeddingVersionControl map[string]VectorEmbeddingConfig `yaml:"EmbeddingVersionControl"` // embedding模型版本控制，key为版本名（即embedding模型名称）
}

// UsingVectorModelInfo 正在使用的向量模型信息（内部函数使用，非七彩石配置项）
type UsingVectorModelInfo struct {
	Biz             string // 调用方系统标识
	Secret          string // 调用方分配的Secret
	OperationMaxIDs int    // 向量接口单次批量操作最大ID数量
	WorkflowUseVdb  bool   // 是否启用Vdb

	EmbeddingModelName            string // 兼容镜像更新前的老版本(新版本废弃)
	LatestEmbeddingModelName      string // 使用的最新模型名
	GroupSuffix                   string // 向量库ID后缀标识
	WorkflowNameTemplate          string // 工作流名称embedding内容模板（go template语法）
	WorkflowExampleTemplate       string // 工作流相似问embedding内容模板（go template语法）
	WorkflowCombinationTemplate   string // 工作流组合embedding内容模板（go template语法）
	WorkflowCombinationExampleNum int    // 工作流组合embedding内容模板中相似问的数量
}
