// Package cos ...
package cos

import (
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/storage"
	"path"
)

//var (
//	cosClient *coswrapper.CosWrapper
//)

//// Init 初始化
//func Init() {
//	cosClient = coswrapper.NewCosWrapper(config.GetMainConfig().Cos.BucketUrl,
//		config.GetMainConfig().Cos.SecretID, config.GetMainConfig().Cos.SecretKey)
//}

// GetCos get cos client
//func GetCos() *coswrapper.CosWrapper {
//	return cosClient
//}

// GetCorpCOSPath 获取企业COS路径
func GetCorpCOSPath(corpID uint64) string {
	return fmt.Sprintf("/corp/%d/doc/", corpID)
}

// GetCorpCOSFilePath 获取企业COS文件路径
func GetCorpCOSFilePath(corpID uint64, filename string) string {
	return path.Join(GetCorpCOSPath(corpID), filename)
}

var StorageCli storage.Storage

// Init cos,minio初始化
func Init() {
	StorageCli = storage.New()
}
