// Package database ...
// @Author: halelv
// @Date: 2023/12/20 19:50
package database

import (
	tredis "git.code.oa.com/trpc-go/trpc-database/goredis"
	tgorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

var (
	llmRobotTaskGORM     *gorm.DB
	llmRobotTaskProdGORM *gorm.DB
	llmRobotTaskDB       mysql.Client

	llmRobotWorkflowGORM     *gorm.DB
	llmRobotWorkflowProdGORM *gorm.DB

	llmRobotTaskDelGORM     *gorm.DB
	llmRobotTaskDelProdGORM *gorm.DB

	llmRobotWorkflowDelGORM     *gorm.DB
	llmRobotWorkflowDelProdGORM *gorm.DB

	redisClient redis.UniversalClient
)

// Init database 初始化
func Init() (err error) {
	llmRobotTaskProdGORM, err = tgorm.NewClientProxy(util.LLMRobotTaskProdDB)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotTaskGORM, err = tgorm.NewClientProxy(util.LLMRobotTaskDB)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotWorkflowProdGORM, err = tgorm.NewClientProxy(util.LLMRobotWorkflowProdDB)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotWorkflowGORM, err = tgorm.NewClientProxy(util.LLMRobotWorkflowDB)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}
	llmRobotTaskDB = mysql.NewClientProxy(util.LLMRobotTaskDB)

	llmRobotTaskDelGORM, err = tgorm.NewClientProxy(util.LLMRobotTaskDBDel)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotTaskDelProdGORM, err = tgorm.NewClientProxy(util.LLMRobotTaskProdDBDel)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotWorkflowDelGORM, err = tgorm.NewClientProxy(util.LLMRobotWorkflowDBDel)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	llmRobotWorkflowDelProdGORM, err = tgorm.NewClientProxy(util.LLMRobotWorkflowProdDBDel)
	if err != nil {
		log.Errorf("init gorm err:%v", err)
		return err
	}

	redisClient, err = tredis.New(util.RpcClientRedis, nil)
	if err != nil {
		log.Errorf("init redis err:%v", err)
		return err
		//panic(err)
	}
	return nil
}

// GetLLMRobotTaskProdGORM 获取 db_llm_robot_task_prod 数据库 gorm
func GetLLMRobotTaskProdGORM() *gorm.DB {
	return llmRobotTaskProdGORM
}

// GetLLMRobotTaskGORM 获取 db_llm_robot_task 数据库 gorm
func GetLLMRobotTaskGORM() *gorm.DB {
	return llmRobotTaskGORM
}

// GetLLMRobotWorkflowProdGORM 获取 db_llm_robot_workflow_prod 数据库 gorm
func GetLLMRobotWorkflowProdGORM() *gorm.DB {
	return llmRobotWorkflowProdGORM
}

// GetLLMRobotWorkflowGORM 获取 db_llm_robot_workflow 数据库 gorm
func GetLLMRobotWorkflowGORM() *gorm.DB {
	return llmRobotWorkflowGORM
}

// GetRedis get redis redisClient
func GetRedis() redis.UniversalClient {
	return redisClient
}

// GetLLMRobotTaskDB 获取db_llm_robot_task 数据库db
func GetLLMRobotTaskDB() mysql.Client {
	return llmRobotTaskDB
}

// GetLLMRobotTaskDelGORM 获取 db_llm_robot_task 数据库删除权限的 gorm
func GetLLMRobotTaskDelGORM() *gorm.DB {
	return llmRobotTaskDelGORM
}

// GetLLMRobotTaskDelProdGORM 获取 db_llm_robot_task_prod 数据库删除权限的 gorm
func GetLLMRobotTaskDelProdGORM() *gorm.DB {
	return llmRobotTaskDelProdGORM
}

// GetLLMRobotWorkflowDelGORM 获取 db_llm_robot_workflow 数据库删除权限的 gorm
func GetLLMRobotWorkflowDelGORM() *gorm.DB {
	return llmRobotWorkflowDelGORM
}

// GetLLMRobotWorkflowDelProdGORM 获取 db_llm_robot_workflow_prod 数据库删除权限的 gorm
func GetLLMRobotWorkflowDelProdGORM() *gorm.DB {
	return llmRobotWorkflowDelProdGORM
}
