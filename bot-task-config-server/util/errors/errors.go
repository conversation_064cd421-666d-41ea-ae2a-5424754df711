// Package errors ...
package errors

import (
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/errs"
)

const (
	ErrCodeCateNameTooLong  = 400032
	ErrCodeCateNameTooShort = 400029
	ErrCodeExcelNumTooFew   = 400037
	ErrCodeExcelNumTooMany  = 400038
	ErrIntentNameTooLong    = 400040
	ErrIntentNameTooShort   = 400041
	ErrWorkflowNameTooLong  = 400042
	ErrWorkflowNameTooShort = 400043

	ErrExampleContentTooLong = 500203
	ErrExampleCountTooLong   = 500204

	ErrVarNameTooLong                 = 500301 // 变量名过长
	ErrVarNameIllegal                 = 500302 // 变量名非法
	ErrVarNameDuplicate               = 500303 // 重命名
	ErrVarNameCountExceed             = 500304 // 变量名超过数量长度
	ErrVarNotFoundInApp               = 500305 // 变量在该应用下不存在
	ErrVarType                        = 500306 // 变量类型错误
	ErrVarDescTooLong                 = 500307 // 自定义变量描述过长
	ErrVarDefaultValueIncorrectFormat = 500500 // 自定义变量默认值格式不对
	ErrVarDefaultValueTooLong         = 500501 // 自定义变量默认值过长

	ErrWorkflowDuplicateNodeName = 500323 // 画布内节点名称有重复

	ErrEntryNameTooLong        = 900101
	ErrEntryExampleTooLong     = 900102
	ErrEntryExampleCountExceed = 900103
	ErrEntryDescTooLong        = 900104
	ErrEntryCountExceed        = 900105
	ErrEntryAliasCountExceed   = 900106
	ErrEntryAliasNameTooLong   = 900107
	ErrEntryAliasDuplicated    = 900108

	// ErrParameterNameRegex ======= 工作流 参数，词条相关 ===
	ErrParameterNameRegex          = 910000
	ErrParameterNameTooLong        = 910001
	ErrParameterDescTooLong        = 910002
	ErrParameterType               = 910003
	ErrParameterNumExceed          = 910004
	ErrParameterExampleNameTooLong = 910005
	ErrWFAliasNameTooLong          = 910006
	ErrParameterExampleNumExceed   = 910007
	ErrWFEntryAliasNumExceed       = 910008
	ErrParameterExampleDuplicated  = 910009
	ErrEntryNotFoundInApp          = 900109
	ErrParameterCustomAskTooLong   = 910002
)

// errors 服务内部定义的错误要暴露出去，调用方需要引用它来判断错误类型
var (
	// =================== Embedding 错误 ========================
	// ErrGetEmbeddingEmpty    = errs.New(10041, "获取embedding向量为空")
	// ErrGetEmbedding         = errs.New(10042, "获取embedding向量失败")
	ErrVectorGroupNotFound  = errs.New(10043, "向量库不存在")
	ErrVectorGroupUpgrading = errs.New(10044, "向量库升级中，请稍后再试")

	// =================== 通用错误码和amdin保持一致 =============
	ErrRobotNotFound            = errs.New(450011, "应用信息不存在")
	ErrExcelIsEmpty             = errs.New(450044, "Excel 文件内容为空")
	ErrExcelHead                = errs.New(450051, "Excel 文件表头错误")
	ErrParams                   = errs.New(450057, "参数异常，请重试")
	ErrInvalidFileName          = errs.New(450065, "文件名校验失败")
	ErrExcelContent             = errs.New(450081, "Excel 文件内容错误")
	ErrInvalidSetExcel          = errs.New(450106, "解析异常")
	ErrResourcePermissionDenied = errs.New(450171, "资源权限不足")
	ErrLicenseInvalid           = errs.New(460025, "您的服务已到期，请联系架构师续费")
	ErrEmptyExcelContent        = errs.New(460026, "Excel内容为空,请检查内容是否填在正确的位置了!")
	ErrTermsUnaccepted          = errs.New(4505009, "服务条款未接受，请查看并同意服务条款")

	// =================== taskflow 任务流程 =============
	ErrSystem                            = errs.New(500000, "系统错误")
	ErrTaskFlowNotFound                  = errs.New(500001, "任务流程不存在")
	ErrCreateTaskFlowExport              = errs.New(500002, "创建任务流程导出任务失败")
	ErrImportTaskFlowFileNotFound        = errs.New(500003, "任务流程导入的文件不存在")
	ErrImportTaskFlowFileIncomplete      = errs.New(500004, "任务流程导入的文件数据不完整")
	ErrTaskFlowUIJsonParams              = errs.New(500005, "任务画布JSON参数错误")
	ErrTaskFlowNameDuplicated            = errs.New(500006, "任务流程名已经存在")
	ErrImportTaskFlowStatus              = errs.New(500007, "任务流程导入状态异常")
	ErrTaskFlowNodePreview               = errs.New(500008, "任务流程节点预览参数异常")
	ErrTaskFlowCantNotPublish            = errs.New(500009, "任务流程发布状态中，不允许修改")
	ErrTaskFlowExportLimit               = errs.New(500010, "任务流程导出数量超过限制")
	ErrTaskFlowCopying                   = errs.New(500011, "任务流程复制中")
	ErrTaskFlowLimit                     = errs.New(500012, "任务流程数量超过限制")
	ErrTaskFlowPublishingNotAllowDeleted = errs.New(500013, "任务流程发布状态中，不允许删除")
	ErrTaskFlowNodePreviewIsEvil         = errs.New(500014, "预览结果包含敏感词")
	// =================== category 分类 =============

	ErrCateNotFound        = errs.New(500101, "分类不存在")
	ErrCateCountExceed     = errs.New(500102, "分类数量超过限制")
	ErrCateDepthExceed     = errs.New(500103, "问答对分类层级超过限制")
	ErrInvalidParentCateID = errs.New(500104, "错误的父级分类 ID")
	ErrCateNameDuplicated  = errs.New(500105, "问答对分类名称重复")
	ErrInvalidCateID       = errs.New(500106, "错误的分类 ID")

	// =================== Corpus 预料 =============

	ErrCorpusNotFound         = errs.New(500201, "语料不存在")
	ErrSaveCorpusToVectors    = errs.New(500202, "保存语料失败")
	ErrRobotExampleDuplicated = errs.New(500205, "该应用下示例问法已经存在")
	ErrRobotExampleNotFound   = errs.New(500206, "示例问法不存在")

	// =================== Intent 意图 =============

	ErrIntentBoundRobot = errs.New(500003, "意图与应用绑定关系不对")
	ErrIntentNameExceed = errs.New(500004, "意图描述超过限定长度")

	// =================== workflow 工作流程 =============

	ErrWorkflowNameDuplicated            = errs.New(500300, "工作流名称已经存在")
	ErrWorkflowUIJsonParams              = errs.New(500301, "工作流画布JSON参数错误")
	ErrWorkflowLimit                     = errs.New(500302, "工作流数量超过限制")
	ErrWorkflowRef                       = errs.New(500303, "工作流被引用中，不允许删除")
	ErrWorkflowNotFound                  = errs.New(500304, "工作流不存在")
	ErrCreateWorkflowExport              = errs.New(500305, "创建工作流程导出工作失败")
	ErrImportWorkflowFileNotFound        = errs.New(500306, "工作流程导入的文件不存在")
	ErrImportWorkflowFileIncomplete      = errs.New(500307, "工作流程导入的文件数据不完整")
	ErrImportWorkflowStatus              = errs.New(500308, "工作流程导入状态异常")
	ErrWorkflowNodePreview               = errs.New(500309, "工作流程节点预览参数异常")
	ErrWorkflowCantNotPublish            = errs.New(500310, "工作流程发布状态中，不允许修改")
	ErrWorkflowExportLimit               = errs.New(500311, "工作流程导出数量超过限制")
	ErrWorkflowCopying                   = errs.New(500312, "工作流程复制中")
	ErrWorkflowPublishingNotAllowDeleted = errs.New(500313, "工作流程发布状态中，不允许删除")
	ErrWorkflowNodePreviewIsEvil         = errs.New(500314, "预览结果包含敏感词")
	ErrWorkflowNodeDebugEvil             = errs.New(500315, "不支持调试的节点类型")
	ErrWorkflowNodeNotFound              = errs.New(500316, "未找到节点")
	ErrWorkflowNodeEvil                  = errs.New(500317, "节点信息缺失")
	ErrWorkflowHasRef                    = errs.New(500318, "不能引用已经引用其他工作流的工作流")
	ErrWorkflowQuoteItself               = errs.New(500319, "工作流不能引用自己")
	ErrWorkflowAndAppRef                 = errs.New(500320, "工作流与应用绑定关系不对")
	ErrWorkflowFeedbackNotFound          = errs.New(500321, "工作流反馈不存在")
	ErrWorkflowWrongVersion              = errs.New(500322, "当前工作流可能被其他人编辑，请刷新后再试。")
	//ErrWorkflowDuplicateNodeNameMsg      = errs.New(500323, "画布内节点名称有重复")
	ErrWorkflowNameDuplicatedProduct = errs.New(500325, "工作流名称已经发布过，不允许重名")
	ErrWorkflowRefNameDuplicated     = errs.New(500326, "工作流模版引用的工作流名称与当前工作流名称相同")

	ErrNodeRunNotFound                = errs.New(500327, "工作节点不存在")
	ErrWorkflowRunNotFound            = errs.New(500328, "工作流运行实例不存在")
	ErrAsyncWorkflowInvalid           = errs.New(500329, "该工作流中包含不支持异步调用的节点，请调整后再选择")
	ErrAsyncWorkflowDialogJsonChanged = errs.New(500330, "该工作流已被修改，请发布成功后再试")
	ErrAsyncWorkflowNotEnabledInApp   = errs.New(500331, "应用未满足开启异步调用条件")
	ErrAsyncWorkflowTokenNotEnough    = errs.New(500332, "该工作流中配置的模型余额不足，请充值或调整模型后再试")

	// =================== workflow 工作流程 =============
	ErrWorkflowPDLYamlFormat             = errs.New(500400, "工作流程PDL格式错误")
	ErrWorkflowAPIYamlFormat             = errs.New(500401, "工作流程API格式错误")
	ErrWorkflowUnableConvert             = errs.New(500402, "工作流程不支持转换PDL")
	ErrWorkflowConverting                = errs.New(500403, "工作流程正在转换中")
	ErrWorkflowConvertingNotAllowEdit    = errs.New(500404, "工作流程正在转换中，不允许编辑")
	ErrWorkflowPublishingNotAllowConvert = errs.New(500405, "工作流程发布状态中，不允许转换")
	ErrWorkflowPublishingNotAllowEdit    = errs.New(500405, "工作流程发布状态中，不允许编辑")
	ErrWorkflowConvertingTooLong         = errs.New(500406, "上一次工作流程转换耗时超时过长，请重试")
	ErrWorkflowConvertStatus             = errs.New(500407, "工作流程转换状态异常")
	ErrPDLVersionNotFound                = errs.New(500304, "工作流PDL版本不存在")

	ErrPermissionDenied      = errs.New(600004, "权限不足")
	ErrAgentPermissionDenied = errs.New(600005, "Agent模式不支持工作流修改") // 2.7 暂不支持

	// ↓ ↓ ↓ 数据同步任务 ↓ ↓ ↓

	ErrTaskFlowLoadFailed          = errs.New(700501, "任务流程加载失败")
	ErrSyncTaskLoadFailed          = errs.New(700502, "发布任务加载失败")
	ErrSyncTaskLogLoadFailed       = errs.New(700503, "发布任务记录加载失败")
	ErrSyncConfigDataLoadFailed    = errs.New(700504, "配置数据查询错误")
	ErrSyncConfigDataReleaseFailed = errs.New(700505, "配置数据发布错误")
	ErrSyncRunDataReleaseFailed    = errs.New(700506, "运行数据发布错误")

	// ↑ ↑ ↑ 数据同步任务 ↑ ↑ ↑

	// =================== Share 分享 =============
	ErrShareUrlRobotNotEqual = errs.New(800501, "获取分享应用信息错误")

	// =================== slot 槽位 =============

	ErrSlotNameDuplicated       = errs.New(900201, "实体名称重复") // 槽位对应前端页面为实体，所以次数文案为实体
	ErrSlotEntityNotFound       = errs.New(900202, "实体不存在")
	ErrEntryNameDuplicated      = errs.New(900203, "词条名称重复")
	ErrEntryEmptyName           = errs.New(900204, "词条名为空")
	ErrEntryNameEqualsAliasName = errs.New(900205, "词条名与同义词重复")

	// =================== token 计费 =============
	ErrTokenNotEnough    = errs.New(900501, "当前应用模型余额不足")
	ErrTokenReportFailed = errs.New(900502, "上报用量失败")

	ErrParamNameDuplicated = errs.New(910103, "参数名称重复")
	ErrParamNotExist       = errs.New(910104, "参数ID不存在")
	ErrWorkflowPublishing  = errs.New(910105, "工作流发布状态中，不允许修改")
	ErrParamNoNeedCopy     = errs.New(910106, "参数ID相同，不需要拷贝")
)

// WorkflowPdlContentError ...
func WorkflowPdlContentError(msg string) error {
	return errs.New(500400, "工作流程PDL格式错误:"+msg)
}

// WorkflowApiInfoError ...
func WorkflowApiInfoError(msg string) error {
	return errs.New(500401, "工作流程API格式错误:"+msg)
}

// WorkflowUnableConvertError ...
func WorkflowUnableConvertError(msg string) error {
	return errs.New(500402, "工作流程不支持转换PDL:"+msg)
}

// EntryNotAllowedDeleted 词条被使用，不允许删除
func EntryNotAllowedDeleted(msg string) error {
	return errs.New(900206, "词条调用中，不可删除")
}

// SlotNotAllowedDeleted 词槽被使用，不允许删除
func SlotNotAllowedDeleted(msg string) error { return errs.New(800103, msg) }

// VarParamNotAllowedDeleted 自定义变量被使用，不允许删除
func VarParamNotAllowedDeleted(msg string) error { return errs.New(800104, msg) }

// TaskFLowVerifyError 校验报错封装
func TaskFLowVerifyError(msg string) error {
	return errs.New(700601, msg)
}

// PreviewError 预览校验报错封装
func PreviewError(msg string) error {
	return errs.New(500008, msg)
}

// OpDataFromDBError 操作DB报错
func OpDataFromDBError(msg string) error {
	return errs.New(600001, msg)
}

// BadRequestError 返回请求错误信息
func BadRequestError(msg string) error {
	return errs.New(400001, "请求参数错误: "+msg)
}

// BadWorkflowReqError 工作流参数错误
func BadWorkflowReqError(msg string) error {
	return errs.New(40002, msg)
}

// Is 判断err是否相等
func Is(err, target error) bool {
	return errors.Is(err, target)
}
