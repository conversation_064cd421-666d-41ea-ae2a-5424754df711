// Package errors ...
// @Author: halelv
// @Date: 2024/3/7 16:39
package errors

import "testing"

func TestIs(t *testing.T) {
	type args struct {
		err    error
		target error
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1: 相等",
			args: args{
				err:    ErrTaskFlowNameDuplicated,
				target: ErrTaskFlowNameDuplicated,
			},
			want: true,
		},
		{
			name: "test2: 相等",
			args: args{
				err:    ErrSyncTaskLogLoadFailed,
				target: ErrSyncTaskLogLoadFailed,
			},
			want: true,
		},
		{
			name: "test3: 不等",
			args: args{
				err:    ErrTaskFlowNameDuplicated,
				target: ErrSyncTaskLogLoadFailed,
			},
			want: false,
		},
		{
			name: "test4: 不等",
			args: args{
				err:    ErrTaskFlowNameDuplicated,
				target: ErrTaskFlowCopying,
			},
			want: false,
		},
		{
			name: "test5: 不等--失败",
			args: args{
				err:    ErrTaskFlowNameDuplicated,
				target: ErrTaskFlowCopying,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Is(tt.args.err, tt.args.target); got != tt.want {
				t.Errorf("Is() = %v, want %v", got, tt.want)
			}
		})
	}
}
