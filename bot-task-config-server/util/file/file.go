// Package file ...
package file

import (
	"archive/zip"
	"bytes"
	"context"
	"reflect"
	"strings"
	"unicode/utf8"

	terrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"github.com/xuri/excelize/v2"
)

// FileInfo 文件信息
type FileInfo struct {
	FileName string
	Data     []byte
}

// SaveData2Xlsx 保存数据为excel文件
func SaveData2Xlsx(data map[string][][]string) (*excelize.File, error) {
	f := excelize.NewFile() // 这个函数会默认创建名为"Sheet1"的工作表
	var err error
	var renameSheet1 = false
	if _, ok := data["Sheet1"]; !ok { // 请求数据里面没有名叫"Sheet1"的工作表，就需要重命名
		renameSheet1 = true
	}
	for key, value := range data {
		if len(value) == 0 {
			continue
		}
		if renameSheet1 {
			_ = f.SetSheetName("Sheet1", key)
			renameSheet1 = false
		}
		_, _ = f.NewSheet(key)
		for x, row := range value {
			for y, cell := range row {
				cellName, err := excelize.CoordinatesToCellName(y+1, x+1)
				if err != nil {
					return nil, err
				}
				if utf8.RuneCountInString(cell) > excelize.TotalCellChars {
					return nil, errors.ErrExcelContent
				}
				if err = f.SetCellStr(key, cellName, cell); err != nil {
					return nil, err
				}
			}
		}
	}
	return f, err
}

// WriteZipData 生成zip文件内容
func WriteZipData(zipWriter *zip.Writer, fileInfo FileInfo) error {
	zipFile, err := zipWriter.Create(fileInfo.FileName)
	if err != nil {
		log.Errorf("create file err:%+v", err)
		return err
	}
	if _, err := zipFile.Write(fileInfo.Data); err != nil {
		log.Errorf("write data to file err:%+v", err)
		return err
	}
	return nil
}

// CheckFunc i 被检查行 row 被检查行的列值 uniqueKeys 用于判断行唯一性的集合
type CheckFunc func(i int, row []string, uniqueKeys map[string]int) string

// CheckFuncByWorkflow i 被检查行 row 被检查行的列值 uniqueKeys 用于判断行唯一性的集合 countMap 用于判断指定工作流对应依赖其他计数
type CheckFuncByWorkflow func(i int, row []string, uniqueKeys, countMap map[string]int) (string, string)

// CheckVarXlsxContentCompatibilityHistory 兼容历史自定义变量检查excel文件内容
func CheckVarXlsxContentCompatibilityHistory(ctx context.Context, fileName string, minRow int,
	head []string, body []byte,
	check CheckFuncByWorkflow) ([][]string, []byte, error) {
	sid := util.RequestID(ctx)
	f, err := excelize.OpenReader(bytes.NewReader(body))
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	if f.SheetCount == 0 {
		return nil, nil, errors.ErrExcelIsEmpty
	}
	sheet := f.GetSheetName(0)
	rows, err := f.Rows(sheet)
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	i, total, rowList, errs, uniqueKeys, countMap := -1, -1, make([][]string, 0), map[int]string{}, map[string]int{},
		map[string]int{}
	for rows.Next() {
		i++
		row, err := rows.Columns()
		if err != nil {
			log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
		// 空行跳过
		if isRowEmpty(head, row) {
			continue
		}
		rowList = append(rowList, row)
		if len(row) > 0 {
			total = i
		}
		if i == 0 {
			headSlice := make([]string, 3)
			if len(row) == 3 {
				copy(headSlice, head[:3])
			} else if len(row) == 5 {
				headSlice = make([]string, 5)
				copy(headSlice, head[:5])
			} else {
				headSlice = head
			}
			log.InfoContextf(ctx, "headSlice ErrItem|headSlice:%+v|head:%+v|row:%+v", headSlice, head, row)
			if !reflect.DeepEqual(row, headSlice) {
				return nil, nil, errors.ErrExcelHead
			}
		} else {
			msg, countErr := check(i, row, uniqueKeys, countMap)
			if len(countErr) > 0 {
				log.WarnContextf(ctx, "CheckVarXlsxContentCompatibilityHistory totalMap:%+v", countMap)
				return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooMany, countErr)
			}
			if msg != "" {
				errs[i] = msg
			}
		}
	}
	log.InfoContextf(ctx, "CheckWorkflowXlsxContent totalMap:%+v", countMap)
	if len(rowList) == 0 {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "空的excel文件")
	}
	if minRow > 0 && total < minRow {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "表格条数少于%d条，请修改后重新上传", minRow)
	}
	var hasError bool
	// check函数检测出来的有问题的item
	log.InfoContextf(ctx, "CheckXlsxContent ErrItem|sid:%s|errs:%+v", sid, errs)
	for index, msg := range errs {
		if index > total {
			continue
		}
		hasError = true
		cell, _ := excelize.CoordinatesToCellName(len(head)+1, index+1, false)
		if err := f.SetCellValue(sheet, cell, msg); err != nil {
			log.ErrorContextf(ctx, "设置单元格值错误, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
	}
	if !hasError {
		return rowList[1:], body, nil
	}
	return checkResColumns(ctx, rowList[1:], fileName, sheet, head, f)
}

// CheckWorkflowXlsxContent 通用按照工作流纬度检查excel文件内容
func CheckWorkflowXlsxContent(ctx context.Context, fileName string, minRow int, head []string, body []byte,
	check CheckFuncByWorkflow) ([][]string, []byte, error) {
	sid := util.RequestID(ctx)
	f, err := excelize.OpenReader(bytes.NewReader(body))
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	if f.SheetCount == 0 {
		return nil, nil, errors.ErrExcelIsEmpty
	}
	sheet := f.GetSheetName(0)
	rows, err := f.Rows(sheet)
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	i, total, rowList, errs, uniqueKeys, countMap := -1, -1, make([][]string, 0), map[int]string{}, map[string]int{},
		map[string]int{}
	for rows.Next() {
		i++
		row, err := rows.Columns()
		if err != nil {
			log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
		// 空行跳过
		if isRowEmpty(head, row) {
			continue
		}
		rowList = append(rowList, row)
		if len(row) > 0 {
			total = i
		}
		if i == 0 {
			if !reflect.DeepEqual(row, head) {
				return nil, nil, errors.ErrExcelHead
			}
		} else {
			msg, countErr := check(i, row, uniqueKeys, countMap)
			if len(countErr) > 0 {
				log.WarnContextf(ctx, "CheckWorkflowXlsxContent totalMap:%+v", countMap)
				return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooMany, countErr)
			}
			if msg != "" {
				errs[i] = msg
			}
		}
	}
	log.InfoContextf(ctx, "CheckWorkflowXlsxContent totalMap:%+v", countMap)
	if len(rowList) == 0 {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "空的excel文件")
	}
	if minRow > 0 && total < minRow {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "表格条数少于%d条，请修改后重新上传", minRow)
	}
	var hasError bool
	// check函数检测出来的有问题的item
	log.InfoContextf(ctx, "CheckXlsxContent ErrItem|sid:%s|errs:%+v", sid, errs)
	for index, msg := range errs {
		if index > total {
			continue
		}
		hasError = true
		cell, _ := excelize.CoordinatesToCellName(len(head)+1, index+1, false)
		if err := f.SetCellValue(sheet, cell, msg); err != nil {
			log.ErrorContextf(ctx, "设置单元格值错误, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
	}
	if !hasError {
		return rowList[1:], body, nil
	}
	return checkResColumns(ctx, rowList[1:], fileName, sheet, head, f)
}

// CheckExampleXlsxContent 按照示例问法模版检查 excel文件内容，（没有head）
func CheckExampleXlsxContent(ctx context.Context, fileName string, minRow, maxRow int, head []string, body []byte,
	check CheckFunc) ([][]string, []byte, error) {
	sid := util.RequestID(ctx)
	f, err := excelize.OpenReader(bytes.NewReader(body))
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	if f.SheetCount == 0 {
		return nil, nil, errors.ErrExcelIsEmpty
	}
	sheet := f.GetSheetName(0)
	rows, err := f.Rows(sheet)
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	i, total, rowList, errs, uniqueKeys := -1, -1, make([][]string, 0), map[int]string{}, map[string]int{}
	for rows.Next() {
		i++
		row, err := rows.Columns()
		if err != nil {
			log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
		// 空行跳过
		if isRowEmpty(head, row) {
			continue
		}
		rowList = append(rowList, row)
		if len(row) > 0 {
			total = i
			if total > maxRow {
				return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooMany, "表格条数超过%d条，请修改后重新上传",
					maxRow)
			}
		}
		if len(head) > 0 && i == 0 {
			if !reflect.DeepEqual(row, head) {
				return nil, nil, errors.ErrExcelHead
			}
			continue
		}
		if msg := check(i, row, uniqueKeys); msg != "" {
			errs[i] = msg
		}
	}
	if len(rowList) == 0 {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "空的excel文件")
	}
	if minRow > 0 && total < minRow {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "表格条数少于%d条，请修改后重新上传", minRow)
	}
	var hasError bool
	// check函数检测出来的有问题的item
	log.InfoContextf(ctx, "CheckXlsxContent ErrItem|sid:%s|errs:%+v", sid, errs)
	for index, msg := range errs {
		if index > total {
			continue
		}
		hasError = true
		cell, err := excelize.CoordinatesToCellName(len(head)+1, index+1, false)
		if err != nil {
			return nil, nil, err
		}
		if err := f.SetCellValue(sheet, cell, msg); err != nil {
			log.ErrorContextf(ctx, "设置单元格值错误, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
	}
	if !hasError {
		return rowList[0:], body, nil
	}
	return checkResColumns(ctx, rowList[0:], fileName, sheet, head, f)
}

// CheckXlsxContent 通用检查excel文件内容
func CheckXlsxContent(ctx context.Context, fileName string, minRow, maxRow int, head []string, body []byte,
	check CheckFunc) ([][]string, []byte, error) {
	sid := util.RequestID(ctx)
	f, err := excelize.OpenReader(bytes.NewReader(body))
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	if f.SheetCount == 0 {
		return nil, nil, errors.ErrExcelIsEmpty
	}
	sheet := f.GetSheetName(0)
	rows, err := f.Rows(sheet)
	if err != nil {
		log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrSystem
	}
	i, total, rowList, errs, uniqueKeys := -1, -1, make([][]string, 0), map[int]string{}, map[string]int{}
	for rows.Next() {
		i++
		row, err := rows.Columns()
		if err != nil {
			log.ErrorContextf(ctx, "读取 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
		// 空行跳过
		if isRowEmpty(head, row) {
			continue
		}
		rowList = append(rowList, row)
		if len(row) > 0 {
			total = i
			if total > maxRow {
				return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooMany, "表格条数超过%d条，请修改后重新上传",
					maxRow)
			}
		}
		if i == 0 {
			if !reflect.DeepEqual(row, head) {
				return nil, nil, errors.ErrExcelHead
			}
		} else {
			if msg := check(i, row, uniqueKeys); msg != "" {
				errs[i] = msg
			}
		}
	}
	if len(rowList) == 0 {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "空的excel文件")
	}
	if minRow > 0 && total < minRow {
		return nil, nil, terrs.Newf(errors.ErrCodeExcelNumTooFew, "表格条数少于%d条，请修改后重新上传", minRow)
	}
	var hasError bool
	// check函数检测出来的有问题的item
	log.InfoContextf(ctx, "CheckXlsxContent ErrItem|sid:%s|errs:%+v", sid, errs)
	for index, msg := range errs {
		if index > total {
			continue
		}
		hasError = true
		cell, _ := excelize.CoordinatesToCellName(len(head)+1, index+1, false)
		if err := f.SetCellValue(sheet, cell, msg); err != nil {
			log.ErrorContextf(ctx, "设置单元格值错误, docName: %s, err: %+v", fileName, err)
			return nil, nil, errors.ErrExcelContent
		}
	}
	if !hasError {
		return rowList[1:], body, nil
	}
	return checkResColumns(ctx, rowList[1:], fileName, sheet, head, f)
}

// isRowEmpty 有头部设置的时候仅判断头部列，没有头部设置判断全列
func isRowEmpty(head []string, row []string) bool {
	columnsNum := len(row)
	if len(head) > 0 && len(row) > len(head) {
		columnsNum = len(head)
	}
	return len(strings.Join(row[:columnsNum], "")) == 0
}

func checkResColumns(ctx context.Context, rows [][]string, fileName string, sheet string, head []string,
	f *excelize.File) ([][]string, []byte, error) {
	sid := util.RequestID(ctx)

	styleID, err := f.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "FF0000"}})
	if err != nil {
		log.ErrorContextf(ctx, "创建样式错误, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrInvalidSetExcel
	}
	col, _ := excelize.ColumnNumberToName(len(head) + 1)
	if err := f.SetColStyle(sheet, col, styleID); err != nil {
		log.ErrorContextf(ctx, "设置列样式错误, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrInvalidSetExcel
	}
	buf, err := f.WriteToBuffer()
	if err != nil {
		log.ErrorContextf(ctx, "回写 xlsx 文件失败, docName: %s, err: %+v", fileName, err)
		return nil, nil, errors.ErrInvalidSetExcel
	}
	log.InfoContextf(ctx, "checkResColumns, writeCheckErrContent,sid:%s, content:%s", sid, buf.Bytes())
	return rows, buf.Bytes(), errors.ErrExcelContent
}
