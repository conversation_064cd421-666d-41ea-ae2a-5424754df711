// Package idgenerator ID生成器材
// @author: halelv
// @date: 2023/12/20 15:52
package idgenerator

import (
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/idgenerator"
	"github.com/google/uuid"
)

// workerInstance 雪花算法 worker
var workerInstance *idgenerator.Worker

// Init init id worker
func Init() (err error) {
	workerInstance, err = idgenerator.GetWorkerInstance()
	if err != nil {
		log.Errorf("init worker instance err:%v", err)
		return err
	}
	return nil
}

// NewUUID new uuid string
func NewUUID() string {
	return uuid.New().String()
}

// NewInt64ID new int64 id
func NewInt64ID() int64 {
	return workerInstance.GetId()
}

// NewStringID new string id
func NewStringID() string {
	return workerInstance.GetStringId()
}
