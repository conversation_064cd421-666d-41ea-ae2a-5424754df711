// bot-task-config-server
//
// @(#)idget.go  星期五, 七月 19, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

// Package idget 获取id
package idget

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/go-comm/set"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"github.com/PuerkitoBio/goquery"
)

// getRelatedVarParamsFromRichText 从富文本中获取关联变量
func getRelatedVarParamsFromRichText(ctx context.Context, source string, varParamsIDList []string) []string {
	if len(source) == 0 {
		return varParamsIDList
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
	if err != nil {
		log.ErrorContextf(ctx, "findVarParamsFromRichText|NewDocumentFromReader"+
			" failed,err:%v", err)
		return nil
	}
	doc.Find("span").Each(func(i int, s *goquery.Selection) {
		dataValue, _ := s.Attr("data-value")
		dataType, _ := s.Attr("data-type")
		log.InfoContextf(ctx, "findVarParamsFromRichText|dataValue=%s｜dataType=%s", dataValue, dataType)
		if dataType == KEP.ConditionInfo_BranchCondition_CUSTOM_VAR.String() {
			varParamsIDList = append(varParamsIDList, dataValue)
		}
	})
	log.InfoContextf(ctx, "GetRelatedVarParamsIDs,getRelatedVarParamsFromRichText|len:%d", len(varParamsIDList))
	return varParamsIDList
}

// 从子参数中获取
func getRelatedFromSubRequest(ctx context.Context, reqs []*KEP.APINodeData_RequestParam,
	slotIDList, varParamsIDList []string) ([]string, []string) {
	for _, r := range reqs {
		if r.GetSourceType() == KEP.APINodeData_RequestParam_SLOT {
			slotIDList = append(slotIDList, r.GetSlotValueData().GetSlotID())
		}
		if r.GetSourceType() == KEP.APINodeData_RequestParam_CUSTOM_VAR {
			varParamsIDList = append(varParamsIDList, r.GetCustomVarValueData().GetValue())
		}
		if r.GetParamType() == entity.ParamTypeObject && r.GetSubRequest() != nil {
			slotIDList, varParamsIDList = getRelatedFromSubRequest(ctx, r.GetSubRequest(), slotIDList, varParamsIDList)
		}
	}
	return slotIDList, varParamsIDList
}

func getRelatedVarParamsFromHeader(ctx context.Context, headers []*KEP.APINodeData_Header, varIds []string) []string {
	for _, h := range headers {
		if h.GetSourceType() == KEP.APINodeData_Header_CUSTOM_VAR {
			varIds = append(varIds, h.GetCustomVarValueData().GetValue())
		}
		varIds = getRelatedVarParamsFromHeader(ctx, h.GetSubHeader(), varIds)
	}
	return varIds
}

// GetAllRelatedFromTree 从任务流程树中获取意图关联实体，自定义变量，词条，分支条件实体全选词条
func GetAllRelatedFromTree(ctx context.Context, tree *KEP.TaskFlow) ([]string, []string, []string, []string) {
	slotIDList := make([]string, 0)
	varParamsIDList := make([]string, 0)
	entryIDList := make([]string, 0)
	slotEntryAllIDList := make([]string, 0)
	if tree == nil {
		return slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList
	}
	for _, node := range tree.GetNodes() {
		// 从分支条件获取 TODO mike 这个地方有必要获取分支条件的slot吗？是不是询问节点和api节点里面有的就满足了
		slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList = getAllRelatedFromNodeBranch(ctx, node,
			slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList)
		// 从各个节点中获取
		slotIDList, varParamsIDList = getAllRelatedFromNode(ctx, node, slotIDList, varParamsIDList)
	}
	// 去重复
	slotIDList = set.RemoveDuplicatesAndEmpty(slotIDList)
	varParamsIDList = set.RemoveDuplicatesAndEmpty(varParamsIDList)
	entryIDList = set.RemoveDuplicatesAndEmpty(entryIDList)
	log.InfoContextf(ctx, "GetAllRelatedIDList|slotIDList len:%d|varParamsIDList len:%d|entryIDList "+
		"len:%d|slotEntryAllIDList len:%d", len(slotIDList), len(varParamsIDList), len(entryIDList),
		len(slotEntryAllIDList))
	return slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList
}

// getAllRelatedFromNode 从节点本身获取词槽，变量
func getAllRelatedFromNode(ctx context.Context, node *KEP.TaskFlowNode, slotIDList []string,
	varParamsIDList []string) ([]string, []string) {
	switch node.GetNodeType() {
	case KEP.NodeType_API:
		for _, r := range node.GetApiNodeData().GetRequest() {
			// query请求参数中数据来源类型为收集实体
			if r.GetSourceType() == KEP.APINodeData_RequestParam_SLOT {
				slotIDList = append(slotIDList, r.GetSlotValueData().SlotID)
			}
			if r.GetParamType() == entity.ParamTypeObject {
				slotIDList, varParamsIDList = getRelatedFromSubRequest(ctx, r.GetSubRequest(),
					slotIDList, varParamsIDList)
				log.InfoContextf(ctx, "getRelatedFromSubRequest slotIDList len:%d, varParamsIDList len:%d",
					len(slotIDList), len(varParamsIDList))
			}
			if r.GetSourceType() == KEP.APINodeData_RequestParam_CUSTOM_VAR {
				varParamsIDList = append(varParamsIDList, r.GetCustomVarValueData().Value)
			}
		}
		for _, r := range node.GetApiNodeData().GetHeaders() {
			if r.GetParamType() == entity.ParamTypeString && r.GetSourceType() == KEP.APINodeData_Header_CUSTOM_VAR {
				varParamsIDList = append(varParamsIDList, r.GetCustomVarValueData().Value)
			}
			if r.GetParamType() == entity.ParamTypeObject {
				varParamsIDList = getRelatedVarParamsFromHeader(ctx, r.GetSubHeader(), varParamsIDList)
				log.InfoContextf(ctx, "GetRelatedVarParamsIDs｜FromHeader,len:%d", len(varParamsIDList))
			}
		}
	case KEP.NodeType_REQUEST:
		slotIDList, varParamsIDList = getAllRelatedFromRequestNode(ctx, node, slotIDList, varParamsIDList)
	case KEP.NodeType_ANSWER: // 回复节点
		answerData := node.GetAnswerNodeData()
		if answerData != nil {
			if answerData.GetAnswerType() == KEP.AnswerNodeData_LLM &&
				answerData.GetLLMAnswerData() != nil {
				varParamsIDList = getRelatedVarParamsFromRichText(ctx, answerData.GetLLMAnswerData().GetPrompt(),
					varParamsIDList)
			}
			if answerData.GetAnswerType() == KEP.AnswerNodeData_INPUT &&
				answerData.GetInputAnswerData() != nil {
				varParamsIDList = getRelatedVarParamsFromRichText(ctx, answerData.GetInputAnswerData().GetPreview(),
					varParamsIDList)
			}
		}
	}
	return slotIDList, varParamsIDList
}

// getAllRelatedFromRequestNode 从询问节点本身获取词槽，变量
func getAllRelatedFromRequestNode(ctx context.Context, node *KEP.TaskFlowNode, slotIDList []string,
	varParamsIDList []string) ([]string, []string) {
	reqNodeData := node.GetRequestNodeData()
	if reqNodeData != nil && reqNodeData.GetRequest() != nil {
		if reqNodeData.GetRequest().GetRequestType() == KEP.RequestNodeData_RequestInfo_SLOT {
			slotIDList = append(slotIDList, reqNodeData.GetRequest().GetRequestValue())
		}
		if (reqNodeData.GetRequest().GetAskType() == KEP.RequestNodeData_RequestInfo_INPUT ||
			reqNodeData.GetRequest().GetRequestType() == KEP.RequestNodeData_RequestInfo_PURPOSE) &&
			reqNodeData.GetRequest().GetCustomAsk() != "" {
			varParamsIDList = getRelatedVarParamsFromRichText(ctx, reqNodeData.GetRequest().GetCustomAsk(),
				varParamsIDList)
		}
	}
	return slotIDList, varParamsIDList
}

// getAllRelatedFromNodeBranch 通过分支获取词槽，变量,词条
func getAllRelatedFromNodeBranch(ctx context.Context, node *KEP.TaskFlowNode, slotIDList, varParamsIDList,
	entryIDList, slotEntryAllIDList []string) ([]string, []string, []string, []string) {
	for _, branch := range node.Branches {
		slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList = getAllRelatedFromCondition(ctx, node,
			branch.GetConditionInfo(), slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList)
	}
	return slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList
}

// getAllRelatedFromCondition 分支条件找 slotId
func getAllRelatedFromCondition(ctx context.Context, node *KEP.TaskFlowNode,
	condInfo *KEP.ConditionInfo, slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList []string) ([]string,
	[]string, []string, []string) {
	condition := condInfo.GetCondition()
	// 数据来源为收集实体
	if condition.GetSourceType() == KEP.ConditionInfo_BranchCondition_SLOT {
		// 实体ID
		slotID := condition.GetSlotValueData().GetSlotID()
		slotIDList = append(slotIDList, slotID)
		if condition.GetComparisonValueSourceType() == entity.ValueSourceEntry ||
			condition.GetComparisonValueSourceType() == entity.ValueSourceEntity ||
			condition.GetInInputValueAddition().GetValueSource() == entity.ValueSourceEntry {
			if condition.GetInInputValueAddition().GetAllEntry() { //全部词条
				slotEntryAllIDList = append(slotEntryAllIDList, slotID)
			} else {
				entryIDList = append(entryIDList, condition.GetInInputValueAddition().GetEntryIDs()...)
			}
		}
	}
	// 数据来源为接口出参；值为引用实体
	if condition.GetSourceType() == KEP.ConditionInfo_BranchCondition_API_RESP {
		// 实体ID
		slotId := condition.GetComparisonSlotInfo().GetSlotID()
		slotIDList = append(slotIDList, slotId)
		// 词条处理;分为选择指定词条和全选
		if condition.GetComparisonValueSourceType() == entity.ValueSourceEntry ||
			condition.GetComparisonValueSourceType() == entity.ValueSourceEntity {
			if condition.GetComparisonSlotInfo().GetAllEntry() {
				slotEntryAllIDList = append(slotEntryAllIDList, slotId)
			} else {
				entryIDList = append(entryIDList, condition.GetComparisonSlotInfo().GetEntryIDs()...)
			}
		}
	}
	// 数据来源为自定义参数
	if condition.GetSourceType() == KEP.ConditionInfo_BranchCondition_CUSTOM_VAR {
		// 变量ID
		varId := condition.GetCustomVarValueData().GetValue()
		varParamsIDList = append(varParamsIDList, varId)
	}

	// 子条件递归处理
	if len(condInfo.GetConditionInfo()) > 0 {
		for _, itemCond := range condInfo.GetConditionInfo() {
			slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList = getAllRelatedFromCondition(ctx, node,
				itemCond, slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList)
		}
	}
	return slotIDList, varParamsIDList, entryIDList, slotEntryAllIDList
}
