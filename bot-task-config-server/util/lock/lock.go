// Package lock 分布式锁
// @Author: halelv -- copy from https://git.woa.com/dialogue-platform/go-comm/blob/master/redlock/redlock.go
// @Date: 2024/2/26 18:45
package lock

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

const (
	// DefaultExpireSeconds 默认的过期时间
	DefaultExpireSeconds = 15
	// DefaultRenewExpireSeconds 默认重新设置过期时间间隔
	DefaultRenewExpireSeconds = 7
	// DefaultRetryMilliSeconds 默认的重试锁的时间
	DefaultRetryMilliSeconds = 500

	deleteScript = `
if redis.call("get",KEYS[1]) == ARGV[1] then 
	return redis.call("del",KEYS[1]) 
else 
	return 0 
end`
)

// Locker 锁的 interface
type Locker interface {
	Lock(ctx context.Context, block bool) (bool, error)
	UnLock(ctx context.Context) error
}

// Config 分布式锁的配置
type Config struct {
	Key                string
	Identity           string
	RedisClient        redis.UniversalClient
	ExpireSeconds      uint32
	RenewExpireSeconds uint32
	RetryMilliseconds  uint32
}

type redisLock struct {
	Config
	stopLocking   chan struct{}
	lockStartTime time.Time
}

// NewRedisLocker 新建锁
func NewRedisLocker(config Config) Locker {
	locker := &redisLock{
		Config:        config,
		stopLocking:   make(chan struct{}, 1),
		lockStartTime: time.Time{},
	}
	if locker.Identity == "" {
		locker.Identity = uuid.New().String()
	}
	return locker
}

// NewDefaultLocker 新建默认的锁
func NewDefaultLocker(lockKey string, identity string, redisClient redis.UniversalClient) Locker {
	return NewRedisLocker(Config{
		Key:                lockKey,
		Identity:           identity,
		RedisClient:        redisClient,
		ExpireSeconds:      DefaultExpireSeconds,
		RenewExpireSeconds: DefaultRenewExpireSeconds,
		RetryMilliseconds:  DefaultRetryMilliSeconds,
	})
}

// Lock 锁
func (r *redisLock) Lock(ctx context.Context, block bool) (bool, error) {
	log.InfoContextf(ctx, "try lock(key: %s, identity: %v)", r.Key, r.Identity)
	for {
		ok, err := r.RedisClient.SetNX(context.Background(),
			r.Key, r.Identity, time.Second*time.Duration(r.ExpireSeconds)).Result()
		if err != nil {
			log.ErrorContextf(ctx, "exec redis SetNX failed, key: %v, identity: %v, error: %v",
				r.Key, r.Identity, err)
			return false, err
		}
		if ok {
			log.InfoContextf(ctx, "lock successfully(key: %s, identity: %v)", r.Key, r.Identity)
			r.lockStartTime = time.Now()
			go r.keepLocking(ctx)
			return true, nil
		}
		if block {
			time.Sleep(time.Millisecond * time.Duration(r.RetryMilliseconds))
		} else {
			return false, nil
		}
	}
}

func (r *redisLock) keepLocking(ctx context.Context) {
	if r.RenewExpireSeconds <= 0 {
		return
	}
	log.InfoContextf(ctx, "start to keep locking, key: %v, identity: %v, expireSeconds: %v", r.Key, r.Identity,
		r.ExpireSeconds)
	ticker := time.NewTicker(time.Second * time.Duration(r.RenewExpireSeconds))
	for {
		select {
		case <-ticker.C:
			ok, err := r.RedisClient.Expire(context.Background(),
				r.Key, time.Second*time.Duration(r.ExpireSeconds)).Result()
			if err != nil {
				log.ErrorContextf(ctx, "exec redis EXPIRE failed, key: %v, error: %v", r.Key, err)
			}
			if !ok {
				log.ErrorContextf(ctx, "key \"%v\" not found, identity: %v", r.Key, r.Identity)
				return
			}
		case <-r.stopLocking:
			log.InfoContextf(ctx, "keep locking is stopped, key: %v, identity: %v", r.Key, r.Identity)
			return
		}
	}
}

// UnLock 解锁
func (r *redisLock) UnLock(ctx context.Context) error {
	log.InfoContextf(ctx, "unlock. duration(%v) for holding lock", time.Since(r.lockStartTime))
	r.stopLocking <- struct{}{}
	result, err := r.RedisClient.Eval(context.Background(), deleteScript, []string{r.Key}, r.Identity).Int()
	if err != nil {
		log.ErrorContextf(ctx, "exec redis deleteScript failed, key: %v, identity: %v, error: %v",
			r.Key, r.Identity, err)
		return err
	}
	if result != 1 {
		log.ErrorContextf(ctx, "unlock failed, key: %v, identity: %v. redis lock with hold already release",
			r.Key, r.Identity)
		return fmt.Errorf("unlock failed, key: %v, identity: %v. redis lock with hold already release",
			r.Key, r.Identity)
	}
	return nil
}
