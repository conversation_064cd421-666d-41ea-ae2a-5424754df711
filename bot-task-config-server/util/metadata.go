package util

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"github.com/spf13/cast"
)

const (
	//metaUserID  = "UserID"
	metaStaffID = "StaffID"
	metaCorpID  = "CorpID"
	metaSID     = "SID"
	//
	//metaToken              = "token"
	metaUin                = "uin"
	metaSubAccountUin      = "sub_account_uin"
	metaLoginUin           = "login_uin"
	metaLoginSubAccountUin = "login_sub_account_uin"
	metaRequestID          = "request_id"
	metaAction             = "action"
	// cloudSID 腾讯云直客集成商ID
	cloudSID = 1
)

// withServerMedata 直接增加 ServerMetadata 值，当外部调用时会自动转换为 ClientMedata
func withServerMedata(ctx context.Context, k, v string) context.Context {
	ctx = trpc.CloneContext(ctx)
	trpc.SetMetaData(ctx, k, []byte(v))
	return ctx
}

// SID 获取SID
func SID(ctx context.Context) int {
	//sid := metadata.Metadata(ctx).Get(metaSID)
	sid := string(codec.Message(ctx).ServerMetaData()[metaSID])
	if sid == "" {
		return 0
	}
	return cast.ToInt(sid)
}

// WithStaffID 携带员工 ID
func WithStaffID(ctx context.Context, staffID uint64) context.Context {
	return withServerMedata(ctx, metaStaffID, strconv.FormatUint(staffID, 10))
}

// WithCorpID 携带企业 ID
func WithCorpID(ctx context.Context, corpID uint64) context.Context {
	return withServerMedata(ctx, metaCorpID, strconv.FormatUint(corpID, 10))
}

// WithSID 携带集成商 ID
func WithSID(ctx context.Context, sID uint64) context.Context {
	return withServerMedata(ctx, metaSID, strconv.FormatUint(sID, 10))
}

// WithUin 携带集成商 UIN
func WithUin(ctx context.Context, uin uint64) context.Context {
	return withServerMedata(ctx, metaUin, strconv.FormatUint(uin, 10))
}

// WithSubUin 携带集成商 subUin
func WithSubUin(ctx context.Context, subUin uint64) context.Context {
	return withServerMedata(ctx, metaSubAccountUin, strconv.FormatUint(subUin, 10))
}

// // WithToken 携带token
//
//	func WithToken(ctx context.Context, token string) context.Context {
//		return withServerMedata(ctx, metaToken, token)
//	}
//
// // Token 获取token
//
//	func Token(ctx context.Context) string {
//		return metadata.Metadata(ctx).Get(metaToken)
//	}
//
// StaffID 获取staffID
func StaffID(ctx context.Context) uint64 {
	// staffID := metadata.Metadata(ctx).Get(metaStaffID)
	staffID := string(codec.Message(ctx).ServerMetaData()[metaStaffID])
	if staffID == "" {
		return 0
	}
	return cast.ToUint64(staffID)
}

// CorpID 获取corpID
func CorpID(ctx context.Context) uint64 {
	// corpID := metadata.Metadata(ctx).Get(metaCorpID)
	corpID := string(codec.Message(ctx).ServerMetaData()[metaCorpID])
	if corpID == "" {
		return 0
	}
	return cast.ToUint64(corpID)
}

//
//// IsCloudSI 判断是否是腾讯云集成商
//func IsCloudSI(ctx context.Context) bool {
//	return (LoginUin(ctx) == "" && LoginSubAccountUin(ctx) == "") ||
//		(LoginUin(ctx) == Uin(ctx) && LoginSubAccountUin(ctx) == SubAccountUin(ctx))
//}

// Uin AKSK 对应的云主账号 Uin
func Uin(ctx context.Context) string {
	//return metadata.Metadata(ctx).Get(metaUin)
	return string(codec.Message(ctx).ServerMetaData()[metaUin])
}

// SubAccountUin AKSK 对应的云子账号 Uin
func SubAccountUin(ctx context.Context) string {
	//return metadata.Metadata(ctx).Get(metaSubAccountUin)
	return string(codec.Message(ctx).ServerMetaData()[metaSubAccountUin])
}

// LoginUin 登录态对应的云主账号 Uin
func LoginUin(ctx context.Context) string {
	//return metadata.Metadata(ctx).Get(metaLoginUin)
	return string(codec.Message(ctx).ServerMetaData()[metaLoginUin])
}

// LoginSubAccountUin 登录态对应的云子账号 Uin
func LoginSubAccountUin(ctx context.Context) string {
	//return metadata.Metadata(ctx).Get(metaLoginSubAccountUin)
	return string(codec.Message(ctx).ServerMetaData()[metaLoginSubAccountUin])
}

// RequestID 云请求 ID
func RequestID(ctx context.Context) string {
	sid := string(codec.Message(ctx).ServerMetaData()[metaRequestID])
	if len(sid) == 0 {
		sid = string(codec.Message(ctx).ServerMetaData()["_sid_"])
	}
	return sid
}

// WithRequestID 云请求 ID
func WithRequestID(ctx context.Context, requestID string) {
	trpc.SetMetaData(ctx, metaRequestID, []byte(requestID))
}

// Action 当前操作
func Action(ctx context.Context) string {
	//return metadata.Metadata(ctx).Get(metaAction)
	return string(codec.Message(ctx).ServerMetaData()[metaAction])
}

// GetUinAndSubAccountUin 获取 Uin、SubAccountUin
func GetUinAndSubAccountUin(ctx context.Context) (string, string) {
	// 与andrekzwu对，需与admin用户体系逻辑保持一致，腾讯云用户用 Uin(ctx)
	if SID(ctx) == cloudSID {
		return Uin(ctx), SubAccountUin(ctx)
	} else {
		// 集成商用 LoginUin(ctx)
		return LoginUin(ctx), LoginSubAccountUin(ctx)
	}
}
