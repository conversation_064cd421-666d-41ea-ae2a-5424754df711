package protoutil

import (
	"errors"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"google.golang.org/protobuf/encoding/protojson"
)

// TaskFlowToJson 对话树转JSON字符串
func TaskFlowToJson(taskFlow *KEP.TaskFlow) (string, error) {
	if taskFlow == nil {
		return "", errors.New("taskFlow is nil")
	}
	options := protojson.MarshalOptions{
		UseEnumNumbers:  false, // 使用枚举的名称(字符串)
		EmitUnpopulated: false, // 未设置值的字段会被过滤掉
		//Indent:          "  ",
		EmitDefaultValues: true, // 生成的JSON字符串中包含默认值
	}
	// 将 protobuf 消息序列化为 JSON
	jsonBytes, err := options.Marshal(taskFlow)
	//if err != nil {
	//	log.Fatalf("TaskFlowToJson|ID:|Failed to marshal JSON: %v", err)
	//}
	return string(jsonBytes), err
}

// JsonToTaskFlow JSON字符串转对话树
func JsonToTaskFlow(jsonString string) (*KEP.TaskFlow, error) {
	if len(jsonString) == 0 {
		return nil, errors.New("jsonString is empty")
	}
	taskFlow := &KEP.TaskFlow{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: false, // 将多余的字段保留在解析后的PB对象中；解析报错
	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), taskFlow)
	//if err != nil {
	//	log.Fatalf("Failed to unmarshal JSON: %v", err)
	//}
	return taskFlow, err
}

// JsonToTaskFlowBackEnd JSON字符串转对话树
func JsonToTaskFlowBackEnd(jsonString string) (*KEP.TaskFlowBackEnd, error) {
	if len(jsonString) == 0 {
		return nil, errors.New("jsonString is empty")
	}
	taskFlowBackEnd := &KEP.TaskFlowBackEnd{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: true, // 忽略PB定义中未定义的字段
	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), taskFlowBackEnd)

	return taskFlowBackEnd, err
}

// JsonToTaskFlowForPreCheck  JSON字符串转对话树 - 简要判断
func JsonToTaskFlowForPreCheck(jsonString string) (*KEP.TaskFlow, error) {
	if len(jsonString) == 0 {
		//return nil, errors.New("jsonString is empty")
		//创建任务流程时没有画布信息，且多处用到了这里；暂时不改
		return nil, nil
	}
	taskFlow := &KEP.TaskFlow{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: true, // 默认false; 没定义在协议中的数据能否过（额外的key、错误的枚举值等）

	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), taskFlow)
	//if err != nil {
	//	log.Fatalf("Failed to unmarshal JSON: %v", err)
	//}
	return taskFlow, err

}
