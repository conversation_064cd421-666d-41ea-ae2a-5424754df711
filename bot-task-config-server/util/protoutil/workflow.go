package protoutil

import (
	"context"
	"errors"
	"reflect"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"google.golang.org/protobuf/encoding/protojson"
)

// CompareWorkflowEqual  比较 Workflow json数据是否相等
func CompareWorkflowEqual(ctx context.Context, jsonStr1, jsonStr2 string) (bool, error) {
	if jsonStr1 == "" || jsonStr2 == "" {
		return false, nil
	}

	var wf1, wf2 *KEP_WF.Workflow
	var err error
	if wf1, err = JsonToWorkflow(jsonStr1); err != nil {
		log.ErrorContextf(ctx, "CompareWorkflowEqual|wf1:%+v|err:%+v", wf1, err)
		return false, err
	}

	if wf2, err = JsonToWorkflow(jsonStr2); err != nil {
		log.ErrorContextf(ctx, "CompareWorkflowEqual|wf2:%+v|err:%+v", wf2, err)
		return false, err
	}
	removeFrontData(wf1)
	removeFrontData(wf2)
	return reflect.DeepEqual(wf1, wf2), nil
}

func removeFrontData(workflow *KEP_WF.Workflow) {
	// 去除前端UI相关的字段，不参与比较
	for _, node := range workflow.GetNodes() {
		node.NodeUI = ""
	}
	workflow.Edge = ""
	removeProtoVersion(workflow)
}

// removeProtoVersion 将ProtoVersion字段置默认值，不参与比较
func removeProtoVersion(workflow *KEP_WF.Workflow) {
	workflow.ProtoVersion = KEP_WF.WorkflowProtoVersion_UNSPECIFIED
}

// JsonToWorkflow  JSON字符串转工作流
func JsonToWorkflow(jsonString string) (*KEP_WF.Workflow, error) {
	if len(jsonString) == 0 {
		return nil, errors.New("jsonString is empty")
	}
	workflow := &KEP_WF.Workflow{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: false, // 将多余的字段保留在解析后的PB对象中；解析报错
	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), workflow)
	return workflow, err
}

// JsonToParameterInfoList JSON字符串转参数列表
func JsonToParameterInfoList(jsonString string) (*KEP_WF.ParameterInfoList, error) {
	if len(jsonString) == 0 {
		return nil, errors.New("jsonString is empty")
	}
	paramInfoList := &KEP_WF.ParameterInfoList{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: false, // 将多余的字段保留在解析后的PB对象中；解析报错
	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), paramInfoList)
	return paramInfoList, err
}

// JsonToPDLToolsInfo JSON字符串转PDL工具信息
func JsonToPDLToolsInfo(jsonString string) (*KEP_WF.PDLToolsInfo, error) {
	if len(jsonString) == 0 {
		return nil, errors.New("jsonString is empty")
	}
	pdlToolsInfo := &KEP_WF.PDLToolsInfo{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: false, // 将多余的字段保留在解析后的PB对象中；解析报错
	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), pdlToolsInfo)
	return pdlToolsInfo, err
}

// JsonToWorkflowForPreCheck  JSON字符串转对话树 - 简要判断
func JsonToWorkflowForPreCheck(jsonString string) (*KEP_WF.Workflow, error) {
	if len(jsonString) == 0 {
		//return nil, errors.New("jsonString is empty")
		//创建任务流程时没有画布信息，且多处用到了这里；暂时不改
		return nil, nil
	}
	workFlow := &KEP_WF.Workflow{}
	options := protojson.UnmarshalOptions{
		DiscardUnknown: true, // 默认false; 没定义在协议中的数据能否过（额外的key、错误的枚举值等）

	}
	// 反序列化 JSON 到 protobuf 消息
	err := options.Unmarshal([]byte(jsonString), workFlow)
	//if err != nil {
	//	log.Fatalf("Failed to unmarshal JSON: %v", err)
	//}
	return workFlow, err

}

// WorkflowToJson 工作流转JSON字符串
func WorkflowToJson(workflow *KEP_WF.Workflow) (string, error) {
	if workflow == nil {
		return "", errors.New("workflow is nil")
	}
	options := protojson.MarshalOptions{
		UseEnumNumbers:  false, // 使用枚举的名称(字符串)
		EmitUnpopulated: false, // 未设置值的字段会被过滤掉
		//Indent:          "  ",
		EmitDefaultValues: true, // 生成的JSON字符串中包含默认值
	}
	// 将 protobuf 消息序列化为 JSON
	jsonBytes, err := options.Marshal(workflow)
	//if err != nil {
	//	log.Fatalf("WorkflowToJson|ID:|Failed to marshal JSON: %v", err)
	//}
	return string(jsonBytes), err
}

// ParameterInfoListToJson 工作流参数列表转JSON字符串
func ParameterInfoListToJson(parameterInfoList *KEP_WF.ParameterInfoList) (string, error) {
	if parameterInfoList == nil {
		return "", errors.New("parameterInfoList is nil")
	}
	options := protojson.MarshalOptions{
		UseEnumNumbers:  false, // 使用枚举的名称(字符串)
		EmitUnpopulated: false, // 未设置值的字段会被过滤掉
		//Indent:          "  ",
		EmitDefaultValues: true, // 生成的JSON字符串中包含默认值
	}
	// 将 protobuf 消息序列化为 JSON
	jsonBytes, err := options.Marshal(parameterInfoList)
	//if err != nil {
	//	log.Fatalf("ParameterInfoListToJson|ID:|Failed to marshal JSON: %v", err)
	//}
	return string(jsonBytes), err
}

// PDLToolsInfoToJson 工作流PDL工具信息转Json字符串
func PDLToolsInfoToJson(pdlToolsInfo *KEP_WF.PDLToolsInfo) (string, error) {
	if pdlToolsInfo == nil {
		return "", errors.New("pdlToolsInfo is nil")
	}
	options := protojson.MarshalOptions{
		UseEnumNumbers:  false, // 使用枚举的名称(字符串)
		EmitUnpopulated: false, // 未设置值的字段会被过滤掉
		//Indent:          "  ",
		EmitDefaultValues: true, // 生成的JSON字符串中包含默认值
	}
	// 将 protobuf 消息序列化为 JSON
	jsonBytes, err := options.Marshal(pdlToolsInfo)
	//if err != nil {
	//	log.Fatalf("pdlToolsInfo|ID:|Failed to marshal JSON: %v", err)
	//}
	return string(jsonBytes), err
}
