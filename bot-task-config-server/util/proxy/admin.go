package proxy

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/PB_SmartService"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

var apiCli = pb.NewApiClientProxy(
	[]client.Option{
		//client.WithServiceName(util.RpcClientAdminApiClient),
	}...)

var loginCli = pb.NewLoginClientProxy(
	[]client.Option{
		//client.WithServiceName(util.RpcClientAdminLoginClient),
	}...)

var accessManager = PB_SmartService.NewAccessManagerClientProxy(
	[]client.Option{
		// https://iwiki.woa.com/p/284289117#4-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE
		// https://iwiki.woa.com/p/4008319150#b-%E5%85%B3%E9%97%AD%E6%9C%8D%E5%8A%A1%E8%B7%AF%E7%94%B1%EF%BC%9A
		client.WithServiceName(util.RpcClientAccessManager),
	}...)

// GetAdminAPIProxy 获取qbot.qbot.admim.api
func GetAdminAPIProxy() pb.ApiClientProxy {
	return apiCli
}

// GetAdminLoginProxy 获取qbot.qbot.admim.login
func GetAdminLoginProxy() pb.LoginClientProxy {
	return loginCli
}

// GetAccessManagerProxy 获取 trpc.SmartService.AccessManagerServer.AccessManager
// 权限底座
func GetAccessManagerProxy() PB_SmartService.AccessManagerClientProxy {
	return accessManager
}
