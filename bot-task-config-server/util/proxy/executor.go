package proxy

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_exec_pycode_server"
)

var codeExecutor = bot_exec_pycode_server.NewCodeExecutorClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientCodeExecutor),
	}...,
)

// GetCodeExecutor 获取代码执行和检查客户端
func GetCodeExecutor() bot_exec_pycode_server.CodeExecutorClientProxy {
	return codeExecutor
}
