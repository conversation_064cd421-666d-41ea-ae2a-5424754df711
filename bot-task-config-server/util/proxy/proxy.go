// Package proxy 代理
package proxy

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	knowledge "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	botllm "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_llm_server"
	interpreter "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	llm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	robot "git.woa.com/ivy/protobuf/trpc-go/customer_service/robot/robot_config"
)

var botDmProxy = KEP_DM.NewBotDmClientProxy(
	[]client.Option{
		// https://iwiki.woa.com/p/284289117#4-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE
		// https://iwiki.woa.com/p/4008319150#b-%E5%85%B3%E9%97%AD%E6%9C%8D%E5%8A%A1%E8%B7%AF%E7%94%B1%EF%BC%9A
		client.WithServiceName(util.RpcClientDM),
	}...)

var lLMProxy = llm.NewChatClientProxy(
	[]client.Option{
		// https://iwiki.woa.com/p/284289117#4-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE
		// https://iwiki.woa.com/p/4008319150#b-%E5%85%B3%E9%97%AD%E6%9C%8D%E5%8A%A1%E8%B7%AF%E7%94%B1%EF%BC%9A
		client.WithServiceName(util.RpcClientLLMManagerServer),
	}...)

var wfDmProxy = KEP_WF_DM.NewWorkflowDmClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientWorkflowDM),
	}...)

var knowledgeProxy = knowledge.NewApiClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientKnowledge),
	}...)

var QidianProxy = robot.NewRobotConfigClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientQidian),
	}...)

var botLLMProxy = botllm.NewLlmClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientBotLLM),
	}...)

var codeExecProxy = interpreter.NewCodeExecClientProxy()

// GetBotDmProxy 获取BotDm服务代理
func GetBotDmProxy() KEP_DM.BotDmClientProxy {
	return botDmProxy
}

// GetLLMProxy 获取LLM服务代理
func GetLLMProxy() llm.ChatClientProxy {
	return lLMProxy
}

// GetWfDmProxy 获取WfDm服务代理
func GetWfDmProxy() KEP_WF_DM.WorkflowDmClientProxy {
	return wfDmProxy
}

// GetQidianProxy 获取企点服务代理
func GetQidianProxy() robot.RobotConfigClientProxy {
	return QidianProxy
}

// GetKnowledgeProxy 获取Knowledge服务代理
func GetKnowledgeProxy() knowledge.ApiClientProxy {
	return knowledgeProxy
}

func GetBotLLMProxy() botllm.LlmClientProxy {
	return botLLMProxy
}

func GetCodeExecProxy() interpreter.CodeExecClientProxy {
	return codeExecProxy
}

//// GetBotDmProxy 获取BotDm服务代理
//func GetBotDmProxy(meta meta0.FuncMeta, ctx0 map[string]string) (KEP.BotDmClientProxy, error) {
//	router, ok := router0.GetRouter(meta, ctx0, BotDmClientProxy)
//	log.Infof("GetBotDmProxy%s|%s|%+v", meta, ok, router)
//	if !ok {
//		return nil, errors.New(BotDmClientProxy + "路由未配置")
//	}
//	opts := []client.Option{
//		client.WithServiceName(router.Service),
//	}
//	opts = append(opts, router.GetNewProxyOptions()...)
//	proxy := KEP.NewBotDmClientProxy(opts...)
//	return proxy, nil
//}
//
//// GetLLMProxy 获取LLM服务代理
//func GetLLMProxy(meta meta0.FuncMeta, ctx0 map[string]string) (PB_SmartAssistant.ChatClientProxy, error) {
//	router, ok := router0.GetRouter(meta, ctx0, LLMManagerObjClient)
//	log.Infof("GetLLMProxy%s|%s|%+v", meta, ok, router)
//	if !ok {
//		return nil, errors.New(LLMManagerObjClient + "路由未配置")
//	}
//	opts := []client.Option{
//		client.WithServiceName(router.Service),
//	}
//	opts = append(opts, router.GetNewProxyOptions()...)
//	proxy := PB_SmartAssistant.NewChatClientProxy(opts...)
//	return proxy, nil
//}
