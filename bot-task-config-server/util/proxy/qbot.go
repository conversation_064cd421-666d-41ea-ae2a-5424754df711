package proxy

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

var qBotChatClient = chat.NewChatClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientQBotChatClient),
	}...)

// GetQBotChatProxy 获取qbot.qbot.chat.chat
func GetQBotChatProxy() chat.ChatClientProxy {
	return qBotChatClient
}

var qBotStreamChatClient = chat.NewStreamChatClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientQBotStreamChatClient),
	}...)

// GetQBotStreamChatProxy 获取qbot.qbot.chat.StreamChat
func GetQBotStreamChatProxy() chat.StreamChatClientProxy {
	return qBotStreamChatClient
}

var qBotFinanceClient = finance.NewFinanceClientProxy(
	[]client.Option{
		client.WithServiceName(util.RpcClientQBotFianceClient),
	}...)

// GetQBotFinanceProxy 获取qbot.finance
func GetQBotFinanceProxy() finance.FinanceClientProxy {
	return qBotFinanceClient
}
