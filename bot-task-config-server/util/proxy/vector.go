// Package proxy ...
// @Author: halelv
// @Date: 2023/12/25 11:48
package proxy

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
)

// vectorClient 向量数据库 Rpc client
var vectorClient = vector_db_manager.NewVectorObjClientProxy(
	[]client.Option{
		// https://iwiki.woa.com/p/284289117#4-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE
		// https://iwiki.woa.com/p/4008319150#b-%E5%85%B3%E9%97%AD%E6%9C%8D%E5%8A%A1%E8%B7%AF%E7%94%B1%EF%BC%9A
		client.WithServiceName(util.RpcClientVector),
	}...)

// GetVectorClient get vector rpc client
func GetVectorClient() vector_db_manager.VectorObjClientProxy {
	return vectorClient
}
