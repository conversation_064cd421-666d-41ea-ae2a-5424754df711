// Package util 工具
// @author: halelv
// @date: 2023/12/20 15:30
package util

// RPC 定时器配置
const (
	// TimerScheduler scheduler by redis
	TimerScheduler = "redis"
	// TimerCheckTaskFlowMigration 任务流迁移任务定时校验
	TimerCheckTaskFlowMigration = "trpc.KEP.bot-task-config-server.TimerCheckTaskFlowMigration"

	// TrpcCliServiceName
	// trpc-cli 相关资料
	// https://iwiki.woa.com/p/194215409
	TrpcCliServiceName = "trpc.client.trpc-cli.service"
)

// RPC Client 配置
const (
	LLMRobotTaskDB     = "mysql.db_llm_robot_task"      // db_llm_robot_task
	LLMRobotTaskProdDB = "mysql.db_llm_robot_task_prod" // db_llm_robot_task_prod

	LLMRobotWorkflowDB     = "mysql.db_llm_robot_workflow"      // db_llm_robot_task
	LLMRobotWorkflowProdDB = "mysql.db_llm_robot_workflow_prod" // db_llm_robot_task_prod

	// LLMRobotTaskDBDel 删除数据mysql配置
	LLMRobotTaskDBDel     = "mysql.db_llm_robot_task.delete"      // db_llm_robot_task
	LLMRobotTaskProdDBDel = "mysql.db_llm_robot_task_prod.delete" // db_llm_robot_task_prod

	LLMRobotWorkflowDBDel     = "mysql.db_llm_robot_workflow.delete"      // db_llm_robot_task
	LLMRobotWorkflowProdDBDel = "mysql.db_llm_robot_workflow_prod.delete" // db_llm_robot_task_prod

	// RpcClientRedis Redis Client
	RpcClientRedis = "redis.bot-task-config-server"

	// RpcClientAdminApiClient Admin Api Client
	RpcClientAdminApiClient = "qbot.qbot.admin.Api"

	// RpcClientAdminLoginClient Admin Login Client
	RpcClientAdminLoginClient = "qbot.qbot.admin.Login"

	// RpcClientAccessManager AccessManager Client
	RpcClientAccessManager = "trpc.SmartService.AccessManagerServer.AccessManager"

	// RpcClientVector Vector Client
	RpcClientVector = "trpc.SmartAssistant.VectorDBManager.VectorObj"

	// RpcClientLLMManagerServer LLMManagerServer Client
	RpcClientLLMManagerServer = "trpc.SmartAssistant.LLMManagerServer.Chat"

	// RpcClientDM DM Client
	RpcClientDM = "trpc.KEP.bot-dm-server.bot-dm"

	// RpcClientWorkflowDM WorkflowDM Client
	RpcClientWorkflowDM = "trpc.KEP.bot-workflow-dm-server.workflow-dm"

	// RpcClientQBotChatClient QBot Chat Client
	RpcClientQBotChatClient = "qbot.qbot.chat.Chat"

	// RpcClientQBotStreamChatClient QBot Chat StreamChat
	RpcClientQBotStreamChatClient = "qbot.qbot.chat.StreamChat"

	// RpcClientQBotFianceClient QBot Fiance Client
	RpcClientQBotFianceClient = "qbot.finance.finance.Fiance"

	// RpcClientKnowledge Knowledge Client
	RpcClientKnowledge = "trpc.KEP.bot-knowledge-config-server.Api"

	// RpcClientCodeExecutor code Client
	RpcClientCodeExecutor = "trpc.KEP.bot-exec-pycode-server.CodeExecutor"

	// RpcPluginConfigApiClient plugin config server api client
	RpcPluginConfigApiClient = "trpc.KEP.plugin_config_server.PluginConfigApi"

	// RpcClientBotLLM Bot LLM Client
	RpcClientBotLLM = "trpc.KEP.bot-llm-server.llm"

	// RpcClientQidian Qidian Client
	RpcClientQidian = "customer_service.robot.robot_config.RobotConfig"
)
