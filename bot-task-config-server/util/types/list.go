// Package types 数据类型相关工具包
// @Author: halelv
// @Date: 2023/12/25 15:50
package types

// SplitStringSlice

// SplitStringSlice ...
//
//	@Description: 将字符串切片按照固定长度切割
//	@param strSlice 待切割切片
//	@param eachSize 单切片长度
//	@return result
func SplitStringSlice(strSlice []string, eachSize int) [][]string {
	// 取模
	mod := len(strSlice) % eachSize
	// 取余
	k := len(strSlice) / eachSize

	// 总片数
	var end int
	if mod == 0 {
		end = k
	} else {
		end = k + 1
	}

	result := make([][]string, 0)
	for i := 0; i < end; i++ {
		if i != k {
			result = append(result, strSlice[i*eachSize:(i+1)*eachSize])
		} else {
			result = append(result, strSlice[i*eachSize:])
		}
	}
	return result
}

// EqualStringSlice ...
//
//	@Description: 判断两个字符串切片中元素是否一致
//	@param strSlice 原切片
//	@param compareSlice 待比较切片
//	@return bool
func EqualStringSlice(strSlice []string, compareSlice []string) bool {
	if len(strSlice) != len(compareSlice) {
		return false
	}

	strMap := make(map[string]string)
	for _, str := range strSlice {
		strMap[str] = str
	}

	compareMap := make(map[string]string)
	for _, str := range compareSlice {
		compareMap[str] = str
	}

	if len(strMap) != len(compareMap) {
		return false
	}

	for k, v := range strMap {
		if compareV, ok := compareMap[k]; !ok || compareV != v {
			return false
		}
	}

	return true
}

// HasDuplicateStringSlice ...
//
//	@Description: 判断字符串切片中是否有重复元素
//	@param strSlice 原切片
//	@return bool
func HasDuplicateStringSlice(strSlice []string) bool {
	strMap := make(map[string]string)
	for _, str := range strSlice {
		if _, ok := strMap[str]; ok {
			return true
		}
		strMap[str] = str
	}
	return false
}

func GetDuplicateStringFromSlice(strSlice []string) []string {
	counts := make(map[string]int)
	for _, str := range strSlice {
		counts[str]++
	}
	duplicateStr := make([]string, 0)
	for str, count := range counts {
		if count > 1 {
			duplicateStr = append(duplicateStr, str)
		}
	}
	return duplicateStr
}
