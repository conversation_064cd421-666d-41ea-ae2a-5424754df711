// Package types 数据类型相关工具包
// @Author: halelv
// @Date: 2023/12/25 16:01
package types

import (
	"reflect"
	"testing"
)

func TestSplitStringSlice(t *testing.T) {
	type args struct {
		strSlice []string
		eachSize int
	}
	tests := []struct {
		name string
		args args
		want [][]string
	}{
		{
			name: "tes1：正好能被整除",
			args: args{
				strSlice: []string{"a", "b", "c", "d", "e", "f"},
				eachSize: 2,
			},
			want: [][]string{
				{"a", "b"},
				{"c", "d"},
				{"e", "f"},
			},
		},
		{
			name: "tes2：不能被整除",
			args: args{
				strSlice: []string{"a", "b", "c", "d", "e", "f", "g"},
				eachSize: 3,
			},
			want: [][]string{
				{"a", "b", "c"},
				{"d", "e", "f"},
				{"g"},
			},
		},
		{
			name: "tes3：切片为空",
			args: args{
				strSlice: []string{},
				eachSize: 3,
			},
			want: [][]string{},
		},
		{
			name: "tes4：切片长度小于每个片段的大小",
			args: args{
				strSlice: []string{"a", "b"},
				eachSize: 3,
			},
			want: [][]string{
				{"a", "b"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SplitStringSlice(tt.args.strSlice, tt.args.eachSize); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SplitStringSlice() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEqualStringSlice(t *testing.T) {
	type args struct {
		strSlice     []string
		compareSlice []string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1: 完全相等",
			args: args{
				strSlice:     []string{"a", "b", "c"},
				compareSlice: []string{"a", "b", "c"},
			},
			want: true,
		},
		{
			name: "test2: 乱序相等",
			args: args{
				strSlice:     []string{"a", "b", "c"},
				compareSlice: []string{"b", "c", "a"},
			},
			want: true,
		},
		{
			name: "test3: 不相等，长度相同",
			args: args{
				strSlice:     []string{"a", "b", "c"},
				compareSlice: []string{"a", "b", "d"},
			},
			want: false,
		},
		{
			name: "test3: 不相等，长度不同",
			args: args{
				strSlice:     []string{"a", "b", "c"},
				compareSlice: []string{"a", "b", "c", "d"},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EqualStringSlice(tt.args.strSlice, tt.args.compareSlice); got != tt.want {
				t.Errorf("EqualStringSlice() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHasDuplicateStringSlice(t *testing.T) {
	type args struct {
		strSlice []string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1: 没有重复元素",
			args: args{
				strSlice: []string{"a", "b", "c", "d"},
			},
			want: false,
		},
		{
			name: "test2: 有1个重复元素",
			args: args{
				strSlice: []string{"a", "a", "c", "d"},
			},
			want: true,
		},
		{
			name: "test3: 有多个重复元素",
			args: args{
				strSlice: []string{"a", "a", "c", "c", "d"},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := HasDuplicateStringSlice(tt.args.strSlice); got != tt.want {
				t.Errorf("HasDuplicateStringSlice() = %v, want %v", got, tt.want)
			}
		})
	}
}
