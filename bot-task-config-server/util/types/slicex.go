// bot-task-config-server
//
// @(#)utils.go  星期一, 十二月 25, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package types

import "golang.org/x/exp/slices"

// Unique returns a new slice with all duplicate values removed.
func Unique[E comparable](s []E) []E {
	unique := make([]E, 0)

	// small sets are better off using loop directly
	// Why 512? because it's a performance dividing line
	if len(s) < 512 {
		for _, v := range s {
			if !slices.Contains(unique, v) {
				unique = append(unique, v)
			}
		}
		return unique
	}

	// large sets are better off using a map
	m := make(map[E]struct{})
	for _, v := range s {
		if _, ok := m[v]; !ok {
			m[v] = struct{}{}
			unique = append(unique, v)
		}
	}
	return unique
}
