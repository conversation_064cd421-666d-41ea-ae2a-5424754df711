package types

import (
	"encoding/json"
	"regexp"
	"strings"

	"git.woa.com/trpc-go/tnet/log"
)

// CheckStrEmpty 判断字符串是否为空，会去掉空格等字符
func CheckStrEmpty(s string) bool {
	// 替换空格
	s = strings.Replace(s, " ", "", -1)
	// 替换回车
	s = strings.Replace(s, "\n", "", -1)
	// 替换tab
	s = strings.Replace(s, "\t", "", -1)
	return len(s) == 0
}

// ConvertSqlFuzzySearch SQL模糊搜索特殊字符处理
func ConvertSqlFuzzySearch(s string) string {
	if strings.Contains(s, "%") || strings.Contains(s, "_") {
		s := strings.ReplaceAll(s, "%", "\\%")
		return strings.ReplaceAll(s, "_", "\\_")
	}
	return s
}

// RemoveRichText 移除富文本
func RemoveRichText(prompt string) string {
	//text := `<p>哈哈哈😃sssd &nbsp;<a href=\"http://www.baidu.com\"
	//target=\"_blank\">请求天气</a> <span data-w-e-type=\"slot\"
	//data-w-e-is-void data-w-e-is-inline data-value=\"api_weather\"
	//data-info="{\"name\":\"API节点-天气\",\"type\":\"params\",\"nodeId\":\"api\"}">
	//$API节点-天气</span>哈哈哈</p>`

	// 处理后： 哈哈哈😃sssd 请求天气 哈哈哈
	if prompt == "" {
		return ""
	}
	// Node ...
	type Node struct {
		Name   string `json:"name"`
		Type   string `json:"type"`
		NodeID string `json:"nodeId"`
	}
	var node Node

	const InfoRegEx = `data-info="({[^}]+})"`
	infoFilter, _ := regexp.Compile(InfoRegEx)
	infos := infoFilter.FindStringSubmatch(prompt)
	if len(infos) >= 2 {
		info := infos[1]
		info = strings.ReplaceAll(info, "\\", "")
		err := json.Unmarshal([]byte(info), &node)
		if err == nil {
			name := node.Name
			prompt = strings.Replace(prompt, "$"+name, "", -1)
		} else {
			log.Errorf("RemoveRichText:%s|%s\n", prompt, err.Error())
		}
	}

	filter := `<p>|<\/p>|<span[^>]*>|<\/span>|<a [^>]*>|<\/a>|<div[^>]*>|<\/div>|<br[^>]*>|<\/br>|&nbsp;`
	return regexp.MustCompile(filter).ReplaceAllString(prompt, "")
}
