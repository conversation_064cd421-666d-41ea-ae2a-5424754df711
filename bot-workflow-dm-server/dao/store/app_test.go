package store

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

var (
	testAppID = "AppID_001"
)

func Test_SaveAppInSandbox(t *testing.T) {
	Convey("SaveAppInSandbox", t, func() {
		req := &KEP_WF_DM.UpsertAppToSandboxRequest{
			AppID:                    testAppID,
			RetrievalWorkflowGroupID: "XXXX",
			RetrievalWorkflowModel:   "modelName",
		}
		err := store.SaveAppInSandbox(context.Background(), req)
		t.Logf("error: %v", err)
		app, err := store.GetApp(context.Background(), 0, testAppID, "")
		t.Logf("error: %v, app: %v", err, app)
	})
}

func Test_GetApp(t *testing.T) {
	<PERSON>vey("GetApp", t, func() {
		app, err := store.GetApp(context.Background(), 0, testAppID, "")
		t.Logf("error: %v, app: %v", err, app)
		app, err = store.GetApp(context.Background(), 1, testAppID, "")
		t.Logf("error: %v, app: %v", err, app)
	})
}

func Test_ReleaseWorkflowApp(t *testing.T) {
	Convey("ReleaseWorkflowApp", t, func() {
		req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
			AppID:              "AppID_001",
			UpsertWorkflowIDs:  []string{"WorkflowID_001"},
			UpsertParameterIDs: []string{"ParameterID_001"},
		}
		err := store.ReleaseWorkflowApp(context.Background(), req)
		t.Logf("error: %v", err)
	})
}

func Test_ReleaseWorkflowApp2(t *testing.T) {
	Convey("ReleaseWorkflowApp delete", t, func() {
		req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
			AppID:             "AppID_001",
			DeleteWorkflowIDs: []string{"WorkflowID_001"},
		}
		err := store.ReleaseWorkflowApp(context.Background(), req)
		t.Logf("error: %v", err)
	})
}

func Test_ReleaseWorkflowApp3(t *testing.T) {
	Convey("ReleaseWorkflowApp 新增槽位", t, func() {
		req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
			AppID:             "AppID_001",
			UpsertWorkflowIDs: []string{"WorkflowID_002"},
			DeleteWorkflowIDs: []string{"WorkflowID_001"},
		}
		err := store.ReleaseWorkflowApp(context.Background(), req)
		t.Logf("error: %v", err)
	})
}

func Test_ReleaseWorkflowApp4(t *testing.T) {
	Convey("ReleaseWorkflowApp delete 删除槽位", t, func() {
		req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
			AppID:             "AppID_001",
			UpsertWorkflowIDs: []string{"WorkflowID_002"},
		}
		err := store.ReleaseWorkflowApp(context.Background(), req)
		t.Logf("error: %v", err)
	})
}

func Test_DeleteWorkflowApp(t *testing.T) {
	Convey("DeleteWorkflowApp", t, func() {
		err := store.DeleteWorkflowApp(context.Background(), "AppID_001")
		t.Logf("error: %v", err)
	})
}
