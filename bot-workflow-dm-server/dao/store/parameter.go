package store

import (
	"context"
	"encoding/json"
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// GetParametersKey 参数的存储KEY
func GetParametersKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.ParametersKeyFormat, getEnvKey(runEnv), appID)
}

// // GetEntriesKey 参数的词条的存储KEY
// func GetEntriesKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
//	return fmt.Sprintf(tconst.EntriesKeyFormat, getEnvKey(runEnv), appID)
// }
//
// // GetInvalidEntriesKey 参数的错误示例的存储KEY
// func GetInvalidEntriesKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
//	return fmt.Sprintf(tconst.InvalidEntriesKeyFormat, getEnvKey(runEnv), appID)
// }

// SaveParametersInSandbox 保存参数
func (r *RedisStore) SaveParametersInSandbox(ctx context.Context, appID string,
	parameters []*KEP_WF_DM.Parameter) error {
	LogRedis(ctx).Infof("SaveParametersInSandbox, appID: %v, parameters: %v,", appID, parameters)

	parameterMapKey := GetParametersKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	idValues := make([]string, 0, len(parameters)*2)
	for _, parameter := range parameters {
		parameterData := util.ToJsonString(parameter)
		idValues = append(idValues, []string{parameter.ParameterID, parameterData}...)
	}
	err := r.client.HSet(ctx, parameterMapKey, idValues).Err()
	if err != nil {
		Log(ctx).Errorf("HSet failed, parameterMapKey: %s, error: %v", parameterMapKey, err)
		return err
	}
	LogRedis(ctx).Infof("SaveParametersInSandbox, done")

	return nil
}

// DeleteParametersInSandbox 删除沙箱的参数
func (r *RedisStore) DeleteParametersInSandbox(ctx context.Context, appID string, parameterIDs []string) error {
	LogRedis(ctx).Infof("DeleteParametersInSandbox, appID:%v, parameterIDs:%v", appID, parameterIDs)
	parameterMapKey := GetParametersKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)

	result, err := r.client.HDel(ctx, parameterMapKey, parameterIDs...).Result()
	if err != nil {
		Log(ctx).Errorf("HDel failed, parameterMapKey: %s, parameterIDs: %v, error: %v", parameterMapKey, parameterIDs, err)
		return err
	}
	if result <= 0 {
		Log(ctx).Warnf("HDel result is: %s, parameterMapKey: %s, parameterIDs: %v, error: %v",
			result, parameterMapKey, parameterIDs, err)
	}

	LogRedis(ctx).Infof("DeleteParametersInSandbox, done")
	return nil
}

// GetParameters 获取应用下的参数
func (r *RedisStore) GetParameters(ctx context.Context, runEnv KEP_WF_DM.RunEnvType,
	appID string) (map[string]*KEP_WF_DM.Parameter,
	error) {
	parameterMapKey := GetParametersKey(runEnv, appID)
	result, err := r.client.HGetAll(ctx, parameterMapKey).Result()
	if err != nil {
		Log(ctx).Errorf("HGetAll failed, parameterMapKey: %s, error: %v", parameterMapKey, err)
		return nil, err
	}
	parameters := make(map[string]*KEP_WF_DM.Parameter, len(result))
	for parameterID, parameterData := range result {
		parameter := &KEP_WF_DM.Parameter{}
		err = json.Unmarshal([]byte(parameterData), parameter)
		if err != nil {
			Log(ctx).Errorf("invalid parameterInfo: %s, error: %v", parameterData, err)
			return nil, err
		}
		parameters[parameterID] = parameter
	}
	return parameters, nil
}

// // GetParameter 获取单个参数
// func (r *RedisStore) GetParameter(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (
//	*KEP_WF_DM.Parameter, error) {
//	parameterMapKey := GetParametersKey(runEnv, appID)
//	result, err := r.client.HGet(ctx, parameterMapKey, parameterID).Result()
//	if err != nil {
//		Log(ctx).Errorf("HGet failed, parameterMapKey: %s, parameterID: %v, error: %v", parameterMapKey, parameterID, err)
//		return nil, err
//	}
//	parameter := &KEP_WF_DM.Parameter{}
//	err = protojson.Unmarshal([]byte(result), parameter)
//	if err != nil {
//		Log(ctx).Errorf("invalid parameter data: %s, error: %v", result, err)
//		return nil, err
//	}
//	return parameter, nil
// }

// GetEntries 获取参数的词条（正确示例）
// func (r *RedisStore) GetEntries(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (
//	map[string]*entity.Entry, error) {
//	entriesMapKey := GetEntriesKey(runEnv, appID)
//	result, err := r.client.HGet(ctx, entriesMapKey, parameterID).Result()
//	if err != nil {
//		if r.IsNotFound(err) {
//			return nil, nil
//		}
//		Log(ctx).Errorf("HGet failed, entriesMapKey: %s, error: %v", entriesMapKey, err)
//		return nil, err
//	}
//	entriesMap := make(map[string]*entity.Entry)
//	err = json.Unmarshal([]byte(result), &entriesMap)
//	if err != nil {
//		Log(ctx).Errorf("invalid examples: %s, error: %v", result, err)
//		return nil, err
//	}
//	return entriesMap, nil
// }
//
// // GetInvalidEntries 获取参数的错误示例
// func (r *RedisStore) GetInvalidEntries(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (
//	map[string]*entity.Entry, error) {
//	entriesMapKey := GetInvalidEntriesKey(runEnv, appID)
//	result, err := r.client.HGet(ctx, entriesMapKey, parameterID).Result()
//	if err != nil {
//		if r.IsNotFound(err) {
//			return nil, nil
//		}
//		Log(ctx).Errorf("HGet failed, entriesMapKey: %s, error: %v", entriesMapKey, err)
//		return nil, err
//	}
//	entriesMap := make(map[string]*entity.Entry)
//	err = json.Unmarshal([]byte(result), &entriesMap)
//	if err != nil {
//		Log(ctx).Errorf("invalid examples: %s, error: %v", result, err)
//		return nil, err
//	}
//	return entriesMap, nil
// }
