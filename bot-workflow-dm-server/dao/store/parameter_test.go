package store

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

var parameters = []*KEP_WF_DM.Parameter{
	{
		ParameterID:   "ParameterID_001",
		ParameterName: "ParameterName_001",
		ParameterDesc: "ParameterDesc_001",
	},
	{
		ParameterID:   "ParameterID_002",
		ParameterName: "ParameterName_002",
		ParameterDesc: "ParameterDesc_002",
	},
}

func Test_SaveSaveParametersInSandbox(t *testing.T) {
	Convey("SaveParametersInSandbox", t, func() {
		err := store.SaveParametersInSandbox(context.Background(), testAppID, parameters)
		t.Logf("error: %v", err)
	})
}

func Test_DeleteParametersInSandbox(t *testing.T) {
	Convey("DeleteParametersInSandbox", t, func() {
		err := store.DeleteParametersInSandbox(context.Background(), testAppID, []string{"ParameterID_001"})
		t.Logf("error: %v", err)
	})
}

func Test_GetParameters(t *testing.T) {
	Convey("GetParameters", t, func() {
		parameters, err := store.GetParameters(context.Background(), KEP_WF_DM.RunEnvType_SANDBOX, testAppID)
		t.Logf("error: %v", err)
		for parameterID, parameter := range parameters {
			t.Logf("parameterID: %v, parameter: %v", parameterID, parameter)
		}
	})
}
