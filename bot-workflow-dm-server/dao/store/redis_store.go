package store

import (
	"errors"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"github.com/go-redis/redis/v8"
)

// RedisStore redis的存储
type RedisStore struct {
	client redis.UniversalClient
}

// NewBaseStore 基本的存储
func NewBaseStore() (BaseStore, error) {
	redisClient, err := goredis.New(tconst.DMRedisServiceName, nil)
	if err != nil {
		return nil, err
	}
	return &RedisStore{
		client: redisClient,
	}, nil
}

// IsNotFound 判断是否为key不存在
func (r *RedisStore) IsNotFound(err error) bool {
	return errors.Is(err, redis.Nil)
}
