package store

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"google.golang.org/protobuf/encoding/protojson"
)

func getSessionKey(runEnv KEP_WF_DM.RunEnvType, appID, sessionID string) string {
	return fmt.Sprintf(tconst.SessionKeyFormat, getEnvKey(runEnv), appID, sessionID)
}

// GetSession 获取session
func (r *RedisStore) GetSession(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, sessionID,
	appID, withWorkflowID string) (session *entity.Session, err error) {
	// 先取存储中的
	sessionStr, err := r.client.Get(ctx, getSessionKey(runEnv, appID, sessionID)).Result()
	if err != nil && !r.IsNotFound(err) {
		Log(ctx).Errorf("GetSession failed, runEnv: %v, appID: %v, error: %v", runEnv, appID, err)
		return nil, err
	}
	if err == nil {
		session = &entity.Session{}
		err = json.Unmarshal([]byte(sessionStr), session)
		if err != nil {
			Log(ctx).Errorf("Unmarshal session failed, sessionStr: %v, error: %v", sessionStr, err)
			return nil, err
		}
		if session.AppID != appID {
			Log(ctx).Errorf("invalid appID: %v, session.AppID: %v", appID, session.AppID)
			return nil, fmt.Errorf("invalid appID: %v, session.AppID: %v", appID, session.AppID)
		}
		if session.App != nil && session.App.WorkflowStrMap != nil {
			session.App.Workflows = make(map[string]*KEP_WF.Workflow)
			for workflowID, workflowStr := range session.App.WorkflowStrMap {
				workflow := &KEP_WF.Workflow{}
				err = protojson.Unmarshal([]byte(workflowStr), workflow)
				if err != nil {
					Log(ctx).Errorf("Unmarshal session failed, sessionStr: %v, error: %v", sessionStr, err)
					return nil, err
				}
				session.App.Workflows[workflowID] = workflow
			}
		}
		if session.App == nil {
			Log(ctx).Errorf("invalid session data, App is nil")
			return nil, fmt.Errorf("invalid session data, App is nil")
		}
		session.App.WorkflowStrMap = nil
		return session, nil
	}

	// 新session
	session = &entity.Session{
		RunEnv:    runEnv,
		SessionID: sessionID,
		AppID:     appID,
	}
	if session.SessionID == "" {
		session.SessionID = entity.GenSessionID()
	}
	Log(ctx).Infof("new conversation, appID: %v, sessionID: %v", appID, session.SessionID)

	session.App, err = r.GetApp(ctx, runEnv, appID, withWorkflowID)
	if err != nil {
		Log(ctx).Errorf("GetApp failed, runEnv: %v, appID: %v, error: %v", runEnv, appID, err)
		return nil, err
	}
	return session, nil
}

// SaveSession 保存session
func (r *RedisStore) SaveSession(ctx context.Context, session *entity.Session) error {
	sessionKey := getSessionKey(session.RunEnv, session.AppID, session.SessionID)
	if session.App != nil && session.App.Workflows != nil {
		session.App.WorkflowStrMap = make(map[string]string)
		for workflowID, workflow := range session.App.Workflows {
			workflowBytes, _ := protojson.Marshal(workflow)
			session.App.WorkflowStrMap[workflowID] = string(workflowBytes)
		}
	}
	sessionStr := util.ToJsonString(session)
	expiredTime := time.Duration(config.GetMainConfig().Session.SaveMaxIdleTime) * time.Minute

	LogRedis(ctx).Infof("sessionKey: %v, sessionStr: %v, expiredTime:%v", sessionKey, sessionStr, expiredTime)
	err := r.client.Set(context.Background(), sessionKey, sessionStr, expiredTime).Err()
	return err
}

// DeleteSession 保存session
func (r *RedisStore) DeleteSession(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, sessionID string) (int64,
	error) {
	sessionKey := getSessionKey(runEnv, appID, sessionID)
	LogRedis(ctx).Infof("DeleteSession, sessionKey: %v", sessionKey)
	result, err := r.client.Del(ctx, sessionKey).Result()
	return result, err
}
