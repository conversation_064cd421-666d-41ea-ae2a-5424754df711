package store

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

func Test_session(t *testing.T) {
	Convey("保存、读取", t, func() {
		session := &entity.Session{
			RunEnv:    KEP_WF_DM.RunEnvType_SANDBOX,
			SessionID: "SessionID_001",
			AppID:     "RobotID_001",
			// Robot: &entity.Robot{
			//	Intents: map[string]*entity.Intent{"": {}},
			// },
			// IntentStack:       nil,
			// ConfirmingSlotIDs: nil,
		}
		err := store.SaveSession(context.Background(), session)
		ShouldBeEmpty(err)

		getSession, _ := store.GetSession(context.Background(), KEP_WF_DM.RunEnvType_SANDBOX, "SessionID_001", "RobotID_001", "")
		ShouldEqual(session, getSession)
	})
}

func Test_DeleteSession(t *testing.T) {
	Convey("delete", t, func() {
		result, err := store.DeleteSession(context.Background(), KEP_WF_DM.RunEnvType_SANDBOX, "not_found", "not_found")
		ShouldBeEmpty(err)
		So(result, ShouldEqual, 0)
	})
}
