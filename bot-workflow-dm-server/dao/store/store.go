// Package store DM的存储
package store

//go:generate mockgen -destination store_mock.go -package store . Store

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	tGorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/task_store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/go-redis/redis/v8"
)

const (
	// AllWorkflows 全部工作流
	AllWorkflows = "AllWorkflows"
)

// LogRedis LogRedis
var LogRedis = logger.LogRedis

// LogDB LogDB
var LogDB = logger.LogDB

// Log Log
var Log = logger.Log

var defaultStore DMStore

// Store 持久化的接口
type Store interface {
	BaseStore
	WorkflowRunStore
	NodeRunStore
	task_store.TaskStore
}

// BaseStore 基本的存储
type BaseStore interface {
	// IsNotFound 是否不存在
	IsNotFound(err error) bool
	// GetSession 获取session
	GetSession(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, sessionID, appID,
		workflowID string) (session *entity.Session, err error)
	// SaveSession 保存session
	SaveSession(ctx context.Context, session *entity.Session) error
	// DeleteSession 保存session
	DeleteSession(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, sessionID string) (int64, error)

	// SaveAppInSandbox 保存应用
	SaveAppInSandbox(ctx context.Context, req *KEP_WF_DM.UpsertAppToSandboxRequest) error
	// ReleaseWorkflowApp 发布应用
	ReleaseWorkflowApp(ctx context.Context, appInfo *KEP_WF_DM.ReleaseWorkflowAppRequest) error
	// DeleteWorkflowApp 删除应用
	DeleteWorkflowApp(ctx context.Context, appID string) error
	// GetApp 获取机器人信息
	GetApp(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) (*entity.App, error)

	// SaveWorkflowsInSandbox 保存工作流
	SaveWorkflowsInSandbox(ctx context.Context, appID string, workflows []*KEP_WF.Workflow) error
	// DeleteWorkflowsInSandbox 删除沙箱的工作流
	DeleteWorkflowsInSandbox(ctx context.Context, appID string, workflowIDs []string) error
	// GetWorkflows 获取应用下的工作流列表
	GetWorkflows(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID string) (map[string]*KEP_WF.Workflow, error)
	// GetWorkflow 获取单个工作流
	GetWorkflow(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) (*KEP_WF.Workflow, error)
	// GetWorkflowExamples 获取工作流示例问法
	GetWorkflowExamples(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) ([]string, error)

	// SaveParametersInSandbox 保存参数
	SaveParametersInSandbox(ctx context.Context, appID string, parameters []*KEP_WF_DM.Parameter) error
	// DeleteParametersInSandbox 删除沙箱的参数
	DeleteParametersInSandbox(ctx context.Context, appID string, parameterIDs []string) error
	// GetParameters 获取应用下的参数
	GetParameters(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID string) (map[string]*KEP_WF_DM.Parameter, error)
	// // GetParameter 获取单个参数
	// GetParameter(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (*KEP_WF_DM.Parameter, error)
	// // GetEntries 获取参数的词条（正确示例）
	// GetEntries(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (map[string]*entity.Entry,
	//	error)
	// // GetInvalidEntries 获取参数的错误示例
	// GetInvalidEntries(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, parameterID string) (
	//	map[string]*entity.Entry, error)

	// SaveVariablesInSandbox 保存槽位
	SaveVariablesInSandbox(ctx context.Context, appID string, variables []*KEP_WF_DM.Var) error
	// DeleteVariablesInSandbox 删除沙箱的槽位
	DeleteVariablesInSandbox(ctx context.Context, appID string, varIDs []string) error
	// GetVariables 获取自定义参数
	GetVariables(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID string) (map[string]*KEP_WF_DM.Var, error)

	// GetCorpInfo 获取企业信息(uin, sid)
	GetCorpInfo(ctx context.Context, corpID uint64) (uint64, uint32, error)
	// SetCorpInfo 设置企业信息(uin, sid)
	SetCorpInfo(ctx context.Context, corpID uint64, uin uint64, sid uint32) error
}

// DMStore TODO
type DMStore struct {
	*RedisStore
	*nodeRunStore
	*workflowRunStore
	task_store.TaskStore
}

// Init 初始化
func Init() {
	var err error
	redisClient, err := goredis.New(tconst.DMRedisServiceName, nil)
	if err != nil {
		Log().Errorf("init redisClient failed, error: %v", err)
		panic(err)
	}
	db, err := tGorm.NewClientProxy(tconst.RpcClientDB)
	if err != nil {
		Log().Errorf("init db client failed. proxy=%s", tconst.RpcClientDB)
		panic(err)
	}
	defaultStore = DMStore{
		RedisStore: &RedisStore{
			client: redisClient,
		},
		nodeRunStore:     &nodeRunStore{db: db},
		workflowRunStore: &workflowRunStore{db: db},
		TaskStore:        task_store.NewStoreByClient(redisClient),
	}
}

// NewDMStore 新建存储
func NewDMStore() (Store, error) {
	var err error
	redisClient, err := goredis.New(tconst.DMRedisServiceName, nil)
	if err != nil {
		return nil, err
	}
	db, err := tGorm.NewClientProxy(tconst.RpcClientDB)
	if err != nil {
		Log().Errorf("init db client failed. proxy=%s", tconst.RpcClientDB)
		return nil, err
	}
	return DMStore{
		RedisStore: &RedisStore{
			client: redisClient,
		},
		nodeRunStore:     &nodeRunStore{db: db},
		workflowRunStore: &workflowRunStore{db: db},
	}, nil
}

func getEnvKey(runEnv KEP_WF_DM.RunEnvType) string {
	if runEnv == KEP_WF_DM.RunEnvType_PRODUCT {
		return "Product"
	} else {
		return "Sandbox"
	}
}

// SetDefaultRedis 设置默认的redis存储
func SetDefaultRedis(redisClient redis.UniversalClient) {
	defaultStore = DMStore{
		RedisStore: &RedisStore{
			client: redisClient,
		},
		nodeRunStore:     &nodeRunStore{db: nil},
		workflowRunStore: &workflowRunStore{db: nil},
	}
}

// Default 默认的存储对象
func Default() Store {
	return defaultStore
}
