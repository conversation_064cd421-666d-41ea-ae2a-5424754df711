// Code generated by MockGen. DO NOT EDIT.
// Source: git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store (interfaces: Store)

// Package store is a generated GoMock package.
package store

import (
	context "context"
	reflect "reflect"
	time "time"

	entity "git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	model "git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	KEP_WF "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	KEP_WF_DM "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
)

// MockStore is a mock of Store interface.
type MockStore struct {
	ctrl     *gomock.Controller
	recorder *MockStoreMockRecorder
}

// MockStoreMockRecorder is the mock recorder for MockStore.
type MockStoreMockRecorder struct {
	mock *MockStore
}

// NewMockStore creates a new mock instance.
func NewMockStore(ctrl *gomock.Controller) *MockStore {
	mock := &MockStore{ctrl: ctrl}
	mock.recorder = &MockStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStore) EXPECT() *MockStoreMockRecorder {
	return m.recorder
}

// CancellationPubSub mocks base method.
func (m *MockStore) CancellationPubSub(arg0 context.Context) (*redis.PubSub, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancellationPubSub", arg0)
	ret0, _ := ret[0].(*redis.PubSub)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancellationPubSub indicates an expected call of CancellationPubSub.
func (mr *MockStoreMockRecorder) CancellationPubSub(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancellationPubSub", reflect.TypeOf((*MockStore)(nil).CancellationPubSub), arg0)
}

// DeleteParametersInSandbox mocks base method.
func (m *MockStore) DeleteParametersInSandbox(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteParametersInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteParametersInSandbox indicates an expected call of DeleteParametersInSandbox.
func (mr *MockStoreMockRecorder) DeleteParametersInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteParametersInSandbox", reflect.TypeOf((*MockStore)(nil).DeleteParametersInSandbox), arg0, arg1, arg2)
}

// DeleteSession mocks base method.
func (m *MockStore) DeleteSession(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2, arg3 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSession", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSession indicates an expected call of DeleteSession.
func (mr *MockStoreMockRecorder) DeleteSession(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSession", reflect.TypeOf((*MockStore)(nil).DeleteSession), arg0, arg1, arg2, arg3)
}

// DeleteVariablesInSandbox mocks base method.
func (m *MockStore) DeleteVariablesInSandbox(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVariablesInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVariablesInSandbox indicates an expected call of DeleteVariablesInSandbox.
func (mr *MockStoreMockRecorder) DeleteVariablesInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVariablesInSandbox", reflect.TypeOf((*MockStore)(nil).DeleteVariablesInSandbox), arg0, arg1, arg2)
}

// DeleteWorkflowApp mocks base method.
func (m *MockStore) DeleteWorkflowApp(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflowApp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkflowApp indicates an expected call of DeleteWorkflowApp.
func (mr *MockStoreMockRecorder) DeleteWorkflowApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflowApp", reflect.TypeOf((*MockStore)(nil).DeleteWorkflowApp), arg0, arg1)
}

// DeleteWorkflowsInSandbox mocks base method.
func (m *MockStore) DeleteWorkflowsInSandbox(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflowsInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkflowsInSandbox indicates an expected call of DeleteWorkflowsInSandbox.
func (mr *MockStoreMockRecorder) DeleteWorkflowsInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflowsInSandbox", reflect.TypeOf((*MockStore)(nil).DeleteWorkflowsInSandbox), arg0, arg1, arg2)
}

// Dequeue mocks base method.
func (m *MockStore) Dequeue(arg0 context.Context, arg1 []string) (*entity.TaskMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dequeue", arg0, arg1)
	ret0, _ := ret[0].(*entity.TaskMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dequeue indicates an expected call of Dequeue.
func (mr *MockStoreMockRecorder) Dequeue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dequeue", reflect.TypeOf((*MockStore)(nil).Dequeue), arg0, arg1)
}

// Done mocks base method.
func (m *MockStore) Done(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Done", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Done indicates an expected call of Done.
func (mr *MockStoreMockRecorder) Done(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Done", reflect.TypeOf((*MockStore)(nil).Done), arg0, arg1, arg2)
}

// Enqueue mocks base method.
func (m *MockStore) Enqueue(arg0 context.Context, arg1 *entity.TaskMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Enqueue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Enqueue indicates an expected call of Enqueue.
func (mr *MockStoreMockRecorder) Enqueue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Enqueue", reflect.TypeOf((*MockStore)(nil).Enqueue), arg0, arg1)
}

// ExtendLease mocks base method.
func (m *MockStore) ExtendLease(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtendLease", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExtendLease indicates an expected call of ExtendLease.
func (mr *MockStoreMockRecorder) ExtendLease(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtendLease", reflect.TypeOf((*MockStore)(nil).ExtendLease), arg0, arg1, arg2)
}

// GetAllUinTaskCounts mocks base method.
func (m *MockStore) GetAllUinTaskCounts(arg0 context.Context) (map[string]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUinTaskCounts", arg0)
	ret0, _ := ret[0].(map[string]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUinTaskCounts indicates an expected call of GetAllUinTaskCounts.
func (mr *MockStoreMockRecorder) GetAllUinTaskCounts(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUinTaskCounts", reflect.TypeOf((*MockStore)(nil).GetAllUinTaskCounts), arg0)
}

// GetApp mocks base method.
func (m *MockStore) GetApp(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2, arg3 string) (*entity.App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApp", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*entity.App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApp indicates an expected call of GetApp.
func (mr *MockStoreMockRecorder) GetApp(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApp", reflect.TypeOf((*MockStore)(nil).GetApp), arg0, arg1, arg2, arg3)
}

// GetCorpInfo mocks base method.
func (m *MockStore) GetCorpInfo(arg0 context.Context, arg1 uint64) (uint64, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCorpInfo", arg0, arg1)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCorpInfo indicates an expected call of GetCorpInfo.
func (mr *MockStoreMockRecorder) GetCorpInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCorpInfo", reflect.TypeOf((*MockStore)(nil).GetCorpInfo), arg0, arg1)
}

// GetNodeRunsByWorkflowRunID mocks base method.
func (m *MockStore) GetNodeRunsByWorkflowRunID(arg0 context.Context, arg1 string) ([]*model.NodeRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeRunsByWorkflowRunID", arg0, arg1)
	ret0, _ := ret[0].([]*model.NodeRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeRunsByWorkflowRunID indicates an expected call of GetNodeRunsByWorkflowRunID.
func (mr *MockStoreMockRecorder) GetNodeRunsByWorkflowRunID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeRunsByWorkflowRunID", reflect.TypeOf((*MockStore)(nil).GetNodeRunsByWorkflowRunID), arg0, arg1)
}

// GetNodeRunsByWorkflowRunIDAndState mocks base method.
func (m *MockStore) GetNodeRunsByWorkflowRunIDAndState(arg0 context.Context, arg1 string, arg2 model.NodeRunState) ([]*model.NodeRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeRunsByWorkflowRunIDAndState", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.NodeRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeRunsByWorkflowRunIDAndState indicates an expected call of GetNodeRunsByWorkflowRunIDAndState.
func (mr *MockStoreMockRecorder) GetNodeRunsByWorkflowRunIDAndState(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeRunsByWorkflowRunIDAndState", reflect.TypeOf((*MockStore)(nil).GetNodeRunsByWorkflowRunIDAndState), arg0, arg1, arg2)
}

// GetParameters mocks base method.
func (m *MockStore) GetParameters(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2 string) (map[string]*KEP_WF_DM.Parameter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameters", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]*KEP_WF_DM.Parameter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameters indicates an expected call of GetParameters.
func (mr *MockStoreMockRecorder) GetParameters(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameters", reflect.TypeOf((*MockStore)(nil).GetParameters), arg0, arg1, arg2)
}

// GetSession mocks base method.
func (m *MockStore) GetSession(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2, arg3, arg4 string) (*entity.Session, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSession", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*entity.Session)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSession indicates an expected call of GetSession.
func (mr *MockStoreMockRecorder) GetSession(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSession", reflect.TypeOf((*MockStore)(nil).GetSession), arg0, arg1, arg2, arg3, arg4)
}

// GetTaskAction mocks base method.
func (m *MockStore) GetTaskAction(arg0 context.Context, arg1 string) (entity.TaskAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskAction", arg0, arg1)
	ret0, _ := ret[0].(entity.TaskAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskAction indicates an expected call of GetTaskAction.
func (mr *MockStoreMockRecorder) GetTaskAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskAction", reflect.TypeOf((*MockStore)(nil).GetTaskAction), arg0, arg1)
}

// GetUinTaskCount mocks base method.
func (m *MockStore) GetUinTaskCount(arg0 context.Context, arg1 string) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUinTaskCount", arg0, arg1)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUinTaskCount indicates an expected call of GetUinTaskCount.
func (mr *MockStoreMockRecorder) GetUinTaskCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUinTaskCount", reflect.TypeOf((*MockStore)(nil).GetUinTaskCount), arg0, arg1)
}

// GetVariables mocks base method.
func (m *MockStore) GetVariables(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2 string) (map[string]*KEP_WF_DM.Var, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVariables", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]*KEP_WF_DM.Var)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVariables indicates an expected call of GetVariables.
func (mr *MockStoreMockRecorder) GetVariables(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVariables", reflect.TypeOf((*MockStore)(nil).GetVariables), arg0, arg1, arg2)
}

// GetWorkflow mocks base method.
func (m *MockStore) GetWorkflow(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2, arg3 string) (*KEP_WF.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*KEP_WF.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockStoreMockRecorder) GetWorkflow(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockStore)(nil).GetWorkflow), arg0, arg1, arg2, arg3)
}

// GetWorkflowExamples mocks base method.
func (m *MockStore) GetWorkflowExamples(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2, arg3 string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowExamples", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowExamples indicates an expected call of GetWorkflowExamples.
func (mr *MockStoreMockRecorder) GetWorkflowExamples(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowExamples", reflect.TypeOf((*MockStore)(nil).GetWorkflowExamples), arg0, arg1, arg2, arg3)
}

// GetWorkflowRunByRunID mocks base method.
func (m *MockStore) GetWorkflowRunByRunID(arg0 context.Context, arg1 string) (*model.WorkflowRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowRunByRunID", arg0, arg1)
	ret0, _ := ret[0].(*model.WorkflowRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowRunByRunID indicates an expected call of GetWorkflowRunByRunID.
func (mr *MockStoreMockRecorder) GetWorkflowRunByRunID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowRunByRunID", reflect.TypeOf((*MockStore)(nil).GetWorkflowRunByRunID), arg0, arg1)
}

// GetWorkflows mocks base method.
func (m *MockStore) GetWorkflows(arg0 context.Context, arg1 KEP_WF_DM.RunEnvType, arg2 string) (map[string]*KEP_WF.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflows", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]*KEP_WF.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflows indicates an expected call of GetWorkflows.
func (mr *MockStoreMockRecorder) GetWorkflows(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflows", reflect.TypeOf((*MockStore)(nil).GetWorkflows), arg0, arg1, arg2)
}

// IsNotFound mocks base method.
func (m *MockStore) IsNotFound(arg0 error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNotFound", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsNotFound indicates an expected call of IsNotFound.
func (mr *MockStoreMockRecorder) IsNotFound(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNotFound", reflect.TypeOf((*MockStore)(nil).IsNotFound), arg0)
}

// IsTaskExisted mocks base method.
func (m *MockStore) IsTaskExisted(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTaskExisted", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTaskExisted indicates an expected call of IsTaskExisted.
func (mr *MockStoreMockRecorder) IsTaskExisted(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTaskExisted", reflect.TypeOf((*MockStore)(nil).IsTaskExisted), arg0, arg1)
}

// IsTaskExpired mocks base method.
func (m *MockStore) IsTaskExpired(arg0 context.Context, arg1, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTaskExpired", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTaskExpired indicates an expected call of IsTaskExpired.
func (mr *MockStoreMockRecorder) IsTaskExpired(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTaskExpired", reflect.TypeOf((*MockStore)(nil).IsTaskExpired), arg0, arg1, arg2)
}

// ListLeaseExpired mocks base method.
func (m *MockStore) ListLeaseExpired(arg0 context.Context, arg1 time.Time, arg2 []string) ([]*entity.TaskMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLeaseExpired", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.TaskMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLeaseExpired indicates an expected call of ListLeaseExpired.
func (mr *MockStoreMockRecorder) ListLeaseExpired(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLeaseExpired", reflect.TypeOf((*MockStore)(nil).ListLeaseExpired), arg0, arg1, arg2)
}

// PublishCancellation mocks base method.
func (m *MockStore) PublishCancellation(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishCancellation", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishCancellation indicates an expected call of PublishCancellation.
func (mr *MockStoreMockRecorder) PublishCancellation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishCancellation", reflect.TypeOf((*MockStore)(nil).PublishCancellation), arg0, arg1)
}

// ReleaseWorkflowApp mocks base method.
func (m *MockStore) ReleaseWorkflowApp(arg0 context.Context, arg1 *KEP_WF_DM.ReleaseWorkflowAppRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseWorkflowApp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseWorkflowApp indicates an expected call of ReleaseWorkflowApp.
func (mr *MockStoreMockRecorder) ReleaseWorkflowApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseWorkflowApp", reflect.TypeOf((*MockStore)(nil).ReleaseWorkflowApp), arg0, arg1)
}

// Requeue mocks base method.
func (m *MockStore) Requeue(arg0 context.Context, arg1 *entity.TaskMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Requeue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Requeue indicates an expected call of Requeue.
func (mr *MockStoreMockRecorder) Requeue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Requeue", reflect.TypeOf((*MockStore)(nil).Requeue), arg0, arg1)
}

// SaveAppInSandbox mocks base method.
func (m *MockStore) SaveAppInSandbox(arg0 context.Context, arg1 *KEP_WF_DM.UpsertAppToSandboxRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAppInSandbox", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveAppInSandbox indicates an expected call of SaveAppInSandbox.
func (mr *MockStoreMockRecorder) SaveAppInSandbox(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAppInSandbox", reflect.TypeOf((*MockStore)(nil).SaveAppInSandbox), arg0, arg1)
}

// SaveNodeRun mocks base method.
func (m *MockStore) SaveNodeRun(arg0 context.Context, arg1 *model.NodeRun) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveNodeRun", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveNodeRun indicates an expected call of SaveNodeRun.
func (mr *MockStoreMockRecorder) SaveNodeRun(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveNodeRun", reflect.TypeOf((*MockStore)(nil).SaveNodeRun), arg0, arg1)
}

// SaveParametersInSandbox mocks base method.
func (m *MockStore) SaveParametersInSandbox(arg0 context.Context, arg1 string, arg2 []*KEP_WF_DM.Parameter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveParametersInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveParametersInSandbox indicates an expected call of SaveParametersInSandbox.
func (mr *MockStoreMockRecorder) SaveParametersInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveParametersInSandbox", reflect.TypeOf((*MockStore)(nil).SaveParametersInSandbox), arg0, arg1, arg2)
}

// SaveSession mocks base method.
func (m *MockStore) SaveSession(arg0 context.Context, arg1 *entity.Session) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveSession", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveSession indicates an expected call of SaveSession.
func (mr *MockStoreMockRecorder) SaveSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveSession", reflect.TypeOf((*MockStore)(nil).SaveSession), arg0, arg1)
}

// SaveVariablesInSandbox mocks base method.
func (m *MockStore) SaveVariablesInSandbox(arg0 context.Context, arg1 string, arg2 []*KEP_WF_DM.Var) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveVariablesInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveVariablesInSandbox indicates an expected call of SaveVariablesInSandbox.
func (mr *MockStoreMockRecorder) SaveVariablesInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveVariablesInSandbox", reflect.TypeOf((*MockStore)(nil).SaveVariablesInSandbox), arg0, arg1, arg2)
}

// SaveWorkflowsInSandbox mocks base method.
func (m *MockStore) SaveWorkflowsInSandbox(arg0 context.Context, arg1 string, arg2 []*KEP_WF.Workflow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWorkflowsInSandbox", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveWorkflowsInSandbox indicates an expected call of SaveWorkflowsInSandbox.
func (mr *MockStoreMockRecorder) SaveWorkflowsInSandbox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWorkflowsInSandbox", reflect.TypeOf((*MockStore)(nil).SaveWorkflowsInSandbox), arg0, arg1, arg2)
}

// SetCorpInfo mocks base method.
func (m *MockStore) SetCorpInfo(arg0 context.Context, arg1, arg2 uint64, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCorpInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCorpInfo indicates an expected call of SetCorpInfo.
func (mr *MockStoreMockRecorder) SetCorpInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCorpInfo", reflect.TypeOf((*MockStore)(nil).SetCorpInfo), arg0, arg1, arg2, arg3)
}

// SetTaskAction mocks base method.
func (m *MockStore) SetTaskAction(arg0 context.Context, arg1 string, arg2 entity.TaskAction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTaskAction", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTaskAction indicates an expected call of SetTaskAction.
func (mr *MockStoreMockRecorder) SetTaskAction(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTaskAction", reflect.TypeOf((*MockStore)(nil).SetTaskAction), arg0, arg1, arg2)
}

// SetWorkflowRunResult mocks base method.
func (m *MockStore) SetWorkflowRunResult(arg0 context.Context, arg1 *model.WorkflowRun, arg2 model.WorkflowRunState, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWorkflowRunResult", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWorkflowRunResult indicates an expected call of SetWorkflowRunResult.
func (mr *MockStoreMockRecorder) SetWorkflowRunResult(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWorkflowRunResult", reflect.TypeOf((*MockStore)(nil).SetWorkflowRunResult), arg0, arg1, arg2, arg3)
}

// SetWorkflowRunToken mocks base method.
func (m *MockStore) SetWorkflowRunToken(arg0 context.Context, arg1 string, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWorkflowRunToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWorkflowRunToken indicates an expected call of SetWorkflowRunToken.
func (mr *MockStoreMockRecorder) SetWorkflowRunToken(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWorkflowRunToken", reflect.TypeOf((*MockStore)(nil).SetWorkflowRunToken), arg0, arg1, arg2)
}

// UpdateWorkflowRun mocks base method.
func (m *MockStore) UpdateWorkflowRun(arg0 context.Context, arg1 *model.WorkflowRun) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflowRun", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWorkflowRun indicates an expected call of UpdateWorkflowRun.
func (mr *MockStoreMockRecorder) UpdateWorkflowRun(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflowRun", reflect.TypeOf((*MockStore)(nil).UpdateWorkflowRun), arg0, arg1)
}
