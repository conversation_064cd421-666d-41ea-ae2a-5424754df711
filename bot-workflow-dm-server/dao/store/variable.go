package store

import (
	"context"
	"encoding/json"
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// GetVariablesKey 任务流的存储KEY
func GetVariablesKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.VariablesKeyFormat, getEnvKey(runEnv), appID)
}

// SaveVariablesInSandbox 保存槽位
func (r *RedisStore) SaveVariablesInSandbox(ctx context.Context, appID string, variables []*KEP_WF_DM.Var) error {
	LogRedis(ctx).Infof("SaveVariablesInSandbox, appID: %v, variables: %v,", appID, variables)

	varMapKey := GetVariablesKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	idValues := make([]string, 0, len(variables)*2)
	for _, variable := range variables {
		variableData := util.ToJsonString(variable)
		idValues = append(idValues, []string{variable.VarID, variableData}...)
	}
	err := r.client.HSet(ctx, varMapKey, idValues).Err()
	if err != nil {
		Log(ctx).Errorf("HSet failed, varMapKey: %s, error: %v", varMapKey, err)
		return err
	}
	LogRedis(ctx).Infof("SaveVariablesInSandbox, done")

	return nil
}

// DeleteVariablesInSandbox 删除沙箱的槽位
func (r *RedisStore) DeleteVariablesInSandbox(ctx context.Context, appID string, varIDs []string) error {
	LogRedis(ctx).Infof("DeleteVariablesInSandbox, appID:%v, varIDs:%v", appID, varIDs)
	varMapKey := GetVariablesKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)

	result, err := r.client.HDel(ctx, varMapKey, varIDs...).Result()
	if err != nil {
		Log(ctx).Errorf("HDel failed, varMapKey: %s, varIDs: %v, error: %v", varMapKey, varIDs, err)
		return err
	}
	if result <= 0 {
		Log(ctx).Warnf("HDel result is: %s, varMapKey: %s, varIDs: %v, error: %v", result, varMapKey, varIDs, err)
	}

	LogRedis(ctx).Infof("DeleteVariablesInSandbox, done")
	return nil
}

// GetVariables 获取自定义参数
func (r *RedisStore) GetVariables(ctx context.Context, runEnv KEP_WF_DM.RunEnvType,
	appID string) (map[string]*KEP_WF_DM.Var, error) {
	varMapKey := GetVariablesKey(runEnv, appID)
	result, err := r.client.HGetAll(ctx, varMapKey).Result()
	if err != nil {
		Log(ctx).Errorf("HGetAll failed, varMapKey: %s, error: %v", varMapKey, err)
		return nil, err
	}
	variables := make(map[string]*KEP_WF_DM.Var, len(result))
	for varID, varData := range result {
		variable := &KEP_WF_DM.Var{}
		err = json.Unmarshal([]byte(varData), variable)
		if err != nil {
			Log(ctx).Errorf("invalid variableInfo: %s, error: %v", varData, err)
			return nil, err
		}
		variables[varID] = variable
	}
	return variables, nil
}
