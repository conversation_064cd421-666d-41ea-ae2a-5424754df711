package store

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

var vars = []*KEP_WF_DM.Var{
	{
		VarID:   "varID001",
		VarName: "varName001",
	},
	{
		VarID:   "varID002",
		VarName: "varName002",
	},
}

func Test_SaveSaveVariableInSandbox(t *testing.T) {
	<PERSON>vey("SaveVariablesInSandbox", t, func() {
		err := store.SaveVariablesInSandbox(context.Background(), testAppID, vars)
		t.Logf("error: %v", err)
	})
}

func Test_DeleteVariablesInSandbox(t *testing.T) {
	<PERSON>vey("DeleteVariablesInSandbox", t, func() {
		err := store.DeleteVariablesInSandbox(context.Background(), testAppID, []string{"varID001", "varID002"})
		t.Logf("error: %v", err)
	})
}

func Test_GetVariables(t *testing.T) {
	<PERSON><PERSON>("GetVariables", t, func() {
		vars, err := store.GetVariables(context.Background(), KEP_WF_DM.RunEnvType_SANDBOX, testAppID)
		t.Logf("error: %v", err)
		for varID, varialbe := range vars {
			t.Logf("varID: %v, varialbe: %v", varID, varialbe)
		}
	})
}
