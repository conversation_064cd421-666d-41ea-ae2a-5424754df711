package store

import (
	"context"
	"encoding/json"
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"google.golang.org/protobuf/encoding/protojson"
)

// GetWorkflowsKey 任务流的存储KEY
func GetWorkflowsKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.WorkflowsKeyFormat, getEnvKey(runEnv), appID)
}

// GetWorkflowExamplesKey 工作流示例的存储KEY
func GetWorkflowExamplesKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.WorkflowExamplesKeyFormat, getEnvKey(runEnv), appID)
}

// GetEnableWorkflowsKey 任务流是否启用的存储KEY
func GetEnableWorkflowsKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.EnableWorkflowsKeyFormat, getEnvKey(runEnv), appID)
}

// GetWorkflowReleaseTimesKey 获取工作流发布时间的存储KEY
func GetWorkflowReleaseTimesKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.WorkflowReleaseTimesKeyFormat, getEnvKey(runEnv), appID)
}

// SaveWorkflowsInSandbox 保存工作流
func (r *RedisStore) SaveWorkflowsInSandbox(ctx context.Context, appID string, workflows []*KEP_WF.Workflow) error {
	LogRedis(ctx).Infof("SaveWorkflowsInSandbox, appID: %v, workflows: %v,", appID, workflows)

	workflowMapKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	idValues := make([]string, 0, len(workflows)*2)
	for _, workflow := range workflows {
		workflowData, _ := protojson.Marshal(workflow)
		idValues = append(idValues, []string{workflow.WorkflowID, string(workflowData)}...)
	}
	err := r.client.HSet(ctx, workflowMapKey, idValues).Err()
	if err != nil {
		Log(ctx).Errorf("HSet failed, workflowMapKey: %s, error: %v", workflowMapKey, err)
		return err
	}
	LogRedis(ctx).Infof("SaveWorkflowsInSandbox, done")

	return nil
}

// DeleteWorkflowsInSandbox 删除沙箱的工作流
func (r *RedisStore) DeleteWorkflowsInSandbox(ctx context.Context, appID string, workflowIDs []string) error {
	LogRedis(ctx).Infof("DeleteWorkflowsInSandbox, appID:%v, workflowIDs:%v", appID, workflowIDs)
	workflowMapKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)

	result, err := r.client.HDel(ctx, workflowMapKey, workflowIDs...).Result()
	if err != nil {
		Log(ctx).Errorf("HDel failed, workflowMapKey: %s, workflowIDs: %v, error: %v", workflowMapKey, workflowIDs, err)
		return err
	}
	if result <= 0 {
		Log(ctx).Warnf("HDel result is: %s, workflowMapKey: %s, workflowIDs: %v, error: %v", result, workflowMapKey,
			workflowIDs, err)
	}

	LogRedis(ctx).Infof("DeleteWorkflowsInSandbox, done")
	return nil
}

// GetWorkflows 获取应用下的工作流列表
func (r *RedisStore) GetWorkflows(ctx context.Context, runEnv KEP_WF_DM.RunEnvType,
	appID string) (map[string]*KEP_WF.Workflow,
	error) {
	workflowMapKey := GetWorkflowsKey(runEnv, appID)
	result, err := r.client.HGetAll(ctx, workflowMapKey).Result()
	if err != nil {
		Log(ctx).Errorf("HGetAll failed, workflowMapKey: %s, error: %v", workflowMapKey, err)
		return nil, err
	}

	enableWorkflowsKey := GetEnableWorkflowsKey(runEnv, appID)
	enableWorkflowsResult, err := r.client.HGetAll(ctx, enableWorkflowsKey).Result()
	if err != nil {
		Log(ctx).Errorf("HGetAll failed, enableWorkflowsKey: %s, error: %v", enableWorkflowsKey, err)
		return nil, err
	}

	// 发布时间
	workflowReleaseTimesKey := GetWorkflowReleaseTimesKey(runEnv, appID)
	workflowReleaseTimes, _ := r.client.HGetAll(ctx, workflowReleaseTimesKey).Result()

	workflows := make(map[string]*KEP_WF.Workflow, len(result))
	for workflowID, workflowData := range result {
		// 过滤掉未启用的
		if enableWorkflowsResult[workflowID] != tconst.WorkflowEnableTrue {
			continue
		}

		workflow := &KEP_WF.Workflow{}
		err = protojson.Unmarshal([]byte(workflowData), workflow)
		if err != nil {
			Log(ctx).Errorf("invalid workflowInfo: %s, error: %v", workflowData, err)
			return nil, err
		}
		// 设置发布时间
		if workflowReleaseTimes != nil && workflowReleaseTimes[workflowID] != "" {
			workflow.ReleaseTime = workflowReleaseTimes[workflowID]
		}
		workflows[workflowID] = workflow
	}
	return workflows, nil
}

// GetWorkflow 获取单个工作流
func (r *RedisStore) GetWorkflow(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) (
	*KEP_WF.Workflow, error) {
	workflowMapKey := GetWorkflowsKey(runEnv, appID)
	result, err := r.client.HGet(ctx, workflowMapKey, workflowID).Result()
	if err != nil {
		Log(ctx).Errorf("HGet failed, workflowMapKey: %s, workflowID: %v, error: %v", workflowMapKey, workflowID, err)
		return nil, err
	}
	enableWorkflowsKey := GetEnableWorkflowsKey(runEnv, appID)
	enableWorkflowResult, err := r.client.HGet(ctx, enableWorkflowsKey, workflowID).Result()
	if err != nil {
		Log(ctx).Errorf("HGet failed, enableWorkflowsKey: %s, error: %v", enableWorkflowsKey, err)
		return nil, err
	}
	workflow := &KEP_WF.Workflow{}
	err = protojson.Unmarshal([]byte(result), workflow)
	if err != nil {
		Log(ctx).Errorf("invalid workflowInfo: %s, error: %v", result, err)
		return nil, err
	}
	workflowReleaseTimesKey := GetWorkflowReleaseTimesKey(runEnv, appID)
	workflowReleaseTime, err := r.client.HGet(ctx, workflowReleaseTimesKey, workflowID).Result()
	if err == nil {
		workflow.ReleaseTime = workflowReleaseTime
	}
	workflow.ReleaseTime = workflowReleaseTime
	if enableWorkflowResult != tconst.WorkflowEnableTrue {
		return workflow, fmt.Errorf("workflow %s is not enable", workflowID)
	}
	return workflow, nil
}

// GetWorkflowExamples 获取工作流示例问法
func (r *RedisStore) GetWorkflowExamples(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) (
	[]string, error) {
	examplesMapKey := GetWorkflowExamplesKey(runEnv, appID)
	result, err := r.client.HGet(ctx, examplesMapKey, workflowID).Result()
	if err != nil {
		if r.IsNotFound(err) {
			return nil, nil
		}
		Log(ctx).Errorf("HGet failed, workflowMapKey: %s, error: %v", examplesMapKey, err)
		return nil, err
	}
	wfExampleMap := make(map[string]string)
	err = json.Unmarshal([]byte(result), &wfExampleMap)
	if err != nil {
		Log(ctx).Errorf("invalid examples: %s, error: %v", result, err)
		return nil, err
	}
	examples := make([]string, 0)
	for _, wfExample := range wfExampleMap {
		examples = append(examples, wfExample)
	}
	return examples, nil
}
