package store

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

var workflows = []*KEP_WF.Workflow{
	{
		ProtoVersion: 110,
		WorkflowID:   "WorkflowID_001",
		WorkflowName: "工作流名称",
		WorkflowDesc: "WorkflowDesc_001",
		Nodes: []*KEP_WF.WorkflowNode{
			{
				NodeID:      "node_001",
				NodeName:    "node_001_name",
				NodeType:    KEP_WF.NodeType_START,
				NodeData:    nil,
				NextNodeIDs: []string{"node_002"},
			}, {
				NodeID:   "node_002",
				NodeName: "node_002_name",
				NodeType: KEP_WF.NodeType_LLM,
				NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
					LLMNodeData: &KEP_WF.LLMNodeData{
						ModelName:   "",
						Temperature: 0,
						TopP:        0,
						MaxTokens:   0,
						Prompt:      "",
					},
				},
				NextNodeIDs: []string{"node_003", "node_004"},
			}, {
				NodeID:   "node_003",
				NodeName: "node_003_name",
				NodeType: KEP_WF.NodeType_ANSWER,
				Inputs: []*KEP_WF.InputParam{
					{
						Name: "aa",
						Type: 0,
						Input: &KEP_WF.Input{
							InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
							Source: &KEP_WF.Input_Reference{
								Reference: &KEP_WF.ReferenceFromNode{
									NodeID:   "node_002",
									JsonPath: "Output.Content",
								},
							},
						},
						Desc: "",
					},
				},
				NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
					Answer: `这个是回复节点33333333333333：LLM： {{aa}}；`,
				}},
				NextNodeIDs: nil,
			}, {
				Inputs: []*KEP_WF.InputParam{
					{
						Name: "aa",
						Type: 0,
						Input: &KEP_WF.Input{
							InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
							Source: &KEP_WF.Input_Reference{
								Reference: &KEP_WF.ReferenceFromNode{
									NodeID:   "node_002",
									JsonPath: "Output.Content",
								},
							},
						},
						Desc: "",
					},
				},
				NodeID:   "node_004",
				NodeName: "node_004_name",
				NodeType: KEP_WF.NodeType_ANSWER,
				NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
					Answer: `这个是回复节点4444444444： LLM： {{aa}}`,
				}},
				NextNodeIDs: nil,
			},
		},
		Edge: "",
	},
	{
		ProtoVersion: 110,
		WorkflowID:   "WorkflowID_002",
		WorkflowName: "WorkflowName_002",
		WorkflowDesc: "WorkflowDesc_002",
		Nodes:        nil,
	},
}

func Test_SaveSaveWorkflowsInSandbox(t *testing.T) {
	Convey("SaveWorkflowsInSandbox", t, func() {
		err := store.SaveWorkflowsInSandbox(context.Background(), testAppID, workflows)
		t.Logf("error: %v", err)
	})
}

func Test_DeleteWorkflowsInSandbox(t *testing.T) {
	Convey("DeleteWorkflowsInSandbox", t, func() {
		err := store.DeleteWorkflowsInSandbox(context.Background(), testAppID, []string{"WorkflowID_001"})
		t.Logf("error: %v", err)
	})
}

func Test_GetWorkflows(t *testing.T) {
	Convey("GetWorkflows", t, func() {
		workflows, err := store.GetWorkflows(context.Background(), KEP_WF_DM.RunEnvType_SANDBOX, testAppID)
		t.Logf("error: %v", err)
		for workflowID, workflow := range workflows {
			t.Logf("workflowID: %v, workflow: %v", workflowID, workflow)
		}
	})
}
