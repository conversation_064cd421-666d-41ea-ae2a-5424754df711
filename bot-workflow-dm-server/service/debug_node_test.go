package service

import (
	"context"
	"fmt"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestDebugWorkflowNode(t *testing.T) {
	tests := []struct {
		name          string
		req           *KEP_WF_DM.DebugWorkflowNodeRequest
		expectedError error
	}{
		{
			name: "Valid LLM Node",
			req: &KEP_WF_DM.DebugWorkflowNodeRequest{
				AppID: "test-app",
				NodeJSON: `{
					  "NodeID": "8d4e5d39-9fc3-b5ea-4016-e347000142d7",
					  "NodeName": "回复1",
					  "NodeType": "ANSWER",
					  "NodeDesc": "",
					  "AnswerNodeData": {
						"Answer": "这个是回复节点"
					  }
				}`,
			},
			expectedError: nil,
		},
		{
			name: "Invalid Node JSON",
			req: &KEP_WF_DM.DebugWorkflowNodeRequest{
				AppID:    "test-app",
				NodeJSON: "invalid json",
			},
			expectedError: errs.New(tconst.ErrCodeFailed, "invalid node, error: proto: syntax error (line 1:1): invalid value invalid"),
		},
		{
			name: "Unsupported Node Type",
			req: &KEP_WF_DM.DebugWorkflowNodeRequest{
				AppID: "test-app",
				NodeJSON: `{
					"nodeType": "UNKNOWN_TYPE"
				}`,
			},
			expectedError: errs.New(tconst.ErrCodeFailed, "invalid NodeType: UNKNOWN"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			w := WorkflowDmImp{}

			reply, err := w.DebugWorkflowNode(ctx, tt.req)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, reply)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, reply)
			}
		})
	}
}

type mockDebugWorkflowNodeDialogServer struct {
	KEP_WF_DM.WorkflowDm_DebugWorkflowNodeDialogServer
	mock.Mock
	recvCh chan struct{}
	ctx    context.Context
}

func (m *mockDebugWorkflowNodeDialogServer) SetContext(ctx context.Context) {
	m.ctx = ctx
}

func (m *mockDebugWorkflowNodeDialogServer) Context() context.Context {
	if m.ctx != nil {
		return m.ctx
	}
	return context.Background()
}

func (m *mockDebugWorkflowNodeDialogServer) Send(reply *KEP_WF_DM.DebugWorkflowNodeDialogReply) error {
	// // 打印 Send 的参数
	// fmt.Printf("Send called with reply-----------: %+v\n", util.ToJsonString(reply))
	args := m.Called(reply)
	return args.Error(0)
}
func (m *mockDebugWorkflowNodeDialogServer) AddRecv() {
	if m.recvCh == nil {
		m.recvCh = make(chan struct{}, 10)
	}
	m.recvCh <- struct{}{}
}

func (m *mockDebugWorkflowNodeDialogServer) Recv() (*KEP_WF_DM.DebugWorkflowNodeDialogRequest, error) {
	<-m.recvCh
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*KEP_WF_DM.DebugWorkflowNodeDialogRequest), args.Error(1)
}

func TestDebugWorkflowNodeDialog(t *testing.T) {
	InitTestRedis()

	tests := []struct {
		name          string
		req           *KEP_WF_DM.DebugWorkflowNodeDialogRequest
		recvError     error
		sendError     error
		expectedError error
	}{
		{
			name: "选项卡节点",
			req: &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				SessionID:   "test-session",
				AppID:       AppID,
				RequestType: KEP_WF_DM.RequestType_RUN,
				NodeJSON: `{
					  "NodeID": "eaa54579-c73e-c151-3346-98b705c220f7",
					  "NodeName": "选项卡1",
					  "NodeType": "OPTION_CARD",
					  "NodeDesc": "",
					  "Inputs": [],
					  "OptionCardNodeData": {
						"Question": "",
						"Options": [
						  {
							"NextNodeIDs": [],
							"Content": "111"
						  },
						  {
							"NextNodeIDs": [],
							"Content": "222"
						  },
						  {
							"NextNodeIDs": [],
							"Content": ""
						  }
						]
					  }
				}`,
				MainModelName: "test-model",
			},
			recvError:     nil,
			sendError:     nil,
			expectedError: nil,
		},
		{
			name: "首包是STOP",
			req: &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				SessionID:     "test-session",
				AppID:         AppID,
				RequestType:   KEP_WF_DM.RequestType_STOP,
				NodeJSON:      `{"nodeType": "LLM"}`,
				MainModelName: "test-model",
			},
			recvError:     nil,
			sendError:     nil,
			expectedError: nil, // Error will be handled by sendDebugNodeFailed
		},
		{
			name: "测试工作流节点的错误处理",
			req: &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				SessionID:   "test-session",
				AppID:       AppID,
				RequestType: KEP_WF_DM.RequestType_RUN,
				NodeJSON: `{
				  "NodeID": "de745185-1317-3a41-0d97-a8cd161e95ab",
				  "NodeName": "回复1",
				  "NodeType": "WORKFLOW_REF",
				  "NodeDesc": "",
				  "WorkflowRefNodeData": {
					"WorkflowID": "error_workflow_001"
				  },
				  "Inputs": [],
				  "Outputs": [],
				  "ExceptionHandling": {
					"Switch": 1,
					"MaxRetries": 3,
					"AbnormalOutputResult": "{\"XX\": 33}",
					"RetryInterval": 3
				  }
				}`,
				MainModelName: "test-model",
			},
			recvError:     nil,
			sendError:     nil,
			expectedError: nil, // Error will be handled by sendDebugNodeFailed
		},
		{
			name: "Invalid Node JSON",
			req: &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				SessionID:   "test-session",
				AppID:       AppID,
				RequestType: KEP_WF_DM.RequestType_RUN,
				NodeJSON: `{
					  "NodeID": "8d4e5d39-9fc3-b5ea-4016-e347000142d7",
					  "NodeName": "回复1",
					  "NodeType": "ANSWER",
					  "NodeDesc": "",
					  "AnswerNodeData": {
						"Answer": "这个是回复节点"
					  }
				}`,
				MainModelName: "test-model",
			},
			recvError:     nil,
			sendError:     nil,
			expectedError: nil, // Error will be handled by sendDebugNodeFailed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockServer := new(mockDebugWorkflowNodeDialogServer)
			mockServer.AddRecv()
			mockServer.On("Recv").Return(tt.req, tt.recvError)
			// 使用 RunFn 来执行自定义逻辑
			mockServer.On("Send", mock.Anything).Run(func(args mock.Arguments) {
				reply := args.Get(0).(*KEP_WF_DM.DebugWorkflowNodeDialogReply)
				fmt.Printf("Send called with reply-----------: %+v\n", util.ToJsonString(reply))
			}).Return(nil)

			w := WorkflowDmImp{store: store.Default()}
			err := w.DebugWorkflowNodeDialog(mockServer)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			mockServer.AssertExpectations(t)
		})
	}
}

func TestDebugWorkflowNodeDialog_Loop_workflow(t *testing.T) {
	InitTestRedis()

	tests := []struct {
		name          string
		reqs          []*KEP_WF_DM.DebugWorkflowNodeDialogRequest
		recvError     error
		sendError     error
		expectedError error
	}{
		{
			name: "循环节点",
			reqs: loopNodeBaseReqs,
		},
		{
			name:          "循环节点-单点对话-选项卡子工作流",
			reqs:          loopNodeOptionCardReqs,
			recvError:     nil,
			sendError:     nil,
			expectedError: nil,
		},
		{
			name: "工作流节点",
			reqs: workflowNodeReqs,
		},
		{
			name: "循环节点-2.9.0-结构变化",
			reqs: loopNode290BaseReqs,
		},
		{
			name: "循环节点-2.9.0-多层嵌套。 循环节点（Inputs参数）、循环工作流1（API参数）、循环工作流2（API参数）、回复工作流（开始节点）",
			reqs: loopNode290MultiLevelReqs,
		},
		{
			name: "工作流-2.9.0-多层嵌套第二批，工作流->工作流->工作流（LLM、回复） A -> B -C工作流",
			reqs: workflowNode290MultiLevelReqs,
		},
		{
			name: "循环的引用-2.9.0-第二批",
			reqs: loopNode290Reqs,
		},
		{
			name: "循环的引用-2.9.0-多类型多层级",
			reqs: multiLevelMultiType290,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for i := range tt.reqs {

				mockServer := new(mockDebugWorkflowNodeDialogServer)
				ctx, cancel := context.WithCancel(context.Background())
				mockServer.SetContext(ctx)
				mockServer.AddRecv()
				mockServer.On("Recv").Return(tt.reqs[i], tt.recvError)
				// 使用 RunFn 来执行自定义逻辑
				mockServer.On("Send", mock.Anything).Run(func(args mock.Arguments) {
					reply := args.Get(0).(*KEP_WF_DM.DebugWorkflowNodeDialogReply)
					fmt.Printf("Send called with reply-----------: %+v\n", util.ToJsonString(reply))
				}).Return(nil)

				w := WorkflowDmImp{store: store.Default()}
				err := w.DebugWorkflowNodeDialog(mockServer)
				if tt.expectedError != nil {
					assert.Error(t, err)
					assert.Equal(t, tt.expectedError.Error(), err.Error())
				} else {
					assert.NoError(t, err)
				}
				mockServer.AssertExpectations(t)
				cancel()
			}
		})
	}
}

func TestDebugWorkflowNodeDialog_Batch_workflow(t *testing.T) {
	InitTestRedis()

	tests := []struct {
		name          string
		reqs          []*KEP_WF_DM.DebugWorkflowNodeDialogRequest
		recvError     error
		sendError     error
		expectedError error
	}{
		{
			name: "批处理节点",
			reqs: batchNodeBaseReqs,
		},
		{
			name: "批处理节点-bug",
			reqs: batchNodeBaseReqsBug,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for i := range tt.reqs {

				mockServer := new(mockDebugWorkflowNodeDialogServer)
				ctx, cancel := context.WithCancel(context.Background())
				mockServer.SetContext(ctx)
				mockServer.AddRecv()
				mockServer.On("Recv").Return(tt.reqs[i], tt.recvError)
				// 使用 RunFn 来执行自定义逻辑
				mockServer.On("Send", mock.Anything).Run(func(args mock.Arguments) {
					reply := args.Get(0).(*KEP_WF_DM.DebugWorkflowNodeDialogReply)
					fmt.Printf("Send called with reply-----------: %+v\n", util.ToJsonString(reply))
				}).Return(nil)

				w := WorkflowDmImp{store: store.Default()}
				err := w.DebugWorkflowNodeDialog(mockServer)
				if tt.expectedError != nil {
					assert.Error(t, err)
					assert.Equal(t, tt.expectedError.Error(), err.Error())
				} else {
					assert.NoError(t, err)
				}
				mockServer.AssertExpectations(t)
				cancel()
			}
		})
	}
}

func TestCheckDebugWorkflowNodeDialog(t *testing.T) {
	tests := []struct {
		name          string
		req           *KEP_WF_DM.DebugWorkflowNodeDialogRequest
		expectedError error
	}{
		{
			name: "Valid Request",
			req: &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				SessionID:     "test-session",
				AppID:         "test-app",
				RequestType:   KEP_WF_DM.RequestType_RUN,
				NodeJSON:      `{"nodeType": "LLM"}`,
				MainModelName: "test-model",
			},
			expectedError: nil,
		},
		{
			name: "Missing Required Fields",
			req:  &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
				// Missing required fields
			},
			expectedError: nil, // govalidator will return an error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkDebugWorkflowNodeDialog(tt.req)
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
